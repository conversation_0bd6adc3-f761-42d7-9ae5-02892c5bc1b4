package us.zoom.cube.site.biz;

import com.google.common.collect.Lists;
import com.googlecode.aviator.AviatorEvaluator;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.lib.integrations.Metrics;
import us.zoom.cube.lib.integrations.MetricsField;
import us.zoom.cube.lib.integrations.MetricsFieldTypeEnum;
import us.zoom.cube.site.core.MetricsAggregationHandler;
import us.zoom.cube.site.core.MetricsAggregationRuleHandler;
import us.zoom.cube.site.core.MetricsHandler;
import us.zoom.cube.site.infra.enums.WebCodeEnum;
import us.zoom.cube.site.infra.utils.IdUtils;
import us.zoom.cube.site.lib.ResponseObject;
import us.zoom.cube.site.lib.input.*;
import us.zoom.cube.site.lib.output.PageResult;
import us.zoom.cube.site.lib.output.agg.AggregationFunctionRuleOutput;
import us.zoom.cube.site.lib.output.agg.AggregationHistogramRuleOutput;
import us.zoom.cube.site.lib.output.agg.AggregationPercentileRuleOutput;
import us.zoom.cube.site.lib.output.config.metrics.MetricsOut;
import us.zoom.cube.site.lib.output.config.metrics.SearchMetricsItemOut;
import us.zoom.cube.site.lib.query.MetricsQuery;
import us.zoom.cube.site.lib.query.PageQuery;
import us.zoom.infra.dao.model.*;
import us.zoom.infra.enums.MetricsTypeEnum;
import us.zoom.infra.utils.Instance;
import us.zoom.cube.lib.utils.JsonUtils;

import java.text.DecimalFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.when;
import static us.zoom.infra.utils.CommonSplitConstants.COMMA_SPLIT;
import static us.zoom.infra.utils.RegexConstants.CUSTOM_FIELD_EXPRESSION_PATTERN;

/**
 * <AUTHOR> Junjian
 * @create 2020/7/29 1:53 PM
 */
@ExtendWith(MockitoExtension.class)
@Slf4j
public class MetricsServiceTest {

    private static final String tenantId = "tenantId1";
    private static final String collectorId = "collectorId1";
    private static final String userId = "userId1";
    private static final String metricsId = "metricsId1";

    @InjectMocks
    private MetricsService metricsService = new MetricsService();

    @Mock
    private AuthService authService;
    @Mock
    private MetricsHandler metricsHandler;
    @Mock
    private MetricsAggregationHandler metricsAggregationHandler;

    @Mock
    private MetricsAggregationRuleHandler metricsAggregationRuleHandler;

    /*@Rule
    public ExpectedException exceptionRule = ExpectedException.none();*/
    @Test
    public void searchMetricsWithVersion0() {
        PageQuery<MetricsQuery> pageQuery = Mockito.mock(PageQuery.class);
        when(pageQuery.getTenantId()).thenReturn("tenant");
        when(pageQuery.getPageIndex()).thenReturn(1);
        when(pageQuery.getPageSize()).thenReturn(20);
        MetricsQuery metricsQuery = Mockito.mock(MetricsQuery.class);
        when(metricsQuery.getMetricsName()).thenReturn("metricsName");
        when(metricsQuery.getMetricsType()).thenReturn(3);
        when(pageQuery.getQueryPara()).thenReturn(metricsQuery);
        List<MetricsDO> metricsDOList = new ArrayList<>();
        MetricsDO metricsDO1 = new MetricsDO();
        metricsDO1.setId("metricsId1");
        metricsDO1.setMetricsName("metricsName1");
        metricsDO1.setType(1);
        metricsDO1.setTagNames("tag1");
        metricsDO1.setTenantId("tenant");
        metricsDOList.add(metricsDO1);

        MetricsDO metricsDO2 = new MetricsDO();
        metricsDO2.setId("metricsId2");
        metricsDO2.setMetricsName("metricsName2");
        metricsDO2.setType(2);
        metricsDO2.setTagNames("tag2");
        metricsDO2.setTenantId("tenant");
        metricsDOList.add(metricsDO2);


        when(metricsHandler.findByMetricsNameLikeAndType("metricsName",3,Lists.newArrayList("tenant"),1,20)).thenReturn(metricsDOList);
        when(metricsHandler.getCountByMetricsNameLikeAndType("metricsName",3,Lists.newArrayList("tenant"))).thenReturn(2);

        List<MetricsAggregationDO> metricsAggregationDOList = new ArrayList<>();
        MetricsAggregationDO metricsAggregationDO1 = new MetricsAggregationDO();
        metricsAggregationDO1.setMetricsId("metricsId1");
        metricsAggregationDO1.setId("aggId1");
        metricsAggregationDOList.add(metricsAggregationDO1);
        when(metricsHandler.listMetricsAggByMetricsIds(Lists.newArrayList("metricsId1","metricsId2"))).thenReturn(metricsAggregationDOList);

        List<MetricsAggregationRuleDO> metricsAggregationRuleDOList = new ArrayList<>();
        MetricsAggregationRuleDO metricsAggregationRuleDO1 = new MetricsAggregationRuleDO();
        metricsAggregationRuleDO1.setAggId("aggId1");
        metricsAggregationRuleDO1.setId("ruleId1");
        metricsAggregationRuleDO1.setVersion(0);
        metricsAggregationRuleDO1.setAggRuleType(1);
        metricsAggregationRuleDO1.setAggTypes("count,sum");
        metricsAggregationRuleDO1.setIsConditioned(0);
        metricsAggregationRuleDOList.add(metricsAggregationRuleDO1);
        when(metricsHandler.listRulesByMetricsAggIds(Lists.newArrayList("aggId1"))).thenReturn(metricsAggregationRuleDOList);


        ResponseObject responseObject = metricsService.searchMetrics(pageQuery);
        System.out.println("responseObject = "+ JsonUtils.toJsonString(responseObject));
        List<SearchMetricsItemOut> searchMetricsItemOutList = (List<SearchMetricsItemOut>)((PageResult)responseObject.getData()).getItems();
        Assertions.assertEquals(2, searchMetricsItemOutList.size());
        searchMetricsItemOutList.stream().forEach(e -> {
            if(e.getId().equals("metricsId1")){
                Assertions.assertEquals(2,e.getFieldsCount().getCommon().get());
                Assertions.assertEquals(0,e.getFieldsCount().getHistogram().get());
                Assertions.assertEquals(0,e.getFieldsCount().getPercentile().get());
            } else if(e.getId().equals("metricsId2")) {
                Assertions.assertEquals(0,e.getFieldsCount().getCommon().get());
                Assertions.assertEquals(0,e.getFieldsCount().getHistogram().get());
                Assertions.assertEquals(0,e.getFieldsCount().getPercentile().get());
            }
        });

    }

    @Test
    public void searchMetricsWithVersion0AndVersion1AndCommonHistogramAndPercentile(){
        PageQuery<MetricsQuery> pageQuery = Mockito.mock(PageQuery.class);
        when(pageQuery.getTenantId()).thenReturn("tenant");
        when(pageQuery.getPageIndex()).thenReturn(1);
        when(pageQuery.getPageSize()).thenReturn(20);
        MetricsQuery metricsQuery = Mockito.mock(MetricsQuery.class);
        when(metricsQuery.getMetricsName()).thenReturn("metricsName");
        when(metricsQuery.getMetricsType()).thenReturn(3);
        when(pageQuery.getQueryPara()).thenReturn(metricsQuery);
        List<MetricsDO> metricsDOList = new ArrayList<>();
        MetricsDO metricsDO1 = new MetricsDO();
        metricsDO1.setId("metricsId1");
        metricsDO1.setMetricsName("metricsName1");
        metricsDO1.setType(1);
        metricsDO1.setTagNames("tag1");
        metricsDO1.setTenantId("tenant");
        metricsDOList.add(metricsDO1);

        MetricsDO metricsDO2 = new MetricsDO();
        metricsDO2.setId("metricsId2");
        metricsDO2.setMetricsName("metricsName2");
        metricsDO2.setType(2);
        metricsDO2.setTagNames("tag2");
        metricsDO2.setTenantId("tenant");
        metricsDOList.add(metricsDO2);


        when(metricsHandler.findByMetricsNameLikeAndType("metricsName",3,Lists.newArrayList("tenant"),1,20)).thenReturn(metricsDOList);
        when(metricsHandler.getCountByMetricsNameLikeAndType("metricsName",3,Lists.newArrayList("tenant"))).thenReturn(2);

        List<MetricsAggregationDO> metricsAggregationDOList = new ArrayList<>();
        MetricsAggregationDO metricsAggregationDO1 = new MetricsAggregationDO();
        metricsAggregationDO1.setMetricsId("metricsId1");
        metricsAggregationDO1.setId("aggId1");
        metricsAggregationDOList.add(metricsAggregationDO1);
        when(metricsHandler.listMetricsAggByMetricsIds(Lists.newArrayList("metricsId1","metricsId2"))).thenReturn(metricsAggregationDOList);

        // version = 0, function, notConditioned
        List<MetricsAggregationRuleDO> metricsAggregationRuleDOList = new ArrayList<>();
        MetricsAggregationRuleDO metricsAggregationRuleDO1 = new MetricsAggregationRuleDO();
        metricsAggregationRuleDO1.setAggId("aggId1");
        metricsAggregationRuleDO1.setId("ruleId1");
        metricsAggregationRuleDO1.setVersion(0);
        metricsAggregationRuleDO1.setAggRuleType(1);
        metricsAggregationRuleDO1.setAggTypes("count,sum");
        metricsAggregationRuleDO1.setIsConditioned(0);
        metricsAggregationRuleDOList.add(metricsAggregationRuleDO1);

        // version = 1, function, conditioned
        MetricsAggregationRuleDO metricsAggregationRuleDO2 = new MetricsAggregationRuleDO();
        metricsAggregationRuleDO2.setAggId("aggId1");
        metricsAggregationRuleDO2.setId("ruleId2");
        metricsAggregationRuleDO2.setVersion(1);
        metricsAggregationRuleDO2.setAggRuleType(1);
        metricsAggregationRuleDO2.setAggTypes("");
        metricsAggregationRuleDO2.setIsConditioned(1);
        metricsAggregationRuleDOList.add(metricsAggregationRuleDO2);

        // version = 1, histogram
        MetricsAggregationRuleDO metricsAggregationRuleDO3 = new MetricsAggregationRuleDO();
        metricsAggregationRuleDO3.setAggId("aggId1");
        metricsAggregationRuleDO3.setId("ruleId3");
        metricsAggregationRuleDO3.setVersion(1);
        metricsAggregationRuleDO3.setAggRuleType(2);
        metricsAggregationRuleDO3.setAggTypes("");
        metricsAggregationRuleDOList.add(metricsAggregationRuleDO3);

        // version = 1, percentile
        MetricsAggregationRuleDO metricsAggregationRuleDO4 = new MetricsAggregationRuleDO();
        metricsAggregationRuleDO4.setAggId("aggId1");
        metricsAggregationRuleDO4.setId("ruleId4");
        metricsAggregationRuleDO4.setVersion(1);
        metricsAggregationRuleDO4.setAggRuleType(3);
        metricsAggregationRuleDO4.setAggTypes("");
        metricsAggregationRuleDOList.add(metricsAggregationRuleDO4);
        when(metricsHandler.listRulesByMetricsAggIds(Lists.newArrayList("aggId1"))).thenReturn(metricsAggregationRuleDOList);


        // mock function item
        List<AggregationFunctionItemDO> aggregationFunctionItemDOList = new ArrayList<>();
        AggregationFunctionItemDO aggregationFunctionItemDO1 = new AggregationFunctionItemDO();
        aggregationFunctionItemDO1.setMetricsAggregationRuleId("ruleId2");
        aggregationFunctionItemDO1.setAggType("count");
        aggregationFunctionItemDOList.add(aggregationFunctionItemDO1);
        when(metricsAggregationRuleHandler.listFunctionItemsByRuleIds(Lists.newArrayList("ruleId2"))).thenReturn(aggregationFunctionItemDOList);

        // mock histogram item
        List<AggregationHistogramRangeItemDO> aggregationHistogramRangeItemDOList = new ArrayList<>();
        AggregationHistogramRangeItemDO aggregationHistogramRangeItemDO1 = new AggregationHistogramRangeItemDO();
        aggregationHistogramRangeItemDO1.setMetricsAggregationRuleId("ruleId3");
        aggregationHistogramRangeItemDOList.add(aggregationHistogramRangeItemDO1);
        AggregationHistogramRangeItemDO aggregationHistogramRangeItemDO2 = new AggregationHistogramRangeItemDO();
        aggregationHistogramRangeItemDO2.setMetricsAggregationRuleId("ruleId3");
        aggregationHistogramRangeItemDOList.add(aggregationHistogramRangeItemDO2);
        when(metricsAggregationRuleHandler.listHistogramRangeItemsByRuleIds(Lists.newArrayList("ruleId3"))).thenReturn(aggregationHistogramRangeItemDOList);


        // mock percentile item
        List<AggregationPercentileItemDO> aggregationPercentileItemDOList = new ArrayList<>();
        AggregationPercentileItemDO aggregationPercentileItemDO = new AggregationPercentileItemDO();
        aggregationPercentileItemDO.setMetricsAggregationRuleId("ruleId4");
        aggregationPercentileItemDOList.add(aggregationPercentileItemDO);
        when(metricsAggregationRuleHandler.listPercentileItemsByRuleIds(Lists.newArrayList("ruleId4"))).thenReturn(aggregationPercentileItemDOList);


        ResponseObject responseObject = metricsService.searchMetrics(pageQuery);

        System.out.println("responseObject = "+ JsonUtils.toJsonString(responseObject));
        List<SearchMetricsItemOut> searchMetricsItemOutList = (List<SearchMetricsItemOut>)((PageResult)responseObject.getData()).getItems();
        Assertions.assertEquals(2, searchMetricsItemOutList.size());
        searchMetricsItemOutList.stream().forEach(e -> {
            if(e.getId().equals("metricsId1")){
                Assertions.assertEquals(3,e.getFieldsCount().getCommon().get());
                Assertions.assertEquals(2,e.getFieldsCount().getHistogram().get());
                Assertions.assertEquals(1,e.getFieldsCount().getPercentile().get());
            } else if(e.getId().equals("metricsId2")) {
                Assertions.assertEquals(0,e.getFieldsCount().getCommon().get());
                Assertions.assertEquals(0,e.getFieldsCount().getHistogram().get());
                Assertions.assertEquals(0,e.getFieldsCount().getPercentile().get());
            }
        });

    }

    @Test
    public void testDisplay(){

    }
    @Test
    public void findByMetricsNameLike() {
        when(authService.hasSuchTenant(Mockito.any(), Mockito.any())).thenReturn(false);
        PageQuery<MetricsQuery> pageQuery = new PageQuery<>();
        pageQuery.setTenantId(tenantId);
        MetricsQuery metricsQuery = new MetricsQuery();
        metricsQuery.setMetricsName("metrics_name");
        pageQuery.setQueryPara(metricsQuery);

        ResponseObject result = metricsService.findByMetricsNameLike(pageQuery);
        Assertions.assertEquals(WebCodeEnum.HasNoSuchTenant.getCode(), result.getOperCode());

        when(authService.hasSuchTenant(Mockito.any(), Mockito.any())).thenReturn(true);
        List<MetricsDO> metricsDOList = Lists.newArrayList();
        metricsDOList.add(new MetricsDO());
        when(metricsHandler.findByMetricsNameLike(Mockito.anyString(), Mockito.anyList(), Mockito.anyInt(), Mockito.anyInt())).thenReturn(metricsDOList);
        when(metricsHandler.getCountByMetricsNameLike(Mockito.any(), Mockito.any())).thenReturn(2);
        result = metricsService.findByMetricsNameLike(pageQuery);
        Assertions.assertEquals(result.getStatus(), "success");
    }

    @Test
    public void checkMetricsName() {
        try {
            boolean result = metricsService.checkMetricsName("+_))(&");
            Assertions.assertFalse(result);
        } catch (Exception e) {
            e.printStackTrace();
        }
        boolean result = metricsService.checkMetricsName("test_name");
        Assertions.assertTrue(result);
    }

    @Test
    public void addMetrics_original() {
        MetricsInput metricsInput = buildOriginalMetrics();
        ResponseObject result = metricsService.addMetrics(metricsInput);
        Assertions.assertEquals(WebCodeEnum.HasNoSuchTenant.getCode(), result.getOperCode());

        when(authService.hasSuchTenant(Mockito.any(), Mockito.any())).thenReturn(true);
        result = metricsService.addMetrics(metricsInput);
        Assertions.assertEquals(result.getStatus(), "success");
    }

    @Test
    public void addMetrics_aggregation() {
        MetricsInput metricsInput = Mockito.mock(MetricsInput.class);
        when(metricsInput.getTenantId()).thenReturn("sjj-tenantId1");
        when(metricsInput.getSelfDefinedId()).thenReturn(null);
        when(metricsInput.getTagNames()).thenReturn(Lists.newArrayList("host","cluster"));
        when(metricsInput.getType()).thenReturn(MetricsTypeEnum.AGGREGATION.getValue());
        when(metricsHandler.hasSameMetricsName(Mockito.anyString(),Mockito.anyList())).thenReturn(false);


        when(metricsInput.getMetricsName()).thenReturn("sjj-ut-metricsName1");
        when(metricsInput.getCollectorId()).thenReturn("sjj-ut-collectorId1");

        MetricsAggregationInput metricsAggregationInput = buildMetricsAggregationInput();
        when(metricsInput.getMetricsAggregation()).thenReturn(metricsAggregationInput);

        when(authService.hasSuchTenant(Mockito.any(), Mockito.any())).thenReturn(true);
        ResponseObject result = metricsService.addMetrics(metricsInput);

        Assertions.assertEquals(result.getStatus(), "success");
    }

    private MetricsAggregationInput buildMetricsAggregationInput(){
        MetricsAggregationInput metricsAggregationInput = new MetricsAggregationInput();
        metricsAggregationInput.setAggPeriod(60);
        metricsAggregationInput.setWaitTime(5000);
        metricsAggregationInput.setFilterCondition("clusterId == 'dev'");
        MetricsAggregationRuleComposeInput metricsAggregationRuleComposeInput = new MetricsAggregationRuleComposeInput();
        List<AggregationFunctionRuleInput> aggregationFunctionRuleInputList = new ArrayList<>();
        AggregationFunctionRuleInput aggregationFunctionRuleInput1 = new AggregationFunctionRuleInput();
        aggregationFunctionRuleInput1.setAggField("load1");
        aggregationFunctionRuleInput1.setIsConditioned(0);
        List<AggregationFunctionItemInput> aggregationFunctionItemInputList1 = new ArrayList<>();
        AggregationFunctionItemInput aggregationFunctionItemInput1 = new AggregationFunctionItemInput();
        aggregationFunctionItemInput1.setValue("count");
        aggregationFunctionItemInputList1.add(aggregationFunctionItemInput1);
        AggregationFunctionItemInput aggregationFunctionItemInput2 = new AggregationFunctionItemInput();
        aggregationFunctionItemInput2.setValue("sum");
        aggregationFunctionItemInputList1.add(aggregationFunctionItemInput2);
        aggregationFunctionRuleInput1.setAggTypes(aggregationFunctionItemInputList1);
        aggregationFunctionRuleInputList.add(aggregationFunctionRuleInput1);

        AggregationFunctionRuleInput aggregationFunctionRuleInput2 = new AggregationFunctionRuleInput();
        aggregationFunctionRuleInput2.setAggField("load1");
        aggregationFunctionRuleInput2.setIsConditioned(1);
        aggregationFunctionRuleInput2.setConditionalFieldPrefix("load1_1");
        List<AggregationFunctionItemInput> aggregationFunctionItemInputList2 = new ArrayList<>();
        AggregationFunctionItemInput aggregationFunctionItemInput2_1 = new AggregationFunctionItemInput();
        aggregationFunctionItemInput2_1.setValue("count");
        aggregationFunctionItemInputList2.add(aggregationFunctionItemInput2_1);

        aggregationFunctionRuleInput2.setAggTypes(aggregationFunctionItemInputList2);
        aggregationFunctionRuleInputList.add(aggregationFunctionRuleInput2);
        metricsAggregationRuleComposeInput.setCommon(aggregationFunctionRuleInputList);

        List<AggregationHistogramRuleInput> aggregationHistogramRuleInputList = new ArrayList<>();
        AggregationHistogramRuleInput aggregationHistogramRuleInput1 = new AggregationHistogramRuleInput();
        aggregationHistogramRuleInput1.setAggField("load1");
        List<AggregationHistogramRangeItemInput> aggregationHistogramRangeItemInputList = new ArrayList<>();
        AggregationHistogramRangeItemInput aggregationHistogramRangeItemInput1 = new AggregationHistogramRangeItemInput();
        aggregationHistogramRangeItemInput1.setLowerLimit(10.1);
        aggregationHistogramRangeItemInput1.setUpperLimit(20.1);
        aggregationHistogramRangeItemInput1.setLowerIntervalNotation(1);
        aggregationHistogramRangeItemInput1.setUpperIntervalNotation(0);
        aggregationHistogramRangeItemInput1.setResultLabel("load_10_20");
        aggregationHistogramRangeItemInputList.add(aggregationHistogramRangeItemInput1);

        AggregationHistogramRangeItemInput aggregationHistogramRangeItemInput2 = new AggregationHistogramRangeItemInput();
        aggregationHistogramRangeItemInput2.setLowerLimit(30.0);
        aggregationHistogramRangeItemInput2.setUpperLimit(70.0);
        aggregationHistogramRangeItemInput2.setLowerIntervalNotation(1);
        aggregationHistogramRangeItemInput2.setUpperIntervalNotation(0);
        aggregationHistogramRangeItemInput2.setResultLabel("load_30_70");
        aggregationHistogramRangeItemInputList.add(aggregationHistogramRangeItemInput2);
        aggregationHistogramRuleInput1.setRanges(aggregationHistogramRangeItemInputList);
        aggregationHistogramRuleInputList.add(aggregationHistogramRuleInput1);

        AggregationHistogramRuleInput aggregationHistogramRuleInput2 = new AggregationHistogramRuleInput();
        aggregationHistogramRuleInput2.setAggField("usedTime");
        List<AggregationHistogramRangeItemInput> aggregationHistogramRangeItemInputList2 = new ArrayList<>();
        AggregationHistogramRangeItemInput aggregationHistogramRangeItemInput3 = new AggregationHistogramRangeItemInput();
        aggregationHistogramRangeItemInput3.setLowerLimit(10.1);
        aggregationHistogramRangeItemInput3.setUpperLimit(20.1);
        aggregationHistogramRangeItemInput3.setLowerIntervalNotation(1);
        aggregationHistogramRangeItemInput3.setUpperIntervalNotation(0);
        aggregationHistogramRangeItemInput3.setResultLabel("load_10_20");
        aggregationHistogramRangeItemInputList2.add(aggregationHistogramRangeItemInput3);
        aggregationHistogramRuleInput2.setRanges(aggregationHistogramRangeItemInputList2);
        aggregationHistogramRuleInputList.add(aggregationHistogramRuleInput2);
        metricsAggregationRuleComposeInput.setHistogram(aggregationHistogramRuleInputList);

        List<AggregationPercentileRuleInput> aggregationPercentileRuleInputList = new ArrayList<>();
        AggregationPercentileRuleInput aggregationPercentileRuleInput = new AggregationPercentileRuleInput();
        aggregationPercentileRuleInput.setAggField("load1");
        List<AggregationPercentileItemInput> aggregationPercentileItemInputList = new ArrayList<>();
        AggregationPercentileItemInput aggregationPercentileItemInput = new AggregationPercentileItemInput();
        aggregationPercentileItemInput.setValue(95);
        aggregationPercentileItemInputList.add(aggregationPercentileItemInput);
        aggregationPercentileRuleInput.setPercentileValues(aggregationPercentileItemInputList);
        aggregationPercentileRuleInputList.add(aggregationPercentileRuleInput);
        metricsAggregationRuleComposeInput.setPercentile(aggregationPercentileRuleInputList);
        metricsAggregationInput.setMetricsAggregationRuleCompose(metricsAggregationRuleComposeInput);
        return metricsAggregationInput;
    }

    @Test
    public void editMetrics_original() {

    }

    @Test
    public void editMetrics_aggregation() {
    }

    private MetricsInput buildAggregationMetrics() {
        MetricsInput metricsInput = buildCommonMetricsInput();
        metricsInput.setType(MetricsTypeEnum.AGGREGATION.getValue());
        MetricsAggregationInput metricsAggregationInput = new MetricsAggregationInput();
        metricsAggregationInput.setAggPeriod(60);
        metricsAggregationInput.setFilterCondition("cloudType = 'aws'");
        metricsAggregationInput.setWaitTime(500);
        List<MetricsAggregationRuleInput> metricsAggregationRuleInputList = Lists.newArrayList();
        MetricsAggregationRuleInput metricsAggregationRuleInput = new MetricsAggregationRuleInput();
        metricsAggregationRuleInput.setAggField("us");
        metricsAggregationRuleInput.setAggTypes(Lists.newArrayList("sum", "max"));
        metricsAggregationRuleInputList.add(metricsAggregationRuleInput);
        // metricsAggregationInput.setMetricsAggregationRuleList(metricsAggregationRuleInputList);
        metricsInput.setMetricsAggregation(metricsAggregationInput);
        return metricsInput;
    }

    private MetricsInput buildOriginalMetrics() {
        MetricsInput metricsInput = buildCommonMetricsInput();
        metricsInput.setType(MetricsTypeEnum.ORIGINAL.getValue());
        return metricsInput;
    }

    private MetricsInput buildCommonMetricsInput() {
        MetricsInput metricsInput = new MetricsInput();
        metricsInput.setMetricsName("testCpuInfo");
        List<String> tagNames = Lists.newArrayList();
        tagNames.add("hostId");
        tagNames.add("regionId");
        metricsInput.setTagNames(tagNames);
        List<MetricsFieldInput> metricsFieldInputList = Lists.newArrayList();
        MetricsFieldInput metricsFieldInput = new MetricsFieldInput();
        metricsFieldInput.setFieldName("us");
        metricsFieldInput.setFieldType(MetricsFieldTypeEnum.number.getValue());
        metricsFieldInputList.add(metricsFieldInput);
        metricsInput.setMetricsFieldList(metricsFieldInputList);
        metricsInput.setCollectorId(collectorId);
        return metricsInput;
    }

    @Test
    public void getMetricsById_original() {
        ResponseObject result = metricsService.getMetricsById(userId, tenantId, "/out/metric/getMetricDefinitionById",metricsId);
        when(authService.hasSuchMetrics(Mockito.any(), Mockito.any())).thenReturn(false);
        Assertions.assertEquals(WebCodeEnum.HasNoSuchMetrics.getCode(), result.getOperCode());

        when(authService.hasSuchMetrics(Mockito.any(), Mockito.any())).thenReturn(true);

        MetricsDO metricsDO = buildMetricsDO();
        when(metricsHandler.getMetricsById(metricsId)).thenReturn(metricsDO);
        List<MetricsFieldDO> metricsFieldDOList = buildMetricsFieldDOList(metricsDO);
        when(metricsHandler.listFieldsByMetricsIds(Mockito.anyList())).thenReturn(metricsFieldDOList);

        result = metricsService.getMetricsById(userId, tenantId, "/out/metric/getMetricDefinitionById",metricsId);
        Assertions.assertEquals(result.getStatus(), "success");
    }

    @Test
    public void getMetricsById_aggregation() {
        when(authService.hasSuchMetrics(Mockito.any(), Mockito.any())).thenReturn(true);

        MetricsDO metricsDO = buildMetricsDO();
        metricsDO.setType(MetricsTypeEnum.AGGREGATION.getValue());
        when(metricsHandler.getMetricsById(metricsId)).thenReturn(metricsDO);
        List<MetricsFieldDO> metricsFieldDOList = buildMetricsFieldDOList(metricsDO);
        when(metricsHandler.listFieldsByMetricsIds(Mockito.anyList())).thenReturn(metricsFieldDOList);

        MetricsAggregationDO metricsAggregationDO = buildMetricsAggregationDO(metricsDO);
        when(metricsAggregationHandler.getByMetricsId(metricsId)).thenReturn(metricsAggregationDO);
        List<MetricsAggregationRuleDO> metricsAggregationRuleDOList = buildMetricsAggregationRuleDOList(metricsAggregationDO);
        when(metricsAggregationRuleHandler.listRulesByAggIds(Mockito.anyList())).thenReturn(metricsAggregationRuleDOList);

        ResponseObject result = metricsService.getMetricsById(userId, tenantId, "/out/metric/getMetricDefinitionById",metricsId);
        Assertions.assertEquals(result.getStatus(), "success");
    }

    private MetricsDO buildMetricsDO() {
        MetricsDO metricsDO = new MetricsDO();
        metricsDO.setId(metricsId);
        metricsDO.setMetricsName("metricsName_test_addOriginalMetrics");
        metricsDO.setTenantId("tenantId_test");
        metricsDO.setTagNames("test_cloud_type,test_host_name");
        metricsDO.setType(MetricsTypeEnum.ORIGINAL.getValue());
        metricsDO.setCreateTime(new Date());
        metricsDO.setModifyTime(new Date());
        return metricsDO;
    }

    private List<MetricsFieldDO> buildMetricsFieldDOList(MetricsDO metricsDO) {
        List<MetricsFieldDO> metricsFieldDOList = Lists.newArrayList();
        MetricsFieldDO metricsFieldDO = new MetricsFieldDO();
        metricsFieldDO.setId(IdUtils.generateId());
        metricsFieldDO.setMetricsId(metricsDO.getId());
        metricsFieldDO.setFieldName("test_fieldName");
        metricsFieldDO.setFieldType(MetricsFieldTypeEnum.number.getValue());
        metricsFieldDO.setCreateTime(new Date());
        metricsFieldDO.setModifyTime(new Date());
        metricsFieldDOList.add(metricsFieldDO);
        return metricsFieldDOList;
    }

    private MetricsAggregationDO buildMetricsAggregationDO(MetricsDO metricsDO) {
        MetricsAggregationDO metricsAggregationDO = new MetricsAggregationDO();
        metricsAggregationDO.setId(IdUtils.generateId());
        metricsAggregationDO.setMetricsId(metricsDO.getId());
        metricsAggregationDO.setAggPeriod(60);
        metricsAggregationDO.setFilterCondition("test_fieldName == 'test_666'");
        metricsAggregationDO.setWaitTime(500);
        metricsAggregationDO.setCreateTime(new Date());
        metricsAggregationDO.setModifyTime(new Date());
        return metricsAggregationDO;
    }

    private List<MetricsAggregationRuleDO> buildMetricsAggregationRuleDOList(MetricsAggregationDO metricsAggregationDO) {
        List<MetricsAggregationRuleDO> metricsAggregationRuleDOList = Lists.newArrayList();
        MetricsAggregationRuleDO metricsAggregationRuleDO = new MetricsAggregationRuleDO();
        metricsAggregationRuleDO.setId(IdUtils.generateId());
        metricsAggregationRuleDO.setAggId(metricsAggregationDO.getId());
        metricsAggregationRuleDO.setAggField("test_aggField");
        metricsAggregationRuleDO.setAggTypes("sum,max,min");
        metricsAggregationRuleDO.setCreateTime(new Date());
        metricsAggregationRuleDO.setModifyTime(new Date());
        metricsAggregationRuleDOList.add(metricsAggregationRuleDO);
        return metricsAggregationRuleDOList;
    }

    private MetricsInput copyFrom(MetricsOut metricsOut) {
        MetricsInput metricsInput = new MetricsInput();
        BeanUtils.copyProperties(metricsOut, metricsInput);
        BeanUtils.copyProperties(metricsOut.getMetricsAggregation(),metricsInput.getMetricsAggregation());
        BeanUtils.copyProperties(metricsOut.getMetricsAggregation().getMetricsAggregationRuleCompose(),metricsInput.getMetricsAggregation().getMetricsAggregationRuleCompose());


        metricsInput.getMetricsAggregation().getMetricsAggregationRuleCompose().setCommon(new ListUtils<AggregationFunctionRuleInput>().copyList(metricsOut.getMetricsAggregation().getMetricsAggregationRuleCompose().getCommon(),AggregationFunctionRuleInput.class));
        metricsInput.getMetricsAggregation().getMetricsAggregationRuleCompose().setHistogram(new ListUtils<AggregationHistogramRuleInput>().copyList(metricsOut.getMetricsAggregation().getMetricsAggregationRuleCompose().getHistogram(),AggregationHistogramRuleInput.class));
        metricsInput.getMetricsAggregation().getMetricsAggregationRuleCompose().setPercentile(new ListUtils<AggregationPercentileRuleInput>().copyList(metricsOut.getMetricsAggregation().getMetricsAggregationRuleCompose().getPercentile(),AggregationPercentileRuleInput.class));
        int commonSize = metricsInput.getMetricsAggregation().getMetricsAggregationRuleCompose().getCommon().size();
        for(int i=0;i<commonSize;i++){
            AggregationFunctionRuleInput input = metricsInput.getMetricsAggregation().getMetricsAggregationRuleCompose().getCommon().get(i);
            AggregationFunctionRuleOutput output = metricsOut.getMetricsAggregation().getMetricsAggregationRuleCompose().getCommon().get(i);
            BeanUtils.copyProperties(output, input);
            input.setAggTypes(new ListUtils<AggregationFunctionItemInput>().copyList(output.getAggTypes(),AggregationFunctionItemInput.class));

        }
        int histogramSize = metricsInput.getMetricsAggregation().getMetricsAggregationRuleCompose().getHistogram().size();
        for(int i=0;i<histogramSize;i++){
            AggregationHistogramRuleInput input = metricsInput.getMetricsAggregation().getMetricsAggregationRuleCompose().getHistogram().get(i);
            AggregationHistogramRuleOutput output = metricsOut.getMetricsAggregation().getMetricsAggregationRuleCompose().getHistogram().get(i);
            BeanUtils.copyProperties(output, input);
            input.setRanges(new ListUtils<AggregationHistogramRangeItemInput>().copyList(output.getRanges(),AggregationHistogramRangeItemInput.class));

        }
        int percentileSize = metricsInput.getMetricsAggregation().getMetricsAggregationRuleCompose().getPercentile().size();
        for(int i=0;i<percentileSize;i++){
            AggregationPercentileRuleInput input = metricsInput.getMetricsAggregation().getMetricsAggregationRuleCompose().getPercentile().get(i);
            AggregationPercentileRuleOutput output = metricsOut.getMetricsAggregation().getMetricsAggregationRuleCompose().getPercentile().get(i);
            BeanUtils.copyProperties(output, input);
            input.setPercentileValues(new ListUtils<AggregationPercentileItemInput>().copyList(output.getPercentileValues(),AggregationPercentileItemInput.class));

        }
//
//        ListUtils<AggregationFunctionRuleInput> utils1 = new ListUtils<>();
//        List<AggregationHistogramRuleInput> aggregationHistogramRuleInputList = Lists.newArrayList();
//        utils1.copyList(metricsOut.getMetricsAggregation().getMetricsAggregationRuleCompose().getHistogram(), aggregationHistogramRuleInputList, AggregationHistogramRuleInput.class);
//        metricsInput.getMetricsAggregation().getMetricsAggregationRuleCompose().setCommon(aggregationFunctionRuleInputsList);

        return metricsInput;

    }

    @Test
    public void t3(){
        String jsonStr = "{\"collectorId\":\"sjj-ut-collectorId1\",\"id\":\"5ab00d33-c42c-48c5-a2e6-6476017a514a\",\"metricsAggregation\":{\"aggPeriod\":60,\"filterCondition\":\"clusterId == 'dev'\",\"id\":\"d356c646-6f32-4ce5-af10-2b543ecc83ce\",\"metricsAggregationRuleCompose\":{\"common\":[{\"aggField\":\"load1\",\"aggId\":\"d356c646-6f32-4ce5-af10-2b543ecc83ce\",\"aggTypes\":[{\"id\":\"b9ea6e11-08f3-4a02-b16c-71e2005f9b6d\",\"value\":\"count\"}],\"conditionalFieldPrefix\":\"load1_1\",\"filterCondition\":\"\",\"id\":\"d39ae1ad-a103-40af-8fae-a6f8b638d32d\",\"isConditioned\":1,\"spcCompute\":1},{\"aggField\":\"load1\",\"aggId\":\"d356c646-6f32-4ce5-af10-2b543ecc83ce\",\"aggTypes\":[{\"id\":\"1c3eead6-f03d-4549-90fb-18abfb90821b\",\"value\":\"count\"},{\"id\":\"057d3894-c717-4279-8232-4d34727a088d\",\"value\":\"sum\"}],\"conditionalFieldPrefix\":\"\",\"filterCondition\":\"b == c\",\"id\":\"e4c79f46-07ef-48fb-b3a9-c5300f2dc24e\",\"isConditioned\":0,\"spcCompute\":1}],\"histogram\":[{\"aggField\":\"load1\",\"aggId\":\"d356c646-6f32-4ce5-af10-2b543ecc83ce\",\"filterCondition\":\"\",\"id\":\"9c6c611d-a6b6-44ee-b07e-82076ece451c\",\"ranges\":[{\"id\":\"6188a713-8a70-4c37-ab0f-e599fe753baa\",\"lowerIntervalNotation\":1,\"lowerLimit\":20.1,\"resultLabel\":\"load_20_30\",\"upperIntervalNotation\":0,\"upperLimit\":30.1},{\"id\":\"8b7e510b-fe33-44eb-9970-fdbdc2ee93d7\",\"lowerIntervalNotation\":1,\"lowerLimit\":30.1,\"resultLabel\":\"load_30_70\",\"upperIntervalNotation\":0,\"upperLimit\":70.0}],\"spcCompute\":0},{\"aggField\":\"usedTime\",\"aggId\":\"d356c646-6f32-4ce5-af10-2b543ecc83ce\",\"filterCondition\":\"\",\"id\":\"4477dfee-040d-41a5-ae3d-70ae93eb4e4c\",\"ranges\":[{\"id\":\"8fff03c6-2360-4e75-937b-9da85df35581\",\"lowerIntervalNotation\":1,\"lowerLimit\":20.1,\"resultLabel\":\"load_20_21\",\"upperIntervalNotation\":0,\"upperLimit\":21.2},{\"id\":\"b82919db-d37a-4d61-8075-92b3f8091fc8\",\"lowerIntervalNotation\":1,\"lowerLimit\":0.1,\"resultLabel\":\"load_0_20\",\"upperIntervalNotation\":0,\"upperLimit\":20.1}],\"spcCompute\":0}],\"percentile\":[{\"aggField\":\"load1\",\"aggId\":\"d356c646-6f32-4ce5-af10-2b543ecc83ce\",\"filterCondition\":\"\",\"id\":\"7382f3cb-ee87-41b7-95f5-a4eccb701396\",\"percentileValues\":[{\"id\":\"a3193727-af45-4408-a966-353d95efed47\",\"value\":15},{\"id\":\"7b718e0c-d595-4fd3-92a7-2ad161feb600\",\"value\":95}],\"spcCompute\":1}]},\"metricsId\":\"5ab00d33-c42c-48c5-a2e6-6476017a514a\",\"spcSamplingWeight\":2,\"spcStatisticalPeriod\":1,\"waitTime\":5000},\"metricsFieldList\":[{\"fieldName\":\"load1.count\",\"fieldType\":1,\"id\":\"b3f9119b-ade1-42c7-a80d-dbb3acf29777\",\"metricsId\":\"5ab00d33-c42c-48c5-a2e6-6476017a514a\"},{\"fieldName\":\"load1.p15\",\"fieldType\":1,\"id\":\"8f21a7b6-90ac-461b-837a-3b2f01398350\",\"metricsId\":\"5ab00d33-c42c-48c5-a2e6-6476017a514a\"},{\"fieldName\":\"load1.p95\",\"fieldType\":1,\"id\":\"14539d81-cb72-492b-8861-7cb8ec59215f\",\"metricsId\":\"5ab00d33-c42c-48c5-a2e6-6476017a514a\"},{\"fieldName\":\"load1.sum\",\"fieldType\":1,\"id\":\"5f966d7c-ea1e-4adf-80a4-1bcd9b29418f\",\"metricsId\":\"5ab00d33-c42c-48c5-a2e6-6476017a514a\"},{\"fieldName\":\"load1_1.count\",\"fieldType\":1,\"id\":\"32ea080c-0a0e-4db8-acd8-85440802c90e\",\"metricsId\":\"5ab00d33-c42c-48c5-a2e6-6476017a514a\"},{\"fieldName\":\"load_0_20\",\"fieldType\":1,\"id\":\"6051bd65-6482-4ff3-bc92-4088cad83966\",\"metricsId\":\"5ab00d33-c42c-48c5-a2e6-6476017a514a\"},{\"fieldName\":\"load_20_21\",\"fieldType\":1,\"id\":\"e7b86475-c150-4791-820c-734143b41208\",\"metricsId\":\"5ab00d33-c42c-48c5-a2e6-6476017a514a\"},{\"fieldName\":\"load_20_30\",\"fieldType\":1,\"id\":\"5a277a32-b2f7-4dd5-8ee1-bed0e7624376\",\"metricsId\":\"5ab00d33-c42c-48c5-a2e6-6476017a514a\"},{\"fieldName\":\"load_30_70\",\"fieldType\":1,\"id\":\"61e77eb6-04bb-4b17-9f83-7b755c72f6c5\",\"metricsId\":\"5ab00d33-c42c-48c5-a2e6-6476017a514a\"}],\"metricsName\":\"sjj-ut-metricsName-20210407-1617784884246\",\"tagNames\":[\"host\",\"cluster\"],\"type\":1}\n";
        MetricsOut metricsOut = JsonUtils.toObject(jsonStr, MetricsOut.class);
        System.out.println("metricsOut = " + JsonUtils.toJsonString(metricsOut));
        MetricsInput metricsInput = copyFrom(metricsOut);
        System.out.println("metricsInput = " + JsonUtils.toJsonString(metricsInput));
    }

    @Test
    public void t1(){
        Map<String, List<String>> map = new HashMap<>();
        map.getOrDefault("a",Lists.newArrayList()).add("vv");
        System.out.println(JsonUtils.toJsonString(map.get("a")));
        map.computeIfAbsent("b",e -> Lists.newArrayList()).add("v");
        System.out.println(JsonUtils.toJsonString(map.get("b")));
    }

    @Test
    public void t2(){
        //exceptionRule.expect(NoSuchElementException.class);
        List<String> list = new ArrayList<>();
        list.add("a");
        assertThrows(NoSuchElementException.class, ()->list.stream().forEach(e -> {
            list.stream().filter(t -> t.equals("b")).findFirst().get();
        }));
        System.out.println("unreachable code");
    }

    @Test
    public void t4(){
        System.out.println(StringUtils.isNotEmpty(null));
        System.out.println(StringUtils.isNotBlank(null));
        System.out.println(StringUtils.isNotEmpty(""));
        System.out.println(StringUtils.isNotBlank(""));
    }

    @Test
    public void t5(){
        System.out.println(5.7/2);
    }

    @Test
    public void t6(){
        Double d1 = 1.234000001;
        Double d2 = 1.234000001;
        System.out.println(d1.equals(d2));
    }



    @Data
    private static class Cat{
        int age;
    }
    @Test
    public void testIntegerEqual(){
        Integer type = 2;
        if(type == MetricsTypeEnum.ORIGINAL.getValue()){
            System.out.println("true");
        }else {
            System.out.println("false");
        }
    }

    @Test
    public void testNullValueInMap(){
        Map<String,String> map = new HashMap<>();
        map.put("a",null);
        map.put("b",null);
    }

    @Test
    public void testEmptyStrToList(){
        String str = "";
        List<String> list = Arrays.asList(StringUtils.split(str,COMMA_SPLIT));
        System.out.println(CollectionUtils.isEmpty(list));
    }

    @Test
    public void testMatcherNull(){

        Matcher expressionMatcher = CUSTOM_FIELD_EXPRESSION_PATTERN.matcher(null);
        System.out.println(expressionMatcher.find());

    }

    @Test
    public void testStreamToMapNull(){
        List<People> peopleList = Lists.newArrayList(new People("a",""),new People("b",null));
        Map<String,String> map = Instance.ofNullable(peopleList).stream().collect(
                Collectors.toMap(e -> e.getName(), e -> Optional.ofNullable(e.getNickName()).orElse(StringUtils.EMPTY)));
        System.out.println(JsonUtils.toJsonString(map));

    }


    @Data
    @AllArgsConstructor
    private static class People{
        String name;
        String nickName;
    }

    @Test
    public void testAnyMatch(){
        List<String> list = Lists.newArrayList("a","b");
        boolean isTagInvalid = Instance.ofNullable(list).stream().anyMatch(field->{
            return false;
        });
        System.out.println(isTagInvalid);
    }

    @Data
    private static class Dog{
        public int age;
    }


    public void testObjectArr(String format, Object... obj){
       // log.info(JsonUtils.toJsonString(obj));

        log.info(format,obj);
    }

    public static void logError(String format, Object... obj){
        log.error(format,obj);
    }
    private static  final DecimalFormat format= new DecimalFormat("#.000");

    @Test
    public void testRatio(){
        double value1 = 60.0;
        double value2 = 60.0;
        Map<String,Double> map = new HashMap<>();
        map.put("aa",Double.parseDouble(format.format(value1/value2*100)));
        System.out.println(JsonUtils.toJsonString(map));
    }

    @Test
    public void testMatch(){
        Metrics metrics = new Metrics();
        List<MetricsField> fieldList = Lists.newArrayList();
        MetricsField metricsField1 = new MetricsField();
        metricsField1.setFieldName("orginUri.count");
        metricsField1.setFieldValue(14d);
        //fieldList.add(metricsField1);

        MetricsField metricsField2 = new MetricsField();
        metricsField2.setFieldName("orginUri.sum");
        metricsField2.setFieldValue(14d);
        fieldList.add(metricsField2);

        MetricsField metricsField3 = new MetricsField();
        metricsField3.setFieldName("field3");
        metricsField3.setFieldValue(16);
        fieldList.add(metricsField3);

        metrics.setFields(fieldList);
        //String condition = "(orginUri.count > 15 && orginUri.sum > 15 && field3 > 15) || (orginUri.sum == 14)";
        String condition = "orginUri.sum > 1 && a.b == 1";
        System.out.println(match(metrics,condition));
    }
    private boolean match(Metrics metrics, String condition){
        if(metrics == null || metrics.getFields() == null){
            return false;
        }
        if(StringUtils.isEmpty(condition)){
            return true;
        }
        Map<String,Object> field2ValueMap = new HashMap<>(metrics.getFields().size());
        metrics.getFields().forEach(e -> {
            String key = e.getFieldName().replaceAll("\\.","_");
            field2ValueMap.put(key,e.getFieldValue());
            field2ValueMap.put(key,e.getFieldValue());
        });
        condition = condition.replaceAll("\\.","_");
        try {
            if(!((boolean) AviatorEvaluator.execute(condition,field2ValueMap,true))) {
                return  false;
            }
            return  true;
        }catch (Exception e){
            logError("filter the metrics result error, metrics = {},condition = {}",metrics,condition,e);
            return  false;
        }
    }

}
