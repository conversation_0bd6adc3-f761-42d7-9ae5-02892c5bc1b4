package us.zoom.cube.site.infra;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * @author: canyon.li
 * @date: 2024/09/02
 **/
@Service
public class LogSampling {

    private static ApplicationContext applicationContext;

    @Autowired
    public void initialize(ApplicationContext ac) {
        applicationContext = ac;
    }

    private static Long infoSamplingCount;

    private static Long warnSamplingCount;

    private static Long errorSamplingCount;

    private static final Long DEFAULT_INFO_SAMPLE_COUNT = 10000L;

    private static final Long DEFAULT_WARN_SAMPLE_COUNT = 1000L;

    private static final Long DEFAULT_ERROR_SAMPLE_COUNT = 1000L;

    public static long getInfoSamplingCount() {
        if (infoSamplingCount != null) {
            return infoSamplingCount;
        }
        infoSamplingCount = Optional.ofNullable(applicationContext)
                .map(ApplicationContext::getEnvironment)
                .map(e -> e.getProperty("log.sampling.count.info"))
                .map(Long::parseLong)
                .orElse(DEFAULT_INFO_SAMPLE_COUNT);
        return infoSamplingCount;
    }

    public static long getWarnSamplingCount() {
        if (warnSamplingCount != null) {
            return warnSamplingCount;
        }
        warnSamplingCount = Optional.ofNullable(applicationContext)
                .map(ApplicationContext::getEnvironment)
                .map(e -> e.getProperty("log.sampling.count.warn"))
                .map(Long::parseLong)
                .orElse(DEFAULT_WARN_SAMPLE_COUNT);
        return warnSamplingCount;
    }

    public static long getErrorSamplingCount() {
        if (errorSamplingCount != null) {
            return errorSamplingCount;
        }
        errorSamplingCount = Optional.ofNullable(applicationContext)
                .map(ApplicationContext::getEnvironment)
                .map(e -> e.getProperty("log.sampling.count.error"))
                .map(Long::parseLong)
                .orElse(DEFAULT_ERROR_SAMPLE_COUNT);
        return errorSamplingCount;
    }

}