package us.zoom.cube.site.core.schedulery;

import com.zoom.op.monitor.domain.DerivedMetric;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import us.zoom.cube.lib.common.TaskStatusEnum;
import us.zoom.cube.lib.utils.JsonUtils;
import us.zoom.cube.scheduler.lib.enums.*;
import us.zoom.cube.scheduler.lib.model.SchedulerJobInfo;
import us.zoom.cube.site.api.intercept.AuthInterceptor;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2022/12/20
 */
@Slf4j
@Component
public class InnerReferenceScheduleHandler {

    public static final String DERIVED_METRIC_BUSINESS_TYPE = "DERIVED_METRIC";

    @Value("${alarm.pull-query.topic:cube_pull_query_alarm}")
    private String alarmPullQueryTopic;

    @Autowired
    private ScheduleJobInfoHandler scheduleJobInfoHandler;

    public void addJobForDerivedMetric(DerivedMetric derivedMetric, boolean enableScheduler) {
        try {
            if (null == derivedMetric.getCheckInterval()) {
                log.warn("The derived metric [{}] check interval is null, don't need to add shedule job.", derivedMetric.getName());
                return;
            }
            SchedulerJobInfo schedulerJobInfo = buildNewSchedulerJobInfo(derivedMetric, enableScheduler);
            scheduleJobInfoHandler.addJobInfo(schedulerJobInfo);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public void updateJobForDerivedMetric(DerivedMetric derivedMetric, boolean enableScheduler) {
        try {
            SchedulerJobInfo maybeExistJonInfo = scheduleJobInfoHandler.findByRefInfo(derivedMetric.getId(), DERIVED_METRIC_BUSINESS_TYPE);
            if (null != maybeExistJonInfo) {
                if (null == derivedMetric.getCheckInterval()) {
                    scheduleJobInfoHandler.deleteByRefInfo(derivedMetric.getId(), DERIVED_METRIC_BUSINESS_TYPE);
                } else {
                    SchedulerJobInfo existJonInfo = updateSchedulerJobInfo(maybeExistJonInfo, derivedMetric, enableScheduler);
                    scheduleJobInfoHandler.updateJobInfo(existJonInfo);
                }
            } else {
                if (null != derivedMetric.getCheckInterval()) {
                    SchedulerJobInfo newJobInfo = buildNewSchedulerJobInfo(derivedMetric, enableScheduler);
                    try {
                        scheduleJobInfoHandler.addJobInfo(newJobInfo);
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public void deleteJobForDerivedMetric(String derivedMetricId) {
        scheduleJobInfoHandler.deleteByRefInfo(derivedMetricId, DERIVED_METRIC_BUSINESS_TYPE);
    }

    private SchedulerJobInfo buildNewSchedulerJobInfo(DerivedMetric derivedMetric, boolean enableScheduler) {
        SchedulerJobInfo schedulerJobInfo = new SchedulerJobInfo();
        schedulerJobInfo.setBusiType(DERIVED_METRIC_BUSINESS_TYPE);
        schedulerJobInfo.setScheduleType(ScheduleType.FIXED_RATE);
        schedulerJobInfo.setTriggerStatus(TriggerStatus.Normal);
        schedulerJobInfo.setChannelMedia(ChannelMedia.AsyncMQ);
        schedulerJobInfo.setExecutorType(ExecutorType.fire_and_forget);
        schedulerJobInfo.setExecutorInfoId(null);
        schedulerJobInfo.setMisfireStrategy(MisfireStrategy.EXECUTE);
        schedulerJobInfo.setTimeRangeEnabled(false);
        schedulerJobInfo.setTimeRangeAlign(true);
        schedulerJobInfo.setTriggerTimeoutRetryCount(0);
        schedulerJobInfo.setTriggerAsyncmqTopic(alarmPullQueryTopic);
        schedulerJobInfo.setTriggerExecutorUrl(null);
        schedulerJobInfo.setCsmsApp(null);
        schedulerJobInfo.setExecutorBlockStrategy(ExecutorBlockStrategy.SERIAL_EXECUTION);
        schedulerJobInfo.setExecutorTimeout(0);
        schedulerJobInfo.setTriggerRouteStrategy(TriggerRouteStrategy.RANDOM);
        schedulerJobInfo.setTurnBackOffsetEnable(true);

        updateSchedulerJobInfo(schedulerJobInfo, derivedMetric, enableScheduler);
        return schedulerJobInfo;
    }

    private SchedulerJobInfo updateSchedulerJobInfo(SchedulerJobInfo schedulerJobInfo, DerivedMetric derivedMetric, boolean enableSchedule) {
        schedulerJobInfo.setName(DERIVED_METRIC_BUSINESS_TYPE + "_" + derivedMetric.getName());
        schedulerJobInfo.setTenantId(AuthInterceptor.getTenantId());
        schedulerJobInfo.setRefId(derivedMetric.getId());
        schedulerJobInfo.setScheduleConf(derivedMetric.getCheckInterval().toString());
        schedulerJobInfo.setEnabled(enableSchedule);
        schedulerJobInfo.setTriggerInitOffsetSeconds(Optional.of(derivedMetric.getQueryTimeOffset()).orElse(0));

        schedulerJobInfo.setCreator(derivedMetric.getCreator());
        schedulerJobInfo.setEditor(derivedMetric.getEditor());
        schedulerJobInfo.setCreateTime(derivedMetric.getCreateTime());
        schedulerJobInfo.setUpdateTime(derivedMetric.getModifyTime());

        schedulerJobInfo.setJobParam(JsonUtils.toJsonString(derivedMetric));

        return schedulerJobInfo;
    }

}
