package us.zoom.cube.site.lib.output;

import java.util.List;

public class PageResult<T> {


    private int total;
    private List<T> items;

    public PageResult(List<T> items){
        this.items=items;
    }

    public PageResult(int total, List<T> items) {
        this.total = total;
        this.items = items;
    }

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }

    public List<T> getItems() {
        return items;
    }

    public void setItems(List<T> items) {
        this.items = items;
    }
}
