package us.zoom.cube.site.core.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import us.zoom.cube.site.biz.SysParaService;
import us.zoom.cube.site.core.TenantHandler;
import us.zoom.cube.site.core.auth.AuthHandler;
import us.zoom.infra.clickhouse.*;
import us.zoom.infra.dao.model.*;
import us.zoom.infra.dao.service.*;
import us.zoom.infra.enums.AggregationOperatorEnum;

import java.util.*;
import java.util.stream.Collectors;

import static us.zoom.op.monitor.definition.parsers.MetadataParser.SEPARATOR_COMMA;

/**
 * <AUTHOR>
 * @date 2022-12-16 10:11
 */
@Component
@Slf4j
public class PiiTableCacheLoader implements CacheLoader {

    @Autowired
    private CollectorFieldDAO collectorFieldDAO;

    @Autowired
    private CollectorMetricsDAO collectorMetricsDAO;

    @Autowired
    private MetricsDAO metricsDAO;

    @Autowired
    private MetricsFieldDAO metricsFieldDAO;

    @Autowired
    private TenantHandler tenantHandler;

    @Autowired
    private AuthHandler authHandler;


    @Autowired
    private ClickhouseHandlerFactory clickhouseHandlerFactory;

    @Autowired
    private SysParaService sysParaService;


    private Set<ChTable> piiTables = new HashSet<>();

    private Map<ChTable, Set<String>> piiTableFields = new HashMap<>();


    public Set<ChTable> getPiiTables() {
        return piiTables;
    }

    @Override
    public void load() {
        loadPiiTables();
    }

    public boolean auth(ChTable t, String userId, String realIp) {
        if (piiTables.contains(t)) {
            String dbName = ChNameDecoder.decode(t.getDbName());
            dbName = tenantHandler.matchInfluxDbNameToTenantName(dbName, false);
            boolean userAccess = authHandler.hasPiiAuth(userId, dbName);
            boolean fromCN = isFromCN(realIp);
            return userAccess && !fromCN;
        }
        return true;
    }

    public boolean isFromCN(String realIp) {
        Set<String> cnOutboundFirewall = sysParaService.getCNOutboundFirewall();
        Set<String> cnFireWallFromProp = clickhouseHandlerFactory.getCnFireWall();
        cnOutboundFirewall.addAll(cnFireWallFromProp);
        boolean fromCN = cnOutboundFirewall.contains(realIp);
        return fromCN;
    }

    public boolean existPii(ChTable t) {
        return piiTables.contains(t);
    }

    public Set<String> getPiiField(ChTable t) {
        return piiTableFields.get(t);
    }


    private void loadPiiTables() {
        try {
            log.info("Begin to load PII table cache");
            List<CollectorFieldDO> piiFields = collectorFieldDAO.getPiiFields();
            Set<ChTable> piiTable = new HashSet<>();
            Map<String, List<CollectorFieldDO>> piiCollectors = piiFields.stream().collect(Collectors.groupingBy(CollectorFieldDO::getCollectorId));
            if (CollectionUtils.isEmpty(piiFields)) {
                log.info("No PII in the system.");
                this.piiTables = piiTable;
                return;
            }
            List<CollectorMetricsDO> collectorMetricsDOS = collectorMetricsDAO.listMetricsCollectorByCollectorIds(piiFields.stream().map(CollectorFieldDO::getCollectorId).collect(Collectors.toList()));
            if (CollectionUtils.isEmpty(collectorMetricsDOS)) {
                log.info("No collectorMetricsDOS in the system, but have collector field, please check following field id{}", piiFields.stream().map(CollectorFieldDO::getCollectorId).collect(Collectors.toList()));
                this.piiTables = piiTable;
                return;
            }

            Map<String, MetricsDO> metricId2Metric = metricsDAO
                    .listMetricsByMetricsIds(collectorMetricsDOS.stream().map(CollectorMetricsDO::getMetricsId).collect(Collectors.toList()))
                    .stream().collect(Collectors.toMap(MetricsDO::getId, u -> u, (a, b) -> b));
            for (CollectorMetricsDO cdo : collectorMetricsDOS) {
                String collectorId = cdo.getCollectorId();
                String metricsId = cdo.getMetricsId();
                try {
                    List<CollectorFieldDO> piiCollectorFields = piiCollectors.get(collectorId);
                    Set<String> piiCollectorFieldsName = piiCollectorFields.stream().map(CollectorFieldDO::getTargetField).collect(Collectors.toSet());
                    Set<String> aggPiiFields = new HashSet<>();
                    for (String s : piiCollectorFieldsName) {
                        for (AggregationOperatorEnum a : AggregationOperatorEnum.values()) {
                            aggPiiFields.add(String.format("%s.%s", s, a.name()));
                        }
                    }
                    piiCollectorFieldsName.addAll(aggPiiFields);
                    MetricsDO metric = metricId2Metric.get(metricsId);
                    if (metric == null) {
                        log.warn("Metric id={} is null", metricsId);
                        continue;
                    }
                    String tagNames = metric.getTagNames();
                    Set<String> metricColumns = new HashSet<>();
                    if (!StringUtils.isEmpty(tagNames)) {
                        metricColumns.addAll(Arrays.asList(tagNames.split(SEPARATOR_COMMA)));
                    }
                    metricColumns.addAll(metricsFieldDAO.listFieldByMetricsId(metricsId).stream().map(MetricsFieldDO::getFieldName).collect(Collectors.toSet()));
                    boolean metricHasPii = !Collections.disjoint(piiCollectorFieldsName, metricColumns);
                    if (metricHasPii) {
                        TenantDO tenant = tenantHandler.getTenantByIdFromCache(metric.getTenantId());
                        if(tenant == null) {continue;}
                        //original metrics name
                        piiTable.add(new ChTable(tenant.getName(), metric.getMetricsName()));
                        //original metrics name and colloctor pii field
                        piiTableFields.put(new ChTable(tenant.getName(), metric.getMetricsName()), piiCollectorFieldsName);
                        //clickhouse table name
                        String chDB = ClickhouseSqlUtil.toClickhouseName(tenant.getName());
                        String chTable = ClickhouseSqlUtil.toClickhouseName(metric.getMetricsName());
                        piiTable.add(new ChTable(chDB, chTable));
                        //clickhouse local table name
                        ClickhouseMultiHandler handlerByEnv = clickhouseHandlerFactory.get().getHandlerByEnv();
                        if (handlerByEnv == null) {
                            log.warn("This env has no clickhouse register. env={}", clickhouseHandlerFactory.get().getEnvironment());
                        } else {
                            for (ClickhouseSchema s : handlerByEnv.getTableSchema(chDB, chTable).values()) {
                                piiTable.add(new ChTable(chDB, s.getLocalTable()));
                                piiTable.add(new ChTable(tenant.getName(), s.getLocalTable()));
                            }
                        }
                    }
                } catch (Exception e) {
                    log.error("Unknown excption when loading pii table cache. Metric_id={} Collector_id={} error={}"
                            , metricsId, collectorId, e);
                }
            }
            this.piiTables = piiTable;
            log.info("Finsh to load PII table cache, table name={}", piiTables.stream().map(ChTable::toString).collect(Collectors.joining(",")));
        } catch (Exception e) {
            log.error("Unknown excption when loading pii table cache.", e);
        }
    }

}
