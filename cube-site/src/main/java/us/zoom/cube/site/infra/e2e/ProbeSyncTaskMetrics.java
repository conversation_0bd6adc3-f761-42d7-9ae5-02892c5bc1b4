package us.zoom.cube.site.infra.e2e;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
public class ProbeSyncTaskMetrics implements Serializable {
    @Serial
    private static final long serialVersionUID = 6598000555803021655L;
    private String service;
    /*** for new data parser*/
    private String appName;
    private String topType;
    private String type;
    private String path_template;
    /*** for new data parser*/
    private String probeTaskId;
    private String probeTaskName;
    private String globalTaskUuid;
    private double avgRt;
    private double minRt;
    private double maxRt;
    private double avgDelay;
    private double minDelay;
    private double maxDelay;
    private long successCount;
    private long failCount;
    private long timeoutCount = 0;
    private Long createTime;
    private String[] probePointGroupList;

    @Data
    public static class ProbePointGroupMetrics {
        private String probePointGroupId;
        private String status;
        private String probePointIp;
        private String probePointGroupName;
        private double rt;
        private double delay;
    }


}

