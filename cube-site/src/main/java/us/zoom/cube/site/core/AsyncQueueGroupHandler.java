package us.zoom.cube.site.core;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import us.zoom.infra.dao.model.AsyncQueueGroupDO;
import us.zoom.infra.dao.service.AsyncQueueGroupDAO;

import java.util.List;

/**
 * @<PERSON> <PERSON>
 * @create 2020/8/5 10:14 AM
 */
@Component
public class AsyncQueueGroupHandler {

    @Autowired
    private AsyncQueueGroupDAO asyncQueueGroupDAO;

    @Autowired
    private AsyncQueueHandler asyncQueueHandler;

    public List<AsyncQueueGroupDO> findByNameLike(String name, List<String> tenantIds, int pageIndex, int pageSize) {
        return asyncQueueGroupDAO.findByNameLike(name, tenantIds, pageSize * (pageIndex - 1), pageSize);
    }

    public int getCountByNameLike(String name, List<String> tenantIds) {
        return asyncQueueGroupDAO.getCountByNameLike(name, tenantIds);
    }

    public void addAqGroup(AsyncQueueGroupDO asyncQueueGroupDO) {
        adjust(asyncQueueGroupDO);
        asyncQueueGroupDAO.addAqGroup(asyncQueueGroupDO);
    }

    public AsyncQueueGroupDO getAqGroupById(String id) {
        return asyncQueueGroupDAO.getAqGroupById(id);
    }

    public AsyncQueueGroupDO getAqGroupByNameAndTenantId(String name, String tenantId){
        return asyncQueueGroupDAO.getAqGroupByNameAndTenantId(name, tenantId);
    }

    public void editAqGroup(AsyncQueueGroupDO asyncQueueGroupDO) {
        adjust(asyncQueueGroupDO);
        asyncQueueGroupDAO.editAqGroup(asyncQueueGroupDO);
    }

    private void adjust(AsyncQueueGroupDO asyncQueueGroupDO) {
        if (null == asyncQueueGroupDO.getCorrelationStrategy()) {
            asyncQueueGroupDO.setCorrelationStrategy("");
        }
        if (null == asyncQueueGroupDO.getUnitTag()) {
            asyncQueueGroupDO.setUnitTag("");
        }
    }

    public void deleteAqGroupById(String id) {
        asyncQueueGroupDAO.deleteById(id);
        asyncQueueHandler.deleteByAsyncQueueGroupId(id);
    }

    public void deleteById(String id) {
        asyncQueueGroupDAO.deleteById(id);
    }

    public List<AsyncQueueGroupDO> listAllForTenant(String tenantId) {
        return asyncQueueGroupDAO.listAllForTenant(tenantId);
    }

    public List<AsyncQueueGroupDO> listAll() {
        return asyncQueueGroupDAO.listAll();
    }

    public boolean hasSameAqGroupName(String name, List<String> tenantIds) {
        return asyncQueueGroupDAO.getCountByName(name, tenantIds) > 0;
    }
}
