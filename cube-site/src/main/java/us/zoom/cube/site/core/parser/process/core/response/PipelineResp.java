package us.zoom.cube.site.core.parser.process.core.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import us.zoom.cube.site.core.parser.process.core.processor.ProcessorResp;

import java.util.Map;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class PipelineResp {
    private PipelineEnum status = PipelineEnum.FAIL;
    private String failMessage;
    private Map<String,Object> inputMap;
    private Map<String, Object> messageMap;
    private Map<String, ProcessorResp> processorRespMap;

}

