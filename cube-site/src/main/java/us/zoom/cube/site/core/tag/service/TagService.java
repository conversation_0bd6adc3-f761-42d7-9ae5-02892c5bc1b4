package us.zoom.cube.site.core.tag.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.site.biz.syspara.TagParaService;
import us.zoom.cube.site.core.tag.TagPermissionUtil;
import us.zoom.cube.site.infra.utils.IdUtils;
import us.zoom.cube.site.lib.BusinessException;
import us.zoom.cube.site.lib.ResponseObject;
import us.zoom.cube.site.lib.input.tag.input.BatchTagInfoInput;
import us.zoom.cube.site.lib.input.tag.input.TagInfoInput;
import us.zoom.cube.site.lib.input.tag.input.TagTypeInput;
import us.zoom.cube.site.lib.output.PageResult;
import us.zoom.cube.site.lib.output.tag.TagInfoOut;
import us.zoom.cube.site.lib.query.PageQuery;
import us.zoom.infra.dao.model.ResourceTagDo;
import us.zoom.infra.dao.model.TagNameDo;
import us.zoom.infra.dao.model.TagTypeDo;
import us.zoom.infra.dao.model.TagTypeScopeDo;
import us.zoom.infra.dao.service.ResourceTagDAO;
import us.zoom.infra.dao.service.TagDAO;
import us.zoom.infra.dao.service.TagTypeDAO;
import us.zoom.infra.dao.service.TagTypeScopeDAO;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/12/31 16:33
 */
@Component
@Slf4j
public class TagService {

    @Autowired
    private TagTypeDAO tagTypeDAO;

    @Autowired
    private ResourceTagDAO resourceTagDAO;

    @Autowired
    private TagDAO tagDAO;

    @Autowired
    private TagPermissionUtil tagPermissionUtil;

    private final String adminTenant = "admin_tenant";
    @Autowired
    private TagTypeScopeDAO tagTypeScopeDAO;

    @Autowired
    private TagParaService tagParaService;

    public ResponseObject addTagTypeCheck(TagTypeInput tagTypeInput) {
        tagPermissionUtil.checkTagOperationPermission(tagTypeInput, tagTypeInput.getSource());
        Boolean isExisted = checkTagTypeExist(tagTypeInput);
        return ResponseObject.success(isExisted);
    }


    private Boolean checkTagTypeExist(TagTypeInput tagTypeInput) {
        Boolean isExisted = false;
        List<TagTypeDo> tagTypeByName = tagTypeDAO.findTagTypeByName(tagTypeInput.getTagTypeName(), tagTypeInput.getTenantId());
        if(!CollectionUtils.isEmpty(tagTypeByName)){
            isExisted = true;
        }
        return isExisted;
    }

    public ResponseObject addTagType(TagTypeInput tagTypeInput) {
        tagPermissionUtil.checkTagOperationPermission(tagTypeInput, tagTypeInput.getSource());
        if(tagTypeInput.getSource() == tagPermissionUtil.getSystemTagType()) {
            tagTypeInput.setTenantId(adminTenant);
        }

        TagTypeDo tagTypeDo = TagTypeDo.builder()
                .source(tagTypeInput.getSource())
                .tagTypeName(tagTypeInput.getTagTypeName())
                .desc(tagTypeInput.getDesc())
                .tenantId(tagTypeInput.getTenantId())
                .creator(tagTypeInput.getUserName())
                .modifier(tagTypeInput.getUserName())
                .id(IdUtils.generateId())
                .build();

        if(checkTagTypeExist(tagTypeInput)){
            throw new IllegalArgumentException("tag type Name and source is duplicate");
        }

        try{
            //add tag type
            tagTypeDAO.add(tagTypeDo);
            // add tag
            List<TagNameDo> tagNameDoList = tagTypeInput.getTagNames().stream().map(
                    name -> TagNameDo.builder().
                            tagTypeId(tagTypeDo.getId()).
                            tagName(name).
                            creator(tagTypeInput.getUserName()).
                            modifier(tagTypeInput.getUserName()).
                            tenantId(tagTypeInput.getTenantId()).
                            id(IdUtils.generateId()).build()
            ).collect(Collectors.toList());
            //add scope
            if(!CollectionUtils.isEmpty(tagTypeInput.getScopes())){
                List<TagTypeScopeDo> tagTypeScopeDos = tagTypeInput.getScopes().stream().map(
                        scopeParam -> TagTypeScopeDo.builder()
                                .scope(scopeParam)
                                .tagTypeId(tagTypeDo.getId())
                                .creator(tagTypeInput.getUserName())
                                .modifier(tagTypeInput.getUserName())
                                .id(IdUtils.generateId()).build()
                ).collect(Collectors.toList());
                tagTypeScopeDAO.addBatch(tagTypeScopeDos);
            }

            tagDAO.addBatch(tagNameDoList);
        }catch (DuplicateKeyException e){
            throw new IllegalArgumentException(tagTypeInput.getTagTypeName() + " is duplicate ");
        }

        return ResponseObject.success(tagTypeDo.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    public ResponseObject updateTagType(TagTypeInput tagTypeInput) {

        TagTypeDo tagTypeInfo = tagTypeDAO.findById(tagTypeInput.getId());

        if(tagTypeInfo == null){
            throw new BusinessException("label type: " + tagTypeInfo.getTagTypeName() + " is not exist");
        }
        tagPermissionUtil.checkTagOperationPermission(tagTypeInput, tagTypeInfo.getSource());
        TagTypeDo tagTypeDo = TagTypeDo.builder()
                .modifier(tagTypeInput.getUserName())
                .desc(tagTypeInput.getDesc())
                .id(tagTypeInput.getId())
                .build();

        List<String> scopes = tagTypeInput.getScopes();
        List<String> diff1 = new ArrayList<>(scopes);
        List<TagTypeScopeDo> tagTypeScopeDos = tagTypeScopeDAO.listByTagTypeId(tagTypeInput.getId());
        List<String> dbScopes = Optional.ofNullable(tagTypeScopeDos).orElse(Collections.emptyList()).stream().map(TagTypeScopeDo::getScope).collect(Collectors.toList());
        List<String> diff2 = new ArrayList<>(dbScopes);

        diff1.removeAll(diff2);
        if(!CollectionUtils.isEmpty(diff1)){
            //add new scope
            List<TagTypeScopeDo> newTagTypes = diff1.stream().map(scope ->
                    TagTypeScopeDo.builder()
                            .tagTypeId(tagTypeDo.getId())
                            .id(IdUtils.generateId())
                            .scope(scope)
                            .creator(tagTypeInput.getUserName())
                            .modifier(tagTypeInput.getUserName())
                            .build()).collect(Collectors.toList());
            tagTypeScopeDAO.addBatch(newTagTypes);
        }

        // delete unused scope
        diff2.removeAll(scopes);
        if(!CollectionUtils.isEmpty(diff2)){
            tagTypeScopeDAO.delBatch(tagTypeInput.getId(), diff2);
        }

        tagTypeDAO.update(tagTypeDo);
        return ResponseObject.success(tagTypeDo.getId());
    }

    public ResponseObject modifyTagTypeScope(TagTypeInput tagTypeInput, boolean isAdd) {

        TagTypeDo tagTypeInfo = tagTypeDAO.findById(tagTypeInput.getId());

        if(tagTypeInfo == null){
            throw new BusinessException("label type: " + tagTypeInfo.getTagTypeName() + " is not exist");
        }
        tagPermissionUtil.checkTagOperationPermission(tagTypeInput, tagTypeInfo.getSource());

        List<TagTypeScopeDo> tagTypeScopeDos = tagTypeScopeDAO.listByTagTypeId(tagTypeInput.getId());

        List<String> existScopes = tagTypeScopeDos.stream().map(TagTypeScopeDo::getScope).collect(Collectors.toList());

        if(isAdd){
            tagTypeInput.getScopes().removeAll(existScopes);
            if(!CollectionUtils.isEmpty(tagTypeInput.getScopes())){
                List<TagTypeScopeDo> newTagTypeScopeDos = tagTypeInput.getScopes().stream().map(
                        scopeParam -> TagTypeScopeDo.builder()
                                .scope(scopeParam)
                                .tagTypeId(tagTypeInput.getId())
                                .creator(tagTypeInput.getUserName())
                                .modifier(tagTypeInput.getUserName())
                                .id(IdUtils.generateId()).build()
                ).collect(Collectors.toList());
                tagTypeScopeDAO.addBatch(newTagTypeScopeDos);
            }
        }else{
            tagTypeInput.getScopes().retainAll(existScopes);
            if(!CollectionUtils.isEmpty(tagTypeInput.getScopes())){
                tagTypeScopeDAO.delBatch(tagTypeInput.getId(), tagTypeInput.getScopes());
            }
        }
        return ResponseObject.success(tagTypeInput.getId());
    }


    public ResponseObject delTagType(TagTypeInput tagTypeInput) {

        TagTypeDo tagTypeInfo = tagTypeDAO.findById(tagTypeInput.getId());
        if(tagTypeInfo == null){
            throw new BusinessException("label type: " + tagTypeInfo.getId() + " is not exist");
        }

        tagPermissionUtil.checkTagOperationPermission(tagTypeInput, tagTypeInfo.getSource());

        List<TagNameDo> tagByTagTypeId = tagDAO.findTagByTagTypeId(tagTypeInput.getId());
        if(!CollectionUtils.isEmpty(tagByTagTypeId)){
            // check resource
            List<String> tagIds = tagByTagTypeId.stream().map(TagNameDo::getId).collect(Collectors.toList());
            List<ResourceTagDo> resourceTagDos = resourceTagDAO.findByTagIds(tagIds);
            if(tagTypeInput.getForceDelete()){
                // if tag type is system tag, it will be might delete other service resource tag
                resourceTagDAO.deleteResourceByTagsId(tagIds);
            }else{
                if(!CollectionUtils.isEmpty(resourceTagDos)){
                    throw new BusinessException("The label associated with resource is not empty, so it cannot be deleted");
                }
            }
        }

        TagTypeDo tagTypeDo = TagTypeDo.builder()
                .id(tagTypeInfo.getId())
                .build();

        try{
            tagTypeDAO.delete(tagTypeDo.getId());
            // delete tag type scope
            tagTypeScopeDAO.deleteScopeByTagTypeIds(Arrays.asList(tagTypeDo.getId()));
        }catch (DuplicateKeyException e){
            throw new IllegalArgumentException("label type " + tagTypeDo.getSource() + " is duplicate ");
        }

        return ResponseObject.success(tagTypeDo.getId());
    }


    public ResponseObject listResourceType() {
        return ResponseObject.success(tagParaService.getScopeList());
    }


    public ResponseObject listTagType(PageQuery<TagTypeInput> pageQuery) {

        TagTypeInput input = pageQuery.getQueryPara();
        String tenantId = pageQuery.getTenantId();
        String tagTypeName = (input != null) ? input.getQueryTagTypeName() : "";
        Integer source = (input != null) ? input.getSource() : null;
        String tagName = (input != null) ? input.getQueryTagName() : null;
        List<String> scopes = (input != null) ? input.getScopes() : null;
        Integer scopeListSize = (scopes != null) ? scopes.size() : null;
        Boolean showNoScope = (input != null) ? input.getShowNoScope() : false;
        int count = tagTypeDAO.
                    countListJoinTagType(tenantId, tagTypeName, source, tagName, scopes, scopeListSize, showNoScope);

        if(count > 0){
            int offset = pageQuery.getPageSize() * (pageQuery.getPageIndex() - 1);
            List<TagTypeDo> tagTypeDos = tagTypeDAO.listJoinTagType(tenantId, tagTypeName, source, tagName, scopes, scopeListSize, showNoScope, offset, pageQuery.getPageSize());

            tagTypeDos.stream().forEach(tagTypeDo -> {
                List<TagNameDo> tagByTagTypeId = tagDAO.findTagByTagTypeId(tagTypeDo.getId());
                List<TagTypeScopeDo> tagTypeScopeDos = tagTypeScopeDAO.listByTagTypeId(tagTypeDo.getId());
                if(!CollectionUtils.isEmpty(tagByTagTypeId)){
                    List<String> dbScopes = tagTypeScopeDos.stream().map(TagTypeScopeDo::getScope).collect(Collectors.toList());
                    tagTypeDo.setScopes(dbScopes);
                }
                tagTypeDo.setTagNameDoList(tagByTagTypeId);
            });
            return ResponseObject.success(pageQuery.getOperId(), new PageResult(count, tagTypeDos));
        }else{
            return ResponseObject.success(pageQuery.getOperId(), new PageResult(count, Collections.emptyList()));
        }
    }

    public ResponseObject addTagInfoCheck(BatchTagInfoInput tagInfoInput) {
        TagTypeDo tagTypeInfo = tagTypeDAO.findById(tagInfoInput.getTagTypeId());
        if(tagTypeInfo == null){
            throw new BusinessException("label type: " + tagInfoInput.getTagTypeId() + " is not exist");
        }
        tagPermissionUtil.checkTagOperationPermission(tagInfoInput, tagTypeInfo.getSource());

        String tenantId = tagInfoInput.getTenantId();

        boolean isExisted = false;
        for(String tagNameName : tagInfoInput.getTagNames()){
            tagInfoInput.setTagName(tagNameName);
            isExisted = checkTagIsExisted(tagInfoInput, tenantId);
            if(isExisted){
                break;
            }
        }

        return ResponseObject.success(isExisted);
    }


    private boolean checkTagIsExisted(TagInfoInput tagInfoInput, String tenantId) {
        boolean isExisted = false;
        List<TagNameDo> tagNames = tagDAO.findTagByName(tagInfoInput.getTagName(), tagInfoInput.getTagTypeId(), tenantId);
        if(!CollectionUtils.isEmpty(tagNames)){
            isExisted = true;
        }
        return isExisted;
    }


    public ResponseObject addTagInfo(BatchTagInfoInput tagInfoInput) {

        TagTypeDo tagTypeInfo = tagTypeDAO.findById(tagInfoInput.getTagTypeId());
        if(tagTypeInfo == null){
            throw new BusinessException("label type: " + tagInfoInput.getTagTypeId() + " is not exist");
        }
        tagPermissionUtil.checkTagOperationPermission(tagInfoInput, tagTypeInfo.getSource());
        String tenantId = tagInfoInput.getTenantId();
        if(tagTypeInfo.getSource() == tagPermissionUtil.getSystemTagType()){
            tenantId = adminTenant;
        }

        String finalTenantId = tenantId;
        List<TagNameDo> tagNameDos =  tagInfoInput.getTagNames().stream().map(
                name-> {
                    TagNameDo tagNameDo = TagNameDo.builder()
                            .modifier(tagInfoInput.getUserName())
                            .creator(tagInfoInput.getUserName())
                            .tenantId(finalTenantId)
                            .tagTypeId(tagInfoInput.getTagTypeId())
                            .tagName(name)
                            .id(IdUtils.generateId())
                            .build();
                    return tagNameDo;
                }
        ).collect(Collectors.toList());

        try{
            tagDAO.addBatch(tagNameDos);
            // for order
            tagTypeDAO.updateGmtModify(tagInfoInput.getTagTypeId(), tagInfoInput.getUserName());
        }catch (DuplicateKeyException e){
            throw new IllegalArgumentException("label name " + tagInfoInput.getTagName() + " is duplicate ");
        }

        return ResponseObject.success(null);
    }


    public ResponseObject updateTagInfo(TagInfoInput tagInfoInput) {

        TagNameDo tagInfo = tagDAO.findById(tagInfoInput.getId());
        if(tagInfo == null){
            throw new BusinessException("label is not exist");
        }

        TagTypeDo tagTypeInfo = tagTypeDAO.findById(tagInfo.getTagTypeId());
        if(tagTypeInfo == null){
            throw new BusinessException("label type " + tagInfoInput.getTagTypeId() + " is not exist");
        }

        tagPermissionUtil.checkTagOperationPermission(tagInfoInput, tagTypeInfo.getSource());

        TagNameDo tagNameDo = TagNameDo.builder()
                .modifier(tagInfoInput.getUserName())
                .tagName(tagInfoInput.getTagName())
                .id(tagInfoInput.getId())
                .build();

        tagDAO.update(tagNameDo);

        return ResponseObject.success(tagNameDo.getId());
    }


    public ResponseObject delTagInfo(TagInfoInput tagInfoInput) {

        TagNameDo tagInfo = tagDAO.findById(tagInfoInput.getId());
        if(tagInfo == null){
            throw new BusinessException("label is not exist");
        }

        TagTypeDo tagTypeInfo = tagTypeDAO.findById(tagInfo.getTagTypeId());
        if(tagTypeInfo == null){
            throw new BusinessException("label type: " + tagTypeInfo.getTagTypeName() + " is not exist");
        }
        tagPermissionUtil.checkTagOperationPermission(tagInfoInput, tagTypeInfo.getSource());

        TagNameDo tagNameDo = TagNameDo.builder()
                .modifier(tagInfoInput.getUserName())
                .id(tagInfoInput.getId())
                .build();

        List<ResourceTagDo> byTagId = resourceTagDAO.findByTagId(tagNameDo.getId());
        if (!CollectionUtils.isEmpty(byTagId)) {
            if (!tagInfoInput.isForceDelete()) {
                throw new BusinessException(tagInfo.getTagName() + " is still in use");
            }

            resourceTagDAO.deleteResourceByTagsId(Arrays.asList(tagNameDo.getId()));
        }

        tagDAO.delete(tagNameDo.getId());

        return ResponseObject.success(tagNameDo.getId());
    }


    public String createOrUpdateTagInfo(TagInfoOut tagInfoOut, String tenantId, String userName) {
        String tagTypeName = tagInfoOut.getTagType().getTagTypeName();
        String tagName = tagInfoOut.getTagName();
        boolean isSystemTag = Integer.valueOf(1).equals(tagInfoOut.getTagType().getSource());
        if (isSystemTag) {
            tenantId = adminTenant;
        }

        List<TagTypeDo> tagTypeList = tagTypeDAO.findTagTypeByName(tagTypeName, tenantId);
        if (!CollectionUtils.isEmpty(tagTypeList)) {
            TagTypeDo existingTagType = tagTypeList.get(0);
            addTagTypeScopes(existingTagType.getId(), tagInfoOut.getTagType().getScopes(), userName);
            List<TagNameDo> existingTags = tagDAO.findTagByName(tagName, existingTagType.getId(), tenantId);
            if (!CollectionUtils.isEmpty(existingTags)) {
                return existingTags.get(0).getId();
            }
            if (isSystemTenant(tenantId)) {
                log.warn("ignore this system tag type:{}, value:{} if the system tag is not exist!!!!", tagTypeName, tagName);
                return null;
            }
            TagNameDo newTag = buildTagNameDo(tagName, existingTagType.getId(), tenantId, userName);
            tagDAO.add(newTag);
            return newTag.getId();
        }else{
            if (isSystemTenant(tenantId)) {
                log.warn("ignore this system tag type:{}, value:{} if the system tag type is not exist!!!!", tagTypeName, tagName);
                return null;
            }
        }

        TagTypeDo newTagType = TagTypeDo.builder()
                .id(IdUtils.generateId())
                .tagTypeName(tagTypeName)
                .desc(tagInfoOut.getTagType().getDesc())
                .tenantId(tenantId)
                .creator(userName)
                .modifier(userName)
                .source(tagInfoOut.getTagType().getSource())
                .build();
        tagTypeDAO.add(newTagType);
        addTagTypeScopes(newTagType.getId(), tagInfoOut.getTagType().getScopes(), userName);

        TagNameDo newTag = buildTagNameDo(tagName, newTagType.getId(), tenantId, userName);
        tagDAO.add(newTag);

        return newTag.getId();
    }

    private boolean isSystemTenant(String tenantId) {
        return adminTenant != null && adminTenant.equals(tenantId);
    }

    private TagNameDo buildTagNameDo(String tagName, String tagTypeId, String tenantId, String userName) {
        return TagNameDo.builder()
                .id(IdUtils.generateId())
                .tagName(tagName)
                .tagTypeId(tagTypeId)
                .tenantId(tenantId)
                .creator(userName)
                .modifier(userName)
                .build();
    }

    private void addTagTypeScopes(String tagTypeId, List<String> scopes, String userName) {
        if (CollectionUtils.isEmpty(scopes)) return;
        for (String scope : scopes) {
            try {
                TagTypeScopeDo scopeDo = TagTypeScopeDo.builder()
                        .scope(scope)
                        .tagTypeId(tagTypeId)
                        .creator(userName)
                        .modifier(userName)
                        .id(IdUtils.generateId())
                        .build();
                tagTypeScopeDAO.add(scopeDo);
            } catch (DuplicateKeyException e) {
                // ignore it
            }
        }
    }

    public ResponseObject listTag(PageQuery<TagInfoInput> pageQuery) {

        int count = tagDAO.
                countListTag(pageQuery.getTenantId(),
                        pageQuery.getQueryPara() == null ? "" : pageQuery.getQueryPara().getTagName(),
                        pageQuery.getQueryPara() == null ? "" : pageQuery.getQueryPara().getTagTypeId());

        if(count > 0){
            List<TagNameDo> tagNameDos = tagDAO.listTag(pageQuery.getTenantId(),
                    pageQuery.getQueryPara() == null ? "" : pageQuery.getQueryPara().getTagName(),
                    pageQuery.getQueryPara() == null ? "" : pageQuery.getQueryPara().getTagTypeId(),
                    pageQuery.getPageSize() * (pageQuery.getPageIndex() - 1), pageQuery.getPageSize());

            tagNameDos.forEach(tagNameDo -> {
                tagNameDo.setTagTypeDo(tagTypeDAO.findById(tagNameDo.getTagTypeId()));
                boolean inUsed = false;
                List<ResourceTagDo> resourceTagDos = resourceTagDAO.findByTagId(tagNameDo.getId());
                if(!CollectionUtils.isEmpty(resourceTagDos)){
                    inUsed = true;
                }
                tagNameDo.setInUsed(inUsed);
            });

            return ResponseObject.success(pageQuery.getOperId(), new PageResult(count, tagNameDos));
        }else{
            return ResponseObject.success(pageQuery.getOperId(), new PageResult(count, Collections.emptyList()));
        }
    }

}
