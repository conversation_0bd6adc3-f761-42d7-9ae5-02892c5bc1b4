package us.zoom.cube.site.core.model.ad;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import us.zoom.infra.dao.model.AlarmDefinitionRuleConditionDO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-07-06 13:20
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AdAlarmRuleCfg {
    private String id;
    private int level;
    private int needHits;
    private String alarmDefinitionId;
    private int rulesOrder;
    private int hitCount;
    private List<AlarmDefinitionRuleConditionDO> alarmDefinitionRuleConditionDOList;
}
