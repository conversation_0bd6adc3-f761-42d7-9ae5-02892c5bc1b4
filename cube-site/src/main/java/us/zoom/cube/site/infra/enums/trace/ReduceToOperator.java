package us.zoom.cube.site.infra.enums.trace;

import org.apache.commons.lang3.StringUtils;

/**
 * @author: eason.jia
 * @date: 2024/8/18
 */
public enum ReduceToOperator {
    LAST("last"),
    SUM("sum"),
    AVG("avg"),
    MIN("min"),
    MAX("max");

    private final String operator;

    ReduceToOperator(String operator) {
        this.operator = operator;
    }

    public String getOperator() {
        return operator;
    }

    public static ReduceToOperator from(String reduceToOperator) {
        for (ReduceToOperator operator : ReduceToOperator.values()) {
            if (StringUtils.equalsIgnoreCase(operator.getOperator(), reduceToOperator)) {
                return operator;
            }
        }
        throw new IllegalArgumentException("illegal reduceToOperator: " + reduceToOperator);
    }
}
