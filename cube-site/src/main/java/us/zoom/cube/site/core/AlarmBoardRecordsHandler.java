package us.zoom.cube.site.core;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zoom.op.monitor.domain.alarm.AlarmBoardSettingItem;
import com.zoom.op.monitor.domain.alarm.AlarmBoardSettingItemSubType;
import com.zoom.op.monitor.domain.alarm.AlarmBoardSettingItemType;
import com.zoom.op.monitor.domain.alarm.AlarmRule;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.site.api.intercept.AuthInterceptor;
import us.zoom.cube.site.biz.SysParaService;
import us.zoom.cube.site.biz.syspara.clickhouse.ClickhouseAuthParaService;
import us.zoom.cube.site.core.config.ClickhouseHandlerFactory;
import us.zoom.cube.site.core.config.PiiTableCacheLoader;
import us.zoom.cube.site.lib.input.AlarmBoardFilterCond;
import us.zoom.cube.site.lib.input.AlarmRecordPageQueryInput;
import us.zoom.cube.site.lib.input.SubAlarmQueryInput;
import us.zoom.cube.site.lib.output.alarm.AlarmRecordPageQueryOutput;
import us.zoom.cube.site.lib.output.alarm.insight.AlarmDetailOutput;
import us.zoom.cube.site.lib.query.TimeRangeQuery;
import us.zoom.infra.clickhouse.ChTable;
import us.zoom.infra.clickhouse.ClickhouseConst;
import us.zoom.infra.clickhouse.ClickhouseSqlUtil;
import us.zoom.infra.dao.model.MetricsDO;
import us.zoom.infra.dao.model.SubAlarmDO;
import us.zoom.infra.dao.model.TenantDO;
import us.zoom.infra.dao.service.AlarmDAO;
import us.zoom.infra.enums.AlarmRecordStatusEnum;
import us.zoom.infra.enums.AlarmSourceTypeEnum;
import us.zoom.infra.enums.FilterItemSystemTypeEnum;
import us.zoom.infra.influx.model.alarm.AlarmMatchRecord;
import us.zoom.infra.model.alarm.AlarmLevel;
import us.zoom.infra.utils.CommonSplitConstants;

import jakarta.annotation.PostConstruct;
import java.io.IOException;
import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * @author: Starls Ding
 * @date: 2022/10/14 15:10
 * @desc:
 */
@Slf4j
@Component
public class AlarmBoardRecordsHandler {

    @Autowired
    private TenantHandler tenantHandler;
    @Autowired
    private AlarmBoardSettingHandler alarmBoardSettingHandler;
    @Autowired
    private ClickhouseHandlerFactory clickhouseHandlerFactory;
    @Autowired
    private AlarmIndexHelperHandler alarmIndexHelperHandler;
    @Autowired
    private ClickhouseAuthParaService clickhouseAuthParaService;
    @Autowired
    private AlarmDAO alarmDAO;
    @Autowired
    private PiiTableCacheLoader piiTableLoader;
    @Autowired
    private SysParaService sysParaService;
    @Autowired
    private SubEnvironmentHandler subEnvironmentHandler;

    private static final String FAIL = "Fail";
    private static final String SUCCESS = "Success";

    private static ExecutorService multipleTableQueryThreadPool = Executors.newFixedThreadPool(30);
    private static ExecutorService multiMinTimeQueryThreadPool = Executors.newFixedThreadPool(30);

    private ObjectMapper objectMapper;

    @PostConstruct
    public void init() {
        objectMapper = new ObjectMapper();
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
    }

    public List<AlarmRecordPageQueryOutput> getAlarmRecords(AlarmRecordPageQueryInput alarmPageQuery) throws Exception {
        List<AlarmRecordPageQueryOutput> alarmRecordsList = new ArrayList<>();

        final String serviceId = alarmPageQuery.getServiceId();
        TenantDO tenant = tenantHandler.getTenantById(serviceId);
        Assert.notNull(tenant, "Can't find the service by id:" + serviceId);
        final String serviceName = tenant.getName();
        //final String dbName = ClickhouseSqlUtil.encodeClickhouseName(tenant.getName());

        TimeRangeQuery timeRange = buildTimeRangeInSeconds(serviceName, alarmPageQuery);

        //before group filter items by type, it must fill up some filed by setting in database
        final List<AlarmBoardFilterCond> filterItems = alarmPageQuery.getFilterItems();
        List<AlarmBoardSettingItem> alarmBoardSettingItems = alarmBoardSettingHandler.getItemInOrdInerByServiceId(serviceId);
        boolean onlySystemTypeFilterFlag = checkAndFillUpParams(alarmBoardSettingItems, filterItems);

        //locate alarm table range by time range, whether noticed and system type filter
        Map<Integer, List<AlarmBoardFilterCond>> filterCondByType = filterItems.stream().collect(Collectors.groupingBy(AlarmBoardFilterCond::getType));
        //List<AlarmBoardFilterCond> systemTypeFilterConds = filterCondByType.get(AlarmBoardSettingItemType.system.value());
        Map<Integer, List<AlarmBoardSettingItem>> settingItemsByType = alarmBoardSettingItems.stream().collect(Collectors.groupingBy(AlarmBoardSettingItem::getType));
        //List<AlarmBoardSettingItem> systemTypeSettingItems = settingItemsByType.get(AlarmBoardSettingItemType.system.value());

        //label1 temporary
        /*List<String> needToQueryAlarmIdeally = onlySystemTypeFilterFlag ?
                Collections.singletonList(ClickhouseConst.ALARM_EACH_SERCICE_RECORD_TABLE_NAME) :
                alarmIndexHelperHandler.getAlarmNamesByTimeRange(tenant.getName(), timeRange.getBegin(), timeRange.getEnd(), alarmPageQuery.isNoticed())
                        .stream().collect(Collectors.toList());*/
        List<String> needToQueryAlarmIdeally = alarmIndexHelperHandler
                .getAlarmNamesByTimeRange(tenant.getName(), timeRange.getBegin(), timeRange.getEnd(), alarmPageQuery.isNoticed())
                .stream().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(needToQueryAlarmIdeally)) {
            return alarmRecordsList;
        }

        Map<String, MetricsDO> alarmNameAndMetrics = alarmIndexHelperHandler.mappingAlarmWithMetric(serviceId);

        //filter alarms needn't to query such as metrics contain PII fields
        filterDoNotQueryAlarms(tenant.getName(), AuthInterceptor.getUserId(), AuthInterceptor.getUserName(), needToQueryAlarmIdeally, alarmNameAndMetrics);
        //sub alarm
        Map<String, SubAlarmDO> alarmAndSubAlarms = getConfigSubAlarmsByServiceId(serviceId);

        //query alarm names
        if (CollectionUtils.isEmpty(needToQueryAlarmIdeally)) {
            //do nothing

        } else if (1 == needToQueryAlarmIdeally.size()) {
            String alarmName = needToQueryAlarmIdeally.get(0);
            String allFilterCondition = buildFilterCondition(alarmPageQuery.isNoticed(), filterCondByType, settingItemsByType);
            String timeCondition = buildTimeCondition(alarmPageQuery, timeRange, serviceName, needToQueryAlarmIdeally, allFilterCondition, true);
            //label1 temporary
            /*if (onlySystemTypeFilterFlag) {
                alarmRecordsList.addAll(querySingleAlarmRecords(dbName, alarmName, allFilterCondition, timeCondition, alarmNameAndMetrics));
            } else {
                alarmRecordsList.addAll(querySingleAlarmRecords(dbName, alarmName, allFilterCondition, timeCondition, alarmNameAndMetrics.get(alarmName)));
            }*/
            alarmRecordsList.addAll(querySingleAlarmRecords(serviceName, alarmName, allFilterCondition, timeCondition, alarmNameAndMetrics.get(alarmName), alarmAndSubAlarms));

        } else {
            //to approach the appointed limit count by using similar binary search if need to query multiple alarm table
            String allFilterCondition = buildFilterCondition(alarmPageQuery.isNoticed(), filterCondByType, settingItemsByType);
            String timeCondition = buildTimeCondition(alarmPageQuery, timeRange, serviceName, needToQueryAlarmIdeally, allFilterCondition, false);
            //query multiple alarms, but query is the same as the single query way except the min time.
            List<Future<List<AlarmRecordPageQueryOutput>>> alarmRecordsFutureList = new ArrayList<>();
            //
            for (String alarmName : needToQueryAlarmIdeally) {
                Future<List<AlarmRecordPageQueryOutput>> alarmRecordsFuture = multipleTableQueryThreadPool.submit(() -> querySingleAlarmRecords(serviceName, alarmName, allFilterCondition, timeCondition, alarmNameAndMetrics.get(alarmName), alarmAndSubAlarms));
                alarmRecordsFutureList.add(alarmRecordsFuture);
            }
            for (Future<List<AlarmRecordPageQueryOutput>> alarmRecordFuture : alarmRecordsFutureList) {
                alarmRecordsList.addAll(alarmRecordFuture.get());
            }
        }
        alarmRecordsList.sort(Comparator.comparing(AlarmRecordPageQueryOutput::getTs).reversed());
        return alarmRecordsList;
    }

    public boolean checkAndFillUpParams
            (List<AlarmBoardSettingItem> alarmBoardSettingItems, List<AlarmBoardFilterCond> filterItems) {
        boolean onlySystemTypeFilter = true;
        //check
        Assert.isTrue(!CollectionUtils.isEmpty(alarmBoardSettingItems), "It seems all the setting item has been deleted, please refresh the this page!");
        Map<String, AlarmBoardSettingItem> idWithSettingItems = alarmBoardSettingItems.stream().collect(Collectors.toMap(AlarmBoardSettingItem::getId, o -> o));
        for (AlarmBoardFilterCond filterCond : filterItems) {
            AlarmBoardSettingItem alarmBoardSettingItem = idWithSettingItems.get(filterCond.getId());
            Assert.notNull(alarmBoardSettingItem, String.format("It seems some setting item has been deleted, please refresh the this page!"));
            filterCond.setName(alarmBoardSettingItem.getName());
            filterCond.setTagName(alarmBoardSettingItem.getTagName());
            filterCond.setType(alarmBoardSettingItem.getType());
            if (onlySystemTypeFilter && AlarmBoardSettingItemType.user.value() == filterCond.getType() && !CollectionUtils.isEmpty(filterCond.getFilterValueItems())) {
                onlySystemTypeFilter = false;
            }
        }
        return onlySystemTypeFilter;
    }

    private final String singleAlarmRecordQuerySql = "select * from %s.%s as atb  \n" +
            "global left join (select * from %s.%s where alarmMatchRecordId='%s' order by channelPriority limit 1 by alarmMatchRecordId) as btb \n" +
            "on atb.`__id`=btb.alarmMatchRecordId \n" +
            "global left join (select * from %s.%s where alarmMatchRecordId='%s' order by status desc limit 1 by alarmMatchRecordId) as ctb \n" +
            "on atb.`__id`=ctb.alarmMatchRecordId \n" +
            "where atb.`__id`='%s' order by atb.time desc";

    public AlarmRecordPageQueryOutput queryAlarmSingleRecord(String serviceName, String alarmName, String
            alarmRecordId, MetricsDO metrics, Map<String, SubAlarmDO> subAlarmDOMap) {

        final String dbName = ClickhouseSqlUtil.encodeClickhouseName(serviceName);
        final String alarmTable = ClickhouseConst.ALARM_EACH_SERCICE_RECORD_TABLE_NAME.equals(alarmName) ?
                ClickhouseConst.ALARM_EACH_SERCICE_RECORD_TABLE_NAME :
                ClickhouseSqlUtil.encodeClickhouseName(ClickhouseConst.ALARM_RECORD_TABLE_PREFIX + alarmName);

        String singleAlarmQuerySql = String.format(singleAlarmRecordQuerySql, dbName, alarmTable,
                dbName, ClickhouseConst.ALARM_MATCH_RECORD_NOTIFICATION_TABLE_NAME, alarmRecordId,
                dbName, ClickhouseConst.ALARM_MATCH_RECORD_STATUS_TABLE_NAME, alarmRecordId,
                alarmRecordId);
        String routedEnv = subEnvironmentHandler.getAlarmInsightEnv(serviceName);
        List<Map<String, Object>> alarmRecordQueryResult = clickhouseHandlerFactory.get().query(serviceName, singleAlarmQuerySql, routedEnv);
        List<AlarmRecordPageQueryOutput> alarmRecordPageQueryOutputs = convertQueryResultToAlarmRecord(alarmRecordQueryResult, metrics, subAlarmDOMap);
        return CollectionUtils.isEmpty(alarmRecordPageQueryOutputs) ? null : alarmRecordPageQueryOutputs.get(0);
    }


    public List<AlarmRecordPageQueryOutput> querySingleAlarmRecords(String serviceName, String alarmName, String
            filterCondition, String timeCondition, MetricsDO metrics, Map<String, SubAlarmDO> subAlarmDOMap) {
        List<Map<String, Object>> alarmRecordQueryResult = queryAlarmRecords(serviceName, alarmName, filterCondition, timeCondition);
        return convertQueryResultToAlarmRecord(alarmRecordQueryResult, metrics, subAlarmDOMap);
    }

    private List<Map<String, Object>> queryAlarmRecords(String serviceName, String alarmName, String
            filterCondition, String timeCondition) {
        String alarmTable = ClickhouseConst.ALARM_EACH_SERCICE_RECORD_TABLE_NAME.equals(alarmName) ?
                ClickhouseConst.ALARM_EACH_SERCICE_RECORD_TABLE_NAME :
                ClickhouseSqlUtil.encodeClickhouseName(ClickhouseConst.ALARM_RECORD_TABLE_PREFIX + alarmName);
        String baseSingleAlarmQuerySql = "select * from %s.%s as atb  \n" +
                "global left join (select * from %s.%s as atb where 1=1 %s order by channelPriority limit 1 by alarmMatchRecordId) btb \n" +
                "on atb.`__id`=btb.alarmMatchRecordId \n" +
                "global left join (select * from %s.%s as atb where 1=1 %s order by status desc limit 1 by alarmMatchRecordId) as ctb \n" +
                "on atb.`__id`=ctb.alarmMatchRecordId \n" +
                "where  1=1 %s %s order by atb.time desc";
        final String dbName = ClickhouseSqlUtil.encodeClickhouseName(serviceName);
        String singleAlarmQuerySql = String.format(baseSingleAlarmQuerySql, dbName, alarmTable,
                dbName, ClickhouseConst.ALARM_MATCH_RECORD_NOTIFICATION_TABLE_NAME, timeCondition,
                dbName, ClickhouseConst.ALARM_MATCH_RECORD_STATUS_TABLE_NAME, timeCondition,
                filterCondition, timeCondition);
        //log.info("getAlarmRecords sql is:{}", singleAlarmQuerySql);
        String routedEnv = subEnvironmentHandler.getAlarmInsightEnv(serviceName);
        List<Map<String, Object>> alarmRecordQueryResult = clickhouseHandlerFactory.get().query(serviceName, singleAlarmQuerySql, routedEnv);
        return alarmRecordQueryResult;
    }

    private String buildFilterCondition(boolean isNoticed, Map<Integer, List<AlarmBoardFilterCond>> filterCondByType,
                                        Map<Integer, List<AlarmBoardSettingItem>> settingItemsByType) {
        StringBuilder allFilterConds = new StringBuilder();
        if (isNoticed) {
            allFilterConds.append(" and ").append(String.format(" __isNoticed='%s' ", isNoticed));
        }
        if (null != filterCondByType) {
            allFilterConds.append(buildFilterSystemTypeCondition(filterCondByType.get(AlarmBoardSettingItemType.system.value())));
        }
        if (null != settingItemsByType) {
            allFilterConds.append(buildFilterUserTypeCondition(settingItemsByType.get(AlarmBoardSettingItemType.user.value()),
                    filterCondByType.get(AlarmBoardSettingItemType.user.value())));
        }
        return allFilterConds.toString();
    }

    public String buildTimeCondition(AlarmRecordPageQueryInput alarmPageQuery, TimeRangeQuery timeRange, String
            serviceName, List<String> needToQueryAlarmNames, String filterCondition, boolean single) throws Exception {
        final long beginSeconds = timeRange.getBegin();
        final long endSeconds = timeRange.getEnd();
        String timeCondition = null;
        if (StringUtils.isNotBlank(alarmPageQuery.getFirstAlarmRecordId())) {
            //timed refresh : original first record as new begin time to query latest records
            timeCondition = String.format(" and atb.time>%d and atb.time<=%d ", beginSeconds, endSeconds);

        } else if (StringUtils.isNotBlank(alarmPageQuery.getLastAlarmRecordId())) {
            //dropdown to refresh to query the earlier records utils frontend begin time.
            long minTimeSeconds = single ?
                    getSingleAlarmMinTimeSeconds(serviceName, needToQueryAlarmNames, filterCondition, beginSeconds, endSeconds, alarmPageQuery.getPageCount()) :
                    getMultipleAlarmMinTimeSeconds(serviceName, needToQueryAlarmNames, filterCondition, beginSeconds, endSeconds, alarmPageQuery.getPageCount());
            timeCondition = String.format(" and atb.time>=%d and atb.time<%d ", minTimeSeconds, endSeconds);

        } else {
            //usually query at first time or filters had changed.
            long minTimeSeconds = single ?
                    getSingleAlarmMinTimeSeconds(serviceName, needToQueryAlarmNames, filterCondition, beginSeconds, endSeconds, alarmPageQuery.getPageCount()) :
                    getMultipleAlarmMinTimeSeconds(serviceName, needToQueryAlarmNames, filterCondition, beginSeconds, endSeconds, alarmPageQuery.getPageCount());
            timeCondition = String.format(" and atb.time>=%d and atb.time<=%d ", minTimeSeconds, endSeconds);
        }
        return timeCondition;
    }

    private String buildFilterSystemTypeCondition(List<AlarmBoardFilterCond> systemTypeFilterConds) {
        //Level/Alarm/Metrics
        StringBuilder builder = new StringBuilder();
        if (CollectionUtils.isEmpty(systemTypeFilterConds)) {
            return builder.toString();
        }
        for (AlarmBoardFilterCond filterCond : systemTypeFilterConds) {

            FilterItemSystemTypeEnum filterSystemCond = FilterItemSystemTypeEnum.getByName(filterCond.getName());
            Assert.notNull(filterSystemCond, "Unknown system type alarm board setting item with name:" + filterCond.getName());

            List<String> filterSystemTypeItemValues = filterCond.getFilterValueItems();
            if (!CollectionUtils.isEmpty(filterSystemTypeItemValues)) {
                builder.append(" and ").append(filterSystemCond.getColumn()).append(" in ( ");
                for (String itemValue : filterSystemTypeItemValues) {
                    String condName = filterSystemCond.getName();
                    if (condName.equals("Level")) {
                        //like P1->warn
                        itemValue = AlarmLevel.getLevel(itemValue).getLevel();
                    }
                    builder.append("'").append(itemValue).append("'").append(",");
                }
                builder.deleteCharAt(builder.length() - 1);
                builder.append(" ) ");
            }
        }
        return builder.toString();
    }

    private String buildFilterUserTypeCondition(List<AlarmBoardSettingItem> settingItemUserTypeConfigs, List<AlarmBoardFilterCond> userTypeFilterConds) {
        StringBuilder builder = new StringBuilder();
        if (CollectionUtils.isEmpty(userTypeFilterConds)) {
            return builder.toString();
        }
        Map<String, AlarmBoardSettingItem> idWithSettingItems = settingItemUserTypeConfigs.stream().collect(Collectors.toMap(AlarmBoardSettingItem::getId, o -> o));
        for (AlarmBoardFilterCond userTypeFilterCond : userTypeFilterConds) {
            List<String> filterItemValues = userTypeFilterCond.getFilterValueItems();
            if (!CollectionUtils.isEmpty(filterItemValues)) {
                AlarmBoardSettingItem settingItemConfig = idWithSettingItems.get(userTypeFilterCond.getId());
                //if user input the empty string for text type, we skip this filter condition
                boolean isTextType = settingItemConfig.getSubType() == AlarmBoardSettingItemSubType.text.value();
                if (isTextType && StringUtils.isBlank(filterItemValues.get(0))) {
                    continue;
                }
                //build customer conditions
                final String tag = settingItemConfig.getTagName();
                builder.append(" and ").append("\"").append(tag).append("\"").append(" in ( ");
                for (String itemValue : filterItemValues) {
                    builder.append("'").append(itemValue).append("'").append(",");
                }
                builder.deleteCharAt(builder.length() - 1);
                builder.append(" ) ");
            }
        }
        return builder.toString();
    }


    private long getSingleAlarmMinTimeSeconds(String serviceName, List<String> needToQueryAlarmNames, String
            filterCondition, long startSeconds, long endSeconds, int limit) {
        if (startSeconds >= endSeconds) {
            return startSeconds;
        }
        final String dbName = ClickhouseSqlUtil.encodeClickhouseName(serviceName);
        String alarmName = needToQueryAlarmNames.get(0);
        final String alarmTable = ClickhouseConst.ALARM_EACH_SERCICE_RECORD_TABLE_NAME.equals(alarmName) ?
                ClickhouseConst.ALARM_EACH_SERCICE_RECORD_TABLE_NAME :
                ClickhouseSqlUtil.encodeClickhouseName(ClickhouseConst.ALARM_RECORD_TABLE_PREFIX + alarmName);
        String minTimeSql = String.format("select min(time) as minTime from (select \"time\" from %s.%s where 1=1 %s and time>=%d and time<=%d order by time desc limit %d)",
                dbName, alarmTable, filterCondition, startSeconds, endSeconds, limit);
        //log.info("Get single alarm min time sql:{}", minTimeSql);
        List<Map<String, Object>> minTimeResult = clickhouseHandlerFactory.get().query(serviceName, minTimeSql);
        if (CollectionUtils.isEmpty(minTimeResult)) {
            return startSeconds;
        } else {
            return ((Timestamp) minTimeResult.get(0).get("minTime")).getTime() / 1000;
        }

    }

    /**
     * fetch min time closed to query page size by binary search thinking
     */
    public long getMultipleAlarmMinTimeSeconds(String serviceName, List<String> needToQueryAlarmNames, String
            filterCondition, long beginSeconds, long endSeconds, int limit) throws Exception {
        final String dbName = ClickhouseSqlUtil.encodeClickhouseName(serviceName);
        long originalBeginSeconds = beginSeconds;
        long lastBeginSeconds = beginSeconds;

        final int allowLoopTotal = 10;
        final int finishBinarySeconds = 60;
        final int allowCountDeviation = 5;

        final String sql = "select count(*) as count from %s.%s where 1=1 %s and time>=%d and time<=%d";

        //tempCount < limit && (endSeconds - beginSeconds) > finishBinarySeconds &&
        int loop = 0;
        for (; loop < allowLoopTotal; loop++) {
            AtomicInteger tempCount = new AtomicInteger(0);
            // break condition_1
            //including beginSeconds == endSeconds
            if (endSeconds - beginSeconds < finishBinarySeconds) {
                return beginSeconds;
            }
            List<Future<Integer>> minTimeQueryFutureList = new ArrayList<>();
            for (String alarmName : needToQueryAlarmNames) {
                final long beginSecondsCopy = beginSeconds;
                Future<Integer> minTimeQueryFuture = multiMinTimeQueryThreadPool.submit(() -> {
                    String alarmTable = ClickhouseSqlUtil.encodeClickhouseName(ClickhouseConst.ALARM_RECORD_TABLE_PREFIX + alarmName);
                    String minTimeSql = String.format(sql, dbName, alarmTable, filterCondition, beginSecondsCopy, endSeconds);
                    //log.info("Get multiple alarm min time sql:{}", minTimeSql);
                    List<Map<String, Object>> eachCountResult = clickhouseHandlerFactory.get().query(serviceName, minTimeSql);
                    //tempCount.getAndAdd(Integer.valueOf(eachCountResult.get(0).get("count").toString()));
                    if (CollectionUtils.isEmpty(eachCountResult)) {
                        return 0;
                    } else {
                        return Integer.valueOf(eachCountResult.get(0).get("count").toString());
                    }
                });
                minTimeQueryFutureList.add(minTimeQueryFuture);
            }
            for (Future<Integer> minTimeQueryFuture : minTimeQueryFutureList) {
                tempCount.getAndAdd(minTimeQueryFuture.get());
            }

            //similar binary search，it's the essence for confirming the time range for multiple table query
            if (tempCount.get() == limit) {
                return beginSeconds;
            } else if (tempCount.get() > limit) {
                if (tempCount.get() - limit < allowCountDeviation) {
                    return beginSeconds;
                }
                lastBeginSeconds = beginSeconds;
                beginSeconds = beginSeconds + (endSeconds - beginSeconds) / 2;
                if (lastBeginSeconds == beginSeconds) {
                    break;
                }
            } else {
                if (originalBeginSeconds == beginSeconds) {
                    //means origin time range just doesn't have enough data
                    return originalBeginSeconds;
                } else {
                    long tempBeginSeconds = beginSeconds;
                    //what you see is right, we must not use the endSeconds here
                    beginSeconds = lastBeginSeconds + (beginSeconds - lastBeginSeconds) / 2;
                    if (tempBeginSeconds == beginSeconds) {
                        break;
                    }
                }
            }
        }
        return lastBeginSeconds;
    }

    public List<AlarmRecordPageQueryOutput> convertQueryResultToAlarmRecord(List<Map<String, Object>> queryResult) {
        List<AlarmRecordPageQueryOutput> alarmRecords = new ArrayList<>();
        for (Map<String, Object> entry : queryResult) {
            AlarmRecordPageQueryOutput alarmRecordOutput = convertQueryResultToAlarmRecord(entry, null, null);
            alarmRecords.add(alarmRecordOutput);
        }
        return alarmRecords;
    }

    public List<AlarmRecordPageQueryOutput> convertQueryResultToAlarmRecord(List<Map<String, Object>> queryResult, MetricsDO metrics, Map<String, SubAlarmDO> subAlarmDOMap) {
        List<AlarmRecordPageQueryOutput> alarmRecords = new ArrayList<>();
        for (Map<String, Object> entry : queryResult) {
            AlarmRecordPageQueryOutput alarmRecordOutput = convertQueryResultToAlarmRecord(entry, metrics, subAlarmDOMap);
            alarmRecords.add(alarmRecordOutput);
        }
        return alarmRecords;
    }

    public List<AlarmRecordPageQueryOutput> convertQueryResultToAlarmRecord(List<Map<String, Object>> queryResult, Map<String, MetricsDO> alarmNameAndMetrics, Map<String, SubAlarmDO> subAlarmDOMap) {
        List<AlarmRecordPageQueryOutput> alarmRecords = new ArrayList<>();
        for (Map<String, Object> entry : queryResult) {
            String alarmName = entry.get("atb.__alarmName").toString();
            AlarmRecordPageQueryOutput alarmRecordOutput = convertQueryResultToAlarmRecord(entry, alarmNameAndMetrics.get(alarmName), subAlarmDOMap);
            alarmRecords.add(alarmRecordOutput);
        }
        return alarmRecords;
    }

    private AlarmRecordPageQueryOutput convertQueryResultToAlarmRecord
            (Map<String, Object> queryResultEntry, MetricsDO metrics, Map<String, SubAlarmDO> subAlarmDOMap) {
        AlarmRecordPageQueryOutput alarmRecordOutput = new AlarmRecordPageQueryOutput();
        alarmRecordOutput.setId(queryResultEntry.get("atb.__id").toString());
        alarmRecordOutput.setAlarmId(queryResultEntry.get("atb.__alarmId").toString());
        final String alarmName = queryResultEntry.get("atb.__alarmName").toString();
        alarmRecordOutput.setAlarmName(alarmName);
        alarmRecordOutput.setStatus(AlarmRecordStatusEnum.statusOf(Integer.valueOf(queryResultEntry.get("ctb.status").toString())).name());
        alarmRecordOutput.setAlarmLevel(queryResultEntry.get("atb.__alarmLevel").toString().toUpperCase());
        alarmRecordOutput.setAlarmRuleId(queryResultEntry.get("atb.__alarmRuleId").toString());
        alarmRecordOutput.setServiceName(queryResultEntry.get("atb.__serviceName").toString());
        alarmRecordOutput.setMetricsName(queryResultEntry.get("atb.__metricsName").toString());
        //tags -> important : use the snapshot tags value instead of current metrics tags
        String tagsJson = Optional.ofNullable(queryResultEntry.get("atb.__tags"))
                .filter(tags -> StringUtils.isNotBlank(tags.toString()))
                .map(Objects::toString)
                .orElse(objectMapper.createObjectNode().toString());
        try {
            alarmRecordOutput.setMetricsTags(objectMapper.readValue(tagsJson, Map.class));
        } catch (IOException e) {
            log.error(String.format("Can't convert the __tags column content into JSON format for tags:%s", tagsJson), e);
            alarmRecordOutput.setMetricsTags(new HashMap<>());
        }

        if (null != subAlarmDOMap && subAlarmDOMap.containsKey(alarmName)) {
            alarmRecordOutput.setHasSubAlarm(true);
            alarmRecordOutput.setSubAlarmId(subAlarmDOMap.get(alarmName).getSubAlarmId());
            alarmRecordOutput.setSubAlarmName(subAlarmDOMap.get(alarmName).getSubAlarmName());
        }

        //todo postpone
        alarmRecordOutput.setCanMarkResolved(false);

        alarmRecordOutput.setNotificationTitle(queryResultEntry.get("btb.title").toString());
        alarmRecordOutput.setNotificationContent(queryResultEntry.get("btb.content").toString());
        alarmRecordOutput.setTs(((Timestamp) queryResultEntry.get("atb.time")).getTime());

        //for ops
        alarmRecordOutput.setCreateTime(((Timestamp) queryResultEntry.get("atb.__gmtCreate")).getTime());
        alarmRecordOutput.setLastCountMatched(Boolean.parseBoolean(queryResultEntry.get("atb.__isLastCountMatched").toString()));
        alarmRecordOutput.setNoticed(Boolean.parseBoolean(queryResultEntry.get("atb.__isNoticed").toString()));
        alarmRecordOutput.setSendingStatus(convertSendingStatus(queryResultEntry.get("btb.status").toString()));
        return alarmRecordOutput;
    }

    private String convertSendingStatus(String numStatus) {
        if (StringUtils.isNotBlank(numStatus) && "1".equals(numStatus)) {
            return SUCCESS;
        } else {
            return FAIL;
        }
    }

    private Map<String, String> fetchTagFromRecords(Map<String, Object> singleAlarmRecord, MetricsDO metrics) {
        String tags = metrics.getTagNames();
        if (StringUtils.isBlank(tags)) {
            return new HashMap<>();
        }
        return Arrays.stream(metrics.getTagNames().split(CommonSplitConstants.COMMA_SPLIT)).collect(Collectors.toMap(e -> e, e -> singleAlarmRecord.get("atb." + e).toString()));
    }

    private TimeRangeQuery buildTimeRangeInSeconds(String serviceName, AlarmRecordPageQueryInput alarmPageQuery) {
        Long beginMillisSeconds = null;
        Long endMillisSeconds = null;
        if (StringUtils.isNotBlank(alarmPageQuery.getFirstAlarmRecordId())) {
            //timed refresh : original first record as new begin time to query latest records
            beginMillisSeconds = getAlarmRecordById(serviceName, alarmPageQuery.getFirstAlarmRecordId()).getTime();
            endMillisSeconds = System.currentTimeMillis();
        } else if (StringUtils.isNotBlank(alarmPageQuery.getLastAlarmRecordId())) {
            //dropdown to refresh to query the earlier records utils frontend begin time.
            beginMillisSeconds = alarmPageQuery.getBegin();
            endMillisSeconds = getAlarmRecordById(serviceName, alarmPageQuery.getLastAlarmRecordId()).getTime();
        } else {
            beginMillisSeconds = alarmPageQuery.getBegin();
            endMillisSeconds = alarmPageQuery.getEnd();
        }
        return new TimeRangeQuery(beginMillisSeconds / 1000, endMillisSeconds / 1000);
    }

    private static final String RECORD_SQL_TEMPLATE = "select amsr.*, acmre.alarmStatus from ( select * from %s.%s where id = '%s') amsr inner join " +
            "(select id, alarmStatus from (select id, alarmStatus, row_number() over(partition by id order by gmtCreate desc) r " +
            "from %s.%s where id = '%s') " +
            "where r = 1) acmre on amsr.id = acmre.id";

    public AlarmMatchRecord getAlarmRecordByIdV2(String serviceName, String alarmRecordId) {
        final String dbName = ClickhouseSqlUtil.encodeClickhouseName(serviceName);
        String sql = String.format("select * from %s.%s where id='%s'", dbName, ClickhouseConst.ALARM_CUBE_MATCH_SERVICE_RECORD_TABLE, alarmRecordId);

        String newSql = String.format(RECORD_SQL_TEMPLATE, dbName, ClickhouseConst.ALARM_CUBE_MATCH_SERVICE_RECORD_TABLE, alarmRecordId
                , dbName, ClickhouseConst.ALARM_CUBE_MATCH_RECORD_EVENT_TABLE, alarmRecordId);
        List<Map<String, Object>> queryResult = clickhouseHandlerFactory.get().query(serviceName, newSql);
        Assert.notEmpty(queryResult, "Can't find the alarm record according to the id:" + alarmRecordId);
        return convertToSingleRecordV2(queryResult.get(0));
    }

    public AlarmDetailOutput convertToAlarmDetailOutput(AlarmMatchRecord alarmMatchRecord, Map<String, SubAlarmDO> subAlarmDOMap) {
        AlarmDetailOutput alarmDetailOutput = new AlarmDetailOutput();
        alarmDetailOutput.setId(alarmMatchRecord.getId());
        alarmDetailOutput.setAlarmLevel(alarmMatchRecord.getAlarmLevel().getLevel());
        alarmDetailOutput.setAlarmId(alarmMatchRecord.getAlarmId());
        alarmDetailOutput.setAlarmRuleId(alarmMatchRecord.getAlarmRuleId());
        alarmDetailOutput.setStatus(alarmMatchRecord.getStatus().name());
        String alarmName = alarmMatchRecord.getAlarmName();
        alarmDetailOutput.setAlarmName(alarmName);
        if (null != subAlarmDOMap && subAlarmDOMap.containsKey(alarmName)) {
            alarmDetailOutput.setHasSubAlarm(true);
            alarmDetailOutput.setSubAlarmId(subAlarmDOMap.get(alarmName).getSubAlarmId());
            alarmDetailOutput.setSubAlarmName(subAlarmDOMap.get(alarmName).getSubAlarmName());
        }
        alarmDetailOutput.setMetricsTags(alarmMatchRecord.getMetricsTags());
        alarmDetailOutput.setServiceName(alarmMatchRecord.getTenantName());
        alarmDetailOutput.setMetricsName(alarmMatchRecord.getMetricsName());
        alarmDetailOutput.setTs(alarmMatchRecord.getTime());
        alarmDetailOutput.setHitRule(Optional.ofNullable(alarmMatchRecord.getHittedRule()).map(AlarmRule::getConditions).orElse(null));

        //wait confirm if can delete
        alarmDetailOutput.setCanMarkResolved(false);
        return alarmDetailOutput;
    }

    private AlarmMatchRecord convertToSingleRecordV2(Map<String, Object> queryFields) {

        AlarmMatchRecord alarmMatchRecord = new AlarmMatchRecord();
        String alarmRecordId = queryFields.get("id").toString();
        alarmMatchRecord.setId(alarmRecordId);
        alarmMatchRecord.setAlarmId(queryFields.get("alarmId").toString());
        alarmMatchRecord.setAlarmName(queryFields.get("alarmName").toString());
        alarmMatchRecord.setAlarmLevel(AlarmLevel.fromLevel(queryFields.get("alarmLevel").toString()));
        alarmMatchRecord.setAlarmRuleId(queryFields.get("alarmRuleId").toString());
        alarmMatchRecord.setAlarmSourceType(AlarmSourceTypeEnum.fromValue(Integer.parseInt(queryFields.get("alarmSourceType").toString())));
        alarmMatchRecord.setMetricsId(queryFields.get("metricsId").toString());
        alarmMatchRecord.setMetricsName(queryFields.get("metricsName").toString());
        alarmMatchRecord.setTenantName(queryFields.get("serviceName").toString());
        alarmMatchRecord.setTime(((Timestamp) queryFields.get("time")).getTime());
        alarmMatchRecord.setTagKey(queryFields.get("tagKey").toString());
        alarmMatchRecord.setStatus(AlarmRecordStatusEnum.valueOf(queryFields.get("alarmStatus").toString()));
        String hittedRule = queryFields.get("hittedRule").toString();
        if (StringUtils.isNotBlank(hittedRule)) {
            try {
                alarmMatchRecord.setHittedRule(objectMapper.readValue(hittedRule, AlarmRule.class));
            } catch (IOException e) {
                log.error(String.format("convert alarm rule error! alarmRecordId:%s", alarmRecordId), e);
            }
        }
        alarmMatchRecord.setPreRecordId(queryFields.get("preRecordId").toString());
        return alarmMatchRecord;
    }

    public AlarmMatchRecord getAlarmRecordById(String serviceName, String alarmRecordId) {
        final String dbName = ClickhouseSqlUtil.encodeClickhouseName(serviceName);
        String sql = String.format("select * from %s.%s where __id='%s'", dbName, ClickhouseConst.ALARM_EACH_SERCICE_RECORD_TABLE_NAME, alarmRecordId);
        List<Map<String, Object>> queryResult = clickhouseHandlerFactory.get().query(serviceName, sql);
        Assert.notEmpty(queryResult, "Can't find the alarm record according to the id:" + alarmRecordId);
        return convertToSingleRecord(queryResult.get(0));
    }

    private AlarmMatchRecord convertToSingleRecord(Map<String, Object> queryFields) {
        AlarmMatchRecord alarmMatchRecord = new AlarmMatchRecord();
        alarmMatchRecord.setTime(((Timestamp) queryFields.get("time")).getTime());
        String alarmRecordId = queryFields.get("__id").toString();
        alarmMatchRecord.setId(alarmRecordId);
        alarmMatchRecord.setAlarmId(queryFields.get("__alarmId").toString());
        alarmMatchRecord.setAlarmName(queryFields.get("__alarmName").toString());
        alarmMatchRecord.setAlarmLevel(AlarmLevel.fromLevel(queryFields.get("__alarmLevel").toString()));
        alarmMatchRecord.setAlarmRuleId(queryFields.get("__alarmRuleId").toString());
        alarmMatchRecord.setSubPeriod(Integer.valueOf(queryFields.get("__subPeriod").toString()));
        alarmMatchRecord.setAggPeriod(Integer.valueOf(queryFields.get("__aggPeriod").toString()));
        alarmMatchRecord.setIsNoticed(Boolean.valueOf(queryFields.get("__isNoticed").toString()));
        String strHittedRule = queryFields.get("__hittedRule").toString();
        if (StringUtils.isNotBlank(strHittedRule)) {
            try {
                alarmMatchRecord.setHittedRule(objectMapper.readValue(strHittedRule, AlarmRule.class));
            } catch (IOException e) {
                log.error(String.format("convert alarm rule error! alarmRecordId:%s", alarmRecordId), e);
            }
        }
        return alarmMatchRecord;
    }

    private void filterDoNotQueryAlarms(String serviceName, String userId, String userName, List<String> needToQueryAlarmIdeally, Map<String, MetricsDO> alarmNameAndMetrics) {
        filterAlarmByMetricPiiField(userId, serviceName, needToQueryAlarmIdeally, alarmNameAndMetrics);
        filterDoNotAccessAlarms(userName, serviceName, needToQueryAlarmIdeally, alarmNameAndMetrics);
    }

    //will change the pii way soon
    private void filterDoNotAccessAlarms(String userName, String serviceName, List<String> needToQueryAlarmIdeally, Map<String, MetricsDO> alarmNameAndMetrics) {
        if (StringUtils.isBlank(userName)) {
            return;
        }
        Map<String, List<String>> serviceMetricsUserAccess = clickhouseAuthParaService.getMetricsAccess();
        Map<String, Set<String>> serviceMetricKeyMap = new HashMap<>();
        for (String serviceMetrics : serviceMetricsUserAccess.keySet()) {
            String[] split = serviceMetrics.split("\\.");
            final String piiService = split[0];
            final String piiMetrics = split[1];
            serviceMetricKeyMap.computeIfAbsent(piiService, sm -> new HashSet<>()).add(piiMetrics);
        }
        if (!serviceMetricKeyMap.containsKey(serviceName)) {
            return;
        } else {
            Set<String> allPiiMetrics = serviceMetricKeyMap.get(serviceName);
            final Map<String, List<String>> metricAlarmMap = new HashMap<>();
            alarmNameAndMetrics.entrySet().forEach(entry -> metricAlarmMap.computeIfAbsent(entry.getValue().getMetricsName(), o -> new ArrayList<>()).add(entry.getKey()));

            //filter user
            for (String piiMetrics : allPiiMetrics) {
                if (!serviceMetricsUserAccess.get(String.join(CommonSplitConstants.METRICS_FIELD_SPLIT, serviceName, piiMetrics)).contains(userName)) {
                    List<String> piiRelatedAlarms = metricAlarmMap.get(piiMetrics);
                    if (null != piiRelatedAlarms) {
                        needToQueryAlarmIdeally.removeAll(piiRelatedAlarms);
                    }
                }
            }
        }
    }

    private void filterAlarmByMetricPiiField(String userId, String serviceName, List<String> needToQueryAlarmIdeally, Map<String, MetricsDO> alarmNameAndMetrics) {
        //metrics -> ChTable
        Set<String> piiFieldAlarm = new HashSet<>();
        for (String needToQueryAlarm : needToQueryAlarmIdeally) {
            MetricsDO metricsDO = alarmNameAndMetrics.get(needToQueryAlarm);
            if (null == metricsDO) {
                continue;
            }
            ChTable chTable = new ChTable(serviceName, metricsDO.getMetricsName());
            if (!piiTableLoader.auth(chTable, userId, AuthInterceptor.getRealIp())) {
                piiFieldAlarm.add(needToQueryAlarm);
            }
        }
        needToQueryAlarmIdeally.removeAll(piiFieldAlarm);
    }

    //<alarmName,SubAlarm>
    public Map<String, SubAlarmDO> getConfigSubAlarmsByServiceId(String serviceId) {
        List<SubAlarmDO> subAlarms = alarmDAO.getSubAlarmsByServiceId(serviceId);
        return subAlarms.stream().collect(Collectors.toMap(SubAlarmDO::getAlarmName, o -> o));
    }

    public List<AlarmRecordPageQueryOutput> getSubAlarmRecords(SubAlarmQueryInput subAlarmQueryInput) {
        final String serviceId = subAlarmQueryInput.getServiceId();
        TenantDO tenant = tenantHandler.getTenantById(serviceId);
        Assert.notNull(tenant, "Can't find the service by id:" + serviceId);
        final String serviceName = tenant.getName();
        final String subAlarmName = subAlarmQueryInput.getSubAlarmName();

        MetricsDO metrics = alarmDAO.getMetrics(subAlarmQueryInput.getSubAlarmId());
        String filterCondition = buildFilterCondition(subAlarmQueryInput.isNoticed(), null, null);
        //local the appointed alarm around 10 records time range
        TimeRangeQuery timeRangeQuery = buildSubAlarmQueryTimeRange(serviceName, subAlarmQueryInput.getSubAlarmName(), subAlarmQueryInput.getTs(), 10);
        String timeCondition = String.format(" and atb.time>=%d and atb.time<=%d ", timeRangeQuery.getBegin(), timeRangeQuery.getEnd());

        return querySingleAlarmRecords(serviceName, subAlarmName, filterCondition, timeCondition, metrics, null);
    }

    private TimeRangeQuery buildSubAlarmQueryTimeRange(String serviceName, String subAlarmName, long baseTimeMs, int limit) {
        final String dbName = ClickhouseSqlUtil.encodeClickhouseName(serviceName);
        final String subAlarmTable = ClickhouseSqlUtil.encodeClickhouseName(ClickhouseConst.ALARM_RECORD_TABLE_PREFIX + subAlarmName);
        long beginTimeSeconds = baseTimeMs / 1000;
        long endTimeSeconds = baseTimeMs / 1000;
        //begin time
        List<Map<String, Object>> beginTimeQuery = clickhouseHandlerFactory.get().query(serviceName,
                String.format("select time from %s.%s where time<=%d order by time asc limit %d ", dbName, subAlarmTable, baseTimeMs / 1000, limit));
        if (!CollectionUtils.isEmpty(beginTimeQuery)) {
            beginTimeSeconds = ((Timestamp) beginTimeQuery.get(0).get("time")).getTime() / 1000;
        }
        //end time
        List<Map<String, Object>> endTimeQuery = clickhouseHandlerFactory.get().query(serviceName,
                String.format("select time from %s.%s where time>%d order by time desc limit %d ", dbName, subAlarmTable, baseTimeMs / 1000, limit));
        if (!CollectionUtils.isEmpty(endTimeQuery)) {
            endTimeSeconds = ((Timestamp) endTimeQuery.get(0).get("time")).getTime() / 1000;
        }
        return new TimeRangeQuery(beginTimeSeconds, endTimeSeconds);
    }


    /**
     * add alarm record event
     */
    public void addAlarmRecordEvent(AlarmMatchRecord alarmMatchRecord){
        String tenantName = alarmMatchRecord.getTenantName();
        try {
            Map<String, Object> columns =new HashMap<>();
            columns.put("id", (alarmMatchRecord.getId()));
            columns.put("ackExpireTime", alarmMatchRecord.getAckExpireTime());
            columns.put("alarmStatus", alarmMatchRecord.getStatus().getStatus());
            columns.put("event", alarmMatchRecord.getAlarmEventType().getValue());
            columns.put("gmtCreate", new Timestamp(System.currentTimeMillis()));
            Timestamp metricTs = new Timestamp(alarmMatchRecord.getTime() == 0 ? System.currentTimeMillis() : alarmMatchRecord.getTime());
            long recordMemorySize = ClickhouseSqlUtil.approximateSize(columns);
            String dbName = ClickhouseSqlUtil.toClickhouseName(tenantName);
            clickhouseHandlerFactory.getClickhouseWriter().write(dbName, ClickhouseConst.ALARM_CUBE_MATCH_RECORD_EVENT_TABLE, columns, metricTs, recordMemorySize, false);
        }catch (Exception e){
            log.error("Error when insert alarm record to clickhouse",e);
        }
    }

}