package us.zoom.cube.site.core;

import com.google.common.collect.Lists;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import us.zoom.infra.dao.model.*;
import us.zoom.infra.dao.service.MetricsAggregationDAO;
import us.zoom.infra.dao.service.MetricsAggregationRuleDAO;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2020/7/19 10:20 AM
 */
@Service
public class MetricsAggregationHandler {

    @Autowired
    private MetricsAggregationDAO metricsAggregationDAO;

    public MetricsAggregationDO getByMetricsId(String metricsId) {
        return metricsAggregationDAO.getByMetricsId(metricsId);
    }

    public List<MetricsAggregationDO> getByMetricsIds(List<String> metricsIds) {
        return metricsAggregationDAO.getByMetricsIds(metricsIds);
    }


    public void updateMetricsAggregation(MetricsAggregationDO metricsAggregationDO){
        metricsAggregationDAO.updateMetricsAggregation(metricsAggregationDO);
    }

    public void insertMetricsAggregation(MetricsAggregationDO metricsAggregationDO){
        metricsAggregationDAO.insertMetricsAggregation(metricsAggregationDO);
    }

    public void deleteById(String id){
        metricsAggregationDAO.deleteById(id);
    }

    public void deleteByIds(List<String> ids) {
        metricsAggregationDAO.deleteByIds(ids);
    }

    public List<MetricsAggregationDO> listMetricsAggByMetricsIds(List<String> metricsIds){
        if(CollectionUtils.isEmpty(metricsIds)){
            return Lists.newArrayList();
        }
        return metricsAggregationDAO.listMetricsAggByMetricsIds(metricsIds);
    }

    public List<MetricsAggregationDO> listAll(){
        return metricsAggregationDAO.listAll();
    }

}
