package us.zoom.cube.site.lib.output;

import java.util.List;

public class TokenPageResult<T> extends PageResult {

    private String pageToken;

    public TokenPageResult(List<T> items) {
        super(items);
    }

    public TokenPageResult(int total, List<T> items, String pageToken) {
        super(total, items);
        this.pageToken = pageToken;
    }

    public String getPageToken() {
        return pageToken;
    }

    public void setPageToken(String pageToken) {
        this.pageToken = pageToken;
    }
}
