package us.zoom.cube.site.infra;

import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import us.zoom.cloud.secrets.spring.annotation.SecretValue;
import us.zoom.cube.site.core.AsyncmqClusterHandler;
import us.zoom.infra.asyncmq.AsyncMQRotateHandler;
import us.zoom.mq.client.AsyncMQ;
import us.zoom.mq.client.DefaultAsyncMQ;
import us.zoom.mq.client.clients.consumer.Consumer;
import us.zoom.mq.client.clients.consumer.RetryableStraw;
import us.zoom.mq.client.clients.producer.Producer;
import us.zoom.mq.client.pojo.Subscriber;
import us.zoom.mq.common.enums.ProtocolStrategy;

import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class AsyncMQInstance {

    private volatile static Consumer consumer;
    private volatile static AsyncMQ asyncMQ;


    private volatile static AsyncMQInstance instance;

    private volatile static List<Producer> producers = new ArrayList<>();
    private volatile static List<AsyncMQ> asyncMQS = new ArrayList<>();

    private static int producerPoolSize = 4;


    public static final String ASYNC_MQ_TYPE = "cube_site_async";

    @Autowired
    private Environment environment;

    @SecretValue("async.mq.endpoint")
    private String asyncMqEndpoint;

    @SecretValue("async.mq.username")
    private String asyncMqUsername;

    @SecretValue("async.mq.password")
    private String asyncMqPassword;

    @Autowired
    private AsyncmqClusterHandler asyncmqClusterHandler;

    private AsyncMQInstance() {
    }

    @PostConstruct
    public void initAsyncMQ() {
        try {
            AsyncMQInstance.init(asyncMqEndpoint, asyncMqUsername, asyncMqPassword);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("initAsyncMQ error", e);
        }
    }

    public static void init(String endpoint, String username, String password) {
        try {
            if (instance == null) {
                synchronized (AsyncMQInstance.class) {
                    if (instance == null) {
                        log.info("init AsyncMQInstance.");
                        if (StringUtils.isBlank(endpoint) || StringUtils.isBlank(username) || StringUtils.isBlank(password)) {
                            throw new RuntimeException("endpoint, username and password cannot be empty");
                        }

                        asyncMQ = new DefaultAsyncMQ(endpoint, username, password);
                        asyncMQS.add(asyncMQ);
                        consumer = asyncMQ.consumer();
                        AsyncMQRotateHandler.addMQListener(asyncMQ, "async.mq.password", username);

                        for (int i = 0; i < producerPoolSize; i++) {
                            AsyncMQ asyncMQ = new DefaultAsyncMQ(endpoint, username, password);
                            asyncMQ.setClientId("async-client-" + i);
                            Producer producer = asyncMQ.producer();
                            producer.setProtocolStrategy(ProtocolStrategy.SIMPLE);
                            producers.add(producer);
                            asyncMQS.add(asyncMQ);
                            AsyncMQRotateHandler.addMQListener(asyncMQ, "async.mq.password", username);
                        }

                        consumer.start();
                        instance = new AsyncMQInstance();
                        log.info("init AsyncMQInstance success.");
                    }
                }
            }
        } catch (Exception e) {
            //e.printStackTrace();
            log.error("init error", e);
        }

    }


    public <T> void registerConsumer(String topic, String groupId, int receiveCount, RetryableStraw<T> batchHandler) {
        try {
            Subscriber subscriber = consumer.registerSubscriber(topic, groupId);
            consumer.setStraw(subscriber, batchHandler);
            //Straw，consumeCount
            consumer.start(subscriber, receiveCount, 1);
            log.info("start consumer, topic:{}, group id:{}, receiveCount:{}", topic, groupId, receiveCount);
        } catch (Exception e) {
            throw new RuntimeException(String.format("register consumer fail, topic:%s, groupId:%s, theadCount:%d", topic, groupId, receiveCount), e);
        }
    }

    public static AsyncMQ getAsyncMQ() {
        return asyncMQ;
    }


    public static AsyncMQInstance getInstance() {
        return instance;
    }

    public Producer getProducer() {
        return producers.get(RandomUtils.nextInt() % producerPoolSize);
    }
}
