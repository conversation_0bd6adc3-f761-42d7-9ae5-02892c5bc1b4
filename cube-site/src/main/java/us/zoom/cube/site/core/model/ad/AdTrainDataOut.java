package us.zoom.cube.site.core.model.ad;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024-01-18 15:12
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AdTrainDataOut {
    private String id;
    private String adConfigId;
    private String metricsId;
    private Integer adTrainStatus;
    private String tenantId;
    private String adTrainInfo;
    private Date createTime;
    private Date modifyTime;
}
