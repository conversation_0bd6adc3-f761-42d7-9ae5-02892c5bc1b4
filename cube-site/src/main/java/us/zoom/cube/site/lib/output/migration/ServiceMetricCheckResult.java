package us.zoom.cube.site.lib.output.migration;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class ServiceMetricCheckResult {

    private boolean checkPass = true;
    private String startTime;
    private String endTime;

    private int skipMetricTotal = 0;
    private int remappingMetricTotal = 0;

    private int checkTotal = 0;
    private int passTotal = 0;

    private int total = 0;
    private int pageSize = 0;
    private int pageTotal = 0;
    private int pageIndex = 0;

    private List<String> targetMissMetrics = new ArrayList<>();
    private List<String> sourceMissMetrics = new ArrayList<>();

    private List<MetricsItemEntry> metricCheckResult = new ArrayList<>();

    public void increaseSkipMetricTotal() {
        skipMetricTotal++;
    }

    public void increaseRemappingMetricTotal() {
        remappingMetricTotal++;
    }

}
