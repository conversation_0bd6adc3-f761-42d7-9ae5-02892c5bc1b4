package us.zoom.cube.site.core;

import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import us.zoom.async.mq.openapi.client.api.OpenApi;
import us.zoom.async.mq.openapi.client.client.OpenApiClient;
import us.zoom.cube.lib.common.AsyncmqClusterTypeEnum;
import us.zoom.cube.site.infra.AsyncMqOpenApiClientWrapper;
import us.zoom.infra.dao.model.AsyncmqClusterDO;
import us.zoom.infra.dao.service.AsyncmqClusterDAO;

import javax.annotation.PostConstruct;
import java.util.*;

/**
 * @Author: luis.zheng
 */
@Component
@Slf4j
public class AsyncmqClusterHandler {
    private AsyncmqClusterDAO asyncmqClusterDAO;

    private RsaService rsaService;

    public static final String DEFAULT_CLUSTER_NAME = "Global";

    private volatile AsyncMqOpenApiClientWrapper defaultOpenApiClient;

    private Map<String, AsyncMqOpenApiClientWrapper> clusterIdToOpenApiClientMap = Maps.newHashMap();

    private Map<String, AsyncMqOpenApiClientWrapper> clusterNameToOpenApiClientMap = Maps.newHashMap();

    @Autowired
    public AsyncmqClusterHandler(AsyncmqClusterDAO asyncmqClusterDAO, RsaService rsaService) {
        this.asyncmqClusterDAO = asyncmqClusterDAO;
        this.rsaService = rsaService;
    }

    @PostConstruct
    public void init() {
        try {
            log.info("start init openApiClient");
            initOpenApiClient();
            log.info("init openApiClient successful");
        } catch (Exception e) {
            log.error("init openApiClient failed", e);
        }
    }

    @Scheduled(cron = "0 0 */1 * * ?")
    public void initOpenApiClient() {

        List<AsyncmqClusterDO> asyncmqClusterDOS = listAll();
        Map<String, AsyncMqOpenApiClientWrapper> tmpClusterIdToOpenApiClientMap = Maps.newHashMap();
        Map<String, AsyncMqOpenApiClientWrapper> tmpClusterNameToOpenApiClientMap = Maps.newHashMap();
        asyncmqClusterDOS.stream()
//                .filter(asyncmqClusterDO -> AsyncmqClusterTypeEnum.common.getValue() == asyncmqClusterDO.getType())
                .sorted((o1, o2) -> {
                    if (StringUtils.equals(o1.getName(), DEFAULT_CLUSTER_NAME)) {
                        return -1;
                    } else if (StringUtils.equals(o2.getName(), DEFAULT_CLUSTER_NAME)) {
                        return 1;
                    } else {
                        return 0;
                    }
                })
                .forEach(asyncmqClusterDO -> {
                    try {
                        decryptSingle(asyncmqClusterDO);
                    } catch (Exception e) {
                        log.error("decrypt password failed", e);
                        return;
                    }
                    AsyncMqOpenApiClientWrapper openApiClientWrapper = createOpenApiClientWrapper(asyncmqClusterDO);
                    if (StringUtils.equals(asyncmqClusterDO.getName(), DEFAULT_CLUSTER_NAME)) {
                        defaultOpenApiClient = openApiClientWrapper;
                    }
                    tmpClusterIdToOpenApiClientMap.put(asyncmqClusterDO.getId(), openApiClientWrapper);
                    tmpClusterNameToOpenApiClientMap.put(asyncmqClusterDO.getName().toLowerCase(), openApiClientWrapper);
                });
        clusterIdToOpenApiClientMap = tmpClusterIdToOpenApiClientMap;
        clusterNameToOpenApiClientMap = tmpClusterNameToOpenApiClientMap;
    }


    private AsyncMqOpenApiClientWrapper createOpenApiClientWrapper(AsyncmqClusterDO asyncmqClusterDO) {
        OpenApi openapi = OpenApiClient.builder()
                .init(asyncmqClusterDO.getEndpoint(), asyncmqClusterDO.getUsername(), asyncmqClusterDO.getPassword())
                .build().openapi();
        return AsyncMqOpenApiClientWrapper.builder().openApi(openapi)
                .asyncMqClusterId(asyncmqClusterDO.getId())
                .asyncMqClusterName(asyncmqClusterDO.getName())
                .endpoint(asyncmqClusterDO.getEndpoint())
                .build();
    }

    public AsyncMqOpenApiClientWrapper getOpenapiClientByClusterName(String name) {
        AsyncMqOpenApiClientWrapper asyncMqOpenApiClientWrapper = clusterNameToOpenApiClientMap.get(name);
        if (null == asyncMqOpenApiClientWrapper) {
            asyncMqOpenApiClientWrapper = defaultOpenApiClient;
        }
        return asyncMqOpenApiClientWrapper;
    }

    public AsyncMqOpenApiClientWrapper getDefaultOpenApiClient() {
        return defaultOpenApiClient;
    }

    public AsyncMqOpenApiClientWrapper getClientByClusterId(String id) {
        return clusterIdToOpenApiClientMap.get(id);
    }


    public Map<String, AsyncMqOpenApiClientWrapper> getClusterIdToOpenApiClientMap() {
        return clusterIdToOpenApiClientMap;
    }

    public List<AsyncmqClusterDO> listAll() {
        List<AsyncmqClusterDO> asyncmqClusterDOS = asyncmqClusterDAO.listAll();
        return asyncmqClusterDOS;
    }

    public List<AsyncmqClusterDO> findByParam(String name, String endpoint, String username, Integer status, String relatedClusterId, int pageIndex, int pageSize) throws Exception {
        List<AsyncmqClusterDO> asyncmqClusterDOS = asyncmqClusterDAO.findByParam(name, endpoint, username, status, relatedClusterId, pageSize * (pageIndex - 1), pageSize);
        return securityFieldSetNullForArray(asyncmqClusterDOS);
    }

    public Integer getCountByParam(String name, String endpoint, String username, Integer status, String relatedClusterId) {
        return asyncmqClusterDAO.getCountByParam(name, endpoint, username, status, relatedClusterId);
    }

    public List<AsyncmqClusterDO> getByIds(Set<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }
        return securityFieldSetNullForArray(asyncmqClusterDAO.getByIds(ids));
    }

    public void add(AsyncmqClusterDO asyncmqClusterDO) throws Exception {
        encrypt(asyncmqClusterDO);
        asyncmqClusterDAO.add(asyncmqClusterDO);
    }

    public void edit(AsyncmqClusterDO asyncmqClusterDO) throws Exception {
        if (asyncmqClusterDO.getPassword() != null) {
            encrypt(asyncmqClusterDO);
        }
        asyncmqClusterDAO.edit(asyncmqClusterDO);
    }

    public AsyncmqClusterDO getSecurityById(String id) {
        AsyncmqClusterDO asyncmqClusterDO = asyncmqClusterDAO.getById(id);
        return asyncmqClusterDO == null ? null : singleSecurityFieldSetNull(asyncmqClusterDO);
    }

    public AsyncmqClusterDO getById(String id) throws Exception {
        AsyncmqClusterDO asyncmqClusterDO = asyncmqClusterDAO.getById(id);
        return asyncmqClusterDO == null ? null : decryptSingle(asyncmqClusterDO);
    }

    public int countById(String id) {
        return asyncmqClusterDAO.countById(id);
    }

    public boolean hasSameName(String name) {
        return asyncmqClusterDAO.countByName(name) > 0;
    }

    public void deleteById(String id) {
        asyncmqClusterDAO.deleteById(id);
    }

    private List<AsyncmqClusterDO> securityFieldSetNullForArray(List<AsyncmqClusterDO> asyncmqClusterDOS) {
        if (CollectionUtils.isEmpty(asyncmqClusterDOS)) {
            return Collections.emptyList();
        }

        for (AsyncmqClusterDO asyncmqClusterDO : asyncmqClusterDOS) {
            singleSecurityFieldSetNull(asyncmqClusterDO);
        }
        return asyncmqClusterDOS;
    }

    private AsyncmqClusterDO singleSecurityFieldSetNull(AsyncmqClusterDO asyncmqClusterDO) {
        asyncmqClusterDO.setPassword(null);
        return asyncmqClusterDO;
    }

    private AsyncmqClusterDO decryptSingle(AsyncmqClusterDO asyncmqClusterDO) throws Exception {
        if (StringUtils.isNotBlank(asyncmqClusterDO.getPassword())) {
            asyncmqClusterDO.setPassword(rsaService.decrypt(asyncmqClusterDO.getPassword()));
        }
        return asyncmqClusterDO;
    }

    private AsyncmqClusterDO encrypt(AsyncmqClusterDO asyncmqClusterDO) throws Exception {
        if (StringUtils.isNotBlank(asyncmqClusterDO.getPassword())) {
            asyncmqClusterDO.setPassword(rsaService.encrypt(asyncmqClusterDO.getPassword()));
        }
        return asyncmqClusterDO;
    }
}
