package us.zoom.cube.site.core;

import org.apache.commons.collections4.CollectionUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import us.zoom.cube.lib.common.CloudTypeEnum;
import us.zoom.cube.lib.common.HubChannelType;
import us.zoom.cube.lib.common.ServerTypeEnum;
import us.zoom.cube.lib.common.UseZipEnum;
import us.zoom.cube.site.CubeSiteApplicationTests;
import us.zoom.cube.site.infra.enums.ServerStatusEnum;
import us.zoom.cube.site.infra.utils.IdUtils;
import us.zoom.cube.site.lib.input.ServerInput;
import us.zoom.cube.site.lib.query.PageQuery;
import us.zoom.cube.site.lib.query.ServerQuery;
import us.zoom.infra.dao.model.ServerDO;
import us.zoom.infra.dao.model.ServerHeartInfoDO;
import us.zoom.infra.dao.model.TenantDO;
import us.zoom.infra.model.TenantServerQuery;
import us.zoom.infra.thread.ThreadLocalStore;
import us.zoom.infra.utils.DateUtils;

import java.lang.management.ManagementFactory;
import java.lang.management.ThreadInfo;
import java.lang.management.ThreadMXBean;
import java.util.*;

/**
 * <AUTHOR> Junjian
 * @create 2020/8/6 4:22 PM
 */
public class ServerHandlerTest extends CubeSiteApplicationTests {

    @Autowired
    private ServerHandler serverHandler;

    @Autowired
    private TenantHandler tenantHandler;

    @Test
    public void testlistServerNoHeartLimitCount(){
        List<TenantDO> tenantDOS = tenantHandler.listAll();
        if(CollectionUtils.isEmpty(tenantDOS)){
            return;
        }
        String tenantId = tenantDOS.get(0).getId();
        List<TenantServerQuery> serverQueries = new ArrayList<>();
        serverQueries.add(new TenantServerQuery(tenantId,"__strange_host1","*************",ServerTypeEnum.agent.name()));
        serverQueries.add(new TenantServerQuery(tenantId,"__strange_host2","*************",ServerTypeEnum.agent.name()));
        serverQueries.add(new TenantServerQuery(tenantId,"__strange_host3","*************",ServerTypeEnum.agent.name()));
        serverQueries.add(new TenantServerQuery(tenantId,"__strange_host4","*************",ServerTypeEnum.hub.name()));

       List<ServerHeartInfoDO> serverDOS = serverHandler.listServer(serverQueries);
       if(CollectionUtils.isNotEmpty(serverDOS)){
           serverDOS.forEach(server->serverHandler.delServer(server.getId()));
       }

        ThreadLocalStore.setTenantInfoLocal(tenantId);
        List<String> serverIds = new ArrayList<>(3);
        ServerInput serverInput=mockServer(tenantId,HubChannelType.fast.name(),CloudTypeEnum.COLO.getType(),"us01","VA","__strange_host1","*************",ServerTypeEnum.agent);
        serverHandler.addServer(serverInput);
        serverIds.add(serverInput.getId());
        serverInput=mockServer(tenantId,HubChannelType.fast.name(),CloudTypeEnum.COLO.getType(),"us01","VA","__strange_host2","*************",ServerTypeEnum.agent);
        serverHandler.addServer(serverInput);
        serverIds.add(serverInput.getId());
        serverInput=mockServer(tenantId,HubChannelType.fast.name(),CloudTypeEnum.COLO.getType(),"us01","VA","__strange_host3","*************",ServerTypeEnum.agent);
        serverHandler.addServer(serverInput);
        serverIds.add(serverInput.getId());

        serverInput=mockServer(tenantId,HubChannelType.fast.name(),CloudTypeEnum.COLO.getType(),"us01","VA","__strange_host4","*************",ServerTypeEnum.hub);
        serverHandler.addServer(serverInput);
        serverIds.add(serverInput.getId());

        serverHandler.bachUpdateStatus(serverIds,ServerStatusEnum.offline);

        List<String> tenantIds = new ArrayList<>();
        tenantIds.add(tenantId);
        List<ServerDO> serverDOList =  serverHandler.listServerNoHeartLimitCount(tenantIds,ServerTypeEnum.agent,2);
        Assertions.assertTrue(serverDOList.size()>=2);

       serverDOList =  serverHandler.listServerNoHeartLimitCount(tenantIds,ServerTypeEnum.agent,3);
        Assertions.assertTrue(serverDOList.size()>=3);

        serverDOList =   serverHandler.listServerNoHeartLimitCount(tenantIds,ServerTypeEnum.hub,1);
        Assertions.assertTrue(serverDOList.size()>=1);

        if(CollectionUtils.isNotEmpty(serverIds)){
            serverIds.forEach(id->serverHandler.delServer(id));
        }

    }

    private ServerInput mockServer(String tenantId, String channelType, int type, String clusterId, String regionId, String host, String ip,ServerTypeEnum serverTypeEnum) {
        ServerInput serverInput = new ServerInput();
        serverInput.setType(serverTypeEnum.name());
        serverInput.setTenantId(tenantId);
        serverInput.setChannelType(channelType);
        serverInput.setCloudType(type);
        serverInput.setClusterId(clusterId);
        serverInput.setRegionId(regionId);
        serverInput.setHost(host);
        serverInput.setIp(ip);
        serverInput.setAgentKey(IdUtils.generateId());
        serverInput.setId(IdUtils.generateId());
        return  serverInput;
    }

    @Test
    public void testDelOverdueServerForSomeTenant(){
        Map<String,Integer> serviceDelInfoMap = new HashMap<>();
        serviceDelInfoMap.put("s1",1);
        serviceDelInfoMap.put("s2",2);
        serverHandler.delOverdueServerForSomeTenant(serviceDelInfoMap,ServerTypeEnum.agent);
    }

    @Test
    public void delOverdueServer(){
        List<TenantDO> tenantDOS = tenantHandler.listAll();
        if(CollectionUtils.isEmpty(tenantDOS)){
            return;
        }
        int overdueDay = 30;
        String tenantId = tenantDOS.get(0).getId();
        ThreadLocalStore.setTenantInfoLocal(tenantId);
        List<TenantServerQuery> serverQueries = new ArrayList<>();
        serverQueries.add(new TenantServerQuery(tenantId,"__strange_host1","*************",ServerTypeEnum.agent.name()));
        serverQueries.add(new TenantServerQuery(tenantId,"__strange_host2","*************",ServerTypeEnum.agent.name()));

        List<ServerHeartInfoDO> serverDOS = serverHandler.listServer(serverQueries);
        if(CollectionUtils.isNotEmpty(serverDOS)){
            serverDOS.forEach(server->serverHandler.delServer(server.getId()));
        }

        ServerInput serverInput=mockServer(tenantId,HubChannelType.fast.name(),CloudTypeEnum.COLO.getType(),"us01","VA","__strange_host1","*************",ServerTypeEnum.agent);
        serverInput.setUpStatusTime(new Date(DateUtils.addDay(new Date(),-overdueDay-1)));
        serverHandler.addServer(serverInput);

        serverInput=mockServer(tenantId,HubChannelType.fast.name(),CloudTypeEnum.COLO.getType(),"us01","VA","__strange_host2","*************",ServerTypeEnum.agent);
        serverInput.setUpStatusTime(new Date(DateUtils.addDay(new Date(),-overdueDay-1)));
        serverHandler.addServer(serverInput);
        int delCount= serverHandler.delOverdueServer(overdueDay,ServerTypeEnum.agent,Arrays.asList(tenantId));
        Assertions.assertTrue(delCount>=2);
    }

    @Test
    public void getThread(){
        ThreadMXBean tmxb = ManagementFactory.getThreadMXBean();

        long[] tids= tmxb.getAllThreadIds();
        if(null == tids || tids.length <=0){
            return;
        }

        for(int i=0;i<tids.length;i++){
            ThreadInfo info=tmxb.getThreadInfo(tids[i]);
            System.out.println("[" + info.getThreadId() + "] " + info.getThreadName());
        }



        // Monitorynchronizer，
        ThreadInfo[] threadInfos = tmxb.dumpAllThreads(false, false);
        // ，D
        for (ThreadInfo info : threadInfos) {
            System.out.println("[" + info.getThreadId() + "] " + info.getThreadName());
        }
    }
    @Test
    public void batchAddSrver(){
        String tenantId="ced90eea-f83c-453c-bc94-c4073cae230d";
        for(int i=0;i<10;i++){
            for(int j=0;j<100;j++){
                List<ServerDO> serverDOS=new ArrayList<>();
                for(int k=0;k<200;k++){
                    ServerDO serverDO=new ServerDO();
                    serverDO.setId(IdUtils.generateId());
                    serverDO.setRegionId("VA");
                    serverDO.setClusterId("dev");
                    serverDO.setHost("host_"+i+"_"+j+"_"+k);
                    serverDO.setChannelType(HubChannelType.normal.name());
                    serverDO.setCloudType(CloudTypeEnum.COLO.getType());
                    serverDO.setIp(i+"."+j+"."+k+".1");
                    serverDO.setUnitTag("u1");
                    serverDO.setType(ServerTypeEnum.agent.name());
                    serverDO.setTenantId(tenantId);
                    serverDO.setGmtCreate(new Date());
                    serverDO.setGmtModify(new Date());
                    serverDO.setUpStatusTime(new Date());
                    serverDOS.add(serverDO);
                }
                serverHandler.batchAdd(serverDOS);

            }
        }
    }
    @Test
    public void getHubServerByIp() {
        ThreadLocalStore.setTenantInfoLocal("0ab0fa30-cbdc-456f-89c3-dc5188620edb");

        PageQuery<ServerQuery> pageQuery=new PageQuery<>();
        ServerQuery serverQuery=new ServerQuery();
//        serverQuery.setName("");
//        serverQuery.setStatus(ServerStatusEnum.offline.getCode());
        serverQuery.setType(ServerTypeEnum.hub.name());

        serverQuery.setUnitTag("luisPressUnit");
        pageQuery.setQueryPara(serverQuery);
        List<ServerDO> serverDOS=serverHandler.findByNameLike(pageQuery);
        serverHandler.getCountByNameLike(pageQuery);

        serverQuery.setType(ServerTypeEnum.agent.name());

        serverDOS=serverHandler.findByNameLikeWithTenant(pageQuery);
        serverHandler.getCountByNameLikeWithTenant(pageQuery);

//        serverHandler.getCountByNameLike("hell","agent","1",null);
//        serverHandler.getCountByNameLike("hell","agent","1",1);
//        serverHandler.getCountByNameLike("hell","agent",1);
//        serverHandler.getCountByNameLike("hell","agent",null);
//        serverHandler.findByNameLike("hell","agent","1",null,1,1);
//        serverHandler.findByNameLike("hell","agent","1",1,1,1);
//        serverHandler.findByNameLike("hell","agent",1,1,1);
//        serverHandler.findByNameLike("hell","agent",1,1,1);

         serverDOS=new ArrayList<>();
        ServerDO record=new ServerDO();
        record.setId("1");
        record.setUpStatusTime(new Date());
        serverDOS.add(record);
        record=new ServerDO();
        record.setId("2");
        record.setUpStatusTime(new Date());
        serverDOS.add(record);
        serverHandler.batchUpdateInstanceInfo(serverDOS);
        serverHandler.bachUpdateStatus(Arrays.asList("1","2","2","2","2","2","2","2","2","2","2"), ServerStatusEnum.online);

        ServerInput serverInput=new ServerInput();
        serverInput.setIp("********");
        serverInput.setHost("h1");
        serverInput.setRegionId("z1");
        serverInput.setClusterId("c1");

        ServerDO result = serverHandler.getHubServerByIp("************");
//        Assertions.assertNotNull(result);

        ServerDO serverDO=  serverHandler.geByTenantAndHost("WEB1","TiDB-manger", ServerTypeEnum.hub.name());
        Assertions.assertNotNull(serverDO);

        for(int i =0;i<1000;i++){
            List<ServerDO> toAddServers=new ArrayList<>(100);
            for(int j=0;j<200;j++){
                ServerDO server=new ServerDO();
                server.setIp(i+"."+j);
                server.setId(IdUtils.generateId());
                server.setCloudType(CloudTypeEnum.COLO.getType());
                server.setClusterId("c1");
                server.setUnitTag("");
                server.setUseZip(UseZipEnum.NO.getType());
                server.setChannelType(HubChannelType.normal.name());
                server.setHost("host"+i+""+j);
                server.setRegionId("r"+i);
            }
            serverHandler.batchAdd(toAddServers);
        }
    }




}
