package us.zoom.cube.site.lib.output.influxdb;

import com.influxdb.client.domain.Bucket;
import com.influxdb.client.domain.BucketRetentionRules;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import org.apache.commons.collections.CollectionUtils;
import us.zoom.infra.influx.model.dbrp.DbrpEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/9/26 9:04 AM
 */

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class BucketOut {

    String bucketId;

    String bucketName;

    int retentionInDays;

    DbrpOut relatedDbrp;

    public static BucketOut fromBucket(Bucket bucket, DbrpEntity dbrpEntity) {
        BucketOut bucketOut = new BucketOut();
        bucketOut.setBucketId(bucket.getId());
        bucketOut.setBucketName(bucket.getName());
        int retentionInDays = 0;
        List<BucketRetentionRules> retentionRules = bucket.getRetentionRules();
        if (!CollectionUtils.isEmpty(retentionRules)) {
            retentionInDays = retentionRules.get(0).getEverySeconds() / 24 / 3600;
        }
        bucketOut.setRetentionInDays(retentionInDays);
        if (dbrpEntity == null) {
            return bucketOut;
        }
        DbrpOut dbrpOut = new DbrpOut();
        dbrpOut.setDbrpId(dbrpEntity.getId());
        dbrpOut.setDatabaseName(dbrpEntity.getDatabase());
        dbrpOut.setRetentionPolicyName(dbrpEntity.getRetentionPolicy());
        bucketOut.setRelatedDbrp(dbrpOut);
        return bucketOut;
    }
}
