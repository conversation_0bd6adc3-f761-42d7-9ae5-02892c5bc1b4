package us.zoom.cube.site.core.auth;

import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import us.zoom.cube.lib.monitor.MonitorWrapper;
import us.zoom.infra.dao.model.MenuItemDO;
import us.zoom.infra.dao.service.MenuItemDAO;
import us.zoom.infra.dao.service.RoleMenuRelaDAO;
import us.zoom.infra.model.menu.MenuTree;
import us.zoom.cube.lib.utils.JsonUtils;
import us.zoom.infra.utils.MenuConstants;

import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class MenuHandler {

    @Autowired
    private MenuItemDAO menuItemDAO;

    @Autowired
    private RoleMenuRelaDAO roleMenuRelaDAO;

    private Logger monitorLog = LoggerFactory.getLogger("Monitor");

    private AtomicReference<Map<String,MenuItemDO>> resIdMenuMapRef = new AtomicReference<>(new HashMap<>());



    @PostConstruct
    public void loadMenuTree() {
        MonitorWrapper monitorWrapper = new MonitorWrapper("load_menu_tree");
        long begin = System.currentTimeMillis();
        try {
            List<MenuItemDO> allMenus= menuItemDAO.listAll();
            monitorWrapper.addField("loadDbTime",System.currentTimeMillis() - begin);
            if(CollectionUtils.isEmpty(allMenus)){
                monitorWrapper.addTag("phase","no_menu");
                monitorWrapper.addTag("status","fail");
                printMonitorLog(monitorWrapper, begin);
                return;
            }

            Map<String,MenuItemDO> resIdMenuMap = new HashMap<>();
            allMenus.forEach(item->{
                resIdMenuMap.put(item.getResourceId(),item);
            });

            if(MapUtils.isNotEmpty(resIdMenuMap)){
                resIdMenuMapRef.set(resIdMenuMap);
                monitorWrapper.addTag("status","success");
            }

        }catch (Exception e){
            log.error("load menu tree error ",e);
            monitorWrapper.addTag("status","fail");
            monitorWrapper.addField("msg",e.getMessage());
            monitorWrapper.addField("exp",JsonUtils.toJsonStringIgnoreExp(e));
        }
        printMonitorLog(monitorWrapper, begin);
    }




    private void loopLoadMenuTree(List<MenuItemDO> allMenus, MenuTree rootTree, Set<String> missedParents) {
        List<MenuItemDO> nextLoopMenus = allMenus;
        //Map<Menu resource id, MenuTree>
        Map<String,MenuTree> menuTreeMap = new HashMap<>();
        menuTreeMap.put(MenuConstants.DEFAULT_MENU_ROOT,rootTree);
        Set<String> parentSourceIds =  new HashSet<>(Arrays.asList(MenuConstants.DEFAULT_MENU_ROOT));

        while(CollectionUtils.isNotEmpty(parentSourceIds)){
            Set<String> newParentSourceIds = new HashSet<>();
            for(MenuItemDO nextLoopMenuItem : nextLoopMenus){
                if(parentSourceIds.contains(nextLoopMenuItem.getParentResId())){
                    MenuTree father =  menuTreeMap.get(nextLoopMenuItem.getParentResId());
                    if( null == father){
                        if(StringUtils.isNotBlank(nextLoopMenuItem.getParentResId())){
                            missedParents.add(nextLoopMenuItem.getParentResId());
                        }
                        continue;
                    }
                    MenuTree son =  MenuTree.copyFromDO(nextLoopMenuItem);
                    father.addSon(son);
                    Collections.sort(father.getSons());
                    menuTreeMap.put(son.getResourceId(),son);
                    newParentSourceIds.add(son.getResourceId());
                }
            }
            if(CollectionUtils.isNotEmpty(nextLoopMenus)){
                nextLoopMenus  = nextLoopMenus.stream().filter(item-> !newParentSourceIds.contains(item.getResourceId()) ).collect(Collectors.toList());
            }
            parentSourceIds = newParentSourceIds;
        }
    }

    private void printMonitorLog(MonitorWrapper monitorWrapper, long begin) {
        monitorWrapper.setTs(System.currentTimeMillis());
        monitorWrapper.addField("totalTime",System.currentTimeMillis()-begin);
        monitorLog.info(JsonUtils.toJsonStringIgnoreExp(monitorWrapper));
    }

    public void addMenuItem(MenuItemDO menuItemDO) {
        menuItemDAO.addMenuItem(menuItemDO);
    }

    public void updateOrder(String resId, Integer order) {
        menuItemDAO.updateOrder(resId,order);
    }

    public MenuItemDO getOneSon(String parentResId) {
       return menuItemDAO.getOneSon(parentResId);
    }


    public MenuTree getMenuTreeWitchCheck(Set<String> checkedMenuResIds){
        MenuTree rootTree = new MenuTree(MenuConstants.DEFAULT_MENU_ROOT, new ArrayList<>());
        loopLoadMenuTree(menuItemDAO.listAll(),rootTree,new HashSet<>());
        rootTree.setCheckedResIds(checkedMenuResIds);
        return rootTree;
    }

    public String getMenuResourceUrlByResId(String menuResId){
       MenuItemDO  menuItemDO = resIdMenuMapRef.get().get(menuResId);
       if(null == menuItemDO ){
           return  null;
       }
       return menuItemDO.getResourceUrl();
    }

    public Set<String> getMenuResUrlsByResIds(Collection<String> menuResIds){
        if(CollectionUtils.isEmpty(menuResIds)){
            return Collections.EMPTY_SET;
        }
        Set<String> result = new HashSet<>();
        for(String resId : menuResIds){
          String resUlr =    getMenuResourceUrlByResId(resId);
          if(StringUtils.isNotBlank(resUlr)){
              result.add(resUlr);
          }
        }
        return result;
    }

    /**
     * delete
     * @param resourceId
     *
     */
    @Transactional
    public void deleteMenuAndRelaBetweenRoleByResId(String resourceId) {
        menuItemDAO.deleteMenuBySourceId(resourceId);
        roleMenuRelaDAO.deleteRelaByMenuResId(resourceId);
    }


    public MenuItemDO getMenuById(String id) {
        return menuItemDAO.getById(id);
    }

    public void editMenu( MenuItemDO menuItemDO) {
        menuItemDAO.editMenu(menuItemDO);
    }

    public MenuTree getMenuTreeFromCacheByResIds(Set<String> menuResIds) {
        if(CollectionUtils.isEmpty(menuResIds)){
            return MenuTree.emptyTree();
        }
        MenuTree rootTree = new MenuTree(MenuConstants.DEFAULT_MENU_ROOT, new ArrayList<>());

        List<MenuItemDO> menuItemDOS = new ArrayList<>();
        menuResIds.forEach(menuResId->{
            MenuItemDO itemDO =  resIdMenuMapRef.get().get(menuResId);
            if(null != itemDO){
                menuItemDOS.add(itemDO);
            }
        });
        loopLoadMenuTree(menuItemDOS,rootTree,new HashSet<>());
        return rootTree;
    }

    public List<MenuItemDO> listAllMenu() {
        return menuItemDAO.listAll();
    }

    public MenuItemDO getMenuItemByResId(String resId){
        return  menuItemDAO.getByResId(resId);
    }

    public List<String> getExistingResourceIds(List<String> resourceIds) {
        return menuItemDAO.getExistingResourceIds(resourceIds);
    }

}
