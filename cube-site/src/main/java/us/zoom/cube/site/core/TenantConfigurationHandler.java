package us.zoom.cube.site.core;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import us.zoom.infra.dao.model.TenantConfigurationDO;
import us.zoom.infra.dao.service.TenantConfigurationDAO;

import java.util.ArrayList;
import java.util.List;

@Component
public class TenantConfigurationHandler {
    @Autowired
    private TenantConfigurationDAO tenantConfigurationDAO;

    public List<TenantConfigurationDO> selectByTenantIds(List<String> tenantIds) {
        if (CollectionUtils.isEmpty(tenantIds)) {
            return new ArrayList<>();
        }
        return tenantConfigurationDAO.selectByTenantIds(tenantIds);
    }

    public TenantConfigurationDO selectByTenantId(String tenantId) {
        return tenantConfigurationDAO.selectByTenantId(tenantId);
    }

    public void batchInsert(List<TenantConfigurationDO> tenantConfigurationDOSToAdd) {
        if (CollectionUtils.isEmpty(tenantConfigurationDOSToAdd)) {
            return;
        }
        tenantConfigurationDAO.batchInsert(tenantConfigurationDOSToAdd);
    }

    public void batchUpdate(List<TenantConfigurationDO> tenantConfigurationDOSToUpdate) {
        if (CollectionUtils.isEmpty(tenantConfigurationDOSToUpdate)) {
            return;
        }
        tenantConfigurationDAO.batchUpdate(tenantConfigurationDOSToUpdate);
    }

    public int insertConfiguration(TenantConfigurationDO tenantConfigurationDO) {
        return tenantConfigurationDAO.insertConfiguration(tenantConfigurationDO);
    }

    public int editConfiguration(TenantConfigurationDO tenantConfigurationDO) {
        return tenantConfigurationDAO.editConfiguration(tenantConfigurationDO);
    }
}
