package us.zoom.cube.site.infra.utils.trace;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @author: eason.jia
 * @date: 2024/8/19
 */
public enum OperatorPrecedence {
    NOOP_PRECEDENCE,
    VALUE_PRECEDENCE,
    FUNCTIONAL_PRECEDENCE,
    PREFIX_PRECEDENCE,
    EXPONENTIAL_PRECEDENCE,
    ADDITIVE_PRECEDENCE,
    BITWISE_PRECEDENCE,
    BITWISE_SHIFT_PRECEDENCE,
    MULTIPLICATIVE_PRECEDENCE,
    COMPARATOR_PRECEDENCE,
    TERNARY_PRECEDENCE,
    LOGICAL_AND_PRECEDENCE,
    LOGICAL_OR_PRECEDENCE,
    SEPARATE_PRECEDENCE;

    public static OperatorPrecedence findOperatorPrecedenceForSymbol(OperatorSymbol symbol) {
        switch (symbol) {
            case NOOP:
                return OperatorPrecedence.NOOP_PRECEDENCE;
            case VALUE:
                return OperatorPrecedence.VALUE_PRECEDENCE;
            case EQ:
            case NEQ:
            case GT:
            case LT:
            case GTE:
            case LTE:
            case REQ:
            case NREQ:
            case IN:
                return OperatorPrecedence.COMPARATOR_PRECEDENCE;
            case AND:
                return OperatorPrecedence.LOGICAL_AND_PRECEDENCE;
            case OR:
                return OperatorPrecedence.LOGICAL_OR_PRECEDENCE;
            case BITWISE_AND:
            case BITWISE_OR:
            case BITWISE_XOR:
                return OperatorPrecedence.BITWISE_PRECEDENCE;
            case BITWISE_LSHIFT:
            case BITWISE_RSHIFT:
                return OperatorPrecedence.BITWISE_SHIFT_PRECEDENCE;
            case PLUS:
            case MINUS:
                return OperatorPrecedence.ADDITIVE_PRECEDENCE;
            case MULTIPLY:
            case DIVIDE:
            case MODULUS:
                return OperatorPrecedence.MULTIPLICATIVE_PRECEDENCE;
            case EXPONENT:
                return OperatorPrecedence.EXPONENTIAL_PRECEDENCE;
            case BITWISE_NOT:
            case NEGATE:
            case INVERT:
                return OperatorPrecedence.PREFIX_PRECEDENCE;
            case COALESCE:
            case TERNARY_TRUE:
            case TERNARY_FALSE:
                return OperatorPrecedence.TERNARY_PRECEDENCE;
            case ACCESS:
            case FUNCTIONAL:
                return OperatorPrecedence.FUNCTIONAL_PRECEDENCE;
            case SEPARATE:
                return OperatorPrecedence.SEPARATE_PRECEDENCE;
        }

        return OperatorPrecedence.VALUE_PRECEDENCE;
    }

    public static Map<String, OperatorSymbol> comparatorSymbols = new HashMap<>();

    static {
        comparatorSymbols.put("==", OperatorSymbol.EQ);
        comparatorSymbols.put("!=", OperatorSymbol.NEQ);
        comparatorSymbols.put(">", OperatorSymbol.GT);
        comparatorSymbols.put(">=", OperatorSymbol.GTE);
        comparatorSymbols.put("<", OperatorSymbol.LT);
        comparatorSymbols.put("<=", OperatorSymbol.LTE);
        comparatorSymbols.put("=~", OperatorSymbol.REQ);
        comparatorSymbols.put("!~", OperatorSymbol.NREQ);
        comparatorSymbols.put("in", OperatorSymbol.IN);
    }

    public static Map<String, OperatorSymbol> logicalSymbols = new HashMap<>();

    static {
        logicalSymbols.put("&&", OperatorSymbol.AND);
        logicalSymbols.put("||", OperatorSymbol.OR);
    }

    public static Map<String, OperatorSymbol> bitwiseSymbols = new HashMap<>();

    static {
        bitwiseSymbols.put("^", OperatorSymbol.BITWISE_XOR);
        bitwiseSymbols.put("&", OperatorSymbol.BITWISE_AND);
        bitwiseSymbols.put("|", OperatorSymbol.BITWISE_OR);
    }

    public static Map<String, OperatorSymbol> bitwiseShiftSymbols = new HashMap<>();

    static {
        bitwiseShiftSymbols.put(">>", OperatorSymbol.BITWISE_RSHIFT);
        bitwiseShiftSymbols.put("<<", OperatorSymbol.BITWISE_LSHIFT);
    }

    public static Map<String, OperatorSymbol> additiveSymbols = new HashMap<>();

    static {
        additiveSymbols.put("+", OperatorSymbol.PLUS);
        additiveSymbols.put("-", OperatorSymbol.MINUS);
    }

    public static Map<String, OperatorSymbol> multiplicativeSymbols = new HashMap<>();

    static {
        multiplicativeSymbols.put("*", OperatorSymbol.MULTIPLY);
        multiplicativeSymbols.put("/", OperatorSymbol.DIVIDE);
        multiplicativeSymbols.put("%", OperatorSymbol.MODULUS);
    }

    public static Map<String, OperatorSymbol> exponentialSymbolsS = new HashMap<>();

    static {
        exponentialSymbolsS.put("**", OperatorSymbol.EXPONENT);
    }

    public static Map<String, OperatorSymbol> prefixSymbols = new HashMap<>();

    static {
        prefixSymbols.put("-", OperatorSymbol.NEGATE);
        prefixSymbols.put("!", OperatorSymbol.INVERT);
        prefixSymbols.put("~", OperatorSymbol.BITWISE_NOT);
    }

    public static Map<String, OperatorSymbol> ternarySymbols = new HashMap<>();

    static {
        ternarySymbols.put("?", OperatorSymbol.TERNARY_TRUE);
        ternarySymbols.put(":", OperatorSymbol.TERNARY_FALSE);
        ternarySymbols.put("??", OperatorSymbol.COALESCE);
    }

    public static Map<String, OperatorSymbol> modifierSymbols = new HashMap<>();

    static {
        modifierSymbols.put("+", OperatorSymbol.PLUS);
        modifierSymbols.put("-", OperatorSymbol.MINUS);
        modifierSymbols.put("*", OperatorSymbol.MULTIPLY);
        modifierSymbols.put("/", OperatorSymbol.DIVIDE);
        modifierSymbols.put("%", OperatorSymbol.MODULUS);
        modifierSymbols.put("**", OperatorSymbol.EXPONENT);
        modifierSymbols.put("&", OperatorSymbol.BITWISE_AND);
        modifierSymbols.put("|", OperatorSymbol.BITWISE_OR);
        modifierSymbols.put("^", OperatorSymbol.BITWISE_XOR);
        modifierSymbols.put(">>", OperatorSymbol.BITWISE_RSHIFT);
        modifierSymbols.put("<<", OperatorSymbol.BITWISE_LSHIFT);
    }

    public static Map<String, OperatorSymbol> separatorSymbols = new HashMap<>();

    static {
        separatorSymbols.put(",", OperatorSymbol.SEPARATE);
    }

    public static boolean isModifierType(OperatorSymbol thisSymbol, List<OperatorSymbol> candidate) {
        for (OperatorSymbol symbolType : candidate) {
            if (thisSymbol == symbolType) {
                return true;
            }
        }
        return false;
    }

    public static String operatorSymbolToString(OperatorSymbol symbol) {
        switch (symbol) {
            case NOOP:
                return "NOOP";
            case VALUE:
                return "VALUE";
            case EQ:
                return "=";
            case NEQ:
                return "!=";
            case GT:
                return ">";
            case LT:
                return "<";
            case GTE:
                return ">=";
            case LTE:
                return "<=";
            case REQ:
                return "=~";
            case NREQ:
                return "!~";
            case AND:
                return "&&";
            case OR:
                return "||";
            case IN:
                return "in";
            case BITWISE_AND:
                return "&";
            case BITWISE_OR:
                return "|";
            case BITWISE_XOR:
                return "^";
            case BITWISE_LSHIFT:
                return "<<";
            case BITWISE_RSHIFT:
                return ">>";
            case PLUS:
                return "+";
            case MINUS:
                return "-";
            case MULTIPLY:
                return "*";
            case DIVIDE:
                return "/";
            case MODULUS:
                return "%";
            case EXPONENT:
                return "**";
            case NEGATE:
                return "-";
            case INVERT:
                return "!";
            case BITWISE_NOT:
                return "~";
            case TERNARY_TRUE:
                return "?";
            case TERNARY_FALSE:
                return ":";
            case COALESCE:
                return "??";
        }
        return "";
    }
}
