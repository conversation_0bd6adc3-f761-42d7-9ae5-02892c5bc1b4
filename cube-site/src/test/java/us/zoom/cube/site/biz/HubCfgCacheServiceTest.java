package us.zoom.cube.site.biz;

import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

/**
 * @Author: <EMAIL>
 * @ModuleOwner: <EMAIL>
 * @Date: 03/23/2022 16:22
 * @Description:
 */
public class HubCfgCacheServiceTest {
    @Test
    public void test() {
        List<String> list = Arrays.asList("aa", "bb", "cc");
        list.forEach(str -> {
            if (str.equals("bb")) {
                return;
            }
            System.out.println(str);
        });
    }
}
