package us.zoom.cube.site.biz;

import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.site.biz.clickhouse.ClickhouseMetricsService;
import us.zoom.cube.site.core.CardHandler;
import us.zoom.cube.site.core.DashHandler;
import us.zoom.cube.site.core.TenantHandler;
import us.zoom.cube.site.core.dashboard.CardQueryUtil;
import us.zoom.cube.site.core.dashboard.CardQuerySqlBuilder;
import us.zoom.cube.site.infra.utils.ConvertUtils;
import us.zoom.cube.site.infra.utils.influx.InfluxSqlParserUtils;
import us.zoom.cube.site.lib.*;
import us.zoom.cube.site.lib.input.BatchCardInput;
import us.zoom.cube.site.lib.input.CardInput;
import us.zoom.cube.site.lib.input.CardSqlInput;
import us.zoom.cube.site.lib.output.PageResult;
import us.zoom.cube.site.lib.query.CardDataQuery;
import us.zoom.cube.site.lib.query.NameQuery;
import us.zoom.cube.site.lib.query.PageQuery;
import us.zoom.infra.clickhouse.ClickhouseConst;
import us.zoom.infra.clickhouse.ClickhouseSqlUtil;
import us.zoom.infra.clickhouse.QueryMonitor;
import us.zoom.infra.dao.model.CardDO;
import us.zoom.infra.dao.model.TenantDO;
import us.zoom.infra.enums.CardTypeEnum;
import us.zoom.infra.influx.service.InfluxService;
import us.zoom.infra.utils.DashConstants;
import us.zoom.cube.lib.utils.JsonUtils;

import java.util.*;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class CardService {

    public static final String SNIPPETS = "${snippet}";

    private static final String EDIT_CARD = "Edited card";
    private static final String DELETE_CARD = "Deleted card";
    private static final String ADDED_CARD = "Added card";

    @Autowired
    private CardHandler cardHandler;

    @Autowired
    private AuthService authService;

    @Autowired
    private InfluxService influxService;

    @Autowired
    private TenantService tenantService;

    @Autowired
    private TenantHandler tenantHandler;

    @Autowired
    private DashHandler dashHandler;

    @Autowired
    ClickhouseMetricsService clickhouseMetricsService;

    @Autowired
    private DashService dashService;


    public ResponseObject searchCard(PageQuery<NameQuery> pageQuery) {
        List<CardDO> cardDOS=cardHandler.searchCard(pageQuery);
        Integer totalCount=cardHandler.countCardByName(pageQuery);
        return ResponseObject.success(new PageResult(totalCount,cardDOS));
    }


    public ResponseObject listCardByDash(IdPara idPara){
        //hash such dash
        return ResponseObject.success(cardHandler.listCardByDash(idPara.getId()));
    }

    public ResponseObject add(CardInput cardInput){
        authService.mustDashOwner(cardInput.getUserId(),cardInput.getDashId());
        cardInput.checkNameAndDesc();
        String id= cardHandler.add(cardInput);
        String currentDashJson = dashService.getCurrentDashJson(cardInput.getDashId());
        dashService.saveHistory(currentDashJson, cardInput.getDashId(), ADDED_CARD);
        dashHandler.rescanDashTenant(cardInput.getDashId());
        return ResponseObject.success(id);
    }

    @Transactional
    public ResponseObject edit(CardInput cardInput){
        String dashId=  cardHandler.getDashIdByCardId(cardInput.getId());
        authService.mustDashOwner(cardInput.getUserId(),dashId);
        cardInput.checkNameAndDesc();

        String currentDashJson = dashService.getCurrentDashJson(cardInput.getDashId());
        cardHandler.edit(cardInput);
        dashService.saveHistory(currentDashJson, cardInput.getDashId(), EDIT_CARD);
        return ResponseObject.success(true);
    }

    public ResponseObject parseSql(CardSqlInput cardSqlInput){

        String sql = cardSqlInput.getSql();
        if (ClickhouseConst.CLICKHOUSE_DB_TYPE.equals(cardSqlInput.getDbType())) {
            return ResponseObject.success(clickhouseMetricsService.parseSql(sql));
        }
        Map res = InfluxSqlParserUtils.parserSql(sql);
        String clickHouseSql = ClickhouseSqlUtil.influxQL2ChSql(ClickhouseSqlUtil.doPlaceholder(sql), new QueryMonitor());
        res.put("clickHouseSql",clickHouseSql);
        return ResponseObject.success(res);
    }

    @Transactional
    public ResponseObject delete(IdListPara idPara) {
        idPara.check();

        String dashId = cardHandler.getDashIdByCardId(idPara.getIds().get(0));
        String currentDashJson = dashService.getCurrentDashJson(dashId);
        authService.mustDashOwner(idPara.getUserId(), dashId);
        idPara.getIds().forEach(id -> {
            String tempDashId = cardHandler.getDashIdByCardId(id);
            if (!tempDashId.equals(dashId)) {
                throw new BusinessException("All cards should be in same dash");
            }

            cardHandler.delete(id);
        });
        dashService.saveHistory(currentDashJson, dashId, DELETE_CARD);
        dashHandler.rescanDashTenant(dashId);

        return ResponseObject.success(true);
    }


    public ResponseObject getById(IdPara idPara){

        return ResponseObject.success(cardHandler.getById(idPara.getId()));
    }


    public ResponseObject getData(CardDataQuery cardDataQuery) {
        CardDO cardDO= cardHandler.getById(cardDataQuery.getId());
        String database=null;
        if (CardTypeEnum.USER.getCode().equals(cardDO.getType())) {
            JsonNode jsonNode = JsonUtils.getValueFromJson(cardDO.getConfigs(), Arrays.asList(CardHandler.CARD_QUERYCFG, CardHandler.CARD_DB));
            database = Optional.ofNullable(jsonNode).map(JsonNode::asText).orElse(null);
//            JSONObject configs = JSONObject.parseObject(cardDO.getConfigs());
//            database = configs.getJSONObject(CardHandler.CARD_QUERYCFG).getString(CardHandler.CARD_DB);
            authService.mustHasSuchDb(cardDataQuery.getUserId(), database);
        }else {
            TenantDO tenantDO=  tenantService.getTenantById(cardDataQuery.getTenantId());
            database=StringUtils.lowerCase(tenantDO.getName());
        }
        String sql= InfluxSqlParserUtils.checkAndAdjustQueryLimit(cardDataQuery.getSql());
        cardDataQuery.setSql(sql);
        return ResponseObject.success(influxService.getMetricDisplay(StringUtils.lowerCase(database),sql));
    }

    public ResponseObject editConfigs(CardInput cardInput) {
        String dashId=  cardHandler.getDashIdByCardId(cardInput.getId());
        authService.mustDashOwner(cardInput.getUserId(),dashId);
        cardHandler.editConfigs(cardInput);
        dashHandler.rescanDashTenant(dashId);
        return ResponseObject.success(true);
    }

    public ResponseObject batchAdd(BatchCardInput cardInputs) {
        authService.mustDashOwner(cardInputs.getUserId(),cardInputs.getDashId());
        //make cards ordered
        int i = 0;
        for (CardInput input : cardInputs.getCardInputs()) {
            input.setId(String.format("%04d", i++));
        }

        Map<String, String> cardIds= cardHandler.batchAdd(cardInputs);
        String currentDashJson = dashService.getCurrentDashJson(cardInputs.getDashId());
        dashService.saveHistory(currentDashJson, cardInputs.getDashId(), ADDED_CARD);
        dashHandler.rescanDashTenant(cardInputs.getDashId());

        return ResponseObject.success(cardIds.values());
    }

    public ResponseObject batchJsonAdd(BatchCardInput batchCardInput) {
        authService.mustDashOwner(batchCardInput.getUserId(),batchCardInput.getDashId());
        if(null != batchCardInput){
            if(!CollectionUtils.isEmpty(batchCardInput.getCardInputs())){
                for (CardInput cardInput : batchCardInput.getCardInputs()){
                    Map allCfgMap = new HashMap();
                    if(!StringUtils.isEmpty(cardInput.getQuery())){
                        Map<String, Object> m = ClickhouseSqlUtil.parseSql(cardInput.getQuery(), false);
                        if(!CollectionUtils.isEmpty(m)){
                            Map sqlMap = new HashMap();
                            if(!CollectionUtils.isEmpty(cardInput.getQueryCfg())){
                                sqlMap.putAll(cardInput.getQueryCfg());
                            }

                            Object database = m.get(DashConstants.DATABASE);
                            if (null != database) {
                                sqlMap.put(DashConstants.DB, database);
                            }
                            sqlMap.put(DashConstants.RAW_TEXT, cardInput.getQuery());
                            if(!CollectionUtils.isEmpty(sqlMap)){
                                allCfgMap.put(DashConstants.QUERY_CONFIG,sqlMap);
                            }
                        }
                    }
                    if (!CollectionUtils.isEmpty(cardInput.getConfigs())){
                        allCfgMap.putAll(cardInput.getConfigs());
                    }
                    allCfgMap.put("batch",true);
                    cardInput.setConfigs(allCfgMap);
                }
            }
        }
        Map<String, String> cardIds= cardHandler.batchAdd(batchCardInput);
        String currentDashJson = dashService.getCurrentDashJson(batchCardInput.getDashId());
        dashService.saveHistory(currentDashJson, batchCardInput.getDashId(), ADDED_CARD);
        dashHandler.rescanDashTenant(batchCardInput.getDashId());
        return ResponseObject.success(cardIds.values());
    }


    public ResponseObject addCondition2Sql(BatchAddCondition2SqlPara addCondition2SqlPara) {

        //authService.checkAuth(addCondition2SqlPara);
        List<String> ret = Lists.newArrayList();
        for (AddCondition2SqlPara para : addCondition2SqlPara.getParaList()) {
            ret.add(ClickhouseSqlUtil.addCondition2Sql(para.getSql(), para.getParams()));
        }

        return ResponseObject.success(ret);
    }

    public ResponseObject getCardSqlAndDesc(IdPara input) {
        Map<String, String> ret = new HashMap<>();
        CardDO byId = cardHandler.getById(input.getId());
        if (null == byId) {
            return ResponseObject.success(ret);
        }

        String configs = byId.getConfigs();
        Map<String, Object> cardProperties = CardQueryUtil.transCardQueryConfigsToSet(configs);
        ret.put(DashConstants.DESC, byId.getDescription());
        Map<String, Object> queryConfig = (LinkedHashMap) cardProperties.get(DashConstants.QUERY_CONFIG);
        Object rawTextObj = queryConfig.get(DashConstants.RAW_TEXT);
        if (null != rawTextObj) {
            ret.put(DashConstants.SQL, (String) rawTextObj);
        } else {
            CardQuerySqlBuilder.Active active = ConvertUtils.mapToBean(queryConfig, CardQuerySqlBuilder.Active.class);
            //active.functionSelectors.put(DashConstants.TAG_VALUES, queryConfig.get(DashConstants.TAG_VALUES));
            CardQuerySqlBuilder.SqlReturnObject relative = CardQuerySqlBuilder.genInfluxQLFromBuilder(
                    active, new CardQuerySqlBuilder.FilterValue(new CardQuerySqlBuilder.TimeRangeValue(DashConstants.RELATIVE, "")), new HashMap<>());
            ret.put(DashConstants.SQL, relative.template);

        }

        return ResponseObject.success(ret);
    }
}
