package us.zoom.cube.site.core.processors;

import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import us.zoom.cube.lib.hub.GroovyProcessorCfg;
import us.zoom.cube.lib.hub.ProcessorTypeEnum;
import us.zoom.cube.site.core.parser.process.core.processor.Processor;
import us.zoom.cube.site.core.parser.process.core.processor.ProcessorFactory;
import us.zoom.cube.site.core.parser.process.core.processor.ProcessorResp;


import java.util.HashMap;
import java.util.Map;

/**
 * @Author: <EMAIL>
 * @ModuleOwner: <EMAIL>
 * @Date: 01/11/2023 17:36
 * @Description:
 */
public class GroovyMapperProcessorTest {
    @Test
    public void test() {
        Processor processor = ProcessorFactory.getInstance(ProcessorTypeEnum.GroovyProcessorCfg.name());
        Map<String, Object> map = new HashMap<>();
        map.put("measure", "test");
        GroovyProcessorCfg processorCfg = new GroovyProcessorCfg();
        processorCfg.setParseRule("    def parseMessage(Map<String, Object> messageMap) {\n" +
                "        messageMap.put(\"hostCopy\", \"host\")\n" +
                "    }");
        processorCfg.setInvokeFunction("parseMessage");
        ProcessorResp resp = processor.process(map, processorCfg);
        Assertions.assertEquals(resp.isContinue(), true);
        Assertions.assertEquals(map.size(), 2);


        processorCfg.setParseRule("public void parseMessage(Map<String, Object> messageMap) {\n" +
                "        messageMap.put(\"hostCopy\", \"host\")\n" +
                "    }");
        processorCfg.setInvokeFunction("parseMessage");
        ProcessorResp resp1 = processor.process(map, processorCfg);
        Assertions.assertEquals(resp1.isContinue(), true);
        Assertions.assertEquals(map.size(), 2);
    }


    @Test
    public void testFail() {
        Processor processor = ProcessorFactory.getInstance(ProcessorTypeEnum.GroovyProcessorCfg.name());
        Map<String, Object> map = new HashMap<>();
        map.put("measure", "test");
        GroovyProcessorCfg processorCfg = new GroovyProcessorCfg();
        processorCfg.setParseRule("    def parseMessage(Map<String, Object> messageMap) {\n" +
                "        messageMap.put(\"hostCopy\", \"host\")\n" +
                "    }");
        processorCfg.setInvokeFunction("parseMessage2");
        ProcessorResp resp = processor.process(map, processorCfg);
        Assertions.assertEquals(resp.isContinue(), false);
        Assertions.assertEquals(StringUtils.isNotBlank(resp.getFailMessage()), true);

    }


    @Test
    public void testInnerJson() {
        Processor processor = ProcessorFactory.getInstance(ProcessorTypeEnum.GroovyProcessorCfg.name());
        Map<String, Object> map = new HashMap<>();
        map.put("measure", "test");
        GroovyProcessorCfg processorCfg = new GroovyProcessorCfg();
        processorCfg.setParseRule("    def parseMessage(Map<String, Object> messageMap) {\n" +
                "        messageMap.put(\"hostCopy\", \"host\")\n" +
                "    }");
        processorCfg.setInvokeFunction("parseMessage2");
        ProcessorResp resp = processor.process(map, processorCfg);
        Assertions.assertEquals(resp.isContinue(), false);
        Assertions.assertEquals(StringUtils.isNotBlank(resp.getFailMessage()), true);

    }



}
