package us.zoom.cube.site.biz;


import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import us.zoom.cube.site.lib.input.tpl.ParserTplInput;
import us.zoom.infra.thread.ThreadLocalStore;

public class MonitorTplServiceTest extends BaseTest {
    String user1="82b4e894-edc7-47b8-ae99-58b6cbf58326";
    String tenantId="0ab0fa30-cbdc-456f-89c3-dc5188620edb";

    @Autowired
    private MonitorTplService monitorTplService;


    @Test
    public void test(){
        ThreadLocalStore.setUserInfoLocal(user1);
        ThreadLocalStore.setTenantInfoLocal(tenantId);
        ParserTplInput parserTplInput=new ParserTplInput();
        parserTplInput.setSourceParserId("4f3bfe9b-74ba-4490-a615-33e858ea102b");
        parserTplInput.setDestParserId("04154831-6b95-41d8-82ac-f9d007152dfc");
        monitorTplService.copyParser(parserTplInput);
    }

    @Test
    public void testRefreshAll(){
        ThreadLocalStore.setUserInfoLocal(user1);
        ThreadLocalStore.setTenantInfoLocal(tenantId);
        ParserTplInput parserTplInput=new ParserTplInput();
        parserTplInput.setSourceParserId("4f3bfe9b-74ba-4490-a615-33e858ea102b");
        monitorTplService.refreshParser(parserTplInput);
    }
}
