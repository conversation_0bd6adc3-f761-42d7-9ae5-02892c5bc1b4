package us.zoom.cube.site.infra.e2e;

import com.alibaba.fastjson.JSON;
import com.baomidou.lock.annotation.Lock4j;
import com.clickhouse.data.value.UnsignedLong;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;
import us.zoom.cube.lib.utils.JsonUtils;
import us.zoom.cube.site.biz.SysParaService;
import us.zoom.cube.site.biz.syspara.AlarmParaService;
import us.zoom.cube.site.core.SubEnvironmentHandler;
import us.zoom.cube.site.core.config.ClickhouseHandlerFactory;
import us.zoom.infra.dao.model.ProbeTaskSyncDO;
import us.zoom.infra.dao.service.ProbeTaskSyncDAO;
import us.zoom.infra.thread.NamedThreadFactory;
import us.zoom.mq.common.Result;
import us.zoom.mq.common.client.task.Task;
import us.zoom.mq.common.response.ProduceResult;

import java.time.Instant;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static us.zoom.infra.utils.CommonSplitConstants.COMMA_SPLIT;

/**
 * @description:
 * @author: <EMAIL>
 * @date: 2024-11-19 18:13
 **/
@Service
@Slf4j
@RequiredArgsConstructor
public class ProbeSyncTaskService {
    private final ProbeTaskSyncDAO probeTaskSyncDAO;
    private final ClickhouseHandlerFactory clickhouseHandlerFactory;
    private final SysParaService sysParaService;
    private final SubEnvironmentHandler subEnvironmentHandler;
    private final AlarmParaService alarmParaService;
    private final ApplicationContext applicationContext;
    private final String SERVICE_NAME = "cube_probe";
    private final String E2E_AGGREGATION_TIMEOUT = "cube.site.e2e.aggregation.timeout";
    private final Long DEFAULT_TIMEOUT = 180000L;


    @Value("${cube.site.e2e.aggregation.producer.topic:us_cube_probe_aggregation_job}")
    public String e2eProducerTopic;


    private final String SQL_TEMPLETE = """
            SELECT
                service,
                probeTaskId,
                probeTaskName,
                globalTaskUuid,
                avg(rt) avgRt,
                min(rt) minRt,
                max(rt) maxRt,
                avg(delay) avgDelay,
                min(delay) minDelay,
                max(delay) maxDelay,
                sum(CASE WHEN status = '1' THEN 1 ELSE 0 END) AS successCount,
                sum(CASE WHEN status != '1' THEN 1 ELSE 0 END) AS failCount,
                groupArray(
                    concat(
                        '{',
                        '"probePointGroupId": "', probePointGroupId, '", ',
                        '"status": "', status, '",',
                        '"probePointIp": "', probePointIp, '",',
                        '"probePointGroupName": "', probePointGroupName, '",',
                         '"rt": "', rt, '",',
                          '"delay": "', delay, '"',
                        '}'
                    )
                ) AS probePointGroupList
            FROM cube_probe.probe_task_metrics
            WHERE globalTaskUuid in(%s)
            GROUP BY service, probeTaskId, probeTaskName, globalTaskUuid
            """;
    private static final ThreadPoolExecutor e2eCacheThreadPool = new ThreadPoolExecutor(1, 10, 60L, TimeUnit.SECONDS, new LinkedBlockingQueue(100), new NamedThreadFactory("e2e syncTask theadPool "), new ThreadPoolExecutor.CallerRunsPolicy());

    /**
     * Use distributed lock + database optimistic lock combination
     * Distributed locks: distributed locks are based on redis clusters, avoiding deadlocks by automatically releasing them after setting the expiration time.
     * Optimistic lock: in the extreme case of distributed lock expires, other machines scramble for resources, there is a probability of execution of duplicate data
     * again through the optimistic lock to ensure data consistency
     */
    @Lock4j(expire = 10000L)
    public void syncTask() {
        log.info("[e2eCacheThreadPool]getQueue:{}", e2eCacheThreadPool.getQueue().size());
        log.info("[e2eCacheThreadPool]getCorePoolSize:{}", e2eCacheThreadPool.getCorePoolSize());
        log.info("[e2eCacheThreadPool]getMaximumPoolSize:{}", e2eCacheThreadPool.getMaximumPoolSize());
        log.info("[e2eCacheThreadPool]getPoolSize:{}", e2eCacheThreadPool.getPoolSize());
        log.info("[e2eCacheThreadPool]getTaskCount:{}", e2eCacheThreadPool.getTaskCount());
        StopWatch stopWatch = new StopWatch();
        //Query all data with pending status
        stopWatch.start("[ProbeSyncTaskService]findPendingStatus");
        List<ProbeTaskSyncDO> pendingList = probeTaskSyncDAO.findPendingStatus();
        stopWatch.stop();
        if (!CollectionUtils.isEmpty(pendingList)) {
            List<String> taskUuidList = pendingList.stream().map(ProbeTaskSyncDO::getGlobalTaskUuid).toList();
            String inParam = taskUuidList.stream().map(s -> "'" + s + "'").collect(Collectors.joining(COMMA_SPLIT));
            String adjustSql = String.format(SQL_TEMPLETE, inParam);
            stopWatch.start("[ProbeSyncTaskService]clickhouse");
            List<Map<String, Object>> ckDataMap = clickhouseHandlerFactory.get().query(SERVICE_NAME, adjustSql);
            stopWatch.stop();
            List<ProbeSyncTaskMetrics> probeSyncTaskMetricList = convertedProbeSyncTaskMetrics(ckDataMap);

            Map<String, ProbeTaskSyncDO> probeTaskSyncDOMap = pendingList.stream().collect(Collectors.toMap(ProbeTaskSyncDO::getGlobalTaskUuid, v -> v));

            Map<String, ProbeSyncTaskMetrics> probeSyncTaskMetricsMap = probeSyncTaskMetricList.stream().collect(Collectors.toMap(ProbeSyncTaskMetrics::getGlobalTaskUuid, v -> v));
            stopWatch.start("[ProbeSyncTaskService]CompletableFuture");
            List<CompletableFuture<String>> futureList = taskUuidList.stream().map(taskUuId -> CompletableFuture.supplyAsync(() -> execute(taskUuId, probeSyncTaskMetricsMap, probeTaskSyncDOMap), e2eCacheThreadPool)).toList();
            CompletableFuture.allOf(futureList.toArray(new CompletableFuture[]{})).join();
            stopWatch.stop();
        }
    }

    private String execute(String taskUuId, Map<String, ProbeSyncTaskMetrics> probeSyncTaskMetricsMap, Map<String, ProbeTaskSyncDO> probeTaskSyncDOMap) {
        try {
            ProbeSyncTaskMetrics probeSyncTaskMetrics = probeSyncTaskMetricsMap.get(taskUuId);
            ProbeTaskSyncDO probeTaskSyncDO = probeTaskSyncDOMap.get(taskUuId);
            ProbeSyncTaskService probeSyncTaskService = applicationContext.getBean(ProbeSyncTaskService.class);
            //Both mysql data and clickhouse data exists
            if (!Objects.isNull(probeSyncTaskMetrics) && !Objects.isNull(probeTaskSyncDO)) {
                Long successCount = probeSyncTaskMetrics.getSuccessCount();
                Long failCount = probeSyncTaskMetrics.getFailCount();
                Long sumCount = successCount + failCount;
                //Determine whether the quantity is the same
                List<String> probeGroups = probeTaskSyncDO.getProbeGroups();
                Long size = (long) probeGroups.size();
                if (Objects.equals(sumCount, size)) {
                    probeTaskSyncDO.setStatus(ProbeTaskSyncStatus.completed.name());
                    probeSyncTaskService.handleMetrics(probeSyncTaskMetrics, probeTaskSyncDO);
                } else {
                    //Determine whether the expiration date has been reached
                    if (isTimeOut(probeTaskSyncDO.getProduceTime(), probeTaskSyncDO.getTimeout())) {
                        probeSyncTaskMetrics.setFailCount(size - successCount);
                        probeSyncTaskMetrics.setTimeoutCount(size - sumCount);
                        probeTaskSyncDO.setStatus(ProbeTaskSyncStatus.timeout.name());
                        probeSyncTaskService.handleMetrics(probeSyncTaskMetrics, probeTaskSyncDO);
                    }
                }

            } else {
                //Determine if the timeout has been exceeded and clickhouse data still does not exist. Modify mysql task status. Avoid invalid data backlog
                if (isTimeOut(probeTaskSyncDO.getProduceTime(), probeTaskSyncDO.getTimeout())) {
                    probeTaskSyncDO.setStatus(ProbeTaskSyncStatus.deficiency.name());
                    //todo Printing logs and setting monitoring alarms
                    log.error("[ProbeSyncTaskService]loss data:{}", JSON.toJSONString(probeTaskSyncDO));
                    probeSyncTaskService.handleMetrics(probeSyncTaskMetrics, probeTaskSyncDO);
                }
            }
        } catch (Exception e) {
            log.error("[ProbeSyncTaskService]Synchronization task id{}, exception message{}", taskUuId, e.getMessage(), e);
        }
        return taskUuId;
    }

    private boolean isTimeOut(Long produceTime, Integer intervalSecond) {
        //e2e The time from the start of probing to writing to the clickhouse is typically 1 minute. So the timeout is set to a minimum of 2 minutes. This means that the latency of the alarm is at least 2 minutes.
        Long defaultTimeout = alarmParaService.getParamLongValue(E2E_AGGREGATION_TIMEOUT, DEFAULT_TIMEOUT);
        long timeout = defaultTimeout + intervalSecond * 1000L;
        return Instant.now().toEpochMilli() - produceTime > timeout;
    }

    private static List<ProbeSyncTaskMetrics> convertedProbeSyncTaskMetrics(List<Map<String, Object>> ckDataMap) {
        return ckDataMap.stream().map(e -> {
            ProbeSyncTaskMetrics probeSyncTaskMetrics = new ProbeSyncTaskMetrics();
            probeSyncTaskMetrics.setGlobalTaskUuid((String) e.get("globalTaskUuid"));
            probeSyncTaskMetrics.setService((String) e.get("service"));
            probeSyncTaskMetrics.setAppName(probeSyncTaskMetrics.getService());
            //This field needs to be created in advance in the cube-site's system variables
            probeSyncTaskMetrics.setTopType(ProbeSyncTaskMetricsEnum.COLLECT_TYPE.getType());
            probeSyncTaskMetrics.setType(ProbeSyncTaskMetricsEnum.PROBE_AGGREGATION_RESULT.getType());
            probeSyncTaskMetrics.setProbeTaskId((String) e.get("probeTaskId"));
            probeSyncTaskMetrics.setProbeTaskName((String) e.get("probeTaskName"));
            probeSyncTaskMetrics.setAvgDelay((Double) e.get("avgDelay"));
            probeSyncTaskMetrics.setMaxDelay((Double) e.get("maxDelay"));
            probeSyncTaskMetrics.setMinDelay((Double) e.get("minDelay"));
            probeSyncTaskMetrics.setAvgRt((Double) e.get("avgRt"));
            probeSyncTaskMetrics.setMinRt((Double) e.get("minRt"));
            probeSyncTaskMetrics.setMaxRt((Double) e.get("maxRt"));
            probeSyncTaskMetrics.setFailCount(((UnsignedLong) e.get("failCount")).longValue());
            probeSyncTaskMetrics.setSuccessCount(((UnsignedLong) e.get("successCount")).longValue());
            Object raw = e.get("probePointGroupList");
            String[] probePointGroupArr = new String[0];
             //Fix ClickhouseHandler.typeTransfer type conversion failure caused by adding list type
            if (raw instanceof List) {
                List<?> probePointGroupList = (List<?>) raw;
                probePointGroupArr = probePointGroupList.stream()
                        .filter(Objects::nonNull)
                        .map(Object::toString)
                        .toArray(String[]::new);
            } else if (raw != null && raw.getClass().isArray()) {
                Object[] array = (Object[]) raw;
                probePointGroupArr = Arrays.stream(array)
                        .filter(Objects::nonNull)
                        .map(Object::toString)
                        .toArray(String[]::new);
            } else {
                log.error("Unexpected type for probePointGroupList: {}", raw != null ? raw.getClass().getName() : "null");
            }
            probeSyncTaskMetrics.setProbePointGroupList(probePointGroupArr);

            return probeSyncTaskMetrics;
        }).toList();
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void handleMetrics(ProbeSyncTaskMetrics probeSyncTaskMetrics, ProbeTaskSyncDO probeTaskSyncDO) {
        //Separate things are executed to prevent one data anomaly in a batch from causing the batch to be rolled back.
        int updateCount = probeTaskSyncDAO.update(probeTaskSyncDO.getGlobalTaskUuid(), probeTaskSyncDO.getStatus(), probeTaskSyncDO.getVersion());
        if (updateCount == 0) {
            log.error("[ProbeSyncTaskService]Optimistic Lock Competition:{}", JSON.toJSONString(probeTaskSyncDO));
            return;
        }
        if (updateCount > 0 && !Objects.isNull(probeSyncTaskMetrics)) {
            Task<String> sourceTask = new Task<>();
            sourceTask.setTopicName(e2eProducerTopic);
            sourceTask.setTaskType(AsyncMqE2ESingleton.ASYNC_MQ_TYPE);
            probeSyncTaskMetrics.setCreateTime(Instant.now().toEpochMilli());
            sourceTask.setPayload(JsonUtils.toJsonString(probeSyncTaskMetrics));
            Result<ProduceResult> result = AsyncMqE2ESingleton.getInstance().getProducer().sendSync(sourceTask);
            if (!result.isSuccess()) {
                log.error("[ProbeSyncTaskService]Failed to push data from e2e producer:{}", JSON.toJSONString(probeSyncTaskMetrics));
                throw new RuntimeException("Failed to push data from e2e producer");
            }
        }

    }

    public int deleteByTime(Integer days) {
        LocalDateTime threeDaysAgo = LocalDateTime.now().minusDays(days);
        return probeTaskSyncDAO.deleteByTime(threeDaysAgo);
    }


}
