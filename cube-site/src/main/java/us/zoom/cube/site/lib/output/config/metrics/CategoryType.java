package us.zoom.cube.site.lib.output.config.metrics;

import lombok.Getter;

/**
 * Enum representing different categories of fields or tags
 * <AUTHOR>
 */
@Getter
public enum CategoryType {
    /**
     * Metric Variable
     */
    METRIC_VAR("Metric Variable"),
    SYSTEM_VAR("System Variable"),
    DERIVED_VAR("Derived Variable"),
    AI_VAR("AI Variable");

    private final String displayName;

    CategoryType(String displayName) {
        this.displayName = displayName;
    }

    @Override
    public String toString() {
        return displayName;
    }
}
