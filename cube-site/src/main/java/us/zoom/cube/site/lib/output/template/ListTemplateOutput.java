package us.zoom.cube.site.lib.output.template;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @Author: <PERSON>
 * @Date:12/19/2022 09:09
 * @Description:
 */
@Data
public class ListTemplateOutput {

    private String templateGroupName;

    private String templateGroupId;

    private String description;

    private List<TemplateOutput> templateList;

    @Data
    public static class TemplateOutput {

        private String id;

        private String name;

        private String serviceName;

        private String serviceId;

        private List<TemplateItemOutput> templateItemList;

        private Date modifyTime;

        private String editor;

        private String creator;

        private Boolean enabled;

        @Data
        public static class TemplateItemOutput {

            private String type;

            private List<TemplateItem> itemList;

            @Data
            public static class TemplateItem {

                private String id;

                private String name;

            }

        }

    }

}
