package us.zoom.cube.site.lib.input;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.util.Assert;
import us.zoom.cube.lib.integrations.MetricsFieldTypeEnum;

/**
 * @Author: <EMAIL>
 * @ModuleOwner: <EMAIL>
 * @Date: 08/15/2024 11:03
 * @Description:
 */
public class CollectorFieldInputTest {
    @Test
    public void isNotExist() {
        String fieldType = "test_type";
        String message = String.format("fieldType:%s,this value is illegal", fieldType);
        Exception exception = Assertions.assertThrows(IllegalArgumentException.class, () -> {
            Assert.isTrue(MetricsFieldTypeEnum.fromName(fieldType) != null, message);
        });
        Assertions.assertEquals(message, exception.getMessage());
    }
}
