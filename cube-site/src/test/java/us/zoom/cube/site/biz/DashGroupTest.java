package us.zoom.cube.site.biz;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import us.zoom.cube.site.core.CardHandler;
import us.zoom.cube.site.lib.IdPara;
import us.zoom.cube.site.lib.ResponseObject;
import us.zoom.cube.site.lib.input.DashTemplateGroupInput;
import us.zoom.cube.site.lib.input.DashTemplateInput;
import us.zoom.cube.site.lib.query.NameQuery;
import us.zoom.cube.site.lib.query.PageQuery;
import us.zoom.infra.dao.model.CardDO;
import us.zoom.infra.enums.DashtemplateTypeEnum;
import us.zoom.infra.thread.ThreadLocalStore;
import us.zoom.cube.lib.utils.JsonUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022-08-01 16:19
 */
public class DashGroupTest extends  BaseTest{
    @Autowired
    private DashTemplateService dashTemplateService;

    @Autowired
    private DashTemplateGroupService dashTemplateGroupService;


    @Autowired
    private CardHandler cardHandler;

    String sharedOwner="7e5c828d-d828-420d-91a5-f2e170b41d78";
    String dashId = "040f8b1d-1148-4bdc-b216-db7275486c9b";
    String tenantId="0ab0fa30-cbdc-456f-89c3-dc5188620edb";


    @Test
    public void test(){
        ThreadLocalStore.setUserInfoLocal("8d987e9d-1736-4aa3-bbfd-2a53e60a38c8");
        DashTemplateInput dashTemplateInput = new DashTemplateInput();
        dashTemplateInput.setTemplateName("test5");
        dashTemplateInput.setDescription("t");
        dashTemplateInput.setDashId(dashId);
        //dashTemplateInput.setTemplateSharedOwners(sharedOwner);
        String t = JsonUtils.toJsonString(dashTemplateInput);
        dashTemplateService.add(dashTemplateInput);
    }

    @Test
    public void test1(){
        ThreadLocalStore.setUserInfoLocal("8d987e9d-1736-4aa3-bbfd-2a53e60a38c8");
        DashTemplateInput dashTemplateInput = new DashTemplateInput();
        dashTemplateInput.setId("8abb8681-baca-42bc-9634-30e55597e727");
        dashTemplateInput.setTemplateName("test2");
        dashTemplateInput.setDescription("te");
        dashTemplateInput.setDashId(dashId);
        dashTemplateInput.setTemplateSharedOwners(sharedOwner);
        String t = JsonUtils.toJsonString(dashTemplateInput);
        dashTemplateService.edit(dashTemplateInput);
    }

    @Test
    public void test2(){
        IdPara idPara = new IdPara();
        idPara.setId("6a21d665-59a5-4611-8651-7239e99749ce");
        String t = JsonUtils.toJsonString(idPara);
        dashTemplateService.delete(idPara);
    }

    @Test
    public void test3(){
        ThreadLocalStore.setUserInfoLocal("8d987e9d-1736-4aa3-bbfd-2a53e60a38c8");
        PageQuery<NameQuery> pageQuery=new PageQuery<>();
        pageQuery.setPageIndex(1);
        pageQuery.setPageSize(10);
        NameQuery nameQuery=new NameQuery();
        //nameQuery.setName("test");
        //pageQuery.setQueryPara(nameQuery);
        pageQuery.setUserId("8d987e9d-1736-4aa3-bbfd-2a53e60a38c8");
        String t = JsonUtils.toJsonString(pageQuery);
        ResponseObject test = dashTemplateService.search(pageQuery);
        String test1= JsonUtils.toJsonString(test.getData());
    }

    @Test
    public void test3_1(){
        ThreadLocalStore.setUserInfoLocal("8d987e9d-1736-4aa3-bbfd-2a53e60a38c8");
        IdPara idPara = new IdPara();
        idPara.setId("89aad6bc-e243-4e8a-91df-318f554cbeab");
        String t = JsonUtils.toJsonString(idPara);
        ResponseObject test = dashTemplateService.searchByGroupId(idPara);
        String test1= JsonUtils.toJsonString(test.getData());
    }

    @Test
    public void test4(){
        ThreadLocalStore.setUserInfoLocal("8d987e9d-1736-4aa3-bbfd-2a53e60a38c8");
        DashTemplateInput dashTemplateInput = new DashTemplateInput();
        dashTemplateInput.setDashId(dashId);
        dashTemplateInput.setTemplateType(DashtemplateTypeEnum.POINT.getCode());
        dashTemplateInput.setTemplateId("dc1819ba-5c2a-45e0-8b24-e3c6fd31d91d");
        dashTemplateInput.setDashName("testTem");
        dashTemplateInput.setDashSharedOwners(sharedOwner);
        dashTemplateInput.setService("test");
        dashTemplateInput.setDescription("test");
        String t = JsonUtils.toJsonString(dashTemplateInput);
        dashTemplateService.addDashByTemplate(dashTemplateInput);
    }

    @Test
    public void test5(){
        ThreadLocalStore.setTenantInfoLocal(tenantId);
        ThreadLocalStore.setUserInfoLocal("8d987e9d-1736-4aa3-bbfd-2a53e60a38c8");
        DashTemplateInput dashTemplateInput = new DashTemplateInput();
        dashTemplateInput.setDashId("73441c75-93bb-4c3a-acd9-857ad7a593e9");
        dashTemplateInput.setTemplateType(DashtemplateTypeEnum.COPY.getCode());
        dashTemplateInput.setTemplateId("e952f973-27c2-4927-a994-84520ddbda4e");
        dashTemplateInput.setDashName("testTem_new");
        dashTemplateInput.setDashSharedOwners(sharedOwner);
        dashTemplateInput.setService("test");
        dashTemplateInput.setDescription("test");
        String t = JsonUtils.toJsonString(dashTemplateInput);
        dashTemplateService.addDashByTemplate(dashTemplateInput);
    }

    @Test
    public void test5_2(){
        ThreadLocalStore.setTenantInfoLocal(tenantId);
        ThreadLocalStore.setUserInfoLocal("8d987e9d-1736-4aa3-bbfd-2a53e60a38c8");
        DashTemplateInput dashTemplateInput = new DashTemplateInput();
        dashTemplateInput.setDashId(dashId);
        dashTemplateInput.setTemplateType(DashtemplateTypeEnum.POINT.getCode());
        dashTemplateInput.setTemplateId("318195e8-9cab-4300-8679-1f49eace128e");
        dashTemplateInput.setDashName("testTem_point");
        //dashTemplateInput.setDashSharedOwners(sharedOwner);
        dashTemplateInput.setService("test");
        dashTemplateInput.setDescription("test");
        String t = JsonUtils.toJsonString(dashTemplateInput);
        dashTemplateService.addDashByTemplate(dashTemplateInput);
    }

    @Test
    public void test6(){
        ThreadLocalStore.setUserInfoLocal("8d987e9d-1736-4aa3-bbfd-2a53e60a38c8");
        DashTemplateGroupInput dashTemplateGroupInput = new DashTemplateGroupInput();
        //dashTemplateGroupInput.setTemplateId("8abb8681-baca-42bc-9634-30e55597e727");
        dashTemplateGroupInput.setTemplateGroupName("testGroup");
        dashTemplateGroupInput.setTemplateGroupSharedOwners(sharedOwner);
        String t = JsonUtils.toJsonString(dashTemplateGroupInput);
        dashTemplateGroupService.add(dashTemplateGroupInput);
    }

    @Test
    public void test7(){
        ThreadLocalStore.setUserInfoLocal("8d987e9d-1736-4aa3-bbfd-2a53e60a38c8");
        DashTemplateGroupInput dashTemplateGroupInput = new DashTemplateGroupInput();
        dashTemplateGroupInput.setId("89aad6bc-e243-4e8a-91df-318f554cbeab");
        dashTemplateGroupInput.setTemplateGroupName("testGroup23");
        dashTemplateGroupInput.setTemplateGroupSharedOwners(sharedOwner);
        String t = JsonUtils.toJsonString(dashTemplateGroupInput);
        dashTemplateGroupService.edit(dashTemplateGroupInput);
    }

    @Test
    public void test8(){
        ThreadLocalStore.setUserInfoLocal("8d987e9d-1736-4aa3-bbfd-2a53e60a38c8");
        DashTemplateGroupInput dashTemplateGroupInput = new DashTemplateGroupInput();
        dashTemplateGroupInput.setTemplateId("8abb8681-baca-42bc-9634-30e55597e727");
        dashTemplateGroupInput.setId("89aad6bc-e243-4e8a-91df-318f554cbeab");
        //dashTemplateGroupInput.setTemplateGroupSharedOwners(sharedOwner);
        String t = JsonUtils.toJsonString(dashTemplateGroupInput);
        dashTemplateGroupService.addGroupRela(dashTemplateGroupInput);
    }

    @Test
    public void test9(){
        ThreadLocalStore.setUserInfoLocal("8d987e9d-1736-4aa3-bbfd-2a53e60a38c8");
        DashTemplateGroupInput dashTemplateGroupInput = new DashTemplateGroupInput();
        dashTemplateGroupInput.setTemplateId("8abb8681-baca-42bc-9634-30e55597e727");
        dashTemplateGroupInput.setId("89aad6bc-e243-4e8a-91df-318f554cbeab");
        //dashTemplateGroupInput.setTemplateGroupSharedOwners(sharedOwner);
        String t = JsonUtils.toJsonString(dashTemplateGroupInput);
        dashTemplateGroupService.removeGroupRela(dashTemplateGroupInput);
    }

    @Test
    public void test10(){
        ThreadLocalStore.setUserInfoLocal("8d987e9d-1736-4aa3-bbfd-2a53e60a38c8");
        PageQuery<NameQuery> pageQuery=new PageQuery<>();
        pageQuery.setPageIndex(1);
        pageQuery.setPageSize(10);
        NameQuery nameQuery=new NameQuery();
        nameQuery.setName("testGroup2");
        pageQuery.setQueryPara(nameQuery);
        pageQuery.setUserId("8d987e9d-1736-4aa3-bbfd-2a53e60a38c8");
        String t = JsonUtils.toJsonString(pageQuery);
        ResponseObject test = dashTemplateGroupService.search(pageQuery);
        String test1= JsonUtils.toJsonString(test.getData());
    }

    @Test
    public void test11(){
        IdPara idPara = new IdPara();
        idPara.setId("32df3e81-2bfb-4e99-831d-dfc455826830");
        String t = JsonUtils.toJsonString(idPara);
        dashTemplateGroupService.delete(idPara);
    }


    @Test
    public void test12(){
       /* CardDO cardDO = cardHandler.getById("03e83404-4676-472f-a8be-957bac327c2d");
        cardHandler.changeCardSqlCfg(cardDO,"cube","ds_web");*/
        CardDO cardDO2 = cardHandler.getById("03e83404-4676-472f-a8be-957bac327c2d");
        Map cfgMap = JsonUtils.toObject(cardDO2.getConfigs(),Map.class);
    }


    @Test
    public void test13(){
        ThreadLocalStore.setUserInfoLocal("b595e442-0032-435f-9a33-9114d5faed9c");
        DashTemplateInput dashTemplateInput = new DashTemplateInput();
        dashTemplateInput.setServiceId("021675a1-8a87-4701-a512-a8c29aabcb41");
        dashTemplateInput.setDashId("0864ded4-e795-48ef-a55a-d3ec22a7bba7");
        dashTemplateInput.setTemplateId("123");
        List<String> zcpOwnerList = new ArrayList<>();
        zcpOwnerList.add("2588504c-99f5-4739-8c98-af9a19e1f540");
        dashTemplateInput.setZcpOwnerList(zcpOwnerList);
        String t = JsonUtils.toJsonString(dashTemplateInput);
        dashTemplateService.addTemplateByZCP(dashTemplateInput);
    }

    @Test
    public void test14(){
        ThreadLocalStore.setUserInfoLocal("b595e442-0032-435f-9a33-9114d5faed9c");
        DashTemplateInput dashTemplateInput = new DashTemplateInput();
        dashTemplateInput.setServiceId("021675a1-8a87-4701-a512-a8c29aabcb41");
        dashTemplateInput.setDashId("0864ded4-e795-48ef-a55a-d3ec22a7bba7");
        dashTemplateInput.setTemplateId("111222");
        String t = JsonUtils.toJsonString(dashTemplateInput);
        dashTemplateService.updateTemplateByZCP("e7a9b242-0c7c-404d-8a00-464decffd28f");
    }

    @Test
    public void test15(){
        ThreadLocalStore.setUserInfoLocal("b595e442-0032-435f-9a33-9114d5faed9c");
        DashTemplateInput dashTemplateInput = new DashTemplateInput();
        dashTemplateInput.setServiceId("021675a1-8a87-4701-a512-a8c29aabcb41");
        dashTemplateInput.setDashId("0864ded4-e795-48ef-a55a-d3ec22a7bba7");
        dashTemplateInput.setTemplateId("111222");
        String t = JsonUtils.toJsonString(dashTemplateInput);
        dashTemplateService.deleteDashByTemplateZCP(null);
    }


}
