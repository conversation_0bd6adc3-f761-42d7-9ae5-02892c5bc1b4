package us.zoom.cube.site.biz.model;

import com.fasterxml.jackson.core.type.TypeReference;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import us.zoom.cube.lib.utils.JsonUtils;

import java.util.Arrays;

/**
 * @Author: <EMAIL>
 * @ModuleOwner: <EMAIL>
 * @Date: 12/05/2023 20:34
 * @Description:
 */
public class LimiterParamTest {

    public String toJson() {
        LimiterParam param = new LimiterParam();
        param.setGlobalPermitsPerSecond(100);
        LimiterParam.UriLimiter uriLimiter1 = new LimiterParam.UriLimiter(Arrays.asList("uri3", "uri4"), 20);
        LimiterParam.UriLimiter uriLimiter2 = new LimiterParam.UriLimiter(Arrays.asList("uri1", "uri1"), 30);
        param.setUriLimiterList(Arrays.asList(uriLimiter1, uriLimiter2));
        return JsonUtils.toJsonStringIgnoreExp(param);
    }

    @Test
    public void testJsonToObject() {
        LimiterParam param = JsonUtils.toObjectByTypeRef(toJson(), new TypeReference<LimiterParam>() {
        });
        Assertions.assertEquals(param.getGlobalPermitsPerSecond(), 100);
        Assertions.assertEquals(param.getUriLimiterList().size(), 2);
    }
}
