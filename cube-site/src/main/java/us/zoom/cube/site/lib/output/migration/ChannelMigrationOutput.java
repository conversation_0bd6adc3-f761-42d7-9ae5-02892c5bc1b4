package us.zoom.cube.site.lib.output.migration;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
public class ChannelMigrationOutput {

    private List<String> success = new ArrayList<>();

    private List<String> skip = new ArrayList<>();

    private List<FailedChannel> fail = new ArrayList<>();


    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FailedChannel {
        private String name;
        private String reason;
    }
}
