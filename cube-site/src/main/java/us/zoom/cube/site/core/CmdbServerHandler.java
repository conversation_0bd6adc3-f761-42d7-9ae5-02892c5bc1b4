package us.zoom.cube.site.core;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import us.zoom.cube.site.infra.utils.IdUtils;
import us.zoom.cube.site.lib.input.network.Detection;
import us.zoom.cube.site.lib.output.cmdb.CMDBServerInfo;
import us.zoom.infra.dao.model.CmdbServerDO;
import us.zoom.infra.dao.service.CmdbServerDAO;
import us.zoom.cube.lib.utils.JsonUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

@Component
public class CmdbServerHandler {
    @Autowired
    private CmdbServerDAO cmdbServerDAO;

    public void batchAddCmdbServer(List<CMDBServerInfo> cmdbServerInfoBatch) {
        if (CollectionUtils.isEmpty(cmdbServerInfoBatch)){
            return;
        }
        List<CmdbServerDO> cmdbServerDOList = new ArrayList<>();
        for (CMDBServerInfo cmdbServerInfo : cmdbServerInfoBatch){
            CmdbServerDO cmdbServerDO = new CmdbServerDO();
            BeanUtils.copyProperties(cmdbServerInfo,cmdbServerDO);
            cmdbServerDO.setId(IdUtils.generateId());
            cmdbServerDO.setServerId(cmdbServerInfo.getId());
            cmdbServerDO.setZone(JsonUtils.toJsonString(cmdbServerInfo.getZone()));
            cmdbServerDO.setPrivateIpMac(JsonUtils.toJsonString(cmdbServerInfo.getPrivateIpMac()));
            cmdbServerDO.setLabelList(JsonUtils.toJsonString(cmdbServerInfo.getLabel()));
            // to make service list
            List<String> serviceList =  new ArrayList<>();
            if (cmdbServerDO.getLabelList()==null){
                cmdbServerDO.setLabelList("");
            }else {
                for (String lable : cmdbServerInfo.getLabel()){
                    String service = cmdbServerInfo.getGroup()+"_"+cmdbServerInfo.getSubGroup()+"_"+lable;
                    serviceList.add(service);
                }
            }
            cmdbServerDO.setServiceList(JsonUtils.toJsonString(serviceList));
            if (cmdbServerDO.getServiceList()==null){
                cmdbServerDO.setServiceList("");
            }
            cmdbServerDOList.add(cmdbServerDO);
        }
        cmdbServerDAO.batchAdd(cmdbServerDOList);
    }


    public void batchUpdateCmdbServer(List<CMDBServerInfo> cmdbServerInfoBatchUpdate) {
        if (CollectionUtils.isEmpty(cmdbServerInfoBatchUpdate)){
            return;
        }
        List<CmdbServerDO> serverUpdate = new ArrayList<>();
        for (CMDBServerInfo server : cmdbServerInfoBatchUpdate){
            CmdbServerDO cmdbServerDO = new CmdbServerDO();
            BeanUtils.copyProperties(server,cmdbServerDO);
            cmdbServerDO.setServerId(server.getId());
            cmdbServerDO.setZone(JsonUtils.toJsonString(server.getZone()));
            cmdbServerDO.setPrivateIpMac(JsonUtils.toJsonString(server.getPrivateIpMac()));
            cmdbServerDO.setLabelList(JsonUtils.toJsonString(server.getLabel()));
            List<String> serviceList =  new ArrayList<>();
            if (cmdbServerDO.getLabelList()==null){
                cmdbServerDO.setLabelList("");
            }else {
                for (String lable : server.getLabel()){
                    String service = server.getGroup()+"_"+server.getSubGroup()+"_"+lable;
                    serviceList.add(service);
                }
            }
            cmdbServerDO.setServiceList(JsonUtils.toJsonString(serviceList));
            if (cmdbServerDO.getServiceList()==null){
                cmdbServerDO.setServiceList("");
            }
            serverUpdate.add(cmdbServerDO);
        }

        cmdbServerDAO.batchUpdate(serverUpdate);
    }

    public List<CmdbServerDO> listByService(String service) {
        return cmdbServerDAO.ListByService(service);

    }

    public int updateBatch(List<CmdbServerDO> cmdbServerDOToUpdate) {
        if (CollectionUtils.isEmpty(cmdbServerDOToUpdate)){
            return -1;
        }
        return cmdbServerDAO.updateLoadRateBatch(cmdbServerDOToUpdate);
    }

    public List<CmdbServerDO> listByDetection(Detection detection) {
        return cmdbServerDAO.listByDetection(detection.getDc(),detection.getService(),detection.getCluster(),detection.getIp());
    }

    public List<CmdbServerDO> listByIps(Set<String> ips) {
        if (ips.size()==0){
            return new ArrayList<>();
        }
        return cmdbServerDAO.listByIps(ips);
    }

    public List<CmdbServerDO> listAll() {
        return cmdbServerDAO.listAll();
    }

    public List<CmdbServerDO> listByIds(List<String> ids) {
        if (ids.size()==0){
            return new ArrayList<>();
        }
        return cmdbServerDAO.listByIds(ids);
    }

    public void updateUpStatusTimeAndThresholdByHostName(String hostName, Double threshold) {
        cmdbServerDAO.updateUpStatusTimeAndThresholdByHostName(hostName, threshold);
    }

    public List<String> getDcList() {
         return cmdbServerDAO.getDcList();
    }

    public List<String> getServiceList(String dc) {
        return cmdbServerDAO.getServiceList(dc);
    }

    public List<CmdbServerDO> getIpList(String dc, String service) {
        return cmdbServerDAO.getIpList(dc,service);
    }

    public void deleteByServerId(String serverId) {
        cmdbServerDAO.deleteByServerId(serverId);
    }
}
