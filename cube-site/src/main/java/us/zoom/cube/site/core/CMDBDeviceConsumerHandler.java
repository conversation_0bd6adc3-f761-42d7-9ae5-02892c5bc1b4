package us.zoom.cube.site.core;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import us.zoom.cloud.secrets.spring.annotation.SecretValue;
import us.zoom.cube.site.lib.output.cmdb.CMDBDeviceInfo;
import us.zoom.cube.site.lib.output.cmdb.NetDeviceTorTopo;
import us.zoom.infra.utils.HttpUtils;
import us.zoom.cube.lib.utils.JsonUtils;
import us.zoom.mq.client.clients.consumer.RetryableStraw;
import us.zoom.mq.common.entity.TaskEntity;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

@Slf4j
@Component
public class CMDBDeviceConsumerHandler implements RetryableStraw<String> {
    private final static TypeReference typeReference = new TypeReference<String>() {
    };
    private final static String INSERT = "insert";
    private final static String UPDATE = "update";
    private final static String DELETE = "delete";
    private final static int SUCCESS_CODE = 200;
    private final static int MAX_RETRY_TIME = 3;
    @Value("${cmdb.login.url}")
    private String loginUrl;
    @Value("${cmdb.tor.topo.query.url}")
    private String torTopoQueryUrl;
    @Value("${cmdb.ca.file.path}")
    private String caFilePath;
    @SecretValue("cmdb.user.name")
    private String userName;
    @SecretValue("cmdb.password")
    private String password;
    @Autowired
    private CmdbDeviceHandler cmdbDeviceHandler;
    @Autowired
    private CmdbServerNetdeviceTopoHandler cmdbServerNetdeviceTopoHandler;

    @Override
    public boolean onMessage(List<TaskEntity<String>> taskEntities) {
        if (CollectionUtils.isEmpty(taskEntities)){
            return false;
        }
        Map<String, String> headerParams = new HashMap<>();
        try{
            String deviceQueryToken = getBearToken();
            headerParams.put("Authorization", "Bearer " + deviceQueryToken);
        }catch (Exception e){
            log.error("get cmdb bearer token error");
            return false;
        }
        AtomicInteger statusCode = new AtomicInteger(SUCCESS_CODE);

        taskEntities.forEach(taskEntity -> {
            try {
                String message = taskEntity.getPayload();

                JsonObject msgObject = JsonParser.parseString(message).getAsJsonObject();
                String operationType = msgObject.get("OperationType").getAsString();
                if (operationType.equals(INSERT)){
                    CMDBDeviceInfo info = new Gson().fromJson(msgObject.get("FullDocument").getAsJsonObject(),CMDBDeviceInfo.class);
                    List<CMDBDeviceInfo> deviceInfoAdd = new ArrayList<>();
                    deviceInfoAdd.add(info);
                    cmdbDeviceHandler.batchAddCmdbDevice(deviceInfoAdd);
                    if (StringUtils.equals(info.getRole(), "tor")) {
                        Map<String, String> torTopoParam = new HashMap<>();
                        torTopoParam.put("devicename", info.getDeviceName());
                        String torTopoContent = HttpUtils.doGet(Arrays.asList(torTopoQueryUrl), torTopoParam, headerParams, null, null, MAX_RETRY_TIME, statusCode);
                        JsonArray torTopoDataSets = JsonParser.parseString(torTopoContent).getAsJsonObject().get("DataSet").getAsJsonArray();
                        if (torTopoDataSets != null && torTopoDataSets.size() > 0) {
                            NetDeviceTorTopo netDeviceTorTopo = new Gson().fromJson(torTopoDataSets.get(0),NetDeviceTorTopo.class);
                            List<NetDeviceTorTopo> netDeviceTorTopoBatchAdd = new ArrayList<>();
                            netDeviceTorTopoBatchAdd.add(netDeviceTorTopo);
                            cmdbServerNetdeviceTopoHandler.batchAddCmdbServerNetdeviceTopo(netDeviceTorTopoBatchAdd);
                        }
                    }
                }else if (operationType.equals(UPDATE)){
                    CMDBDeviceInfo info = new Gson().fromJson(msgObject.get("FullDocument").getAsJsonObject(),CMDBDeviceInfo.class);
                    List<CMDBDeviceInfo> deviceInfoUpdate = new ArrayList<>();
                    deviceInfoUpdate.add(info);
                    cmdbDeviceHandler.batchUpdateCmdbDevice(deviceInfoUpdate);
                    if (StringUtils.equals(info.getRole(), "tor")) {
                        Map<String, String> torTopoParam = new HashMap<>();
                        torTopoParam.put("devicename", info.getDeviceName());
                        String torTopoContent = HttpUtils.doGet(Arrays.asList(torTopoQueryUrl), torTopoParam, headerParams, null, null, MAX_RETRY_TIME, statusCode);
                        JsonArray torTopoDataSets = JsonParser.parseString(torTopoContent).getAsJsonObject().get("DataSet").getAsJsonArray();
                        if (torTopoDataSets != null && torTopoDataSets.size() > 0) {
                            NetDeviceTorTopo netDeviceTorTopo = new Gson().fromJson(torTopoDataSets.get(0),NetDeviceTorTopo.class);
                            List<NetDeviceTorTopo> netDeviceTorTopoBatchUpdate = new ArrayList<>();
                            netDeviceTorTopoBatchUpdate.add(netDeviceTorTopo);
                            cmdbServerNetdeviceTopoHandler.batchUpdateCmdbServerNetdeviceTopo(netDeviceTorTopoBatchUpdate);
                        }
                    }
                    log.info("update success");
                }else if(operationType.equals(DELETE)){
                    CMDBDeviceInfo info = new Gson().fromJson(msgObject.get("FullDocument").getAsJsonObject(),CMDBDeviceInfo.class);
                    String deviceId = info.getId();
                    cmdbDeviceHandler.deleteByDeviceId(deviceId);
                    cmdbServerNetdeviceTopoHandler.deleteByDeviceId(deviceId);
                }
            }catch (Exception e){
                log.info("consumer cmdb server update message error");
            }
        });
        return false;
    }

    private String getBearToken() {
        List<String> urls = new ArrayList<>();
        urls.add(loginUrl);
        Map<String, String> headerParams = new HashMap<>(1);
        headerParams.put("Content-Type", "application/json;charset=UTF-8");
        Map<String, String> param = new HashMap<>();
        param.put("name", userName);
        param.put("password", password);
        String queryJson = JsonUtils.toJsonString(param);
        String content = null;
        AtomicInteger statusCode = new AtomicInteger(SUCCESS_CODE);
        try {
            content = HttpUtils.post(caFilePath, urls, param, headerParams, queryJson, statusCode, null, MAX_RETRY_TIME);
            String accessToken = JsonParser.parseString(content).getAsJsonObject().get("access_token").getAsString();
            return accessToken;
        } catch (Exception e) {
            log.error("log in cmdb error", e.getMessage());
        }
        return null;
    }

    @Override
    public TypeReference<String> type() {
        return typeReference;
    }
}
