package us.zoom.cube.site.infra.enums.trace;

import org.apache.commons.lang3.StringUtils;

/**
 * @author: eason.jia
 * @date: 2024/8/18
 */
public enum HavingOperator {
    EQUAL("="),
    NOT_EQUAL("!="),
    GREATER_THAN(">"),
    GREATER_THAN_OR_EQ(">="),
    LESS_THAN("<"),
    LESS_THAN_OR_EQ("<="),
    IN("IN"),
    NOT_IN("NOT_IN");

    private final String operator;

    HavingOperator(String operator) {
        this.operator = operator;
    }

    public String getOperator() {
        return operator;
    }

    public static HavingOperator from(String havingOperator) {
        for (HavingOperator operator : HavingOperator.values()) {
            if (StringUtils.equalsIgnoreCase(operator.getOperator(), havingOperator)) {
                return operator;
            }
        }
        throw new IllegalArgumentException("illegal operator: " + havingOperator);
    }

    public static boolean validate(String havingOperator) {
        for (HavingOperator operator : HavingOperator.values()) {
            if (StringUtils.equalsIgnoreCase(operator.getOperator(), havingOperator)) {
                return true;
            }
        }
        return false;
    }
}
