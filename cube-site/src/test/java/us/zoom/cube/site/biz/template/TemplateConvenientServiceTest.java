package us.zoom.cube.site.biz.template;


import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ActiveProfiles;
import us.zoom.cube.site.biz.BaseTest;
import us.zoom.cube.site.lib.ResponseObject;

@ActiveProfiles("perf_for_local")
public class TemplateConvenientServiceTest extends BaseTest {

    @Autowired
    private TemplateConvenientService templateConvenientService;

    @Test
    public void testBindOldInfra2Tpl() throws Exception{
        ResponseObject responseObject = templateConvenientService.bindOldInfra2Tpl("9ce3fae8-e05d-4305-b51d-2b9d9b5091f3", "4cd241e7-be6b-41f6-8336-575faf9d4ba8", true);
        Assertions.assertEquals("success", responseObject.getMessage());
    }
}