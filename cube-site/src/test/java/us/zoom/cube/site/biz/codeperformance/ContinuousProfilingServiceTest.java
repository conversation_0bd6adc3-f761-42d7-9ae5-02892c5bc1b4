package us.zoom.cube.site.biz.codeperformance;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import us.zoom.cube.lib.utils.JsonUtils;
import us.zoom.cube.site.lib.profiling.SelfTotalTimeDto;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @author: <PERSON>ls Ding
 * @date: 2024/3/6 15:00
 * @desc:
 */

class ContinuousProfilingServiceTest {

    private ContinuousProfilingService cpService;

    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @BeforeEach
    public void init() {
        cpService = new ContinuousProfilingService();
    }


    /**
     * These are the test cases for the method calculateAutoInterval
     * @throws ParseException
     */

    @Test
    public void testCalculateAutoIntervalFor1Min() throws ParseException {
        long startTime = sdf.parse("2024-03-06 12:00:01").getTime();
        long endTime = sdf.parse("2024-03-06 12:01:01").getTime();

        ContinuousProfilingService.AutoIntervalResult autoIntervalResult = cpService.calculateAutoInterval(startTime, endTime);
        Assertions.assertEquals("1 SECOND", autoIntervalResult.getCondition());
        Assertions.assertEquals(1, autoIntervalResult.getIntervalSeconds());
    }

    @Test
    public void testCalculateAutoIntervalFor30Mins() throws ParseException {
        long startTime = sdf.parse("2024-03-06 01:00:01").getTime();
        long endTime = sdf.parse("2024-03-06 01:30:01").getTime();

        ContinuousProfilingService.AutoIntervalResult autoIntervalResult = cpService.calculateAutoInterval(startTime, endTime);
        Assertions.assertEquals("10 SECOND", autoIntervalResult.getCondition());
        Assertions.assertEquals(10, autoIntervalResult.getIntervalSeconds());
    }

    @Test
    public void testCalculateAutoIntervalFor45Mins() throws ParseException {
        long startTime = sdf.parse("2024-03-06 01:00:01").getTime();
        long endTime = sdf.parse("2024-03-06 01:45:01").getTime();

        ContinuousProfilingService.AutoIntervalResult autoIntervalResult = cpService.calculateAutoInterval(startTime, endTime);
        Assertions.assertEquals("15 SECOND", autoIntervalResult.getCondition());
        Assertions.assertEquals(15, autoIntervalResult.getIntervalSeconds());
    }

    @Test
    public void testCalculateAutoIntervalFor1Hour() throws ParseException {
        long startTime = sdf.parse("2024-03-06 01:00:01").getTime();
        long endTime = sdf.parse("2024-03-06 02:00:01").getTime();

        ContinuousProfilingService.AutoIntervalResult autoIntervalResult = cpService.calculateAutoInterval(startTime, endTime);
        Assertions.assertEquals("20 SECOND", autoIntervalResult.getCondition());
        Assertions.assertEquals(20, autoIntervalResult.getIntervalSeconds());
    }

    @Test
    public void testCalculateAutoIntervalFor3Hours() throws ParseException {
        long startTime = sdf.parse("2024-03-06 01:00:01").getTime();
        long endTime = sdf.parse("2024-03-06 04:00:01").getTime();

        ContinuousProfilingService.AutoIntervalResult autoIntervalResult = cpService.calculateAutoInterval(startTime, endTime);
        Assertions.assertEquals("1 MINUTE", autoIntervalResult.getCondition());
        Assertions.assertEquals(60, autoIntervalResult.getIntervalSeconds());
    }

    @Test
    public void testCalculateAutoIntervalFor5Hours() throws ParseException {
        long startTime = sdf.parse("2024-03-06 01:00:01").getTime();
        long endTime = sdf.parse("2024-03-06 06:00:01").getTime();

        ContinuousProfilingService.AutoIntervalResult autoIntervalResult = cpService.calculateAutoInterval(startTime, endTime);
        Assertions.assertEquals("1 MINUTE", autoIntervalResult.getCondition());
        Assertions.assertEquals(60, autoIntervalResult.getIntervalSeconds());
    }

    @Test
    public void testCalculateAutoIntervalFor8Days() throws ParseException {
        long startTime = sdf.parse("2024-03-06 01:00:01").getTime();
        long endTime = sdf.parse("2024-03-14 01:00:01").getTime();

        ContinuousProfilingService.AutoIntervalResult autoIntervalResult = cpService.calculateAutoInterval(startTime, endTime);
        Assertions.assertEquals("1 HOUR", autoIntervalResult.getCondition());
        Assertions.assertEquals(60 * 60, autoIntervalResult.getIntervalSeconds());
    }

    @Test
    public void testCalculateAutoIntervalFor200Days() throws ParseException {
        long startTime = sdf.parse("2024-03-06 01:00:01").getTime();
        long endTime = sdf.parse("2024-09-22 01:00:01").getTime();

        ContinuousProfilingService.AutoIntervalResult autoIntervalResult = cpService.calculateAutoInterval(startTime, endTime);
        Assertions.assertEquals("1 DAY", autoIntervalResult.getCondition());
        Assertions.assertEquals(24 * 60 * 60, autoIntervalResult.getIntervalSeconds());
    }

    /**
     * These are the test cases for the method calculateSelfAndTotalTimeForSymbols
     * Sample:
     * 80  50	10
     * A  	A	A
     * B	B
     * C	E
     * D
     *
     * Result:
     * 	    Total  Self
     * A 	140		10
     * B	130		 0
     * C	 80		 0
     * D	 80		80
     * E 	 50		50
     * @throws ParseException
     */
    @Test
    public void testMutipleStackForSelfAndTotalTime() {
        //mock the stack chains by queried data
        List<Map<String, Object>> queryResult = new ArrayList<>();
        //A->B->C->D
        queryResult.add(Map.of("stack", JsonUtils.toJsonString(List.of("A", "B", "C", "D")), "cpuTime", 80));
        //A->B->E
        queryResult.add(Map.of("stack", JsonUtils.toJsonString(List.of("A", "B", "E")), "cpuTime", 50));
        //A
        queryResult.add(Map.of("stack", JsonUtils.toJsonString(List.of("A")), "cpuTime", 10));

        SelfTotalTimeDto selfTotalTimeDto = Assertions.assertDoesNotThrow(() -> cpService.calculateSelfAndTotalTimeForSymbols(queryResult), "calculateSelfAndTotalTimeForSymbols should not throw exception");

        //A
        Assertions.assertEquals(140, selfTotalTimeDto.getTimeMap().get("A").getTotalTime());
        Assertions.assertEquals(10, selfTotalTimeDto.getTimeMap().get("A").getSelfTime());

        //B
        Assertions.assertEquals(130, selfTotalTimeDto.getTimeMap().get("B").getTotalTime());
        Assertions.assertEquals(0, selfTotalTimeDto.getTimeMap().get("B").getSelfTime());
        //C
        Assertions.assertEquals(80, selfTotalTimeDto.getTimeMap().get("C").getTotalTime());
        Assertions.assertEquals(0, selfTotalTimeDto.getTimeMap().get("C").getSelfTime());
        //D
        Assertions.assertEquals(80, selfTotalTimeDto.getTimeMap().get("D").getTotalTime());
        Assertions.assertEquals(80, selfTotalTimeDto.getTimeMap().get("D").getSelfTime());
        //E
        Assertions.assertEquals(50, selfTotalTimeDto.getTimeMap().get("E").getTotalTime());
        Assertions.assertEquals(50, selfTotalTimeDto.getTimeMap().get("E").getSelfTime());
    }

    /**
     * * Test case for duplicated symbols in a single stack
     * Sample:
     * 80
     * A
     * B
     * A
     *
     * Result:
     * 	    Total	Self
     * A 	80		80
     * B	80		 0
     */
    @Test
    public void testDuplicatedSymbolsInSingleStack() {
        //mock the stack chains by queried data
        List<Map<String, Object>> queryResult = new ArrayList<>();
        //A->B->A
        queryResult.add(Map.of("stack", JsonUtils.toJsonString(List.of("A", "B", "A")), "cpuTime", 80));

        SelfTotalTimeDto selfTotalTimeDto = Assertions.assertDoesNotThrow(() -> cpService.calculateSelfAndTotalTimeForSymbols(queryResult), "calculateSelfAndTotalTimeForSymbols should not throw exception");

        //A
        Assertions.assertEquals(80, selfTotalTimeDto.getTimeMap().get("A").getTotalTime());
        Assertions.assertEquals(80, selfTotalTimeDto.getTimeMap().get("A").getSelfTime());
        //B
        Assertions.assertEquals(80, selfTotalTimeDto.getTimeMap().get("B").getTotalTime());
        Assertions.assertEquals(0, selfTotalTimeDto.getTimeMap().get("B").getSelfTime());
    }

    /**
     * * Another test case for duplicated symbols in a single stack
     * Sample:
     * 80	50
     * A 	B
     * B
     * A
     *
     * Result:
     * 	    Total	Self
     * A 	 80		80
     * B	130		50
     */
    @Test
    public void testDuplicatedSymbolsInSingleStackCase2() {
        //mock the stack chains by queried data
        List<Map<String, Object>> queryResult = new ArrayList<>();
        //A->B->A
        queryResult.add(Map.of("stack", JsonUtils.toJsonString(List.of("A", "B", "A")), "cpuTime", 80));
        //B
        queryResult.add(Map.of("stack", JsonUtils.toJsonString(List.of("B")), "cpuTime", 50));

        SelfTotalTimeDto selfTotalTimeDto = Assertions.assertDoesNotThrow(() -> cpService.calculateSelfAndTotalTimeForSymbols(queryResult), "calculateSelfAndTotalTimeForSymbols should not throw exception");
        //A
        Assertions.assertEquals(80, selfTotalTimeDto.getTimeMap().get("A").getTotalTime());
        Assertions.assertEquals(80, selfTotalTimeDto.getTimeMap().get("A").getSelfTime());
        //B
        Assertions.assertEquals(130, selfTotalTimeDto.getTimeMap().get("B").getTotalTime());
        Assertions.assertEquals(50, selfTotalTimeDto.getTimeMap().get("B").getSelfTime());
    }

    /**
     * * Test case for circular dependency stack
     * Sample:
     * 80	50
     * A 	B
     * B	A
     *
     * Result:
     * 	    Total	Self
     * A 	130		50
     * B 	130		80
     */
    @Test
    public void testCircularDependencyCaseStack() {
        //mock the stack chains by queried data
        List<Map<String, Object>> queryResult = new ArrayList<>();
        //A->B
        queryResult.add(Map.of("stack", JsonUtils.toJsonString(List.of("A", "B")), "cpuTime", 80));
        //B->A
        queryResult.add(Map.of("stack", JsonUtils.toJsonString(List.of("B", "A")), "cpuTime", 50));

        SelfTotalTimeDto selfTotalTimeDto = Assertions.assertDoesNotThrow(() -> cpService.calculateSelfAndTotalTimeForSymbols(queryResult), "calculateSelfAndTotalTimeForSymbols should not throw exception");

        //A
        Assertions.assertEquals(130, selfTotalTimeDto.getTimeMap().get("A").getTotalTime());
        Assertions.assertEquals(50, selfTotalTimeDto.getTimeMap().get("A").getSelfTime());

        //B
        Assertions.assertEquals(130, selfTotalTimeDto.getTimeMap().get("B").getTotalTime());
        Assertions.assertEquals(80, selfTotalTimeDto.getTimeMap().get("B").getSelfTime());
    }


}