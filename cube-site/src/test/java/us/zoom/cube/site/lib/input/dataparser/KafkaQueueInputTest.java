package us.zoom.cube.site.lib.input.dataparser;


import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import us.zoom.cube.lib.common.AuthEncrTypeEnum;
import us.zoom.cube.lib.common.DataParserPurposeEnum;
import us.zoom.cube.lib.common.KafkaZipTypeEnum;
import us.zoom.cube.lib.common.QueuePurposeTypeEnum;
import us.zoom.cube.site.infra.utils.IdUtils;

/**
 * @Author: luis.zheng
 * @Date: 2021/8/20 2:13
 */
public class KafkaQueueInputTest {
    KafkaQueueInput taskOutputKqi = null;
    KafkaQueueInput kqi = null;

    @BeforeEach
    public void before() {
        taskOutputKqi = new KafkaQueueInput();
        taskOutputKqi.setDataParserId(IdUtils.generateId());
        taskOutputKqi.setKafkaClusterId(IdUtils.generateId());
        taskOutputKqi.setType(QueuePurposeTypeEnum.alarm_output.name());
        taskOutputKqi.setTopics("topic");
        taskOutputKqi.setCompressionType(KafkaZipTypeEnum.gzip.name());


        kqi = new KafkaQueueInput();
        kqi.setDataParserId(IdUtils.generateId());
        kqi.setKafkaClusterId(IdUtils.generateId());
        kqi.setType(QueuePurposeTypeEnum.alarm_output.name());
        kqi.setTopics("topic");
        kqi.setCompressionType(KafkaZipTypeEnum.gzip.name());
        kqi.setUnitTagId(IdUtils.generateId());
        kqi.setGroupId(IdUtils.generateId());
        kqi.setProtocol(AuthEncrTypeEnum.saslSsl.name());
        kqi.setPollSize(200);
        kqi.setThreadCount(1);
        kqi.setSessionTimeout(200000);
    }

    @Test
    public void testDataParserIdIsBlank() {
        taskOutputKqi.setDataParserId(null);
        taskOutputKqi.setType(QueuePurposeTypeEnum.input.name());
        IllegalArgumentException err = Assertions.assertThrows(IllegalArgumentException.class, () -> {
            taskOutputKqi.baseCheck();
        });
        Assertions.assertEquals(err.getMessage(), "dataParserId is blank");
    }

    @Test
    public void testKafkaClusterIdIsBlank() {
        taskOutputKqi.setKafkaClusterId(null);
        taskOutputKqi.setType(QueuePurposeTypeEnum.forward_output.name());
        IllegalArgumentException err = Assertions.assertThrows(IllegalArgumentException.class, () -> {
            taskOutputKqi.baseCheck();
        });
        Assertions.assertEquals(err.getMessage(), "kafkaClusterId is blank");
    }

    @Test
    public void testInputCheckTypeNotExist() {
        taskOutputKqi.setType("ddsdsd");
        IllegalArgumentException err = Assertions.assertThrows(IllegalArgumentException.class, () -> {
            taskOutputKqi.inputCheck();
        });
        Assertions.assertEquals(err.getMessage(), "type: ddsdsd not exist");
    }

    @Test
    public void testInputCheckUnitTagId() {
        kqi.setType(QueuePurposeTypeEnum.input.name());
        kqi.setUnitTagId(" ");
        IllegalArgumentException err = Assertions.assertThrows(IllegalArgumentException.class, () -> {
            kqi.inputCheck();
        });
        Assertions.assertEquals(err.getMessage(), "unitTagId is blank");

        kqi.setUnitTagId(null);
        err = Assertions.assertThrows(IllegalArgumentException.class, () -> {
            kqi.inputCheck();
        });
        Assertions.assertEquals(err.getMessage(), "unitTagId is blank");
    }

    @Test
    public void testInputCheckGroupId() {
        kqi.setType(QueuePurposeTypeEnum.input.name());
        kqi.setGroupId(" ");
        IllegalArgumentException err = Assertions.assertThrows(IllegalArgumentException.class, () -> {
            kqi.inputCheck();
        });
        Assertions.assertEquals(err.getMessage(), "groupId is blank");

        kqi.setGroupId(null);
        err = Assertions.assertThrows(IllegalArgumentException.class, () -> {
            kqi.inputCheck();
        });
        Assertions.assertEquals(err.getMessage(), "groupId is blank");

        kqi.setGroupId("0123456789" + "0123456789" + "0123456789" + "0123456789" + "0123456789" + "0123456789" + "0123456789" + "0123456789" + "0123456789" + "0123456789");
        err = Assertions.assertThrows(IllegalArgumentException.class, () -> {
            kqi.inputCheck();
        });
        Assertions.assertEquals(err.getMessage(), "groupId length is more than 100");

        kqi.setGroupId("0123456789");
        kqi.inputCheck();
    }

    @Test
    public void testSize() {
        kqi.setType(QueuePurposeTypeEnum.input.name());
        kqi.setPollSize(-1);
        IllegalArgumentException err = Assertions.assertThrows(IllegalArgumentException.class, () -> {
            kqi.inputCheck();
        });
        Assertions.assertEquals(err.getMessage(), "pollSize is less than 0");

        kqi.setPollSize(10001);
        err = Assertions.assertThrows(IllegalArgumentException.class, () -> {
            kqi.inputCheck();
        });
        Assertions.assertEquals(err.getMessage(), "pollSize is more than " + 10000);

        kqi.setPollSize(1000);
        kqi.inputCheck();

        kqi.setThreadCount(0);
        err = Assertions.assertThrows(IllegalArgumentException.class, () -> {
            kqi.inputCheck();
        });
        Assertions.assertEquals(err.getMessage(), "threadCount is less than 0");

        kqi.setThreadCount(101);
        err = Assertions.assertThrows(IllegalArgumentException.class, () -> {
            kqi.inputCheck();
        });
        Assertions.assertEquals(err.getMessage(), "threadCount is more than " + 100);

        kqi.setThreadCount(99);
        kqi.inputCheck();

        kqi.setSessionTimeout(0);
        err = Assertions.assertThrows(IllegalArgumentException.class, () -> {
            kqi.inputCheck();
        });
        Assertions.assertEquals(err.getMessage(), "sessionTimeOut is less than 0");
        kqi.setSessionTimeout(222);

        kqi.setProtocol("0");
        kqi.inputCheck();
        Assertions.assertEquals(kqi.getProtocol(), AuthEncrTypeEnum.ssl.name());
    }

    @Test
    public void testOutputCheckTypeNotExist() {
        taskOutputKqi.setType("QueuePurposeTypeEnum.calc_output.name()");
        IllegalArgumentException err = Assertions.assertThrows(IllegalArgumentException.class, () -> {
            taskOutputKqi.outputCheck(DataParserPurposeEnum.handle);
        });
        Assertions.assertEquals(err.getMessage(), "type: QueuePurposeTypeEnum.calc_output.name() not exist");

        taskOutputKqi.setType(QueuePurposeTypeEnum.input.name());
        err = Assertions.assertThrows(IllegalArgumentException.class, () -> {
            taskOutputKqi.outputCheck(DataParserPurposeEnum.handle);
        });
        Assertions.assertEquals(err.getMessage(), "output queue type cann't input");
    }

    @Test
    public void testOutputCheckType() {
        taskOutputKqi.setType(QueuePurposeTypeEnum.calc_output.name());
        IllegalArgumentException err = Assertions.assertThrows(IllegalArgumentException.class, () -> {
            taskOutputKqi.outputCheck(DataParserPurposeEnum.forward);
        });
        Assertions.assertEquals(err.getMessage(), "Purpose is forwarding, Kafka queue type must be forward_output type");

        taskOutputKqi.setType(QueuePurposeTypeEnum.alarm_output.name());
        err = Assertions.assertThrows(IllegalArgumentException.class, () -> {
            taskOutputKqi.outputCheck(DataParserPurposeEnum.forward);
        });
        Assertions.assertEquals(err.getMessage(), "Purpose is forwarding, Kafka queue type must be forward_output type");


        taskOutputKqi.setType(QueuePurposeTypeEnum.forward_output.name());
        err = Assertions.assertThrows(IllegalArgumentException.class, () -> {
            taskOutputKqi.outputCheck(DataParserPurposeEnum.handle);
        });
        Assertions.assertEquals(err.getMessage(), "Purpose is processing, Kafka queue type catn't be forward_output type");
    }

    @Test
    public void testCompressionTypeNotExist() {
        taskOutputKqi.setType(QueuePurposeTypeEnum.calc_output.name());
        taskOutputKqi.setCompressionType("ddsdsd");
        IllegalArgumentException err = Assertions.assertThrows(IllegalArgumentException.class, () -> {
            taskOutputKqi.outputCheck(DataParserPurposeEnum.handle);
        });
        Assertions.assertEquals(err.getMessage(), "compressionType: ddsdsd not exist");
    }
}
