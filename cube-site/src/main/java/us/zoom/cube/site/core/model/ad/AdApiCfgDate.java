package us.zoom.cube.site.core.model.ad;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-08-07 10:59
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AdApiCfgDate {
    private String id;
    private String adName;
    private String fieldName;
    private Boolean adStatus;
    private String anomalyDirectionType;
    private String thresValueFilterUp;
    private String thresValueFilterDown;
    private String rollingAlertSensitivityAll;
    private String rollingAlertSensitivityNum;
    private String detectionInterval;
    private List<AdTagCfgData> adTagCfgDateList;
}