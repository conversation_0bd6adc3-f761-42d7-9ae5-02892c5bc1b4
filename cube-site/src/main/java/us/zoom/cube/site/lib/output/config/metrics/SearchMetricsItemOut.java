package us.zoom.cube.site.lib.output.config.metrics;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import us.zoom.cube.site.lib.output.tag.TagInfoOut;
import us.zoom.infra.dao.model.TagNameDo;

import java.util.Date;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @create 2020/7/19 10:03 AM
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@AllArgsConstructor
@NoArgsConstructor
public class SearchMetricsItemOut {

    String id;

    String metricsName;

    String tenantId;

    String tagNames;

    String dataParserName;

    Integer type;

    Date modifyTime;

    FieldsCount fieldsCount = new FieldsCount();

    String creator;

    String editor;

    Boolean enabled;

    Date createTime;

    String documentLink;

    List<TagInfoOut> labelInfoList;

    @Data
    @FieldDefaults(level = AccessLevel.PRIVATE)
    @AllArgsConstructor
    @NoArgsConstructor
    public static class FieldsCount {
        AtomicInteger common = new AtomicInteger(0);
        AtomicInteger histogram = new AtomicInteger(0);
        AtomicInteger percentile = new AtomicInteger(0);
        AtomicInteger customize = new AtomicInteger(0);

        public void addCommon(Integer add) {
            common.getAndAdd(add);
        }

        public void addHistogram(Integer add) {
            histogram.getAndAdd(add);
        }

        public void addPercentile(Integer add) {
            percentile.getAndAdd(add);
        }

        public void addCustomize(Integer add) {
            customize.getAndAdd(add);
        }
    }
}
