package us.zoom.cube.site.core.model.resource;

import java.util.List;

public class FlinkJarSummary {

    private String address;
    private List<FlinkJarFile> files;

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public List<FlinkJarFile> getFiles() {
        return files;
    }

    public void setFiles(List<FlinkJarFile> files) {
        this.files = files;
    }
}
