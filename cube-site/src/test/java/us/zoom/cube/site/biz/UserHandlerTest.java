package us.zoom.cube.site.biz;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import us.zoom.cube.site.CubeSiteApplication;
import us.zoom.cube.site.core.UserHandler;

@SpringBootTest(classes = CubeSiteApplication.class,webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)//springboot
public class UserHandlerTest extends  BaseTest {

    @Autowired
    UserHandler userHandler;

    @Test
    public void newUserAndAssginDefaultService(){
        userHandler.newUserAndAssginDefaultService("<EMAIL>");

    }
}
