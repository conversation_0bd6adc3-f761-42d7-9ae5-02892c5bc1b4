package us.zoom.cube.site.core;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import us.zoom.cube.site.api.intercept.AuthInterceptor;
import us.zoom.cube.site.biz.ProbeAuthService;
import us.zoom.cube.site.biz.TenantService;
import us.zoom.cube.site.infra.utils.IdUtils;
import us.zoom.cube.site.lib.dto.probe.ProbeTaskDTO;
import us.zoom.cube.site.lib.dto.probe.ProbeTaskSummaryDTO;
import us.zoom.cube.site.lib.input.ProbeTaskBatchInput;
import us.zoom.cube.site.lib.input.ProbeTaskInput;
import us.zoom.cube.site.lib.probe.ProbeTaskConverter;
import us.zoom.cube.site.lib.query.ProbeTaskQuery;
import us.zoom.infra.dao.model.ProbeTaskDO;
import us.zoom.infra.dao.model.ProbeTaskMappingDO;
import us.zoom.infra.dao.model.ProbeTaskSummaryDO;
import us.zoom.infra.dao.model.TenantDO;
import us.zoom.infra.dao.service.ProbeTaskDAO;
import us.zoom.infra.dao.service.ProbeTaskMappingDAO;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @authoer: eason.jia
 * @createDate: 2022/6/29
 * @description:
 */
@Slf4j
@Component
public class ProbeTaskHandler {

    @Autowired
    private ProbeTaskDAO probeTaskDAO;
    @Autowired
    private ProbeTaskMappingDAO probeTaskMappingDAO;
    @Autowired
    private ProbeTaskConverter probeTaskConverter;
    @Autowired
    private ProbeAuthService probeAuthService;
    @Autowired
    private TenantService tenantService;

    public ProbeTaskSummaryDTO getSummaryInfo() {
        ProbeTaskSummaryDTO probeTaskSummaryDTO = new ProbeTaskSummaryDTO();
        probeTaskSummaryDTO.setServiceTaskSummary(new HashMap<>());
        List<ProbeTaskSummaryDO> probeTaskSummaryDOList = probeTaskDAO.getTaskSummary();
        probeTaskSummaryDTO.setServiceSummary(String.valueOf(probeTaskSummaryDOList.size()));
        int taskEnableNum = 0;
        int taskNum = 0;
        Map<String, String> serviceId2Name = probeAuthService.getServiceNameMap(probeTaskSummaryDOList.stream().map(ProbeTaskSummaryDO::getServiceId).collect(Collectors.toList()));
        for (ProbeTaskSummaryDO probeTaskSummaryDO : probeTaskSummaryDOList) {
            taskEnableNum += probeTaskSummaryDO.getEnableNum();
            taskNum += probeTaskSummaryDO.getEnableNum() + probeTaskSummaryDO.getDisableNum();
            String serviceName = serviceId2Name.get(probeTaskSummaryDO.getServiceId());
            if (StringUtils.isBlank(serviceName)) {
                serviceName = "unknown service";
            }
            probeTaskSummaryDTO.getServiceTaskSummary().put(serviceName,
                    String.format("%s/%s", probeTaskSummaryDO.getEnableNum(), probeTaskSummaryDO.getEnableNum() + probeTaskSummaryDO.getDisableNum()));

        }
        probeTaskSummaryDTO.setTaskSummary(taskEnableNum + "/" + taskNum);
        return probeTaskSummaryDTO;
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public String addProbeTask(ProbeTaskInput probeTaskInput) {
        if (StringUtils.isBlank(probeTaskInput.getId())) {
            probeTaskInput.setId(IdUtils.generateId());
        }
        ProbeTaskDO probeTaskDO = probeTaskConverter.toProbeTaskDO(probeTaskInput);
        probeTaskDO.setCreatedUser(AuthInterceptor.getUserName());
        probeTaskDO.setLastModifiedUser(AuthInterceptor.getUserName());
        probeTaskDAO.add(probeTaskDO);
        probeTaskMappingDAO.batchAdd(probeTaskConverter.toProbeTaskMappingDOList(probeTaskInput));
        return probeTaskInput.getId();
    }
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public String addProbeBatchTask(ProbeTaskBatchInput probeTaskInput) {
        if (StringUtils.isBlank(probeTaskInput.getId())) {
            probeTaskInput.setId(IdUtils.generateId());
        }
        ProbeTaskDO probeTaskDO = probeTaskConverter.toProbeTaskDO(probeTaskInput);
        probeTaskDAO.add(probeTaskDO);
        probeTaskMappingDAO.batchAdd(probeTaskConverter.toProbeTaskMappingDOList(probeTaskInput));
        return probeTaskInput.getId();
    }
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void deleteProbeTaskById(String id) {
        probeTaskDAO.deleteById(id);
        probeTaskMappingDAO.deleteByProbeTaskId(id);
    }

    public String getProbeTaskExtend(String id) {
        ProbeTaskDO probeTaskDO = probeTaskDAO.getById(id);
        if (probeTaskDO != null) {
            return probeTaskDO.getExtend();
        }
        return null;
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void updateProbeTaskExtend(String id, String value) {
        ProbeTaskDO probeTaskDO = probeTaskDAO.getById(id);
        if (probeTaskDO != null) {
            probeTaskDAO.modifyExtend(id, value);
        }
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void modifyProbeTask(ProbeTaskInput probeTaskInput) {
        if (StringUtils.isBlank(probeTaskInput.getId())) {
            throw new IllegalArgumentException("probe task id can not be empty");
        }
        ProbeTaskDO probeTaskDO = probeTaskConverter.toProbeTaskDO(probeTaskInput);
        if (StringUtils.isBlank(probeTaskDO.getLastModifiedUser())) {
            probeTaskDO.setLastModifiedUser(AuthInterceptor.getUserName());
        }
        List<ProbeTaskMappingDO> mappingDOList = probeTaskConverter.toProbeTaskMappingDOList(probeTaskInput);
        probeTaskDAO.modify(probeTaskDO);
        probeTaskMappingDAO.deleteByProbeTaskId(probeTaskInput.getId());
        probeTaskMappingDAO.batchAdd(mappingDOList);
    }
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void modifyBatchProbeTask(ProbeTaskBatchInput probeTaskInput) {
        if (StringUtils.isBlank(probeTaskInput.getId())) {
            throw new IllegalArgumentException("probe task id can not be empty");
        }
        ProbeTaskDO probeTaskDO = probeTaskConverter.toProbeTaskDO(probeTaskInput);
        List<ProbeTaskMappingDO> mappingDOList = probeTaskConverter.toProbeTaskMappingDOList(probeTaskInput);
        probeTaskDAO.modify(probeTaskDO);
        probeTaskMappingDAO.deleteByProbeTaskId(probeTaskInput.getId());
        probeTaskMappingDAO.batchAdd(mappingDOList);
    }

    public Map<String, String> getTaskId2TaskNameMap(List<String> ids) {
        Map<String, String> map = new HashMap<>();
        if (ids == null || ids.isEmpty()) {
            return map;
        }
        List<ProbeTaskDO> probeTaskDOList = probeTaskDAO.getBatchByIds(ids);
        probeTaskDOList.forEach(probeTaskDO -> {
            map.put(probeTaskDO.getId(), probeTaskDO.getName());
        });
        return map;
    }


    public ProbeTaskDTO getProbeTaskById(String id) {
        ProbeTaskDO probeTaskDO = probeTaskDAO.getById(id);
        if (probeTaskDO == null) {
            return null;
        }
        List<ProbeTaskMappingDO> mappingDOList = probeTaskMappingDAO.getByProbeTaskId(id);
        return probeTaskConverter.toProbeTaskDTO(probeTaskDO, mappingDOList);
    }

    public List<ProbeTaskDTO> getProbeTasksByServiceId(String serviceId) {
        List<ProbeTaskDO> probeTaskDOList = probeTaskDAO.getAllByServiceId(serviceId);
        if (probeTaskDOList == null) {
            probeTaskDOList = new ArrayList<>();
        }
        List<ProbeTaskDTO> probeTaskDTOList = new ArrayList<>();
        for (ProbeTaskDO probeTaskDO : probeTaskDOList) {
            probeTaskDTOList.add(getProbeTaskById(probeTaskDO.getId()));
        }
        return probeTaskDTOList;
    }

    public List<ProbeTaskDTO> searchByConditions(ProbeTaskQuery probeTaskQuery, int pageIndex, int pageSize) {
        if (StringUtils.isBlank(probeTaskQuery.getServiceId()) && StringUtils.isNotBlank(probeTaskQuery.getServiceName())) {
            TenantDO tenant = tenantService.getTenantByName(probeTaskQuery.getServiceName());
            if (tenant != null) {
                probeTaskQuery.setServiceId(tenant.getId());
            }
        }
        List<ProbeTaskDO> probeTaskDOS = probeTaskDAO.searchByConditions(
                probeTaskQuery.getName(),
                probeAuthService.adaptServiceIds(probeTaskQuery.getServiceId()),
                probeTaskQuery.getTarget(),
                probeTaskQuery.getTags(),
                probeTaskQuery.getStatus(),
                probeTaskQuery.getAggregationStatus(),
                (pageIndex - 1) * pageSize,
                pageSize);
        if (probeTaskDOS == null || probeTaskDOS.isEmpty()) {
            return new ArrayList<>();
        }
        Map<String, List<ProbeTaskMappingDO>> probeTaskMapping = probeTaskMappingDAO.getByProbeTaskIds(
                        probeTaskDOS.stream()
                                .map(ProbeTaskDO::getId)
                                .collect(Collectors.toList()))
                .stream()
                .collect(Collectors.groupingBy(ProbeTaskMappingDO::getProbeTaskId));
        List<ProbeTaskDTO> probeTaskDTOS = new LinkedList<>();
        probeTaskDOS.stream()
                .map(probeTaskDO ->
                        probeTaskConverter.toProbeTaskDTO(probeTaskDO, probeTaskMapping.get(probeTaskDO.getId())))
                .forEach(probeTaskDTOS::add);
        return probeTaskDTOS;
    }

    public int countByConditions(ProbeTaskQuery probeTaskQuery) {
        return probeTaskDAO.countByConditions(
                probeTaskQuery.getName(),
                probeAuthService.adaptServiceIds(probeTaskQuery.getServiceId()),
                probeTaskQuery.getTarget(),
                probeTaskQuery.getTags(),
                probeTaskQuery.getStatus(),
                probeTaskQuery.getAggregationStatus()
        );
    }

    public void modifyProbeTaskStatus(String id, String status, String modifiedUser) {
        probeTaskDAO.modify(ProbeTaskDO.builder()
                .id(id)
                .status(status)
                .lastModifiedUser(modifiedUser)
                .build());
    }
}
