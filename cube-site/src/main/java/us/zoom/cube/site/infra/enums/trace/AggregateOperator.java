package us.zoom.cube.site.infra.enums.trace;

import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * @author: eason.jia
 * @date: 2024/8/18
 */
public enum AggregateOperator {
    NO_OP("noop"),
    COUNT("count"),
    COUNT_DISTINCT("count_distinct"),
    SUM("sum"),
    AVG("avg"),
    MIN("min"),
    MAX("max"),
    P05("p05"),
    P10("p10"),
    P20("p20"),
    P25("p25"),
    P50("p50"),
    P75("p75"),
    P90("p90"),
    P95("p95"),
    P99("p99"),
    RATE("rate"),
    SUM_RATE("sum_rate"),
    AVG_RATE("avg_rate"),
    MIN_RATE("min_rate"),
    MAX_RATE("max_rate"),
    RATE_SUM("rate_sum"),
    RATE_AVG("rate_avg"),
    RATE_MIN("rate_min"),
    RATE_MAX("rate_max"),
    HIST_QUANT50("hist_quantile_50"),
    HIST_QUANT75("hist_quantile_75"),
    HIST_QUANT90("hist_quantile_90"),
    HIST_QUANT95("hist_quantile_95"),
    HIST_QUANT99("hist_quantile_99");

    private final String operator;

    AggregateOperator(String operator) {
        this.operator = operator;
    }

    public String getOperator() {
        return operator;
    }

    public static AggregateOperator from(String aggregateOperator) {
        for (AggregateOperator operator : AggregateOperator.values()) {
            if (StringUtils.equals(operator.getOperator(), aggregateOperator)) {
                return operator;
            }
        }
        throw new IllegalArgumentException("illegal operator: " + aggregateOperator);
    }

    public static boolean validate(String aggregateOperator) {
        for (AggregateOperator operator : AggregateOperator.values()) {
            if (StringUtils.equals(operator.getOperator(), aggregateOperator)) {
                return true;
            }
        }
        return false;
    }

    public static boolean requireAttribute(String aggregateOperator) {
        if (StringUtils.equalsAny(aggregateOperator, NO_OP.getOperator(), COUNT.getOperator(), RATE.getOperator())) {
            return false;
        }
        return true;
    }

    public static Map<AggregateOperator, String> AGGREGATE_OPERATOR_TO_SQL_MAPPING = new HashMap<>();

    static {
        // aggregation
        AGGREGATE_OPERATOR_TO_SQL_MAPPING.put(AVG, "avg");
        AGGREGATE_OPERATOR_TO_SQL_MAPPING.put(MAX, "max");
        AGGREGATE_OPERATOR_TO_SQL_MAPPING.put(MIN, "min");
        AGGREGATE_OPERATOR_TO_SQL_MAPPING.put(SUM, "sum");
        AGGREGATE_OPERATOR_TO_SQL_MAPPING.put(RATE, "count");
        AGGREGATE_OPERATOR_TO_SQL_MAPPING.put(RATE_SUM, "sum");
        AGGREGATE_OPERATOR_TO_SQL_MAPPING.put(RATE_AVG, "avg");
        AGGREGATE_OPERATOR_TO_SQL_MAPPING.put(RATE_MAX, "max");
        AGGREGATE_OPERATOR_TO_SQL_MAPPING.put(RATE_MIN, "min");

        // Percentile
        AGGREGATE_OPERATOR_TO_SQL_MAPPING.put(P05, "0.05");
        AGGREGATE_OPERATOR_TO_SQL_MAPPING.put(P10, "0.10");
        AGGREGATE_OPERATOR_TO_SQL_MAPPING.put(P20, "0.20");
        AGGREGATE_OPERATOR_TO_SQL_MAPPING.put(P25, "0.25");
        AGGREGATE_OPERATOR_TO_SQL_MAPPING.put(P50, "0.50");
        AGGREGATE_OPERATOR_TO_SQL_MAPPING.put(P75, "0.75");
        AGGREGATE_OPERATOR_TO_SQL_MAPPING.put(P90, "0.90");
        AGGREGATE_OPERATOR_TO_SQL_MAPPING.put(P95, "0.95");
        AGGREGATE_OPERATOR_TO_SQL_MAPPING.put(P99, "0.99");
    }
}