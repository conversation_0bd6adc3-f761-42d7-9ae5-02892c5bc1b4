package us.zoom.cube.site.core;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import us.zoom.infra.dao.model.InspectionRuleRelaDO;
import us.zoom.infra.dao.service.InspectionRuleRelaDAO;

import java.util.List;

/**
 * @Description:
 * @author: <PERSON>
 * @date: 2023/04/11 10:38
 */
@Service
public class InspectionRuleRelaHandler {

    @Autowired
    private InspectionRuleRelaDAO inspectionRuleRelaDAO;

    public int addInspectionRuleRela(InspectionRuleRelaDO inspectionRelaRule) {
        return inspectionRuleRelaDAO.addInspectionRuleRela(inspectionRelaRule);
    }

    public List<InspectionRuleRelaDO> selectByInspectionId(String inspectionId) {
        return inspectionRuleRelaDAO.selectByInspectionId(inspectionId);
    }

    public int deleteById(String id) {
        return inspectionRuleRelaDAO.deleteById(id);
    }
}
