package us.zoom.cube.site;


import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import us.zoom.infra.dao.service.AuthDao;

import java.util.Arrays;

@SpringBootTest(classes = CubeSiteApplication.class,webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)//springboot
public class AuthDaoTest {

    @Autowired
    private AuthDao authDao;

    @Test
    public void test(){
        
        authDao.listCollectorIdsByTenantIds(Arrays.asList("1","2"));
    }
}
