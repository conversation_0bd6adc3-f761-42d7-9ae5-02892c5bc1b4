package us.zoom.cube.site.lib.input;

import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import us.zoom.cube.lib.trace.TraceAgentRule;
import us.zoom.cube.site.lib.BasePara;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @authoer: adan.zeng
 * @createDate: 2024/10/15
 * @description:
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TraceAgentConfigInput extends BasePara {

    @NotBlank(groups = {ModifyTraceAgentConfigGroup.class}, message = "config id can not be empty")
    private String id;
    @NotBlank(groups = {AddTraceAgentConfigGroup.class, QueryTraceAgentConfigGroup.class}, message = "service id can not be empty")
    private String serviceId;
    @NotBlank(groups = {AddTraceAgentConfigGroup.class, QueryTraceAgentConfigGroup.class}, message = "region can not be empty")
    private String region;
    @NotBlank(groups = {AddTraceAgentConfigGroup.class, QueryTraceAgentConfigGroup.class}, message = "cluster can not be empty")
    private String cluster;
    @NotBlank(groups = {AddTraceAgentConfigGroup.class, ModifyTraceAgentConfigGroup.class}, message = "sampler can not be empty")
    @Pattern(regexp = "always_on|always_off|traceidratio|parentbased_always_on|parentbased_always_off|parentbased_traceidratio",
            groups = {AddTraceAgentConfigGroup.class, ModifyTraceAgentConfigGroup.class}, message = "sampler must be one of the following: always_on, always_off, traceidratio, parentbased_always_on, parentbased_always_off, parentbased_traceidratio")
    private String sampler;
    @DecimalMin(value = "0.00", message = "sampleRatio should be greater than or equals to 0.00")
    @DecimalMax(value = "1.00", message = "sampleRatio should be less than or equals to 1.00")
    private double sampleRatio;

    private List<TraceAgentRule> rules;

    private Set<String> recordKeys;

    private Boolean zoomPropagatorEnable;

    private Boolean captureErrorSpanEnabled;

    private Boolean recordDroppedSpanEnabled;

    private Boolean captureErrorLogEnabled;

    private Boolean enableMybatisEnhancement;

    private Boolean mybatisEnhancementAsClient;

    private Boolean reportByHttpProtobufEnabled;

    private Integer maxExportBatchSize;

    private Map<String, Set<Integer>> skipErrorLogStackFrameMapping;

    private Set<String> propagableKeys;

    public interface AddTraceAgentConfigGroup {

    }

    public interface ModifyTraceAgentConfigGroup {

    }

    public interface QueryTraceAgentConfigGroup {

    }

}