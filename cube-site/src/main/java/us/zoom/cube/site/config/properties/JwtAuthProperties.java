package us.zoom.cube.site.config.properties;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * @author: <PERSON><PERSON> Ding
 * @date: 2022/5/30 09:31
 * @desc:
 */
@Component
@ConditionalOnProperty(name = "cube.authorization.enableJwt", havingValue = "true")
@ConfigurationProperties(prefix = "cube.authorization")
public class JwtAuthProperties {

    private boolean enableJwt;

    public boolean isEnableJwt() {
        return enableJwt;
    }

    public void setEnableJwt(boolean enableJwt) {
        this.enableJwt = enableJwt;
    }
}
