package us.zoom.cube.site.lib.output.trace;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TraceItem {
    private String traceId;
    private String trackingId;
    private String spanId;
    private Long durationNano;
    private Date startTimeUtc;
    private String kind; // server | consumer
    private Boolean hasError;
    private String name;
    private Integer statusCode;
}