package us.zoom.cube.lib.hub;


import com.google.common.collect.Lists;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import us.zoom.async.mq.openapi.client.api.OpenApi;
import us.zoom.async.mq.openapi.client.client.OpenApiClient;
import us.zoom.async.mq.openapi.model.base.Result;
import us.zoom.async.mq.openapi.model.result.topic.TopicResult;

import java.util.List;

/**
 * @Author: luis.zheng
 * @Date: 2021/10/13 5:30 PM
 */
public class AsyncMQCfgTest {
    @Test
    public void test() {
        AsyncMQCfg asyncMQCfg = new AsyncMQCfg();
        asyncMQCfg.setGroupId("groupId");
        asyncMQCfg.setInputTopic("topic");
        asyncMQCfg.setThreadCount(2);

        AsyncMQCfg asyncMQCfg1 = new AsyncMQCfg();
        asyncMQCfg1.setGroupId("groupId");
        asyncMQCfg1.setInputTopic("topic");
        asyncMQCfg1.setThreadCount(2);
        Assertions.assertTrue(asyncMQCfg1.equals(asyncMQCfg), "equals failed");
    }

    @Test
    public void test1() {
        AsyncMQCfg asyncMQCfg = new AsyncMQCfg();
        asyncMQCfg.setGroupId(null);
        asyncMQCfg.setInputTopic("topic");
        asyncMQCfg.setThreadCount(null);

        AsyncMQCfg asyncMQCfg1 = new AsyncMQCfg();
        asyncMQCfg1.setGroupId(null);
        asyncMQCfg1.setInputTopic("topic");
        asyncMQCfg1.setThreadCount(null);
        Assertions.assertTrue(asyncMQCfg1.equals(asyncMQCfg), "equals failed");
    }
}
