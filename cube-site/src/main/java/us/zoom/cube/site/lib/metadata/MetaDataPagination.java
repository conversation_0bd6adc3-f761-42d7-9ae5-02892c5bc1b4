package us.zoom.cube.site.lib.metadata;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Author: charles.hu
 * Date: 2024/12/10
 * Description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class MetaDataPagination {
    private int count;
    private int totalPage;
    private int pageNumber;
    private int pageSize;
    private List<MetaDataComponentInput> list;
}
