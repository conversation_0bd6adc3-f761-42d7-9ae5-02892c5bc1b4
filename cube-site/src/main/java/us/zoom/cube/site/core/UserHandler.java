package us.zoom.cube.site.core;

import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.mindrot.jbcrypt.BCrypt;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import us.zoom.cube.lib.common.RoleTypeEnum;
import us.zoom.cube.lib.monitor.MonitorWrapper;
import us.zoom.cube.lib.utils.JsonUtils;
import us.zoom.cube.site.biz.syspara.AuthParaService;
import us.zoom.cube.site.core.auth.TenantUserRelaHandler;
import us.zoom.cube.site.core.usergroup.UserGroupHandler;
import us.zoom.cube.site.infra.utils.IdUtils;
import us.zoom.cube.site.lib.input.UserInCache;
import us.zoom.infra.dao.model.TenantDO;
import us.zoom.infra.dao.model.TenantUserRelaDO;
import us.zoom.infra.dao.model.UserDO;
import us.zoom.infra.dao.model.UserGroupUserRelaDO;
import us.zoom.infra.dao.service.UserDAO;
import us.zoom.infra.enums.UserStatusEnum;

import java.security.SecureRandom;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;

@Component
@Slf4j
public class UserHandler {

    @Autowired
    private AuthParaService authParaService;

    @Autowired
    private TenantHandler tenantHandler;

    @Autowired
    private UserDAO userDAO;

    @Autowired
    private TenantUserRelaHandler tenantUserRelaHandler;

    @Autowired
    private UserGroupHandler userGroupHandler;

    private Logger monitorLog = LoggerFactory.getLogger("Monitor");


    /**
     * Map<User Id, UserInCache>
     */
    private AtomicReference<Map<String,UserInCache>> userIdUserMapRef = new AtomicReference<>(new HashMap<>());
    /**
     * Map<User Name, User UserId>
     */
    private AtomicReference<Map<String,String>> userNameIdMapRef = new AtomicReference<>(new HashMap<>());



    @PostConstruct
    public void loadUserCache() {

        MonitorWrapper monitorWrapper = new MonitorWrapper("load_user");
        long begin = System.currentTimeMillis();
        try{

            List<UserDO> userDOS = userDAO.listAll();
            monitorWrapper.addField("queryDbTime",System.currentTimeMillis());
            if(org.apache.commons.collections.CollectionUtils.isEmpty(userDOS)){
                monitorWrapper.addTag("phase","no_user");
                monitorWrapper.addTag("status","fail");
                printMonitorLog(monitorWrapper, begin);
                return;
            }
            Map<String,UserInCache> userIdUserDOMap = new HashMap<>();
            Map<String,String> userNameIdMap = new HashMap<>();
            userDOS.forEach(user->{
                UserInCache userCache = new UserInCache();
                userCache.setId(user.getId());
                userCache.setName(user.getName());
                userIdUserDOMap.put(user.getId(),userCache);
                userNameIdMap.put(user.getName(),user.getId());
            });

            if(MapUtils.isNotEmpty(userIdUserDOMap)){
                userIdUserMapRef.set(userIdUserDOMap);
                userNameIdMapRef.set(userNameIdMap);
            }
            monitorWrapper.addTag("status","success");
        }catch (Exception e){
            log.error("load user error ",e);
            monitorWrapper.addTag("status","fail");
            monitorWrapper.addField("msg",e.getMessage());
            monitorWrapper.addField("exp",JsonUtils.toJsonStringIgnoreExp(e));
        }
        printMonitorLog(monitorWrapper, begin);

    }


    public UserInCache getUserFromCache(String userId){
        return  userIdUserMapRef.get().get(userId);
    }

    public UserInCache getUserFromCacheByName(String userName){
      String userId =   userNameIdMapRef.get().get(userName);
      return  userIdUserMapRef.get().get(userId);
    }


    private void printMonitorLog(MonitorWrapper monitorWrapper, long begin) {
        monitorWrapper.setTs(System.currentTimeMillis());
        monitorWrapper.addField("totalTime",System.currentTimeMillis()-begin);
        monitorLog.info(JsonUtils.toJsonStringIgnoreExp(monitorWrapper));
    }

    public static String getRandomString(int length){
        String str = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        SecureRandom random = new SecureRandom();
        StringBuffer sb = new StringBuffer();
        for(int i = 0; i < length; i++) {
            int number = random.nextInt(62);
            sb.append(str.charAt(number));
        }
        return sb.toString();
    }


    public void newUserAndAssginDefaultService(String name){
        String defaultRole = authParaService.getDefaultRole();
        String defaultService =  authParaService.getDefaultService();
        TenantDO tenantDO = tenantHandler.getTenantByNameInLowerCaseFromCache(defaultService);
        UserDO userDO = null;
        if(StringUtils.isNotBlank(defaultRole) && null != tenantDO){
            userDO = addUser(name,defaultRole);
            TenantUserRelaDO tenantUserRelaDO = new TenantUserRelaDO();
            tenantUserRelaDO.setId(IdUtils.generateId());
            tenantUserRelaDO.setRole(defaultRole);
            tenantUserRelaDO.setTenantId(tenantDO.getId());
            tenantUserRelaDO.setUserId(userDO.getId());
            tenantHandler.addTenantAll(Arrays.asList(tenantUserRelaDO));
        }else{
            userDO = addUser(name, RoleTypeEnum.normal.name());
        }

        if(CollectionUtils.isNotEmpty(authParaService.getDefaultUserGroupList())){
            List<UserGroupUserRelaDO> userGroupUserRelaDOList = new ArrayList<>();
            for(String groupId :  authParaService.getDefaultUserGroupList() ){
                UserGroupUserRelaDO relaDO = new UserGroupUserRelaDO();
                relaDO.setId(IdUtils.generateId());
                relaDO.setGroupId(groupId);
                relaDO.setUserId(userDO.getId());
                userGroupUserRelaDOList.add(relaDO);
            }
            userGroupHandler.addUserToUserGroup(userGroupUserRelaDOList);
        }
    }

    public UserDO getUserByName(String userName) {
        Assert.notNull(userName,"user name is null");
        return userDAO.getUserByName(userName);
    }

    public boolean setTenant(String userId, String tenantId) {
        Assert.notNull(userId,"user id is null");
        Assert.notNull(tenantId,"tenant id is null");
        userDAO.setTenant(userId,tenantId);
        return  true;
    }

    public UserDO getUserById(String userId) {
        Assert.notNull(userId,"user id is null");
        return  userDAO.getUserById(userId);
    }

    public List<UserDO> findByNameLike(String name, int pageIndex, int pageSize) {
        return userDAO.findByNameLike(name,pageSize*(pageIndex-1), pageSize);
    }

    public List<UserDO> searchAlarmMentionUser(String name, int pageIndex, int pageSize) {
        return userDAO.searchAlarmMentionUser(name, pageSize*(pageIndex-1), pageSize);
    }

    public int getCountByNameLike(String name) {
        return userDAO.getCountByNameLike(name);
    }

    public void editUser(UserDO userDO, Boolean resetPass) {
        if(resetPass){
            String passwordNew = BCrypt.hashpw(userDO.getPasswd(), BCrypt.gensalt());
            userDO.setPasswordNew(passwordNew);
        }
        userDAO.editUser(userDO,resetPass);
    }

    public void delUser(String id) {
        userDAO.delUser(id);
    }

    public void updateStatus(List<String> userIds, UserStatusEnum statusEnum) {
        userDAO.batchUpdateStatus(userIds, statusEnum.getStatus());
    }

    public List<UserDO> listAll() {
        return  userDAO.listAll();
    }

    public List<UserDO> findByRole(String role) {
        return  userDAO.findByRole(role);
    }

    public List<UserDO> listByIds(List<String> ids) {
        return  userDAO.listByIds(ids);

    }

    public UserDO addUser(String userName, String role) {
        if (StringUtils.isBlank(userName)) {
            return new UserDO();
        }
        UserDO userByName = userDAO.getUserByName(userName);
        if (userByName != null) {
            return userByName;
        }

        UserDO userDO = new UserDO();
        userDO.setName(userName);
        userDO.setId(IdUtils.generateId());
        userDO.setPasswordNew("");
        userDO.setRole(role);
        userDAO.add(userDO);

        return userDO;
    }

    public List<UserDO> getByIds(Set<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }
        return userDAO.getByIds(ids);
    }

    public String getNameById(String userId) {
        Assert.notNull(userId,"user id is null");
        return userDAO.getNameById(userId);
    }

    public List<UserDO> getByName(List<String> nameList) {
        Assert.isTrue(CollectionUtils.isNotEmpty(nameList), "user id is null");
        return userDAO.listByName(nameList);
    }
}
