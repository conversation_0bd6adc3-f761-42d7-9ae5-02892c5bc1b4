package us.zoom.cube.site.config;

import com.google.common.collect.ImmutableMap;
import lombok.SneakyThrows;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.NoSuchBeanDefinitionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.context.ApplicationContext;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import us.zoom.commons.jwt.service.TokenCreatedRequest;
import us.zoom.commons.jwt.service.helper.JWTs;
import us.zoom.cube.site.api.intercept.SingleJwtAuthInterceptor;
import us.zoom.cube.site.config.properties.JwtAuthProperties;

import java.util.Date;
import java.util.HashMap;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

/**
 * @author: Starls Ding
 * @desc:
 *      Attention：
 *      1. do not use @AutoConfigureMockMvc which will load all the @...AutoConfiguration including SecurityAutoConfiguration
 *          and makes exclude not working no matter using @ImportAutoConfiguration or @EnableAutoConfiguration
 *      2. don't try to use @RunWith(SpringRunner.class) any more because JUnit5 change the engine since SpringBoot v2.6
 */

@SpringBootTest(properties = "cube.authorization.enableJwt=true", webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class JwtAuthConfigIntegrationTest {

    @Autowired
    private TestRestTemplate restTemplate;
    @Autowired
    private ApplicationContext context;

    @Value("${auth.zcp2cube.audience.name:cube}")
    private String zcpJwtAudience;
    @Value("${auth.zcp2cube.issuer.name:zcp-iam}")
    private String zcpJwtIssue;

    @SpyBean
    SingleJwtAuthInterceptor singleJwtAuthInterceptor;

    @SneakyThrows
    @Test
    public void onlyJwtAuthConfigWork() {
        assertNotNull(context.getBean(JwtAuthProperties.class));
        assertNotNull(context.getBean(JwtAuthConfig.class));

        assertThrows(NoSuchBeanDefinitionException.class, () -> context.getBean(SiteConfig.class));
        // No SecurityAutoConfiguration means no DefaultSecurityFilterChain flows
        assertThrows(NoSuchBeanDefinitionException.class, () -> context.getBean(SecurityAutoConfiguration.class));
        assertThrows(NoSuchBeanDefinitionException.class, () -> context.getBean("oktaOAuth2WebSecurityConfigurerAdapter"));

        final String testUri = "/api/toggle/debug";

        //no auth result
        Assertions.assertEquals(HttpStatus.UNAUTHORIZED, this.restTemplate.getForEntity(testUri,String.class).getStatusCode());
        //verify request is handled by zoom jwt interceptor
        verify(singleJwtAuthInterceptor).preHandle(any(),any(),any());

        //header with auth token test
        String token = singleJwtAuthInterceptor.getJwtService()
                .newToken(TokenCreatedRequest.newCompabilityOne(
                            zcpJwtIssue,
                            zcpJwtAudience,
                            JWTs.buildHeader(),
                            new HashMap<String,Object>(){{
                                put(TokenCreatedRequest.EXPIRES_AT, new Date(System.currentTimeMillis() + 3 * 60 * 1000L));
                                put("username", "<EMAIL>");
                            }}
                        ));

        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setBearerAuth(token);

        Assertions.assertEquals(HttpStatus.OK, this.restTemplate.exchange(testUri, HttpMethod.GET,new HttpEntity(httpHeaders),String.class).getStatusCode());
        verify(singleJwtAuthInterceptor,times(2)).preHandle(any(),any(),any());
    }
}
