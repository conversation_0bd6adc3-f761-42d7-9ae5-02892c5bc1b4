package us.zoom.cube.site.core;

import com.google.common.collect.Lists;
import org.apache.ibatis.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import us.zoom.infra.dao.model.*;
import us.zoom.infra.dao.service.MetricsAggregationDAO;
import us.zoom.infra.dao.service.MetricsAggregationRuleDAO;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2020/7/19 10:20 AM
 */
@Service
public class MetricsAggregationRuleHandler {

    @Autowired
    private MetricsAggregationRuleDAO metricsAggregationRuleDAO;

    @Autowired
    private AggregationFunctionItemHandler aggregationFunctionItemHandler;

    public void insertMetricsAggregationRule(MetricsAggregationRuleDO metricsAggregationRuleDO){
        metricsAggregationRuleDAO.insertMetricsAggregationRule(metricsAggregationRuleDO);
    }

    public void batchInsertMetricsAggregationRule(List<MetricsAggregationRuleDO> metricsAggregationRuleDOList){
        if(!CollectionUtils.isEmpty(metricsAggregationRuleDOList)) {
            metricsAggregationRuleDAO.batchInsertMetricsAggregationRule(metricsAggregationRuleDOList);
        }
    }

    public int updateMetricsAggregationRule(MetricsAggregationRuleDO metricsAggregationRuleDO) {
        return metricsAggregationRuleDAO.updateMetricsAggregationRule(metricsAggregationRuleDO);
    }

    public int batchUpdateMetricsAggregationRule(List<MetricsAggregationRuleDO> metricsAggregationRuleDOList){
        if(CollectionUtils.isEmpty(metricsAggregationRuleDOList)){
            return 0;
        }
        return metricsAggregationRuleDAO.batchUpdateMetricsAggregationRule(metricsAggregationRuleDOList);
    }


    public List<MetricsAggregationRuleDO> listRulesByAggIds(List<String> aggIds) {
        if(CollectionUtils.isEmpty(aggIds)){
            return Lists.newArrayList();
        }
        return metricsAggregationRuleDAO.listRulesByAggIds(aggIds);
    }

    public void batchDeleteByIdList(List<String> idList){
        if(!CollectionUtils.isEmpty(idList)) {
            metricsAggregationRuleDAO.batchDeleteByIdList(idList);
        }
    }

    public void deleteByAggId(String aggId) {
        metricsAggregationRuleDAO.deleteByAggId(aggId);
    }

    public void deleteByAggIds(List<String> aggIds) {
        metricsAggregationRuleDAO.deleteByAggIds(aggIds);
    }

    public List<MetricsAggregationRuleDO> listAll() {
        return metricsAggregationRuleDAO.listAll();
    }

    public List<AggregationFunctionItemDO> listFunctionItemsByRuleIds(List<String> ruleIds){
        if(CollectionUtils.isEmpty(ruleIds)){
            return Lists.newArrayList();
        }
        return metricsAggregationRuleDAO.listFunctionItemsByRuleIds(ruleIds);
    }


    public List<AggregationHistogramRangeItemDO> listHistogramRangeItemsByRuleIds(List<String> ruleIds) {
        if(CollectionUtils.isEmpty(ruleIds)){
            return Lists.newArrayList();
        }
        return metricsAggregationRuleDAO.listHistogramRangeItemsByRuleIds(ruleIds);
    }

    public List<AggregationPercentileItemDO> listPercentileItemsByRuleIds(List<String> ruleIds) {
        if(CollectionUtils.isEmpty(ruleIds)){
            return Lists.newArrayList();
        }
        return metricsAggregationRuleDAO.listPercentileItemsByRuleIds(ruleIds);
    }

    public int countFunctionItemsByRuleIds(List<String> ruleIds) {
        if(CollectionUtils.isEmpty(ruleIds)){
            return 0;
        }
        return metricsAggregationRuleDAO.countFunctionItemsByRuleIds(ruleIds);
    }


    public int countHistogramRangeItemsByRuleIds(List<String> ruleIds) {
        if(CollectionUtils.isEmpty(ruleIds)){
            return 0;
        }
        return metricsAggregationRuleDAO.countHistogramRangeItemsByRuleIds(ruleIds);
    }

    public int countPercentileItemsByRuleIds(List<String> ruleIds) {
        if(CollectionUtils.isEmpty(ruleIds)){
            return 0;
        }
        return metricsAggregationRuleDAO.countPercentileItemsByRuleIds(ruleIds);
    }

    @Transactional(rollbackFor = Exception.class)
    public void migrateFunctionRules(List<MetricsAggregationRuleDO> updateRuleDOList, List<AggregationFunctionItemDO> addFunctionItemDOList) {
        batchUpdateMetricsAggregationRule(updateRuleDOList);
        aggregationFunctionItemHandler.batchInsertAggregationFunction(addFunctionItemDOList);
    }


    public List<String> getAggRuleFieldsByMetricsId(String metricsId) {
        return metricsAggregationRuleDAO.getAggRuleFieldsByMetricsId(metricsId);
    }
}
