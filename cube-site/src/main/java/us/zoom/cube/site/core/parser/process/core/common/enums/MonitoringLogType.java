package us.zoom.cube.site.core.parser.process.core.common.enums;

/**
 * @Author: <EMAIL>
 * @ModuleOwner: <EMAIL>
 * @Date: 12/24/2022 16:21
 * @Description:
 */
public enum MonitoringLogType {
    transform, dfConsumerFail, dpConsumerFail, dataParserFail, groovyFail, groovyInitFail, jsonPreFail, filterFail,
    sendCalcFail, sendAlarmFail, pipelineFail, groovyProcessorFail, filterProcessorFail, createTaskFail,
    dataForwardFail, dataForwardAsyncSendFail, dataForwardSyncSendFail,
    reMapperProcessorFail, timestampProcessorFail, ipProcessorFail, processorFail, extractUsedPipelineIdFail,
    notFound, consumerFail, customerLabelProcessorFail, redisFail
}
