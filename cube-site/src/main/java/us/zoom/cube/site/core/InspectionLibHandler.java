package us.zoom.cube.site.core;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import us.zoom.infra.dao.model.InspectionLibDO;
import us.zoom.infra.dao.service.InspectionLibDAO;

/**
* @Description: 
* @author: <PERSON>
* @date: 2023/04/11 10:17
*/
@Service
public class InspectionLibHandler {

    public static final String DEFAULT_TEMPLATE_NAME = "common-template";

    @Autowired
    private InspectionLibDAO inspectionLibDAO;

    public InspectionLibDO findOne() {
        return inspectionLibDAO.findOne();
    }

    public InspectionLibDO findByName(String name) {
        return inspectionLibDAO.findByName(name);
    }

    public int add(InspectionLibDO inspectionLibDO) {
        return inspectionLibDAO.add(inspectionLibDO);
    }

}
