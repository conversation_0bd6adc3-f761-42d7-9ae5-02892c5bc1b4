package us.zoom.cube.site.lib.output.dataparser;

import us.zoom.cube.site.lib.common.dataparser.BaseDataParserPipeline;
import us.zoom.cube.site.lib.input.dataparser.DataField;
import us.zoom.cube.site.lib.input.dataparser.DataParserFilterProcessorInput;
import us.zoom.cube.site.lib.input.dataparser.DataParserGrokProcessorInput;
import us.zoom.cube.site.lib.input.dataparser.DataParserRemapperProcessorInput;

import java.util.List;

/**
 * <AUTHOR> @date 2020/3/10
 */
public class DataParserPipelineOut extends BaseDataParserPipeline  {
    private List<DataParserPipelineOut> sons;
    private List<DataField> fields;
    private List<BaseProcessorOut> processores;
    private Boolean newDiscovery;

    public List<DataParserPipelineOut> getSons() {
        return sons;
    }

    public void setSons(List<DataParserPipelineOut> sons) {
        this.sons = sons;
    }

    public List<DataField> getFields() {
        return fields;
    }

    public void setFields(List<DataField> fields) {
        this.fields = fields;
    }

    public List<BaseProcessorOut> getProcessores() {
        return processores;
    }

    public void setProcessores(List<BaseProcessorOut> processores) {
        this.processores = processores;
    }

    public Boolean getNewDiscovery() {
        return newDiscovery;
    }

    public void setNewDiscovery(Boolean newDiscovery) {
        this.newDiscovery = newDiscovery;
    }
}
