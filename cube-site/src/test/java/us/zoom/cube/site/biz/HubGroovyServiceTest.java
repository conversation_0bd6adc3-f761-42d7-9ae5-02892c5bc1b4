package us.zoom.cube.site.biz;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import us.zoom.cube.lib.hub.DataParserRawDataTypeEnum;
import us.zoom.cube.site.biz.syspara.HubGroovyParaService;
import us.zoom.cube.site.lib.input.DataFlowInput;
import us.zoom.cube.site.lib.input.dataparser.DataParserGroovyProcessorInput;
import us.zoom.cube.site.lib.input.dataparser.DataParserSourceInput;
import us.zoom.cube.site.lib.input.dataparser.DataParserV2Input;
import us.zoom.infra.dao.model.DataFlowDO;
import us.zoom.infra.dao.model.DataParserDO;
import us.zoom.infra.dao.model.DataParserSourceDO;

/**
 * @Author: <EMAIL>
 * @ModuleOwner: <EMAIL>
 * @Date: 01/12/2024 19:35
 * @Description:
 */
public class HubGroovyServiceTest {
    private final String SECURITY_CHAMPION_ROLE = "securityChampion";
    private HubGroovyParaService hubGroovyParaService;
    private AuthService authService;
    private HubGroovyCheckService hubGroovyCheckService;

    private String userId = "user-id-001";
    private String tenantId = "tenant-001";
    private String role = SECURITY_CHAMPION_ROLE;
    private DataFlowInput dataFlowInput;
    private DataFlowDO dataFlowDO;
    private DataParserGroovyProcessorInput processorInput;
    private DataParserSourceInput dataParserSourceInput;
    private DataParserSourceDO dataParserSourceDO;
    private DataParserV2Input dataParserInput;
    private DataParserDO dataParserDO;

    @BeforeEach
    public void beforeEach() {
        dataParserInput = new DataParserV2Input();
        dataParserInput.setTenantId(tenantId);
        dataParserInput.setUserId(userId);
        dataParserDO = new DataParserDO();
        dataParserSourceInput = new DataParserSourceInput();
        dataParserSourceInput.setTenantId(tenantId);
        dataParserSourceInput.setUserId(userId);
        dataParserSourceDO = new DataParserSourceDO();
        processorInput = new DataParserGroovyProcessorInput();
        processorInput.setTenantId(tenantId);
        processorInput.setUserId(userId);
        dataFlowInput = new DataFlowInput();
        dataFlowInput.setTenantId(tenantId);
        dataFlowInput.setUserId(userId);
        dataFlowDO = new DataFlowDO();
        hubGroovyParaService = Mockito.mock(HubGroovyParaService.class);
        Mockito.when(hubGroovyParaService.getGroovySwitch()).thenReturn(true);
        Mockito.when(hubGroovyParaService.getGroovyRole()).thenReturn(SECURITY_CHAMPION_ROLE);
        authService = Mockito.mock(AuthService.class);
        Mockito.when(authService.hasSuchRole(dataFlowInput.getUserId(), dataFlowInput.getTenantId(), SECURITY_CHAMPION_ROLE))
                .thenReturn(false);
        hubGroovyCheckService = new HubGroovyCheckService(authService, hubGroovyParaService);
    }

    @Test
    public void testSwitchClose() {
        Mockito.when(hubGroovyParaService.getGroovySwitch()).thenReturn(false);
        Assertions.assertEquals(hubGroovyCheckService.hasDataFlowGroovyPermission(dataFlowInput), true);
    }

    @Test
    public void testNotSecurityChampion() {
        Mockito.when(authService.hasSuchRole(dataFlowInput.getUserId(), dataFlowInput.getTenantId(), SECURITY_CHAMPION_ROLE)).thenReturn(false);
        Assertions.assertEquals(hubGroovyCheckService.hasDataFlowGroovyPermission(dataFlowInput), true);
    }

    @Test
    public void testHasSecurityChampionHasGroovy() {

        dataFlowInput.setScriptMethod(null);
        dataFlowInput.setScriptContent("content");
        Boolean boo = hubGroovyCheckService.hasDataFlowGroovyPermission(dataFlowInput);
        Assertions.assertEquals(boo, false);

        dataFlowInput.setScriptMethod("method");
        dataFlowInput.setScriptContent(null);
        Boolean boo1 = hubGroovyCheckService.hasDataFlowGroovyPermission(dataFlowInput);
        Assertions.assertEquals(boo1, false);

        dataFlowInput.setScriptMethod("method");
        dataFlowInput.setScriptContent("content");
        Boolean boo2 = hubGroovyCheckService.hasDataFlowGroovyPermission(dataFlowInput);
        Assertions.assertEquals(boo2, false);
    }

    @Test
    public void testHasSecurityChampionNotGroovy1() {
        dataFlowInput.setScriptMethod(null);
        dataFlowInput.setScriptContent(null);
        Assertions.assertEquals(hubGroovyCheckService.hasDataFlowGroovyPermission(dataFlowInput), true);
    }

    @Test
    public void testHasSecurityChampionNotGroovy2() {
        dataFlowInput.setScriptMethod(null);
        dataFlowInput.setScriptContent(null);
        Assertions.assertEquals(hubGroovyCheckService.hasDataFlowGroovyPermission(dataFlowInput, dataFlowDO), true);
    }

    @Test
    public void testHasSecurityChampionGroovyEquals() {
        dataFlowInput.setScriptMethod("method");
        dataFlowInput.setScriptContent("content");
        dataFlowDO.setScriptMethod("method");
        dataFlowDO.setScriptContent("content");
        Mockito.when(authService.hasSuchRole(dataFlowInput.getUserId(), dataFlowInput.getTenantId(), SECURITY_CHAMPION_ROLE)).thenReturn(true);
        Assertions.assertEquals(hubGroovyCheckService.hasDataFlowGroovyPermission(dataFlowInput, dataFlowDO), true);
    }

    @Test
    public void testHasSecurityChampionGroovyNotEquals() {
        dataFlowInput.setScriptMethod("method");
        dataFlowInput.setScriptContent("content");
        dataFlowDO.setScriptMethod("method1");
        dataFlowDO.setScriptContent("content1");
        Assertions.assertEquals(hubGroovyCheckService.hasDataFlowGroovyPermission(dataFlowInput, dataFlowDO), false);
    }

    @Test
    public void testHasGroovyProcessorNotPermission() {
        Assertions.assertEquals(hubGroovyCheckService.hasGroovyProcessorPermission(processorInput), false);
    }

    @Test
    public void testHasGroovyProcessorPermission() {
        Mockito.when(authService.hasSuchRole(processorInput.getUserId(), processorInput.getTenantId(), SECURITY_CHAMPION_ROLE)).thenReturn(true);
        Assertions.assertEquals(hubGroovyCheckService.hasGroovyProcessorPermission(processorInput), true);
    }

    @Test
    public void testHasDataParserSourcePermission() {
        Mockito.when(authService.hasSuchRole(dataParserSourceInput.getUserId(), dataParserSourceInput.getTenantId(), SECURITY_CHAMPION_ROLE)).thenReturn(true);
        dataParserSourceInput.setRawDataFormat("other");
        Assertions.assertEquals(hubGroovyCheckService.hasDataParserSourcePermission(dataParserSourceInput), true);
    }

    @Test
    public void testHasDataParserSourcePermissionRawDataFormat() {
        dataParserSourceInput.setRawDataFormat(DataParserRawDataTypeEnum.json.name());
        Assertions.assertEquals(hubGroovyCheckService.hasDataParserSourcePermission(dataParserSourceInput), true);
        dataParserSourceInput.setRawDataFormat(DataParserRawDataTypeEnum.other.name());
        Assertions.assertEquals(hubGroovyCheckService.hasDataParserSourcePermission(dataParserSourceInput), false);
    }

    @Test
    public void testHasDataParserSourcePermissionGroovy() {
        dataParserSourceInput.setRawDataFormat(DataParserRawDataTypeEnum.json.name());
        Assertions.assertEquals(hubGroovyCheckService.hasDataParserSourcePermission(dataParserSourceInput, dataParserSourceDO), true);
        dataParserSourceInput.setRawDataFormat(DataParserRawDataTypeEnum.other.name());
        dataParserSourceDO.setInvokeFunction("function");
        dataParserSourceDO.setRawDataParseRule("setRawDataParseRule");
        Assertions.assertEquals(hubGroovyCheckService.hasDataParserSourcePermission(dataParserSourceInput, dataParserSourceDO), false);

        dataParserSourceInput.setRawDataFormat(DataParserRawDataTypeEnum.other.name());
        dataParserSourceDO.setInvokeFunction("function");
        dataParserSourceDO.setRawDataParseRule("setRawDataParseRule");
        dataParserSourceInput.setInvokeFunction("function");
        dataParserSourceInput.setRawDataParseRule("setRawDataParseRule");
        Assertions.assertEquals(hubGroovyCheckService.hasDataParserSourcePermission(dataParserSourceInput, dataParserSourceDO), true);

        dataParserSourceInput.setRawDataFormat(DataParserRawDataTypeEnum.other.name());
        dataParserSourceDO.setInvokeFunction("function1");
        dataParserSourceDO.setRawDataParseRule("setRawDataParseRule");
        dataParserSourceInput.setInvokeFunction("function");
        dataParserSourceInput.setRawDataParseRule("setRawDataParseRule");
        Assertions.assertEquals(hubGroovyCheckService.hasDataParserSourcePermission(dataParserSourceInput, dataParserSourceDO), false);

        dataParserSourceInput.setRawDataFormat(DataParserRawDataTypeEnum.other.name());
        dataParserSourceDO.setInvokeFunction("function");
        dataParserSourceDO.setRawDataParseRule("setRawDataParseRule1");
        dataParserSourceInput.setInvokeFunction("function");
        dataParserSourceInput.setRawDataParseRule("setRawDataParseRule");
        Assertions.assertEquals(hubGroovyCheckService.hasDataParserSourcePermission(dataParserSourceInput, dataParserSourceDO), false);

        dataParserSourceInput.setRawDataFormat(DataParserRawDataTypeEnum.other.name());
        dataParserSourceDO.setInvokeFunction("function1");
        dataParserSourceDO.setRawDataParseRule("setRawDataParseRule1");
        dataParserSourceInput.setInvokeFunction("function");
        dataParserSourceInput.setRawDataParseRule("setRawDataParseRule");
        Assertions.assertEquals(hubGroovyCheckService.hasDataParserSourcePermission(dataParserSourceInput, dataParserSourceDO), false);
    }


    @Test
    public void testHasDataParserPermission() {
        Mockito.when(authService.hasSuchRole(dataFlowInput.getUserId(), dataFlowInput.getTenantId(), SECURITY_CHAMPION_ROLE)).thenReturn(true);
        Assertions.assertEquals(hubGroovyCheckService.hasDataParserPermission(dataParserInput), true);
    }

    @Test
    public void testHasDataParserPermissionRawDataFormat() {
        dataParserInput.setRawDataType(DataParserRawDataTypeEnum.json.getCode());
        Assertions.assertEquals(hubGroovyCheckService.hasDataParserPermission(dataParserInput), true);
        dataParserInput.setRawDataType(DataParserRawDataTypeEnum.other.getCode());
        Assertions.assertEquals(hubGroovyCheckService.hasDataParserPermission(dataParserInput), false);
    }

    @Test
    public void testHasDataParserPermissionPermissionGroovy() {
        dataParserInput.setRawDataType(DataParserRawDataTypeEnum.json.getCode());
        Assertions.assertEquals(hubGroovyCheckService.hasDataParserPermission(dataParserInput, dataParserDO), true);
        dataParserInput.setRawDataType(DataParserRawDataTypeEnum.other.getCode());
        dataParserDO.setInvokeFunction("function");
        dataParserDO.setRawDataParseRule("setRawDataParseRule");
        Assertions.assertEquals(hubGroovyCheckService.hasDataParserPermission(dataParserInput, dataParserDO), false);

        dataParserInput.setRawDataType(DataParserRawDataTypeEnum.other.getCode());
        dataParserDO.setInvokeFunction("function");
        dataParserDO.setRawDataParseRule("setRawDataParseRule");
        dataParserInput.setInvokeFunction("function");
        dataParserInput.setRawDataParseRule("setRawDataParseRule");
        Assertions.assertEquals(hubGroovyCheckService.hasDataParserPermission(dataParserInput, dataParserDO), true);

        dataParserInput.setRawDataType(DataParserRawDataTypeEnum.other.getCode());
        dataParserDO.setInvokeFunction("function1");
        dataParserDO.setRawDataParseRule("setRawDataParseRule");
        dataParserInput.setInvokeFunction("function");
        dataParserInput.setRawDataParseRule("setRawDataParseRule");
        Assertions.assertEquals(hubGroovyCheckService.hasDataParserPermission(dataParserInput, dataParserDO), false);

        dataParserInput.setRawDataType(DataParserRawDataTypeEnum.other.getCode());
        dataParserDO.setInvokeFunction("function");
        dataParserDO.setRawDataParseRule("setRawDataParseRule1");
        dataParserInput.setInvokeFunction("function");
        dataParserInput.setRawDataParseRule("setRawDataParseRule");
        Assertions.assertEquals(hubGroovyCheckService.hasDataParserPermission(dataParserInput, dataParserDO), false);

        dataParserInput.setRawDataType(DataParserRawDataTypeEnum.other.getCode());
        dataParserDO.setInvokeFunction("function1");
        dataParserDO.setRawDataParseRule("setRawDataParseRule1");
        dataParserInput.setInvokeFunction("function");
        dataParserInput.setRawDataParseRule("setRawDataParseRule");
        Assertions.assertEquals(hubGroovyCheckService.hasDataParserPermission(dataParserInput, dataParserDO), false);
    }
}
