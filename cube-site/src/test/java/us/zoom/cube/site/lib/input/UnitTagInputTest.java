package us.zoom.cube.site.lib.input;

import com.google.gson.Gson;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import us.zoom.cube.lib.common.TaskTypeEnum;
import us.zoom.infra.enums.CommonStatusEnum;

/**
 * @Author: luis.zheng
 * @Date: 2021/8/10 10:39
 */
public class UnitTagInputTest {
    private Gson gson = new Gson();

    @Test
    public void testJson() {
        UnitTagInput input = new UnitTagInput();
        input.setName("name");
        input.setStatus(null);
        input.setDesc(null);
        String json = gson.toJson(input);
        System.out.println(json);
        UnitTagInput formJson = gson.fromJson(json, UnitTagInput.class);
        Assertions.assertTrue(formJson.getStatus().equals(CommonStatusEnum.enable.toString()));
        Assertions.assertTrue(formJson.getDesc().equals(""));
    }

    @Test
    public void testAddCheckNameLength() {
        UnitTagInput input = new UnitTagInput();
        String name = "";
        for (int i = 0; i < 257; i++) {
            name = name + "1";
        }
        System.out.println(name.length());
        input.setName(name);
        IllegalArgumentException exception = Assertions.assertThrows(IllegalArgumentException.class, () ->
                input.check());
        Assertions.assertEquals("Name length must be less than 256", exception.getMessage());
    }

    @Test
    public void testAddCheckNameIsNull() {
        UnitTagInput input = new UnitTagInput();
        input.setName(null);
        IllegalArgumentException exception = Assertions.assertThrows(IllegalArgumentException.class, () ->
                input.check());
        Assertions.assertEquals("name not null", exception.getMessage());
    }

    @Test
    public void testAddCheckDescLength() {
        UnitTagInput input = new UnitTagInput();
        String name = "name";
        input.setName(name);
        input.setTaskType(TaskTypeEnum.calc.name());
        input.check();
        input.setDesc("123456789-123456789-123456789-123456789-123456789-123456789-123456789-123456789-123456789-123456789-1");
        System.out.println(input.getDesc().length());
        IllegalArgumentException exception = Assertions.assertThrows(IllegalArgumentException.class, () ->
                input.check());
        Assertions.assertEquals("desc length greater than 100", exception.getMessage());
    }

    @Test
    public void testCheckTaskTypeIsNull() {
        UnitTagInput input = new UnitTagInput();
        String name = "name";
        input.setName(name);
        input.setTaskType(null);
        IllegalArgumentException exception = Assertions.assertThrows(IllegalArgumentException.class, () ->
                input.check());
        Assertions.assertEquals("taskType is not exist", exception.getMessage());
    }

    @Test
    public void testCheckTaskTypeNotExist() {
        UnitTagInput input = new UnitTagInput();
        String name = "name";
        input.setName(name);
        input.setTaskType("null");
        IllegalArgumentException exception = Assertions.assertThrows(IllegalArgumentException.class, () ->
                input.check());
        Assertions.assertEquals("taskType is not exist", exception.getMessage());
    }

    @Test
    public void testCheckStatusNotExist() {
        UnitTagInput input = new UnitTagInput();
        String name = "name";
        input.setName(name);
        input.setTaskType(TaskTypeEnum.calc.name());
        input.setStatus("null");
        IllegalArgumentException exception = Assertions.assertThrows(IllegalArgumentException.class, () ->
                input.check());
        Assertions.assertEquals("status is not exist", exception.getMessage());
    }

    @Test
    public void testEditCheck() {
        UnitTagInput input = new UnitTagInput();
        IllegalArgumentException exception = Assertions.assertThrows(IllegalArgumentException.class, () ->
                input.editCheck());
        Assertions.assertEquals("id is null", exception.getMessage());
    }
}
