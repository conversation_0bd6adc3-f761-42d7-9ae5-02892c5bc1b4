package us.zoom.cube.site.lib.output.alarm;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * @author: <PERSON><PERSON> Ding
 * @date: 2022/10/25 21:50
 * @desc:
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AlarmRecordOverviewEntry {

    private String name;

    private int count;

    private String status;

    //key is level peril
    private Map<String, Integer> levelCount = new LinkedHashMap<>();

    private int isHighlighting;

}
