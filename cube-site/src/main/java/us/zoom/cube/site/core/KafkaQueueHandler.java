package us.zoom.cube.site.core;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.lib.common.CubeConstants;
import us.zoom.infra.dao.model.KafkaQueueDO;
import us.zoom.infra.dao.service.KafkaQueueDAO;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * @Author: luis.zheng
 */
@Component
public class KafkaQueueHandler {
    @Autowired
    private KafkaQueueDAO kafkaQueueDAO;

    public List<KafkaQueueDO> listAll() {
        List<KafkaQueueDO> kafkaQueueDOList = new ArrayList<>();
        long counts = kafkaQueueDAO.queryCounts();
        int pageSize = CubeConstants.CUBE_SQL_BATCH_QUERY_SIZE;
        long pageNum = counts / pageSize + (counts % pageSize == 0 ? 0 : 1);
        for (int i = 1 ; i <= pageNum ; i++){
            kafkaQueueDOList.addAll(kafkaQueueDAO.listBatch(pageSize * (i - 1),pageSize));
        }
        return  kafkaQueueDOList;
    }

    public List<KafkaQueueDO> getByDataParserIds(Set<String> dataParserIds) {
        if (CollectionUtils.isEmpty(dataParserIds)) {
            return new ArrayList<>();
        }
        return kafkaQueueDAO.getByDataParserIds(dataParserIds);
    }

    public List<KafkaQueueDO> getCalcAndAlarmQueueByDataParserIds(Set<String> dataParserIds) {
        if (CollectionUtils.isEmpty(dataParserIds)) {
            return new ArrayList<>();
        }
        return kafkaQueueDAO.getCalcAndAlarmQueueByDataParserIds(dataParserIds);
    }

    public int countByUnitTagId(String unitTagId) {
        return kafkaQueueDAO.countByUnitTagId(unitTagId);
    }

    public int countByKafkaClusterId(String kafkaClusterId) {
        return kafkaQueueDAO.countByKafkaClusterId(kafkaClusterId);
    }

    public List<String> getDistinctDataParserByKafkaClusterId(String kafkaClusterId, Integer limitNum) {
        return kafkaQueueDAO.getDistinctDataParserByKafkaClusterId(kafkaClusterId, limitNum);
    }

    public int countByOuterId(String outerId) {
        return kafkaQueueDAO.countByOuterId(outerId);
    }

    public void batchAdd(List<KafkaQueueDO> kafkaQueueDOS) {
        if (CollectionUtils.isEmpty(kafkaQueueDOS)) {
            return;
        }
        kafkaQueueDAO.batchAdd(kafkaQueueDOS);
    }
    public void batchUpdate(List<KafkaQueueDO> kafkaQueueDOS) {
        if (CollectionUtils.isEmpty(kafkaQueueDOS)) {
            return;
        }
        kafkaQueueDAO.batchUpdate(kafkaQueueDOS);
    }

    public void deleteByDataParserId(String dataParserId) {
        kafkaQueueDAO.deleteByDataParserId(dataParserId);
    }

    public void deleteByIds(Set<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        kafkaQueueDAO.deleteByIds(ids);
    }

    public Set<String> findDataParserByTopic(String topic) {
        return kafkaQueueDAO.findDataParserByTopic(topic);
    }

    public Set<String> findDataParserByService(String tenantId) {
        return kafkaQueueDAO.findDataParserByService(tenantId);
    }

    public List<KafkaQueueDO> findByType(List<String> types) {
        return kafkaQueueDAO.findByType(types);
    }
}
