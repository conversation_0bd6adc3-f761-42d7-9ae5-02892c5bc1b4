package us.zoom.cube.site.core.usergroup;

import com.okta.commons.lang.Collections;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.lib.common.CubeConstants;
import us.zoom.cube.lib.monitor.MonitorWrapper;
import us.zoom.cube.site.core.TenantHandler;
import us.zoom.cube.site.core.UserHandler;
import us.zoom.cube.site.core.auth.TenantUserRelaHandler;
import us.zoom.cube.site.core.auth.UserRoleHandler;
import us.zoom.cube.site.infra.utils.IdUtils;
import us.zoom.cube.site.lib.input.QueryTenantUserInput;
import us.zoom.cube.site.lib.input.UserInCache;
import us.zoom.cube.site.lib.output.PageResult;
import us.zoom.cube.site.lib.query.PageQuery;
import us.zoom.infra.dao.model.*;
import us.zoom.infra.dao.service.UserGroupDAO;
import us.zoom.infra.dao.service.UserGroupTenantRelaDAO;
import us.zoom.infra.dao.service.UserGroupUserRelaDAO;
import us.zoom.infra.model.IdAndName;
import us.zoom.infra.model.usergroup.UserGroupCfg;
import us.zoom.infra.model.usergroup.UserGroupTenantUserTO;
import us.zoom.infra.model.usergroup.UserGroupUserTO;
import us.zoom.infra.thread.ThreadLocalStore;
import us.zoom.infra.utils.Instance;
import us.zoom.cube.lib.utils.JsonUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class UserGroupHandler {

    private Logger monitorLog = LoggerFactory.getLogger("Monitor");

    private static final String COPY_PREFIX = "COPY ";

    @Autowired
    private UserGroupDAO userGroupDAO;

    @Autowired
    private UserGroupUserRelaDAO userGroupUserRelaDAO;

    @Autowired
    private UserGroupTenantRelaDAO userGroupTenantRelaDAO;

    @Autowired
    private UserHandler userHandler;

    @Autowired
    private TenantHandler tenantHandler;

    @Autowired
    private TenantUserRelaHandler tenantUserRelaHandler;

    @Autowired
    private UserRoleHandler userRoleHandler;

    public List<UserGroupUserRelaDO> getAllUserGroupUserRela() {
        return userGroupUserRelaDAO.listAll();
    }

    public void addUserGroup(UserGroupDO userGroupDO) {
        userGroupDAO.add(userGroupDO);
    }

    public void deleteUserGroup(String id) {
        userGroupDAO.deleteById(id);
    }

    public List<UserGroupDO> findByParam(List<String> userGroupIds, String name, String owner, String sharedOwnerId, int pageIndex, int pageSize) {
        return userGroupDAO.findByParam(userGroupIds, name, owner, sharedOwnerId, pageIndex, pageSize);
    }

    public int getCountByParam(List<String> userGroupIds, String name, String owner) {
        return userGroupDAO.getCountByParam(userGroupIds, name, owner);
    }

    public void editUserGroup(UserGroupDO userGroupDO) {
        userGroupDAO.edit(userGroupDO);

    }

    public void addUserToUserGroup(List<UserGroupUserRelaDO> userGroupUserRelaDOList) {
        userGroupUserRelaDAO.addBatch(userGroupUserRelaDOList);

    }

    public List<String> getGroupIdByUserId(String userId) {
        return userGroupUserRelaDAO.getGroupIdByUserId(userId);
    }

    public void deleteUserFromUserGroup(List<UserGroupUserRelaDO> userGroupUserRelaDOList) {
        if (!Collections.isEmpty(userGroupUserRelaDOList)) {
            userGroupUserRelaDOList.forEach(x -> userGroupUserRelaDAO.deleteByUserAndGroupId(x.getUserId(), x.getGroupId()));
        }

    }

    public void addTenantToUserGroup(List<UserGroupTenantRelaDO> userGroupTenantRelaDOList) {
        if (CollectionUtils.isEmpty(userGroupTenantRelaDOList)) {
            return;
        }
        userGroupTenantRelaDAO.addBatch(userGroupTenantRelaDOList);
    }

    public void deleteTenantFromUserGroup(List<UserGroupTenantRelaDO> userGroupTenantRelaDOList) {

        if (!Collections.isEmpty(userGroupTenantRelaDOList)) {
            userGroupTenantRelaDOList.forEach(x -> userGroupTenantRelaDAO.deleteByTenantAndGroupId(x.getTenantId(), x.getGroupId()));
        }

    }

    public void batchDeleteTenantFromUserGroup(List<String> tenantIdList, String groupId) {
        if (Collections.isEmpty(tenantIdList)) {
            return;
        }
        userGroupTenantRelaDAO.batchDeleteByTenantAndGroupId(tenantIdList, groupId);
    }

    public void batchDeleteUserFromUserGroup(List<String> userIdList, String groupId) {
        if (Collections.isEmpty(userIdList)) {
            return;
        }
        userGroupUserRelaDAO.batchDeleteByUserAndGroupId(userIdList, groupId);
    }

    private void printMonitorLog(MonitorWrapper monitorWrapper, long begin) {
        monitorWrapper.setTs(System.currentTimeMillis());
        monitorWrapper.addField("totalTime", System.currentTimeMillis() - begin);
        monitorLog.info(JsonUtils.toJsonStringIgnoreExp(monitorWrapper));
    }

    public UserGroupDO getUserGroup(String id) {
        return userGroupDAO.getById(id);
    }

    public UserGroupDO getUserGroupByName(String name) {
        return userGroupDAO.getByName(name);
    }

    public List<UserGroupUserRelaDO> getUserRelaByGroupId(String groupId) {
        return userGroupUserRelaDAO.getByGroupId(groupId);
    }

    public List<UserGroupTenantRelaDO> getTenantRelaByGroupId(String groupId) {
        return userGroupTenantRelaDAO.getByGroupId(groupId);
    }

    public List<UserGroupDO> getUserGroupHasSuchRole(String role) {
        return userGroupDAO.getUserGroupHasSuchRole(role);
    }

    public PageResult<UserGroupTenantUserTO> searchTenantUserByRole(PageQuery<QueryTenantUserInput> queryTenantUserInput) {
        QueryTenantUserInput queryPara = queryTenantUserInput.getQueryPara();

        List<UserGroupTenantUserTO> tenantUserRelaDOList = new ArrayList<>();
        int count = 0;

        List<UserGroupDO> userGroupHasSuchRole = getUserGroupHasSuchRole(queryPara.getRole());
        List<String> userGroupIds = Instance.ofNullable(userGroupHasSuchRole).stream()
                .map(x -> x.getId()).collect(Collectors.toList());
        UserRoleDO roleByName = userRoleHandler.getRoleByName(queryPara.getRole());
        if (roleByName != null && roleByName.getCrossService()) {
            if (!CollectionUtils.isEmpty(userGroupHasSuchRole)) {
                tenantUserRelaDOList = userGroupUserRelaDAO.findCrossServiceByParam(userGroupIds,
                        queryPara.getUid(), queryTenantUserInput.getStartIndex(), queryTenantUserInput.getPageSize());
                count = userGroupUserRelaDAO.countCrossServiceByParam(userGroupIds,
                        queryPara.getUid());
            }
        } else {

            tenantUserRelaDOList = userGroupUserRelaDAO.findByParam(userGroupIds,
                    queryPara.getUid(), queryPara.getTid(), queryTenantUserInput.getStartIndex(), queryTenantUserInput.getPageSize());
            count = userGroupUserRelaDAO.countByParam(userGroupIds,
                    queryPara.getUid(), queryPara.getTid());
        }
        fillData(tenantUserRelaDOList, roleByName, userGroupIds);

        PageResult ps = new PageResult(count, tenantUserRelaDOList);
        return ps;
    }

    private void fillData(List<UserGroupTenantUserTO> tenantUserRelaDOList, UserRoleDO roleDO, List<String> containUserGroupIds) {
        Map<String, UserGroupCfg> userGroupMap = tenantUserRelaHandler.getUserGroupMap();

        Instance.ofNullable(tenantUserRelaDOList).stream().forEach(x -> {
            UserInCache userFromCache = userHandler.getUserFromCache(x.getUserId());
            x.setUserName(null != userFromCache ? userFromCache.getName() : null);

            if (roleDO!=null && roleDO.getCrossService()) {
                x.setTenantName(CubeConstants.ALL_SERVICES);

                List<IdAndName> userGroupIds = userGroupMap.values().stream()
                        .filter(userGroup -> containUserGroupIds.contains(userGroup.getId()))
                        .filter(userGroup -> userGroup.getRole().equals(roleDO.getRole()))
                        .map(userGroup -> new IdAndName(userGroup.getId(), userGroup.getName())).collect(Collectors.toList());
                x.setUserGroups(userGroupIds);
                x.setRole(roleDO.getRole());
            } else {
                TenantDO tenantById = tenantHandler.getTenantById(x.getTenantId());
                x.setTenantName(null != tenantById ? tenantById.getName() : null);
                List<IdAndName> userGroupIds = userGroupMap.values().stream()
                        .filter(userGroup -> containUserGroupIds.contains(userGroup.getId()))
                        .filter(userGroup -> org.apache.commons.collections4.CollectionUtils.containsAny(userGroup.getTenantIdList(), x.getTenantId())
                                && org.apache.commons.collections4.CollectionUtils.containsAny(userGroup.getUserIdList(), x.getUserId()))
                        .map(userGroup -> new IdAndName(userGroup.getId(), userGroup.getName())).collect(Collectors.toList());
                x.setUserGroups(userGroupIds);
                x.setRole(roleDO.getRole());
            }

        });
    }

    public List<UserGroupUserTO> findTenantUserByUserId(String userId) {
        return userGroupDAO.findUserTenantRoleByParam(userId);
    }

    public String duplicateUserGroupDO(UserGroupDO userGroup) {
        UserGroupDO target = new UserGroupDO();
        BeanUtils.copyProperties(userGroup, target);
        target.setId(IdUtils.generateId());
        target.setOwner(ThreadLocalStore.getUserNameLocal());
        target.setName(COPY_PREFIX + target.getName());
        addUserGroup(target);

        List<UserGroupUserRelaDO> userGroupUserRelaDOList = userGroupUserRelaDAO.getByGroupId(userGroup.getId());
        List<UserGroupUserRelaDO> userGroupUserRelaDOListToCopy = new ArrayList<>();
        if (!Collections.isEmpty(userGroupUserRelaDOList)) {
            userGroupUserRelaDOList.forEach(x -> {
                UserGroupUserRelaDO userGroupUserRelaDO = new UserGroupUserRelaDO();
                userGroupUserRelaDO.setId(IdUtils.generateId());
                userGroupUserRelaDO.setGroupId(target.getId());
                userGroupUserRelaDO.setUserId(x.getUserId());

                userGroupUserRelaDOListToCopy.add(userGroupUserRelaDO);
            });
            addUserToUserGroup(userGroupUserRelaDOListToCopy);
        }

        List<UserGroupTenantRelaDO> userGroupTenantRelaDOList = userGroupTenantRelaDAO.getByGroupId(userGroup.getId());
        List<UserGroupTenantRelaDO> userGroupTenantRelaDOListToCopy = new ArrayList<>();
        if (!Collections.isEmpty(userGroupTenantRelaDOList)) {
            userGroupTenantRelaDOList.forEach(x -> {
                UserGroupTenantRelaDO userGroupTenantRelaDO = new UserGroupTenantRelaDO();
                userGroupTenantRelaDO.setId(IdUtils.generateId());
                userGroupTenantRelaDO.setGroupId(target.getId());
                userGroupTenantRelaDO.setTenantId(x.getTenantId());
                userGroupTenantRelaDOListToCopy.add(userGroupTenantRelaDO);
            });
            addTenantToUserGroup(userGroupTenantRelaDOListToCopy);
        }

        return target.getId();

    }
}
