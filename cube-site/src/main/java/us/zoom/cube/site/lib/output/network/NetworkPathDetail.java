package us.zoom.cube.site.lib.output.network;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class NetworkPathDetail {
    @JsonProperty("sNode")
    private String sNode;
    @JsonProperty("dNode")
    private String dNode;
    private List<PathNodeData> pathNodeData;
    private List<PathNodeLink> pathNodeLinks;
}

