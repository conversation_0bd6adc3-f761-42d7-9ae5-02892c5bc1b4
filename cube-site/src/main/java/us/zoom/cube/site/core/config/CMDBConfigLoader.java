package us.zoom.cube.site.core.config;

import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import us.zoom.cloud.secrets.spring.annotation.SecretValue;
import us.zoom.cube.site.biz.SysParaService;
import us.zoom.cube.site.core.*;
import us.zoom.cube.site.infra.AsyncMQInstance;
import us.zoom.cube.site.lib.output.cmdb.CMDBDeviceInfo;
import us.zoom.cube.site.lib.output.cmdb.CMDBServerInfo;
import us.zoom.cube.site.lib.output.cmdb.NetDeviceTorTopo;
import us.zoom.cube.site.lib.query.CMDBServerQuery;
import us.zoom.infra.dao.model.CmdbNetdeviceDO;
import us.zoom.infra.dao.model.CmdbServerDO;
import us.zoom.infra.dao.model.DistributedLockDO;
import us.zoom.infra.dao.service.DistributedLockDAO;
import us.zoom.infra.thread.CacheLoaderScheduler;
import us.zoom.infra.utils.HttpUtils;
import us.zoom.infra.utils.Instance;
import us.zoom.infra.utils.IpUtils;
import us.zoom.cube.lib.utils.JsonUtils;

import jakarta.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;


@Component
@Slf4j
public class CMDBConfigLoader {

    private static final int SUCCESS_CODE = 200;
    private static final int MAX_RETRY_TIME = 3;
    private int querySize = 100;
    private String lockType = "cmdb_load_assigner";
    private int schedule = 24*60;
    private int lockTimeOut = schedule;

    @Value("${cmdb.login.url}")
    private String loginUrl;
    @Value("${cmdb.server.query.url}")
    private String serverQueryUrl;
    @Value("${cmdb.device.query.url}")
    private String deviceQueryUrl;
    @Value("${cmdb.tor.topo.query.url}")
    private String torTopoQueryUrl;
    @Value("${cmdb.ca.file.path}")
    private String caFilePath;
    @SecretValue("cmdb.user.name")
    private String userName;
    @SecretValue("cmdb.password")
    private String password;

    @Value("${cmdb.server.topic}")
    private String serverUpdateTopic;
    @Value("${cmdb.device.topic}")
    private String deviceUpdateTopic;
    private String consumerGroup = "cube-group";


    @Autowired
    private CmdbServerHandler cmdbServerHandler;
    @Autowired
    private CmdbDeviceHandler cmdbDeviceHandler;
    @Autowired
    private CmdbServerNetdeviceTopoHandler cmdbServerNetdeviceTopoHandler;
    @Autowired
    private Environment environment;
    @Autowired
    private CMDBServerConsumerHandler cmdbServerConsumerHandler;
    @Autowired
    private CMDBDeviceConsumerHandler cmdbDeviceConsumerHandler;
    @Autowired
    private SysParaService sysParaService;
    @Autowired
    private DistributedLockDAO distributedLockDAO;

    @PostConstruct
    public void initCMDBConfigLoader() {
        try {
            CacheLoaderScheduler.getInstance().getScheduler().scheduleWithFixedDelay(new Runnable() {
                @Override
                public void run() {
                    try{
                        if (sysParaService.isCMDBConfigLoader() && tryLock()){
                            List<CmdbServerDO> cmdbServerDOS = cmdbServerHandler.listAll();
                            Map<String,CmdbServerDO> serverMap = Instance.ofNullable(cmdbServerDOS).stream().collect(Collectors.toMap(CmdbServerDO::getServerId,cmdbServerDO -> cmdbServerDO));
                            List<CmdbNetdeviceDO> netdeviceDOS = cmdbDeviceHandler.listAll();
                            Map<String,CmdbNetdeviceDO> netdeviceMap = Instance.ofNullable(netdeviceDOS).stream().collect(Collectors.toMap(CmdbNetdeviceDO::getDeviceId,cmdbNetdeviceDO -> cmdbNetdeviceDO));
                            log.info("cmdb server and netdevice info load begin");
                            List<CMDBServerInfo> serverInfos = loadServerInfo(serverMap);
                            log.info("load server info success");
                            loadDeviceInfo(netdeviceMap);
                            log.info("load device success");
                        }else {
                            log.info("not update");
                        }
                    } catch (Exception e){
                        e.printStackTrace();
                        log.error("load cmdb info error", e);
                    }

                }
            }, 3, schedule, TimeUnit.MINUTES);
        // consumer

            AsyncMQInstance.init(environment.getProperty("async.mq.endpoint"), environment.getProperty("async.mq.username"), environment.getProperty("async.mq.password"));
            AsyncMQInstance.getInstance().registerConsumer(serverUpdateTopic,consumerGroup,1,cmdbServerConsumerHandler);
            AsyncMQInstance.getInstance().registerConsumer(deviceUpdateTopic,consumerGroup,1,cmdbDeviceConsumerHandler);
        }catch (Exception e){
            e.printStackTrace();
            log.error("initCMDBConfigLoader", e);
        }
    }

    public List<CMDBServerInfo> loadServerInfo(Map<String,CmdbServerDO> serverMap) {
        String serverQueryToken = getBearToken();
        if (StringUtils.isBlank(serverQueryToken)){
            log.error("fail to get login token");
            return null;
        }
        return getServerTotal(serverQueryToken,serverMap);
    }

    public void loadDeviceInfo(Map<String,CmdbNetdeviceDO> netdeviceMap) {
        String deviceQueryToken = getBearToken();
        if (StringUtils.isBlank(deviceQueryToken)){
            log.error("fail to get login token");
            return;
        }
        getDeviceTotal(deviceQueryToken,netdeviceMap);
    }


    private List<CMDBDeviceInfo> getDeviceTotal(String deviceQueryToken,Map<String,CmdbNetdeviceDO> netdeviceMap) {
        List<CMDBDeviceInfo> deviceInfoList = new ArrayList<>();
        long totalCount = getDeviceBatch(deviceInfoList, 0, querySize, deviceQueryToken,netdeviceMap);
        int queryCount = (int) (totalCount / querySize) + (totalCount % querySize == 0 ? 0 : 1);
        for (int j = 2; j <= queryCount; j++) {
            getDeviceBatch(deviceInfoList, querySize * (j - 1), querySize, deviceQueryToken,netdeviceMap);
        }
        log.info(String.valueOf(deviceInfoList.size()));
        return deviceInfoList;
    }

    private long getDeviceBatch(List<CMDBDeviceInfo> deviceInfoList, long offset, int querySize, String deviceQueryToken,Map<String,CmdbNetdeviceDO> netdeviceMap) {
        AtomicInteger statusCode = new AtomicInteger(SUCCESS_CODE);

        List<String> urls = new ArrayList<>();
        urls.add(deviceQueryUrl);

        Map<String, String> headerParams = new HashMap<>();
        headerParams.put("Authorization", "Bearer " + deviceQueryToken);

        Map<String, String> param = new HashMap<>();
        param.put("precise", "false");
        param.put("offset", String.valueOf(offset));
        param.put("limit", String.valueOf(querySize));

        String content = HttpUtils.doGet(caFilePath,urls, param, headerParams, null, null, MAX_RETRY_TIME, statusCode);

        long totalCount = JsonParser.parseString(content).getAsJsonObject().get("TotalCount").getAsLong();
        JsonArray dataSetJson =  JsonParser.parseString(content).getAsJsonObject().get("DataSet").getAsJsonArray();
        List<CMDBDeviceInfo> cmdbDeviceInfoBatchAdd = new ArrayList<>();
        List<CMDBDeviceInfo> cmdbDeviceInfoBatchUpdate = new ArrayList<>();
        List<NetDeviceTorTopo> netDeviceTorTopoBatchAdd = new ArrayList<>();
        List<NetDeviceTorTopo> netDeviceTorTopoBatchUpdate = new ArrayList<>();
        for (int i = 0; i < dataSetJson.size(); i++) {
            CMDBDeviceInfo info = new Gson().fromJson(dataSetJson.get(i),CMDBDeviceInfo.class);
            if (netdeviceMap.containsKey(info.getId())){
                cmdbDeviceInfoBatchUpdate.add(info);
            }else {
                cmdbDeviceInfoBatchAdd.add(info);
            }
            if (StringUtils.equals(info.getRole(), "tor")) {
                Map<String, String> torTopoParam = new HashMap<>();
                torTopoParam.put("devicename", info.getDeviceName());
                String torTopoContent = HttpUtils.doGet(caFilePath,Arrays.asList(torTopoQueryUrl), torTopoParam, headerParams, null, null, MAX_RETRY_TIME, statusCode);
                JsonArray torTopoDataSets =  JsonParser.parseString(torTopoContent).getAsJsonObject().get("DataSet").getAsJsonArray();

                if (torTopoDataSets != null && torTopoDataSets.size() > 0) {
                    NetDeviceTorTopo netDeviceTorTopo = new Gson().fromJson(torTopoDataSets.get(0),NetDeviceTorTopo.class);
                    if (netdeviceMap.containsKey(info.getId())){
                        netDeviceTorTopoBatchUpdate.add(netDeviceTorTopo);
                    }else {
                        netDeviceTorTopoBatchAdd.add(netDeviceTorTopo);
                    }
                }
            }
            deviceInfoList.add(info);
        }
        // handler
        cmdbServerNetdeviceTopoHandler.batchAddCmdbServerNetdeviceTopo(netDeviceTorTopoBatchAdd);
        cmdbServerNetdeviceTopoHandler.batchUpdateCmdbServerNetdeviceTopo(netDeviceTorTopoBatchUpdate);
        cmdbDeviceHandler.batchAddCmdbDevice(cmdbDeviceInfoBatchAdd);
        cmdbDeviceHandler.batchUpdateCmdbDevice(cmdbDeviceInfoBatchUpdate);
        return totalCount;
    }

    private List<CMDBServerInfo> getServerTotal(String serverQueryToken,Map<String,CmdbServerDO> serverMap) {
        List<CMDBServerInfo> serverInfos = new ArrayList<>();
        long totalCount = getServerBatch(serverMap,serverInfos, 0, querySize, serverQueryToken);
        int queryCount = (int) (totalCount / querySize) + (totalCount % querySize == 0 ? 0 : 1);
        for (int j = 2; j <= queryCount; j++) {
            getServerBatch(serverMap,serverInfos, querySize * (j - 1), querySize, serverQueryToken);
        }
        log.info(String.valueOf(serverInfos.size()));
        return serverInfos;
    }

    private long getServerBatch(Map<String,CmdbServerDO> serverMap,List<CMDBServerInfo> cmdbServerInfos, long offset, int querySize, String serverQueryToken) {
        long totalCount = 0;
        List<CMDBServerInfo> cmdbServerInfoBatchAdd = new ArrayList<>();
        List<CMDBServerInfo> cmdbServerInfoBatchUpdate = new ArrayList<>();

        List<String> urls = new ArrayList<>();
        urls.add(serverQueryUrl);
        Map<String, String> headerParams = new HashMap<>();
        headerParams.put("Accept", "application/json");
        headerParams.put("Authorization", "Bearer " + serverQueryToken);
        Map<String, CMDBServerQuery> param = new HashMap<>();
        CMDBServerQuery cmdbServerQuery = new CMDBServerQuery(offset, querySize);
        param.put("query", cmdbServerQuery);
        log.info(JsonUtils.toJsonString(param));
        String content = null;
        AtomicInteger statusCode = new AtomicInteger(SUCCESS_CODE);
        try {
            content = HttpUtils.post(caFilePath, urls, new HashMap<>(), headerParams, JsonUtils.toJsonString(param), statusCode, null, MAX_RETRY_TIME);
            totalCount = JsonParser.parseString(content).getAsJsonObject().get("TotalCount").getAsLong();;
            JsonArray dataSetJson = JsonParser.parseString(content).getAsJsonObject().get("DataSet").getAsJsonArray();
            for (int i = 0; i < dataSetJson.size(); i++) {
                JsonObject object = dataSetJson.get(i).getAsJsonObject();
                CMDBServerInfo info = new Gson().fromJson(object,CMDBServerInfo.class);
                if (object.has("cloud_tags") && object.getAsJsonObject("cloud_tags").has("Cluster")) {
                    info.setCluster(object.getAsJsonObject("cloud_tags").get("Cluster").getAsString());
                }
                JsonArray publicIpMac = object.get("publicip_mac").getAsJsonArray();
                if (publicIpMac.size()>0){
                    info.setPublicIpMac(publicIpMac.get(0).getAsString());
                }else {
                    info.setPublicIpMac("");
                }
                if (serverMap.containsKey(info.getId())){
                    cmdbServerInfoBatchUpdate.add(info);
                }else {
                    cmdbServerInfoBatchAdd.add(info);
                }
                cmdbServerInfos.add(info);
            }
        } catch (Exception e) {
            log.error("log in cmdb error", e);
        }
        log.info("cmdbServerInfoBatchUpdate size {}",cmdbServerInfoBatchUpdate.size());
        log.info("cmdbServerInfoBatchAdd size {}",cmdbServerInfoBatchAdd.size());
        cmdbServerHandler.batchAddCmdbServer(cmdbServerInfoBatchAdd);
        cmdbServerHandler.batchUpdateCmdbServer(cmdbServerInfoBatchUpdate);
        return totalCount;
    }

    private String getBearToken() {
        List<String> urls = new ArrayList<>();
        urls.add(loginUrl);
        Map<String, String> headerParams = new HashMap<>(1);
        headerParams.put("Content-Type", "application/json;charset=UTF-8");
        Map<String, String> param = new HashMap<>();
        param.put("name", userName);
        param.put("password", password);
        String queryJson = JsonUtils.toJsonString(param);
        String content = null;
        AtomicInteger statusCode = new AtomicInteger(SUCCESS_CODE);
        try {
            content = HttpUtils.post(caFilePath, urls, param, headerParams, queryJson, statusCode, null, MAX_RETRY_TIME);
            String accessToken = JsonParser.parseString(content).getAsJsonObject().get("access_token").getAsString();
            return accessToken;
        } catch (Exception e) {
            log.error("log in cmdb error", e.getMessage());
        }
        return null;
    }


    private boolean tryLock() {
        List<DistributedLockDO> distributedLockDAOList = distributedLockDAO.listByType(lockType);
        if (CollectionUtils.isEmpty(distributedLockDAOList)) {
            return false;
        }
        boolean lockResult = distributedLockDAO.lockWithType(lockType, distributedLockDAOList.get(0).getHandler(), IpUtils.getLocalIP(), lockTimeOut) > 0;
        return lockResult;
    }
}
