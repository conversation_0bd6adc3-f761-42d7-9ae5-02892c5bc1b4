package us.zoom.cube.site.config;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.boot.test.context.runner.WebApplicationContextRunner;
import org.springframework.context.annotation.Bean;
import us.zoom.cube.site.api.intercept.SingleJwtAuthInterceptor;
import us.zoom.cube.site.config.properties.JwtAuthProperties;
import us.zoom.infra.dao.service.TenantDAO;
import us.zoom.infra.dao.service.UserDAO;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * @author: Starls Ding
 * @date: 2022/5/30 11:17
 * @desc:
 */

public class JwtAuthConfigUnitTest {

    private WebApplicationContextRunner applicationContextRunner;

    @BeforeEach
    void setUp() {
        applicationContextRunner = new WebApplicationContextRunner();
    }


    @Test
    public void onlyJwtConfigWork() {
        applicationContextRunner
                //.withClassLoader(new FilteredClassLoader(JwtAuthProperties.class))
                .withPropertyValues("cube.authorization.enableJwt=true")
                .withUserConfiguration(MockBeanDependencyConfiguration.class, JwtAuthProperties.class, SiteConfig.class, JwtAuthConfig.class)
                .run(context -> {
                    //We can load only JwtAuthConfig not SiteConfig becasue we have the revelent property to make it.
                    assertThat(context).doesNotHaveBean(SiteConfig.class);
                    assertThat(context).hasSingleBean(JwtAuthConfig.class);
                });
    }



}

//do not use @TestConfiguration here which will inject to all other integration test context and cause bean ununique.
class MockBeanDependencyConfiguration {

    @Bean
    UserDAO userDAO() {
        return Mockito.mock(UserDAO.class);
    }

    @Bean
    TenantDAO tenantDAO() {
        return Mockito.mock(TenantDAO.class);
    }

    @Bean
    SingleJwtAuthInterceptor singleJwtAuthInterceptor() {
        return Mockito.mock(SingleJwtAuthInterceptor.class);
    }

}

