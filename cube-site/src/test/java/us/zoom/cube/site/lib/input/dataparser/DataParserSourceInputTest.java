package us.zoom.cube.site.lib.input.dataparser;


import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import us.zoom.cube.site.infra.utils.IdUtils;
import us.zoom.infra.enums.CommonStatusEnum;

/**
 * @Author: <EMAIL>
 * @ModuleOwner: <EMAIL>
 * @Date: 03/11/2022 09:41
 * @Description:
 */
public class DataParserSourceInputTest {

    DataParserSourceInput input = new DataParserSourceInput();

    @BeforeEach
    public void before() {
        input.setId(IdUtils.generateId());
        input.setName("name");
        input.setSource("source");
        input.setRawDataFormat("rawDataType");
        input.setRawDataParseType("RawDataParseType");
        input.setRawDataParseRule("RawDataParseRule(");
        input.setInvokeFunction("invokeFunction");
        input.setStatus(CommonStatusEnum.disable.name());
    }

    @Test
    public void testName() {
        input.setName("    ");
        IllegalArgumentException err = Assertions.assertThrows(IllegalArgumentException.class, () -> {
            input.check();
        });
        Assertions.assertEquals(err.getMessage(), "name is blank");
    }

    @Test
    public void testName1() {
        String name = "";
        for (int i = 0; i < 395; i++) {
            name = name + "i";
        }
        input.setName(name);
        IllegalArgumentException err = Assertions.assertThrows(IllegalArgumentException.class, () -> {
            input.check();
        });
        Assertions.assertEquals("name length exceeds 384", err.getMessage());
    }

    @Test
    public void testSource() {
        String source = "";
        for (int i = 0; i < 1025; i++) {
            source = source + "i";
        }
        input.setSource(source);
        IllegalArgumentException err = Assertions.assertThrows(IllegalArgumentException.class, () -> {
            input.check();
        });
        Assertions.assertEquals("source length exceeds 1024", err.getMessage());
    }

    @Test
    public void testSource1() {
        String sou = "";
        input.setSource(sou);
        IllegalArgumentException err = Assertions.assertThrows(IllegalArgumentException.class, () -> {
            input.check();
        });
        Assertions.assertEquals("source is blank", err.getMessage());
    }

    @Test
    public void testStatus() {
        input.setStatus("sou");
        IllegalArgumentException err = Assertions.assertThrows(IllegalArgumentException.class, () -> {
            input.check();
        });
        Assertions.assertEquals("status must be enable or disable, current value:sou", err.getMessage());
    }
}
