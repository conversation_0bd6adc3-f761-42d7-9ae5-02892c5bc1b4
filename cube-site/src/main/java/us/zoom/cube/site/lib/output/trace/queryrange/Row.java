package us.zoom.cube.site.lib.output.trace.queryrange;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;
import java.util.Map;

/**
 * @author: eason.jia
 * @date: 2024/8/19
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Row {
    private Timestamp timestamp;
    private Map<String, Object> data;
}
