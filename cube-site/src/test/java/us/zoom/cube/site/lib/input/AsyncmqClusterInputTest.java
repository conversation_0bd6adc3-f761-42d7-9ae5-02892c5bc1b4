package us.zoom.cube.site.lib.input;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import us.zoom.cube.lib.utils.JsonUtils;

/**
 * @Author: <EMAIL>
 * @ModuleOwner: <EMAIL>
 * @Date: 06/12/2023 10:45
 * @Description:
 */
public class AsyncmqClusterInputTest {
    @Test
    public void testCheck() {
        AsyncmqClusterInput input = new AsyncmqClusterInput();
        input.setName("");
        IllegalArgumentException exception = Assertions.assertThrows(IllegalArgumentException.class, () ->
                input.check());
        Assertions.assertEquals("name is blank", exception.getMessage());
        input.setName(null);
        exception = Assertions.assertThrows(IllegalArgumentException.class, () ->
                input.check());
        Assertions.assertEquals("name is blank", exception.getMessage());
        input.setName("1234567890123456789012345678901234567890123456789012345678901");
        exception = Assertions.assertThrows(IllegalArgumentException.class, () ->
                input.check());
        Assertions.assertEquals("name length is more than 60", exception.getMessage());
        input.setName("123");
        input.setEndpoint("");
        exception = Assertions.assertThrows(IllegalArgumentException.class, () ->
                input.check());
        Assertions.assertEquals("endpoint is blank", exception.getMessage());
        input.setEndpoint("123456789");
        input.setUsername("");
        exception = Assertions.assertThrows(IllegalArgumentException.class, () ->
                input.check());
        Assertions.assertEquals("username is blank", exception.getMessage());

        input.setUsername("222");
    }
    @Test
    public void toJson() {
        AsyncmqClusterInput input = new AsyncmqClusterInput();
        input.setName("name");
        input.setEndpoint("endpoint");
        input.setUsername("username");
        input.setPassword("password");

        System.out.println(JsonUtils.toJsonString(input));
    }
}
