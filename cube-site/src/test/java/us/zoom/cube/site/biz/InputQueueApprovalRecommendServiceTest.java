package us.zoom.cube.site.biz;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import us.zoom.cube.lib.common.AsyncMqQueueSourceTypeEnum;
import us.zoom.cube.site.biz.model.InputQueueRecommendModel;
import us.zoom.cube.site.biz.model.InputQueueRecommendParam;
import us.zoom.infra.dao.model.AsyncMqQueueDO;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @Author: <EMAIL>
 * @ModuleOwner: <EMAIL>
 * @Date: 08/12/2023 17:10
 * @Description:
 */
public class InputQueueApprovalRecommendServiceTest {
    private InputQueueApprovalRecommendService iqars = null;

    @BeforeEach
    public void before() {
        iqars = new InputQueueApprovalRecommendService();
    }

    @Test
    public void testIsWhiteList() {
        InputQueueRecommendParam param = new InputQueueRecommendParam();
        param.setWhiteEndWith(Sets.newHashSet("aaa", "bbb"));
        Assertions.assertEquals(iqars.isWhiteList("topicaaa", param), true);
        Assertions.assertEquals(iqars.isWhiteList("topicccc", param), false);
        Assertions.assertEquals(iqars.isWhiteList("topicbbb", param), true);
    }

    @Test
    public void testJson() throws JsonProcessingException {
        ObjectMapper objectMapper = new ObjectMapper();
        InputQueueRecommendParam param = objectMapper.readValue("{\"flinkQueue\":\"flink123\",\"alarmQueue\":\"alarm123\",\"hubUnitTagMap\":{\"eu\":\"1\",\"au\":\"1\",\"sg\":\"1\",\"in\":\"1\",\"ch\":\"1\",\"jp\":\"1\",\"eu02\":\"1\",\"ca\":\"1\",\"sa\":\"1\"},\"whiteEndWith\":[\"public\",\"infra\"]}\n", InputQueueRecommendParam.class);
        Assertions.assertEquals(param != null, true);
    }

    @Test
    public void testRecommendNonUsUnitTagId() throws JsonProcessingException {

        Map<String, String> hubUnitTagMap = Maps.newHashMap();
        hubUnitTagMap.put("eu02", "1");
        hubUnitTagMap.put("in", "2");
        hubUnitTagMap.put("eu", "3");
        hubUnitTagMap.put("ca", "4");
        hubUnitTagMap.put("au", "5");
        hubUnitTagMap.put("sa", "6");
        hubUnitTagMap.put("sg", "7");
        hubUnitTagMap.put("ch", "8");
        hubUnitTagMap.put("jp", "9");
        hubUnitTagMap.put("us", "10");
        hubUnitTagMap.put("default", "0");

        InputQueueRecommendParam param = new InputQueueRecommendParam();
        param.setHubUnitTagMap(hubUnitTagMap);

        InputQueueRecommendModel config = new InputQueueRecommendModel();
        iqars.recommendNonUsUnitTagId("topic_", param);
        Assertions.assertEquals(config.getUnitTagId(), hubUnitTagMap.get("default"));
        iqars.recommendNonUsUnitTagId("us", param);
        Assertions.assertEquals(config.getUnitTagId(), hubUnitTagMap.get("us"));
    }
}
