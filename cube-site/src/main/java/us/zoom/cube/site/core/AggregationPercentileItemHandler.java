package us.zoom.cube.site.core;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import us.zoom.infra.dao.model.AggregationHistogramRangeItemDO;
import us.zoom.infra.dao.model.AggregationPercentileItemDO;
import us.zoom.infra.dao.service.AggregationHistogramRangeItemDAO;
import us.zoom.infra.dao.service.AggregationPercentileItemDAO;

import java.util.List;

/**
 * @<PERSON> <PERSON>
 * @create 2020/7/19 10:20 AM
 */
@Service
public class AggregationPercentileItemHandler {

    @Autowired
    private AggregationPercentileItemDAO aggregationPercentileItemDAO;

    public int insertAggregationPercentile(AggregationPercentileItemDO aggregationPercentileItemDO){
        return aggregationPercentileItemDAO.insertAggregationPercentile(aggregationPercentileItemDO);
    }

    public int batchInsertAggregationPercentile(List<AggregationPercentileItemDO> aggregationPercentileItemDOList){
        if(CollectionUtils.isEmpty(aggregationPercentileItemDOList)){
            return 0;
        }
        return aggregationPercentileItemDAO.batchInsertAggregationPercentile(aggregationPercentileItemDOList);
    }

    public AggregationPercentileItemDO getById(String id){
        return aggregationPercentileItemDAO.getById(id);
    }

    public void deleteById(String id){
        aggregationPercentileItemDAO.deleteById(id);
    }

    public List<AggregationPercentileItemDO> listAll(){
        return aggregationPercentileItemDAO.listAll();
    }

    public void deleteByRuleId(String ruleId){
        aggregationPercentileItemDAO.deleteByRuleId(ruleId);
    }

    public int updateAggregationPercentile(AggregationPercentileItemDO aggregationPercentileItemDO){
        return aggregationPercentileItemDAO.updateAggregationPercentile(aggregationPercentileItemDO);
    }

    public int batchUpdateAggregationPercentile(List<AggregationPercentileItemDO> aggregationPercentileItemDOList){
        if(CollectionUtils.isEmpty(aggregationPercentileItemDOList)){
            return 0;
        }
        return aggregationPercentileItemDAO.batchUpdateAggregationPercentile(aggregationPercentileItemDOList);
    }


    public void deleteByRuleIdList(List<String> ruleIdList){
        if(!CollectionUtils.isEmpty(ruleIdList)) {
            aggregationPercentileItemDAO.deleteByRuleIdList(ruleIdList);
        }
    }

    public void batchDeleteByIdList(List<String> idList){
        if(!CollectionUtils.isEmpty(idList)) {
            aggregationPercentileItemDAO.batchDeleteByIdList(idList);
        }
    }

}
