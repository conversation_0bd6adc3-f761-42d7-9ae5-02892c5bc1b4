package us.zoom.cube.site.infra.utils;

import org.apache.commons.lang3.StringUtils;
import us.zoom.infra.utils.CommonSplitConstants;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

public class IdUtils {

    /**
     *
     * @return
     */
    public static String generateId()
    {
        String result= UUID.randomUUID().toString();

        if( -1 != result.indexOf(CommonSplitConstants.SPLIT)){
            result= result.replaceAll("_","-");
        }
        return result;
    }

    /**
     * generate id based on the input key
     * @param keyList input key list.
     * @return id
     */
    public static String generateId(List<String> keyList) {
        StringBuilder combinedString = new StringBuilder();
        for (String key : keyList) {
            combinedString.append(key);
        }

        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(combinedString.toString().getBytes(StandardCharsets.UTF_8));
            String hexString = bytesToHex(hash);
            return hexString.substring(0, 36);
        } catch (Exception ignored) {
        }
        return generateId();
    }

    private static String bytesToHex(byte[] hash) {
        StringBuilder hexString = new StringBuilder(2 * hash.length);
        for (byte b : hash) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) {
                hexString.append('0');
            }
            hexString.append(hex);
        }
        return hexString.toString();
    }

    public static void main(String[] args) {
        List<String> keyList = List.of("us_agent_heart_beat");

        String generatedId = generateId(keyList);
        System.out.println("Generated ID: " + generatedId);
    }
}
