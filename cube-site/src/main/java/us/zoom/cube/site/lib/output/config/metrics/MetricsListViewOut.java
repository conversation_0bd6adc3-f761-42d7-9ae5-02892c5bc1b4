package us.zoom.cube.site.lib.output.config.metrics;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2020/7/19 9:32 AM
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class MetricsListViewOut {
    String id;

    String metricsName;

    List<String> tagNames;

    FieldsCount fieldsCount;

    Integer type;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    Date modifyTime = new Date();

    @Data
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class FieldsCount{
        Integer common;
        Integer histogram;
        Integer percentile;
    }

}
