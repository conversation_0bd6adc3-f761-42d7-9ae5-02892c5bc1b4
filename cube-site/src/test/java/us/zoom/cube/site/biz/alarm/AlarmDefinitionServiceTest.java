package us.zoom.cube.site.biz.alarm;

import com.zoom.op.monitor.domain.alarm.*;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import us.zoom.cube.site.biz.BaseTest;
import us.zoom.cube.site.lib.ResponseObject;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: canyon.li
 * @date: 2023/09/13
 **/
public class AlarmDefinitionServiceTest  extends BaseTest {

    @Autowired
    AlarmDefinitionService alarmDefinitionService;

    @Test
    public void getById() {
        try {
            ResponseObject<AlarmDefinition> res = alarmDefinitionService.getById("e01852ff-2f62-4964-bf17-57b2571bffc7");
            System.out.println(res);
        } catch (Exception e) {
            System.out.println("test error:" + e.getMessage());
        }
    }

    @Test
    public void testUpdateTransactional() {
        try {
            ResponseObject<AlarmDefinition> res = alarmDefinitionService.getById("557e15e1-fd58-4505-893d-99590e2d8fc4");
            AlarmDefinition alarmDefinition = res.getData();

            AlarmRule alarmRule = new AlarmRule();
            alarmRule.setLevel(AlarmLevel.INFO);
            alarmRule.setNeedHits(2);
            alarmRule.setHitCount(2);

            List<RuleCondition> conditions = new ArrayList<>();
            RuleCondition ruleCondition = new RuleCondition();
            ruleCondition.setConditionType(ConditionType.FIELD);
            ruleCondition.setName("cpuLoadProcess");
            ruleCondition.setValueType("number");
            ruleCondition.setOperator("==");
            ruleCondition.setThreshold("1");

            conditions.add(ruleCondition);
            alarmRule.setConditions(conditions);
            List<AlarmRule> rules = new ArrayList<>();
            rules.add(alarmRule);
            alarmDefinition.setRules(rules);

            alarmDefinitionService.update(alarmDefinition);
            System.out.println(res);
        } catch (Exception e) {
            System.out.println("test error:" + e.getMessage());
        }
    }
}
