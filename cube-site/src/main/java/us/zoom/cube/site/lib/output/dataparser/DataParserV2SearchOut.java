package us.zoom.cube.site.lib.output.dataparser;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR> zheng
 */
@Data
public class DataParserV2SearchOut {
    private String id;

    private String name;
    /**
     * @see us.zoom.cube.lib.hub.DataParserRawDataTypeEnum
     */
    private Integer rawDataType;
    /**
     * @see us.zoom.cube.lib.common.DataParserUseStatusEnum
     */
    private Integer useStatus;

    private String tenantId;

    private String serviceName;

    private String createUserId;
    private String createUserName;

    private String editUserId;
    private String editUserName;

    private String topicTemplate;

    private Date gmtModify;
    private Date gmtCreate;

    public DataParserV2SearchOut(String id, String name, Integer rawDataType, String tenantId, String tenantName, Integer useStatus,
                                 String createUserId, String createUserName, String editUserId, String editUserName, String topicTemplate,
                                 Date gmtModify, Date gmtCreate) {
        this.id = id;
        this.name = name;
        this.rawDataType = rawDataType;
        this.tenantId = tenantId;
        this.serviceName = tenantName;
        this.useStatus = useStatus;
        this.createUserId = createUserId;
        this.createUserName = createUserName;
        this.editUserId = editUserId;
        this.editUserName = editUserName;
        this.topicTemplate = topicTemplate;
        this.gmtModify = gmtModify;
        this.gmtCreate = gmtCreate;
    }
}
