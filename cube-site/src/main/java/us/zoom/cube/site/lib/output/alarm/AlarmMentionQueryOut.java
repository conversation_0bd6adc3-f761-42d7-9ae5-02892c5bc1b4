package us.zoom.cube.site.lib.output.alarm;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import org.apache.commons.lang3.StringUtils;
import us.zoom.infra.dao.model.UserDO;

import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @author: canyon.li
 * @date: 2023/07/24
 **/
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AlarmMentionQueryOut {

    String email;

    String displayName;

    private static final String COMMON_REGEX = "([a-zA-Z]+).([a-zA-Z]+)@zoom.us";
    private static final Pattern PATTERN = Pattern.compile(COMMON_REGEX);
    public static AlarmMentionQueryOut convertFromUserDO(UserDO userDO) {
        String name = Optional.ofNullable(userDO).map(UserDO::getName).orElse(null);
        if (StringUtils.isBlank(name)) {
            return null;
        }
        AlarmMentionQueryOut out = new AlarmMentionQueryOut();
        out.setEmail(name);
        out.setDisplayName(convertToDisplayName(name));
        return out;
    }

    public static String convertToDisplayName(String name) {

        String displayName = name;

        if (StringUtils.isBlank(name)) {
            return displayName;
        }

        try {
            Matcher matcher = PATTERN.matcher(name);
            if (matcher.find()) {
                String firstName = StringUtils.capitalize(matcher.group(1));
                String lastName = StringUtils.capitalize(matcher.group(2));
                displayName = String.format("%s %s", firstName, lastName);
            }
        } catch (Exception ignored) {
        }
        return displayName;
    }
}
