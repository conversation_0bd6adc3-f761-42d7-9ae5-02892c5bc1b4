package us.zoom.cube.site.core;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import us.zoom.infra.dao.model.NetworkDetectionInfoDO;
import us.zoom.infra.dao.service.NetworkDetectionInfoDAO;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

@Component
public class NetworkDetectionInfoHandler {
    @Autowired
    private NetworkDetectionInfoDAO networkDetectionInfoDAO;

    public List<NetworkDetectionInfoDO> findKeys(List<String> detectionKeys) {
        return networkDetectionInfoDAO.findByKeys(detectionKeys);
    }

    public int insertBatch(List<NetworkDetectionInfoDO> networkDetectionInfoTOAdd) {
        if (CollectionUtils.isEmpty(networkDetectionInfoTOAdd)){
            return -1;
        }
        return networkDetectionInfoDAO.insertBatch(networkDetectionInfoTOAdd);
    }

    public int updateBatch(List<NetworkDetectionInfoDO> networkDetectionInfoTOUpdate) {
        if (CollectionUtils.isEmpty(networkDetectionInfoTOUpdate)){
            return -1;
        }
        return networkDetectionInfoDAO.updateBatch(networkDetectionInfoTOUpdate);
    }

    public List<NetworkDetectionInfoDO> selectByDetectionIds(List<String> detectionIds) {
        return networkDetectionInfoDAO.selectByDetectionIds(detectionIds);
    }
    public List<NetworkDetectionInfoDO> listByIpAndDetectionId(String publicIpMac,List<String> detectionIds) {
        return networkDetectionInfoDAO.listByIpAndDetectionId(publicIpMac,detectionIds);
    }

    public List<NetworkDetectionInfoDO> listByDetectionKeys(Set<String> detectionKeysToReduce) {
        return networkDetectionInfoDAO.listByDetectionKeys(detectionKeysToReduce);
    }
}
