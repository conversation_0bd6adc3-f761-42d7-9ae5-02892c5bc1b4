package us.zoom.cube.site.lib.output.asyncmq;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import us.zoom.mq.common.spec.Role;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AsyncmqLoginResult {
    String name;
    Role role;
    String token;
    Long expiration;
    String refreshToken;
    Long refreshExpiration;
    String publisher;
}
