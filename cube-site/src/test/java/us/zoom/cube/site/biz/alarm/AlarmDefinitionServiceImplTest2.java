package us.zoom.cube.site.biz.alarm;

import com.zoom.op.monitor.domain.alarm.*;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import us.zoom.cube.site.CubeSiteApplication;
import us.zoom.cube.site.lib.ResponseObject;
import us.zoom.infra.thread.ThreadLocalStore;
import us.zoom.cube.lib.utils.JsonUtils;

import java.util.ArrayList;
import java.util.List;


@SpringBootTest(classes = CubeSiteApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class AlarmDefinitionServiceImplTest2 {

    @Autowired
    private AlarmDefinitionService alarmDefinitionService;

    @Test
    public void test_save_alarmDefinition() {
        String userId = "06e6bd8d-8591-4866-968b-ff197957793a";
        ThreadLocalStore.setUserInfoLocal(userId);
        String tenantId = "47046ca9-3df0-4b05-97f0-ccb99303ee9b";
        ThreadLocalStore.setTenantInfoLocal(tenantId);


        AlarmDefinition alarmDefinition = new AlarmDefinition();
        alarmDefinition.setEnabled(true);
        alarmDefinition.setTenantId(tenantId);
        alarmDefinition.setUserId(userId);
        List<AlarmRule> rules = new ArrayList<>();
        AlarmRule alarmRule = new AlarmRule();
        alarmRule.setNeedHits(1);
        alarmRule.setLevel(AlarmLevel.WARN);
        List<RuleCondition> conditions = new ArrayList<>();
        RuleCondition ruleCondition = new RuleCondition();
        ruleCondition.setConditionType(ConditionType.FIELD);
        ruleCondition.setName("clusterId");
        ruleCondition.setOperator("in");
        ruleCondition.setThreshold("a,b");
        conditions.add(ruleCondition);
        alarmRule.setConditions(conditions);
        rules.add(alarmRule);
        alarmDefinition.setRules(rules);


        List<Notification> notifications = new ArrayList<>();
        Notification notification = new Notification();
        Channel channel = new Channel();
        channel.setId("b82551ec-2201-4518-beb5-9b4abeed3edd");
        notification.setChannel(channel);
        notification.setTitle("this is alarm syn");
        notification.setContent("byteIn is ${byteIn}");
        notification.setWhichLevels("WARN");
        notifications.add(notification);
        alarmDefinition.setNotifications(notifications);

        alarmDefinition.setTimesInPeriod(3);
        alarmDefinition.setPeriodInMinutes(30);
        alarmDefinition.setName("test_c_test_original_alarm_" + System.nanoTime());
        alarmDefinition.setMetricId("3a77992d-d453-485d-a6dd-1675e946e51a");

        ResponseObject<AlarmDefinition> responseObject = alarmDefinitionService.save(alarmDefinition);
        System.out.println(JsonUtils.toJsonString(responseObject));
        Assertions.assertTrue("success".equals(responseObject.getStatus()));

    }


}