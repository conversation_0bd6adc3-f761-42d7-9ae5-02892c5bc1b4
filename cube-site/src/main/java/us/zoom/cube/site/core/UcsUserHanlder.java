package us.zoom.cube.site.core;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import us.zoom.infra.dao.model.UcsUserOrgInfoDO;
import us.zoom.infra.dao.service.UcsUserDAO;

import java.util.List;

/**
 * Author: charles.hu
 * Date: 2024/12/16
 * Description:
 */
@Component
@Slf4j
public class UcsUserHanlder {

    @Autowired
    private UcsUserDAO ucsUserDAO;

    @Transactional(rollbackFor = Exception.class)
    public void batchAddUcsUser(List<UcsUserOrgInfoDO> ucsUserOrgInfoDOList) {
        ucsUserDAO.batchInsert(ucsUserOrgInfoDOList);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateAddUcsUser(List<UcsUserOrgInfoDO> ucsUserOrgInfoDOList) {
        ucsUserDAO.batchUpdate(ucsUserOrgInfoDOList);
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteAddUcsUser(List<String> idList) {
        ucsUserDAO.batchDelete(idList);
    }

    public List<UcsUserOrgInfoDO> getAllUcsUser() {
        return ucsUserDAO.getAll();
    }
}
