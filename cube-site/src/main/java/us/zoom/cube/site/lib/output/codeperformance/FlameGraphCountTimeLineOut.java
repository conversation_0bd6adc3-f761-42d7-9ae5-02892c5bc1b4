package us.zoom.cube.site.lib.output.codeperformance;

import lombok.Data;

/**
 * @author: tobey.zhu
 * @date: /2023/07/20
 * @description:
 */
@Data
public class FlameGraphCountTimeLineOut {
    private Long time;
    private Double cpu;
    private Long sampleCount;

    @Data
    public static class CpuUs {
        private Long time;
        private Double cpu;

        public CpuUs(Long time, Double cpu) {
            this.time = time;
            this.cpu = cpu;
        }
    }

    public FlameGraphCountTimeLineOut(Long time, Double cpu, Long sampleCount) {
        this.time = time;
        this.cpu = cpu;
        this.sampleCount = sampleCount;
    }
}
