package us.zoom.cube.site.biz;

import org.reflections.Reflections;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import us.zoom.cube.site.infra.utils.IdUtils;
import us.zoom.infra.enums.MenuTypeEnum;
import us.zoom.infra.utils.MenuConstants;

import java.lang.reflect.Method;
import java.util.Set;

public class MenuScanTest {




    public static void main(String []gs) {

        //GetMapping
        String packageName = "us.zoom.cube.site.api";
        //String packageName = "us.zoom.cube.site.api.web.alarm.silence";
        Reflections f = new Reflections(packageName);
        // 
        Set<Class<?>> set = f.getTypesAnnotatedWith(RequestMapping.class);
        int index = 1;
        for (Class<?> c : set) {
            parseRequestMapping(index, c);
            index++;
        }

         set = f.getTypesAnnotatedWith(GetMapping.class);
        for (Class<?> c : set) {
            parseGetMapping(index, c);
            index++;
        }

    }

    private static void parseRequestMapping(int index, Class<?> c) {
        RequestMapping annotation = c.getAnnotation(RequestMapping.class);

        Method[] methods =  c.getMethods();
        String parentResId = c.getSimpleName().replace("Controller","")+annotation.value()[0].replaceAll("/","_");

        boolean hidden = true;
//        boolean hidden = annotation.value()[0].startsWith("/out/") ? true : false;
        System.out.println("insert into menu_item(id, name, resource_id, resource_url, hidden,menu_order,parent_res_id, type,create_time, modify_time) " +
                "values('"+ IdUtils.generateId()+"', '"+c.getSimpleName()+"', '"+parentResId+"', '"+annotation.value()[0] +"',"+hidden+", "+index+",'"+ MenuConstants.DEFAULT_MENU_ROOT+"',"+ MenuTypeEnum.Menu.getCode()+",now(), now());");
        for(int i =0;i<methods.length;i++){
            RequestMapping requestMappingsMetod = methods[i].getAnnotation(RequestMapping.class);
            if(null != requestMappingsMetod){
                String resUrl = annotation.value()[0] + (requestMappingsMetod.value()[0].startsWith("/") ? requestMappingsMetod.value()[0]:"/"+requestMappingsMetod.value()[0] );
                String resId = resUrl.replaceAll("/","_");
//                System.out.println(resUrl);
//
                System.out.println("insert into menu_item(id, name, resource_id, resource_url, hidden,menu_order,parent_res_id, type,create_time, modify_time) " +
                        "values('"+IdUtils.generateId()+"', '"+c.getSimpleName()+requestMappingsMetod.value()[0]+"', '"+resId+"', '"+resUrl+"',"+hidden+", "+i+",'"+parentResId+"',"+MenuTypeEnum.Menu.getCode()+",now(), now());");
            }

            try{
                GetMapping getMappingsMetod = methods[i].getAnnotation(GetMapping.class);
                if(null != getMappingsMetod){
                    String resUrl = (null == requestMappingsMetod) ?
                            annotation.value()[0] + getMappingsMetod.value()[0] :
                            annotation.value()[0]+ (requestMappingsMetod.value()[0].startsWith("/") ? requestMappingsMetod.value()[0]:"/"+requestMappingsMetod.value()[0] );
                    String resId = resUrl.replaceAll("/","_");
//                    System.out.println(resUrl);

                    System.out.println("insert into menu_item(id, name, resource_id, resource_url, hidden,menu_order,parent_res_id, type,create_time, modify_time) " +
                            "values('"+IdUtils.generateId()+"', '"+c.getSimpleName()+getMappingsMetod.value()[0]+"', '"+resId+"', '"+resUrl+"',"+hidden+", "+i+",'"+parentResId+"',"+MenuTypeEnum.Menu.getCode()+",now(), now());");
                }
            }catch (Exception e){
//                e.printStackTrace();
            }

        }
    }


    private static void parseGetMapping(int index, Class<?> c) {
        GetMapping annotation = c.getAnnotation(GetMapping.class);

        Method[] methods =  c.getMethods();
        String parentResId = c.getSimpleName().replace("Controller","")+annotation.value()[0].replaceAll("/","_");

        boolean hidden =true;
//        boolean hidden = annotation.value()[0].startsWith("/out/") ? true : false;
        System.out.println("insert into menu_item(id, name, resource_id, resource_url, hidden,menu_order,parent_res_id, type,create_time, modify_time) " +
                "values('"+ IdUtils.generateId()+"', '"+c.getSimpleName()+"', '"+parentResId+"', '"+annotation.value()[0] +"',"+hidden+", "+index+",'"+ MenuConstants.DEFAULT_MENU_ROOT+"',"+ MenuTypeEnum.Menu.getCode()+",now(), now());");
        for(int i =0;i<methods.length;i++){
            GetMapping requestMappingsMetod = methods[i].getAnnotation(GetMapping.class);
            if(null != requestMappingsMetod){
                String resUrl = annotation.value()[0] +requestMappingsMetod.value()[0];
                String resId = resUrl.replaceAll("/","_");
//                System.out.println(resUrl);

                System.out.println("insert into menu_item(id, name, resource_id, resource_url, hidden,menu_order,parent_res_id, type,create_time, modify_time) " +
                        "values('"+IdUtils.generateId()+"', '"+c.getSimpleName()+requestMappingsMetod.value()[0]+"', '"+resId+"', '"+resUrl+"',"+hidden+", "+i+",'"+parentResId+"',"+MenuTypeEnum.Menu.getCode()+",now(), now());");
            }

            GetMapping getMappingsMetod = methods[i].getAnnotation(GetMapping.class);
            if(null != getMappingsMetod){
                String resUrl = annotation.value()[0] +getMappingsMetod.value()[0];
                String resId = resUrl.replaceAll("/","_");
//                System.out.println(resUrl);

                System.out.println("insert into menu_item(id, name, resource_id, resource_url, hidden,menu_order,parent_res_id, type,create_time, modify_time) " +
                        "values('"+IdUtils.generateId()+"', '"+c.getSimpleName()+getMappingsMetod.value()[0]+"', '"+resId+"', '"+resUrl+"',"+hidden+", "+i+",'"+parentResId+"',"+MenuTypeEnum.Menu.getCode()+",now(), now());");
            }
        }
    }

}
