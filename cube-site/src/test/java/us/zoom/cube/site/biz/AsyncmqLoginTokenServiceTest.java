package us.zoom.cube.site.biz;


import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ActiveProfiles;
import us.zoom.cube.site.core.RsaService;
import us.zoom.cube.site.core.config.AsyncmqAdminInstance;
import us.zoom.cube.site.lib.ResponseObject;
import us.zoom.cube.site.lib.input.asyncmq.AsyncmqLoginInput;
import us.zoom.cube.lib.utils.JsonUtils;
import us.zoom.cube.site.lib.output.asyncmq.AsyncmqLoginResult;
import us.zoom.mq.common.Result;
import us.zoom.mq.common.response.LoginResult;

import java.util.Map;

@ActiveProfiles("perf_for_local")
public class AsyncmqLoginTokenServiceTest extends BaseTest {

    @Autowired
    private RsaService rsaService;

    @Autowired
    private AsyncmqLoginTokenService asyncmqLoginTokenService;

    @Test
    public void testAsyncmqLogin() throws Exception{
        Result<LoginResult> loginResultResult = AsyncmqAdminInstance.getInstance().login("8o-fNNAnQWizXtXmfl65eQ", rsaService.decrypt("Tn5XX2qomLfdM2osy0UF5M5S13p80DFPWg/HAH2rlswZ23csmtBE5k/ebEa5UtJJxELSQ58FMcXL61o26qocsndEHZvqf+nEs5/ofvOIV9xJU/MmT1RGYmxbASC/XvUXGVdHqyTqqyJunT5pGl1lKwdUzC22XTBsNCqhRT1ZpIM"));
        System.out.println(JsonUtils.toJsonString(loginResultResult));
        Assertions.assertTrue(loginResultResult.isSuccess());
    }

    @Test
    public void testGetLoginInfo() throws Exception {
        AsyncmqLoginInput asyncmqLoginInput = createInput("W9vL_f5MSl6pFaWFoRayCg");
        ResponseObject responseObject = asyncmqLoginTokenService.getToken(asyncmqLoginInput);
        System.out.println(JsonUtils.toJsonStringIgnoreExp(responseObject));
        Assertions.assertNotNull(responseObject);
        AsyncmqLoginResult result = (AsyncmqLoginResult)responseObject.getData();
        Assertions.assertNotNull(result.getToken());
        Assertions.assertTrue((result.getExpiration() > System.currentTimeMillis()), "expire time need greater then now");

        AsyncmqLoginInput asyncmqLoginInput1 =  createInput("KOSKOT_4SeuWqg8DffQYmg");
        ResponseObject responseObject1 = asyncmqLoginTokenService.getToken(asyncmqLoginInput1);
        System.out.println(JsonUtils.toJsonStringIgnoreExp(responseObject1));
        Assertions.assertNotNull(responseObject1);
        AsyncmqLoginResult result1 = (AsyncmqLoginResult)responseObject1.getData();
        Assertions.assertNotNull(result1.getToken());
        Assertions.assertTrue((result1.getExpiration() > System.currentTimeMillis()), "expire time need greater then now");

        AsyncmqLoginInput asyncmqLoginInput2 = createInput("aaaKOT_4SeuWqg8DffQaaa");
        ResponseObject responseObject2 = asyncmqLoginTokenService.getToken(asyncmqLoginInput2);
        System.out.println(JsonUtils.toJsonStringIgnoreExp(responseObject2));
        Assertions.assertNotNull(responseObject2);
        AsyncmqLoginResult result2 = (AsyncmqLoginResult)responseObject2.getData();
        Assertions.assertNotNull(result2.getToken());
        Assertions.assertTrue((result2.getExpiration() > System.currentTimeMillis()), "expire time need greater then now");
    }

    public AsyncmqLoginInput createInput(String accountId) {
        AsyncmqLoginInput asyncmqLoginInput = new AsyncmqLoginInput();
        asyncmqLoginInput.setAccountId(accountId);
        asyncmqLoginInput.setEmail("<EMAIL>");
        asyncmqLoginInput.setDc("us");
        asyncmqLoginInput.setBusiness("mmr");
        return asyncmqLoginInput;
    }
}