package us.zoom.cube.site.lib.output.expression;

import lombok.AccessLevel;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.util.List;

/**
 * @author: canyon.li
 * @date: 2024/12/03
 **/
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ExpressionParseOutput {

    /**
     * List of valid variables (variables included in the metrics)
     */
    List<String> validVariables;

    /**
     * fields & tags in metrics
     */
    List<String> metricsFieldAndTags;
}
