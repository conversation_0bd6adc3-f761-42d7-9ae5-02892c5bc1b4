package us.zoom.cube.site.lib;

import jakarta.validation.constraints.NotBlank;

public class IdPara extends BasePara {

    public IdPara() {
    }

    public IdPara(String id) {
        this.id = id;
    }

    @NotBlank
    private String id;


    public boolean isShowNotDerived() {
        return showNotDerived;
    }

    public void setShowNotDerived(boolean showDerived) {
        this.showNotDerived = showDerived;
    }

    private boolean showNotDerived = false;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
}
