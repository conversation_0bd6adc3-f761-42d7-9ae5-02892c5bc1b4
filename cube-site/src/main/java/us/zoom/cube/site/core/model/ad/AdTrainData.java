package us.zoom.cube.site.core.model.ad;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;
import us.zoom.cube.site.lib.BasePara;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024-01-04 13:47
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AdTrainData extends BasePara {
    private String id;
    private String adConfigId;
    private String metricsId;
    private Integer adTrainStatus;
    private String adTrainInfo;

    public void check() {
        Assert.isTrue(StringUtils.isNotBlank(adConfigId), "adConfigId can not be empty");
    }

    public void checkAll() {
        Assert.isTrue(StringUtils.isNotBlank(adConfigId), "adConfigId can not be empty");
        Assert.isTrue(StringUtils.isNotBlank(metricsId), "metricsId can not be empty");
        Assert.isTrue(!Objects.isNull(adTrainStatus), "adTrainStatus can not be empty");
    }
}
