package us.zoom.cube.site.lib.output.config.metrics;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AccessLevel;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.apache.commons.lang3.StringUtils;
import us.zoom.cube.lib.integrations.MetricsFieldTypeEnum;

import java.util.List;

import static us.zoom.cube.lib.hub.HubConstants.FIELD_STRING_TYPE;
import static us.zoom.cube.lib.hub.HubConstants.MEASURE_TAG;


@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class MetricsVarAndFunc {
    List<VariableCategory> variables;
    List<FunctionCategory> functions;

    @Data
    public static class VariableCategory {
        private String categoryName;
        private String categoryDesc;
        private List<Variable> list;
    }

    @Data
    @NoArgsConstructor
    public static class Variable {
        private String name;
        private String description; // For System and AI Variables
        private String type;

        public Variable(MetricsTagsAndFieldsOutput metricsTagsAndFieldsOutput) {
            this.name = metricsTagsAndFieldsOutput.getName();
            if (StringUtils.equals(metricsTagsAndFieldsOutput.getMetaType(), MEASURE_TAG)) {
                this.type = FIELD_STRING_TYPE;
            } else {
                MetricsFieldTypeEnum fieldTypeEnum = MetricsFieldTypeEnum.fromValue(Integer.valueOf(metricsTagsAndFieldsOutput.getFieldType()));
                this.type = fieldTypeEnum == null ? null : fieldTypeEnum.name();
            }
            this.description = metricsTagsAndFieldsOutput.getDescription();
        }

    }

    @Data
    public static class FunctionCategory {
        private String categoryName;
        private String categoryDesc;
        private List<Function> list;
    }

    @Data
    public static class Function {
        private String name;
        private String description;
    }

}
