package us.zoom.cube.site.core.model.ad;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import us.zoom.cube.site.lib.output.tag.TagInfoOut;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-07-06 10:50
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AdCfgDataOut {
    private String id;
    private String adName;
    private String metricsId;
    private String metricsName;
    private Integer aggPeriod;
    private Integer metricsType;
    private String fieldName;
    private Boolean adStatus;
    private String tenantId;
    private String tenantName;
    private String anomalyDirectionType;
    private String thresValueFilterUp;
    private String thresValueFilterDown;
    private String rollingAlertSensitivityAll;
    private String rollingAlertSensitivityNum;
    private String detectionInterval;
    private Date modifyTime;
    private Date createTime;
    private Integer adTrainStatus;
    private String creator;
    private String editor;
    private Boolean fillEmptyAsZero;
    private String dataSourceType;
    private Boolean piiAuth;
    private List<AdTagCfgData> adTagCfgDataList;
    private List<TagInfoOut> labelInfoList;
}