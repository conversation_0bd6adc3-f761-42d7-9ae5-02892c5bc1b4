package us.zoom.cube.site.biz;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import us.zoom.cube.site.infra.utils.IdUtils;
import us.zoom.cube.site.lib.IdListPara;
import us.zoom.cube.site.lib.IdPara;
import us.zoom.cube.site.lib.ResponseObject;
import us.zoom.cube.site.lib.input.BatchCardInput;
import us.zoom.cube.site.lib.input.CardInput;
import us.zoom.cube.site.lib.input.DashInput;
import us.zoom.cube.site.lib.output.PageResult;
import us.zoom.cube.site.lib.output.dash.DashHasUserRelaOut;
import us.zoom.cube.site.lib.query.*;
import us.zoom.infra.dao.model.CardDO;
import us.zoom.infra.enums.CardTypeEnum;
import us.zoom.infra.enums.DashUserRelaTypeEnum;
import us.zoom.infra.enums.DashVisibilityEnum;
import us.zoom.infra.model.dash.ParaCfg;
import us.zoom.infra.thread.ThreadLocalStore;
import us.zoom.cube.lib.utils.JsonUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class DashCardServiceTest extends  BaseTest {


    @Autowired
    private  DashService dashService;

    @Autowired
    private CardService cardService;

    String desc="hello,desc";
    String name="testDash";
    String clusterId="clusterId";
    String va="VA";
    String sharedOwner="82b4e894-edc7-47b8-ae99-58b6cbf58326,2565ab89-d345-49e4-9548-9066da3001db";


    String desc2="hello,desc";
    String name2="testDash";
    String sharedOwner2="['1']";
    String user1="82b4e894-edc7-47b8-ae99-58b6cbf58326";
    String user2="2565ab89-d345-49e4-9548-9066da3001db";
    String clusterId2="clusterId";
    String va2="VA2";
    String tenantId="0ab0fa30-cbdc-456f-89c3-dc5188620edb";


    @Test
    public void test4(){
        ThreadLocalStore.setUserInfoLocal(user1);
        ThreadLocalStore.setTenantInfoLocal(tenantId);
        IdPara idPara=new IdPara();
        idPara.setId("66306126-135d-4681-b62e-4f931c1790ef");
        dashService.getDashAndCards(idPara);
    }

//    @Test
    public void test3(){
        ThreadLocalStore.setUserInfoLocal(user1);
        ThreadLocalStore.setTenantInfoLocal(tenantId);
        CardInput cardInput= mockCard("5c5756ce-4519-4d70-a9a7-8cbb88a0d491","testCard12",CardTypeEnum.USER);
        Map<String,Map<String,String>> configs=new HashMap<>();
        Map<String,String> db=new HashMap<>();
        db.put("db","iweb");
        configs.put("queryConfig",db);
        cardInput.setConfigs(configs);
        BatchCardInput batchCardInput=new BatchCardInput();
        batchCardInput.setDashId("5c5756ce-4519-4d70-a9a7-8cbb88a0d491");
        batchCardInput.setCardInputs(new ArrayList<>());
        batchCardInput.getCardInputs().add(cardInput);
        cardService.batchAdd(batchCardInput);
    }
    @Test
    public void test7(){
        ThreadLocalStore.setUserInfoLocal(user1);
        ThreadLocalStore.setTenantInfoLocal(tenantId);
        String string = "{\"dashId\":\"6154ddca-c8fe-40aa-8750-03b23512dbcd\",\"cardInputs\":[{\"name\":\"ike_test_batch_Add\",\"query\":\"SELECT mean('AT.avg') AS 'mean_AT.avg' FROM 'ds_web'.'autogen'.'dynamo' WHERE time > :dashboardTime: AND time < :upperDashboardTime: GROUP BY time(:interval:)\",\"type\":2,\"description\":\"ike_test_batch_Add\",\"configs\":{\"type\":\"line\",\"gridPos\":{\"x\":0,\"y\":null,\"w\":6,\"h\":8}}}]}";
        BatchCardInput batchCardInput = JsonUtils.toObject(string,BatchCardInput.class);
        Assertions.assertTrue(!StringUtils.isEmpty((String)cardService.batchJsonAdd(batchCardInput).getData()));
    }


    @Test
    public void test8(){
        ThreadLocalStore.setUserInfoLocal(user1);
        ThreadLocalStore.setTenantInfoLocal(tenantId);
        DashInput dashInput= mockDashInput();
        dashInput.setId("e516df78-d08d-4c3d-9f98-a41e055cc2a0");
        dashInput.setTags("123,456");
        Assertions.assertTrue(!CollectionUtils.isEmpty((List)dashService.getRelated(dashInput).getData()));
    }

    @Test
    public void test10(){
        ThreadLocalStore.setUserInfoLocal(user1);
        ThreadLocalStore.setTenantInfoLocal(tenantId);
        /*DashInput dashInput= mockDashInput();
        dashInput.setId("2ff369da-8a73-4a7b-98d0-f210e4e1c9d7");
        dashInput.setTags("test1,test2");
        dashInput.setService("testSer");*/
        String test = "{\"name\":\"ike_test_perf_copy\",\"serviceCount\":1,\"tags\":null,\"id\":\"e516df78-d08d-4c3d-9f98-a41e055cc2a0\"}";
        String test2 = "{\"name\":\"ike_test\",\"serviceCount\":1,\"id\":\"0dc3c2dc-0c5c-444d-ae79-9f15d1dd2257\"}";
        String test3 = "{\"name\":\"test0001ss_copy\",\"serviceCount\":0,\"service\":\"infra_monitor_cube_site\",\"tags\":\"\",\"id\":\"e8d918a0-f720-4349-b024-2b4dca1192df\"}";
        DashInput dashInput = JsonUtils.toObject(test3,DashInput.class);
        Assertions.assertTrue(!StringUtils.isEmpty((String)dashService.copy(dashInput).getData()));
    }

    @Test
    public void test11(){
        ThreadLocalStore.setUserInfoLocal(user1);
        ThreadLocalStore.setTenantInfoLocal(tenantId);
        BatchCardInput batchCardInput= new BatchCardInput();
        batchCardInput.setDashId("2ff369da-8a73-4a7b-98d0-f210e4e1c9d7");
        List<CardInput> list = new ArrayList<>();
        CardInput cardInput = new CardInput();
        cardInput.setDashId("2ff369da-8a73-4a7b-98d0-f210e4e1c9d7");
        cardInput.setName("testname");
        cardInput.setType(1);
        cardInput.setQuery("SELECT mean(\"AT.avg\") AS \"mean_AT.avg\" FROM \"ds_web\".\"autogen\".\"dynamo\" WHERE time > :dashboardTime: AND time < :upperDashboardTime: GROUP BY time(:interval:)");
        cardInput.setDescription("test");
        list.add(cardInput);
        CardInput cardInput2 = new CardInput();
        cardInput2.setDashId("2ff369da-8a73-4a7b-98d0-f210e4e1c9d7");
        cardInput2.setName("testname");
        cardInput2.setType(1);
        cardInput2.setQuery("SELECT mean(\"AT.avg\") AS \"mean_AT.avg\" FROM \"ds_web\".\"autogen\".\"dynamo\" WHERE time > :dashboardTime: AND time < :upperDashboardTime: GROUP BY time(:interval:)");
        cardInput2.setDescription("test");
        list.add(cardInput2);
        batchCardInput.setCardInputs(list);
        Assertions.assertTrue(!StringUtils.isEmpty((String)cardService.batchJsonAdd(batchCardInput).getData()));
    }

    @Test
    public void test12(){
        ThreadLocalStore.setUserInfoLocal(user1);
        ThreadLocalStore.setTenantInfoLocal(tenantId);
        DashInput dashInput= mockDashInput();
        dashInput.setId("2ff369da-8a73-4a7b-98d0-f210e4e1c9d7");
        dashInput.setTags("test1,test2");
        dashInput.setService("testSer1");
        dashInput.setServiceCount(2);
        Assertions.assertTrue(!StringUtils.isEmpty((String)dashService.edit(dashInput).getData()));
    }

    @Test
    public void test13(){
        ThreadLocalStore.setUserInfoLocal(user1);
        ThreadLocalStore.setTenantInfoLocal(tenantId);
        //DashInput dashInput= mockDashInput();
        String test = "{\"name\":\"ike_single_service\",\"serviceCount\":0,\"service\":\"command-center\",\"description\":\"ike_single_service_test\",\"tags\":\"ike_test,ike_test_02\",\"visibility\":1,\"sharedOwners\":\"\"}";
        DashInput dashInput = JsonUtils.toObject(test,DashInput.class);
        Assertions.assertTrue(!StringUtils.isEmpty((String)dashService.add(dashInput).getData()));
    }

    @Test
    public void test14(){
        ThreadLocalStore.setUserInfoLocal(user1);
        ThreadLocalStore.setTenantInfoLocal(tenantId);
        DashInput dashInput= mockDashInput();
         dashInput.setId("e516df78-d08d-4c3d-9f98-a41e055cc2a0");
        dashService.getDashJson(dashInput);
        Assertions.assertTrue(!StringUtils.isEmpty((String)dashService.getDashJson(dashInput).getData()));
    }

    @Test
    public void test18(){
        ThreadLocalStore.setUserInfoLocal(user1);
        ThreadLocalStore.setTenantInfoLocal(tenantId);
        DashInput dashInput= mockDashInput();
        dashInput.setId("2ff369da-8a73-4a7b-98d0-f210e4e1c9d7");
        String test="{\"name\":\"ike_test_perf1\",\"visibility\":\"publicDash\",\"configs\":{\"layout\":[{\"i\":\"a4727be9-dc33-4f0e-8ee5-cdb07e233caa\",\"x\":0,\"y\":32,\"h\":8,\"w\":6},{\"i\":\"bbf9becf-a831-4edd-8061-645954695d06\",\"x\":6,\"y\":16,\"h\":8,\"w\":6},{\"i\":\"9c857315-11e9-4331-afc0-3dd9e3fdcc53\",\"x\":6,\"y\":24,\"h\":8,\"w\":6},{\"i\":\"fd2d4d8a-5235-45d7-8471-1092514fda7f\",\"x\":6,\"y\":0,\"h\":8,\"w\":6},{\"i\":\"1c07e430-9da9-4620-9d3c-48da86500d25\",\"x\":0,\"y\":40,\"h\":8,\"w\":6},{\"i\":\"edb30729-5531-4be8-aa78-19d766d1c080\",\"x\":0,\"y\":16,\"h\":8,\"w\":6},{\"i\":\"da1643fb-3ae6-411c-b2f4-e265ca5b36fb\",\"x\":0,\"y\":0,\"h\":8,\"w\":6},{\"i\":\"5daddddf-86c9-499e-b314-82d92886409d\",\"x\":0,\"y\":8,\"h\":8,\"w\":6},{\"i\":\"0d74795f-9ecb-41af-8dbb-dd2ded109897\",\"x\":0,\"y\":24,\"h\":8,\"w\":6},{\"i\":\"2aa1722a-c676-4ce4-857b-b06ab25b222c\",\"x\":6,\"y\":8,\"h\":8,\"w\":6},{\"i\":\"eadc727e-d779-4907-89d3-8570143b949f\",\"x\":6,\"y\":32,\"h\":8,\"w\":6}],\"variables\":[{\"type\":\"values\",\"name\":\":ipa:\",\"order\":0,\"tenant\":\"infra_monitor_cube_site\",\"measurement\":\"cpu\",\"keys\":\"ip\",\"options\":\"cpu\",\"defaultValue\":\"\"},{\"type\":\"custom\",\"name\":\":ipb:\",\"order\":1,\"options\":\"ds_web,ds\",\"multi\":true,\"defaultValue\":[\"ds_web\",\"ds\"]},{\"type\":\"service\",\"name\":\":serviceName:\",\"order\":1},{\"type\":\"text\",\"name\":\":ipg:\",\"order\":2,\"defaultValue\":\"123\"},{\"name\":\":ipc:\",\"type\":\"text\",\"order\":2},{\"name\":\":ipd:\",\"type\":\"custom\",\"options\":\"***********,***********\",\"multi\":true,\"defaultValue\":[],\"order\":3}],\"defaultTimeRange\":\"1h\",\"autoRefreshOptions\":[\"5s\",\"10s\",\"15s\",\"30s\",\"60s\",\"4s\",\"6s\",\"8s\",\"9s\"],\"links\":[{\"type\":\"dashboard\",\"tags\":[\"123\"],\"dropdown\":true,\"blank\":true,\"timeRange\":true,\"variable\":true,\"id\":\"1652339846225_0\"},{\"type\":\"link\",\"blank\":true,\"timeRange\":false,\"variable\":true,\"title\":\"title\",\"url\":\"https://cube-perf.zoomdev.us:8443/dashboards/e516df78-d08d-4c3d-9f98-a41e055cc2a0/settings/links?Key33=Value33\",\"id\":\"1652339862732_1\"},{\"type\":\"dashboard\",\"tags\":[\"456\"],\"id\":\"1652343964911_2\"}]},\"description\":\"ike_test_perf1231232\",\"type\":\"multipleService\",\"service\":null,\"tags\":\"\",\"card\":[{\"userId\":\"82b4e894-edc7-47b8-ae99-58b6cbf58326\",\"operId\":null,\"accessToken\":null,\"timeZone\":null,\"tenantId\":\"0ab0fa30-cbdc-456f-89c3-dc5188620edb\",\"id\":\"a4727be9-dc33-4f0e-8ee5-cdb07e233caa\",\"dashId\":null,\"name\":\"ike_test\",\"type\":2,\"configs\":{\"queryConfig\":{\"groupByTime\":\"auto\",\"compareTime\":\"none\",\"visualType\":\"line\",\"measurement\":\"cpu\",\"fieldsAlias\":{},\"rawText\":null,\"db\":\"ds_web\",\"rp\":\"autogen\",\"tagValues\":{},\"fields\":[\"id\"],\"functionSelectors\":{\"id\":{\"mean\":[]}},\"groupByTagKey\":[],\"tags\":{\"tagValues\":{},\"groupByTagKey\":[]},\"timeRange\":{\"type\":\"relative\",\"value\":\"30m\"},\"visualConfigs\":{}},\"type\":\"line\",\"gridPos\":{\"x\":0,\"y\":48,\"h\":8,\"w\":6}},\"description\":\"ike_test\",\"query\":null,\"queryCfg\":null},{\"userId\":\"82b4e894-edc7-47b8-ae99-58b6cbf58326\",\"operId\":null,\"accessToken\":null,\"timeZone\":null,\"tenantId\":\"0ab0fa30-cbdc-456f-89c3-dc5188620edb\",\"id\":\"bbf9becf-a831-4edd-8061-645954695d06\",\"dashId\":null,\"name\":\"ike_test_time\",\"type\":2,\"configs\":{\"queryConfig\":{\"groupByTime\":\"auto\",\"compareTime\":\"none\",\"visualType\":\"bar\",\"measurement\":\"cpu\",\"fieldsAlias\":{},\"rawText\":null,\"db\":\"ds_web\",\"rp\":\"autogen\",\"tagValues\":{},\"fields\":[\"id\",\"hi\",\"hi.avg\",\"hi.count\",\"hi.sum\",\"ni\",\"si\"],\"functionSelectors\":{\"id\":{\"mean\":[]},\"hi\":{\"mean\":[]},\"hi.avg\":{\"mean\":[]},\"hi.count\":{\"mean\":[]},\"hi.sum\":{\"mean\":[]},\"ni\":{\"mean\":[]},\"si\":{\"mean\":[]}},\"groupByTagKey\":[],\"tags\":{\"tagValues\":{},\"groupByTagKey\":[]},\"timeRange\":{\"type\":\"relative\",\"value\":\"30m\"},\"visualConfigs\":{\"eChartTheme\":\"default\",\"yAxisMin\":\"auto\",\"yAxisMax\":\"auto\",\"yAxisFormat\":\"raw\",\"decimalPlaces\":\"\",\"timeFormat\":\"YYYY-MM-DD HH:mm:ss\",\"omitHead\":false,\"legendHeight\":\"auto\"," +
                "\"legendMode\":\"table\",\"legendPlacement\":\"right\",\"thresholds\":[{\"value\":10,\"color\":\"#0e72ed\"},{\"value\":20,\"color\":\"#e3ab1b\"},{\"value\":30,\"color\":\"#ae2b2b\"},{\"value\":40,\"color\":\"#767676\"},{\"value\":50,\"color\":\"#26b857\"}],\"cardLinks\":[{\"title\":\"__all_variables\",\"link\":\"https://cube-perf.zoomdev.us:8443/dashboards?${__all_variables}\",\"blank\":true},{\"title\":\"__time_range\",\"link\":\"https://cube-perf.zoomdev.us:8443/dashboards?${__time_range}\",\"blank\":true},{\"title\":\"${__from}&to=${__to}\",\"link\":\"https://cube-perf.zoomdev.us:8443/dashboards?from=${__from}&to=${__to}\",\"blank\":true},{\"title\":\"variable name\",\"link\":\"https://cube-perf.zoomdev.us:8443/dashboards?ip=${ipa}\",\"blank\":true}],\"thresholdMode\":\"abs\",\"baseColor\":\"#73bf69\"}},\"gridPos\":{\"x\":6,\"y\":24,\"h\":8,\"w\":6},\"type\":\"bar\"},\"description\":\"ike_test_time\",\"query\":null,\"queryCfg\":null},{\"userId\":\"82b4e894-edc7-47b8-ae99-58b6cbf58326\",\"operId\":null,\"accessToken\":null,\"timeZone\":null,\"tenantId\":\"0ab0fa30-cbdc-456f-89c3-dc5188620edb\",\"id\":\"9c857315-11e9-4331-afc0-3dd9e3fdcc53\",\"dashId\":null,\"name\":\"card_batch_add01\",\"type\":2,\"configs\":{\"queryConfig\":{\"database\":\"infra_monitor_cube_site\",\"rawText\":null,\"areTagsAccepted\":false,\"range\":{\"upper\":\"\",\"lower\":\"now() - 30m\"},\"shifts\":null,\"retentionPolicy\":\"autogen\",\"groupBy\":{\"time\":\"1m\",\"tags\":[]},\"fields\":[{\"value\":\"mean\",\"type\":\"func\",\"alias\":\"mean_hi\",\"args\":[{\"value\":\"hi\",\"type\":\"field\",\"alias\":\"\"}]}],\"fill\":\"null\",\"measurement\":\"cpu\",\"tags\":{}},\"batch\":true,\"type\":\"line\",\"gridPos\":{\"x\":6,\"y\":32,\"h\":8,\"w\":6}},\"description\":\"demo card\",\"query\":null,\"queryCfg\":null},{\"userId\":\"82b4e894-edc7-47b8-ae99-58b6cbf58326\",\"operId\":null,\"accessToken\":null,\"timeZone\":null,\"tenantId\":\"0ab0fa30-cbdc-456f-89c3-dc5188620edb\",\"id\":\"fd2d4d8a-5235-45d7-8471-1092514fda7f\",\"dashId\":null,\"name\":\"ike_test_stats\",\"type\":2,\"configs\":{\"queryConfig\":{\"groupByTime\":\"auto\",\"compareTime\":\"none\",\"visualType\":\"stats\",\"measurement\":\"cpu\",\"fieldsAlias\":{},\"rawText\":null,\"db\":\"infra_monitor_cube_site\",\"rp\":\"autogen\",\"tagValues\":{},\"fields\":[\"id\"],\"functionSelectors\":{\"hi\":{\"mean\":[]},\"id\":{\"mean\":[]}},\"groupByTagKey\":[],\"tags\":{\"tagValues\":{},\"groupByTagKey\":[]},\"timeRange\":{\"type\":\"relative\",\"value\":\"30m\"},\"visualConfigs\":{\"format\":\"raw\",\"timeFormat\":\"YYYY-MM-DD HH:mm:ss\",\"prefix\":\"prefix\",\"suffix\":\"req/s\",\"decimalPlaces\":\"auto\",\"thresholds\":[{\"value\":0.5,\"color\":\"#26b857\"},{\"value\":2,\"color\":\"#0e72ed\"},{\"value\":3,\"color\":\"#e3ab1b\"},{\"value\":4,\"color\":\"#ae2b2b\"}],\"displayField\":\"mean_id\",\"calculation\":0,\"thresholdMode\":\"abs\",\"baseColor\":\"#73bf69\"}},\"gridPos\":{\"w\":8,\"h\":8,\"x\":0,\"y\":0},\"type\":\"stats\"},\"description\":\"ike_test_stats\",\"query\":null,\"queryCfg\":null},{\"userId\":\"82b4e894-edc7-47b8-ae99-58b6cbf58326\",\"operId\":null,\"accessToken\":null,\"timeZone\":null,\"tenantId\":\"0ab0fa30-cbdc-456f-89c3-dc5188620edb\",\"id\":\"1c07e430-9da9-4620-9d3c-48da86500d25\",\"dashId\":null,\"name\":\"ike_Test\",\"type\":2,\"configs\":{\"queryConfig\":{\"groupByTime\":\"auto\",\"compareTime\":\"none\",\"visualType\":\"stats\",\"measurement\":\"cpu\",\"fieldsAlias\":{},\"rawText\":null,\"db\":\"ds_cube_hub\",\"rp\":\"autogen\",\"tagValues\":{},\"fields\":[\"id\"],\"functionSelectors\":{\"id\":{\"mean\":[]}},\"groupByTagKey\":[],\"tags\":{\"tagValues\":{},\"groupByTagKey\":[]},\"timeRange\":{\"type\":\"relative\",\"value\":\"30m\"},\"visualConfigs\":{\"format\":\"raw\",\"timeFormat\":\"YYYY-MM-DD HH:mm:ss\",\"decimalPlaces\":\"auto\",\"thresholds\":[{\"value\":30,\"color\":\"#26b857\"},{\"value\":70,\"color\":\"#e3ab1b\"},{\"value\":100}],\"displayField\":\"mean_id\",\"calculation\":0,\"thresholdMode\":\"abs\",\"baseColor\":\"#73bf69\"}},\"gridPos\":{\"x\":0,\"y\":48,\"h\":8,\"w\":6},\"type\":\"stats\"},\"description\":\"ike_Test\",\"query\":null,\"queryCfg\":null},{\"userId\":\"82b4e894-edc7-47b8-ae99-58b6cbf58326\"," +
                "\"operId\":null,\"accessToken\":null,\"timeZone\":null,\"tenantId\":\"0ab0fa30-cbdc-456f-89c3-dc5188620edb\",\"id\":\"edb30729-5531-4be8-aa78-19d766d1c080\",\"dashId\":null,\"name\":\"ike_test_Card\",\"type\":2,\"configs\":{\"queryConfig\":{\"groupByTime\":\"auto\",\"compareTime\":\"none\",\"visualType\":\"gauge\",\"measurement\":\"cpu\",\"fieldsAlias\":{},\"rawText\":null,\"db\":\"infra_monitor_cube_site\",\"rp\":\"autogen\",\"tagValues\":{},\"fields\":[\"id\"],\"functionSelectors\":{\"hi\":{\"mean\":[]},\"id\":{\"mean\":[]}},\"groupByTagKey\":[],\"tags\":{\"tagValues\":{},\"groupByTagKey\":[]},\"timeRange\":{\"type\":\"relative\",\"value\":\"30m\"},\"visualConfigs\":{\"thresholds\":[{\"value\":200,\"color\":\"#26b857\"},{\"value\":300,\"color\":\"#0e72ed\"},{\"value\":400,\"color\":\"#e3ab1b\"},{\"value\":500,\"color\":\"#ae2b2b\"},{\"value\":600}],\"displayField\":\"mean_hi\",\"calculation\":0,\"thresholdMode\":\"abs\",\"baseColor\":\"#73bf69\"}},\"gridPos\":{\"x\":0,\"y\":16,\"h\":8,\"w\":6},\"type\":\"gauge\"},\"description\":\"ike_test_Card\",\"query\":null,\"queryCfg\":null},{\"userId\":\"82b4e894-edc7-47b8-ae99-58b6cbf58326\",\"operId\":null,\"accessToken\":null,\"timeZone\":null,\"tenantId\":\"0ab0fa30-cbdc-456f-89c3-dc5188620edb\",\"id\":\"da1643fb-3ae6-411c-b2f4-e265ca5b36fb\",\"dashId\":null,\"name\":\"ike_test\",\"type\":2,\"configs\":{\"queryConfig\":{\"groupByTime\":\"auto\",\"compareTime\":\"none\",\"visualType\":\"bar\",\"measurement\":null,\"fieldsAlias\":{},\"rawText\":\" SELECT mean(\\\"hi\\\") AS \\\"mean_hi\\\", mean(\\\"id\\\") AS \\\"mean_id\\\", mean(\\\"ni\\\") AS \\\"mean_ni\\\" FROM \\\"infra_monitor_cube_site\\\".\\\"autogen\\\".\\\"cpu\\\" WHERE time > :dashboardTime: AND time < :upperDashboardTime: AND ( \\\"ip\\\"=':ipa:' ) AND usePrecent=60 GROUP BY time(:interval:) \",\"db\":\"infra_monitor_cube_site\",\"rp\":\"autogen\",\"tagValues\":{},\"fields\":[],\"functionSelectors\":{},\"groupByTagKey\":[],\"tags\":{\"tagValues\":{},\"groupByTagKey\":[]},\"timeRange\":{\"type\":\"relative\",\"value\":\"30m\"},\"visualConfigs\":{}},\"gridPos\":{\"w\":8,\"h\":8,\"x\":0,\"y\":0,\"static\":true},\"type\":\"bar\"},\"description\":\"ike_test\",\"query\":null,\"queryCfg\":null},{\"userId\":\"82b4e894-edc7-47b8-ae99-58b6cbf58326\",\"operId\":null,\"accessToken\":null,\"timeZone\":null,\"tenantId\":\"0ab0fa30-cbdc-456f-89c3-dc5188620edb\",\"id\":\"5daddddf-86c9-499e-b314-82d92886409d\",\"dashId\":null,\"name\":\"ike_test_raw_sql\",\"type\":2,\"configs\":{\"queryConfig\":{\"groupByTime\":\"auto\",\"compareTime\":\"none\",\"visualType\":\"line\",\"measurement\":null,\"fieldsAlias\":{},\"rawText\":\" SELECT mean(\\\"hi\\\") AS \\\"mean_hi\\\", mean(\\\"id\\\") AS \\\"mean_id\\\" FROM \\\"infra_monitor_cube_site\\\".\\\"autogen\\\".\\\"cpu\\\" WHERE time > :dashboardTime: AND time < :upperDashboardTime: AND ( \\\"ip\\\"=':ipd:' ) AND usedPercent > 60  GROUP BY time(:interval:) \",\"db\":\"infra_monitor_cube_site\",\"rp\":\"autogen\",\"tagValues\":{},\"fields\":[],\"functionSelectors\":{},\"groupByTagKey\":[],\"tags\":{\"tagValues\":{},\"groupByTagKey\":[]},\"timeRange\":{\"type\":\"relative\",\"value\":\"30m\"},\"visualConfigs\":{}},\"gridPos\":{\"w\":8,\"h\":8,\"x\":0,\"y\":0},\"type\":\"line\"},\"description\":\"ike_test_raw_sql\",\"query\":null,\"queryCfg\":null},{\"userId\":\"82b4e894-edc7-47b8-ae99-58b6cbf58326\",\"operId\":null,\"accessToken\":null,\"timeZone\":null,\"tenantId\":\"0ab0fa30-cbdc-456f-89c3-dc5188620edb\",\"id\":\"0d74795f-9ecb-41af-8dbb-dd2ded109897\",\"dashId\":null,\"name\":\"ike_test_card_links\",\"type\":2,\"configs\":{\"queryConfig\":{\"groupByTime\":\"auto\",\"compareTime\":\"none\",\"visualType\":\"stats\",\"measurement\":\"cpu\",\"fieldsAlias\":{},\"rawText\":null,\"db\":\"ds_web\",\"rp\":\"autogen\",\"tagValues\":{},\"fields\":[\"id\",\"hi.count\",\"hi.sum\"],\"functionSelectors\":{\"id\":{\"mean\":[]},\"hi.sum\":{\"mean\":[]},\"hi.count\":{\"mean\":[]},\"hi.avg\":{\"mean\":[]},\"hi\":{\"mean\":[]},\"ni\":{\"mean\":[]}},\"groupByTagKey\":[],\"tags\":{\"tagValues\":{},\"groupByTagKey\":[]},\"timeRange\":{\"type\":\"relative\",\"value\":\"30m\"},\"visualConfigs\":{\"format\":\"raw\",\"timeFormat\":\"YYYY-MM-DD HH:mm:ss\"," +
                "\"decimalPlaces\":\"auto\",\"thresholds\":[{\"value\":50}],\"displayField\":\"mean_id\",\"cardLinks\":[{\"title\":\"dashboard\",\"link\":\"https://cube-perf.zoomdev.us:8443/dashboards/e516df78-d08d-4c3d-9f98-a41e055cc2a0/card/add?${__all_variables}\",\"blank\":true}],\"calculation\":0,\"thresholdMode\":\"abs\",\"baseColor\":\"#73bf69\"}},\"gridPos\":{\"x\":0,\"y\":32,\"h\":8,\"w\":6},\"type\":\"stats\"},\"description\":\"ike_test_card_links\",\"query\":null,\"queryCfg\":null},{\"userId\":\"82b4e894-edc7-47b8-ae99-58b6cbf58326\",\"operId\":null,\"accessToken\":null,\"timeZone\":null,\"tenantId\":\"0ab0fa30-cbdc-456f-89c3-dc5188620edb\",\"id\":\"2aa1722a-c676-4ce4-857b-b06ab25b222c\",\"dashId\":null,\"name\":\"ike_test_pre_defined_test\",\"type\":2,\"configs\":{\"queryConfig\":{\"groupByTime\":\"auto\",\"compareTime\":\"none\",\"visualType\":\"line\",\"measurement\":\"cpu\",\"fieldsAlias\":{},\"rawText\":null,\"db\":\"infra_monitor_cube_site\",\"rp\":\"autogen\",\"tagValues\":{\"ip\":[\":ipa:\"]},\"fields\":[\"id\"],\"functionSelectors\":{\"hi\":{\"mean\":[]},\"ni\":{\"mean\":[]},\"id\":{\"mean\":[]}},\"groupByTagKey\":[],\"tags\":{\"tagValues\":{},\"groupByTagKey\":[]},\"timeRange\":{\"type\":\"relative\",\"value\":\"30m\"},\"visualConfigs\":{\"eChartTheme\":\"default\",\"yAxisMin\":\"auto\",\"yAxisMax\":\"auto\",\"yAxisFormat\":\"raw\",\"decimalPlaces\":\"auto\",\"timeFormat\":\"YYYY-MM-DD HH:mm:ss\",\"omitHead\":false,\"legendHeight\":800,\"legendMode\":\"list\",\"legendPlacement\":\"bottom\",\"thresholdMode\":\"abs\",\"baseColor\":\"#73bf69\"}},\"gridPos\":{\"x\":0,\"y\":32,\"w\":6,\"h\":8},\"type\":\"line\"},\"description\":\"ike_test_pre_defined_test\",\"query\":null,\"queryCfg\":null},{\"userId\":\"82b4e894-edc7-47b8-ae99-58b6cbf58326\",\"operId\":null,\"accessToken\":null,\"timeZone\":null,\"tenantId\":\"0ab0fa30-cbdc-456f-89c3-dc5188620edb\",\"id\":\"eadc727e-d779-4907-89d3-8570143b949f\",\"dashId\":null,\"name\":\"card_batch_add02\",\"type\":2,\"configs\":{\"queryConfig\":{\"database\":\"infra_monitor_cube_site\",\"rawText\":null,\"areTagsAccepted\":false,\"range\":{\"upper\":\"\",\"lower\":\"now() - 30m\"},\"shifts\":null,\"retentionPolicy\":\"autogen\",\"groupBy\":{\"time\":\"1m\",\"tags\":[]},\"fields\":[{\"value\":\"mean\",\"type\":\"func\",\"alias\":\"mean_hi\",\"args\":[{\"value\":\"hi\",\"type\":\"field\",\"alias\":\"\"}]}],\"fill\":\"null\",\"measurement\":\"cpu\",\"tags\":{}},\"batch\":true,\"type\":\"line\",\"gridPos\":{\"x\":6,\"y\":32,\"w\":6,\"h\":8}},\"description\":\"demo card\",\"query\":null,\"queryCfg\":null}]}";
        dashService.setDashJson(test);
        //Assertions.assertTrue(!StringUtils.isEmpty((String)dashService.setDashJson(dashInput).getData()));
    }




    public void test2(){
        ThreadLocalStore.setUserInfoLocal(user1);
        ThreadLocalStore.setTenantInfoLocal(tenantId);

        PageQuery<SearchHaveQuery> pageQuery2=new PageQuery<>();
        pageQuery2.setPageIndex(1);
        pageQuery2.setPageSize(100);
        SearchHaveQuery nameQuery2=new SearchHaveQuery();
        nameQuery2.setName(name);
        pageQuery2.setQueryPara(nameQuery2);

        PageQuery<NameQuery> pageQuery=new PageQuery<>();
        pageQuery.setPageIndex(1);
        pageQuery.setPageSize(100);
        NameQuery nameQuery=new NameQuery();
        nameQuery.setName(name);
        pageQuery.setQueryPara(nameQuery);
        ResponseObject<PageResult<DashHasUserRelaOut>> responseObject=dashService.searchHave(pageQuery2);
        DashInput dashInput= mockDashInput();

        IdPara idPara=new IdPara();
        if(responseObject == null || responseObject.getData().getItems() == null || responseObject.getData().getItems().size() <= 0){
            ResponseObject   tmp =dashService.add(dashInput);
            String id=(String)tmp.getData();
            dashInput.setId(id);
            idPara.setId(id);
        }else{
            DashHasUserRelaOut relaDO=  responseObject.getData().getItems().get(0);
            dashInput.setId(relaDO.getId());
            dashInput.setName(name);
            dashInput.setSharedOwners(sharedOwner);
            dashInput.setDescription(desc);
            //dashInput.setVisibility(DashVisibilityEnum.PUBLIC.getCode());
            dashService.edit(dashInput);
            dashInput.setConfigs(new HashMap());
            dashService.changeConfigs(dashInput);
            idPara.setId(relaDO.getId());

        }
        nameQuery.setName("sys");
        idPara.setId("retr");
        dashService.getDashAndCards(idPara);
        ResponseObject res= cardService.searchCard(pageQuery);
        res.getData();
    }


    public void test(){

        ThreadLocalStore.setUserInfoLocal(user1);
        ThreadLocalStore.setTenantInfoLocal(tenantId);

        PageQuery<SearchPublicDashQuery> pageQuery3=new PageQuery<>();
        pageQuery3.setPageIndex(1);
        pageQuery3.setPageSize(100);
        SearchPublicDashQuery nameQuery3=new SearchPublicDashQuery();
        nameQuery3.setName(name);
        pageQuery3.setQueryPara(nameQuery3);

        PageQuery<SearchCanFromPublicQuery> pageQuery2=new PageQuery<>();
        pageQuery2.setPageIndex(1);
        pageQuery2.setPageSize(100);
        SearchCanFromPublicQuery nameQuery2=new SearchCanFromPublicQuery();
        nameQuery2.setName(name);
        pageQuery2.setQueryPara(nameQuery2);

        PageQuery<SearchHaveQuery> pageQuery=new PageQuery<>();
        pageQuery.setPageIndex(1);
        pageQuery.setPageSize(100);
        SearchHaveQuery nameQuery=new SearchHaveQuery();
        nameQuery.setName(name);
        pageQuery.setQueryPara(nameQuery);
        ResponseObject<PageResult<DashHasUserRelaOut>> responseObject=dashService.searchHave(pageQuery);
        DashInput dashInput= mockDashInput();

        if(responseObject == null || responseObject.getData().getItems() == null || responseObject.getData().getItems().size() <= 0){
            ResponseObject   tmp =dashService.add(dashInput);
            String id=(String)tmp.getData();
            dashInput.setId(id);
        }else{
            DashHasUserRelaOut relaDO=  responseObject.getData().getItems().get(0);
            dashInput.setId(relaDO.getId());
            dashInput.setName(name);
            dashInput.setSharedOwners(sharedOwner);
            dashInput.setDescription(desc);
            //dashInput.setVisibility(DashVisibilityEnum.PUBLIC.getCode());
            dashService.edit(dashInput);
            dashInput.setConfigs(new HashMap());
            dashService.changeConfigs(dashInput);
        }
        //dashService.setDefault(dashInput);
        responseObject=dashService.searchHave(pageQuery);
        if(responseObject == null && responseObject.getData().getItems() == null && responseObject.getData().getItems().size() <= 0){
           throw new RuntimeException("error");
        }

        DashHasUserRelaOut dashHasUserRelaDO=responseObject.getData().getItems().get(0);
        Assertions.assertTrue(dashHasUserRelaDO.getName().equals(name));
        Assertions.assertTrue(dashHasUserRelaDO.getConfigs().equals(JsonUtils.toJsonString(dashInput.getConfigs())));
        Assertions.assertTrue(dashHasUserRelaDO.getDescription().equals(desc));
        Assertions.assertTrue(dashHasUserRelaDO.getVisibility().equals(DashVisibilityEnum.PUBLIC.getCode()));
        Assertions.assertTrue(dashHasUserRelaDO.getSharedOwners().equals(sharedOwner));
        Assertions.assertTrue(dashHasUserRelaDO.getRelaType().equals(DashUserRelaTypeEnum.OWNER.getCode()));


        //add card
        PageQuery<NameQuery> pageQuery21=new PageQuery<>();
        pageQuery21.setPageIndex(1);
        pageQuery21.setPageSize(100);
        NameQuery nameQuery21=new NameQuery();
        nameQuery21.setName(name);
        pageQuery21.setQueryPara(nameQuery21);
        testCard(pageQuery21, nameQuery, responseObject, dashHasUserRelaDO);

        IdPara idPara =new IdPara();
        nameQuery.setName("");
        //update bse info
        dashInput.setId(dashHasUserRelaDO.getId());
        dashInput.setName(name2);
        dashInput.setSharedOwners(sharedOwner2);
        dashInput.setDescription(desc2);
        dashService.edit(dashInput);
        idPara.setId(dashInput.getId());

        nameQuery.setName("");
        pageQuery.setQueryPara(nameQuery);
        pageQuery.setPageSize(100);
        ResponseObject res = dashService.searchCanUseFromPublic(pageQuery2);
        ThreadLocalStore.setUserInfoLocal(user2);

        //search from public
        nameQuery.setName(name2);
        pageQuery.setQueryPara(nameQuery);
        res  = dashService.searchCanUseFromPublic(pageQuery2);
        List<DashHasUserRelaOut> dashDOS= ((PageResult<DashHasUserRelaOut>) res.getData()).getItems();
        Assertions.assertNotNull(dashDOS);
        Assertions.assertTrue(dashDOS.get(0).getName().equals(name2));

        dashService.add(dashInput);
        //update visibility
        ThreadLocalStore.setUserInfoLocal(user1);
        //dashInput.setVisibility(DashVisibilityEnum.PRIVATE.getCode());
        dashService.edit(dashInput);
        ThreadLocalStore.setUserInfoLocal(user2);

        //delete the rela
        dashService.delDashUserRela(idPara);


        //change configs
        ThreadLocalStore.setUserInfoLocal(user1);
        Map cfg=mockCfg(clusterId2,va2);
        dashInput.setConfigs(cfg);
        dashService.changeConfigs(dashInput);

        //delete dash
        dashService.delDashUserRela(idPara);
        res=   dashService.searchHave(pageQuery);
        Assertions.assertTrue(res.getData() == null || ((PageResult<DashHasUserRelaOut>) res.getData()).getItems() ==null ||((PageResult<DashHasUserRelaOut>) res.getData()).getItems().size()==0 );


    }

    private IdPara testCard(PageQuery<NameQuery> pageQuery, NameQuery nameQuery, ResponseObject<PageResult<DashHasUserRelaOut>> responseObject, DashHasUserRelaOut dashHasUserRelaDO) {
        nameQuery.setName("sys");
        ResponseObject resObj=   cardService.searchCard(pageQuery);
        IdPara idPara=new IdPara();
        idPara.setId(dashHasUserRelaDO.getId());
        resObj=  cardService.listCardByDash(idPara);

        if(resObj == null || resObj.getData() == null ||((List)resObj.getData()).size()<=0 ){
            cardService.add(mockCard(dashHasUserRelaDO.getId(),"cpu", CardTypeEnum.USER));
            cardService.add(mockCard(dashHasUserRelaDO.getId(),"mem", CardTypeEnum.USER));
            cardService.add(mockCard(dashHasUserRelaDO.getId(),"net", CardTypeEnum.USER));
        }

        resObj=  cardService.listCardByDash(idPara);
        List<CardDO> cardDOS= (List<CardDO>) resObj.getData();
        CardDO card=cardDOS.get(0);
        //edit card
        card.setName("user1");
        card.setDescription(cardDesc2);
        CardInput cardInput=new CardInput();
        BeanUtils.copyProperties(card,cardInput);
        cardService.edit(cardInput);
        idPara.setId(card.getId());
        resObj=cardService.getById(idPara);

        card= (CardDO) resObj.getData();

        Assertions.assertTrue(card.getName().equals("user1"));
        Assertions.assertTrue(card.getDescription().equals(cardDesc2));

        cardInput.setConfigs(mockCardConfg());
        cardService.editConfigs(cardInput);
        resObj=cardService.getById(idPara);

        card= (CardDO) resObj.getData();
        Assertions.assertTrue(card.getConfigs().equals(JsonUtils.toJsonString(cardInput.getConfigs())));
        List<String> ids = new ArrayList();
        IdListPara para = new IdListPara();
        para.setIds(ids);
        cardDOS.forEach(cardDO -> {
            ids.add(cardDO.getId());
        });
        cardService.delete(para);

        return idPara;
    }

    private String cardDesc1="cardDesc1";
    private String cardDesc2="cardDesc2";


    private String cardCfg1="cardCfg1";
    private String cardCfg2="cardCfg2";
    private CardInput mockCard(String dashId,String name, CardTypeEnum cardTypeEnum) {
        CardInput cardInput=new CardInput();
        cardInput.setDashId(dashId);
        cardInput.setConfigs(mockCardConfg());
        cardInput.setDescription(cardDesc1);
        cardInput.setName(name);
        cardInput.setType(cardTypeEnum.getCode());
        cardInput.setTenantId(tenantId);
        return  cardInput;
    }



    private Map mockCardConfg() {
        Map cardCfg=new HashMap();
        cardCfg.put("code",cardCfg1);
        return cardCfg;
    }


    private DashInput mockDashInput() {
        DashInput dashInput=new DashInput();

        Assertions.assertNotNull(dashInput.getUserId());
        Assertions.assertNotNull(dashInput.getTenantId());
        dashInput.setId(IdUtils.generateId());
        dashInput.setDescription(desc);
        //dashInput.setVisibility(DashVisibilityEnum.PUBLIC.getCode());
        dashInput.setName(name);
        dashInput.setSharedOwners(sharedOwner);
        Map dashCfg = mockCfg(clusterId,va);
        dashInput.setConfigs(dashCfg);
        return dashInput;
    }

    private Map mockCfg(String p1,String v1) {
        Map dashCfg=new HashMap();
        List<ParaCfg> paras=new ArrayList<>();
        ParaCfg paraCfg=new ParaCfg();
        paraCfg.setName(p1);
        paraCfg.setDefaultValue(v1);
        paras.add(paraCfg);
        dashCfg.put("paras",paras);
        return dashCfg;
    }

}
