package us.zoom.cube.site.infra.e2e;

import com.alibaba.fastjson.JSON;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.lib.common.ProbeTaskStatusEnum;
import us.zoom.cube.lib.common.ProbeTypeEnum;
import us.zoom.cube.lib.probe.*;
import us.zoom.cube.lib.utils.JsonUtils;
import us.zoom.cube.site.infra.utils.IdUtils;
import us.zoom.infra.asyncmq.CubeConsumer;
import us.zoom.infra.dao.model.ProbeTaskSyncDO;
import us.zoom.infra.dao.service.ProbeTaskSyncDAO;
import us.zoom.mq.common.entity.TaskContext;
import us.zoom.mq.common.entity.TaskEntity;

import java.time.Instant;
import java.util.List;
import java.util.Map;


/**
 * @description:
 * @author: <EMAIL>
 * @date: 2024-11-19 18:17
 **/
@Service
@Slf4j
@AllArgsConstructor
public class ProbeMqConsumer extends CubeConsumer {
    private static final String SPLIT = ":";

    private final ProbeTaskSyncDAO probeTaskSyncDAO;

    private static final String PROBE_TYPE_FIELD_NAME = "probeType";


    @Override
    public boolean onMessage(List<TaskEntity<byte[]>> taskEntities) {
        try {
            if (CollectionUtils.isEmpty(taskEntities)) {
                return false;
            }
            for (TaskEntity<byte[]> t : taskEntities) {
                handleTask(t);
            }
        } catch (Exception e) {
            log.error("taskEntities: {},ProbeMqConsumer error:{} ", JSON.toJSONString(taskEntities), e.getMessage(), e);
        }
        return false;
    }

    private void handleTask(TaskEntity<byte[]> t) {
        try {
            String message = transferMessageToString(t.getPayloadType(), t.getPayload());
            SchedulerJobInfo schedulerJobInfo = JSON.parseObject(message, SchedulerJobInfo.class);
            SchedulerJobCfg schedulerJobCfg = JSON.parseObject(schedulerJobInfo.getConfig(), SchedulerJobCfg.class);
            if (!(StringUtils.equals(ProbeTaskStatusEnum.ENABLE.getDesc(), schedulerJobCfg.getAggregationStatus())
                    && StringUtils.equals(ProbeTaskStatusEnum.ENABLE.getDesc(), schedulerJobCfg.getStatus()))) {
                return;
            }
            TaskContext taskContext = t.getTaskContext();
            boolean isTimeout = isTimeout(taskContext.getProduceTime(), schedulerJobCfg.getIntervalMs());
            if (isTimeout) {
                return;
            }
            Map[] steps = JSON.parseObject(schedulerJobCfg.getSteps(), Map[].class);
            int timeout = 0;
            for (int i = 0; i < steps.length; i++) {
                Map step = steps[i];
                String probeType = (String) step.get(PROBE_TYPE_FIELD_NAME);
                Class<? extends BaseProbeStepCfg> clazz = getStepClass(probeType);
                if (clazz == null) {
                    throw new IllegalArgumentException("illegal probeType: " + probeType);
                }
                BaseProbeStepCfg baseProbeStepCfg = JSON.parseObject(JsonUtils.toJsonString(step), clazz);
                timeout += baseProbeStepCfg.getTimeout();
            }
            //save mysql
            String globalTaskUuid = taskContext.getTaskId() + SPLIT + taskContext.getProduceTime();
            ProbeTaskSyncDO taskSyncDO = ProbeTaskSyncDO.builder()
                    .id(IdUtils.generateId())
                    .globalTaskUuid(globalTaskUuid)
                    .timeout(timeout)
                    .produceTime(taskContext.getProduceTime())
                    .probeGroups(schedulerJobCfg.getProbePointGroupIds())
                    .status(ProbeTaskSyncStatus.pending.name())
                    .interval(schedulerJobCfg.getIntervalMs())
                    .serviceName(schedulerJobCfg.getServiceName())
                    .probeTaskName(schedulerJobCfg.getProbeTaskName())
                    .build();
            //Determine if it exists, insert if it doesn't
            int existsCount = probeTaskSyncDAO.existsCount(globalTaskUuid);
            if (existsCount == 0) {
                probeTaskSyncDAO.add(taskSyncDO);
            }
        } catch (Exception e) {
            log.error("taskEntity: {},ProbeMqConsumer error:{} ", JSON.toJSONString(t), e.getMessage(), e);
        }
    }

    private boolean isTimeout(long produceTime, long intervalMs) {
        return (Instant.now().toEpochMilli() - produceTime) >= intervalMs;
    }


    private Class<? extends BaseProbeStepCfg> getStepClass(String probeType) {
        if (ProbeTypeEnum.HTTP.getType().equalsIgnoreCase(probeType)) {
            return HttpProbeStepCfg.class;
        }
        if (ProbeTypeEnum.TCP.getType().equalsIgnoreCase(probeType)) {
            return TcpProbeStepCfg.class;
        }
        if (ProbeTypeEnum.GROOVY.getType().equalsIgnoreCase(probeType)) {
            return GroovyProbeStepConfig.class;
        }
        if (ProbeTypeEnum.EXECUTABLE_PROGRAM.getType().equalsIgnoreCase(probeType)) {
            return ExecutableProgramProbeStepCfg.class;
        }
        return null;
    }


}
