package us.zoom.cube.site.core.parser.process.core.processor;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProcessorResp {
    private boolean isContinue;
    private String failMessage;
    private String id;
    private String type;
    private Integer order;
    private String name;
    private Map<String, Object> inputMap;
    private Map<String, Object> outputMap;

    public ProcessorResp(boolean isContinue,Map<String, Object> inputMap) {
        this.isContinue =isContinue;
        this.inputMap = inputMap;
    }
}
