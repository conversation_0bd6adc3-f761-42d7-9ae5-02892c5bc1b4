package us.zoom.cube.site.lib.output;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: <PERSON><PERSON> Ding
 * @date: 2023/8/2 13:07
 * @desc:
 */
@Data
public class PipelineDeleteCheckOut {

    private String id;

    private ToDelete toDelete;

    public PipelineDeleteCheckOut() {
    }

    public PipelineDeleteCheckOut(String id) {
        this(id, new ArrayList<>(), new ArrayList<>());
    }

    public PipelineDeleteCheckOut(String id, List<String> metrics, List<String> alarms) {
        this.id = id;
        this.toDelete = new ToDelete();
        this.toDelete.setMetrics(metrics);
        this.toDelete.setAlarms(alarms);
    }

    @Data
    private class ToDelete{

        private List<String> metrics = new ArrayList<>();

        private List<String> alarms = new ArrayList<>();

    }

}
