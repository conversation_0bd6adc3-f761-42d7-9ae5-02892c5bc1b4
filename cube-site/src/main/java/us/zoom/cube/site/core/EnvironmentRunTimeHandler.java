package us.zoom.cube.site.core;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import us.zoom.infra.dao.model.EnvironmentRunTimeDO;
import us.zoom.infra.dao.service.EnvironmentRunTimeDAO;


import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-08-10 13:38
 */
@Component
public class EnvironmentRunTimeHandler {

    @Autowired
    private EnvironmentRunTimeDAO environmentRunTimeDAO;

    @Transactional(rollbackFor = Exception.class)
    public void editEnvironmentRunTime(EnvironmentRunTimeDO environmentRunTimeDO){
        environmentRunTimeDAO.edit(environmentRunTimeDO);
    }

    public List<EnvironmentRunTimeDO> searchEnvironmentRunTime(){
        return environmentRunTimeDAO.searchAll();
    }

}
