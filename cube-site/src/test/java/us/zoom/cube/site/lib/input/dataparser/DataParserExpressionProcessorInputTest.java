package us.zoom.cube.site.lib.input.dataparser;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import us.zoom.cube.lib.config.hub.enums.FieldTypeEnum;
import us.zoom.cube.lib.config.hub.processor.ExpressionEntryCfg;
import us.zoom.cube.lib.config.hub.processor.ExpressionSchema;
import us.zoom.cube.lib.utils.JsonUtils;

import java.util.Arrays;
import java.util.List;

/**
 * @Author: <EMAIL>
 * @ModuleOwner: <EMAIL>
 * @Date: 04/10/2025 16:13
 * @Description:
 */
public class DataParserExpressionProcessorInputTest {
    @Test
    public void test() {
        DataParserExpressionProcessorInput input = new DataParserExpressionProcessorInput();
        input.setSchema(null);
        RuntimeException err2 = Assertions.assertThrows(RuntimeException.class, () -> {
            input.check();
        });
        Assertions.assertEquals("Expression config is null!", err2.getMessage());

        List<ExpressionEntryCfg> entries = Arrays.asList(new ExpressionEntryCfg("host", "", FieldTypeEnum.string),
                new ExpressionEntryCfg("name", "", FieldTypeEnum.string));
        ExpressionSchema schema = new ExpressionSchema(entries);
        input.setSchema(schema);
        input.check();
    }

    @Test
    public void testJson() {
        List<ExpressionEntryCfg> entries = Arrays.asList(new ExpressionEntryCfg("host", "", FieldTypeEnum.string),
                new ExpressionEntryCfg("name", "", FieldTypeEnum.string));
        ExpressionSchema schema = new ExpressionSchema(entries);
        System.out.println(JsonUtils.toJsonString(schema));
    }
}
