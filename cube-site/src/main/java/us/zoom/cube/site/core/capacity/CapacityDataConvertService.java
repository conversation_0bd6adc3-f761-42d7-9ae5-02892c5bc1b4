package us.zoom.cube.site.core.capacity;

import lombok.extern.slf4j.Slf4j;
import org.influxdb.dto.QueryResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.lib.hub.MetricFieldCfg;
import us.zoom.cube.lib.hub.MetricsCfg;
import us.zoom.cube.lib.integrations.Metrics;
import us.zoom.cube.lib.integrations.MetricsField;
import us.zoom.cube.lib.integrations.MetricsFieldTypeEnum;
import us.zoom.cube.site.core.config.ClickhouseHandlerFactory;
import us.zoom.cube.site.core.model.common.CapacityModel;
import us.zoom.cube.site.lib.CapacityDashCfg;
import us.zoom.infra.clickhouse.ClickhouseHandler;
import us.zoom.infra.clickhouse.ClickhouseSerieList;
import us.zoom.infra.clickhouse.ClickhouseSqlUtil;
import us.zoom.infra.clickhouse.InfluxResultAdapter;
import us.zoom.infra.enums.CapacityQueryEnum;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022-05-24 10:48
 */
@Component
@Slf4j
public class CapacityDataConvertService {

    @Autowired
    private ClickhouseHandlerFactory clickhouseHandlerFactory;

    /**
     * getData
     * @param oriSql
     * @return
     */
    public QueryResult getData(String oriSql, String serviceName) {
        long startTime = System.currentTimeMillis();
        String adjustSql = adjustSql(oriSql);
        List<Map<String, Object>> query = clickhouseHandlerFactory.get().query(serviceName,adjustSql);
        QueryResult data = InfluxResultAdapter.wrapToInfluxLike(query, oriSql, false);
        long endTime = System.currentTimeMillis();
        log.info("CapacityService_clickhouse cost = {} ms,serviceName: {} , sql = {}", endTime - startTime,serviceName,adjustSql);
        return data;
    }

    private String adjustSql(String oriSql) {
        String adjustSql = ClickhouseSqlUtil.removeGroupByIfNotAggrFuncs(oriSql);
        return adjustSql;
    }

    /**
     * transferQueryResult2Metrics
     * @param queryResult
     * @return
     */
    public List<Metrics> transferQueryResult2Metrics(QueryResult queryResult) {

        List<Metrics> rawData = new ArrayList<>();
        List<QueryResult.Result> results = queryResult.getResults();
        for (QueryResult.Result result : results) {
            if (CollectionUtils.isEmpty(result.getSeries())) {
                continue;
            }
            for (QueryResult.Series series : result.getSeries()) {
                for (int i = 0; i < series.getValues().size(); i++) {
                    Map<String, Object> record = new HashMap<>(series.getColumns().size());
                    for (int j = 0; j < series.getColumns().size(); j++) {
                        record.put(series.getColumns().get(j), series.getValues().get(i).get(j));
                        record.putAll(series.getTags());
                    }

                    Metrics metrics = transfer2Metrics(series, record);
                    if (!CollectionUtils.isEmpty(metrics.getFields())) {
                        rawData.add(metrics);
                    }
                }
            }
        }
        return rawData;
    }

    /**
     * transfer2Metrics
     * @param series
     * @param record
     * @return
     */
    public Metrics transfer2Metrics(QueryResult.Series series, Map<String, Object> record) {
        MetricsCfg metricsCfg = getCapacityMerics();
        Map<String, String> tagMap = new HashMap<>();
        if (null != metricsCfg && !org.springframework.util.StringUtils.isEmpty(metricsCfg.getTagNames())) {
            tagMap = Arrays.stream(metricsCfg.getTagNames().split(",")).collect(Collectors.toMap(String::toString, e -> e));
        }

        Metrics m = new Metrics();
        Map<String, String> tags = new HashMap<>();
        List<MetricsField> mfList = new ArrayList<>(record.size() - 1);
        for (Map.Entry<String, Object> entry : record.entrySet()) {
            if (tagMap.containsKey(entry.getKey())) {
                tags.put(entry.getKey(), entry.getValue().toString());
            } else {
                MetricsField mf = new MetricsField();
                mf.setFieldName(entry.getKey());
                mf.setFieldValue(entry.getValue());
                if (entry.getValue() instanceof String) {
                    mf.setFieldType(MetricsFieldTypeEnum.string);
                } else {
                    mf.setFieldType(MetricsFieldTypeEnum.number);
                }
                mfList.add(mf);
            }
        }
        m.setTags(tags);
        m.setFields(mfList);

        return m;
    }

    /**
     * getCapacityMerics
     * @return
     */
    private MetricsCfg getCapacityMerics() {
        CapacityModel capacityModel = new CapacityModel();
        MetricsCfg metricsCfg = new MetricsCfg();
        metricsCfg.setTagNames(capacityModel.getTags());
        List<MetricFieldCfg> metricFieldCfgList = new ArrayList<>();
        MetricFieldCfg metricFieldCfg = new MetricFieldCfg();
        metricFieldCfg.setFieldName(capacityModel.getOneAReduceCount());
        metricFieldCfg.setFieldType(1);
        MetricFieldCfg metricFieldCfg2 = new MetricFieldCfg();
        metricFieldCfg2.setFieldName(capacityModel.getTwoAReduceCount());
        metricFieldCfg2.setFieldType(1);
        MetricFieldCfg metricFieldCfg3 = new MetricFieldCfg();
        metricFieldCfg3.setFieldName(capacityModel.getThreeAReduceCount());
        metricFieldCfg3.setFieldType(1);
        MetricFieldCfg metricFieldCfg4 = new MetricFieldCfg();
        metricFieldCfg4.setFieldName(capacityModel.getMax_us());
        metricFieldCfg4.setFieldType(1);
        MetricFieldCfg metricFieldCfg5 = new MetricFieldCfg();
        metricFieldCfg5.setFieldName(capacityModel.getServerCount());
        metricFieldCfg5.setFieldType(1);
        metricFieldCfgList.add(metricFieldCfg);
        metricFieldCfgList.add(metricFieldCfg2);
        metricFieldCfgList.add(metricFieldCfg3);
        metricFieldCfgList.add(metricFieldCfg4);
        metricFieldCfgList.add(metricFieldCfg5);
        metricsCfg.setMetricFieldCfgList(metricFieldCfgList);
        return metricsCfg;
    }

}
