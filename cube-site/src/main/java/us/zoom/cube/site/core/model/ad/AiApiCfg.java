package us.zoom.cube.site.core.model.ad;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-02-02 10:10
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AiApiCfg {
    private String id;
    private String adName;
    private String fieldName;
    private Boolean adStatus;
    private String anomalyDirectionType;
    private String thresholdFilterUp;
    private String thresholdFilterDown;
    private String rollingAlertSensitivityAll;
    private String rollingAlertSensitivityNum;
    private String detectionInterval;
    private Boolean fillEmptyAsZero;
    List<AdTagCfgData> adTagCfgDataList;
}
