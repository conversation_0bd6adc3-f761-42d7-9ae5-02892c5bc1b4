package us.zoom.cube.site.infra.e2e;

import org.apache.commons.collections4.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import us.zoom.cloud.secrets.rotate.handler.CurrentVersionKVSecretHandler;
import us.zoom.cloud.secrets.rotate.vo.Status;
import us.zoom.cloud.secrets.vo.KVSecret;
import us.zoom.mq.client.AsyncMQ;
import us.zoom.mq.client.exception.AsyncMQClientException;

import java.util.Map;

/**
 * @author: eason.jia
 * @date: 2023/8/1
 */
public class ProbeCurrentVersionKVSecretHandler implements CurrentVersionKVSecretHandler {

    private static final Logger logger = LoggerFactory.getLogger(ProbeCurrentVersionKVSecretHandler.class);

    private static final String ASYNC_MQ_PASSWORD_KEY = "async.mq.password";
    private AsyncMQ asyncMQ;
    private String username;

    public ProbeCurrentVersionKVSecretHandler(AsyncMQ asyncMQ, String username) {
        this.asyncMQ = asyncMQ;
        this.username = username;
    }

    @Override
    public Map<String, Status> preVerify(Map<String, KVSecret> kvSecretMap) {
        if (MapUtils.isEmpty(kvSecretMap) || !kvSecretMap.containsKey(ASYNC_MQ_PASSWORD_KEY)) {
            logger.error("mq rotate secret map is empty or the map not contains {} key", ASYNC_MQ_PASSWORD_KEY);
            return Status.fail(kvSecretMap, "mq rotate secret map is empty or the map not contains specified key");
        }
        KVSecret secret = kvSecretMap.get(ASYNC_MQ_PASSWORD_KEY);
        try {
            if (asyncMQ.verifyCredential(username, secret.getValue())) {
                logger.info("mq preVerify success, secret name = {}, version={}", secret.getSecretName(), secret.getVersionId());
                return Status.success(kvSecretMap);
            }
        } catch (AsyncMQClientException e) {
            logger.error("mq verify credential failed {}, secret name = {}, version={}", e.getMessage(), secret.getSecretName(), secret.getVersionId());
            return Status.fail(kvSecretMap, e.getMessage());
        }
        logger.error("mq preVerify failed, secret name = {}, version={}", secret.getSecretName(), secret.getVersionId());
        return Status.fail(kvSecretMap, "mq preVerify failed");
    }

    @Override
    public Map<String, Status> handle(Map<String, KVSecret> kvSecretMap) {
        if (MapUtils.isEmpty(kvSecretMap) || !kvSecretMap.containsKey(ASYNC_MQ_PASSWORD_KEY)) {
            logger.error("mq rotate secret map is empty or the map not contains {} key", ASYNC_MQ_PASSWORD_KEY);
            return Status.fail(kvSecretMap, "mq rotate secret map is empty or the map not contains specified key");
        }
        KVSecret secret = kvSecretMap.get(ASYNC_MQ_PASSWORD_KEY);
        try {
            if (asyncMQ.updatePassword(secret.getValue())) {
                logger.info("mq handle success, secret name = {}, version={}", secret.getSecretName(), secret.getVersionId());
                return Status.success(kvSecretMap);
            }
        } catch (AsyncMQClientException e) {
            logger.error("mq update password failed {}, secret name = {}, version={}", e.getMessage(), secret.getSecretName(), secret.getVersionId());
            return Status.fail(kvSecretMap, e.getMessage());
        }
        logger.error("mq handler failed, secret name = {}, version={}", secret.getSecretName(), secret.getVersionId());
        return Status.fail(kvSecretMap, "mq handler failed");
    }
}