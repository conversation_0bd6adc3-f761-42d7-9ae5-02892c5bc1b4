package us.zoom.cube.site.core;

import com.google.common.collect.Lists;
import org.apache.ibatis.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import us.zoom.infra.dao.model.AggregationPercentileItemDO;
import us.zoom.infra.dao.model.MetricsFieldDO;
import us.zoom.infra.dao.service.AggregationPercentileItemDAO;
import us.zoom.infra.dao.service.MetricsFieldDAO;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2020/7/19 10:20 AM
 */
@Service
public class MetricsFieldHandler {

    @Autowired
    private MetricsFieldDAO metricsFieldDAO;

    public int insertMetricsField(MetricsFieldDO metricsFieldDO){
        return metricsFieldDAO.insertMetricsField(metricsFieldDO);
    }


    public int batchInsertMetricsField(List<MetricsFieldDO> metricsFieldDOList){
        if(CollectionUtils.isEmpty(metricsFieldDOList)){
            return 0;
        }
        return metricsFieldDAO.batchInsertMetricsField(metricsFieldDOList);
    }


    public List<MetricsFieldDO> listFieldByMetricsIds(List<String> metricsIds){
        if(CollectionUtils.isEmpty(metricsIds)){
            return Lists.newArrayList();
        }
        return metricsFieldDAO.listFieldByMetricsIds(metricsIds);
    }


    public void deleteByMetricsId(String metricsId){
        metricsFieldDAO.deleteByMetricsId(metricsId);
    }

    public void deleteByMetricsIds(List<String> metricsIds){
        metricsFieldDAO.deleteByMetricsIds(metricsIds);
    }

    public MetricsFieldDO getMetricsFieldByMetricsIdAndFieldName(String metricsId, String fieldName){
        return metricsFieldDAO.getMetricsFieldByMetricsIdAndFieldName(metricsId,fieldName);
    }

    public List<MetricsFieldDO> listAll(){
        return metricsFieldDAO.listAll();
    }


    public void deleteByFieldIds(List<String> fieldIds){
        if(!CollectionUtils.isEmpty(fieldIds)){
            metricsFieldDAO.deleteByFieldIds(fieldIds);
        }
    }

    public int updateMetricsField(MetricsFieldDO metricsFieldDO){
        return metricsFieldDAO.updateMetricsField(metricsFieldDO);
    }

    public int batchUpdateMetricsField(List<MetricsFieldDO> metricsFieldDOList){
        if(CollectionUtils.isEmpty(metricsFieldDOList)){
            return 0;
        }
        return metricsFieldDAO.batchUpdateMetricsField(metricsFieldDOList);
    }

    public void batchUpdataMetricsFieldDescription(List<MetricsFieldDO> metricsFieldDOList) {
        if (CollectionUtils.isEmpty(metricsFieldDOList)) {
            return;
        }
        metricsFieldDAO.batchUpdateMetricsFieldDescription(metricsFieldDOList);
    }

}
