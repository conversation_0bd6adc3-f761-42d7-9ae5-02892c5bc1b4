package us.zoom.cube.site.core;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.lib.common.TaskTypeEnum;
import us.zoom.infra.dao.model.CubeServerDO;
import us.zoom.infra.dao.service.CubeServerDAO;
import us.zoom.infra.utils.IpUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * @Author: toby.fang
 */
@Component
public class CubeServerHandler {

    @Autowired
    private CubeServerDAO cubeServerDAO;

    public List<CubeServerDO> listAll() {
        List<CubeServerDO> CubeServerDOS = cubeServerDAO.listAll();
        return CubeServerDOS;
    }

    public List<CubeServerDO> listByIpAndType(String ip, String type){
        return cubeServerDAO.listByIpAndType(ip, type);
    }

    public List<CubeServerDO> findByParam(String ip, String hostName, String type, String unitTagId, String clusterId,
                                          String regionId, Integer status, String env, int pageIndex, int pageSize) {
        List<CubeServerDO> CubeServerDOS = cubeServerDAO.findByParamLike(ip, hostName, type, unitTagId, clusterId, regionId,
                status, env, pageSize * (pageIndex - 1), pageSize);
        return CubeServerDOS;
    }

    public Integer getCountByParam(String ip, String hostName, String type, String unitTagId, String clusterId, String regionId, Integer status, String env) {
        return cubeServerDAO.getCountByParam(ip, hostName, type, unitTagId, regionId, clusterId, status, env);
    }

    public void add(CubeServerDO cubeServerDO) {
        cubeServerDAO.add(cubeServerDO);
    }

    public void edit(CubeServerDO cubeServerDO) {
        cubeServerDAO.edit(cubeServerDO);
    }

    public CubeServerDO getById(String id) {
        return cubeServerDAO.getById(id);
    }

    public int getCountByIpNotId(String ip, String id) {
        return cubeServerDAO.getCountByIpNotId(ip, id);
    }

    public void editCubeServerStatus(int status, String id) {
        cubeServerDAO.editStatus(status, id);
    }

    public List<CubeServerDO> getByIds(Set<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }
        return cubeServerDAO.getByIds(ids);
    }

    public void deleteById(String id) {
        cubeServerDAO.deleteById(id);
    }
}
