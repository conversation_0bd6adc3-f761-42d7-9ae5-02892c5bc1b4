package us.zoom.cube.site.core.auth;

import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import us.zoom.cube.site.core.DashTreeHandler;
import us.zoom.cube.site.core.TenantHandler;
import us.zoom.cube.site.core.UserHandler;
import us.zoom.cube.site.core.usergroup.UserGroupHandler;
import us.zoom.cube.site.infra.enums.WebCodeEnum;
import us.zoom.cube.site.lib.AuthException;
import us.zoom.cube.site.lib.BasePara;
import us.zoom.cube.site.lib.input.UserInCache;
import us.zoom.infra.dao.model.TenantDO;
import us.zoom.infra.dao.model.UserRoleDO;
import us.zoom.infra.dao.service.AlarmDAO;
import us.zoom.infra.dao.service.AuthDao;
import us.zoom.infra.dao.service.MetricsDAO;
import us.zoom.infra.model.menu.MenuTree;
import us.zoom.infra.thread.NamedThreadFactory;
import us.zoom.infra.utils.Instance;

import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class AuthHandler {
    private final String NO_PERMISSION = "No permission: ";

    @Autowired
    AuthDao authDao;

    @Autowired
    AlarmDAO alarmDAO;

    @Autowired
    private MetricsDAO metricsDAO;

    @Autowired
    private UserHandler userHandler;

    @Autowired
    private UserRoleHandler userRoleHandler;

    @Autowired
    private MenuHandler menuHandler;

    @Autowired
    private RoleMenuRelaHandler roleMenuRelaHandler;

    @Autowired
    private TenantUserRelaHandler tenantUserRelaHandler;

    @Autowired
    private TenantHandler tenantHandler;

    @Autowired
    private UserGroupHandler userGroupHandler;

    @Autowired
    private DashTreeHandler dashTreeHandler;

    @PostConstruct
    public void init(){
//        loadAllUserRoleMenuCache();
        Executors.newScheduledThreadPool(1,new NamedThreadFactory(" User Role Menu Cache Loader Scheduler")).scheduleAtFixedRate(new Runnable() {
            @Override
            public void run() {
                loadAllUserRoleMenuCache();
            }
        }, 5,1, TimeUnit.MINUTES);
    }

    public void loadAllUserRoleMenuCache() {
        //don't change the order
        dashTreeHandler.loadDashTree();
        menuHandler.loadMenuTree();
        userRoleHandler.loadRoleCache();
        userHandler.loadUserCache();
        roleMenuRelaHandler.loadRela();
        tenantUserRelaHandler.loadRela();
    }



    /**
     * new method
     * @param basePara
     * @return
     */
    public MenuTree getMenuTree(BasePara basePara){
        UserInCache userInCache = userHandler.getUserFromCache(basePara.getUserId());
        if(null == userInCache ){
            return MenuTree.emptyTree();
        }

        Set<String> menuResIdsForRoles = getMenuResIds(basePara.getUserId(),basePara.getTenantId());
        if (CollectionUtils.isEmpty(menuResIdsForRoles)){
            return MenuTree.emptyTree();
        }
        return menuHandler.getMenuTreeFromCacheByResIds(menuResIdsForRoles);
    }

    private  Set<String> getMenuResIds(String userId,String tenantId ) {
        Set<String> menuResIdsForRoles = new HashSet<>();
        //the role of the tenant plus the cross service role
        Set<String> roles = getUserRoles(userId, tenantId ,true ,true);

        if(CollectionUtils.isEmpty(roles)){
            return  menuResIdsForRoles;
        }

        roles.forEach(role->{
            Set<String> resIds = roleMenuRelaHandler.getMenuResIdsByRoleName(role);
            if(CollectionUtils.isNotEmpty(resIds)){
                menuResIdsForRoles.addAll(resIds);
            }
        });
        return menuResIdsForRoles;
    }

    public Boolean canOperate(String userId, String serviceId){
        if(canCrossAndOperate(userId)){
            return true;
        }

        Set<String> authedServiceIdSet = getServiceUserCanOperate(userId).stream().map(e -> e.getId()).collect(Collectors.toSet());
        return authedServiceIdSet.contains(serviceId);
    }

    public List<TenantDO> getServiceUserCanOperate(String userId){
        //Map<tenantId,Set<role>>
        Map<String, Set<String>> tenantRolesMap = tenantUserRelaHandler.getUserTenantRoles(userId);
        if(MapUtils.isEmpty(tenantRolesMap)){
            return Collections.EMPTY_LIST;
        }

        Set<String> allRolesFromAllTenant = new HashSet<>();
        tenantRolesMap.values().forEach(item->{
            if(CollectionUtils.isNotEmpty(item)){
                allRolesFromAllTenant.addAll(item);
            }
        });

        Set<String>  crossServiceRoles =  userRoleHandler.getCrossServiceRole(allRolesFromAllTenant);
        //has cross auth
        if(CollectionUtils.isNotEmpty(crossServiceRoles)){
            for(String role : crossServiceRoles){
                UserRoleDO userRoleDO =  userRoleHandler.getRoleByNameFromCache(role);
                if(null != userRoleDO && userRoleDO.getCanOperate()){
                    return tenantHandler.getAllTenantFromCache();
                }
            }

        }

        Set<String> tenantIdSets = new HashSet<>();
        tenantRolesMap.forEach((tenantId,roles)->{
            if(hasOperateAuth(roles)){
                tenantIdSets.add(tenantId);
            }
        });

        return Instance.ofNullable(tenantIdSets).stream().map(tenantId -> tenantHandler.getTenantByIdFromCache(tenantId))
                .filter(x -> null != x).collect(Collectors.toList());

    }

    public Boolean canCrossAndOperate(String userId){
        //Map<tenantId,Set<role>>
        Map<String, Set<String>> tenantRolesMap = tenantUserRelaHandler.getUserTenantRoles(userId);


        Set<String> allRolesFromAllTenant = new HashSet<>();
        if (null != tenantRolesMap) {
            tenantRolesMap.values().forEach(item -> {
                if (CollectionUtils.isNotEmpty(item)) {
                    allRolesFromAllTenant.addAll(item);
                }
            });
        }

        Set<String>  crossServiceRoles =  userRoleHandler.getCrossServiceRole(allRolesFromAllTenant);
        //has cross auth
        if(CollectionUtils.isNotEmpty(crossServiceRoles)){
            for(String role : crossServiceRoles){
                UserRoleDO userRoleDO =  userRoleHandler.getRoleByNameFromCache(role);
                if(null != userRoleDO && userRoleDO.getCanOperate()){
                    return true;
                }
            }

        }
        return false;
    }

    private boolean hasOperateAuth(Set<String> roles) {
        if(CollectionUtils.isEmpty(roles)){
            return false;
        }
        for(String role :roles){
            userRoleHandler.getRoleByNameFromCache(role);
            UserRoleDO userRoleDO =  userRoleHandler.getRoleByNameFromCache(role);
            if(null != userRoleDO && userRoleDO.getCanOperate()){
                return true;
            }
        }
        return false;
    }

    private  Set<String> getMenuResUrls(String userId,String tenantId ) {
        Set<String> result = new HashSet<>();
        //the role of the tenant plus the cross service role
        Set<String> roles = getUserRoles(userId, tenantId ,true ,true);

        if(CollectionUtils.isEmpty(roles)){
            return  result;
        }

        roles.forEach(role->{
            Set<String> resUrls = roleMenuRelaHandler.getMenuResUrlByRoleName(role);
            if(CollectionUtils.isNotEmpty(resUrls)){
                result.addAll(resUrls);
            }
        });
        return result;
    }

    public void checkAuth(String userId, String tenantId, String api) {
        Set<String> menuResUrls = getMenuResUrls(userId, tenantId);
        if (CollectionUtils.isEmpty(menuResUrls) || !menuResUrls.contains(api)) {
            if (reloadUserToCache(userId, tenantId, api)) {
                return;
            }
            log.warn("checkAuth failed, userId={},tenantId={}, api={}", userId, tenantId, api);
            throw new AuthException(WebCodeEnum.PermitError.getCode(), WebCodeEnum.PermitError.getErrMsg() + NO_PERMISSION + api);
        }
    }

    private boolean reloadUserToCache(String userId, String tenantId, String api) {
        tenantUserRelaHandler.reloadUserToCache(userId);
        Set<String> menuResUrls = getMenuResUrls(userId, tenantId);
        if (CollectionUtils.isEmpty(menuResUrls) || !menuResUrls.contains(api)) {
            return false;
        }
        return true;
    }

    public boolean hasPiiAuth(String userId,String tenantNameIgnoreCase){
        TenantDO tenantDO = tenantHandler.getTenantByNameInLowerCaseFromCache(tenantNameIgnoreCase);
//      Set<String> rolesForTenant = getUserRoles(userId,tenantDO.getId(),true,false);
//      Set<String>  piiRoles = userRoleHandler.getPiiRole(rolesForTenant);
//      if(CollectionUtils.isNotEmpty(piiRoles)){
//          return true;
//      }

        //check cross service role has pii auth
        Set<String> allRoles = getUserRoles(userId, tenantDO.getId(), true, true);
        Set<String> piiRoles = userRoleHandler.getPiiRole(allRoles);
        if (CollectionUtils.isNotEmpty(piiRoles)) {
            return true;
        }
        return false;
    }

    public boolean hasSuchRole(String userId, String role) {
        Map<String, Set<String>> tenantRolesMap = tenantUserRelaHandler.getUserTenantRoles(userId);
        Set<String> allRolesFromAllTenant = new HashSet<>();
        tenantRolesMap.values().forEach(item->{
            if(CollectionUtils.isNotEmpty(item)){
                allRolesFromAllTenant.addAll(item);
            }
        });

        return allRolesFromAllTenant.contains(role);
    }

    public boolean hasSuchRole(String userId, String tenantId, String role) {
        Set<String> roles = getUserRoles(userId, tenantId ,true ,true);

        return roles.contains(role);
    }

    private Set<String> getUserRoles( String userId,String tenantId ,boolean includeTenantRole,boolean includeCrossServiceRole) {
        //Map<tenantId,Set<role>>
        Map<String, Set<String>> tenantRolesMap = tenantUserRelaHandler.getUserTenantRoles(userId);
        if(MapUtils.isEmpty(tenantRolesMap)){
            return Collections.EMPTY_SET;
        }

        Set<String> result = new HashSet<>();
        Set<String> roleForTenant = tenantRolesMap.get(tenantId);
        if(includeTenantRole && CollectionUtils.isNotEmpty(roleForTenant)){
            result.addAll(roleForTenant);
        }

        if(!includeCrossServiceRole){
           return  result;
        }
        Set<String> allRolesFromAllTenant = new HashSet<>();
        tenantRolesMap.values().forEach(item->{
            if(CollectionUtils.isNotEmpty(item)){
                allRolesFromAllTenant.addAll(item);
            }
        });

        Set<String>  crossServiceRoles =  userRoleHandler.getCrossServiceRole(allRolesFromAllTenant);

        if(CollectionUtils.isNotEmpty(crossServiceRoles)){
            result.addAll(crossServiceRoles);
        }

        return result;
    }

    public void checkAuthIgnoreTenant(BasePara basePara){
        this.checkAuthIgnoreTenant(basePara.getUserId(),basePara.getAuthResourceUrl());
    }


    public void checkAuthIgnoreTenant(String userId,String apiPath){
        //Map<tenantId,Set<Role Name>>
        Map<String, Set<String>> tenantRolesMap = tenantUserRelaHandler.getUserTenantRoles(userId);
        if(MapUtils.isEmpty(tenantRolesMap)){
            log.error("userid={}, api={}" , userId,apiPath);
            throw new AuthException( WebCodeEnum.PermitError);
        }

        Set<String> allRolesFromAllTenant = new HashSet<>();
        tenantRolesMap.values().forEach(item->{
            if(CollectionUtils.isNotEmpty(item)){
                allRolesFromAllTenant.addAll(item);
            }
        });

        for(String role : allRolesFromAllTenant){
            Set<String> menuResUrls = roleMenuRelaHandler.getMenuResUrlByRoleName(role);
            if(CollectionUtils.isNotEmpty(menuResUrls) && menuResUrls.contains(apiPath)){
                return;
            }
        }
        log.error("userid={}, api={}" , userId,apiPath);
        throw new AuthException( WebCodeEnum.PermitError);
    }


    public boolean hasSuchTenant(String userId, String tenantId) {

        //Map<tenantId,Set<role>>
        Map<String, Set<String>> tenantRolesMap = tenantUserRelaHandler.getUserTenantRoles(userId);
        if(MapUtils.isEmpty(tenantRolesMap)){
            return false;
        }

        if(tenantRolesMap.containsKey(tenantId)){
            return true;
        }

        Set<String> allRolesFromAllTenant = new HashSet<>();
        tenantRolesMap.values().forEach(item->{
            if(CollectionUtils.isNotEmpty(item)){
                allRolesFromAllTenant.addAll(item);
            }
        });

        Set<String>  crossServiceRoles =  userRoleHandler.getCrossServiceRole(allRolesFromAllTenant);
        if(CollectionUtils.isNotEmpty(crossServiceRoles)){
            return true;
        }
        return false;
    }

    public boolean hasCrosseServiceAuth(String userId,String tenantId ){
        return  hasSuchTenant(userId,tenantId);
    }

    /**
     *  get the tenant that user has , when user has cross service role ,return all tenantIds
     * @param userId
     * @return
     */
    public List<TenantDO> getAllTenantUserHas(String userId){
        if(hasCrosseServiceAuth(userId,null)){
          return   tenantHandler.getAllTenantFromCache();
        }
        //Map<tenantId,Set<role>>
        Map<String, Set<String>> tenantRolesMap = tenantUserRelaHandler.getUserTenantRoles(userId);
        if(MapUtils.isEmpty(tenantRolesMap)){
            return Collections.EMPTY_LIST;
        }
        List<TenantDO> result = new ArrayList<>();
        tenantRolesMap.keySet().forEach(tenantId->{
            TenantDO tenantDO = tenantHandler.getTenantByIdFromCache(tenantId);
            if(null != tenantDO){
                result.add(tenantDO);
            }
        });
        return result;
    }

    public boolean hasCrossServiceAuthIgnoreTenant(String userId){
        return hasSuchTenant(userId,null);
    }


    public List<String> listCollectorIds(String userId) {
        List<TenantDO> allTenantIds = getAllTenantUserHas(userId);
        if(CollectionUtils.isEmpty(allTenantIds)){
            return  Collections.EMPTY_LIST;
        }
        return authDao.listCollectorIdsByTenantIds(allTenantIds.stream().map(item->item.getId()).collect(Collectors.toList()));
    }


//    public List<TenantUserRelationDO> getCountUserTenantRela(String userId, String tenantId) {
//        return authDao.getUserTenantRela(userId,tenantId);
//    }

    public List<String> listTanentIdsByUserId(String userId) {
        List<TenantDO> tenantDOS =  getAllTenantUserHas(userId);
        return Instance.ofNullable(tenantDOS).stream().map(item->item.getId()).collect(Collectors.toList());
//        return authDao.listTanentIdsByUserId(userId);
    }

    public List<String> listAlarmIdsByTenantIds(List<String> tenantIds) {
        return alarmDAO.listAlarmIdsByTenantIds(tenantIds);
    }

    public List<String> listTopicIdsByTenantIds(List<String> tenantIds) {
        Assert.notEmpty(tenantIds,"tenant id is empty!");
        return authDao.listTopicIdsByTenantIds(tenantIds);
    }

    public List<String> listPatternIdByTenantIds(List<String> tenantIds) {
        Assert.notEmpty(tenantIds,"tenant id is empty!");
        return authDao.listPatternIdByTenantIds(tenantIds);
    }

    public List<String> listDataParserIdByTenantIds(List<String> tenantIds) {
        Assert.notEmpty(tenantIds,"tenant id is empty!");
        return authDao.listDataParserIdByTenantIds(tenantIds);
    }


    public List<String> listDataParserSourceIdByTenantIds(List<String> tenantIds) {
        Assert.notEmpty(tenantIds,"tenant id is empty!");
        return authDao.listDataParserSourceIdByTenantIds(tenantIds);
    }

    public List<String> listMetricsIdsByTenantIds(List<String> tenantIds) {
        return metricsDAO.listMetricsIdsByTenantIds(tenantIds);
    }

    public void checkAuth(BasePara basePara) {
        checkAuth(basePara.getUserId(),basePara.getTenantId(),basePara.getAuthResourceUrl());
    }


    public boolean hasSuchTenantIds(String userId, List<String> tenantIds) {
        if (hasCrosseServiceAuth(userId, null)) {
            return true;
        }
        List<String> authTenantIds = Instance.ofNullable(getAllTenantUserHas(userId)).stream().map(item -> item.getId()).collect(Collectors.toList());
        return authTenantIds.containsAll(tenantIds);
    }
}
