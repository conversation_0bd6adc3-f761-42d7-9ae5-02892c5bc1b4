package us.zoom.cube.site.lib.output.alarm;

import com.zoom.op.monitor.domain.DerivedMetric;
import com.zoom.op.monitor.domain.alarm.*;
import lombok.Data;
import us.zoom.cube.site.lib.output.tag.TagInfoOut;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class AlarmDefinitionOutput {

    private String id;

    private String name;

    private String metricId;

    private List<AlarmRule> rules;

    private Integer timesInPeriod;

    private Integer periodInMinutes;

    private Boolean enabled = true;

    private AlarmConfigStatusEnum status = AlarmConfigStatusEnum.APPROVED;

    private List<Notification> notifications;

    private List<AlarmExtensionRelation> alarmExtensionRelations;

    private String description;

    private String subAlarmName;

    private String subAlarmId;

    private String creator;

    private String editor;

    private String levelsSendIncident;

    private Integer silenceStatus;

    private List<AlarmGroupIdName> alarmGroupList;

    private String userId;

    private String tenantId;

    protected Date createTime;

    protected Date modifyTime;

    List<TagInfoOut> labelInfoList;

    AlarmMatchModeEnum alarmMatchMode;

    String groupTags;

    AlarmSourceEnum source = AlarmSourceEnum.METRIC;

    AlarmSourceTypeEnum sourceType = AlarmSourceTypeEnum.DEFAULT;

    private DerivedMetric derivedMetric;

    @Data
    public static class AlarmGroupIdName{
        private String id;
        private String name;
    }


}
