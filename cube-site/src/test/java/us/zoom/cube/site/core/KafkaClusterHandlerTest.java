package us.zoom.cube.site.core;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import us.zoom.cube.lib.common.SaslMechanismEnum;
import us.zoom.infra.dao.model.KafkaClusterDO;
import us.zoom.infra.dao.service.KafkaClusterDAO;

/**
 * @Author: luis.zheng
 * @Date: 2021/8/10 3:51
 */
public class KafkaClusterHandlerTest {
    private KafkaClusterDAO kafkaClusterDAO;
    private RsaService rsaService;
    private KafkaClusterHandler kafkaClusterHandler;

    private String publicKey = "";
    private String privateKey = "";

    @BeforeEach
    public void before() {
        rsaService = new RsaService();
        rsaService.setCommonPrivatekey(privateKey);
        rsaService.setCommonPublicKey(publicKey);

        kafkaClusterDAO = Mockito.mock(KafkaClusterDAO.class);

        kafkaClusterHandler = new KafkaClusterHandler(kafkaClusterDAO, rsaService);
    }

    private String encryptStr = "encryptStr";
    private String id = "id-id-id";

    @Test
    public void testAdd() throws Exception {
        rsaService = new RsaService();
        rsaService.setCommonPrivatekey(privateKey);
        rsaService.setCommonPublicKey(publicKey);

        KafkaClusterDO kafkaClusterDO = createKafkaCluster();
        kafkaClusterHandler.add(kafkaClusterDO);
        Assertions.assertNotEquals(kafkaClusterDO.getSslKeystorePassword(), encryptStr);
    }

    @Test
    public void testEdit() throws Exception {
        rsaService = new RsaService();
        rsaService.setCommonPrivatekey(privateKey);
        rsaService.setCommonPublicKey(publicKey);

        KafkaClusterDO kafkaClusterDO = createKafkaCluster();
        kafkaClusterHandler.edit(kafkaClusterDO);
        Assertions.assertNotEquals(kafkaClusterDO.getSslKeystorePassword(), encryptStr);
    }

    @Test
    public void testGetById() throws Exception {
        rsaService = new RsaService();
        rsaService.setCommonPrivatekey(privateKey);
        rsaService.setCommonPublicKey(publicKey);

        KafkaClusterDO kafkaClusterDO = createKafkaCluster();
        kafkaClusterHandler.add(kafkaClusterDO);

        Mockito.when(kafkaClusterDAO.getById(id)).thenReturn(kafkaClusterDO);
        KafkaClusterDO getKafkaClusterDO = kafkaClusterHandler.getSecurityById(id);
        Assertions.assertEquals(getKafkaClusterDO.getSslKeystorePassword(), encryptStr);
    }

    @Test
    public void testHasSameName() {
        Mockito.when(kafkaClusterDAO.countByName("name")).thenReturn(1);
        boolean flag1 = kafkaClusterHandler.hasSameName("name");
        Assertions.assertEquals(flag1, true);

        Mockito.when(kafkaClusterDAO.countByName("name")).thenReturn(0);
        boolean flag2 = kafkaClusterHandler.hasSameName("name");
        Assertions.assertEquals(flag2, false);

        Mockito.when(kafkaClusterDAO.countByName("name")).thenReturn(10);
        boolean flag3 = kafkaClusterHandler.hasSameName("name");
        Assertions.assertEquals(flag3, true);
    }

    private KafkaClusterDO createKafkaCluster() {
        KafkaClusterDO kafkaClusterDO = new KafkaClusterDO();
        kafkaClusterDO.setId(id);
        kafkaClusterDO.setName("ds01-cluster-name");
        kafkaClusterDO.setRegionId("VA");
        kafkaClusterDO.setClusterId("ds01");
        kafkaClusterDO.setBootstrapServersPlain("ec2-50-16-132-120.compute-1.amazonaws.com:9093");
        kafkaClusterDO.setBootstrapServersSsl("ec2-50-16-132-120.compute-1.amazonaws.com:9095");
        kafkaClusterDO.setBootstrapServersSasl("ec2-50-16-132-120.compute-1.amazonaws.com:9095");
        kafkaClusterDO.setBootstrapServersSslSasl("ec2-50-16-132-120.compute-1.amazonaws.com:9095");
        kafkaClusterDO.setSslTruststoreLocation("adsfasdf");
        kafkaClusterDO.setSslTruststorePassword("adsfasdf");
        kafkaClusterDO.setSslEndpointIdentificationAlgorithm("");
        kafkaClusterDO.setSslKeystoreLocation("");
        kafkaClusterDO.setSslKeystorePassword(encryptStr);
        kafkaClusterDO.setSslKeyPassword("");
        kafkaClusterDO.setSaslJaasConfig("");
        kafkaClusterDO.setSaslMechanism(SaslMechanismEnum.GSSAPI.name());
        return kafkaClusterDO;
    }
}
