package us.zoom.cube.site.core;

import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import us.zoom.infra.dao.model.DataParserIpProcessorDO;
import us.zoom.infra.dao.service.DataParserIpProcessorDAO;

import java.util.List;

@Component
public class DataParserIpProcessorHandler {
    @Autowired
    private DataParserIpProcessorDAO dataParserIpProcessorDAO;


    public void addTimestampProcessor(DataParserIpProcessorDO dataParserIpProcessorDO) {
        Assert.notNull(dataParserIpProcessorDO,"timestamp processor is null !");
        dataParserIpProcessorDAO.add(dataParserIpProcessorDO);
    }

    public DataParserIpProcessorDO getIpProcessorById(String id) {
        Assert.notNull(id, "id is null !");
        return dataParserIpProcessorDAO.getIpProcessorById(id);
    }

    public void editIpProcessor(DataParserIpProcessorDO dataParserIpProcessorDO) {
        Assert.notNull(dataParserIpProcessorDO,"dataParserIpProcessorDO is null !");
        dataParserIpProcessorDAO.editIpProcessor(dataParserIpProcessorDO);
    }

    public void delIpProcessor(String id) {
        Assert.notNull(id, "id is null !");
        dataParserIpProcessorDAO.delIpProcessor(id);
    }

    public List<DataParserIpProcessorDO> listByPipelineIds(List<String> pipeLineIds) {
        List<DataParserIpProcessorDO> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(pipeLineIds)) {
            return result;
        }
        return dataParserIpProcessorDAO.listByPipelineIds(pipeLineIds);
    }

    public void delIByPipelineIds(List<String> pipeLineIds) {
        if(CollectionUtils.isEmpty(pipeLineIds)){
            return;
        }
        dataParserIpProcessorDAO.delByPipeIds(pipeLineIds);
    }
}
