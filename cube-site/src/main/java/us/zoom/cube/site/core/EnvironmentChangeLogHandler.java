package us.zoom.cube.site.core;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import us.zoom.cube.site.lib.input.UserInCache;
import us.zoom.infra.dao.model.EnvironmentChangeLogDO;
import us.zoom.infra.dao.service.EnvironmentChangeLogDAO;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2022-08-10 13:38
 */
@Component
public class EnvironmentChangeLogHandler {

    @Autowired
    private EnvironmentChangeLogDAO environmentChangeLogDAO;

    @Autowired
    private UserHandler userHandler;

    @Transactional(rollbackFor = Exception.class)
    public void addEnvironmentChangeLog(EnvironmentChangeLogDO environmentChangeLogDO){
        environmentChangeLogDAO.add(environmentChangeLogDO);
    }

    public List<EnvironmentChangeLogDO> searchEnvironmentChangeLog( ){
        List<EnvironmentChangeLogDO> environmentChangeLogDOList = environmentChangeLogDAO.searchAll();
        environmentChangeLogDOList.forEach(x -> {
            UserInCache userFromCache = userHandler.getUserFromCache(x.getChangeOwner());
            x.setChangeOwnerName(null != userFromCache ? userFromCache.getName() : null);
        });
        return environmentChangeLogDOList;
    }



}
