package us.zoom.cube.site.core;

import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import us.zoom.infra.dao.model.InspectionTaskDO;
import us.zoom.infra.dao.service.InspectionTaskDAO;

import java.util.List;

/**
 * @Description:
 * @author: <PERSON>
 * @date: 2023/04/11 09:45
 */
@Service
@Slf4j
public class InspectionTaskHandler {

    @Autowired
    private InspectionTaskDAO inspectionTaskDAO;

    public int countByPlanId(String planId) {
        Assert.hasText(planId, "planId is empty");
        return inspectionTaskDAO.countByPlanId(planId);
    }

    public List<InspectionTaskDO> pageQuery(String planId, long index, long size) {
        Assert.hasText(planId, "planId is empty");
        Assert.isTrue(size >= 0, "page size is error");
        Assert.isTrue(index >= 0, "index is error");
        return inspectionTaskDAO.pageQuery(planId, index, size);
    }

    public InspectionTaskDO findById(String id) {
        return inspectionTaskDAO.findById(id);
    }

}
