package us.zoom.cube.site.core;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import us.zoom.cube.lib.utils.JsonUtils;
import us.zoom.cube.site.CubeSiteApplicationTests;
import us.zoom.cube.site.core.model.ad.AdCfgData;
import us.zoom.cube.site.core.model.ad.AdTagCfgData;
import us.zoom.cube.site.core.model.ad.AdTagHolidayCfgData;
import us.zoom.cube.site.infra.utils.JacksonUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-07-11 13:31
 */
public class AdhandlerTest extends CubeSiteApplicationTests {

    @Autowired
    AdCfgHandler adCfgHandler;




    @Test
    public void addAd(){

        AdCfgData adCfgData = new AdCfgData();
        adCfgData.setId("d07cec36-e36b-437b-a546-76c20c91aecd");
        adCfgData.setUserId("8d987e9d-1736-4aa3-bbfd-2a53e60a38c8");
        adCfgData.setTenantName("Infra_Monitor_Cube-Alarm");
        adCfgData.setTenantId("1f8cacb9-8769-481f-8ad8-443f18799fa2");
        adCfgData.setMetricsId("e72a88f9-01fa-43ad-8da9-adff6ea58d0b");
        adCfgData.setMetricsName("cpu_agg3");
        adCfgData.setAdName("testNew");
        adCfgData.setAnomalyDirectionType(null);
        adCfgData.setMetricsType(0);
        adCfgData.setAdStatus(true);
        adCfgData.setAggPeriod(800);
        //adCfgData.setFieldName("us.avg");
        adCfgData.setFieldName("us.avg,us.sum,us.tt,us.aa");
        List<AdTagHolidayCfgData> adTagHolidayCfgDataList = new ArrayList<>();
        /*AdTagHolidayCfgData adTagHolidayCfgData = new AdTagHolidayCfgData();
        adTagHolidayCfgData.setTagName("name");
        adTagHolidayCfgData.setTagValue("test");
        adTagHolidayCfgData.setTagHoliday("us02");
        adTagHolidayCfgDataList.add(adTagHolidayCfgData);*/
        List<AdTagCfgData> adTagCfgDataList = new ArrayList<>();
        AdTagCfgData adTagCfgData = new AdTagCfgData();
        adTagCfgData.setTagName("clusterId");
        adTagCfgData.setTagValue("oh2024");
        adTagCfgData.setAdTagType(2);
        adTagCfgData.setTagHolidayCfgDataList(adTagHolidayCfgDataList);
        adTagCfgDataList.add(adTagCfgData);
        adCfgData.setAdTagCfgDataList(adTagCfgDataList);
        //AdCfgData adCfgData2= JsonUtils.toObject("{\"id\":\"72de8061-00e4-41bd-a6da-0e1815463a57\",\"adName\":\"eddy_test_agg_st_t1.sum_60s_ai\",\"metricsId\":\"a56bd226-1f72-4285-a90f-7cd0b9854325\",\"metricsName\":\"eddy_test_agg\",\"aggPeriod\":60,\"metricsType\":1,\"fieldName\":\"st_t1.sum\",\"adStatus\":false,\"tenantId\":\"241b3c55-d6a5-4687-a8c2-9d3d6e098bea\",\"tenantName\":\"Infra_Monitor_Cube_Site\",\"anomalyDirectionType\":\"3\",\"thresValueFilterUp\":null,\"thresValueFilterDown\":null,\"rollingAlertSensitivityAll\":\"3\",\"rollingAlertSensitivityNum\":\"3\",\"detectionInterval\":null,\"modifyTime\":\"2024-11-13T05:45:01.000+00:00\",\"createTime\":\"2024-10-11T02:44:04.000+00:00\",\"adTrainStatus\":null,\"creator\":\"<EMAIL>\",\"editor\":\"<EMAIL>\",\"fillEmptyAsZero\":false,\"piiAuth\":null,\"adTagCfgDataList\":[{\"id\":\"e0dafc8b-c8a1-4bf1-824f-60f73f91cdf6\",\"adConfigId\":\"72de8061-00e4-41bd-a6da-0e1815463a57\",\"tagName\":\"appName\",\"tagValue\":null,\"adTagType\":2,\"tagHolidayCfgDataList\":[]},{\"id\":\"d01823a0-1663-4ac3-990a-efe774579002\",\"adConfigId\":\"72de8061-00e4-41bd-a6da-0e1815463a57\",\"tagName\":\"cell\",\"tagValue\":null,\"adTagType\":2,\"tagHolidayCfgDataList\":[]},{\"id\":\"1cb780d8-f3cc-43d2-b47a-8f7ba45db80d\",\"adConfigId\":\"72de8061-00e4-41bd-a6da-0e1815463a57\",\"tagName\":\"clusterId\",\"tagValue\":\"perfout\",\"adTagType\":1,\"tagHolidayCfgDataList\":[{\"id\":\"643b396f-0865-4da4-b204-347ea8eae850\",\"adConfigId\":\"72de8061-00e4-41bd-a6da-0e1815463a57\",\"adTagConfigId\":\"1cb780d8-f3cc-43d2-b47a-8f7ba45db80d\",\"tagName\":\"clusterId\",\"tagValue\":\"perfout\",\"tagHoliday\":null}]}]}",AdCfgData.class);
        //adCfgHandler.updateAdCfg(adCfgData2);
        //adCfgHandler.updateAdCfg(adCfgData);
        //adCfgHandler.getAdCfgById("f45313c9-d521-4488-accd-4ed6052a8a7a");
        adCfgHandler.deleteAdCfg("00172780-f783-4b3f-bcb7-8629802ae33d");


        /*adTagCfgData.setTagValue("oh3");

        List<AdTagHolidayCfgData> adTagHolidayCfgDataList2 = new ArrayList<>();
        AdTagHolidayCfgData adTagHolidayCfgData2 = new AdTagHolidayCfgData();
        adTagHolidayCfgData2.setTagName("name");
        adTagHolidayCfgData2.setTagValue("test5");
        adTagHolidayCfgData2.setTagHoliday("us5");
        adTagHolidayCfgDataList2.add(adTagHolidayCfgData);
        List<AdTagCfgData> adTagCfgDataList2 = new ArrayList<>();
        AdTagCfgData adTagCfgData2= new AdTagCfgData();
        adTagCfgData2.setTagName("clusterId");
        adTagCfgData2.setTagValue("oh5");
        adTagCfgData2.setAdTagType(1);
        adTagCfgData2.setTagHolidayCfgDataList(adTagHolidayCfgDataList);
        adTagCfgDataList2.add(adTagCfgData);
        adCfgData.setAdTagCfgDataList(adTagCfgDataList);
        adCfgHandler.updateAdCfg(adCfgData);*/

        //adCfgHandler.deleteAdCfg();

        //adCfgHandler.addAdCfg(adCfgData);
        //adCfgHandler.batchAddAdCfg(adCfgData);
        //updataAd();
        //deleteAd();




    }

    @Test
    public void updataAd(){
        AdCfgData adCfgData = new AdCfgData();
        adCfgData.setId("c592368f-1070-45ba-9b28-657da7b89699");
        adCfgData.setAdStatus(true);
        adCfgData.setFieldName("us.avg2");
        adCfgData.setAggPeriod(500);
        List<AdTagCfgData> adTagCfgDataList = new ArrayList<>();
        AdTagCfgData adTagCfgData = new AdTagCfgData();
        adTagCfgData.setTagName("clusterId");
        adTagCfgData.setTagValue("oh3");
        AdTagCfgData adTagCfgData2 = new AdTagCfgData();
        adTagCfgData2.setTagName("clusterId");
        adTagCfgData2.setTagValue("oh5");
        adTagCfgDataList.add(adTagCfgData);
        adTagCfgDataList.add(adTagCfgData);
        adCfgData.setAdTagCfgDataList(adTagCfgDataList);
        adCfgHandler.updateAdCfg(adCfgData);

    }

    @Test
    public void getAdByMetricsId(){

        List<AdCfgData> adCfgDatas = adCfgHandler.getAdCfgByMetricsId("e72a88f9-01fa-43ad-8da9-adff6ea58d0b");

    }

    @Test
    public void deleteAd(){
        adCfgHandler.deleteAdCfg("d49d6834-afff-48eb-95a7-3a7136b3cc84");
    }



    @Test
    public void batchAddAd(){
        List<AdCfgData> adCfgDatas = new ArrayList<>();
        AdCfgData adCfgDate = new AdCfgData();
        adCfgDate.setTenantName("Infra_Monitor_Cube-Alarm");
        adCfgDate.setTenantId("1f8cacb9-8769-481f-8ad8-443f18799fa2");
        adCfgDate.setMetricsId("e72a88f9-01fa-43ad-8da9-adff6ea58d0b");
        adCfgDate.setMetricsName("cpu_agg");
        adCfgDate.setMetricsType(0);
        adCfgDate.setAdStatus(true);
        adCfgDate.setFieldName("us.avg");

        List<AdTagCfgData> adTagCfgDataList = new ArrayList<>();
        AdTagCfgData adTagCfgData = new AdTagCfgData();
        adTagCfgData.setTagName("clusterId");
        adTagCfgData.setTagValue("oh");
        adTagCfgDataList.add(adTagCfgData);
        adCfgDate.setAdTagCfgDataList(adTagCfgDataList);
        adCfgDatas.add(adCfgDate);

        AdCfgData adCfgDate2 = new AdCfgData();
        adCfgDate2.setTenantName("ds_web");
        adCfgDate2.setTenantId("b8a83c60-c8a2-4d0b-a198-ccf03e6b12db");
        adCfgDate2.setMetricsId("c4d4418d-27c8-4b2a-8e04-799cac031d85");
        adCfgDate2.setMetricsName("spc_data");
        adCfgDate2.setMetricsType(0);
        adCfgDate2.setAdStatus(true);
        adCfgDate2.setFieldName("errorCode.count");

        List<AdTagCfgData> adTagCfgDataList2 = new ArrayList<>();
        AdTagCfgData adTagCfgData2 = new AdTagCfgData();
        adTagCfgData2.setTagName("clusterId");
        adTagCfgData2.setTagValue("oh");
        adTagCfgDataList2.add(adTagCfgData);
        adCfgDate2.setAdTagCfgDataList(adTagCfgDataList);
        adCfgDatas.add(adCfgDate2);



    }


}
