package us.zoom.cube.site.core;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import us.zoom.infra.dao.model.InfluxdbHaRelationDO;
import us.zoom.infra.dao.service.InfluxdbHaRelationDAO;
import us.zoom.infra.dao.service.InfluxdbTenantRelationDAO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/8/11 5:24 PM
 */

@Service
@Slf4j
public class InfluxdbHaRelationHandler {

    @Autowired
    private InfluxdbHaRelationDAO influxDbHaRelationDAO;

    @Autowired
    private InfluxdbTenantRelationDAO influxdbTenantRelationDAO;

    @Transactional(rollbackFor = Exception.class)
    public void addHaCluster(List<InfluxdbHaRelationDO> influxDbHaRelationDos) {

    }


}
