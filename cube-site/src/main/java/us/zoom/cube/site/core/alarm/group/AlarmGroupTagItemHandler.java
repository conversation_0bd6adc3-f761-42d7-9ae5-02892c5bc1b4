package us.zoom.cube.site.core.alarm.group;

import com.okta.commons.lang.Collections;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import us.zoom.infra.dao.model.AlarmGroupTagItemDO;
import us.zoom.infra.dao.service.AlarmGroupTagItemDAO;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: <EMAIL>
 * @ModuleOwner: <EMAIL>
 * @Date: 01/05/2023 19:33
 * @Description:
 */
@Service
public class AlarmGroupTagItemHandler {

    @Autowired
    private AlarmGroupTagItemDAO alarmGroupTagItemDAO;

    public List<AlarmGroupTagItemDO> findByAlarmGroupServiceItemIdList(List<String> alarmGroupServiceItemIdList){
        if(Collections.isEmpty(alarmGroupServiceItemIdList)){
            return new ArrayList<>();
        }
        return alarmGroupTagItemDAO.findByAlarmGroupServiceItemIdList(alarmGroupServiceItemIdList);
    }


    public void deleteByAlarmGroupServiceItemId(String serviceItemId){
        alarmGroupTagItemDAO.deleteByAlarmGroupServiceItemId(serviceItemId);
    }

    public int batchInsert(List<AlarmGroupTagItemDO> alarmGroupTagItemDOList){
        if(Collections.isEmpty(alarmGroupTagItemDOList)){
            return 0;
        }
        return alarmGroupTagItemDAO.batchInsert(alarmGroupTagItemDOList);
    }

    public int batchUpdate(List<AlarmGroupTagItemDO> alarmGroupTagItemDOList){
        if(Collections.isEmpty(alarmGroupTagItemDOList)){
            return 0;
        }
        return alarmGroupTagItemDAO.batchUpdate(alarmGroupTagItemDOList);
    }

    public int batchDelete(List<String> idList){
        if(Collections.isEmpty(idList)){
            return 0;
        }
        return alarmGroupTagItemDAO.batchDelete(idList);
    }
}
