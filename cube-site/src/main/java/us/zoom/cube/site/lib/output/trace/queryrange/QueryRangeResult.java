package us.zoom.cube.site.lib.output.trace.queryrange;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: eason.jia
 * @date: 2024/8/19
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QueryRangeResult {
    private String queryName;
    private List<Series> series;
    private List<Row> list;
    private Table table;
    private String errorMessage;
}
