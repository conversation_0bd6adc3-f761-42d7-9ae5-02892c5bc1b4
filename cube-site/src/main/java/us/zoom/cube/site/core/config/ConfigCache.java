package us.zoom.cube.site.core.config;

import com.google.common.collect.Maps;
import lombok.Data;
import org.springframework.stereotype.Component;
import us.zoom.infra.dao.model.AsyncmqAccountMappingDO;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/9/1 3:04 PM
 */

@Component
@Data
public class ConfigCache {

    private Map<String, String> influxDatabaseToTenantNameMap;

    public ConfigCache() {
        this.influxDatabaseToTenantNameMap = new HashMap<>();
    }

    public Map<String, String> getInfluxDatabaseToTenantNameMap() {
        return influxDatabaseToTenantNameMap;
    }

    public void setInfluxDatabaseToTenantNameMap(Map<String, String> influxDatabaseToTenantNameMap) {
        this.influxDatabaseToTenantNameMap = influxDatabaseToTenantNameMap;
    }
}
