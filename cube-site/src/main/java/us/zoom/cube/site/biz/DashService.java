package us.zoom.cube.site.biz;

import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;
import us.zoom.cube.lib.common.*;
import us.zoom.cube.lib.common.DashVisibilityEnum;
import us.zoom.cube.lib.utils.JsonUtils;
import us.zoom.cube.sdk.util.MonitorLogReporter;
import us.zoom.cube.site.api.intercept.AuthInterceptor;
import us.zoom.cube.site.biz.dashboard.DashSysParaService;
import us.zoom.cube.site.biz.dashboard.MigrationServiceMonitor;
import us.zoom.cube.site.core.*;
import us.zoom.cube.site.core.usergroup.UserGroupHandler;
import us.zoom.cube.site.infra.enums.WebCodeEnum;
import us.zoom.cube.site.infra.utils.JacksonUtils;
import us.zoom.cube.site.lib.BasePara;
import us.zoom.cube.site.lib.IdPara;
import us.zoom.cube.site.lib.ResponseObject;
import us.zoom.cube.site.lib.common.ValueLabel;
import us.zoom.cube.site.lib.dto.TopDashDTO;
import us.zoom.cube.site.lib.input.*;
import us.zoom.cube.site.lib.input.alarm.definition.TenantNameInput;
import us.zoom.cube.site.lib.monitor.FixDashCardDataMonitor;
import us.zoom.cube.site.lib.output.PageResult;
import us.zoom.cube.site.lib.output.dash.DashCardOut;
import us.zoom.cube.site.lib.output.dash.DashHasUserRelaOut;
import us.zoom.cube.site.lib.output.dash.DashUserOut;
import us.zoom.cube.site.lib.output.dash.TagsRelatedOutput;
import us.zoom.cube.site.lib.query.*;
import us.zoom.cube.site.lib.query.dashtree.GetDashTreeQuery;
import us.zoom.infra.dao.model.*;
import us.zoom.infra.dao.service.DashDAO;
import us.zoom.infra.enums.*;
import us.zoom.infra.model.IdAndName;
import us.zoom.infra.model.dash.DashTree;
import us.zoom.infra.utils.CommonSplitConstants;
import us.zoom.infra.utils.Instance;
import us.zoom.infra.utils.IpUtils;

import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class DashService {
    private final Logger monitorLog = LoggerFactory.getLogger("Monitor");

    public static final Instant DIRTY_DATA_START = Instant.parse("2023-11-01T00:00:00.000Z");
    public static final String DIRTY_DATA_KEY_CARD = "\"db\":\"Cube_Template\"";
    public static final String DIRTY_DATA_TEMPLATE_CARD = "\"db\":\"%s\"";
    public static final String DIRTY_DATA_KEY_DASH = "\"tenant\":\"Cube_Template\"";
    public static final String DIRTY_DATA_TEMPLATE_DASH = "\"tenant\":\"%s\"";
    public static final String CUBE_TEMPLATE = "Cube_Template";
    public static final int MAX_VERSION_NUM_TO_KEEP = 50;
    public static final int MAX_DISPLAY_DASH = 10;
    private static final String EDIT_BY_JSON = "Edited by JSON Model";
    private static final String RESTORED_FROM_VERSION = "Restored from version ";
    private static final int DELTA = 1;
    private static final String LAYOUT = "layout";
    private static final String LAYOUT_ID = "i";

    @Autowired
    private DashHandler dashHandler;

    @Autowired
    private UserGroupHandler userGroupHandler;

    @Autowired
    private DashTreeHandler dashTreeHandler;

    @Autowired
    private UserViewStatisticHandler userViewStatisticHandler;

    @Autowired
    private AuthService authService;

    @Autowired
    private UserHandler userHandler;

    @Autowired
    private CardHandler cardHandler;

    @Autowired
    DashTemplateHandler dashTemplateHandler;

    @Autowired
    private DashDAO dashDAO;

    @Autowired
    private TenantHandler tenantHandler;

    @Autowired
    private DashSysParaService dashSysParaService;


    public ResponseObject setDefault(DefaultDashInput dashInput) {
        authService.checkAuthIgnoreTenant(dashInput);

        dashHandler.setDefault(dashInput.getId(), dashInput.getUserId(), dashInput.getModule());
        return ResponseObject.success(true);
    }

    public ResponseObject getDefault(DefaultDashInput basePara) {
        authService.checkAuthIgnoreTenant(basePara);

        String defaultDashId = dashHandler.getDefault(basePara.getUserId(), basePara.getModule());
        //if user not set default dash, fill it with top used dash
        if (!StringUtils.hasText(defaultDashId)) {
            if (DashModuleEnum.APM.getCode().equals(basePara.getModule())) {
                GetDashTreeQuery query = new GetDashTreeQuery();
                query.setModule(basePara.getModule());
                query.setLoadFromDB(true);
                DashTree dashTree = dashTreeHandler.getDashTree(query);
                defaultDashId = dashTree.findFirstDashboardId();

            } else {
                List<DashUserViewStatisticDO> topUserViewDash = userViewStatisticHandler.getTopUserViewDash(basePara.getUserId(), null, CubeConstants.CUBE_DEFAULT_START_INDEX, CubeConstants.CUBE_DEFAULT_PAGE_SIZE);
                for (DashUserViewStatisticDO dashUserViewStatisticDO : topUserViewDash) {
                    DashDO dashById = dashHandler.getDashById(dashUserViewStatisticDO.getRelatedId());
                    if (dashById == null) {
                        userViewStatisticHandler.deleteByTypeAndRelateId(UserViewStatisticTypeEnum.DASH.getValue(), dashUserViewStatisticDO.getRelatedId());
                        continue;
                    }

                    defaultDashId = dashById.getId();
                    break;
                }
            }
        }

        return ResponseObject.success(defaultDashId);
    }

    public ResponseObject<PageResult<DashHasUserRelaOut>> searchHave(PageQuery<SearchHaveQuery> pageQuery ) {
        authService.checkAuthIgnoreTenant(pageQuery);
        Integer count = dashHandler.getSearchHaveCount(pageQuery);
        //for performance
        if (count < pageQuery.getPageSize() && pageQuery.getPageIndex() == 1) {
            pageQuery.setPageSize(count);
        }
        List<DashHasUserRelaDO> dashDOS = dashHandler.searchHave(pageQuery);
        List<DashHasUserRelaOut> dashHasUserRelaOuts = convertDash(dashDOS);
        PageResult<DashHasUserRelaOut> result = new PageResult<>(count, dashHasUserRelaOuts);
        return ResponseObject.success(result);
    }

    private List<DashHasUserRelaOut> convertDash(List<DashHasUserRelaDO> dashDOS) {
        if (CollectionUtils.isEmpty(dashDOS)) {
            return Collections.emptyList();
        }

        List<DashHasUserRelaOut> result = new ArrayList<>(dashDOS.size());
        for (DashHasUserRelaDO dashHasUserRelaDO : dashDOS) {
            DashHasUserRelaOut dashHasUserRelaOut = new DashHasUserRelaOut();
            BeanUtils.copyProperties(dashHasUserRelaDO, dashHasUserRelaOut);
            result.add(dashHasUserRelaOut);
        }

        return result;
    }


    public ResponseObject  searchCanUseFromPublic(PageQuery<SearchCanFromPublicQuery> pageQuery ) {
        authService.checkAuthIgnoreTenant(pageQuery);
        List<DashHasUserRelaDO> dashDOS= dashHandler.searchCanUseFromPublic(pageQuery);
        Integer count=dashHandler.getCanUseFromPublicCount(pageQuery);
        List<DashHasUserRelaOut> dashHasUserRelaOuts=convertDash(dashDOS);
        PageResult<DashHasUserRelaOut> result=new PageResult<>(count,dashHasUserRelaOuts);
        return ResponseObject.success(result);
    }

    @Transactional
    public ResponseObject add( DashInput dashInput) {
        authService.checkAuthIgnoreTenant(dashInput);
        String id = dashHandler.add(dashInput);
        return ResponseObject.success(id);
    }

    public ResponseObject edit(DashInput dashInput) {
        authService.checkAuthIgnoreTenant(dashInput);

        authService.mustDashOwner(dashInput.getUserId(), dashInput.getId());
        dashInput.checkNameAndDesc();
        dashHandler.edit(dashInput);
        dashHandler.rescanDashTenant(dashInput.getId());

        return ResponseObject.success(true);
    }

    public ResponseObject getDashAndCards(IdPara idPara) {

        userViewStatisticHandler.userView(AuthInterceptor.getUserId(), idPara.getId(), UserViewStatisticTypeEnum.DASH, DELTA);

        authService.checkAuthIgnoreTenant(idPara);

        String dashId = idPara.getId();

        DashHasUserRelaDO dashHasUserRela = dashHandler.getDashHasUserRela(AuthInterceptor.getUserId(), dashId);
        if (dashHasUserRela == null) {
            return ResponseObject.failWithWebCode(WebCodeEnum.DashboardWasDeleted);
        }

        checkCreatorAndSharedOwner(dashHasUserRela);

        List<CardDO> cards = cardHandler.listCardByDash(dashId);

        DashHasUserRelaOut out = buildDash(dashHasUserRela);

        return ResponseObject.success(new DashCardOut(out, cards));
    }

    private void checkCreatorAndSharedOwner(DashDO dashById) {
        //data correction, set first user of dash as creator
        List<DashUserRelaDO> dashUserRelaDOS = dashHandler.listAllDashUser(dashById.getId(), null);
        if (CollectionUtils.isEmpty(dashUserRelaDOS) || StringUtils.hasText(dashById.getCreator())) {
            return;
        }

        boolean needUpdate1 = isNeedUpdateCreator(dashById, dashUserRelaDOS);
        boolean needUpdate2 = isNeedUpdateSharedOwner(dashById, dashUserRelaDOS);
        if (needUpdate1 || needUpdate2) {
            dashDAO.edit(dashById);
        }
    }

    private boolean isNeedUpdateSharedOwner(DashDO dashById, List<DashUserRelaDO> dashUserRelaDOS) {
        Set<String> userList = dashUserRelaDOS.stream().filter(x -> DashUserRelaTypeEnum.OWNER.getCode().equals(x.getRelaType())).map(x -> x.getUserId()).collect(Collectors.toSet());
        //owner list is not empty
        if (CollectionUtils.isEmpty(userList)) {
            return false;
        }
        //need to merge to shared owner
        if (StringUtils.hasText(dashById.getSharedOwners())) {
            List<String> shareOwnerList = Splitter.on(CommonSplitConstants.COMMA_SPLIT).splitToList(dashById.getSharedOwners());
            String originalShareOwner = shareOwnerList.stream().sorted().collect(Collectors.joining(CommonSplitConstants.COMMA_SPLIT));
            userList.addAll(shareOwnerList);
            String newSharedOwner = userList.stream().sorted().collect(Collectors.joining(CommonSplitConstants.COMMA_SPLIT));

            boolean equals = org.apache.commons.lang3.StringUtils.equals(originalShareOwner, newSharedOwner);
            if (equals) {
                return false;
            } else {
                dashById.setSharedOwners(newSharedOwner);
                return true;
            }

            //use user list as new shared owner
        } else {
            String newSharedOwner = userList.stream().sorted().collect(Collectors.joining(CommonSplitConstants.COMMA_SPLIT));
            dashById.setSharedOwners(newSharedOwner);
            return true;
        }
    }

    private boolean isNeedUpdateCreator(DashDO dashById, List<DashUserRelaDO> dashUserRelaDOS) {
        if (StringUtils.hasText(dashById.getCreator())) {
            return false;
        }

        String userId = dashUserRelaDOS.get(0).getUserId();
        UserInCache userFromCache = userHandler.getUserFromCache(userId);
        if (null != userFromCache) {
            dashById.setCreator(userFromCache.getName());
            return true;
        }
        return false;
    }

    public Set<String> getAllDashUsers(String dashId) {
        DashHasUserRelaDO dash = new DashHasUserRelaDO();
        DashDO dashById = dashHandler.getDashById(dashId);
        BeanUtils.copyProperties(dashById, dash);
        dash.setId(dashId);
        Set<String> ownerListInDashTree = getDashTreeUserAndGroup(dash);

        Set<String> allDashUser = fillUserAndGroup(dash, new DashHasUserRelaOut());
        allDashUser.addAll(ownerListInDashTree);

        return allDashUser;
    }

    @NotNull
    private DashHasUserRelaOut buildDash(DashHasUserRelaDO dash) {
        DashHasUserRelaOut out = new DashHasUserRelaOut();
        BeanUtils.copyProperties(dash, out);

        Set<String> allUserInDashAndTree = getDashTreeUserAndGroup(dash);

        Set<String> dashOwnUsers = fillUserAndGroup(dash, out);
        allUserInDashAndTree.addAll(dashOwnUsers);

        List<ValueLabel> users = Instance.ofNullable(allUserInDashAndTree).stream()
                .map(x -> {
                    UserInCache userFromCache = userHandler.getUserFromCache(x);
                    ValueLabel ian = new ValueLabel(x, null != userFromCache ? userFromCache.getName() : null);
                    return ian;
                }).collect(Collectors.toList());
        out.setUsers(users);

        //set rela type for front-end
        out.setRelaType(org.apache.commons.collections4.CollectionUtils.containsAny(allUserInDashAndTree, AuthInterceptor.getUserId())
                || org.apache.commons.lang3.StringUtils.equals(AuthInterceptor.getUserName(), dash.getCreator())
                ? DashUserRelaTypeEnum.OWNER.getCode() : DashUserRelaTypeEnum.USER.getCode());

        List<String> dashTreeIdByDashId = dashTreeHandler.getParentDashTreeIdByDashId(dash.getId());
        out.setParentDashTreeItemId(Instance.ofNullable(dashTreeIdByDashId).stream().collect(Collectors.joining(CommonSplitConstants.COMMA_SPLIT)));

        return out;
    }

    private Set<String> getDashTreeUserAndGroup(DashHasUserRelaDO dash) {
        List<String> dashTreeIdList = dashTreeHandler.getParentDashTreeIdByDashId(dash.getId());
        Set<String> ownerListInDashTree = dashTreeHandler.getOwnerListInDashTree(dashTreeIdList);

        return ownerListInDashTree;
    }

    private @NotNull Set<String> fillUserAndGroup(DashHasUserRelaDO dash, DashHasUserRelaOut out) {
        Set<String> allDashUser = getOwnerOfDash(dash);
        if (StringUtils.hasText(dash.getSharedUserGroup())) {
            List<String> userGroupsIds = Splitter.on(CommonSplitConstants.COMMA_SPLIT).splitToList(dash.getSharedUserGroup());
            Instance.ofNullable(userGroupsIds).stream().forEach(x -> {
                List<String> userId = dashHandler.getUserGroupCache(x);
                allDashUser.addAll(userId);
            });
            // shared user and group updated
            out.setSharedUserGroup(dash.getSharedUserGroup());
        }
        out.setSharedOwners(dash.getSharedOwners());

        return allDashUser;
    }

    @NotNull
    private Set<String> getOwnerOfDash(DashDO dash) {
        String sharedOwners = dash.getSharedOwners();

        List<String> allDashUser = Lists.newArrayList();
        if (StringUtils.hasText(sharedOwners)) {
            allDashUser.addAll(Splitter.on(CommonSplitConstants.COMMA_SPLIT).splitToList(sharedOwners));
        }

        return new HashSet<>(allDashUser);
    }

    public ResponseObject delete(IdPara idPara) {
        authService.checkAuthIgnoreTenant(idPara);
        //authService.mustDashOwner(idPara.getUserId(),idPara.getId());
        dashHandler.delete(idPara);
        return ResponseObject.success(true);
    }


    public ResponseObject changeConfigs(DashInput dashInput) {
        authService.checkAuthIgnoreTenant(dashInput);

        Assert.notNull(dashInput.getConfigs(),"config is null");
        Assert.notNull(dashInput.getId(),"id is null");
        authService.mustDashOwner(dashInput.getUserId(),dashInput.getId());
        dashHandler.changeConfigs(dashInput);
        return ResponseObject.success(true);
    }

    public ResponseObject delDashUserRela(IdPara idPara) {
        authService.checkAuthIgnoreTenant(idPara);
        Assert.notNull(idPara.getId(),"id is null");
        authService.mustDashOwner(idPara.getUserId(),idPara.getId());
        dashHandler.delDashUserRela(idPara);
        return ResponseObject.success(true);

    }

    public ResponseObject getRelated(DashInput dashInput) {
        authService.checkAuthIgnoreTenant(dashInput);

        //authService.mustDashOwner(dashInput.getUserId(),dashInput.getId());
        Map<String,DashDO> map = dashHandler.getRelated(dashInput);
        List<TagsRelatedOutput> resList= new ArrayList<>();
        map.values().stream().forEach(
                dashDO -> {
                    TagsRelatedOutput tagsRelatedOutput = new TagsRelatedOutput();
                    tagsRelatedOutput.setTags(dashDO.getTags());
                    tagsRelatedOutput.setId(dashDO.getId());
                    tagsRelatedOutput.setName(dashDO.getName());
                    resList.add(tagsRelatedOutput);
                }
        );
        return ResponseObject.success(resList);
    }

    @Transactional
    public ResponseObject editByJson(String dashJsonModelStr, String comment) {
        authService.checkAuthByGetAuthFromAuthInterceptor();
        DashJsonEvAllInput dashJsonEvAllInput = JsonUtils.toObject(dashJsonModelStr, DashJsonEvAllInput.class);
        String dashId = dashJsonEvAllInput.getId();

        authService.mustDashOwner(AuthInterceptor.getUserId(), dashId);

        String previousJson = getCurrentDashJson(dashId);

        editDashAndCard(dashJsonEvAllInput);

        saveHistory(previousJson, dashJsonEvAllInput.getId(), org.apache.commons.lang3.StringUtils.isBlank(comment) ? EDIT_BY_JSON : comment);

        dashHandler.rescanDashTenant(dashId);

        dashTreeHandler.deleteDashTreeItemByDashId(dashId);
        dashHandler.addDash2Tree(AuthInterceptor.getUserName(), dashJsonEvAllInput.getParentDashTreeItemId(), null, dashId);

        return getDashAndCards(new IdPara(dashId));
    }

    private void editDashAndCard(DashJsonEvAllInput dashJsonEvAllInput) {

        DashJsonModel dashJsonModel = new DashJsonModel();
        BeanUtils.copyProperties(dashJsonEvAllInput,dashJsonModel);
        List<CardInput> card2EditList = new ArrayList<>();
        List<CardInput> card2AddList = new ArrayList<>();

        List<String> card2DeleteList = cardHandler.getDashCardRelaByDashId(dashJsonEvAllInput.getId()).stream().map(x-> x.getCardId()).collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(dashJsonEvAllInput.getCard())) {
            for (CardJsonEvInput cardJsonEvInput : dashJsonEvAllInput.getCard()) {
                CardInput cardInput = new CardInput();
                BeanUtils.copyProperties(cardJsonEvInput, cardInput);

                if (card2DeleteList.contains(cardJsonEvInput.getId())) {
                    card2EditList.add(cardInput);
                } else {
                    card2AddList.add(cardInput);
                }

            }
        }
        card2DeleteList.removeAll(card2EditList.stream().map(x -> x.getId()).collect(Collectors.toSet()));

        Assert.notNull(dashJsonModel.getType(),"dash type is not match");
        Assert.notNull(dashJsonModel.getName(),"name is null");
        Assert.notNull(dashJsonModel.getConfigs(),"cfg is null");
        Assert.notNull(dashJsonModel.getVisibility(),"visibility is null");
        Assert.notNull(dashJsonModel.getDescription(),"description is null");

        DashInput dashInput = processDashInput(dashJsonModel);
        String dashId = dashJsonEvAllInput.getId();
        dashInput.setId(dashId);
        dashInput.checkNameAndDesc();

        dashHandler.edit(dashInput);

        Map<String, String> idMap = new HashMap<>();

        if (CollectionUtils.isNotEmpty(card2AddList)) {
            BatchCardInput batchCardInput = new BatchCardInput();
            batchCardInput.setDashId(dashId);
            batchCardInput.setCardInputs(card2AddList);
            idMap.putAll(cardHandler.batchAdd(batchCardInput));
        }

        for (CardInput cardInput : card2EditList) {
            cardInput.setDashId(dashId);
            cardHandler.edit(cardInput);
            cardHandler.editConfigs(cardInput);
            idMap.put(cardInput.getId(), cardInput.getId());
        }

        cardHandler.batchDelete(card2DeleteList);

        changeConfigs(dashId, dashInput.getConfigs(), idMap);
    }

    public String getCurrentDashJson(String dashId) {
        DashInput dashInputTemp = new DashInput();
        dashInputTemp.setId(dashId);
        DashJsonModel previousJsonModel = this.getDashJsonModel(dashInputTemp);
        previousJsonModel.setId(dashId);
        String previousJson = JsonUtils.toJsonString(previousJsonModel);
        return previousJson;
    }

    private void changeConfigs(String dashId, Map configMap, Map<String, String> idMap) {
        if (MapUtils.isEmpty(configMap)) {
            return;
        }

        List<Map> list = new ArrayList();
        Object object = configMap.get(LAYOUT);
        if (null != object) {
            List layoutList = JsonUtils.toObject(JsonUtils.toJsonString(object), List.class);
            for (Object layout : layoutList) {
                Map layoutMap = JsonUtils.toObject(JsonUtils.toJsonString(layout), Map.class);
                String id = idMap.get(layoutMap.get(LAYOUT_ID));
                if (null != id) {
                    layoutMap.put(LAYOUT_ID, id);
                    list.add(layoutMap);
                }
            }

            configMap.put(LAYOUT, list);
        }

        DashInput dashInputCfg = new DashInput();
        dashInputCfg.setConfigs(configMap);
        dashInputCfg.setId(dashId);
        dashHandler.changeConfigs(dashInputCfg);

    }

    public ResponseObject copy(DashInput dashInput ) {
        authService.checkAuthIgnoreTenant(dashInput);
        Assert.notNull(dashInput.getId(),"id is null");
        //authService.mustDashOwner(dashInput.getUserId(),dashInput.getId());
        DashDO dashOld = dashDAO.getDashById(dashInput.getId());
        DashDO dashDO = dashHandler.copyDash(dashInput);
        HashMap<String,String> idMap = cardHandler.copyCard(dashInput.getId(),dashDO.getId(),true,dashInput.getService(),dashOld.getService());

        changeConfigs(dashDO.getId(), JsonUtils.toObject(dashDO.getConfigs(), Map.class), idMap);

        dashHandler.rescanDashTenant(dashDO.getId());

        return ResponseObject.success(dashDO.getId());
    }

    public ResponseObject getDashJson(DashInput dashInput ) {
        Assert.notNull(dashInput.getId(),"id is null");
        authService.checkAuthIgnoreTenant(dashInput);

        DashJsonModel dashJsonModel = getDashJsonModel(dashInput);
        return ResponseObject.success(dashJsonModel);
    }

    @NotNull
    private DashJsonModel getDashJsonModel(DashInput dashInput) {
        DashDO dashDO = dashHandler.getDashById(dashInput.getId());
        List<CardDO> cardDOs = cardHandler.getCardByDashId(dashInput.getId());
        String parentDashTreePathByDashId = dashTreeHandler.getParentDashTreePathByDashId(dashDO.getId());

        DashJsonModel dashJsonModel = processDashJsonModel(dashDO,cardDOs);
        dashJsonModel.setParentDashTreeItemId(parentDashTreePathByDashId);
        return dashJsonModel;
    }

    public ResponseObject setDashJson(String dashJsonModelStr) {
        DashJsonEvAllInput dashJsonEvAllInput = JsonUtils.toObject(dashJsonModelStr, DashJsonEvAllInput.class);
        DashJsonModel dashJsonModel = new DashJsonModel();
        dashJsonModel.getTenantId();
        BeanUtils.copyProperties(dashJsonEvAllInput, dashJsonModel);
        List<CardInput> cardInputList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(dashJsonEvAllInput.getCard())) {
            for (CardJsonEvInput cardJsonEvInput : dashJsonEvAllInput.getCard()) {
                CardInput cardInput = new CardInput();
                BeanUtils.copyProperties(cardJsonEvInput, cardInput);
                cardInputList.add(cardInput);
            }
        }
        dashJsonModel.setCard(cardInputList);
        //authService.mustDashOwner(dashInput.getUserId(),dashInput.getId());
        Assert.notNull(dashJsonModel.getType(),"dash type is not match");
        Assert.notNull(dashJsonModel.getName(),"name is null");
        Assert.notNull(dashJsonModel.getConfigs(),"cfg is null");
        Assert.notNull(dashJsonModel.getVisibility(),"visibility is null");
        Assert.notNull(dashJsonModel.getDescription(),"description is null");
        DashInput dashInput = processDashInput(dashJsonModel);
        String dashId = dashHandler.add(dashInput);
        Map<String,String> idMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(dashJsonModel.getCard())) {
            for (CardInput cardInput : dashJsonModel.getCard()) {
                cardInput.setDashId(dashId);
                String cardId = cardHandler.add(cardInput);
                idMap.put(cardInput.getId(), cardId);
            }
            changeConfigs(dashId, dashInput.getConfigs(), idMap);
        }

        dashHandler.addDash2Tree(AuthInterceptor.getUserName(), dashJsonEvAllInput.getParentDashTreeItemId(),
                dashJsonEvAllInput.getModule(), dashId);

        dashHandler.rescanDashTenant(dashId);

        return ResponseObject.success(dashId);
    }

    private DashJsonModel processDashJsonModel(DashDO dashDO,List<CardDO> cardDOS){
        DashJsonModel dashJsonModel = new DashJsonModel();
        dashJsonModel.setName(dashDO.getName());
        dashJsonModel.setConfigs(JsonUtils.toObject(dashDO.getConfigs(), Map.class));
        dashJsonModel.setTags(dashDO.getTags());
        dashJsonModel.setType(DashServiceEnum.fromValue(dashDO.getServiceCount()).name());
        dashJsonModel.setDescription(dashDO.getDescription());
        dashJsonModel.setVisibility(DashVisibilityEnum.fromValue(dashDO.getVisibility()).name());
        dashJsonModel.setService(dashDO.getService());
        //userIds, separated by comma
        String sharedOwners = dashDO.getSharedOwners();
        if (org.apache.commons.lang3.StringUtils.isNotBlank(sharedOwners)) {
            List<String> sharedOwnerLists = Splitter.on(CommonSplitConstants.COMMA_SPLIT).splitToList(sharedOwners);
            sharedOwners = Instance.ofNullable(sharedOwnerLists).stream().map(x -> {
                UserInCache userFromCache = userHandler.getUserFromCache(x);
                return (null != userFromCache) ? userFromCache.getName() : null;

            }).filter(x -> org.apache.commons.lang3.StringUtils.isNotBlank(x)).collect(Collectors.joining(CommonSplitConstants.COMMA_SPLIT));

            dashJsonModel.setSharedOwners(sharedOwners);
        }

        String sharedUserGroupStr = dashDO.getSharedUserGroup();
        if (org.apache.commons.lang3.StringUtils.isNotBlank(sharedUserGroupStr)) {
            List<String> sharedUserGroup = Splitter.on(CommonSplitConstants.COMMA_SPLIT).splitToList(sharedUserGroupStr);
            sharedUserGroupStr = Instance.ofNullable(sharedUserGroup).stream()
                    .map(x -> (null != userGroupHandler.getUserGroup(x)) ? userGroupHandler.getUserGroup(x).getName() : null)
                    .filter(x -> org.apache.commons.lang3.StringUtils.isNotBlank(x))
                    .collect(Collectors.joining(CommonSplitConstants.COMMA_SPLIT));

            dashJsonModel.setSharedUserGroup(sharedUserGroupStr);
        }

        List<CardInput> cardInputList = new ArrayList<>();
        for (CardDO cardDO : cardDOS){
            CardInput cardInput = new CardInput();
            BeanUtils.copyProperties(cardDO,cardInput);
            cardInput.setConfigs(JsonUtils.toObject(cardDO.getConfigs(),Map.class));
            cardInput.setId(cardDO.getId());
            // card userId must null, get userId from threadlocal
            cardInput.setUserId(null);
            cardInput.setTenantId(null);
            cardInputList.add(cardInput);
        }
        dashJsonModel.setCard(cardInputList);
        return dashJsonModel;
    }

    private DashInput processDashInput(DashJsonModel dashJsonModel){
        DashInput dashInput = new DashInput();
        dashInput.setName(dashJsonModel.getName());
        dashInput.setConfigs(dashJsonModel.getConfigs());
        dashInput.setService(dashJsonModel.getService());
        dashInput.setTags(dashJsonModel.getTags());
        dashInput.setServiceCount(DashServiceEnum.fromValueName(dashJsonModel.getType()).getValue());
        dashInput.setDescription(dashJsonModel.getDescription());

        String sharedOwners = dashJsonModel.getSharedOwners();
        if (org.apache.commons.lang3.StringUtils.isNotBlank(sharedOwners)) {
            List<String> sharedOwnerLists = Splitter.on(CommonSplitConstants.COMMA_SPLIT).splitToList(sharedOwners);
            sharedOwners = Instance.ofNullable(sharedOwnerLists).stream().map(x -> {
                UserInCache userFromCache = userHandler.getUserFromCacheByName(x);
                return (null != userFromCache) ? userFromCache.getId() : null;

            }).filter(x -> org.apache.commons.lang3.StringUtils.isNotBlank(x)).collect(Collectors.joining(CommonSplitConstants.COMMA_SPLIT));

            dashInput.setSharedOwners(sharedOwners);
        }

        String sharedUserGroupStr = dashJsonModel.getSharedUserGroup();
        if (org.apache.commons.lang3.StringUtils.isNotBlank(sharedUserGroupStr)) {
            List<String> sharedOwnerLists = Splitter.on(CommonSplitConstants.COMMA_SPLIT).splitToList(sharedUserGroupStr);
            sharedUserGroupStr = Instance.ofNullable(sharedOwnerLists).stream().map(x -> {
                UserGroupDO userGroupByName = userGroupHandler.getUserGroupByName(x);
                return (null != userGroupByName) ? userGroupByName.getId() : null;

            }).filter(x -> org.apache.commons.lang3.StringUtils.isNotBlank(x)).collect(Collectors.joining(CommonSplitConstants.COMMA_SPLIT));

            dashInput.setSharedUserGroup(sharedUserGroupStr);
        }
        return dashInput;
    }

    public ResponseObject<List<Map<String, String>>> getDashBoardByName(FullSearchDashInput fullSearchDashInput) {
        List<DashHasUserRelaDO> dashHasUserRelaDOS = dashHandler.fullSearchByName(fullSearchDashInput);
        List<Map<String, String>> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(dashHasUserRelaDOS)) {
            return ResponseObject.success(result);
        }
        dashHasUserRelaDOS.forEach(d -> {
            Map<String, String> dash = new HashMap<>();
            dash.put("id", d.getId());
            dash.put("name", d.getName());
            result.add(dash);
        });
        log.info("test log， test git commit");
        return ResponseObject.success(result);
    }

    public ResponseObject getDashHistory(PageQuery<IdPara> pageQuery) {
        authService.checkAuth(pageQuery);
        authService.mustDashOwner(AuthInterceptor.getUserId(), pageQuery.getQueryPara().getId());

        String dashId = pageQuery.getQueryPara().getId();
        int count = dashHandler.countDashHistory(pageQuery);
        if (count > MAX_VERSION_NUM_TO_KEEP) {
            PageQuery<IdPara> temp = new PageQuery<>();
            temp.setPageSize(MAX_VERSION_NUM_TO_KEEP);
            temp.setPageIndex(1);
            temp.setQueryPara(new IdPara(pageQuery.getQueryPara().getId()));
            List<DashHistoryDO> dashHistory = dashHandler.getDashHistory(temp);
            int minVersion = dashHistory.get(MAX_VERSION_NUM_TO_KEEP - 1).getVersion();
            dashHandler.deleteOldVersion(dashId, minVersion);

            count = MAX_VERSION_NUM_TO_KEEP;
        }

        List<DashHistoryDO> dashHistory = dashHandler.getDashHistory(pageQuery);


        return ResponseObject.success(pageQuery.getOperId(), new PageResult(count, dashHistory));
    }

    public void saveHistory(String json, String dashId, String comment){
        dashHandler.saveHistory(json, dashId, comment);
    }

    @Transactional
    public ResponseObject restoreVersion(RestoreVersionInput restoreVersionInput) {
        authService.checkAuth(restoreVersionInput);
        authService.mustDashOwner(AuthInterceptor.getUserId(), restoreVersionInput.getDashId());
        restoreVersionInput.check();

        DashHistoryDO history = dashHandler.getHistory(restoreVersionInput.getDashId(), restoreVersionInput.getVersion());
        if (history == null) {
            return ResponseObject.fail("version not exists");
        }

        this.editByJson(history.getJson(), RESTORED_FROM_VERSION + restoreVersionInput.getVersion());

        IdPara idPara = new IdPara(restoreVersionInput.getDashId());

        return getDashAndCards(idPara);
    }

    public ResponseObject getDashUserList(IdPara idPara) {
        authService.checkAuth(idPara);

        String dashId = idPara.getId();
        DashDO dashById = dashHandler.getDashById(dashId);
        if (null == dashById) {
            return ResponseObject.failWithWebCode(WebCodeEnum.DashboardWasDeleted);
        }

        Set<String> ownerOfDash = getOwnerOfDash(dashById);
        List<ValueLabel> users = Instance.ofNullable(ownerOfDash).stream().map(x -> {
            UserInCache userFromCache = userHandler.getUserFromCache(x);
            ValueLabel vl = new ValueLabel(x, userFromCache != null ? userFromCache.getName() : null);
            return vl;
        }).collect(Collectors.toList());

        List<DashUserRelaDO> dashUserRelaDOS = dashHandler.listAllDashUser(dashId, DashTopEnum.YES.getCode());
        List<ValueLabel> add2Favorite = Instance.ofNullable(dashUserRelaDOS).stream().map(x -> {
            UserInCache userFromCache = userHandler.getUserFromCache(x.getUserId());
            ValueLabel vl = new ValueLabel(x.getUserId(), userFromCache != null ? userFromCache.getName() : null);
            return vl;
        }).collect(Collectors.toList());

        return ResponseObject.success(new DashUserOut(users, add2Favorite));
    }

    @NotNull
    private List<TopDashDTO> getTopUsedDash() {
        PageQuery<NameQuery> pageQuery = new PageQuery<>();
        pageQuery.setPageSize(MAX_DISPLAY_DASH);
        pageQuery.setQueryPara(new NameQuery());
        List<DashHasUserRelaDO> searchDash2Top = dashHandler.searchTopDash(pageQuery);
        List<TopDashDTO> topUsedDashList = Instance.ofNullable(searchDash2Top)
                .stream().filter(x -> x.getTop() != null && x.getTop() > 0).map(x -> {
                    TopDashDTO topDashDO = new TopDashDTO();
                    BeanUtils.copyProperties(x, topDashDO);
            return topDashDO;
        }).collect(Collectors.toList());
        return topUsedDashList;
    }

    public ResponseObject setTopDash(IdPara idPara) {
        authService.checkAuthIgnoreTenant(idPara);

        dashHandler.setTopDash(idPara.getId(), AuthInterceptor.getUserId());

        return ResponseObject.success(true);
    }

    public ResponseObject unsetTopDash(IdPara idPara) {
        authService.checkAuthIgnoreTenant(idPara);

        dashHandler.unsetTopDash(idPara.getId(), AuthInterceptor.getUserId());
        return ResponseObject.success(true);
    }

    public ResponseObject searchDash2Top(PageQuery<NameQuery> pageQuery) {
        authService.checkAuthIgnoreTenant(pageQuery);

        List<DashHasUserRelaDO> dashHasUserRelaDOS = dashHandler.searchTopDash(pageQuery);
        List<TopDashDTO> collect = Instance.ofNullable(dashHasUserRelaDOS).stream().map(x -> {
            TopDashDTO topDashDO = new TopDashDTO();
            BeanUtils.copyProperties(x, topDashDO);
            return topDashDO;
        }).collect(Collectors.toList());
        int count = dashHandler.countTopDash(pageQuery);

        return ResponseObject.success(new PageResult<>(count,collect));
    }

    public ResponseObject addDashAndCard(DashWithCardInput dashWithCardInput) {
        authService.checkAuthIgnoreTenant(dashWithCardInput);
        authService.hasSuchTenant(dashWithCardInput.getUserId(), dashWithCardInput.getTenantId());
        dashWithCardInput.checkNameAndDesc();
        List<CardInput> cardInputs = dashWithCardInput.getCardInputs();
        if (CollectionUtils.isNotEmpty(cardInputs)) {
            cardInputs.forEach(x -> x.checkNameAndDesc());
        }

        DashInput dashInput = buildDashInput(dashWithCardInput);
        String dashId = dashHandler.add(dashInput);

        if (CollectionUtils.isNotEmpty(cardInputs)) {
            BatchCardInput batchCardInput = new BatchCardInput();
            batchCardInput.setCardInputs(cardInputs);
            batchCardInput.setDashId(dashId);
            cardHandler.batchAdd(batchCardInput);

        }
        dashHandler.rescanDashTenant(dashId);

        return ResponseObject.success(dashId);
    }

    private DashInput buildDashInput(DashWithCardInput dashWithCardInput) {
        DashInput dashInput = new DashInput();
        dashInput.setConfigs(dashWithCardInput.getConfigs());
        dashInput.setService(dashWithCardInput.getService());
        dashInput.setSharedOwners(dashWithCardInput.getSharedOwners());
        dashInput.setDescription(dashWithCardInput.getDescription());
        dashInput.setName(dashWithCardInput.getName());
        dashInput.setType(DashTypeEnum.USER.getCode());
        dashInput.setParentDashTreeItemId(dashWithCardInput.getParentDashTreeItemId());
        dashInput.setModule(dashWithCardInput.getModule());
        dashInput.setServiceCount(dashWithCardInput.getServiceCount());
        dashInput.setTags(dashWithCardInput.getTags());
        dashInput.setSharedUserGroup(dashWithCardInput.getSharedUserGroup());

        return dashInput;
    }

    public ResponseObject getDashTenantList(PageQuery<TenantNameInput> pageQuery) {
        authService.checkAuthIgnoreTenant(pageQuery);

        PageResult<String> result = dashHandler.getDashTenantList(pageQuery.getQueryPara().getTenantName(), pageQuery.getStartIndex(), pageQuery.getPageSize());
        List<IdAndName> tenantList = result.getItems().stream().map(x -> {
            IdAndName ian = new IdAndName();
            ian.setId(x);
            TenantDO tenantById = tenantHandler.getTenantById(x);
            ian.setName(null == tenantById ? null : tenantById.getName());

            return ian;
        }).collect(Collectors.toList());

        return ResponseObject.success(new PageResult<>(result.getTotal(), tenantList));
    }

    public ResponseObject recommendDashList(PageQuery<RecommendDashQuery> recommendDashQuery) {
        authService.checkAuthIgnoreTenant(recommendDashQuery);

        List<TopDashDTO> ret = new ArrayList<>();
        int count;
        RecommendDashQuery queryPara = recommendDashQuery.getQueryPara();
        if (queryPara.getType() == RecommendDashTypeEnum.TOP_USED.ordinal()) {
            ret = getTopUsedList(recommendDashQuery);

            count = userViewStatisticHandler.countTopUserViewDash(AuthInterceptor.getUserId(), null);

        } else if (queryPara.getType() == RecommendDashTypeEnum.RECENTLY_VISIT.ordinal()) {
            ret = getRecentlyVisitList(recommendDashQuery);

            count = userViewStatisticHandler.countRecentlyVisitDash(AuthInterceptor.getUserId());

        } else if (queryPara.getType() == RecommendDashTypeEnum.FAVORITE_LIST.ordinal()) {

            PageQuery<NameQuery> pageQuery = new PageQuery<>();
            NameQuery nameQuery = new NameQuery();
            pageQuery.setPageSize(recommendDashQuery.getPageSize());
            pageQuery.setPageIndex(recommendDashQuery.getPageIndex());
            pageQuery.setQueryPara(nameQuery);
            List<DashHasUserRelaDO> searchDash2Top = dashHandler.searchTopDash(pageQuery);
            ret = Instance.ofNullable(searchDash2Top).stream().map(x -> new TopDashDTO(x.getId(), x.getName(), x.getTop())).toList();
            count = dashHandler.countTopDash(pageQuery);

        } else if (queryPara.getType() == RecommendDashTypeEnum.ALL_CAN_ACCESS.ordinal()) {

            Set<String> allDashHave = dashHandler.getAllDashHave(AuthInterceptor.getUserId());
            count = allDashHave.size();
            if (count > 0) {
                List<String> dashList = allDashHave.stream().skip(recommendDashQuery.getStartIndex()).limit(recommendDashQuery.getPageSize()).toList();
                List<DashHasUserRelaDO> dashHasUserRelaDOS = dashHandler.listDashRelaBy(dashList, AuthInterceptor.getUserId());
                ret = dashHasUserRelaDOS.stream().map(x -> new TopDashDTO(x.getId(), x.getName(), x.getTop())).toList();
            }

        } else {

            PageQuery<BasePara> pq = new PageQuery<>();
            pq.setPageIndex(recommendDashQuery.getPageIndex());
            pq.setPageSize(recommendDashQuery.getPageSize());
            List<DashHasUserRelaDO> dashDOS = dashHandler.searchByOwner(pq);

            ret = Instance.ofNullable(dashDOS).stream().map(x -> new TopDashDTO(x.getId(), x.getName(), x.getTop())).collect(Collectors.toList());
            count = dashHandler.countByOwner(new PageQuery<>());
        }

        return ResponseObject.success(new PageResult<>(count, ret));
    }

    @NotNull
    private List<TopDashDTO> getTopUsedList(PageQuery<RecommendDashQuery> recommendDashQuery) {
        RecommendDashQuery queryPara = recommendDashQuery.getQueryPara();
        List<DashUserViewStatisticDO> topUserViewDash = userViewStatisticHandler.getTopUserViewDash(recommendDashQuery.getUserId(), null, recommendDashQuery.getStartIndex(), recommendDashQuery.getPageSize());
        List<TopDashDTO> dashList = Instance.ofNullable(topUserViewDash).stream().map(x -> {
            DashDO dashById = dashDAO.getDashById(x.getRelatedId());
            TopDashDTO idAndName = new TopDashDTO(x.getRelatedId(), null != dashById ? dashById.getName() : null, x.getTop());

            return idAndName;
        }).collect(Collectors.toList());
        return dashList;
    }

    private List<TopDashDTO> getRecentlyVisitList(PageQuery<RecommendDashQuery> recommendDashQuery) {
        RecommendDashQuery queryPara = recommendDashQuery.getQueryPara();
        List<DashUserViewStatisticDO> topUserViewDash = userViewStatisticHandler.getRecentlyVisitDash(recommendDashQuery.getUserId(), recommendDashQuery.getStartIndex(), recommendDashQuery.getPageSize());
        List<TopDashDTO> dashList = Instance.ofNullable(topUserViewDash).stream().map(x -> {
            TopDashDTO idAndName = new TopDashDTO(x.getRelatedId(), x.getName(), null);
            return idAndName;
        }).collect(Collectors.toList());
        return dashList;
    }

    public ResponseObject migrationServiceMonitor(String serviceMonitorJson) {
        authService.hasSuchRole(AuthInterceptor.getUserId(), null, RoleTypeEnum.systemMaintainer.name());

        if (StringUtils.isEmpty(serviceMonitorJson)) {
            return ResponseObject.fail("service monitor json is null");
        }

        Set<String> ret;
        try {
            MigrationServiceMonitor migrationServiceMonitor = JacksonUtils.getObjectMapper().readValue(serviceMonitorJson, MigrationServiceMonitor.class);
            ret = dashHandler.migrationServiceMonitor(migrationServiceMonitor);

        } catch (Exception e) {
            log.error("migrate service monitor dash error", e);
            return ResponseObject.fail("parse service monitor json error");
        }

        return ResponseObject.success(ret);
    }

    public ResponseObject fixDashTenantId(BasePara basePara) {
        int count = dashHandler.fixDashTenantId();

        return ResponseObject.success(count);
    }

}

