package us.zoom.cube.site.lib.output.metric;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class LineChartOut {

    /**
     * x
     */
    private Map<String, List<Map<String, Object>>> lineData;
    /**
     * x
     */
    private String XField;

    /**
     * y，
     */
    private List<String> YFields;


    public LineChartOut(Map<String, List<Map<String, Object>>> lineData, String XField, List<String> YFields) {
        this.lineData=lineData;
        this.XField=XField;
        this.YFields=YFields;
    }


    public Map<String,List<Map<String, Object>>>  getLineData() {
        return lineData;
    }

    public void setLineData(Map<String,List<Map<String, Object>>> lineData) {
        this.lineData = lineData;
    }

    public String getXField() {
        return XField;
    }

    public void setXField(String XField) {
        this.XField = XField;
    }

    public List<String> getYFields() {
        return YFields;
    }

    public void setYFields(List<String> YFields) {
        this.YFields = YFields;
    }
}
