package us.zoom.cube.site.core;

import cn.hutool.core.lang.Assert;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.site.infra.utils.IdUtils;
import us.zoom.infra.dao.model.TemplateDO;
import us.zoom.infra.dao.service.TemplateDAO;

import java.util.Collections;
import java.util.List;
import java.util.regex.Matcher;

import static us.zoom.infra.utils.RegexConstants.JAVA_VARIABLE_PATTERN;

/**
 * @Author: <PERSON>
 * @Date:12/14/2022 17:05
 * @Description:
 */
@Component
@RequiredArgsConstructor
public class TemplateHandlerV2 {

    private final TemplateDAO templateDAO;

    public String addTemplate(TemplateDO template) {
        Assert.notNull(template, "template can't be null");
        String name = template.getName();
        Assert.isTrue(StringUtils.isNotEmpty(name), "template name can't be null");

        template.setId(IdUtils.generateId());
        checkTemplateName(name);

        templateDAO.addTemplate(template);
        return template.getId();
    }

    private static void checkTemplateName(String name) {
        Matcher matcher = JAVA_VARIABLE_PATTERN.matcher(name);
        Assert.isTrue(matcher.matches(), "template name " + name + " is illegal");
    }

    public TemplateDO getByName(String templateName) {
        Assert.isTrue(StringUtils.isNotEmpty(templateName), "template name can't be empty");

        TemplateDO templateDO = templateDAO.findByName(templateName);
        return templateDO;
    }

    public TemplateDO getById(String templateId) {
        Assert.isTrue(templateId != null, "template id can't be empty");

        TemplateDO templateDO = templateDAO.findById(templateId);
        return templateDO;
    }

    public void updateTemplate(TemplateDO updateTemplate) {
        Assert.isTrue(updateTemplate != null, "updateTemplate can't be empty");

        checkTemplateName(updateTemplate.getName());
        templateDAO.updateTemplate(updateTemplate);
    }

    public List<TemplateDO> listByIds(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return templateDAO.listByIds(ids);
    }

    public List<TemplateDO> listByNames(List<String> names) {
        if (CollectionUtils.isEmpty(names)) {
            return Collections.emptyList();
        }
        return templateDAO.listByNames(names);
    }

    public void deleteById(String id) {
        templateDAO.deleteById(id);
    }

}
