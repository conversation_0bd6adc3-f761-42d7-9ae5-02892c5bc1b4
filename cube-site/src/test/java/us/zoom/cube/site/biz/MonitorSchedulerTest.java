package us.zoom.cube.site.biz;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import us.zoom.cube.site.core.ServerHandler;
import us.zoom.cube.site.core.TenantHandler;
import us.zoom.cube.site.core.monitor.MonitorScheduler;

public class MonitorSchedulerTest  extends  BaseTest {

    @Autowired
    private MonitorScheduler monitorScheduler;

    @Autowired
    private ServerHandler serverHandler;
    @Autowired
    private TenantHandler tenantHandler;

    @Test
    public void test(){
//        monitorScheduler.processHeart(ServerTypeEnum.agent);
//        serverHandler.offlineServer(1);
//        tenantHandler.recoryNotice(12);
//        monitorScheduler.processHeart(ServerTypeEnum.agent);
//        monitorScheduler.processHeart(ServerTypeEnum.agent);
//        monitorScheduler.processHeart(ServerTypeEnum.agent);
//        monitorScheduler.processHeart(ServerTypeEnum.agent);
//        monitorScheduler.processHeart(ServerTypeEnum.agent);

    }
}
