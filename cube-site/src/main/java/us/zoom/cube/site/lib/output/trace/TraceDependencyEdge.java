package us.zoom.cube.site.lib.output.trace;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TraceDependencyEdge {
    private String parentId;
    private String childId;
    private Long p50;    // nano time
    private Long p75;
    private Long p90;
    private Long p95;
    private Long p99;
    private Double errorRate;
    private Double callRate; // per second
} 