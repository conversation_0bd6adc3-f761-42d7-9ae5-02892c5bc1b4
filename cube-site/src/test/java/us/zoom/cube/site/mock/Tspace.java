package us.zoom.cube.site.mock;

public class Tspace {

    public static void main(String []gs){
        System.out.println("es01-->"+Math.abs("es01".hashCode()));
        System.out.println("va1-->"+Math.abs("va1".hashCode()));
        System.out.println("web-->"+Math.abs("web".hashCode()));
        System.out.println("h1-->"+Math.abs("h1".hashCode()));
        System.out.println("us01-->"+Math.abs("us01".hashCode()));
        System.out.println("va2-->"+Math.abs("va2".hashCode()));
        System.out.println("pbx-->"+Math.abs("pbx".hashCode()));
        System.out.println("h2-->"+Math.abs("h2".hashCode()));

    }
}
