package us.zoom.cube.site.core.model.ad;

import lombok.Data;
import us.zoom.cube.sdk.annotation.CubeMonitorLog;
import us.zoom.cube.sdk.annotation.Field;
import us.zoom.cube.sdk.annotation.Tag;

/**
 * <AUTHOR>
 * @date 2024-04-09 13:50
 */
@Data
@CubeMonitorLog(measure = "AdCfgCoverMetric")
public class AdCfgCoverMetric {
    @Tag
    private String host;
    @Tag
    private String ip;
    @Tag
    private String serviceName;
    @Field
    private Integer metricCount;
    @Field
    private Integer modelCfgCount;
    @Field
    private Integer alarmCfgCount;
    @Field
    private Integer alarmCfgEnabledCount;
    @Field
    private Integer alarmCfgDisabledCount;
    @Field
    private Integer trainCount;
    @Field
    private Integer completedCount;
    @Field
    private Integer trainingCount;
    @Field
    private Integer initialisingCount;
    @Field
    private Integer failCount;
    @Field
    private String modelCfgList;
    @Field
    private String alarmCfgList;

    public AdCfgCoverMetric(String host, String ip, String serviceName, Integer metricCount, Integer modelCfgCount,
                            Integer alarmCfgCount, Integer alarmCfgEnabledCount, Integer alarmCfgDisabledCount,
                            Integer trainCount, Integer initialisingCount, Integer trainingCount, Integer completedCount,
                            Integer failCount, String modelCfgList, String alarmCfgList) {
        this.host = host;
        this.ip = ip;
        this.serviceName = serviceName;
        this.metricCount = metricCount;
        this.modelCfgCount = modelCfgCount;
        this.alarmCfgCount = alarmCfgCount;
        this.alarmCfgEnabledCount = alarmCfgEnabledCount;
        this.alarmCfgDisabledCount = alarmCfgDisabledCount;
        this.trainCount = trainCount;
        this.initialisingCount = initialisingCount;
        this.trainingCount = trainingCount;
        this.completedCount = completedCount;
        this.failCount = failCount;
        this.modelCfgList = modelCfgList;
        this.alarmCfgList = alarmCfgList;
    }
}
