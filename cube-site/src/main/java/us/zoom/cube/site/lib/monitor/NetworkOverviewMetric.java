package us.zoom.cube.site.lib.monitor;

import us.zoom.cube.sdk.annotation.CubeMonitorLog;
import us.zoom.cube.sdk.annotation.Field;
import us.zoom.cube.sdk.annotation.Tag;

@CubeMonitorLog(measure = "networkOverviewMetric")
public class NetworkOverviewMetric {
    @Tag
    private String host;
    @Tag
    private String type;
    @Field
    private String error;

    public String getHost() {
        return host;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getError() {
        return error;
    }

    public void setError(String error) {
        this.error = error;
    }

    public NetworkOverviewMetric(String host, String type, String error) {
        this.host = host;
        this.type = type;
        this.error = error;
    }

    public NetworkOverviewMetric() {
    }
}
