package us.zoom.cube.site.api.web.alarm.group;

import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import us.zoom.cube.site.biz.alarm.group.AlarmGroupService;
import us.zoom.cube.site.lib.ResponseObject;
import us.zoom.cube.site.lib.input.alarm.group.AddAlarmList2Group;
import us.zoom.cube.site.lib.input.alarm.group.AlarmGroupInput;
import us.zoom.cube.site.lib.query.*;

@RestController
@RequestMapping("/api/alarm/alarmGroup")
public class AlarmGroupController {

    @Autowired
    private AlarmGroupService alarmGroupService;

    @RequestMapping(value = "/showAlarmGroupList", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject showAlarmGroupList(@Valid @RequestBody PageQuery<AlarmGroupQuery> pageQuery) throws Exception {
        return alarmGroupService.showAlarmGroupList(pageQuery);
    }

    @RequestMapping(value = "/getAlarmGroupById", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject getAlarmGroupById(@Valid @RequestBody AlarmGroupIdQuery alarmGroupIdQuery) throws Exception {
        return alarmGroupService.getAlarmGroupById(alarmGroupIdQuery);
    }

    @RequestMapping(value = "/delAlarmGroupById", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject delAlarmGroupById(@Valid @RequestBody AlarmGroupIdQuery alarmGroupIdQuery) throws Exception {
        return alarmGroupService.delAlarmGroupById(alarmGroupIdQuery);
    }

    @RequestMapping(value = "/addAlarmGroup", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject addAlarmGroup(@Valid @RequestBody AlarmGroupInput alarmGroupInput) throws Exception {
        return alarmGroupService.addAlarmGroup(alarmGroupInput);
    }

    @RequestMapping(value = "/addAlarmGroup/V2", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject addAlarmGroupV2(@Valid @RequestBody AlarmGroupInput alarmGroupInput) throws Exception {
        return alarmGroupService.addAlarmGroupV2(alarmGroupInput);
    }

    @RequestMapping(value = "/updateAlarmGroup", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject updateAlarmGroup(@Valid @RequestBody AlarmGroupInput alarmGroupInput) throws Exception {
        return alarmGroupService.updateAlarmGroup(alarmGroupInput, AlarmGroupService.V1);
    }

    @RequestMapping(value = "/updateAlarmGroup/V2", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject updateAlarmGroupV2(@Valid @RequestBody AlarmGroupInput alarmGroupInput) throws Exception {
        return alarmGroupService.updateAlarmGroupV2(alarmGroupInput);
    }

    @RequestMapping(value = "/listService", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject listService(@Valid @RequestBody AlarmGroupServiceQuery serviceQuery) throws Exception {
        return alarmGroupService.listService(serviceQuery);
    }


    @RequestMapping(value = "/listAlarm", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject listAlarm(@Valid @RequestBody AlarmGroupAlarmQuery alarmQuery) throws Exception {
        return alarmGroupService.listAlarm(alarmQuery);
    }

    @RequestMapping(value = "/listGroupTag", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject listAlarm(@Valid @RequestBody AlarmGroupTagQuery alarmGroupTagQuery) throws Exception {
        return alarmGroupService.listGroupTag(alarmGroupTagQuery);
    }

    @RequestMapping(value = "/showAlarmGroupSimpleList", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject showAlarmGroupList(@Valid @RequestBody AlarmGroupNameQuery alarmGroupNameQuery) throws Exception {
        return alarmGroupService.showAlarmGroupSimpleList(alarmGroupNameQuery);
    }

    @RequestMapping(value = "/addAlarmList", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject addAlarmList(@Valid @RequestBody AddAlarmList2Group addAlarmList2Group) throws Exception {
        return alarmGroupService.addAlarmList(addAlarmList2Group);
    }

    @RequestMapping(value = "/listGroupTagV2", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject listGroupTag(@Valid @RequestBody AlarmGroupTagQuery alarmGroupTagQuery) throws Exception {
        return alarmGroupService.listGroupTagV2(alarmGroupTagQuery);
    }

    @RequestMapping(value = "/listTagAndFiled", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject listGroupTagAndFiled(@Valid @RequestBody AlarmGroupTagQuery alarmGroupTagQuery) throws Exception {
        return alarmGroupService.listTagAndFiled(alarmGroupTagQuery);
    }
}
