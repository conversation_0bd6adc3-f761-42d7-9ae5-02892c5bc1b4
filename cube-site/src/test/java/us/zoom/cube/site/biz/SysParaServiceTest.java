package us.zoom.cube.site.biz;

import org.apache.commons.collections.CollectionUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import us.zoom.cube.site.lib.IdPara;
import us.zoom.cube.site.lib.ResponseObject;
import us.zoom.cube.site.lib.input.SysParaInput;
import us.zoom.cube.site.lib.output.PageResult;
import us.zoom.cube.site.lib.query.PageQuery;
import us.zoom.cube.site.lib.query.SysParaQuery;
import us.zoom.infra.dao.model.SysParaDO;
import us.zoom.infra.thread.ThreadLocalStore;

import java.util.List;

public class SysParaServiceTest extends  BaseTest {


    @Autowired
    private  SysParaService sysParaService;
    String user1="82b4e894-edc7-47b8-ae99-58b6cbf58326";
    String user2="2565ab89-d345-49e4-9548-9066da3001db";
    String clusterId2="clusterId2";
    String clusterId1="clusterId1";
    String type="testType";

    String us04="us04";
    String us05="us05";
    String va2="VA2";
    String tenantId="0ab0fa30-cbdc-456f-89c3-dc5188620edb";

    @Test
    public void testOut() {
        System.out.println("first one:" + sysParaService.isEditableByOut("closeAgentOfflineMetricReport", "closeAgentOfflineMetricReport"));
        System.out.println("second one:" + sysParaService.isEditableByOut("closeAgentOfflineMetricReport", "closeAgentOfflineMetric"));
        System.out.println("third one:" + sysParaService.isEditableByOut("canyonTestType", "canyonPK_1"));
    }


    @Test
    public void test(){

        ThreadLocalStore.setUserInfoLocal(user1);
        ThreadLocalStore.setTenantInfoLocal(tenantId);

        //query and add
        PageQuery<SysParaQuery> pageQuery=new PageQuery();
        SysParaQuery sysParaQuery=new SysParaQuery();
        sysParaQuery.setType(type);
        sysParaQuery.setParaKey(us04);
        pageQuery.setQueryPara(sysParaQuery);
        ResponseObject responseObject=sysParaService.searchSysPara(pageQuery);
        if(null == responseObject || ((PageResult<SysParaDO>)responseObject.getData())== null || CollectionUtils.isEmpty(((PageResult<SysParaDO>)responseObject.getData()).getItems())){
            SysParaInput sysParaInput=mockSysParaInput();
            sysParaService.add(sysParaInput);
            sysParaInput.setValue(us05);
            sysParaService.add(sysParaInput);

        }

        responseObject=sysParaService.searchSysPara(pageQuery);
        List<SysParaDO> sysParaDOS=((PageResult<SysParaDO>)(responseObject.getData())).getItems();
        SysParaDO sysParaDO=sysParaDOS.get(0);
        SysParaInput sysParaInput=new SysParaInput();
        BeanUtils.copyProperties(sysParaDO,sysParaInput);
        //edit
        sysParaInput.setValue(us05);
        sysParaInput.setParaKey(us05);
        sysParaInput.setType(clusterId2);
        sysParaService.edit(sysParaInput);

        //getById
        IdPara id=new IdPara();
        id.setId(sysParaInput.getId());
        sysParaDO=(SysParaDO) sysParaService.getById(id).getData();
        Assertions.assertTrue(sysParaDO.getType().equals(clusterId2));
        Assertions.assertTrue(sysParaDO.getParaKey().equals(us05));
        Assertions.assertTrue(sysParaDO.getValue().equals(us05));


        sysParaInput.setType(type);
        sysParaService.edit(sysParaInput);

        //listByType
        pageQuery.getQueryPara().setType(type);
        responseObject=sysParaService.listByType(sysParaQuery);
        sysParaDOS=((PageResult<SysParaDO>)(responseObject.getData())).getItems();

        boolean equals=isTypeTheSame(sysParaDOS);
        Assertions.assertTrue(equals);

        for(SysParaDO sysParaDO1:sysParaDOS){
            id.setId(sysParaDO1.getId());
            sysParaService.delete(id);
        }


    }

    private boolean isTypeTheSame(List<SysParaDO> sysParaDOS) {
        boolean equals=true;
        for(int i=0;i<sysParaDOS.size();i++){
            if((i+1)>=sysParaDOS.size()){
                return equals;
            }
            equals=sysParaDOS.get(i).getType().equals(sysParaDOS.get(i+1).getType());
            if(!equals){
                return false;
            }
        }
        return equals;
    }

    private SysParaInput mockSysParaInput() {
        SysParaInput sysParaInput=new SysParaInput();
        sysParaInput.setType(type);
        sysParaInput.setParaKey(us04);
        sysParaInput.setValue(us04);
        return sysParaInput;
    }


}
