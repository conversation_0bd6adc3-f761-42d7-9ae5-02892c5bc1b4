package us.zoom.cube.site.core.model.ad;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-08-07 10:01
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AdMetricsCfg {
    private String metricsId;
    private String metricsName;
    private Integer aggPeriod;
    private Integer metricsType;
    private String tenantId;
    private String tenantName;
    private List<AdApiCfgDate> adCfgDates;
    private List<AdAlarmCfg> adAlarmCfgList;
}
