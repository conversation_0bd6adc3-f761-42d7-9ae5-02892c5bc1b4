package us.zoom.cube.site.lib.output.clickhouse;

/**
 * @author: <PERSON>
 * @date: 2022/9/28 11:10
 */
public class StillInfluxVariable {

    private String name;
    private String id;

    public StillInfluxVariable(){

    }

    public String getName() {
        return name;
    }

    public StillInfluxVariable setName(String name) {
        this.name = name;
        return this;
    }

    public String getId() {
        return id;
    }

    public StillInfluxVariable setId(String id) {
        this.id = id;
        return this;
    }
}
