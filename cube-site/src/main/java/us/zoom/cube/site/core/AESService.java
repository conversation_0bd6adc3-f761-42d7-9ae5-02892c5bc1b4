package us.zoom.cube.site.core;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.EnvironmentAware;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;
import us.zoom.cloud.secrets.spring.annotation.SecretValue;
import us.zoom.infra.utils.AESUtils;

/**
 * @Author: <EMAIL>
 * @ModuleOwner: <EMAIL>
 * @Date: 04/21/2022 14:51
 * @Description:
 */
@Component
@Slf4j
@Configuration
public class AESService implements EnvironmentAware {

    /**
     * store in csms
     */
    @SecretValue("aes.key")
    private String aesKey;

    private Environment environment;


    @Override
    public void setEnvironment(Environment environment) {
        this.environment = environment;
    }

    /**
     *
     * @param input
     * @return
     * @throws Exception
     */
    public  String encrypt( String input) throws Exception{
        return AESUtils.encrypt(input, aesKey);
    }

    /**
     *
     * @param input
     * @return
     * @throws Exception
     */
    public  String decrypt(String input ) throws Exception{
        return AESUtils.decrypt(input, aesKey);
    }

    public void setAesKey(String aesKey) {
        this.aesKey = aesKey;
    }

}
