package us.zoom.cube.site;

import com.google.common.collect.Maps;
import us.zoom.jsqlparser.JSQLParserException;
import us.zoom.jsqlparser.expression.BinaryExpression;
import us.zoom.jsqlparser.expression.IntervalExpression;
import us.zoom.jsqlparser.expression.StringValue;
import us.zoom.jsqlparser.parser.CCJSqlParserManager;
import us.zoom.jsqlparser.statement.select.PlainSelect;
import us.zoom.jsqlparser.statement.select.Select;
import us.zoom.jsqlparser.statement.select.SelectBody;
import us.zoom.jsqlparser.statement.select.SelectExpressionItem;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import us.zoom.infra.clickhouse.*;

import java.io.StringReader;
import java.util.HashMap;
import java.util.Map;


/**
 * @author: <PERSON>
 * @date: 2022/9/22 09:27
 */

@ExtendWith(MockitoExtension.class)
public class TestSqlTranslate {

    public static CCJSqlParserManager parserManager = new CCJSqlParserManager();


    @Test
    public void translate(){
        String ori = "  SELECT mean(\"used\")/1024/1024 AS \"mean_used_gb\" FROM \"infra_monitor_cube-clickhouse\".\"autogen\".\"disk\" WHERE time > :dashboardTime: AND time < :upperDashboardTime: AND ( \"clusterId\"='cubePrimeClickhouse-2' )AND ( \"device\"='/dev/nvme1n1p1' ) GROUP BY time(:interval:), \"device\", \"host\" ";
        SqlPlaceholder sqlPlaceholder=new SqlPlaceholder();
        String s = ClickhouseSqlUtil.influxQL2ChSql(ClickhouseSqlUtil.doPlaceholder(ori,sqlPlaceholder), new QueryMonitor());
        String ret= ClickhouseSqlUtil.revertPlaceholder(s,sqlPlaceholder);
        System.out.println(ret);
    }

    @Test
    public void complexWhere(){
        String ori="select \"clusterId\",sum(\"value\")sum_value from(\n" +
                "select \"clusterId\",\"ipPublic\",\"name\",any(\"value\")value  from(\n" +
                "select \"clusterId\",\"ipPublic\",\"name\",\"value\",time from \"Meeting_Web_logsystem-kafka\".kafka_server_replica\n" +
                "where time>subtractDays(now(), 5) order by time desc )t1 group by \"clusterId\",\"ipPublic\",\"name\"  )t2 where value>0 group by \"clusterId\"";
        SqlPlaceholder sqlPlaceholder=new SqlPlaceholder();
        String s = ClickhouseSqlUtil.influxQL2ChSql(ClickhouseSqlUtil.doPlaceholder(ori,sqlPlaceholder), new QueryMonitor());
        String ret= ClickhouseSqlUtil.revertPlaceholder(s,sqlPlaceholder);
        System.out.println(ret);
    }

    @Test
    public void simpleJoin()  throws JSQLParserException {
        String sql="select b.a from b join c on b.a=c.a";
        us.zoom.jsqlparser.statement.Statement statement = parserManager.parse(new StringReader(sql));
        if(statement instanceof Select) {
            SelectBody selectBody = ((Select) statement).getSelectBody();
            if (selectBody instanceof PlainSelect) {
                SelectSqlPartFinder spf = new SelectSqlPartFinder(selectBody);
                spf.parse();

            }
        }
    }

    @Test
    public void testAddEquals() throws JSQLParserException {
        String ori = "SELECT   avg(\"hi\") AS \"avg_hi\",   toStartOfInterval (time, INTERVAL :INTERVAL:) AS time,   \"host\",   \"clusterId\" FROM   \"Infra_Monitor_Cube_Site\".\"cpu\" WHERE   time > :dashboardTime:   AND time < :upperDashboardTime: GROUP BY   time,   \"host\",   \"clusterId\" ORDER BY   time";
        SqlPlaceholder placeholder = new SqlPlaceholder();
        String sql = ClickhouseSqlUtil.doPlaceholder(ori, placeholder);
        us.zoom.jsqlparser.statement.Statement statement = parserManager.parse(new StringReader(sql));

        HashMap<String, Object> param = Maps.newHashMap();
        param.put("clusterId", "us01");
        param.put("cpu", 1);
        param.put("memory", 2.0);

        if (statement instanceof Select) {
            SelectBody selectBody = ((Select) statement).getSelectBody();
            if (selectBody instanceof PlainSelect) {
                ClickhouseSqlUtil.addEquals((PlainSelect) selectBody, param);
            }
        }

        String ret = statement.toString();

        for (Map.Entry<String, String> entry : placeholder.getMap().entrySet()) {
            ret = ret.replace(entry.getKey(), entry.getValue());
        }

        Assertions.assertEquals("SELECT avg(\"hi\") AS \"avg_hi\", toStartOfInterval(time, INTERVAL :INTERVAL:) AS time, \"host\", \"clusterId\" FROM \"Infra_Monitor_Cube_Site\".\"cpu\" WHERE clusterId = 'us01' AND cpu = 1 AND memory = 2.0 AND time > :dashboardTime: AND time < :upperDashboardTime: GROUP BY time, \"host\", \"clusterId\" ORDER BY time", ret);

    }


    @Test
    public void join() throws JSQLParserException {
        String ori="select sum(a),toStartOfInterval(time, INTERVAL :INTERVAL:) AS time from (select j from k where l=:l: )t1 global join (select c from d where d='1' and (g>:donot: or h in ('1','2')) and i like '%:pp:%')t2 on t1.a=t2.c where 2=2 and f!=:donot: and time > :dashboardTime: ";
        SqlPlaceholder placeholder = new SqlPlaceholder();
        String sql = ClickhouseSqlUtil.doPlaceholder(ori, placeholder);
        us.zoom.jsqlparser.statement.Statement statement = parserManager.parse(new StringReader(sql));
        Map<String,Object> param1=new HashMap<>();
        param1.put("type","interval");
        param1.put("value","5 MINUTE");
        Map<String,Object> param2=new HashMap<>();
        param2.put("type","string");
        param2.put("value","lvaluein");
        Map<String,Object> param3=new HashMap<>();
        param3.put("type","string");
        param3.put("value",null);
        Map<String,Object> param4=new HashMap<>();
        param4.put("type","string");
        param4.put("value","pp_value");
        Map<String, ClickhouseVariable>cvs=new HashMap<>();
        cvs.put("INTERVAL",new ClickhouseVariable("INTERVAL",param1));
        cvs.put("l",new ClickhouseVariable("l",param2));
        cvs.put("donot",new ClickhouseVariable("donot",param3));
        cvs.put("pp",new ClickhouseVariable("pp",param4));
        if(statement instanceof Select) {
            SelectBody selectBody = ((Select) statement).getSelectBody();
            if (selectBody instanceof PlainSelect) {
                SelectSqlPartFinder spf=new SelectSqlPartFinder(selectBody);
                spf.parse();
                for(SelectExpressionItem ps:spf.getSelectExpressionItems()){
                    ClickhouseSqlUtil.replaceTimeInterval(ps, (IntervalExpression) new ClickhouseVariable("INTERVAL",param1).getValueWithType());
                }
                for(BinaryExpression b:spf.getConditions()){
                    ClickhouseSqlUtil.replaceWhereCondition(selectBody,b,cvs);
                }
            }
            Assertions.assertEquals("SELECT sum(a), toStartOfInterval(time, INTERVAL 5 MINUTE) AS time FROM (SELECT j FROM k WHERE l = 'lvaluein') t1 GLOBAL JOIN (SELECT c FROM d WHERE d = '1' AND (1 = 1 OR h IN ('1', '2')) AND i LIKE '%pp_value%') t2 ON t1.a = t2.c WHERE 2 = 2 AND 1 = 1 AND time > _dashboardTime_",
                    statement.toString());
         }
    }

    @Test
    public void function() throws JSQLParserException {
        String ori="SELECT uniqExact((exceptionType, codeLineno, codeNamespace, codeFunction)) AS count FROM \"cube_trace\".\"error_log_metrics_region_agg\" WHERE clusterId = ':cluster:' AND regionId = :region: AND time >= toDateTime(:start: / 1000) AND time <= toDateTime(:end: / 1000)";
        SqlPlaceholder placeholder = new SqlPlaceholder();
        String sql = ClickhouseSqlUtil.doPlaceholder(ori, placeholder);
        us.zoom.jsqlparser.statement.Statement statement = parserManager.parse(new StringReader(sql));
        Map<String,Object> param1=new HashMap<>();
        param1.put("type","string");
        param1.put("value","main");
        Map<String,Object> param2=new HashMap<>();
        param2.put("type","string");
        param2.put("value",null);
        Map<String,Object> param3=new HashMap<>();
        param3.put("type","number");
        param3.put("value",10000000);
        Map<String,Object> param4=new HashMap<>();
        param4.put("type","number");
        param4.put("value",1000000000);
        Map<String, ClickhouseVariable>cvs=new HashMap<>();
        cvs.put("cluster",new ClickhouseVariable("cluster",param1));
        cvs.put("region",new ClickhouseVariable("region",param2));
        cvs.put("start",new ClickhouseVariable("start",param3));
        cvs.put("end",new ClickhouseVariable("end",param4));
        if(statement instanceof Select) {
            SelectBody selectBody = ((Select) statement).getSelectBody();
            if (selectBody instanceof PlainSelect) {
                SelectSqlPartFinder spf=new SelectSqlPartFinder(selectBody);
                spf.parse();
                for(BinaryExpression b:spf.getConditions()){
                    ClickhouseSqlUtil.replaceWhereCondition(selectBody,b,cvs, true);
                }
            }
            Assertions.assertEquals("SELECT uniqExact((exceptionType, codeLineno, codeNamespace, codeFunction)) AS count FROM \"cube_trace\".\"error_log_metrics_region_agg\" WHERE clusterId = 'main' AND 1 = 1 AND time >= toDateTime(10000000 / 1000) AND time <= toDateTime(1000000000 / 1000)",
                    statement.toString());
        }
    }

    @Test
    public void testTimePlain() throws JSQLParserException {
        String ori = "select sum(a),toStartOfInterval(time, INTERVAL 5 MINUTE) AS time    from (select j from k )t1 global join (select c from d where time < '2022-09-06 23:00:00')t2 on t1.a=t2.c where  time > 1234567890 AND time < subtractMinutes(now(), 30)  ";
        PlainSelect plainSelect = ClickhouseSqlUtil.getPlainSelect(ori);

        SelectSqlPartFinder selectSqlPartFinder = new SelectSqlPartFinder(plainSelect);
        selectSqlPartFinder.parse();
        System.out.println();
    }

        @Test
    public void time() throws JSQLParserException {
        String ori="select sum(a), time from (select j from k )t1 global join (select c from d where time < :dashboardTime2:)t2 on t1.a=t2.c where  time > :dashboardTime: AND time < :upperDashboardTime:  ";
        SqlPlaceholder placeholder = new SqlPlaceholder();
        String sql = ClickhouseSqlUtil.doPlaceholder(ori, placeholder);
        us.zoom.jsqlparser.statement.Statement statement = parserManager.parse(new StringReader(sql));
        Map<String,Object> param1=new HashMap<>();
        param1.put("type","time");
        param1.put("value","subtractMinutes(now(), 30)");
        Map<String,Object> param2=new HashMap<>();
        param2.put("type","time");
        param2.put("value","1234567890");
        Map<String,Object> param3=new HashMap<>();
        param3.put("type","time");
        param3.put("value","2022-09-06 23:00:00");
        Map<String, ClickhouseVariable>cvs=new HashMap<>();
        cvs.put("upperDashboardTime",new ClickhouseVariable("upperDashboardTime",param1));
        cvs.put("dashboardTime",new ClickhouseVariable("dashboardTime",param2));
        cvs.put("dashboardTime2",new ClickhouseVariable("dashboardTime2",param3));
        if(statement instanceof Select) {
            SelectBody selectBody = ((Select) statement).getSelectBody();
            if (selectBody instanceof PlainSelect) {
                SelectSqlPartFinder spf=new SelectSqlPartFinder(selectBody);
                spf.parse();
//                for(SelectExpressionItem ps:spf.getSelectExpressionItems()){
//                    ClickhouseSqlUtil.replaceTimeInterval(ps, (IntervalExpression) new ClickhouseVariable("INTERVAL",param1).getValueWithType());
//                }
                for(BinaryExpression b:spf.getConditions()){
                    ClickhouseSqlUtil.replaceWhereCondition(selectBody,b,cvs);
                }
            }
            System.out.println(statement);
        }
    }

    @Test
    public void compare() throws JSQLParserException {
        String sql=" SELECT avg(\"us\") AS \"avg_us\", toStartOfInterval(time, INTERVAL 1 MINUTE) AS time, \"ip\" FROM \"Infra_Monitor_Cube-ClickHouse\".\"cpu\" WHERE time > subtractMinutes(now(), 30) GROUP BY time, \"ip\" ORDER BY time ";
        us.zoom.jsqlparser.statement.Statement statement = parserManager.parse(new StringReader(sql));
        if(statement instanceof Select) {
            SelectBody selectBody = ((Select) statement).getSelectBody();
            if (selectBody instanceof PlainSelect) {
                String compareSql = ClickhouseSqlUtil.getCompareSql(sql, "24h");
                System.out.println(compareSql);
            }
        }
    }

    @Test
    public void enhancedReplacement() throws JSQLParserException {
        String sql="select if(a=':a:',1,0) as b from table";
        SqlPlaceholder placeholder = new SqlPlaceholder();
        sql = ClickhouseSqlUtil.doPlaceholder(sql, placeholder);
        Map<String,Object> param1=new HashMap<>();
        param1.put("type","string");
        param1.put("value","aValue");
        Map<String, ClickhouseVariable>cvs=new HashMap<>();
        cvs.put("a",new ClickhouseVariable("a",param1));
        for (Map.Entry<String, ClickhouseVariable> en : cvs.entrySet()) {
            Object valueWithType = en.getValue().getValueWithType();
            if (!(valueWithType instanceof StringValue)) {
                continue;
            }
            String valueName = ClickhouseSqlUtil.toTranslatable(en.getKey());
            sql = sql.replace(valueName, ((StringValue) valueWithType).getValue());
        }
        Assertions.assertEquals("select if(a='aValue',1,0) as b from table",sql);
    }

    @Test
    public void parseArray() throws JSQLParserException {
        String sql="SELECT\n" +
                " divisor,\n" +
                " dividend,\n" +
                " divisor / dividend as res\n" +
                " from\n" +
                "  (\n" +
                "  SELECT\n" +
                "   arrayDifference(groupArray(us)) as divisors ,\n" +
                "   arrayDifference(groupArray(toUnixTimestamp(time))) as dividends\n" +
                "  from\n" +
                "   `rich_test_service`.cpu) array\n" +
                " join divisors as divisor,\n" +
                "  dividends as dividend\n" +
                " where\n" +
                "  dividend != 0";
        us.zoom.jsqlparser.statement.Statement statement = parserManager.parse(new StringReader(sql));
        Assertions.assertEquals(statement.toString(),"SELECT divisor, dividend, divisor / dividend AS res FROM (SELECT arrayDifference(groupArray(us)) AS divisors, arrayDifference(groupArray(toUnixTimestamp(time))) AS dividends FROM `rich_test_service`.cpu) array JOIN divisors AS divisor, dividends AS dividend WHERE dividend != 0");

    }
}
