package us.zoom.cube.site.core;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.lib.common.CubeConstants;
import us.zoom.cube.site.lib.input.DataForwardBatchUpdateField;
import us.zoom.cube.site.lib.query.DataForwardQuery;
import us.zoom.infra.dao.model.DataForwardDO;
import us.zoom.infra.dao.service.DataForwardDAO;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> zheng
 */
@Component
public class DataForwardHandler {
    @Autowired
    private DataForwardDAO dataForwardDAO;

    public List<DataForwardDO> listAll() throws Exception {
        List<DataForwardDO> dataForwardList = new ArrayList<>();
        long counts = dataForwardDAO.queryCounts();
        int pageSize = CubeConstants.CUBE_SQL_BATCH_QUERY_SIZE;
        long pageNum = counts / pageSize + (counts % pageSize == 0 ? 0 : 1);
        for (int i = 1; i <= pageNum; i++) {
            dataForwardList.addAll(dataForwardDAO.listBatch(pageSize * (i - 1), pageSize));
        }
        return dataForwardList;
    }

    public List<DataForwardDO> findByParam(DataForwardQuery dataForwardQuery, int pageIndex, int pageSize) {
        return dataForwardDAO.findByParam(dataForwardQuery.getId(),
                dataForwardQuery.getUnitTagId(),
                dataForwardQuery.getRule(),
                dataForwardQuery.getInputTopic(),
                dataForwardQuery.getInputTopicDcName(),
                dataForwardQuery.getInputTopicPartition(),
                dataForwardQuery.getInputKafkaClusterName(),
                dataForwardQuery.getInputTopicStatus(),
                dataForwardQuery.getGroupId(),
                dataForwardQuery.getThreadCount(),
                dataForwardQuery.getOutputTopic(),
                dataForwardQuery.getOutputTopicDcName(),
                dataForwardQuery.getOutputTopicPartition(),
                dataForwardQuery.getOutputKafkaClusterName(),
                dataForwardQuery.getOutputTopicStatus(),
                dataForwardQuery.getEditor(),
                dataForwardQuery.getCreator(),
                dataForwardQuery.getStatus(),
                dataForwardQuery.getInputClusterId(),
                dataForwardQuery.getOutputClusterId(),
                dataForwardQuery.getSendMode(), pageSize * (pageIndex - 1), pageSize);
    }

    public int countByParam(DataForwardQuery dataForwardQuery) {
        return dataForwardDAO.getCountByParam(dataForwardQuery.getId(),
                dataForwardQuery.getUnitTagId(),
                dataForwardQuery.getRule(),
                dataForwardQuery.getInputTopic(),
                dataForwardQuery.getInputTopicDcName(),
                dataForwardQuery.getInputTopicPartition(),
                dataForwardQuery.getInputKafkaClusterName(),
                dataForwardQuery.getInputTopicStatus(),
                dataForwardQuery.getGroupId(),
                dataForwardQuery.getThreadCount(),
                dataForwardQuery.getOutputTopic(),
                dataForwardQuery.getOutputTopicDcName(),
                dataForwardQuery.getOutputTopicPartition(),
                dataForwardQuery.getOutputKafkaClusterName(),
                dataForwardQuery.getOutputTopicStatus(),
                dataForwardQuery.getEditor(),
                dataForwardQuery.getCreator(),
                dataForwardQuery.getStatus(),
                dataForwardQuery.getInputClusterId(),
                dataForwardQuery.getOutputClusterId(),
                dataForwardQuery.getSendMode());
    }

    public List<DataForwardDO> getByUserName(String userName) {
        return dataForwardDAO.getByUserName(userName);
    }

    public void batchAdd(List<DataForwardDO> dataForwardDOList) {
        if (CollectionUtils.isEmpty(dataForwardDOList)) {
            return;
        }
        dataForwardDAO.batchAdd(dataForwardDOList);
    }

    public void deleteByUnitTagId(String unitTagId) {
        dataForwardDAO.deleteByUnitTagId(unitTagId);
    }

    public DataForwardDO getById(String id) {
        return dataForwardDAO.getById(id);
    }

    public List<DataForwardDO> getByInputOutput(String inputTopic, String inputClusterId,
                                                String outputTopic, String outputClusterId) {
        return dataForwardDAO.getByInputOutput(inputTopic, inputClusterId, outputTopic, outputClusterId);
    }

    public void batchUpdate(List<DataForwardDO> dataForwardDOList) {
        if (CollectionUtils.isEmpty(dataForwardDOList)) {
            return;
        }
        dataForwardDAO.batchUpdate(dataForwardDOList);
    }

    public void batchDelete(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        dataForwardDAO.batchDelete(ids);
    }

    public void batchUpdateField(DataForwardBatchUpdateField input) {
        dataForwardDAO.batchUpdateField(input.getIds(), input.getUnitTagId(), input.getRule(), input.getStatus(), input.getGroupId(), input.getThreadCount(), input.getSendMode());
    }
}
