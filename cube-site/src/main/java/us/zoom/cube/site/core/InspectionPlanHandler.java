package us.zoom.cube.site.core;

import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import us.zoom.infra.dao.model.InspectionPlanDO;
import us.zoom.infra.dao.service.InspectionPlanDAO;

import java.util.List;

/**
 * @Description:
 * @author: <PERSON>
 * @date: 2023/04/11 09:34
 */
@Service
@Slf4j
public class InspectionPlanHandler {

    @Autowired
    private InspectionPlanDAO inspectionPlanDao;

    public int countByTenant(String tenantId) {
        Assert.hasText(tenantId, "tenantId is empty");
        return inspectionPlanDao.countByTenant(tenantId);
    }

    public List<InspectionPlanDO> selectByTenantId(String tenantId, long size, long index) {
        Assert.hasText(tenantId, "tenantId is empty");
        Assert.isTrue(size >= 0, "page size is error");
        Assert.isTrue(index >= 0, "index is error");
        return inspectionPlanDao.selectByTenantId(tenantId, size, index);
    }

    public InspectionPlanDO selectById(@Param("id") String id) {
        Assert.hasText(id, "id is empty");
        return inspectionPlanDao.selectById(id);
    }

    public InspectionPlanDO selectByPlanName(@Param("id") String planName) {
        Assert.hasText(planName, "planName is empty");
        return inspectionPlanDao.selectByName(planName);
    }

    public List<String> selectByLibId(@Param("libId") String libId) {
        Assert.hasText(libId, "libId is empty");
        return inspectionPlanDao.selectByLibId(libId);
    }

    public int add(InspectionPlanDO inspectionPlanDO) {
        return inspectionPlanDao.add(inspectionPlanDO);
    }

    public int update(InspectionPlanDO inspectionPlanDO) {
        return inspectionPlanDao.update(inspectionPlanDO);
    }

    public int updateSelective(InspectionPlanDO inspectionPlanDO) {
        return inspectionPlanDao.updateSelective(inspectionPlanDO);
    }
}
