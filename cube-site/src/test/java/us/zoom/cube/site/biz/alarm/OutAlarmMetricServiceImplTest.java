package us.zoom.cube.site.biz.alarm;


import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ActiveProfiles;
import us.zoom.cube.site.BaseSpringTest;
import us.zoom.cube.site.lib.ResponseObject;
import us.zoom.cube.site.lib.output.alarm.OutAlarmId;
import us.zoom.cube.site.lib.output.metric.MetricInfo;

@ActiveProfiles("perf_for_local")
public class OutAlarmMetricServiceImplTest extends BaseSpringTest  {

    @Autowired
    OutAlarmMetricService outAlarmMetricService;

    @Test
    public void getMetricByAlarmId() {
        OutAlarmId outAlarmId = new OutAlarmId();
        outAlarmId.setAlarmId("fed54106-84ce-4996-b14d-949af3ffdb6d");
        ResponseObject<MetricInfo> metricInfo = outAlarmMetricService.getMetricByAlarmId(outAlarmId);
        Assertions.assertEquals(1, metricInfo.getData().getField().size());
        Assertions.assertEquals(4, metricInfo.getData().getTag().size());
        Assertions.assertEquals("Meeting_Web_nws-nginx", metricInfo.getData().getServiceName());
    }
}