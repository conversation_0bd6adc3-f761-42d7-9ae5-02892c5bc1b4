package us.zoom.cube.site.core.capacity;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import us.zoom.cube.site.biz.SysParaService;
import us.zoom.infra.clickhouse.ChNameEncoder;
import us.zoom.infra.utils.DateUtils;

/**
 * <AUTHOR>
 * @date 2022-05-24 10:45
 */
@Component
@Slf4j
public class CapacitySqlParseService {

    @Autowired
    private SysParaService sysParaService;

    /**
     * sqlMaxTasks
     * @param tenantName
     * @param time
     * @param samplingPeriod
     * @return
     */
    public String sqlMaxTasks(String tenantName, long time, int samplingPeriod, int averageTime ,String tags) {
        String startTime = DateUtils.fromLongToDate(DateUtils.addDay(time, -samplingPeriod));
        String endTime = DateUtils.fromLongToDate(time);
        StringBuffer buildSql = new StringBuffer();
       /* if (StringUtils.equals(sysParaService.getCapacityParasCfg().getCapacityClacCfg().getSpecialrAchitecture(), tenantName)) {
            buildSql.append("SELECT max(us) as max_us,clusterId,zoneName from (select avg(us) as us,toStartOfInterval(time, INTERVAL ")
                    .append(sysParaService.getCapacityParasCfg().getCapacityClacCfg().getAverageTime()).append("minute) AS time ,clusterId,zoneName from ");
            buildSql.append(ChNameEncoder.encode(tenantName)).append(".cpu where").append("( time >= \'").
                    append(startTime).append("\' and time <= \'").
                    append(endTime).append("\') ").append("GROUP BY time,clusterId,zoneName) GROUP BY clusterId,zoneName");
            return buildSql.toString();
        }*/
        buildSql.append("SELECT max(us) as max_us, ").append(tags).append(" from (select median(us) as us,toStartOfInterval(time, INTERVAL ")
                .append(averageTime).append(" minute) AS time ,").append(tags).append(" from ");
        buildSql.append(ChNameEncoder.encode(tenantName)).append(".cpu where").append("( time >= toDateTime('").
                append(startTime).append("') and time <= toDateTime('").
                append(endTime).append("')) ").append("GROUP BY time,").append(tags).append(") GROUP BY ").append(tags);
        return buildSql.toString();
    }

    /**
     * sqlNumTasks
     * @param tenantName
     * @return
     */
    public String sqlNumTasks(String tenantName,String tags) {
        StringBuffer buildSql = new StringBuffer();
        /*if (StringUtils.equals(sysParaService.getCapacityParasCfg().getCapacityClacCfg().getSpecialrAchitecture(), tenantName)) {
            buildSql.append("SELECT COUNT(DISTINCT instanceId) as serverCount,clusterId,zoneName from ");
            buildSql.append(ChNameEncoder.encode(tenantName)).append(".cpu GROUP BY clusterId,zoneName");
            return buildSql.toString();
        }*/
        buildSql.append("SELECT COUNT(DISTINCT instanceId) as serverCount,").append(tags).append(" from ");
        buildSql.append(ChNameEncoder.encode(tenantName)).append(".cpu GROUP BY ").append(tags);
        return buildSql.toString();
    }

}
