package us.zoom.cube.site.lib.output.metric;

import java.util.List;
import java.util.Map;
public class MetricCondition {
    private String id;
    private String aggName;
    private Integer querySourceType;
    private Map<String, List<String>> filters;


    public MetricCondition() {
    }

    public MetricCondition(String id,Integer querySourceType,String aggName, Map<String, List<String>> filters) {
        this.id = id;
        this.querySourceType=querySourceType;
        this.aggName=aggName;
        this.filters = filters;
    }


    public String getAggName() {
        return aggName;
    }

    public void setAggName(String aggName) {
        this.aggName = aggName;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Map<String, List<String>> getFilters() {
        return filters;
    }

    public void setFilters(Map<String, List<String>> filters) {
        this.filters = filters;
    }

    public Integer getQuerySourceType() {
        return querySourceType;
    }

    public void setQuerySourceType(Integer querySourceType) {
        this.querySourceType = querySourceType;
    }
}
