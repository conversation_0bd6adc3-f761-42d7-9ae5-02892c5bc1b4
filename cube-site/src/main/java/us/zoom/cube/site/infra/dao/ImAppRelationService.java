package us.zoom.cube.site.infra.dao;

/**
 * ImApp 关系映射服务接口
 */
public interface ImAppRelationService {
    
    /**
     * 根据 id 查询对应的 to_jid
     * 
     * @param id ImApp 关系 ID
     * @return 对应的 to_jid，如果没找到返回 null
     */
    String getToJidById(String id);
    
    /**
     * 检查 ImApp 关系是否存在
     * 
     * @param id ImApp 关系 ID
     * @return true 如果关系存在且有效
     */
    boolean existsById(String id);
}