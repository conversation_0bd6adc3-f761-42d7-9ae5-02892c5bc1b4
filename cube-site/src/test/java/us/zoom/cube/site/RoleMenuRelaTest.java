package us.zoom.cube.site;


import org.apache.commons.collections.CollectionUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import us.zoom.cube.site.infra.utils.IdUtils;
import us.zoom.infra.dao.model.RoleMenuRelaDO;
import us.zoom.infra.dao.service.RoleMenuRelaDAO;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@SpringBootTest(classes = CubeSiteApplication.class,webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)//springboot
public class RoleMenuRelaTest {

    @Autowired
    private RoleMenuRelaDAO roleMenuRelaDAO;


    @Test
    public void testWhole(){

        String role="test_role";
        String menuRes1="test_menu_res_1";
        String menuRes2="test_menu_res_2";

        roleMenuRelaDAO.deleteRelaByRoleNames(Arrays.asList(role));
        //test bach add rela
        List<RoleMenuRelaDO> relaDOS = new ArrayList<>();
        relaDOS.add(mock(role,menuRes1));
        relaDOS.add(mock(role,menuRes2));

        roleMenuRelaDAO.batchAddRela(relaDOS);

        List<RoleMenuRelaDO>  roleMenuRelaDOS =  roleMenuRelaDAO.listByRoleName(role);

        Assertions.assertTrue(2 == roleMenuRelaDOS.size(),"add or listByRoleName  fail ");

        roleMenuRelaDAO.deleteRelaByMenuResId(menuRes1);
        roleMenuRelaDAO.deleteRelaByMenuResId(menuRes2);

        roleMenuRelaDOS =   roleMenuRelaDAO.listByRoleName(role);
        Assertions.assertTrue(CollectionUtils.isEmpty(roleMenuRelaDOS),"add or listByRoleName  fail ");

    }

    private RoleMenuRelaDO mock(String name, String resId) {
        RoleMenuRelaDO relaDO = new RoleMenuRelaDO();
        relaDO.setId(IdUtils.generateId());
        relaDO.setRoleName(name);
        relaDO.setMenuResId(resId);
        return relaDO;
    }
}
