package us.zoom.cube.site.core.parser.process.core.common.constant;

import com.google.common.collect.Sets;

import java.util.Set;

public class Constants {
    public static final String ENTER = "\n";

    public static final String MEASURE = "measure";
    public static final String MEASURE_CUBE_VER = "cubeVer";
    public static final String MEASURE_TS = "ts";
    public static final String MEASURE_FIELD = "field";
    public static final String MEASURE_HIS_FIELD = "hisField";
    public static final String MEASURE_SUM_FIELD = "sumField";
    public static final String MEASURE_HIS_SCHEMA = "hisSchema";
    public static final String MEASURE_SUM_SCHEMA = "sumSchema";
    public static final String MEASURE_TAG = "tag";

    // zdca message must include: message、labelList、appName and topType, and not null
    public static final String AGENT_MESSAGE = "message";
    public static final String AGENT_APP_NAME = "appName";
    public static final String AGENT_TOP_TYPE = "topType";
    public static final String AGENT_LABEL_LIST = "labelList";

    public static final String AGENT_LOG_STREAM = "logstream";

    public static final String AGENT_TIMESTAMP = "timestamp";
    public static final String AGENT_TS = "ts";
    public static final String AGENT_APP_CLUSTER = "appcluster";
    public static final String AGENT_PATH_TEMPLATE = "path_template";
    public static final String AGENT_HOST = "host";
    public static final String AGENT_IP = "ip";
    public static final String AGENT_INFRA_TYPE = "infraType";
    public static final String AGENT_CLUSTER_ID = "clusterId";
    public static final String AGENT_REGION_ID = "regionId";
    public static final String AGENT_ZONE_NAME = "zoneName";
    public static final String AGENT_INSTANCE_ID = "instanceId";
    public static final String AGENT_IP_PUBLIC = "ipPublic";
    public static final String AGENT_IS_K8S = "isK8S";
    public static final String AGENT_CSP = "csp";
    public static final String AGENT_CELL = "cell";
    public static final String AGENT_STACK = "stack";
    public static final String AGENT_NODE_NAME = "nodeName";
    public static final String AGENT_NAMESPACE = "namespace";
    public static final String AGENT_POD_NAME = "podName";
    public static final String AGENT_CONTAINER_NAME = "containerName";
    public static final String AGENT_CONTAINER_ID = "containerId";
    public static final String AGENT_TENANT = "tenant";

    //LogTypeEnum.AGENT_LOG: clusterId、instanceId、hostName、regionId
    public static final String COMMON_FIELD_CLUSTER_ID = "clusterId";
    public static final String COMMON_FIELD_INSTANCE_ID = "instanceId";
    public static final String COMMON_FIELD_HOST_NAME = "hostName";
    public static final String COMMON_FIELD_REGION_ID = "regionId";

    //base info
    public static final String COMMON_FIELD_TS = "ts";
    public static final String COMMON_FIELD_APP_NAME = "appName";
    public static final String COMMON_FIELD_TOP_TYPE = "topType";
    public static final String COMMON_FIELD_HOST = "host";
    public static final String COMMON_FIELD_IP = "ip";
    public static final String COMMON_FIELD_INFRA_TYPE = "infraType";
    public static final String COMMON_FIELD_ZONE_NAME = "zoneName";
    public static final String COMMON_FIELD_CSP = "csp";
    public static final String COMMON_FIELD_IP_PUBLIC = "ipPublic";
    public static final String COMMON_FIELD_IS_K8S = "isK8S";
    public static final String COMMON_FIELD_PATH_TEMPLATE = "path_template";
    public static final String COMMON_FIELD_CELL = "cell";
    public static final String COMMON_FIELD_STACK = "stack";
    public static final String COMMON_FIELD_NODE_NAME = "nodeName";
    public static final String COMMON_FIELD_NAMESPACE = "namespace";
    public static final String COMMON_FIELD_POD_NAME = "podName";
    public static final String COMMON_FIELD_CONTAINER_NAME = "containerName";
    public static final String COMMON_FIELD_CONTAINER_ID = "containerId";
    public static final String COMMON_FIELD_TENANT = "tenant";

    public static final Set<String> NOT_PUT_JSON = Sets.newHashSet(COMMON_FIELD_TOP_TYPE, COMMON_FIELD_PATH_TEMPLATE);
    public static final Set<String> NOT_PUT_MEASURE = Sets.newHashSet(COMMON_FIELD_TS, COMMON_FIELD_TOP_TYPE, COMMON_FIELD_PATH_TEMPLATE);

    public static final String FIELD_STRING_TYPE = "string";
    public static final String FIELD_NUMBER_TYPE = "number";
    public static final String FIELD_ARRAY_TYPE = "array";

    public static final String STANDARD_MAP_FIELD = "mapField";

    public static final String REFRESH_TAG = "refresh";
    public static final String ASYNC_MQ_TYPE = "cube";
    public static final String KINESIS = "kinesis";
    public static final String UNDER_SCORE = "_";
    public static final String SPOT = ".";
    public static final String SERVICE = "service";
    public static final String AA_ENV = "aaEnv";
    public static final String DF_NAME = "dfName";
    public static final String DF_ID = "dfId";
    public static final String DP_NAME = "dpName";
    public static final String DP_ID = "dpId";
    public static final String UNIT = "unit";
    public static final String INPUT = "input";
    public static final String INPUT_DC = "inputDc";
    public static final String G_ID = "gId";
    public static final String LOG_TYPE = "logType";
    public static final String ALARM = "alarm";
    public static final String CALC = "calc";
    public static final String NAME = "name";
    public static final String ID = "id";
    public static final String SOURCE_TYPE = "sourceType";

    public static final String CACHE_SIZE = "cacheSize";
    public static final String DEL_SIZE = "delSize";

    public static final String DASHBOARD = "dashboard";
}

