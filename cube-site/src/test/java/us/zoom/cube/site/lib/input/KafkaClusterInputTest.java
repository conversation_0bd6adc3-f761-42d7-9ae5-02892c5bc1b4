package us.zoom.cube.site.lib.input;


import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import us.zoom.cube.lib.common.SaslMechanismEnum;

/**
 * @Author: luis.zheng
 * @Date: 2021/8/17 10:22 
 */
public class KafkaClusterInputTest {

    @Test
    public void testCheckNameIsNull() {
        KafkaClusterInput input = createKafkaClusterInput();
        input.setName(null);
        IllegalArgumentException err = Assertions.assertThrows(IllegalArgumentException.class, () -> {
            input.check();
        });
        Assertions.assertEquals(err.getMessage(), "name is null");
    }

    @Test
    public void testCheckNameLength() {
        KafkaClusterInput input = createKafkaClusterInput();
        //30
        input.setName("1234567890" + "1234567890" + "12345678901");
        IllegalArgumentException err1 = Assertions.assertThrows(IllegalArgumentException.class, () -> {
            input.check();
        });
        Assertions.assertEquals(err1.getMessage(), "name length is more than 30");
    }

    @Test
    public void testCheckRegionBlank() {
        KafkaClusterInput input = createKafkaClusterInput();
        input.setRegionId("  ");
        IllegalArgumentException err1 = Assertions.assertThrows(IllegalArgumentException.class, () -> {
            input.check();
        });
        Assertions.assertEquals(err1.getMessage(), "regionId is blank");
    }

    @Test
    public void testCheckRegionLength() {
        KafkaClusterInput input = createKafkaClusterInput();
        input.setRegionId("1234567890" + "1234567890" + "12345678901");
        IllegalArgumentException err2 = Assertions.assertThrows(IllegalArgumentException.class, () -> {
            input.check();
        });
        Assertions.assertEquals(err2.getMessage(), "regionId length is more than 30");
    }

    @Test
    public void testCheckClusterBlank() {
        KafkaClusterInput input = createKafkaClusterInput();
        input.setRegionId("regionId");
        input.setClusterId("  ");
        IllegalArgumentException err3 = Assertions.assertThrows(IllegalArgumentException.class, () -> {
            input.check();
        });
        Assertions.assertEquals(err3.getMessage(), "clusterId is blank");
    }

    @Test
    public void testCheckClusterLength() {
        KafkaClusterInput input = createKafkaClusterInput();
        input.setClusterId("1234567890" + "1234567890" + "12345678901");
        IllegalArgumentException err4 = Assertions.assertThrows(IllegalArgumentException.class, () -> {
            input.check();
        });
        Assertions.assertEquals(err4.getMessage(), "clusterId length is more than 30");
    }

    @Test
    public void testCheckBootstrapServersBlank() {
        KafkaClusterInput input = createKafkaClusterInput();
        input.setBootstrapServersPlain(null);
        input.setBootstrapServersSsl("  ");
        input.setBootstrapServersSasl("  ");
        input.setBootstrapServersSslSasl("  ");
        IllegalArgumentException err1 = Assertions.assertThrows(IllegalArgumentException.class, () -> {
            input.check();
        });
        Assertions.assertEquals(err1.getMessage(), "all bootstrapServers are blank");
    }

    @Test
    public void testCheckBootstrapServersNull() {
        KafkaClusterInput input = createKafkaClusterInput();
        input.setBootstrapServersPlain(null);
        input.setBootstrapServersSsl(null);
        input.setBootstrapServersSasl(null);
        input.setBootstrapServersSslSasl(null);
        IllegalArgumentException err2 = Assertions.assertThrows(IllegalArgumentException.class, () -> {
            input.check();
        });
        Assertions.assertEquals(err2.getMessage(), "all bootstrapServers are blank");
    }

    @Test
    public void testCheckBootstrapServersLength() {
        KafkaClusterInput input = createKafkaClusterInput();
        StringBuilder sb = new StringBuilder();
        for (int i = 1; i < 2000; i++) {
            sb.append("z");
        }
        input.setBootstrapServersPlain(sb.toString());
        input.check();
    }

    @Test
    public void testCheckBootstrapServersTooLength() {
        KafkaClusterInput input = createKafkaClusterInput();
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < 2001; i++) {
            sb.append("z");
        }
        input.setBootstrapServersPlain(sb.toString());
        input.setBootstrapServersSsl(null);
        input.setBootstrapServersSasl(null);
        input.setBootstrapServersSslSasl(null);
        IllegalArgumentException err2 = Assertions.assertThrows(IllegalArgumentException.class, () -> {
            input.check();
        });
        Assertions.assertEquals(err2.getMessage(), "bootstrapServersPlain length is more than 2000");
    }

    @Test
    public void testCheckBootstrapServersOK() {
        KafkaClusterInput input = createKafkaClusterInput();
        input.setBootstrapServersPlain("setBootstrapServersPlain");
        input.setBootstrapServersSsl("");
        input.check();

        input.setBootstrapServersPlain("");
        input.setBootstrapServersSsl("setBootstrapServersSsl");
        input.check();
    }

    @Test
    public void testCheckSaslMechanismIsNull() {
        KafkaClusterInput input = createKafkaClusterInput();
        input.setSaslMechanism(null);
        input.check();
        Assertions.assertEquals(input.getSaslMechanism(), SaslMechanismEnum.PLAIN.name());
    }

    @Test
    public void testCheckSaslMechanismNotExist() {
        KafkaClusterInput input = createKafkaClusterInput();
        input.setSaslMechanism("null");
        input.check();
        Assertions.assertEquals(input.getSaslMechanism(), SaslMechanismEnum.PLAIN.name());
    }

    @Test
    public void testCheckSaslMechanismNotExist1() {
        KafkaClusterInput input = createKafkaClusterInput();
        input.setSaslMechanism("sss");
        input.check();
        Assertions.assertEquals(input.getSaslMechanism(), SaslMechanismEnum.PLAIN.name());
    }

    private KafkaClusterInput createKafkaClusterInput() {
        KafkaClusterInput input = new KafkaClusterInput();
        input.setName("ds01-cluster-name");
        input.setRegionId("VA");
        input.setClusterId("ds01");
        input.setBootstrapServersPlain("setBootstrapServersPlain");
        input.setBootstrapServersSsl("setBootstrapServersSsl");
        input.setSslTruststoreLocation("adsfasdf");
        input.setSslTruststorePassword("adsfasdf");
        input.setSslEndpointIdentificationAlgorithm("");
        input.setSslKeystoreLocation("");
        input.setSslKeystorePassword("encryptStr");
        input.setSslKeyPassword("");
        input.setSaslJaasConfig("");
        input.setSaslMechanism(SaslMechanismEnum.GSSAPI.name());
        return input;
    }
}
