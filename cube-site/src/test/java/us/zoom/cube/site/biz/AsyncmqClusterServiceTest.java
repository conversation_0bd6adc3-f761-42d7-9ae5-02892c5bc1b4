package us.zoom.cube.site.biz;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.BeanUtils;
import org.springframework.util.Assert;
import us.zoom.cube.site.core.AsyncMqQueueHandler;
import us.zoom.cube.site.core.AsyncmqClusterHandler;
import us.zoom.cube.site.infra.utils.IdUtils;
import us.zoom.cube.site.lib.IdPara;
import us.zoom.cube.site.lib.ResponseObject;
import us.zoom.cube.site.lib.input.AsyncmqClusterInput;
import us.zoom.infra.dao.model.AsyncmqClusterDO;
import us.zoom.mq.client.AsyncMQClientConfig;
import us.zoom.mq.client.DefaultAsyncMQ;
import us.zoom.mq.common.Result;
import us.zoom.mq.common.param.LoginParam;
import us.zoom.mq.common.response.LoginResult;

import jakarta.validation.Valid;

/**
 * @Author: luis.zheng
 * @Date: 2021/8/6 2:33
 */
public class AsyncmqClusterServiceTest {

    private AsyncmqClusterHandler asyncmqClusterHandler;
    private AsyncMqQueueHandler asyncMqQueueHandler;
    private AuthService authService;
    private AsyncmqClusterService asyncmqClusterService;

    @BeforeEach
    public void before() {
        asyncmqClusterHandler = Mockito.mock(AsyncmqClusterHandler.class);
        asyncMqQueueHandler = Mockito.mock(AsyncMqQueueHandler.class);
        authService = Mockito.mock(AuthService.class);

        this.asyncmqClusterService = new AsyncmqClusterService(asyncmqClusterHandler, asyncMqQueueHandler, authService);
    }

    @Test
    public ResponseObject add(@Valid AsyncmqClusterInput input) throws Exception {
        authService.checkAuth(input);
        input.check();
        AsyncmqClusterDO asyncmqClusterDO = new AsyncmqClusterDO();
        BeanUtils.copyProperties(input, asyncmqClusterDO);
        Assert.isTrue(!asyncmqClusterHandler.hasSameName(input.getName()), "Exists Asyncmq cluster with the same name");
        asyncmqClusterDO.setId(IdUtils.generateId());
        asyncmqClusterDO.setEditor(input.getUserId());
        asyncmqClusterHandler.add(asyncmqClusterDO);
        return ResponseObject.success(asyncmqClusterDO.getId());
    }

    public ResponseObject edit(@Valid AsyncmqClusterInput input) throws Exception {
        authService.checkAuth(input);
        Assert.notNull(input.getId(), "id is null");
        input.check();
        AsyncmqClusterDO asyncmqClusterDO = new AsyncmqClusterDO();
        BeanUtils.copyProperties(input, asyncmqClusterDO);
        AsyncmqClusterDO oldAsyncmqClusterDO = asyncmqClusterHandler.getSecurityById(input.getId());
        Assert.isTrue(oldAsyncmqClusterDO != null, "old asyncmq cluster is null");
        if (!input.getName().equals(oldAsyncmqClusterDO.getName())) {
            Assert.isTrue(!asyncmqClusterHandler.hasSameName(input.getName()), "Exists asyncmq cluster with the same name");
        }
        asyncmqClusterHandler.edit(oldAsyncmqClusterDO);
        return ResponseObject.success(true);
    }

    public ResponseObject getById(IdPara idPara) throws Exception {
        authService.checkAuth(idPara);
        Assert.notNull(idPara.getId(), "id is null");
        AsyncmqClusterDO asyncmqClusterDO = asyncmqClusterHandler.getSecurityById(idPara.getId());

        if (asyncmqClusterDO == null) {
            return ResponseObject.fail("asyncmq cluster not exist, id:" + idPara.getId());
        }
        return ResponseObject.success(asyncmqClusterDO);
    }

    public ResponseObject<String> deleteById(IdPara idPara) {
        authService.checkAuth(idPara);
        Integer count = asyncMqQueueHandler.countByAsyncmqClusterId(idPara.getId());
        if (count == 0) {
            asyncmqClusterHandler.deleteById(idPara.getId());
            return ResponseObject.success("success");
        } else {
            return ResponseObject.fail(String.format("asyncmq cluster is used by %d topics", count));
        }
    }

    public ResponseObject testConnection(AsyncmqClusterInput asyncmqClusterInput) {
        authService.checkAuth(asyncmqClusterInput);
        return testAsyncmqConnection(asyncmqClusterInput);
    }

    public ResponseObject testConnectivityById(IdPara idPara) throws Exception {
        authService.checkAuth(idPara);
        Assert.notNull(idPara.getId(), "id is null");
        AsyncmqClusterDO asyncmqClusterDO = asyncmqClusterHandler.getById(idPara.getId());
        AsyncmqClusterInput asyncmqClusterInput = new AsyncmqClusterInput();
        BeanUtils.copyProperties(asyncmqClusterDO, asyncmqClusterInput);
        return testAsyncmqConnection(asyncmqClusterInput);
    }

    private ResponseObject testAsyncmqConnection(AsyncmqClusterInput input) {
        try {
            AsyncMQClientConfig config = AsyncMQClientConfig.builder()
                    .endpoint(input.getEndpoint())
                    .appName(input.getUsername())
                    .appKey(input.getPassword()).build();
            DefaultAsyncMQ asyncMQ = new DefaultAsyncMQ(config);
            Result<LoginResult> result = asyncMQ.admin()
                    .login(LoginParam.builder()
                            .username(input.getUsername())
                            .password(input.getPassword())
                            .build()
                    );
            if (result.isSuccess()) {
                return ResponseObject.success("success");
            } else {
                return ResponseObject.fail(result.getReason());
            }
        } catch (Exception e) {
            return ResponseObject.fail(e.getMessage());
        }
    }
}
