package us.zoom.cube.site.core;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import us.zoom.cube.site.core.model.ad.AdAlarmQuery;
import us.zoom.cube.site.core.model.ad.AiAlarmCfg;
import us.zoom.cube.site.core.model.ad.AiAlarmConditionCfg;
import us.zoom.cube.site.core.model.ad.AiAlarmRuleCfg;
import us.zoom.infra.dao.model.AiAlarmConditionDO;
import us.zoom.infra.dao.model.AiAlarmDO;
import us.zoom.infra.dao.model.AiAlarmRuleDO;
import us.zoom.infra.dao.service.AdAlarmConditionDAO;
import us.zoom.infra.dao.service.AdAlarmDAO;
import us.zoom.infra.dao.service.AdAlarmRuleDAO;
import us.zoom.infra.utils.Instance;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024-01-04 14:17
 */
@Component
@Slf4j
public class AdAlarmHandler {

    @Autowired
    private AdAlarmConditionDAO adAlarmConditionDAO;

    @Autowired
    private AdAlarmRuleDAO adAlarmRuleDAO;

    @Autowired
    private AdAlarmDAO adAlarmDAO;


    public List<AiAlarmCfg> getAiAlarmByAdId(AdAlarmQuery adAlarmQuery) {
        List<AiAlarmConditionDO> aiAlarmConditionDOList = adAlarmConditionDAO.listAdAlarmConditionByAdId(adAlarmQuery.getId());
        Map<String, List<AiAlarmCfg>> adAlarmCfgMap = new HashMap<>(aiAlarmConditionDOList.size());
        if (!CollectionUtils.isEmpty(aiAlarmConditionDOList)) {
            adAlarmCfgMap = getAiAlarmCfg(aiAlarmConditionDOList);
        }
        return adAlarmCfgMap.get(adAlarmQuery.getMetricsId());
    }

    public Map<String, List<AiAlarmCfg>> getAiAlarmCfg(List<AiAlarmConditionDO> aiAlarmConditionDOList) {
        return Instance.ofNullable(buildAiAlarmCfg(aiAlarmConditionDOList)).stream().collect(Collectors.groupingBy(AiAlarmCfg::getMetricId));
    }

    public List<AiAlarmCfg> buildAiAlarmCfg(List<AiAlarmConditionDO> aiAlarmConditionDOList) {
        Map<String, List<AiAlarmConditionDO>> adAlarmConditionMap = Instance.ofNullable(aiAlarmConditionDOList).stream().collect(Collectors.groupingBy(AiAlarmConditionDO::getAlarmRuleId));
        List<AiAlarmRuleDO> aiAlarmRuleDOList = adAlarmRuleDAO.listAdAlarmRuleByIds(new ArrayList<>(adAlarmConditionMap.keySet()));
        Map<String, List<AiAlarmRuleDO>> aiAlarmRuleMap = Instance.ofNullable(aiAlarmRuleDOList).stream().collect(Collectors.groupingBy(AiAlarmRuleDO::getAlarmDefinitionId));
        List<AiAlarmDO> aiAlarmDOList = adAlarmDAO.listAdAlarmByIds(new ArrayList<>(aiAlarmRuleMap.keySet()));
        Map<String, AiAlarmDO> aiAlarmMap = Instance.ofNullable(aiAlarmDOList).stream().collect(Collectors.toMap(AiAlarmDO::getId, e -> e));
        List<AiAlarmCfg> aiAlarmCfgList = new ArrayList<>();
        for (String adAlarmId : aiAlarmMap.keySet()) {
            AiAlarmCfg aiAlarmCfg = new AiAlarmCfg();
            AiAlarmDO aiAlarmDO = aiAlarmMap.get(adAlarmId);
            BeanUtils.copyProperties(aiAlarmDO, aiAlarmCfg);
            List<AiAlarmRuleCfg> adAlarmRuleCfgList = creatAiModelAlarmRule(aiAlarmRuleMap.get(aiAlarmDO.getId()), adAlarmConditionMap);
            aiAlarmCfg.setAiAlarmRuleCfgList(adAlarmRuleCfgList);
            aiAlarmCfgList.add(aiAlarmCfg);
        }
        return aiAlarmCfgList;
    }

    private List<AiAlarmRuleCfg> creatAiModelAlarmRule(List<AiAlarmRuleDO> aiAlarmRuleDOList, Map<String, List<AiAlarmConditionDO>> adAlarmConditionMap) {
        List<AiAlarmRuleCfg> adAlarmRuleCfgList = new ArrayList<>();
        for (AiAlarmRuleDO aiAlarmRuleDO : aiAlarmRuleDOList) {
            AiAlarmRuleCfg aiAlarmRuleCfg = new AiAlarmRuleCfg();
            BeanUtils.copyProperties(aiAlarmRuleDO, aiAlarmRuleCfg);
            aiAlarmRuleCfg.setAiAlarmConditionCfgList(creatAiModelAlarmCondition(adAlarmConditionMap.get(aiAlarmRuleDO.getId())));
            adAlarmRuleCfgList.add(aiAlarmRuleCfg);
        }
        return adAlarmRuleCfgList;
    }

    private List<AiAlarmConditionCfg> creatAiModelAlarmCondition(List<AiAlarmConditionDO> aiAlarmConditionMap) {
        List<AiAlarmConditionCfg> aiAlarmConditionCfgList = new ArrayList<>();
        for (AiAlarmConditionDO aiAlarmConditionDO : aiAlarmConditionMap) {
            AiAlarmConditionCfg aiAlarmConditionCfg = new AiAlarmConditionCfg();
            BeanUtils.copyProperties(aiAlarmConditionDO, aiAlarmConditionCfg);
            aiAlarmConditionCfgList.add(aiAlarmConditionCfg);
        }
        return aiAlarmConditionCfgList;
    }

    public Map<String, List<AiAlarmCfg>> getAiAlarm() {
        List<AiAlarmConditionDO> aiAlarmConditionDOList = adAlarmConditionDAO.listAdAlarmCondition();
        Map<String, List<AiAlarmCfg>> adAlarmCfgMap = new HashMap<>(aiAlarmConditionDOList.size());
        if (!CollectionUtils.isEmpty(aiAlarmConditionDOList)) {
            adAlarmCfgMap = getAiAlarmCfg(aiAlarmConditionDOList);
        }
        return adAlarmCfgMap;
    }
}
