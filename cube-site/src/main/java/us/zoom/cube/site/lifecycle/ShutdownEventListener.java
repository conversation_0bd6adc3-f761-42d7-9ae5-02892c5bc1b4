package us.zoom.cube.site.lifecycle;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import us.zoom.cube.site.core.config.AsyncMQLoader;

/**
 * @author: <PERSON><PERSON>
 * @date: 2025/4/14 14:33
 * @desc:
 */
@Slf4j
@Component
public class ShutdownEventListener {

    @Autowired
    private AsyncMQLoader asyncMQLoader;

    @EventListener
    public void handleContextClosed(ContextClosedEvent event) {
        //mq consumer pause/stop
        try {
            asyncMQLoader.shutdownAsyncMQCluster();
            Thread.sleep(10 * 1000);
        } catch (InterruptedException e) {
            log.error("ShutdownEventListener error", e);
        }
    }
}
