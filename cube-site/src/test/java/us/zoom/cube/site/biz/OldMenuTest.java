package us.zoom.cube.site.biz;

import us.zoom.cube.lib.common.RoleTypeEnum;
import us.zoom.cube.site.biz.model.AuthParaModel;
import us.zoom.cube.site.infra.utils.IdUtils;
import us.zoom.cube.site.lib.Menu;
import us.zoom.infra.enums.MenuTypeEnum;
import us.zoom.cube.lib.utils.JsonUtils;
import us.zoom.infra.utils.MenuConstants;

import java.util.*;

public class OldMenuTest {




    public static void mockPara(){
        AuthParaModel authParaModel = new AuthParaModel();
        authParaModel.setDefaulRole("applicationGuestors");
        authParaModel.setDefaulServiceName("Cube_Demo_Service");
        Map<String, String> oldRole2NewRole = new HashMap<>();
        oldRole2NewRole.put(RoleTypeEnum.normal.name(),"applicationGuestors");
        oldRole2NewRole.put(RoleTypeEnum.admin.name(),"admin_role");
        oldRole2NewRole.put(RoleTypeEnum.tenantAdmin.name(),"applicationOwners");
        authParaModel.setOldRole2NewRole(oldRole2NewRole);
        System.out.println(JsonUtils.toJsonStringIgnoreExp(authParaModel));
    }
    public static void main(String []gs) {
        mockPara();
        OldMenuTest oldMenuTest = new OldMenuTest();
        oldMenuTest.init();

        int parentIndex = 1;
        for(Menu parent:superAdminMenus){
            String parentName = parent.getText();
            System.out.println("insert into menu_item(id, name, resource_id, resource_url, hidden,menu_order,parent_res_id, type,create_time, modify_time) " +
        "values('"+ IdUtils.generateId()+"', '"+parentName+"', '"+parent.getKey()+"', null,false, "+parentIndex+",'"+ MenuConstants.DEFAULT_MENU_ROOT +"',"
                    + MenuTypeEnum.Menu.getCode()+",now(), now());");

            int sonIndex = 1;
            for(Menu son: parent.getChildren()){
                System.out.println("insert into menu_item(id, name, resource_id, resource_url, hidden,menu_order,parent_res_id, type,create_time, modify_time) " +
                        "values('"+ IdUtils.generateId()+"', '"+son.getText()+"', '"+son.getKey()+"', null,false, "+sonIndex+",'"+ parent.getKey() +"',"
                        + MenuTypeEnum.Menu.getCode()+",now(), now());");
                sonIndex++;
            }
            parentIndex++;

        }
    }



    private static  final List<Menu> superAdminMenus=new ArrayList<>();
    private static  final List<Menu> tenantAdminMenus=new ArrayList<>();
    private static  final List<Menu> tenantNormalMenus=new ArrayList<>();


    public void init() {

        Menu dashboard = createDashboardMenu();

        Menu usage = createUsageMenu();
        Menu usageNormal = createUsageMenuNormal();
        Menu environmentMenu = createEnvironmentMenu();

        Menu dataExplore=createDataExploreMenu();

        Menu serverLog=new Menu("serverLog","group","Server Log","",new ArrayList<>());
        Menu topicList=new Menu("serverLog.topic","","Topic",null,null);
        Menu dataParserList=new Menu("serverLog.metadata","","Metadata",null,null);
        Menu createGather=new Menu("serverLog.metricDef","","MetricDef",null,null);
        serverLog.getChildren().add(topicList);
        serverLog.getChildren().add(dataParserList);
        serverLog.getChildren().add(createGather);

        Menu webLogHasParser=new Menu("webLog","group","Collector & Metric","",new ArrayList<>());
        Menu dtaParserSource=new Menu("webLog.dataParserSource","","New Data Parser",null,null);
        Menu DataParser=new Menu("webLog.newDataParser","","Data Parser",null,null);
        Menu webMetric=new Menu("webLog.webMetric","","Metric",null,null);
        webLogHasParser.getChildren().add(dtaParserSource);
        webLogHasParser.getChildren().add(DataParser);
        webLogHasParser.getChildren().add(webMetric);

        Menu webLogNoParser=new Menu("webLog","group","Collector & Metric","",new ArrayList<>());
        webLogNoParser.getChildren().add(webMetric);

        Menu alarm = createAlarmMenu();


        Menu unit = new Menu("unit", "group", "Resource Setting", "", new ArrayList<>());
        Menu dataFlow=new Menu("unit.dataFlow","","Data Flow",null,null);

        Menu adminUnit=new Menu("unit","group","Resource Unit","",new ArrayList<>());

        Menu kafkaCluster=new Menu("unit.kafkaCluster","","Kafka Cluster",null,null);
        Menu taskQueue=new Menu("unit.taskQueue","","Task Queue",null,null);
        Menu asyncMQ=new Menu("unit.asyncMQ","","Input Queue Group",null,null);
        Menu unitTags=new Menu("unit.unitTags","","Unit Tags",null,null);
        Menu cubeServer=new Menu("unit.cubeServer","","Cube Server",null,null);
        Menu logicUnit=new Menu("unit.logicUnit","","Logic Unit",null,null);
        Menu calc=new Menu("unit.calc","","Calculation",null,null);
        Menu labelSource=new Menu("unit.labelSource","","Label Source",null,null);
        Menu agent=new Menu("unit.agent","","Agent",null,null);
        Menu hub=new Menu("unit.hub","","Hub",null,null);
        Menu outputAQ=new Menu("unit.outputAQ","","Output Queue",null,null);

        adminUnit.getChildren().add(dataFlow);
        adminUnit.getChildren().add(unitTags);
        adminUnit.getChildren().add(kafkaCluster);
        adminUnit.getChildren().add(taskQueue);
        adminUnit.getChildren().add(asyncMQ);
        adminUnit.getChildren().add(logicUnit);
        adminUnit.getChildren().add(calc);
        adminUnit.getChildren().add(labelSource);
        adminUnit.getChildren().add(agent);
        adminUnit.getChildren().add(hub);
        adminUnit.getChildren().add(outputAQ);
        adminUnit.getChildren().add(cubeServer);

        Menu networkMonitorMenu = createNetworkMonitorMenu();

        Menu tenantUnit=new Menu("unit","group","Resource Unit","",new ArrayList<>());
        tenantUnit.getChildren().add(agent);
        tenantUnit.getChildren().add(outputAQ);
//        tenantUnit.getChildren().add(hub);
//        tenantUnit.getChildren().add(labelSource);

        Menu influxDBAdmin = new Menu("influxDBAdmin", "group", "InfluxDB Admin", "", new ArrayList<>());
        Menu influxDBCluster = new Menu("influxDBAdmin.influxDBCluster", "", "InfluxDB Cluster", "", null);
        Menu influxDBDatabase = new Menu("influxDBAdmin.influxDBDatabase", "", "InfluxDB Database", "", null);
        Menu clickhouseCluster = new Menu("influxDBAdmin.clickhouseCluster", "", "ClickHouse Cluster", "", null);

        influxDBAdmin.getChildren().add(influxDBCluster);
        influxDBAdmin.getChildren().add(influxDBDatabase);
        influxDBAdmin.getChildren().add(clickhouseCluster);


        Menu e2eAdmin = new Menu("e2e", "group", "E2E", "", new ArrayList<>());
        Menu e2eDashboard = new Menu("e2e.e2e-dashboard", "", "E2E Dashboard", "", null);
        Menu probeTasks = new Menu("e2e.probe-tasks", "", "Probe Tasks", "", null);
        Menu probePoints = new Menu("e2e.probe-points", "", "Probe Points", "", null);
        Menu probeArgs = new Menu("e2e.probe-args", "", "Probe Args", "", null);
        e2eAdmin.getChildren().add(e2eDashboard);
        e2eAdmin.getChildren().add(probeTasks);
        e2eAdmin.getChildren().add(probePoints);
        e2eAdmin.getChildren().add(probeArgs);

        Menu e2eTenantAdmin = new Menu("e2e", "group", "E2E", "", new ArrayList<>());
        e2eTenantAdmin.getChildren().add(e2eDashboard);
        e2eTenantAdmin.getChildren().add(probeTasks);
        e2eTenantAdmin.getChildren().add(probeArgs);

        Menu e2eTenantNormal = new Menu("e2e", "group", "E2E", "", new ArrayList<>());
        e2eTenantNormal.getChildren().add(e2eDashboard);

        Menu authentication=new Menu("authentication","group","Authentication","",new ArrayList<>());
        Menu superAuthentication=new Menu("authentication","group","Authentication","",new ArrayList<>());
        Menu user=new Menu("authentication.user","","User",null,null);
        Menu opAdmin=new Menu("authentication.opAdmin","","Op Admin",null,null);
        Menu tenant=new Menu("authentication.tenant","","Service",null,null);
        Menu tenantAuthority=new Menu("authentication.tenantAuthority","","Service Authority",null,null);

        authentication.getChildren().add(tenantAuthority);
        superAuthentication.getChildren().add(user);
        superAuthentication.getChildren().add(opAdmin);
        superAuthentication.getChildren().add(tenant);
        superAuthentication.getChildren().add(tenantAuthority);

        Menu system = new Menu("system", "group", "System", "", new ArrayList<>());
        Menu systemParameter = new Menu("system.parameters", "", "Parameters", "", null);
        system.getChildren().add(systemParameter);
        Menu tools = new Menu("system.tools", "", "Dashboard Tools", "", null);
        system.getChildren().add(tools);

        Menu deprecated = new Menu("deprecated", "group", "Deprecated", "", new ArrayList<>());
        Menu dataParser=new Menu("deprecated.dataParser","","Collector DataParser",null,null);
        Menu deprecatedAsyncMQ=new Menu("deprecated.asyncMQ","","Input Queue Group",null,null);
        Menu deprecatedOutputAQ=new Menu("deprecated.outputAQ","","Output Queue",null,null);
        Menu deprecatedCalculation = new Menu("deprecated.calcUnit","","Calculation Unit",null,null);
        deprecated.getChildren().add(dataParser);
        deprecated.getChildren().add(deprecatedAsyncMQ);
        deprecated.getChildren().add(deprecatedOutputAQ);
        deprecated.getChildren().add(deprecatedCalculation);

        //set menu fro super admin
        superAdminMenus.add(dashboard);
        superAdminMenus.add(dataExplore);
        superAdminMenus.add(usage);
        superAdminMenus.add(serverLog);
        superAdminMenus.add(webLogHasParser);
        superAdminMenus.add(alarm);
        superAdminMenus.add(adminUnit);
        superAdminMenus.add(networkMonitorMenu);
        superAdminMenus.add(e2eAdmin);
        superAdminMenus.add(influxDBAdmin);
        superAdminMenus.add(superAuthentication);
        superAdminMenus.add(system);
        superAdminMenus.add(deprecated);
        superAdminMenus.add(environmentMenu);


        // set menu for tenant admin
        tenantAdminMenus.add(dashboard);
        tenantAdminMenus.add(dataExplore);
        tenantAdminMenus.add(serverLog);
        //tenantAdminMenus.add(usage);
        tenantAdminMenus.add(alarm);
        tenantAdminMenus.add(networkMonitorMenu);
        tenantAdminMenus.add(tenantUnit);
        tenantAdminMenus.add(authentication);
        tenantAdminMenus.add(e2eTenantAdmin);
        tenantAdminMenus.add(usageNormal);

        //set menu for normal user
        tenantNormalMenus.add(dashboard);
        tenantNormalMenus.add(usageNormal);
        tenantNormalMenus.add(dataExplore);
        tenantNormalMenus.add(e2eTenantNormal);
    }



    private Menu createAlarmMenu() {
        Menu alarm=new Menu("alarm","group","Alarm & Notify","",new ArrayList<>());
        Menu alarmList=new Menu("alarm.alarmDef","","Alarm",null,null);
        Menu imChannel=new Menu("alarm.noticeChannel","","Notice Channel",null,null);
        alarm.getChildren().add(alarmList);
        alarm.getChildren().add(imChannel);
        return alarm;
    }

    private Menu createUsageMenu() {
        Menu Usage=new Menu("usage","group","Usage Monitor","",new ArrayList<>());
        Menu dashBoard=new Menu("usage.dashBoard","","Dashboard",null,null);
        Menu setting=new Menu("usage.setting","","Settings",null,null);
        Usage.getChildren().add(dashBoard);
        Usage.getChildren().add(setting);
        return Usage;
    }

    private Menu createUsageMenuNormal() {
        Menu Usage=new Menu("usage","group","Usage Monitor","",new ArrayList<>());
        Menu dashBoard=new Menu("usage.dashBoard","","DashBoard",null,null);
        Usage.getChildren().add(dashBoard);
        return Usage;
    }

    private Menu createEnvironmentMenu() {
        Menu Environment=new Menu("Environment","group","Environment Settings","",new ArrayList<>());
        Menu setting=new Menu("Environment.setting","","Settings",null,null);
        Environment.getChildren().add(setting);
        return Environment;
    }

    private Menu createDataExploreMenu() {
        Menu dataExploreMenu=new Menu("dataExplore","group","Data Explorer","",new ArrayList<>());
        Menu influxdbQuerier=new Menu("dataExplore.influxdbQuerier","","InfluxDB Querier",null,null);
        Menu alarmRecords=new Menu("dataExplore.alarmRecords","","Alarm Records",null,null);
        Menu infrastructure = new Menu("dataExplore.infrastructure","","Infrastructure",null,null);
        dataExploreMenu.getChildren().add(influxdbQuerier);
        dataExploreMenu.getChildren().add(alarmRecords);
        dataExploreMenu.getChildren().add(infrastructure);
        return dataExploreMenu;
    }

    private Menu createDashboardMenu() {
        Menu dashboard=new Menu("dashboard","group","Dashboard","",new ArrayList<>());
        Menu overview=new Menu("dashboard.overview","","Overview",null,null);
        Menu host=new Menu("dashboard.singleHost","","Single Host",null,null);
        Menu metric=new Menu("dashboard.singleMetric","","Single Metric",null,null);
        Menu template=new Menu("dashboard.template","","Template",null,null);
//        Menu dataExplore=new Menu("dashboard.dataExplore","","Data Explore",null,null);
//        Menu alarmRecord=new Menu("dashboard.alarmRecord","","Alarm Record",null,null);

        dashboard.getChildren().add(overview);
        dashboard.getChildren().add(host);
        dashboard.getChildren().add(metric);
        dashboard.getChildren().add(template);
//        dashboard.getChildren().add(dataExplore);
//        dashboard.getChildren().add(alarmRecord);
        return dashboard;
    }

    private Menu createNetworkMonitorMenu(){
        Menu networkMonitor = new Menu("networkMonitor","group","Network Monitor","",new ArrayList<>());
        Menu networkDashboard = new Menu("networkMonitor.networkDashboard","","Network Dashboard",null,null);
        Menu strategyList = new Menu("networkMonitor.strategyList","","Strategy List",null,null);
        Menu serviceDetectionConfig = new Menu("networkMonitor.serviceDetectionConfig","","Service Detection Config",null,null);
        networkMonitor.getChildren().add(networkDashboard);
        networkMonitor.getChildren().add(strategyList);
        networkMonitor.getChildren().add(serviceDetectionConfig);
        return networkMonitor;
    }

}
