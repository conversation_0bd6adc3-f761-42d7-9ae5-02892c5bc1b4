package us.zoom.cube.site.lib.monitor.alarm;

import lombok.Builder;
import lombok.Data;
import us.zoom.cube.sdk.annotation.CubeMonitorLog;
import us.zoom.cube.sdk.annotation.Field;
import us.zoom.cube.sdk.annotation.Tag;

@CubeMonitorLog(measure = "SilenceAutoDeleteMonitorLogEntity")
@Data
@Builder
public class SilenceAutoDeleteMonitorLogEntity {
    @Tag
    private String silenceId;

    @Tag
    private String silenceName;

    @Tag
    private String serviceName;

    @Tag
    private String type;

    @Field
    private String alarmSilenceDO;
}
