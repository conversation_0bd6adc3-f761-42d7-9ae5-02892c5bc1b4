package us.zoom.cube.site.core;


import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import us.zoom.cube.site.CubeSiteApplicationTests;
import us.zoom.cube.site.infra.utils.IdUtils;
import us.zoom.infra.dao.model.DataParserLabelMysqlProcessorDO;

import java.util.Date;

/**
 * <AUTHOR>
 * @create 2020/8/27 1:20 PM
 */
public class DataParserLabelMysqlProcessorHandlerTest extends CubeSiteApplicationTests {

    @Autowired
    private DataParserLabelMysqlProcessorHandler dataParserLabelMysqlProcessorHandler;

    @Test
    public void addLabelMysqlProcessor() {
        DataParserLabelMysqlProcessorDO labelMysqlProcessorDO = buildDO();
        dataParserLabelMysqlProcessorHandler.addLabelMysqlProcessor(labelMysqlProcessorDO);
        dataParserLabelMysqlProcessorHandler.delLabelMysqlProcessor(labelMysqlProcessorDO.getId());
    }

    private DataParserLabelMysqlProcessorDO buildDO() {
        DataParserLabelMysqlProcessorDO processorDO = new DataParserLabelMysqlProcessorDO();
        processorDO.setId(IdUtils.generateId());
        processorDO.setDataParserPipelineId(IdUtils.generateId());
        processorDO.setName("processorName");
        processorDO.setLabelDataSourceId(IdUtils.generateId());
        processorDO.setNameSpace("processorNameSpace");
        processorDO.setScript("select * from user where userId = #{userId};");
        processorDO.setKeyFields("userId");
        processorDO.setValueFields("testFields");
        processorDO.setOrder(1);
        processorDO.setGmtCreate(new Date());
        processorDO.setGmtModify(new Date());

        return processorDO;
    }
}
