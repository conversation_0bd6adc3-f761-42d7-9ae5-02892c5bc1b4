package us.zoom.cube.site.biz.template;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ActiveProfiles;
import us.zoom.cube.site.biz.BaseTest;
import us.zoom.cube.site.core.TemplateHandlerV2;
import us.zoom.cube.site.core.TenantHandler;
import us.zoom.cube.site.lib.input.template.InnerTemplateBindDTO;
import us.zoom.infra.dao.model.TemplateDO;
import us.zoom.infra.dao.model.TenantDO;

import static org.junit.jupiter.api.Assertions.assertThrows;

@ActiveProfiles("perf_for_local")
public class TemplateInnerUtilServiceTest extends BaseTest {

    @Autowired
    private TemplateInnerUtilService templateInnerUtilService;

    @Autowired
    private TenantHandler tenantHandler;

    @Autowired
    private TemplateHandlerV2 templateHandlerV2;

    @Test
    public void checkTemplateParameter() {
        String templateId = "807ad5c6-5548-4932-8bf2-37563697a702";
        String serviceId = "9ce3fae8-e05d-4305-b51d-2b9d9b5091f3";
        TenantDO tenantDO = tenantHandler.getTenantById(serviceId);
        TemplateDO templateDO = templateHandlerV2.getById(templateId);
        InnerTemplateBindDTO innerTemplateBindDTO = new InnerTemplateBindDTO();
        innerTemplateBindDTO.setServiceName(tenantDO.getName());
        innerTemplateBindDTO.setTemplateName(templateDO.getName());
        innerTemplateBindDTO.setWithTopic(false);
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            templateInnerUtilService.checkTemplateParameter(innerTemplateBindDTO);
        });

    }
}