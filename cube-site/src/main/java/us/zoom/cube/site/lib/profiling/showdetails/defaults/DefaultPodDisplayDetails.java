package us.zoom.cube.site.lib.profiling.showdetails.defaults;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: <PERSON><PERSON> Ding
 * @date: 2024/11/28 14:37
 * @desc:
 */

public enum DefaultPodDisplayDetails {

    pid(true),

    process(false),
    ;

    private boolean display;

    DefaultPodDisplayDetails(boolean display) {
        this.display = display;
    }

    public boolean isDisplay() {
        return display;
    }

    public static Map<String, Boolean> getDefaultPodDisplayDetails() {
        return Arrays.stream(values()).collect(Collectors.toMap(DefaultPodDisplayDetails::name, DefaultPodDisplayDetails::isDisplay));
    }
}
