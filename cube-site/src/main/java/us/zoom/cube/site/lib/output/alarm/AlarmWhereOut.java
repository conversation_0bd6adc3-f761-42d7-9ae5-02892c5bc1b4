package us.zoom.cube.site.lib.output.alarm;

public class AlarmWhereOut {
    private Integer type;
    private String fieldType;
    private String operator;
    private String cmpValue;
    private String field;

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getFieldType() {
        return fieldType;
    }

    public void setFieldType(String fieldType) {
        this.fieldType = fieldType;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getCmpValue() {
        return cmpValue;
    }

    public void setCmpValue(String cmpValue) {
        this.cmpValue = cmpValue;
    }

    public String getField() {
        return field;
    }

    public void setField(String field) {
        this.field = field;
    }
}
