package us.zoom.cube.site.core.schedulery;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import us.zoom.cube.scheduler.lib.model.SchedulerExecutorInfo;
import us.zoom.cube.site.api.intercept.AuthInterceptor;
import us.zoom.cube.site.infra.utils.IdUtils;
import us.zoom.infra.dao.service.scheduler.SchedulerExecutorInfoDao;

import java.util.Date;
import java.util.List;

/**
 * @author: <PERSON><PERSON> Ding
 * @date: 2025/4/15 17:40
 * @desc:
 */
@Slf4j
@Component
public class ScheduleExecutorInfoHandler {

    @Autowired
    private SchedulerExecutorInfoDao executorInfoDao;

    /**
     * Add a new executor info
     *
     * @param executorInfo the executor info to add
     */
    public void addExecutorInfo(SchedulerExecutorInfo executorInfo) {
        // Validate input
        if (executorInfo == null) {
            log.error("ExecutorInfoHandler addExecutorInfo executorInfo is null");
            throw new IllegalArgumentException("executorInfo is null");
        }

        if (StringUtils.isBlank(executorInfo.getName())) {
            log.error("ExecutorInfoHandler addExecutorInfo name is blank");
            throw new IllegalArgumentException("name is blank");
        }

        /*if (StringUtils.isBlank(executorInfo.getUnit())) {
            log.error("ExecutorInfoHandler addExecutorInfo unit is blank");
            throw new IllegalArgumentException("unit is blank");
        }

        if (StringUtils.isBlank(executorInfo.getCubeService())) {
            log.error("ExecutorInfoHandler addExecutorInfo cubeService is blank");
            throw new IllegalArgumentException("cubeService is blank");
        }*/

        if (null != executorInfoDao.findByNameAndServiceId(executorInfo.getName(), executorInfo.getTenantId())) {
            throw new IllegalArgumentException("there is already an executor with the same name");
        }

        // Generate ID
        if (StringUtils.isBlank(executorInfo.getId())) {
            executorInfo.setId(IdUtils.generateId());
        } else {
            throw new IllegalArgumentException("Executor creation should not have id!");
        }

        // Set timestamps
        Date now = new Date();
        executorInfo.setCreateTime(now);
        executorInfo.setUpdateTime(now);

        // Set creator and editor
        String userName = AuthInterceptor.getUserName();
        executorInfo.setCreator(userName);
        executorInfo.setEditor(userName);

        // Persist to database
        executorInfoDao.add(executorInfo);
    }

    /**
     * Update an existing executor info
     *
     * @param executorInfo the executor info to update
     */
    public void updateExecutorInfo(SchedulerExecutorInfo executorInfo) {
        // Validate input
        if (executorInfo == null) {
            log.error("ExecutorInfoHandler updateExecutorInfo executorInfo is null");
            throw new IllegalArgumentException("executorInfo is null");
        }

        // Check if executor exists
        if (StringUtils.isBlank(executorInfo.getId())) {
            log.error("ExecutorInfoHandler updateExecutorInfo id is null");
            throw new IllegalArgumentException("Executor update should have id!");
        }

        SchedulerExecutorInfo existingExecutorInfo = executorInfoDao.findById(executorInfo.getId());
        if (null == existingExecutorInfo) {
            log.error("ExecutorInfoHandler updateExecutorInfo executorInfo not found, id: {}", executorInfo.getId());
            throw new IllegalArgumentException("Executor not found");
        }

        if (StringUtils.isBlank(executorInfo.getName())) {
            log.error("ExecutorInfoHandler updateExecutorInfo name is blank");
            throw new IllegalArgumentException("name is blank");
        }

/*        if (StringUtils.isBlank(executorInfo.getUnit())) {
            log.error("ExecutorInfoHandler updateExecutorInfo unit is blank");
            throw new IllegalArgumentException("unit is blank");
        }*/


        // Update timestamp and editor
        executorInfo.setUpdateTime(new Date());
        executorInfo.setEditor(AuthInterceptor.getUserName());

        // Preserve creation info
        executorInfo.setCreateTime(existingExecutorInfo.getCreateTime());
        executorInfo.setCreator(existingExecutorInfo.getCreator());

        // Update in database
        executorInfoDao.update(executorInfo);
    }

    /**
     * Delete an executor info by id
     *
     * @param id the id of the executor info to delete
     */
    public void deleteExecutorInfo(String id) {
        // Check if id is valid
        if (StringUtils.isBlank(id)) {
            log.error("ExecutorInfoHandler deleteExecutorInfo id is null");
            throw new IllegalArgumentException("id is null");
        }

        // Check if executor exists
        SchedulerExecutorInfo existingExecutorInfo = executorInfoDao.findById(id);
        if (null == existingExecutorInfo) {
            log.error("ExecutorInfoHandler deleteExecutorInfo executorInfo not found, id: {}", id);
            throw new IllegalArgumentException("executorInfo not found");
        }

        // Delete from database
        executorInfoDao.delete(id);
    }

    /**
     * Get an executor info by id
     *
     * @param id the id of the executor info to get
     * @return the executor info with the given id, or null if not found
     */
    public SchedulerExecutorInfo getExecutorInfoById(String id) {
        // Check if id is valid
        if (StringUtils.isBlank(id)) {
            log.error("ExecutorInfoHandler getExecutorInfoById id is null");
            throw new IllegalArgumentException("id is null");
        }

        // Retrieve from database
        return executorInfoDao.findById(id);
    }

    /**
     * Get all executor infos
     *
     * @return a list of all executor infos
     */
    public List<SchedulerExecutorInfo> getAllExecutorInfo() {
        return executorInfoDao.findAll();
    }

    /**
     * Disable an executor info by id
     *
     * @param id the id of the executor info to disable
     */
    public void disableExecutorInfo(String id) {
        // Check if id is valid
        if (StringUtils.isBlank(id)) {
            log.error("ExecutorInfoHandler disableExecutorInfo id is null");
            throw new IllegalArgumentException("id is null");
        }

        // Check if executor exists
        SchedulerExecutorInfo existingExecutorInfo = executorInfoDao.findById(id);
        if (null == existingExecutorInfo) {
            log.error("ExecutorInfoHandler disableExecutorInfo executorInfo not found, id: {}", id);
            throw new IllegalArgumentException("executorInfo not found");
        }

        // Disable in database
        executorInfoDao.disable(id);
    }

    /**
     * Enable an executor info by id
     *
     * @param id the id of the executor info to enable
     */
    public void enableExecutorInfo(String id) {
        // Check if id is valid
        if (StringUtils.isBlank(id)) {
            log.error("ExecutorInfoHandler enableExecutorInfo id is null");
            throw new IllegalArgumentException("id is null");
        }

        // Check if executor exists
        SchedulerExecutorInfo existingExecutorInfo = executorInfoDao.findById(id);
        if (null == existingExecutorInfo) {
            log.error("ExecutorInfoHandler enableExecutorInfo executorInfo not found, id: {}", id);
            throw new IllegalArgumentException("executorInfo not found");
        }

        // Enable in database
        executorInfoDao.enable(id);
    }
}
