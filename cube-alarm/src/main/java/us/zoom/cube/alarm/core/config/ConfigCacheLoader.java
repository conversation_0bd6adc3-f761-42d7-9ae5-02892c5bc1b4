package us.zoom.cube.alarm.core.config;

import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import us.zoom.cube.alarm.biz.service.ConfigurableListener;
import us.zoom.cube.alarm.infra.redis.MultiRedisService;
import us.zoom.cube.config.client.api.ConfigApi;
import us.zoom.infra.clickhouse.ChTable;
import us.zoom.infra.loader.PiiTableLoader;
import us.zoom.infra.thread.CacheLoaderScheduler;

import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * <AUTHOR>
 * @create 2019/10/21 10:04
 */
@Component
@Slf4j
public class ConfigCacheLoader {

    @Value("${cache.load.interval.minute}")
    private int loadIntervalInMinute;

    @Autowired
    private ConfigApi configApi;

    @Autowired
    private MultiRedisService redisService;

    @Autowired
    private AlarmCacheLoader alarmCacheLoader;

    @Autowired
    private MetricsCacheLoader metricsCacheLoader;

    @Autowired
    private EnvironmentCacheLoader environmentCacheLoader;

    @Autowired
    private TenantCacheLoader tenantCacheLoader;

    @Autowired
    private CubeServerCacheLoader cubeServerCacheLoader;

    @Autowired
    private ConfigurableListener configurableListener;

    @Autowired
    private ClickhouseLoader clickhouseLoader;

    @Autowired
    private EnvCacheLoader envCacheLoader;

    @Autowired
    private AlarmSilenceCacheLoader alarmSilenceCacheLoader;

    @Autowired
    private PiiTableLoader piiTableLoader;

    @Autowired
    private AdCacheLoader adCacheLoader;

    @Autowired
    private ImAppRelationsCacheLoader imAppRelationsCacheLoader;

    private static final AtomicBoolean INIT_OVER = new AtomicBoolean(false);

    @PostConstruct
    public void startLoad() {
        configurableListener.register(cubeServerCacheLoader);
        loadCfg2Cache();
        INIT_OVER.set(true);
        CacheLoaderScheduler.getInstance().getScheduler().scheduleAtFixedRate(() -> {
            if (INIT_OVER.get()) {
                loadCfg2Cache();
            }
        }, 1, loadIntervalInMinute, TimeUnit.MINUTES);
    }

    public boolean isInitOver() {
        return INIT_OVER.get();
    }

    private void loadCfg2Cache() {
        long begin = System.currentTimeMillis();
        log.info("begin loadCfg2Cache!");

        try {
            log.info("canyon_0716_test loadCfg2Cache!");
            redisService.set("canyon_0716_test", "0704_value" + System.currentTimeMillis(), false);
            redisService.set("canyon_0716_2_test", "0704_value_2_" + System.currentTimeMillis(), true);
        } catch (Exception e) {
            log.error("canyon_0716_test error", e);
        }

        try {
            adCacheLoader.load();
        } catch (Exception e) {
            log.error("adCacheLoader error! ", e);
        }

        try {
            Map<ChTable, Set<String>> piiTableCache = configApi.getPiiTableCache();
            piiTableLoader.setPiiTableFields(piiTableCache);
        } catch (Throwable e) {
            log.error("piiTableLoader error! ", e);
        }

        try {
            tenantCacheLoader.load();
        } catch (Exception e) {
            log.error("tenantCacheLoader error! ", e);
        }

        try {
            metricsCacheLoader.load();
        } catch (Exception e) {
            log.error("metricsCacheLoader error! ", e);
        }

        try {
            alarmCacheLoader.loadCache();
        } catch (Exception e) {
            log.error("alarmCacheLoader error! ", e);
        }

        try {
            environmentCacheLoader.load();
        } catch (Exception e) {
            log.error("environmentCacheLoader error! ", e);
        }

        try {
            clickhouseLoader.load();
        } catch (Exception e) {
            log.error("clickhouseLoader error! ", e);
        }

        try {
            cubeServerCacheLoader.load();
        } catch (Exception e) {
            log.error("cubeServerCacheLoader error! ", e);
        }

        try {
            envCacheLoader.load();
        } catch (Exception e) {
            log.error("envCacheLoader error! ", e);
        }

        try {
            alarmSilenceCacheLoader.load();
        } catch (Exception e) {
            log.error("alarmSilenceCacheLoader error! ", e);
        }

        try {
            imAppRelationsCacheLoader.load();
        } catch (Exception e) {
            log.error("imAppRelationsCacheLoader error! ", e);
        }

        log.info("end loadCfg2Cache, cost= {}", System.currentTimeMillis() - begin);
    }
}
