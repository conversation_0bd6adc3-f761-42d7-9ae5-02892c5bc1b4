package us.zoom.cube.site.lib.input;


import com.google.gson.Gson;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import us.zoom.cube.site.infra.utils.IdUtils;
import us.zoom.infra.enums.CommonStatusEnum;

/**
 * @Author: <EMAIL>
 * @ModuleOwner: <EMAIL>
 * @Date: 03/01/2022 11:21
 * @Description:
 */
public class DataFlowInputTest {
    DataFlowInput input = new DataFlowInput();
    String id = IdUtils.generateId();

    @BeforeEach
    public void before() {
        input.setName("data flow name");
        input.setId(id);
        input.setStatus(CommonStatusEnum.enable.name());
        // input.setInputTopicProfile("topicProfile");
        input.setGroup("nws");
        System.out.println(new Gson().toJson(input));
    }

    @Test
    public void testGroup1() {
        input.setGroup("");
        input.check();
        Assertions.assertEquals("", input.getGroup());
    }

    @Test
    public void testGroup2() {
        input.setGroup("    ");
        input.check();
        Assertions.assertEquals("", input.getGroup());
    }

    @Test
    public void testStatus1() {
        String status = "ddd";
        input.setStatus(status);

        IllegalArgumentException exception = Assertions.assertThrows(IllegalArgumentException.class, () ->
                input.check());
        Assertions.assertEquals("status is not exist, status:" + status, exception.getMessage());
    }

    @Test
    public void testStatus2() {
        String status = null;
        input.setStatus(status);

        IllegalArgumentException exception = Assertions.assertThrows(IllegalArgumentException.class, () ->
                input.check());
        Assertions.assertEquals("status is not exist, status:" + status, exception.getMessage());
    }

    @Test
    public void testName1() {
        String name = " ";
        input.setName(name);

        IllegalArgumentException exception = Assertions.assertThrows(IllegalArgumentException.class, () ->
                input.check());
        Assertions.assertEquals("name is null or blank", exception.getMessage());
    }

    @Test
    public void testName2() {
        String name = "";
        for (int i = 0; i <= 512; i++) {
            name = name + "i";
        }
        input.setName(name);

        IllegalArgumentException exception = Assertions.assertThrows(IllegalArgumentException.class, () ->
                input.check());
        Assertions.assertEquals("Name length must be less than 512", exception.getMessage());
    }

    @Test
    public void testId() {
        input.setId(null);

        IllegalArgumentException exception = Assertions.assertThrows(IllegalArgumentException.class, () ->
                input.editCheck());
        Assertions.assertEquals("id not null", exception.getMessage());
    }
    @Test
    public void testScript() {
        input.setScriptContent(null);
        input.setScriptContent(null);
        input.editCheck();
        Assertions.assertEquals("", input.getScriptContent());
        Assertions.assertEquals("", input.getScriptMethod());
    }

    @Test
    public void testScript1() {
        input.setName("aa_bb_cc");
        input.setScriptMethod("ScriptMethod");
        input.setScriptContent("securityChampion");
        input.editCheck();
        Assertions.assertEquals("ScriptMethod", input.getScriptMethod());
        Assertions.assertEquals("securityChampion", input.getScriptContent());

        input.setScriptMethod(null);
        input.setScriptContent(null);
        input.editCheck();
        Assertions.assertEquals("", input.getScriptMethod());
        Assertions.assertEquals("", input.getScriptContent());
    }

}
