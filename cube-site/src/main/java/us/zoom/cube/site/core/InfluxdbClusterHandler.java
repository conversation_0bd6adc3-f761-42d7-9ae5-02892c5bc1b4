package us.zoom.cube.site.core;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.site.lib.input.influxadmin.InfluxdbHaClusterInput;
import us.zoom.infra.dao.model.InfluxdbHaRelationDO;
import us.zoom.infra.dao.model.InfluxdbClusterDO;
import us.zoom.infra.dao.model.InfluxdbTenantRelationDO;
import us.zoom.infra.dao.service.InfluxdbHaRelationDAO;
import us.zoom.infra.dao.service.InfluxdbClusterDAO;
import us.zoom.infra.dao.service.InfluxdbTenantRelationDAO;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/8/11 5:24 PM
 */

@Service
@Slf4j
public class InfluxdbClusterHandler {

    @Autowired
    private InfluxdbClusterDAO influxdbClusterDAO;

    @Autowired
    private InfluxdbTenantRelationDAO influxdbTenantRelationDAO;

    @Autowired
    private InfluxdbHaRelationDAO influxDbHaRelationDAO;

    public List<InfluxdbClusterDO> listAll() {
        return influxdbClusterDAO.listAll();
    }

    public List<InfluxdbClusterDO> searchCluster(int pageIndex, int pageSize, String name, String type) {
        return influxdbClusterDAO.search((pageIndex - 1) * pageSize, pageSize, name, type);
    }

    public int countCluster(String name, String type) {
        return influxdbClusterDAO.count(name, type);
    }

    public void addCluster(InfluxdbClusterDO influxdbClusterDO) {
        influxdbClusterDAO.add(influxdbClusterDO);
    }

    public void addHaCluster(String clusterId, List<InfluxdbHaClusterInput>urls) {
        for(InfluxdbHaClusterInput url:urls){
            InfluxdbHaRelationDO ido=new InfluxdbHaRelationDO();
            ido.setId(UUID.randomUUID().toString());
            ido.setInfluxClusterId(clusterId);
            ido.setServerUrl(url.getServerUrl());
            ido.setIsPrimary(url.getIsPrimary());
            influxDbHaRelationDAO.add(ido);
        }
    }


    @Transactional(rollbackFor = Exception.class)
    public void setDefaultCluster(String influxdbClusterId) {
        influxdbClusterDAO.setDefaultCluster(influxdbClusterId);
        influxdbTenantRelationDAO.removeRelationInCurrentCluster(influxdbClusterId);
        influxdbClusterDAO.resetOtherDefaultClusters(influxdbClusterId);
    }

    public InfluxdbClusterDO getClusterById(String id) {
        return influxdbClusterDAO.getById(id);
    }

    public void editCluster(InfluxdbClusterDO influxdbClusterDO) {
        influxdbClusterDAO.edit(influxdbClusterDO);
    }

    @Transactional(rollbackFor = Exception.class)
    public void editHaCluster(String clusterId, List<InfluxdbHaClusterInput>inputs) {
        List<InfluxdbHaRelationDO> exist = influxDbHaRelationDAO.getByClusterId(clusterId);
        Map<String, String> existUrl2Id = exist.stream().collect(Collectors.toMap(u -> u.getServerUrl(), u -> u.getId()));
        Set<String> Existurls = exist.stream().map(u -> u.getServerUrl()).collect(Collectors.toSet());
        //update for those already in the database
        inputs.stream().filter(e->Existurls.contains(e.getServerUrl())).forEach(input->{
            InfluxdbHaRelationDO d=new InfluxdbHaRelationDO();
            d.setIsPrimary(input.getIsPrimary());
            d.setModifyTime(new Date());
            d.setId(existUrl2Id.get(input.getServerUrl()));
            influxDbHaRelationDAO.edit(d);
        });
        //insert for those not in the database
        addHaCluster(clusterId,inputs.stream().filter(e->!Existurls.contains(e.getServerUrl())).collect(Collectors.toList()));
        //delete for those not in input
        Set<String> inputUrls = inputs.stream().map(u -> u.getServerUrl()).collect(Collectors.toSet());
        existUrl2Id.entrySet().stream().filter(u->!inputUrls.contains(u.getKey())).forEach(u->{
            influxDbHaRelationDAO.delete(u.getValue());
        });


    }

    public void delete(String id) {
        influxdbClusterDAO.delete(id);
    }

    public void deleteHACluster(String clusterId) {
        influxDbHaRelationDAO.deleteByClusterId(clusterId);
    }

    public List<InfluxdbTenantRelationDO> listAllTenantRelation() {
        return influxdbTenantRelationDAO.listAll();
    }

    public boolean checkExistingRelation(String relatedTenantId) {
        int count = influxdbTenantRelationDAO.countRelationForTenant(relatedTenantId);
        return count > 0;
    }

    public void addTenantRelation(InfluxdbTenantRelationDO influxdbTenantRelationDO) {
        influxdbTenantRelationDAO.add(influxdbTenantRelationDO);
    }

    public void deleteRelationWithinTenant(String relatedTenantId) {
        influxdbTenantRelationDAO.deleteRelationWithinTenant(relatedTenantId);
    }

    public void deleteTenantRelation(String relatedTenantId, String influxdbClusterId) {
        influxdbTenantRelationDAO.delete(relatedTenantId, influxdbClusterId);
    }

    public int countRelationForCluster(String influxdbClusterId) {
        return influxdbTenantRelationDAO.countRelationForCluster(influxdbClusterId);
    }

    public List<InfluxdbTenantRelationDO> listTenantRelationByInfluxdbClusterId(String influxdbClusterId) {
        return influxdbTenantRelationDAO.listTenantRelationByInfluxdbClusterId(influxdbClusterId);
    }

    public List<InfluxdbHaRelationDO>getHaRelationByClusterIds(List<String>clusterIds){
        if(!CollectionUtils.isEmpty(clusterIds)) {
            return influxDbHaRelationDAO.queryByClusterIds(clusterIds);
        }else{
            return new LinkedList<>();
        }
    }
}
