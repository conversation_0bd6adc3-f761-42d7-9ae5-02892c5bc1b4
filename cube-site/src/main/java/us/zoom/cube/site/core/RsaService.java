package us.zoom.cube.site.core;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.EnvironmentAware;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;
import us.zoom.cloud.secrets.spring.annotation.SecretValue;
import us.zoom.infra.utils.KeyUtils;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
@Configuration
public class RsaService implements EnvironmentAware {
    /**
     * store in csms
     */
    @SecretValue("common_private_key")
    private String commonPrivatekey;

    private Environment environment;

    /**
     * store in csms
     */
    @SecretValue("common_public_key")
    private String commonPublicKey;

    @Override
    public void setEnvironment(Environment environment) {
        this.environment = environment;
    }

    /**
     *
     * @param input
     * @return
     * @throws Exception
     */
    public  String encrypt( String input) throws Exception{
        return  KeyUtils.encryptByPublicKey(input, commonPublicKey);
    }

    /**
     *
     * @param input
     * @return
     * @throws Exception
     */
    public  String decrypt(String input ) throws Exception{
        return  KeyUtils.decryptByPrivateKey(input, commonPrivatekey);
    }

    public void setCommonPrivatekey(String commonPrivatekey) {
        this.commonPrivatekey = commonPrivatekey;
    }

    public void setCommonPublicKey(String commonPublicKey) {
        this.commonPublicKey = commonPublicKey;
    }
}
