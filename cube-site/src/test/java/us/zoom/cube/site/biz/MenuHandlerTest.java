package us.zoom.cube.site.biz;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.reflections.Reflections;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.web.bind.annotation.RequestMapping;
import us.zoom.cube.site.CubeSiteApplication;
import us.zoom.cube.site.core.auth.MenuHandler;
import us.zoom.cube.site.infra.utils.IdUtils;
import us.zoom.infra.dao.model.MenuItemDO;
import us.zoom.infra.dao.service.MenuItemDAO;
import us.zoom.infra.enums.MenuTypeEnum;
import us.zoom.infra.model.menu.MenuTree;
import us.zoom.cube.lib.utils.JsonUtils;
import us.zoom.infra.utils.MenuConstants;

import java.util.*;
import java.util.stream.Collectors;

@SpringBootTest(classes = CubeSiteApplication.class,webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)//springboot
public class MenuHandlerTest extends  BaseTest {

    @Autowired
    MenuHandler menuHandler;


    @Mock
    private MenuItemDAO menuItemDAO;


    public static void main(String []gs) {
        String packageName = "us.zoom.cube.site.api";
        Reflections f = new Reflections(packageName);
        // 
        Set<Class<?>> set = f.getTypesAnnotatedWith(RequestMapping.class);
        for (Class<?> c : set) {
            // 
            RequestMapping annotation = c.getAnnotation(RequestMapping.class);
            //
            System.out.println(annotation.value());
        }
    }

    @Test
    public void loadTreeNormal(){
        //normal
        List<MenuItemDO>  menuItemDOS = mockNormalMenuTree();
        menuItemDOS.forEach(menuItemDO ->{
            menuHandler.deleteMenuAndRelaBetweenRoleByResId(menuItemDO.getResourceId());
            menuHandler.addMenuItem(menuItemDO);
        } );

        menuHandler.loadMenuTree();
        MenuTree menuTree = menuHandler.getMenuTreeWitchCheck(new HashSet<>(Arrays.asList("editMenu")));
        System.out.println(JsonUtils.toJsonStringIgnoreExp(menuTree));
        Assertions.assertTrue(menuTree.getResourceId().equals(MenuConstants.DEFAULT_MENU_ROOT), "root error");


        List<MenuTree> sons = menuTree.getSons().stream().filter(item->item.getResourceId().equals("menu_res")).collect(Collectors.toList());
        sons = sons.get(0).getSons().stream().filter(item->item.getResourceId().equals("add_menu_res")||item.getResourceId().equals("editMenu")).collect(Collectors.toList());
        Assertions.assertTrue(sons != null && sons.size() == 2, "no son");

        MenuTree addMenu =  sons.get(0);
        Assertions.assertTrue(addMenu != null && addMenu.getName().equals("addMenu"), "addMenu fail");

        addMenu.getSons();

        MenuTree editMenu =  sons.get(1);
        Assertions.assertTrue(editMenu != null && editMenu.getName().equals("editMenu"), "editMenu fail");
//        Assertions.assertTrue("editMenu not check",editMenu != null && editMenu.isChecked() == false);


        Set<String> urls = menuHandler.getMenuResUrlsByResIds(Arrays.asList("editMenuPage","menu_res"));

        Assertions.assertTrue(editMenu != null && urls.size() == 2, "urls size  fail");
        Assertions.assertTrue(editMenu != null && urls.contains("/api/menu") && urls.contains("/api/editMenuPage"), "urls content  fail");

        menuItemDOS.forEach(menuItemDO ->{
            menuHandler.deleteMenuAndRelaBetweenRoleByResId(menuItemDO.getResourceId());
        } );

    }


    @Test
    public void loadTreeWithCircle(){
        //normal
        List<MenuItemDO>  menuItemDOS = mockCircleMenuTree();
        menuItemDOS.forEach(menuItemDO ->{
            menuHandler.deleteMenuAndRelaBetweenRoleByResId(menuItemDO.getResourceId());
            menuHandler.addMenuItem(menuItemDO);
        } );

        menuHandler.loadMenuTree();
        MenuTree menuTree = menuHandler.getMenuTreeWitchCheck(new HashSet<>());
        System.out.println(JsonUtils.toJsonStringIgnoreExp(menuTree));
        Assertions.assertTrue(menuTree.getResourceId().equals(MenuConstants.DEFAULT_MENU_ROOT), "root error");

        List<MenuTree> sons = menuTree.getSons().stream().filter(item->item.getResourceId().equals("menu_res")).collect(Collectors.toList());
        sons = sons.get(0).getSons().stream().filter(item->item.getResourceId().equals("add_menu_res")||item.getResourceId().equals("editMenu")).collect(Collectors.toList());
        Assertions.assertTrue(sons != null && sons.size() == 1, "no son");


        MenuTree editMenu =  sons.get(0);
        Assertions.assertTrue(editMenu != null && editMenu.getName().equals("editMenu"), "editMenu fail");

        menuItemDOS.forEach(menuItemDO ->{
            menuHandler.deleteMenuAndRelaBetweenRoleByResId(menuItemDO.getResourceId());
        } );

    }



    private List<MenuItemDO> mockCircleMenuTree () {
        List<MenuItemDO> result = new ArrayList<>();
        MenuItemDO menu = mock("menu", MenuConstants.DEFAULT_MENU_ROOT,true,1,"menu_res","/api/menu", MenuTypeEnum.Menu.getCode());


        result.add(menu);
        MenuItemDO addMenu = mock("addMenu", "delMenuPageSon",true,1,"add_menu_res","/api/addMenu", MenuTypeEnum.Menu.getCode());
        result.add(addMenu);
        MenuItemDO addMenuPage = mock("add_menu_res_page", addMenu.getResourceId(),true,1,"add_menu_res_page","/api/addMenuPage", MenuTypeEnum.Api.getCode());
        result.add(addMenuPage);
        MenuItemDO delMenuPage = mock("delMenuPage", addMenu.getResourceId(),true,2,"delMenuPage","/api/delMenuPage", MenuTypeEnum.Api.getCode());
        result.add(delMenuPage);

        MenuItemDO delMenuPageSon = mock("delMenuPageSon", delMenuPage.getResourceId(),true,1,"delMenuPageSon","/api/delMenuPage/delMenuPageSon", MenuTypeEnum.Api.getCode());
        result.add(delMenuPageSon);

        MenuItemDO editMenu = mock("editMenu", menu.getResourceId(),true,2,"editMenu","/api/editMenu", MenuTypeEnum.Menu.getCode());
        result.add(editMenu);
        MenuItemDO editMenuPage = mock("editMenuPage", editMenu.getResourceId(),true,1,"editMenuPage","/api/editMenuPage", MenuTypeEnum.Api.getCode());
        result.add(editMenuPage);

        return result;
    }

    private List<MenuItemDO> mockNormalMenuTree() {
        List<MenuItemDO> result = new ArrayList<>();
        MenuItemDO menu = mock("menu", MenuConstants.DEFAULT_MENU_ROOT,true,1,"menu_res","/api/menu", MenuTypeEnum.Menu.getCode());


        result.add(menu);
        MenuItemDO addMenu = mock("addMenu", menu.getResourceId(),true,1,"add_menu_res","/api/addMenu", MenuTypeEnum.Menu.getCode());
        result.add(addMenu);
        MenuItemDO addMenuPage = mock("add_menu_res_page", addMenu.getResourceId(),true,1,"add_menu_res_page","/api/addMenuPage", MenuTypeEnum.Api.getCode());
        result.add(addMenuPage);
        MenuItemDO delMenuPage = mock("delMenuPage", addMenu.getResourceId(),true,2,"delMenuPage","/api/delMenuPage", MenuTypeEnum.Api.getCode());
        result.add(delMenuPage);

        MenuItemDO editMenu = mock("editMenu", menu.getResourceId(),true,2,"editMenu","/api/editMenu", MenuTypeEnum.Menu.getCode());
        result.add(editMenu);
        MenuItemDO editMenuPage = mock("editMenuPage", editMenu.getResourceId(),true,1,"editMenuPage","/api/editMenuPage", MenuTypeEnum.Api.getCode());
        result.add(editMenuPage);

        return result;
    }


    private MenuItemDO mock(String name,String parentResId,Boolean hidden,Integer order,String resId,String resUrl,Integer type) {
        MenuItemDO menuItemDO = new MenuItemDO();
        menuItemDO.setId(IdUtils.generateId());
        menuItemDO.setType(type);
        menuItemDO.setName(name);
        menuItemDO.setParentResId(parentResId);
        menuItemDO.setHidden(hidden);
        menuItemDO.setMenuOrder(order);
        menuItemDO.setResourceId(resId);
        menuItemDO.setResourceUrl(resUrl);
        return menuItemDO;
    }



}
