package us.zoom.cube.site.api.config;

import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.util.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.reactive.function.client.ClientRequest;
import org.springframework.web.reactive.function.client.ExchangeFilterFunction;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;
import reactor.netty.http.client.HttpClient;
import reactor.netty.resources.ConnectionProvider;
import us.zoom.cluster.commons.jwt.utils.UUID;
import us.zoom.infra.utils.JWTUtils;

import java.time.Duration;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;

import static us.zoom.cube.site.infra.constants.trace.TraceConstant.X_ZM_TRACKING_ID;

@Configuration
@Slf4j
@RequiredArgsConstructor
public class SmartCubeSyncWebClientConfig {
    public static final String SMART_CUBE = "smart-cube";

    @Value("${smartcube.api}")
    private String smartCubeApi;
    @Value("${smart-cube.syncWebClient.maxConnections:200}")
    private Integer syncMaxConnections;

    @Value("${smart-cube.syncWebClient.pendingAcquireTimeout:45}")
    private Integer syncPendingAcquireTimeout;
    @Value("${smart-cube.syncWebClient.connectionTimeout:10000}")
    private Integer syncConnectionTimeout;
    @Value("${smart-cube.syncWebClient.responseTimeout:120}")
    private Integer syncResponseTimeout;
    @Value("${smart-cube.syncWebClient.writeTimeout:60}")
    private Integer syncWriteTimeout;
    @Value("${smart-cube.syncWebClient.readTimeout:120}")
    private Integer syncReadTimeout;


    /**
     * Configure chat history query WebClient
     */
    @Bean("smartCubeCommonlClient")
    public WebClient smartCubeCommonlClient() {
        return createWebClient(smartCubeApi, "smartCubeCommonlClient");
    }

    /**
     * Create common WebClient configuration
     */
    private WebClient createWebClient(String baseUrl, String clientName) {
        // Configure HTTP connection pool
        ConnectionProvider provider = ConnectionProvider.builder(clientName + "-connection-pool")
                .maxConnections(syncMaxConnections)  // Higher connection pool for high concurrency
                .pendingAcquireTimeout(Duration.ofSeconds(syncPendingAcquireTimeout))  // Longer timeout for connection acquisition
                .build();


        // Configure HTTP client
        HttpClient httpClient = HttpClient.create(provider)
                .option(io.netty.channel.ChannelOption.CONNECT_TIMEOUT_MILLIS, syncConnectionTimeout)  // 30 seconds connection timeout
                .responseTimeout(Duration.ofSeconds(syncResponseTimeout))  // 6 minutes response timeout
                .doOnConnected(conn -> conn.addHandlerLast(new io.netty.handler.timeout.ReadTimeoutHandler(syncReadTimeout))  // 6 minutes read timeout
                        .addHandlerLast(new io.netty.handler.timeout.WriteTimeoutHandler(syncWriteTimeout)));  // 5 minutes write timeout


        return WebClient.builder()
                .baseUrl(baseUrl)
                .clientConnector(new ReactorClientHttpConnector(httpClient))
                .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .defaultHeader(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE)
                // Dynamically add Authorization and trackingId
                .filter(ExchangeFilterFunction.ofRequestProcessor(
                        clientRequest -> {
                            String token = getToken();
                            String trackingId = UUID.random();
                            ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
                            if (!Objects.isNull(requestAttributes)) {
                                HttpServletRequest request = requestAttributes.getRequest();
                                String trackId = request.getHeader(X_ZM_TRACKING_ID);
                                if (StringUtils.hasText(trackId)) {
                                    trackingId = trackId;
                                }
                            }
                            return Mono.just(ClientRequest.from(clientRequest)
                                    .header("Authorization", "Bearer " + token)
                                    .header(X_ZM_TRACKING_ID, trackingId)
                                    .build());
                        }
                ))
                // Add request logging functionality
                .filter(ExchangeFilterFunction.ofRequestProcessor(
                        clientRequest -> {
                            List<String> trackId = clientRequest.headers().get(X_ZM_TRACKING_ID);
                            log.info("{} (trackId:{})Request: {} {}",
                                    clientName,
                                    trackId,
                                    clientRequest.method(),
                                    clientRequest.url())
                            ;
                            return Mono.just(clientRequest);
                        }
                ))
                // Add response logging functionality
                .filter(ExchangeFilterFunction.ofResponseProcessor(
                        clientResponse -> {
                            log.info("{} Response status: {}",
                                    clientName,
                                    clientResponse.statusCode());
                            return Mono.just(clientResponse);
                        }
                ))
                // Add error handling
                .filter(ExchangeFilterFunction.ofResponseProcessor(
                        clientResponse -> {
                            if (clientResponse.statusCode().is4xxClientError()) {
                                log.error("{} Client error: {}",
                                        clientName,
                                        clientResponse.statusCode());
                            } else if (clientResponse.statusCode().is5xxServerError()) {
                                log.error("{} Server error: {}",
                                        clientName,
                                        clientResponse.statusCode());
                            }
                            return Mono.just(clientResponse);
                        }
                ))
                .build();
    }

    public String getToken() {
        return JWTUtils.generateToken(SMART_CUBE, SMART_CUBE, 3600L, new HashMap(), new HashMap());
    }
} 