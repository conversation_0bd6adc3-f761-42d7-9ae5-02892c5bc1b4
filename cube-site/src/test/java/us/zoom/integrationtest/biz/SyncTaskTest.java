package us.zoom.integrationtest.biz;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import us.zoom.cube.lib.utils.JsonUtils;
import us.zoom.cube.site.biz.BaseTest;
import us.zoom.cube.site.biz.metricsync.cloudwatch.ForegroundTaskWrapper;
import us.zoom.cube.site.biz.metricsync.cloudwatch.SyncTaskConsumer;
import us.zoom.mq.common.entity.TaskEntity;

import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * <AUTHOR>
 */
public class SyncTaskTest extends BaseTest {

    @Autowired
    private SyncTaskConsumer syncTaskConsumer;

    @Test
    public void testSyncTask() {
        String taskConfig = "";
        TaskEntity<byte[]> objectTaskEntity = new TaskEntity<>();
        objectTaskEntity.setPayload(taskConfig.getBytes(StandardCharsets.UTF_8));
        syncTaskConsumer.onMessage(Lists.newArrayList(objectTaskEntity));
    }
}
