package us.zoom.cube.site.infra.enums.trace;

import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * @author: eason.jia
 * @date: 2024/8/19
 */
public enum FilterOperator {
    EQUAL("="),
    NOT_EQUAL("!="),
    GREATER_THAN(">"),
    GREATER_THAN_OR_EQ(">="),
    LESS_THAN("<"),
    LESS_THAN_OR_EQ("<="),
    IN("in"),
    NOT_IN("nin"),
    CONTAINS("contains"),
    NOT_CONTAINS("ncontains"),
    REGEX("regex"),
    NOT_REGEX("nregex"),
    LIKE("like"),
    NOT_LIKE("nlike"),
    EXISTS("exists"),
    NOT_EXISTS("nexists"),
    HAS("has"),
    NOT_HAS("nhas");

    private String operator;

    FilterOperator(String operator) {
        this.operator = operator;
    }

    public String getOperator() {
        return operator;
    }

    public static FilterOperator from(String filterOperator) {
        for (FilterOperator operator : FilterOperator.values()) {
            if (StringUtils.equalsIgnoreCase(operator.getOperator(), filterOperator)) {
                return operator;
            }
        }
        throw new IllegalArgumentException("illegal filterOperator: " + filterOperator);
    }

    public static final Map<FilterOperator, String> TRACES_OPERATOR_MAPPING = new HashMap<>();

    static {
        TRACES_OPERATOR_MAPPING.put(IN, "IN");
        TRACES_OPERATOR_MAPPING.put(NOT_IN, "NOT IN");
        TRACES_OPERATOR_MAPPING.put(EQUAL, "=");
        TRACES_OPERATOR_MAPPING.put(NOT_EQUAL, "!=");
        TRACES_OPERATOR_MAPPING.put(LESS_THAN, "<");
        TRACES_OPERATOR_MAPPING.put(LESS_THAN_OR_EQ, "<=");
        TRACES_OPERATOR_MAPPING.put(GREATER_THAN, ">");
        TRACES_OPERATOR_MAPPING.put(GREATER_THAN_OR_EQ, ">=");
        TRACES_OPERATOR_MAPPING.put(LIKE, "ILIKE");
        TRACES_OPERATOR_MAPPING.put(NOT_LIKE, "NOT ILIKE");
        TRACES_OPERATOR_MAPPING.put(REGEX, "match(%s, %s)");
        TRACES_OPERATOR_MAPPING.put(NOT_REGEX, "NOT match(%s, %s)");
        TRACES_OPERATOR_MAPPING.put(CONTAINS, "ILIKE");
        TRACES_OPERATOR_MAPPING.put(NOT_CONTAINS, "NOT ILIKE");
        TRACES_OPERATOR_MAPPING.put(EXISTS, "has(%s%s, '%s')");
        TRACES_OPERATOR_MAPPING.put(NOT_EXISTS, "NOT has(%s%s, '%s')");
    }
}
