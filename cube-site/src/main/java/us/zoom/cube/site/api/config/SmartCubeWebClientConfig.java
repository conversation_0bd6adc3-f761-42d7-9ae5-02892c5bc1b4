package us.zoom.cube.site.api.config;

import io.netty.channel.ChannelOption;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.util.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.reactive.function.client.ClientRequest;
import org.springframework.web.reactive.function.client.ExchangeFilterFunction;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;
import reactor.netty.http.client.HttpClient;
import reactor.netty.resources.ConnectionProvider;
import us.zoom.cluster.commons.jwt.utils.UUID;
import us.zoom.infra.utils.JWTUtils;

import java.time.Duration;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;

import static us.zoom.cube.site.infra.constants.trace.TraceConstant.X_ZM_TRACKING_ID;

@Configuration
@Slf4j
@RequiredArgsConstructor
public class SmartCubeWebClientConfig {
    public static final String SMART_CUBE = "smart-cube";

    @Value("${smartcube.api}")
    private String smartCubeApi;
    @Value("${smart-cube.webClient.maxConnections:400}")
    private Integer maxConnections;

    @Value("${smart-cube.webClient.pendingAcquireTimeout:45}")
    private Integer pendingAcquireTimeout;
    @Value("${smart-cube.webClient.connectionTimeout:10000}")
    private Integer connectionTimeout;


    @Value("${smart-cube.webClient.maxIdleTimeMs:300000}")
    private Integer maxIdleTimeMs;
    @Value("${smart-cube.webClient.maxLifeTimeMs:1800000}")
    private Integer maxLifeTimeMs;
    @Value("${smart-cube.webClient.evictIntervalMs:30000}")
    private Integer evictIntervalMs;

    /**
     * Configure AI Hub WebClient
     */
    @Bean("smartCubeChatClient")
    public WebClient smartCubeChatClient() {
        return createWebClient(smartCubeApi, "smartCubeChatClient");
    }


    /**
     * Create common WebClient configuration
     */
    private WebClient createWebClient(String baseUrl, String clientName) {
        // Configure HTTP connection pool
        ConnectionProvider provider = ConnectionProvider.builder(clientName + "-connection-pool")
                .maxConnections(maxConnections)
                .pendingAcquireTimeout(Duration.ofSeconds(pendingAcquireTimeout))
                .maxIdleTime(Duration.ofMillis(maxIdleTimeMs))
                .maxLifeTime(Duration.ofMillis(maxLifeTimeMs))
                .evictInBackground(Duration.ofMillis(evictIntervalMs))
                .build();


        // Configure HTTP client
        HttpClient httpClient = HttpClient.create(provider)
                .option(ChannelOption.CONNECT_TIMEOUT_MILLIS, connectionTimeout)  // Connection timeout 10 seconds
                .option(ChannelOption.SO_KEEPALIVE, true)
                .keepAlive(true)
                .compress(true);

        return WebClient.builder()
                .baseUrl(baseUrl)
                .clientConnector(new ReactorClientHttpConnector(httpClient))
                .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .defaultHeader(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE)
                // Dynamically add Authorization and trackingId
                .filter(ExchangeFilterFunction.ofRequestProcessor(
                        clientRequest -> {
                            String token = getToken();
                            String trackingId = UUID.random();
                            ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
                            if (!Objects.isNull(requestAttributes)) {
                                HttpServletRequest request = requestAttributes.getRequest();
                                String trackId = request.getHeader(X_ZM_TRACKING_ID);
                                if (StringUtils.hasText(trackId)) {
                                    trackingId = trackId;
                                }
                            }
                            return Mono.just(ClientRequest.from(clientRequest)
                                    .header("Authorization", "Bearer " + token)
                                    .header(trackingId, X_ZM_TRACKING_ID)
                                    .build());
                        }
                ))
                // Add request logging functionality
                .filter(ExchangeFilterFunction.ofRequestProcessor(
                        clientRequest -> {
                            List<String> trackId = clientRequest.headers().get(X_ZM_TRACKING_ID);
                            log.info("{} (trackId:{})Request: {} {}",
                                    clientName,
                                    trackId,
                                    clientRequest.method(),
                                    clientRequest.url())
                            ;
                            return Mono.just(clientRequest);
                        }
                ))
                // Add response logging functionality
                .filter(ExchangeFilterFunction.ofResponseProcessor(
                        clientResponse -> {
                            log.info("{} Response status: {}",
                                    clientName,
                                    clientResponse.statusCode());
                            return Mono.just(clientResponse);
                        }
                ))
                // Add error handling
                .filter(ExchangeFilterFunction.ofResponseProcessor(
                        clientResponse -> {
                            if (clientResponse.statusCode().is4xxClientError()) {
                                log.error("{} Client error: {}",
                                        clientName,
                                        clientResponse.statusCode());
                            } else if (clientResponse.statusCode().is5xxServerError()) {
                                log.error("{} Server error: {}",
                                        clientName,
                                        clientResponse.statusCode());
                            }
                            return Mono.just(clientResponse);
                        }
                ))
                .build();
    }

    public String getToken() {
        return JWTUtils.generateToken(SMART_CUBE, SMART_CUBE, 3600L, new HashMap(), new HashMap());
    }
} 