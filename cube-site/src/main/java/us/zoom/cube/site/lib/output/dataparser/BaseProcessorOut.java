package us.zoom.cube.site.lib.output.dataparser;

public class BaseProcessorOut  implements Comparable<BaseProcessorOut>{
    private String id;
    private String name;
    private Integer order;
    private String dataParserPipelineId;
    private String dataParserId;
    /**
     * ref the ProcessorTypeEnum
     */
    private String type;


    public BaseProcessorOut() {
    }

    public BaseProcessorOut(String id, String name, Integer order, String dataParserPipelineId, String dataParserId, String type) {
        this.id = id;
        this.name = name;
        this.order = order;
        this.dataParserPipelineId = dataParserPipelineId;
        this.dataParserId = dataParserId;
        this.type = type;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getOrder() {
        return order;
    }

    public void setOrder(Integer order) {
        this.order = order;
    }

    public String getDataParserPipelineId() {
        return dataParserPipelineId;
    }

    public void setDataParserPipelineId(String dataParserPipelineId) {
        this.dataParserPipelineId = dataParserPipelineId;
    }

    public String getDataParserId() {
        return dataParserId;
    }

    public void setDataParserId(String dataParserId) {
        this.dataParserId = dataParserId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    @Override
    public int compareTo(BaseProcessorOut obj) {

        if(null == obj || null == obj.getOrder()){
            return  1;
        }

        if(null == order){
            return -1;
        }
        return this.getOrder()-obj.getOrder();
    }
}
