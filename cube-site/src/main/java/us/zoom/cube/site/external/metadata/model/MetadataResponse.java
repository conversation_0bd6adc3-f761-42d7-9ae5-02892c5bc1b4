package us.zoom.cube.site.external.metadata.model;

import lombok.Data;
import java.util.List;
import java.util.Map;

@Data
public class MetadataResponse {
    private boolean success;
    private int errorCode;
    private String errorMessage;
    private MetadataData data;

    @Data
    public static class MetadataData {
        private int count;
        private int totalPage;
        private int pageNumber;
        private int pageSize;
        private List<MetadataItem> list;
    }

    @Data
    public static class MetadataItem {
        private String id;
        private Map<String, String> attributes;
        private Object tags;
        private int version;
        private String creator;
        private String modifier;
        private String createTime;
        private String modifyTime;
    }
} 