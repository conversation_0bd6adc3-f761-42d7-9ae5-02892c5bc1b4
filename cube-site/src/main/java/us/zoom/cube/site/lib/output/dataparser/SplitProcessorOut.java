package us.zoom.cube.site.lib.output.dataparser;

import us.zoom.cube.lib.config.hub.processor.SplitSchema;

public class SplitProcessorOut extends BaseProcessorOut {
    private String sourceField;
    private SplitSchema schema;

    public SplitProcessorOut(String id, String name, Integer order, String dataParserPipelineId, String dataParserId, String type, String sourceField, SplitSchema schema) {
        super(id, name, order, dataParserPipelineId, dataParserId, type);
        this.sourceField = sourceField;
        this.schema = schema;
    }

    public SplitProcessorOut() {

    }

    public String getSourceField() {
        return sourceField;
    }

    public void setSourceField(String sourceField) {
        this.sourceField = sourceField;
    }

    public SplitSchema getSchema() {
        return schema;
    }

    public void setSchema(SplitSchema schema) {
        this.schema = schema;
    }
}
