package us.zoom.cube.site.biz;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import us.zoom.cube.lib.agent.AgentBaseInfo;
import us.zoom.cube.lib.agent.AgentCfg;
import us.zoom.cube.lib.agent.AgentHeartInfo;
import us.zoom.cube.lib.common.CloudTypeEnum;
import us.zoom.cube.lib.common.HubChannelType;
import us.zoom.cube.lib.common.ServerTypeEnum;
import us.zoom.cube.site.core.ServerHandler;
import us.zoom.cube.site.infra.utils.IdUtils;
import us.zoom.cube.site.lib.input.ServerInput;
import us.zoom.cube.site.lib.query.PageQuery;
import us.zoom.cube.site.lib.query.ServerQuery;
import us.zoom.infra.dao.model.HubConnectionDO;
import us.zoom.infra.dao.model.ServerDO;
import us.zoom.infra.thread.ThreadLocalStore;
import us.zoom.infra.utils.Instance;

import java.util.*;
import java.util.stream.Collectors;

public class AgentServiceTest extends BaseTest {
    @Autowired
    AgentService agentService;

    @Autowired
    ServerHandler serverHandler;
    private static List<ServerDO> serverDOList = new ArrayList();
    String clusterId = "dev";
    String regionId = "va3";

    @Test
    public void newTest() throws InterruptedException {
        for (int i = 0; i <= 5; i++) {
            AgentHeartInfo agentHeartInfo = new AgentHeartInfo();
            agentHeartInfo.setHost("rtcwebgw-webrtcab-ubuntu-02");
            agentHeartInfo.setAppName("rtcab");
            agentHeartInfo.setIp("**************");
            agentHeartInfo.setTs(System.currentTimeMillis());
            agentHeartInfo.setCsp("GCP");
            agentHeartInfo.setRegionId("TW_RTCAB");
            agentHeartInfo.setIpPublic("**************");
            agentHeartInfo.setDiskType(1);
            agentHeartInfo.setInstanceId("rtcwebgw-webrtcab-ubuntu-02.asia-east1-b.c.gatewaydevops-service");
            agentHeartInfo.setClusterId("us01");
            List<AgentHeartInfo> list = Collections.singletonList(agentHeartInfo);
//            agentService.heart(list);
            Thread.sleep(60000);
        }


    }


    public void mockData() {
        ThreadLocalStore.setTenantInfoLocal("tenant1");

        PageQuery<ServerQuery> pageQuery = new PageQuery<>();
        ServerQuery serverQuery = new ServerQuery();
        serverQuery.setName("TestAgent");
        serverQuery.setType(ServerTypeEnum.agent.name());
        pageQuery.setPageSize(600);
        pageQuery.setPageIndex(1);
        serverQuery.setUnitTag("luisPressUnit");
        pageQuery.setQueryPara(serverQuery);
        serverDOList = serverHandler.findByNameLike(pageQuery);
        if (serverDOList.size() > 0) {
            return;
        }
        for (int i = 0; i < 10; i++) {
            ServerDO server = serverHandler.getServerByHost("TestHub" + i, ServerTypeEnum.agent.name());
            if (null != server) {
                continue;
            }
            ServerInput serverInput = new ServerInput();
            serverInput.setType(ServerTypeEnum.hub.name());
            serverInput.setTenantId("tenant1");
            serverInput.setChannelType(i % 2 == 0 ? HubChannelType.fast.name() : HubChannelType.normal.name());
            serverInput.setCloudType(CloudTypeEnum.COLO.getType());
            serverInput.setClusterId(clusterId);
            serverInput.setRegionId(regionId);
            serverInput.setHost("TestHub" + i);
            serverInput.setIp("192.168.1." + i);
            serverInput.setId(IdUtils.generateId());
            serverHandler.addServer(serverInput);
        }

        for (int i = 283; i < 600; i++) {
            ServerDO server = serverHandler.getServerByHost("TestAgent" + i, ServerTypeEnum.agent.name());
            if (null != server) {
                continue;
            } else {
                serverDOList.add(server);
            }
            ServerInput serverInput = new ServerInput();
            serverInput.setType(ServerTypeEnum.agent.name());
            serverInput.setTenantId("tenant1");
            serverInput.setAgentKey(IdUtils.generateId());
            serverInput.setCloudType(CloudTypeEnum.COLO.getType());
            serverInput.setClusterId(clusterId);
            serverInput.setRegionId(regionId);
            serverInput.setHost("TestAgent" + i);
            serverInput.setIp("192.168." + Math.abs(new Random().nextInt()) % 255 + "." + Math.abs(new Random().nextInt()) % 255);
            serverInput.setId(IdUtils.generateId());
            ServerDO serverDO = new ServerDO();
            BeanUtils.copyProperties(serverInput, serverDO);
            serverDOList.add(serverDO);
            serverHandler.addServer(serverInput);
        }
    }

    @Test
    public void test() {
        AgentCfg agentCfg = new AgentCfg();
        ServerDO serverDO = serverDOList.get(0);
        AgentBaseInfo agentBaseInfo = new AgentBaseInfo();
        agentBaseInfo.setHost(serverDO.getHost());
        agentBaseInfo.setIp(serverDO.getIp());
        //
        agentService.rebalancedHubConnection(agentCfg, serverDO, agentBaseInfo);

        agentBaseInfo.setFastHubIp("***********");
        agentService.rebalancedHubConnection(agentCfg, serverDO, agentBaseInfo);


        agentBaseInfo.setFastHubIp("***********");
        agentBaseInfo.setNormalHubIp("***********");
        agentService.rebalancedHubConnection(agentCfg, serverDO, agentBaseInfo);

        for (int i = 0; i < 200; i++) {
            serverDO = serverDOList.get(i);
            agentBaseInfo = new AgentBaseInfo();
            agentBaseInfo.setHost(serverDO.getHost());
            agentBaseInfo.setIp(serverDO.getIp());
            agentService.rebalancedHubConnection(agentCfg, serverDO, agentBaseInfo);

            agentBaseInfo.setFastHubIp(agentCfg.getFastHubIps().get(Math.abs(new Random().nextInt()) % agentCfg.getFastHubIps().size()));
            agentBaseInfo.setNormalHubIp(agentCfg.getNormalHubIps().get(Math.abs(new Random().nextInt()) % agentCfg.getNormalHubIps().size()));
            agentService.rebalancedHubConnection(agentCfg, serverDO, agentBaseInfo);

        }

        List<HubConnectionDO> connections = serverHandler.listHubConnByHubIps(Arrays.asList("***********", "***********", "***********", "***********", "***********", "***********", "***********", "***********", "***********", "***********"));
        Map<String, List<HubConnectionDO>> hubConnMap = Instance.ofNullable(connections).stream().collect(Collectors.groupingBy(item -> item.getHubIp()));
        Integer avgConnCount = hubConnMap.entrySet().stream().mapToInt(item -> item.getValue().size()).sum() / hubConnMap.size();
        hubConnMap.entrySet().forEach(item -> {
            int size = Instance.ofNullable(item.getValue()).size();
            System.out.println(item.getKey() + ":" + size);
            Assertions.assertTrue(size < (avgConnCount + AgentService.TOLERATE_VALUE));
        });

        serverDO = serverDOList.get(0);
        agentBaseInfo = new AgentBaseInfo();
        agentBaseInfo.setHost(serverDO.getHost());
        agentBaseInfo.setIp(serverDO.getIp());
        agentBaseInfo.setFastHubIp("***********");
        agentBaseInfo.setNormalHubIp("***********");
        agentService.rebalancedHubConnection(agentCfg, serverDO, agentBaseInfo);
        Assertions.assertTrue(matchIp(Arrays.asList("***********"), agentCfg.getFastHubIps()));
        Assertions.assertTrue(matchIp(Arrays.asList("***********"), agentCfg.getNormalHubIps()));

        agentService.rebalancedHubConnection(agentCfg, serverDO, agentBaseInfo);
        Assertions.assertTrue(matchIp(Arrays.asList("***********"), agentCfg.getFastHubIps()));
        Assertions.assertTrue(matchIp(Arrays.asList("***********"), agentCfg.getNormalHubIps()));
        agentService.rebalancedHubConnection(agentCfg, serverDO, agentBaseInfo);
        Assertions.assertTrue(matchIp(Arrays.asList("***********"), agentCfg.getFastHubIps()));
        Assertions.assertTrue(matchIp(Arrays.asList("***********"), agentCfg.getNormalHubIps()));

//        Assertions.assertTrue(matchIp( Arrays.asList("***********","***********","***********"),agentCfg.getNormalHubIps()) && notContains( Arrays.asList("***********","***********"),agentCfg.getFastHubIps()));
    }


    private boolean notContains(List<String> one, List<String> two) {

        for (String item : one) {
            if (two.contains(item)) {
                return false;
            }
        }
        return true;
    }

    private boolean matchIp(List<String> one, List<String> two) {

        for (String item : one) {
            if (!two.contains(item)) {
                return false;
            }
        }
        return true;
    }

    private boolean onlyMatchIp(List<String> one, List<String> two) {
        if (one.size() != two.size()) {
            return false;
        }

        for (String item : one) {
            if (!two.contains(item)) {
                return false;
            }
        }
        return true;
    }
}
