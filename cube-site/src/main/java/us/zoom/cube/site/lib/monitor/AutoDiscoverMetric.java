package us.zoom.cube.site.lib.monitor;


import lombok.Builder;
import lombok.Data;
import us.zoom.cube.sdk.annotation.CubeMonitorLog;
import us.zoom.cube.sdk.annotation.Field;
import us.zoom.cube.sdk.annotation.Tag;

@Data
@Builder
@CubeMonitorLog(measure = "AutoDiscoverMetric")
public class AutoDiscoverMetric {
    @Tag
    private String switchStatus;
    @Field
    private Integer totalSize;
    @Field
    private Integer updateSize;
    @Field
    private Integer addSize;
    @Field
    private Long dbCost;
    @Field
    private Long preCost;
    @Field
    private String error;
}
