package us.zoom.cube.site.core;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import us.zoom.infra.dao.model.DataParserLabelMysqlProcessorDO;
import us.zoom.infra.dao.service.DataParserLabelMysqlProcessorDAO;

import java.util.List;

@Component
public class DataParserLabelMysqlProcessorHandler {


    @Autowired
    private DataParserLabelMysqlProcessorDAO dataParserLabelMysqlProcessorDAO;


    public void addLabelMysqlProcessor(DataParserLabelMysqlProcessorDO labelMysqlProcessorDO) {
        Assert.notNull(labelMysqlProcessorDO,"label mysql processor is null !");
        dataParserLabelMysqlProcessorDAO.add(labelMysqlProcessorDO);
    }



    public void editLabelMysqlProcessor(DataParserLabelMysqlProcessorDO labelMysqlProcessorDO) {
        Assert.notNull(labelMysqlProcessorDO,"label mysql processor   is null !");
        dataParserLabelMysqlProcessorDAO.edit(labelMysqlProcessorDO);
    }

    public void delLabelMysqlProcessor(String id) {
        Assert.notNull(id,"id is null !");
        dataParserLabelMysqlProcessorDAO.del(id);
    }

    public void delLabelMysqlProcessorByDataParserPipelineId(String dataParserPipelineId) {
        Assert.notNull(dataParserPipelineId,"dataParserPipelineId is null !");
        dataParserLabelMysqlProcessorDAO.delByDataParserPipelineId(dataParserPipelineId);
    }

    public DataParserLabelMysqlProcessorDO getLabelMysqlProcessorById(String id) {
        Assert.notNull(id,"id is null !");
        return dataParserLabelMysqlProcessorDAO.getLabelProcessorById(id);
    }

    public List<DataParserLabelMysqlProcessorDO> listByPipelineIds(List<String> pipeLineIds) {
        return dataParserLabelMysqlProcessorDAO.listByPipelineIds(pipeLineIds);
    }
    public void delLabelMysqlProcessorByPipelineIds(List<String> pipeLineIds) {
        if(CollectionUtils.isEmpty(pipeLineIds)){
            return;
        }
        dataParserLabelMysqlProcessorDAO.delByPipelindIds(pipeLineIds);
    }
}
