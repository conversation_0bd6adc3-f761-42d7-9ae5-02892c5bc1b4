package us.zoom.cube.site.biz;


import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import us.zoom.cube.lib.common.AuthEncrTypeEnum;
import us.zoom.cube.lib.common.QueueTypeEnum;
import us.zoom.cube.lib.common.SwitchEnum;
import us.zoom.cube.lib.common.TaskTypeEnum;
import us.zoom.cube.site.core.*;
import us.zoom.cube.site.lib.BasePara;
import us.zoom.cube.site.lib.ResponseObject;
import us.zoom.cube.site.lib.input.EnvironmentTaskRelaInput;
import us.zoom.cube.site.lib.input.TaskQueueInput;
import us.zoom.cube.site.lib.input.dataparser.SetDefaultInput;
import us.zoom.infra.dao.model.EnvironmentTaskRelaDO;
import us.zoom.infra.dao.model.KafkaClusterDO;
import us.zoom.infra.dao.model.TaskQueueDO;
import us.zoom.infra.dao.model.UnitTagDO;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @Author: luis.zheng
 * @Date: 2021/8/18 10:56
 */
public class TaskQueueServiceTest {
    private TaskQueueService taskQueueService;

    private TaskQueueHandler taskQueueHandler;

    private UnitTagHandler unitTagHandler;

    private KafkaClusterHandler kafkaClusterHandler;

    private KafkaQueueHandler kafkaQueueHandler;

    private DataFlowHandler dataFlowHandler;

    private AsyncMqQueueHandler asyncMqQueueHandler;

    private EnvironmentTaskRelaHandler environmentTaskRelaHandler;

    private CalculationHandler calculationHandler;

    private AuthService authService;

    private String userId = "userId-10001";
    private String id = "id-10001";

    @BeforeEach
    public void before() {
        taskQueueService = Mockito.mock(TaskQueueService.class);
        taskQueueHandler = Mockito.mock(TaskQueueHandler.class);
        unitTagHandler = Mockito.mock(UnitTagHandler.class);
        kafkaClusterHandler = Mockito.mock(KafkaClusterHandler.class);
        authService = Mockito.mock(AuthService.class);
        asyncMqQueueHandler = Mockito.mock(AsyncMqQueueHandler.class);
        kafkaQueueHandler = Mockito.mock(KafkaQueueHandler.class);
        dataFlowHandler = Mockito.mock(DataFlowHandler.class);
        environmentTaskRelaHandler = Mockito.mock(EnvironmentTaskRelaHandler.class);
        calculationHandler = Mockito.mock(CalculationHandler.class);
//        Mockito.doNothing().when(authService).mustAdmin(userId);
        Mockito.doNothing().when(authService).checkAuth(new BasePara());
        Mockito.when(kafkaClusterHandler.hasSameName("name")).thenReturn(true);

        taskQueueService = new TaskQueueService(taskQueueHandler, unitTagHandler, kafkaClusterHandler, authService, kafkaQueueHandler, asyncMqQueueHandler,dataFlowHandler,environmentTaskRelaHandler,calculationHandler);
    }

    private String unitTagId = "unitTagId";

    @Test
    public void testAddCheckUnitTagId() throws Exception {
        TaskQueueInput input = createInput();
        Mockito.when(unitTagHandler.getById(unitTagId)).thenReturn(null);
        Mockito.when(kafkaClusterHandler.getSecurityById(input.getQueueCluster().get(0).getQueueClusterId())).thenReturn(new KafkaClusterDO());
        IllegalArgumentException err = Assertions.assertThrows(IllegalArgumentException.class, () -> {
            taskQueueService.add(input);
        });
        Assertions.assertEquals(err.getMessage(), "unit tag not exist, id:" + input.getUnitTagId());
    }

    @Test
    public void testAddCheckKafkaClusterId() {
        TaskQueueInput input = createInput();
        Mockito.when(unitTagHandler.getById(unitTagId)).thenReturn(new UnitTagDO());
        Mockito.when(taskQueueHandler.findByName(input.getName())).thenReturn(null);
        Mockito.when(kafkaClusterHandler.countById(input.getQueueCluster().get(0).getQueueClusterId())).thenReturn(0);
        IllegalArgumentException err = Assertions.assertThrows(IllegalArgumentException.class, () -> {
            taskQueueService.add(input);
        });
        Assertions.assertEquals(err.getMessage(), "KafkaCluster not exist, id:" + input.getQueueCluster().get(0).getQueueClusterId());

        Mockito.when(kafkaClusterHandler.countById(input.getQueueCluster().get(0).getQueueClusterId())).thenReturn(3);
        List<EnvironmentTaskRelaDO> list = new ArrayList<>();
        Mockito.when(environmentTaskRelaHandler.addOrUpdate(input.getId(), list)).thenReturn(true);
        taskQueueService.add(input);
    }

    @Test
    public void testAddTaskQueueExisted() {
        TaskQueueInput input = createInput();
        Mockito.when(unitTagHandler.getById(unitTagId)).thenReturn(new UnitTagDO());
        Mockito.when(kafkaClusterHandler.countById(input.getQueueCluster().get(0).getQueueClusterId())).thenReturn(1);
        TaskQueueDO taskQueueDOTmp = new TaskQueueDO();
        taskQueueDOTmp.setName("taskQueueName");
        Mockito.when(taskQueueHandler.findUnique(input.getQueueCluster().get(0).getQueueClusterId(), input.getGroupId(), input.getTopic())).thenReturn(taskQueueDOTmp);
        IllegalArgumentException err = Assertions.assertThrows(IllegalArgumentException.class, () -> {
            taskQueueService.add(input);
        });
        Assertions.assertEquals(err.getMessage(), "Repeat with " + taskQueueDOTmp.getName());

        Mockito.when(kafkaClusterHandler.countById(input.getQueueCluster().get(0).getQueueClusterId())).thenReturn(3);
        Mockito.when(taskQueueHandler.findUnique(input.getQueueCluster().get(0).getQueueClusterId(), input.getGroupId(), input.getTopic())).thenReturn(null);
        taskQueueService.add(input);
    }

    @Test
    public void testAddCheck() {
        TaskQueueInput input = createInput();
        Mockito.when(unitTagHandler.getById(unitTagId)).thenReturn(new UnitTagDO());
        Mockito.when(kafkaClusterHandler.countById(input.getQueueCluster().get(0).getQueueClusterId())).thenReturn(1);
        ResponseObject responseObject = taskQueueService.add(input);
        Assertions.assertEquals(responseObject.getStatus(), "success");
    }

    @Test
    public void testEditExist() {
        TaskQueueInput input = createInput();
        Mockito.when(unitTagHandler.getById(unitTagId)).thenReturn(new UnitTagDO());
        Mockito.when(taskQueueHandler.findByName(input.getName())).thenReturn(null);
        Mockito.when(kafkaClusterHandler.countById(input.getQueueCluster().get(0).getQueueClusterId())).thenReturn(1);
        Mockito.when(taskQueueHandler.getById(input.getId())).thenReturn(null);
        IllegalArgumentException err = Assertions.assertThrows(IllegalArgumentException.class, () -> {
            taskQueueService.edit(input);
        });
        Assertions.assertEquals(err.getMessage(), "old TaskQueueDO is null");
    }

    @Test
    public void testEditIdIsNull() {
        TaskQueueInput input = createInput();
        Mockito.when(unitTagHandler.getById(unitTagId)).thenReturn(new UnitTagDO());
        Mockito.when(kafkaClusterHandler.countById(input.getQueueCluster().get(0).getQueueClusterId())).thenReturn(1);
        input.setId(null);
        Mockito.when(taskQueueHandler.getById(input.getId())).thenReturn(null);
        IllegalArgumentException err = Assertions.assertThrows(IllegalArgumentException.class, () -> {
            taskQueueService.edit(input);
        });
        Assertions.assertEquals(err.getMessage(), "id is blank");
    }

    @Test
    public void testEditNotRepeat() {
        TaskQueueInput input = createInput();
        Mockito.when(unitTagHandler.getById(unitTagId)).thenReturn(new UnitTagDO());
        Mockito.when(kafkaClusterHandler.countById(input.getQueueCluster().get(0).getQueueClusterId())).thenReturn(1);
        Mockito.when(taskQueueHandler.getById(input.getId())).thenReturn(new TaskQueueDO());
        Mockito.when(taskQueueHandler.findUniqueNotId(input.getTaskType(), input.getGroupId(), input.getTopic(), input.getId())).thenReturn(null);
        taskQueueService.edit(input);
    }

    @Test
    public void testEditRepeat() {
        TaskQueueInput input = createInput();
        Mockito.when(unitTagHandler.getById(unitTagId)).thenReturn(new UnitTagDO());
        Mockito.when(kafkaClusterHandler.countById(input.getQueueCluster().get(0).getQueueClusterId())).thenReturn(1);
        Mockito.when(taskQueueHandler.getById(input.getId())).thenReturn(new TaskQueueDO());
        TaskQueueDO taskQueueDOTmp = new TaskQueueDO();
        Mockito.when(taskQueueHandler.findByNameNotId(input.getName(), input.getId())).thenReturn(taskQueueDOTmp);
        IllegalArgumentException err = Assertions.assertThrows(IllegalArgumentException.class, () -> {
            taskQueueService.edit(input);
        });
        Assertions.assertEquals(err.getMessage(), "Name already exists");
    }

    @Test
    public void testSetDefaultIdIsNull() {
        SetDefaultInput input = new SetDefaultInput();
        input.setId(null);
        input.setIsDefault(SwitchEnum.no.name());
        Mockito.when(taskQueueHandler.getById(id)).thenReturn(new TaskQueueDO());
        IllegalArgumentException err = Assertions.assertThrows(IllegalArgumentException.class, () -> {
            taskQueueService.setDefault(input);
        });
        Assertions.assertEquals(err.getMessage(), "id is null");
    }

    @Test
    public void testSetDefaultIsDefault() {
        SetDefaultInput input = new SetDefaultInput();
        input.setId(id);
        input.setIsDefault("SwitchEnum.no.name()");
        Mockito.when(taskQueueHandler.getById(id)).thenReturn(new TaskQueueDO());
        IllegalArgumentException err = Assertions.assertThrows(IllegalArgumentException.class, () -> {
            taskQueueService.setDefault(input);
        });
        Assertions.assertEquals(err.getMessage(), "isDefault " + "SwitchEnum.no.name()" + " not exist");
    }

    public TaskQueueDO create() {
        TaskQueueDO taskQueueDO = new TaskQueueDO();
        taskQueueDO.setName("name");
        taskQueueDO.setTopic("topic");
        taskQueueDO.setUnitTagId("unitTagId");
        taskQueueDO.setGroupId("groupId");
        taskQueueDO.setSessionTimeout(200000);
        taskQueueDO.setThreadCount(2);
        taskQueueDO.setPollSize(200);
        taskQueueDO.setProtocol(AuthEncrTypeEnum.saslSsl.name());
        taskQueueDO.setIsDefault(SwitchEnum.yes.name());
        taskQueueDO.setKafkaClusterId("kafkaClusterId");
        taskQueueDO.setQueueType(QueueTypeEnum.kafka.name());
        taskQueueDO.setTaskType(TaskTypeEnum.calc.name());
        taskQueueDO.setCreateTime(new Date());
        taskQueueDO.setModifyTime(new Date());
        return taskQueueDO;
    }

    public TaskQueueInput createInput() {
        TaskQueueInput input = new TaskQueueInput();
        input.setId("id");
        input.setName("name");
        input.setTopic("topic");
        input.setUnitTagId("unitTagId");
        input.setGroupId("groupId");
        input.setSessionTimeout(200000);
        input.setThreadCount(2);
        input.setPollSize(200);
        input.setProtocol(AuthEncrTypeEnum.saslSsl.name());
        input.setIsDefault(SwitchEnum.yes.name());
        createEnvTaskRelaInput();
        input.setTaskType(TaskTypeEnum.calc.name());

        List<EnvironmentTaskRelaInput> environmentTaskRelaInputs = new ArrayList<>();
        EnvironmentTaskRelaInput environmentTaskRelaInput =createEnvTaskRelaInput();
        environmentTaskRelaInputs.add(environmentTaskRelaInput);
        input.setQueueCluster(environmentTaskRelaInputs);

        return input;
    }

    private EnvironmentTaskRelaInput createEnvTaskRelaInput(){
        EnvironmentTaskRelaInput environmentTaskRelaInput = new EnvironmentTaskRelaInput();
        environmentTaskRelaInput.setQueueType("kafka");
        environmentTaskRelaInput.setEnvironmentName("MAIN");
        environmentTaskRelaInput.setQueueClusterId("test-id");
        environmentTaskRelaInput.setQueueClusterName("test-cluster");

        return environmentTaskRelaInput;
    }
}
