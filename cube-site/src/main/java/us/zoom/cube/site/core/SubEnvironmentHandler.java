package us.zoom.cube.site.core;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.EnvironmentAware;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import us.zoom.cube.site.biz.SysParaService;
import us.zoom.cube.site.core.config.CacheLoader;
import us.zoom.cube.site.core.config.ClickhouseHandlerFactory;
import us.zoom.cube.site.core.config.EnvironmentParamCache;
import us.zoom.cube.site.lib.query.SubEnvironmentQuery;
import us.zoom.infra.dao.model.EnvironmentDO;
import us.zoom.infra.dao.model.SubEnvironmentDO;
import us.zoom.infra.dao.service.EnvironmentDAO;
import us.zoom.infra.dao.service.SubEnvironmentDAO;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @author: canyon.li
 * @date: 2023/03/07
 **/
@Component
@Slf4j
public class SubEnvironmentHandler implements CacheLoader {
    @Autowired
    private SubEnvironmentDAO subEnvironmentDAO;

    @Autowired
    private EnvironmentParamCache environmentParamCache;

    @Autowired
    private TenantHandler tenantHandler;

    @Autowired
    private ClickhouseHandlerFactory clickhouseHandlerFactory;

    @Autowired
    private SysParaService sysParaService;

    private static final Map<String, String> ALARM_INSIGHT_ENV_CONVERT = ImmutableMap.of("main", "MAIN", "standby", "standby");

    private static final Map<String, String> DATA_QUERY_ENV_CONVERT = ImmutableMap.of("main", "MAIN", "standby", "standby");

    /**
     * serviceName -> SubEnvironmentDO
     */
    Map<String, SubEnvironmentDO> subEnvConfigCache = Maps.newHashMap();

    private static final String PERF = "perf";

    private static final String SERVICE_PREFIX = "service_";

    @Override
    public void load() {
        long begin = System.currentTimeMillis();
        log.info("Begin loading subEnvironmentList!");
        List<SubEnvironmentDO> subEnvironmentList = subEnvironmentDAO.searchAll();
        log.info("subEnvironmentList size = {}", subEnvironmentList.size());
        this.subEnvConfigCache = subEnvironmentList.stream().collect(Collectors.toMap(SubEnvironmentDO::generateCacheKey, subEnvCfg -> subEnvCfg, (a, b) -> a));
        log.info("Loading subEnv to configCache finished, time cost = {}", (System.currentTimeMillis() - begin));
    }

    @Transactional(rollbackFor = Exception.class)
    public void addSubEnvironment(SubEnvironmentDO subEnvironmentDO) {
        subEnvironmentDAO.add(subEnvironmentDO);
    }

    @Transactional(rollbackFor = Exception.class)
    public void editSubEnvironment(SubEnvironmentDO subEnvironmentDO) {
        subEnvironmentDAO.edit(subEnvironmentDO);
    }

    public List<SubEnvironmentDO> searchSubEnvironment() {
        return subEnvironmentDAO.searchAll();

    }

    public void delSubEnvironment(String id) {
        subEnvironmentDAO.del(id);
    }

    public String getAlarmInsightEnv(String serviceName) {
        String key = SERVICE_PREFIX + serviceName;
        SubEnvironmentDO subEnvironmentDO = subEnvConfigCache.get(key);
        if (Objects.isNull(subEnvironmentDO)) {
            return null;
        }
        String alarmInsightEnv = subEnvironmentDO.getAlarmInsightEnv();
        String configEnv = environmentParamCache.getCurrConfigEnv();
        if (configEnv != null && configEnv.startsWith(PERF)) {
            return ALARM_INSIGHT_ENV_CONVERT.getOrDefault(alarmInsightEnv, alarmInsightEnv);
        }
        return subEnvironmentDO.getAlarmInsightEnv();
    }

    public String getDataQueryEnv(String service) {
        String key = SERVICE_PREFIX + service;
        SubEnvironmentDO subEnvironmentDO = subEnvConfigCache.get(key);
        if (Objects.isNull(subEnvironmentDO)) {
            return null;
        }
        String dataQueryEnv = subEnvironmentDO.getDataQueryEnv();
        String configEnv = environmentParamCache.getCurrConfigEnv();
        if ((configEnv != null) && configEnv.startsWith(PERF)) {
            return DATA_QUERY_ENV_CONVERT.getOrDefault(dataQueryEnv, dataQueryEnv);
        }
        return dataQueryEnv;
    }

    public String getDataQueryEnvWithDefault(String originServiceName) {
        String serviceName = tenantHandler.matchInfluxDbNameToTenantName(originServiceName);
        String env = getDataQueryEnv(serviceName);
        if (StringUtils.isEmpty(env)) {
            env = sysParaService.getClickhouseEnvRoute(serviceName);
        }
        if (StringUtils.isEmpty(env)) {
            env = clickhouseHandlerFactory.get().getQueryEnv();
        }
        return env;
    }

    public SubEnvironmentDO searchSubEnvironmentById(String id) {
        return subEnvironmentDAO.findById(id);

    }

    public List<SubEnvironmentDO> searchSubEnvironment(SubEnvironmentQuery subEnvironmentQuery, int pageIndex, int pageSize) {

        SubEnvironmentDO subEnvironmentDO = new SubEnvironmentDO();
        BeanUtils.copyProperties(subEnvironmentQuery, subEnvironmentDO);

        return subEnvironmentDAO.findAllByNameLike(subEnvironmentDO, (pageIndex - 1) * pageSize, pageSize);

    }

    public List<SubEnvironmentDO> searchSubEnvironmentWithNormal(SubEnvironmentQuery subEnvironmentQuery, int pageIndex, int pageSize) {

        SubEnvironmentDO subEnvironmentDO = new SubEnvironmentDO();
        BeanUtils.copyProperties(subEnvironmentQuery, subEnvironmentDO);

        return subEnvironmentDAO.findAllByNameLikeWithNormalAppend(subEnvironmentDO, (pageIndex - 1) * pageSize, pageSize);

    }

    public int getSubEnvironmentCount(SubEnvironmentQuery subEnvironmentQuery) {
        SubEnvironmentDO subEnvironmentDO = new SubEnvironmentDO();
        BeanUtils.copyProperties(subEnvironmentQuery, subEnvironmentDO);
        return subEnvironmentDAO.getCountByNameLike(subEnvironmentDO);
    }

    public int getSubEnvironmentCountWithNormal(SubEnvironmentQuery subEnvironmentQuery) {
        SubEnvironmentDO subEnvironmentDO = new SubEnvironmentDO();
        BeanUtils.copyProperties(subEnvironmentQuery, subEnvironmentDO);
        return subEnvironmentDAO.getCountByNameLikeWithNormalAppend(subEnvironmentDO);
    }
}
