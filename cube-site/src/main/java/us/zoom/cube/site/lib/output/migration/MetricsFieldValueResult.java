package us.zoom.cube.site.lib.output.migration;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: <PERSON><PERSON> Ding
 * @date: 01/08/2025 08:51
 * @desc:
 */
@Data
public class MetricsFieldValueResult {

    private boolean checkPass;
    private boolean sourceServiceMonitor;
    private int comparedTotal;
    private String startTime;
    private String endTime;

    private List<FieldStatisticEntry> debugStatistic = new ArrayList<>();

    private List<String> targetMissFields = new ArrayList<>();
    private List<String> sourceMissFields = new ArrayList<>();

    //important warning !!!!
    private List<String> stringTypeFieldWarning = new ArrayList<>();

    //important warning !!!!
    private List<String> zeroNumberFieldSumWarning = new ArrayList<>();

    //important warning !!!!
    private List<String> zeroStringFieldCountWarning = new ArrayList<>();


    private List<FieldStatisticEntry> NotEqualStatistic = new ArrayList<>();

    private String errorException;

    public void finalCheck() {
        checkPass = targetMissFields.isEmpty() && sourceMissFields.isEmpty() && stringTypeFieldWarning.isEmpty() && zeroNumberFieldSumWarning.isEmpty() && zeroStringFieldCountWarning.isEmpty() && NotEqualStatistic.isEmpty();
    }
}
