package us.zoom.cube.site.biz.alarm.group;


import com.zoom.op.monitor.dao.alarm.AlarmDefinitionDao;
import com.zoom.op.monitor.domain.INameAndMetricId;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ActiveProfiles;
import us.zoom.cube.site.biz.BaseTest;
import us.zoom.cube.site.core.TenantHandler;
import us.zoom.infra.dao.model.TenantDO;

import java.util.ArrayList;
import java.util.List;


@ActiveProfiles("perf_for_local")
public class AlarmGroupServiceTest extends BaseTest {

    @Autowired
    private AlarmDefinitionDao alarmDefinitionDao;

    @Autowired
    private TenantHandler tenantHandler;

    @Test
    public void testTagV2() {
        TenantDO tenantDO = tenantHandler.getTenantByName("");
        List<String> metricIds = new ArrayList<>();
        List<INameAndMetricId> ids = alarmDefinitionDao.getAlarmNameAndMetricIdByTenant(tenantDO.getId());
        ids.forEach(alarmNameMetricId -> {
            metricIds.add(alarmNameMetricId.getMetricId());
        });
        Assertions.assertNotNull(metricIds);
    }
}