package us.zoom.cube.site.biz;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import us.zoom.cube.lib.agent.AgentHeartInfo;
import us.zoom.cube.site.biz.agent.AgentKeepAliveSendService;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: canyon.li
 * @date: 2024/06/27
 **/
public class AgentKeepAliveTest extends BaseTest {

    @Autowired
    AgentKeepAliveSendService agentKeepAliveSendService;

    @Autowired
    AgentService agentService;

    @Test
    public void sendKeepAliveMsgTest() {
        try {
            agentKeepAliveSendService.sendKeepAliveMsg();
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
    }

    @Test
    public void keepAliveHeart() {
        try {
            List<AgentHeartInfo> heartInfos = new ArrayList<>();

            AgentHeartInfo agentHeartInfo1 = new AgentHeartInfo();
            agentHeartInfo1.setTopic("us_agent_heart_beat");
            agentHeartInfo1.setPartition(1);
            agentHeartInfo1.setTs(System.currentTimeMillis());
            heartInfos.add(agentHeartInfo1);

            AgentHeartInfo agentHeartInfo2 = new AgentHeartInfo();
            agentHeartInfo2.setTopic("us_agent_heart_beat");
            agentHeartInfo2.setPartition(2);
            agentHeartInfo2.setTs(System.currentTimeMillis());
            heartInfos.add(agentHeartInfo2);

            AgentHeartInfo agentHeartInfo3 = new AgentHeartInfo();
            agentHeartInfo3.setTopic("us_agent_heart_beat");
            agentHeartInfo3.setPartition(3);
            agentHeartInfo3.setTs(System.currentTimeMillis());
            heartInfos.add(agentHeartInfo3);

            AgentHeartInfo agentHeartInfo4 = new AgentHeartInfo();
            agentHeartInfo4.setTopic("eu02_agent_heart_beat");
            agentHeartInfo4.setPartition(4);
            agentHeartInfo4.setTs(System.currentTimeMillis());
            heartInfos.add(agentHeartInfo4);

            agentService.keepAliveHeart(heartInfos);
        } catch (Exception e) {
            System.out.println("keepAliveHeart error:" + e.getMessage());
        }
    }

    @Test
    public void agentHeart() {
        try {
            List<AgentHeartInfo> heartInfos = new ArrayList<>();

            AgentHeartInfo agentHeartInfo1 = new AgentHeartInfo();
            agentHeartInfo1.setTopic("us_agent_heart_beat");
            agentHeartInfo1.setPartition(1);
            agentHeartInfo1.setTs(System.currentTimeMillis());
            heartInfos.add(agentHeartInfo1);

            agentService.heart(heartInfos);
        } catch (Exception e) {
            System.out.println("AgentHeart error:" + e.getMessage());
        }
    }


}
