package us.zoom.cube.site.core;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import us.zoom.cube.lib.agent.AgentBaseInfo;
import us.zoom.infra.dao.model.DataParserDO;
import us.zoom.infra.dao.service.DataParserDAO;

import java.util.List;

@Component
public class AgentHandler {


    @Autowired
    private DataParserDAO dataParserDAO;


    public List<DataParserDO> listDataParserByServerId(String servierId ) {
        Assert.notNull(servierId,"server id is null!");
       return dataParserDAO.listDataParserByServerId(servierId);
    }
}
