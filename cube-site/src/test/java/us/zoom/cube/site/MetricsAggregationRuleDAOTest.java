package us.zoom.cube.site;


import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import us.zoom.infra.dao.model.MetricsAggregationRuleDO;
import us.zoom.infra.dao.service.MetricsAggregationRuleDAO;

import java.util.List;
import java.util.stream.Collectors;

public class MetricsAggregationRuleDAOTest extends BaseSpringTest{
    @Autowired
    MetricsAggregationRuleDAO metricsAggregationRuleDAO;

    @Test
    public void listAllMetricsAgg(){
        List<MetricsAggregationRuleDO> list = metricsAggregationRuleDAO.listAll();
        Assertions.assertNotNull(list);
        list.forEach(e -> {
            Assertions.assertNotNull(e.getVersion());
            Assertions.assertEquals(0, e.getVersion().intValue());
        });

    }
    @Test
    public void listAllMetricsRule(){
        List<MetricsAggregationRuleDO> list0 = metricsAggregationRuleDAO.listAll();
        List<String> ids = list0.stream().map(e -> e.getAggId()).collect(Collectors.toList());
        List<MetricsAggregationRuleDO> list1 = metricsAggregationRuleDAO.listRulesByAggIds(ids);
        Assertions.assertNotNull(list0);
        Assertions.assertNotNull(list1);
        Assertions.assertEquals(list0.size(),list1.size());

    }
}
