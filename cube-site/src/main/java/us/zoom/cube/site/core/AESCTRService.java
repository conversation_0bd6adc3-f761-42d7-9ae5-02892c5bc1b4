package us.zoom.cube.site.core;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.EnvironmentAware;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;
import us.zoom.infra.utils.AESCTRUtils;
import us.zoom.infra.utils.AESUtils;

import javax.crypto.KeyGenerator;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;

/**
 * @Author: <EMAIL>
 * @ModuleOwner: <EMAIL>
 * @Date: 04/21/2022 14:51
 * @Description:
 */
@Component
@Slf4j
@Configuration
public class AESCTRService implements EnvironmentAware {

    private Environment environment;


    @Override
    public void setEnvironment(Environment environment) {
        this.environment = environment;
    }

    public byte[] generateIV() throws NoSuchAlgorithmException {
        KeyGenerator kgen = KeyGenerator.getInstance("AES");
        SecureRandom secureRandom = new SecureRandom();
        kgen.init(256, secureRandom);
        byte[] iv = new byte[16];
        secureRandom.nextBytes(iv);
        return iv;
    }

    /**
     *
     * @param input
     * @return
     * @throws Exception
     */
    public  byte[] encrypt( byte[] input, byte[] aesKey, byte[] iv) throws Exception{
        return AESCTRUtils.encrypt(input, aesKey, iv);
    }

    /**
     *
     * @param input
     * @return
     * @throws Exception
     */
    public  byte[] mac( byte[] input, byte[] aesKey ) throws Exception{
        return AESCTRUtils.mac(input, aesKey );
    }

    /**
     *
     * @param input
     * @return
     * @throws Exception
     */
    public  byte[] decrypt(byte[] input, byte[] aesKey, byte[] iv) throws Exception{
        return AESCTRUtils.decrypt(input, aesKey, iv);
    }



}
