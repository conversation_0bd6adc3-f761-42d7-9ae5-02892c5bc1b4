package us.zoom.cube.site.core.model.ad;

import lombok.Data;
import us.zoom.cube.sdk.annotation.CubeMonitorLog;
import us.zoom.cube.sdk.annotation.Field;
import us.zoom.cube.sdk.annotation.Tag;

/**
 * <AUTHOR>
 * @date 2024-06-03 13:58
 */
@Data
@CubeMonitorLog(measure = "AdCfgDetailMetric")
public class AdCfgDetailMetric {
    @Tag
    private String host;
    @Tag
    private String ip;
    @Tag
    private String serviceName;
    @Field
    private String modelName;
    @Field
    private String metricsName;
    @Field
    private Integer metricsType;
    @Field
    private Integer aggPeriod;
    @Field
    private String fieldName;
    @Field
    private Boolean adStatus;
    @Field
    private String anomalyDirectionType;
    @Field
    private String thresValueFilterUp;
    @Field
    private String thresValueFilterDown;
    @Field
    private Boolean fillEmptyAsZero;
    @Field
    private Boolean holidayStatus;

    public AdCfgDetailMetric(String host, String ip, String serviceName, String modelName, String metricsName,
                             Integer metricsType, Integer aggPeriod, String fieldName, Boolean adStatus,
                             String anomalyDirectionType, String thresValueFilterUp, String thresValueFilterDown,
                             Boolean fillEmptyAsZero, Boolean holidayStatus) {
        this.host = host;
        this.ip = ip;
        this.serviceName = serviceName;
        this.modelName = modelName;
        this.metricsName = metricsName;
        this.metricsType = metricsType;
        this.aggPeriod = aggPeriod;
        this.fieldName = fieldName;
        this.adStatus = adStatus;
        this.anomalyDirectionType = anomalyDirectionType;
        this.thresValueFilterUp = thresValueFilterUp;
        this.thresValueFilterDown = thresValueFilterDown;
        this.fillEmptyAsZero = fillEmptyAsZero;
        this.holidayStatus = holidayStatus;
    }
}
