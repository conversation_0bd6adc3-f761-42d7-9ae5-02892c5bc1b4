package us.zoom.cube.site.biz;

import org.apache.commons.io.FileUtils;
import org.springframework.util.Assert;
import us.zoom.cube.site.lib.input.role.UserRoleInput;
import us.zoom.cube.lib.utils.JsonUtils;

import java.io.File;
import java.io.IOException;
import java.util.Map;

public class RoleInputTest {



    public static void main(String []gs){
        creaeRoleMenuSql();
    }
    public static void checkPara(){
        UserRoleInput roleInput  = mock("test",true,true,"test");
        roleInput.checkWhenAdd();


        roleInput.setRole(null);
        String result = checkWhenAdd(roleInput);
        Assert.isTrue("Role Name can't be blank.".equals(result),"fail");

        roleInput.setRole("dfsd sdfs");
        result = checkWhenAdd(roleInput);
        Assert.isTrue("Name can't contain blank char".equals(result),"fail");

        roleInput.setRole(mockStr(65));
        result = checkWhenAdd(roleInput);
        Assert.isTrue("The max lengh is 64".equals(result),"fail");

        roleInput.setRoleDesc(mockStr(6500));
        roleInput.setRole("sdf");
        result = checkWhenAdd(roleInput);
        Assert.isTrue("Role desc max lengh is 1024".equals(result),"fail");

    }

    private static String mockStr(int length) {
        StringBuilder builder = new StringBuilder();
        for(int i=0;i<length;i++){
            builder.append("a");
        }
        return  builder.toString();
    }



    public  static  String checkWhenAdd(UserRoleInput userRoleInput ){
        String result = null;
        try{
            userRoleInput.checkWhenAdd();
        }catch (Exception e){
            result = e.getMessage();
            System.out.println(result);
        }
        return result;
    }


    private static UserRoleInput mock(String name, Boolean crossService , Boolean pii, String desc) {
        UserRoleInput userRoleInput = new UserRoleInput();
        userRoleInput.setRole(name);
        userRoleInput.setCrossService(crossService);
        userRoleInput.setPiiAccess(pii);
        userRoleInput.setRoleDesc(desc);
        return userRoleInput;
    }


    public static void creaeRoleMenuSql(){
        try {

            String rawDatas=  FileUtils.readFileToString(new File("D:\\\\zoom\\\\role_menu_rela.json"), "UTF-8");
            java.util.List<Map> relaDOS = JsonUtils.toObject(rawDatas,java.util.List.class);
            for(Map real:relaDOS){
                String sql ="insert into role_menu_rela(id,role_name,menu_res_id,create_time,modify_time)values('"+real.get("id")+"','"+real.get("role_name")+"','"+real.get("menu_res_id")+"',now(),now());";
                System.out.println(sql);
            }

        } catch (IOException e) {
            e.printStackTrace();
        }
    }

}
