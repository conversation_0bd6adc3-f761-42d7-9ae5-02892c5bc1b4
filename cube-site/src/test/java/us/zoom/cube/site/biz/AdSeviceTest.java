package us.zoom.cube.site.biz;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import us.zoom.cube.site.CubeSiteApplicationTests;
import us.zoom.cube.site.biz.ai.AiAlarmService;
import us.zoom.cube.site.biz.ai.AiCfgApiService;
import us.zoom.cube.site.biz.ai.AiCheckService;
import us.zoom.cube.site.biz.ai.AiTrainService;
import us.zoom.cube.site.core.model.ad.*;
import us.zoom.cube.site.lib.ResponseObject;
import us.zoom.cube.site.lib.query.PageQuery;
import us.zoom.cube.lib.utils.JsonUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-11-14 14:05
 */
public class AdSeviceTest extends CubeSiteApplicationTests {


    @Autowired
    us.zoom.cube.site.biz.ai.AiCfgService aiCfgService;

    @Autowired
    AiTrainService aiTrainService;

    @Autowired
    AiAlarmService aiAlarmService;

    @Autowired
    AiCfgApiService aiCfgApiService;

    @Autowired
    private AiCheckService aiCheckService;


    @Test
    public void searchAdAlarmAndAdTrain(){
        AdAlarmQuery adAlarmQuery = new AdAlarmQuery();
        adAlarmQuery.setId("9f7371e2-185c-4421-ba7b-0045ef3ea06c");
        adAlarmQuery.setMetricsId("b3e47388-a956-4781-a8ab-f4ad4f8e2221");
        //ResponseObject responseObject = aiAlarmService.getAdAlarm(adAlarmQuery);
        AdTrainData adTrainData = new AdTrainData();
        adTrainData.setAdConfigId("7675a64d-6929-42bc-851f-f7eb7e53302d");
        adTrainData.setAdTrainStatus(1);
        adTrainData.setAdTrainInfo("test");
        adTrainData.setMetricsId("653e1843-3a50-4e76-a05e-5c29aa08c7e3");
        adTrainData.setTenantId("1863ff76-8d25-44c7-b9de-a6c15fa27479");
        //ResponseObject responseObject1 = adTrainService.addAdTrainStatus(adTrainData);
        adTrainData.setAdTrainStatus(3);
        //adTrainData.setAdConfigId("tth96bb3803-bdbc-4c2f-a956-7615b4c6fa31");
        adTrainData.setAdTrainInfo("test33");

        String s ="{\"adConfigId\":\"93fc5e6f-3fde-4699-8179-b1c282df2466\",\"metricsId\": \"eebf0fee-34c4-45ea-822a-69c15e5946b9\", \"adTrainStatus\": 3, \"adTrainInfo\": \"trainingTest\", \"tenantId\": \"b8a83c60-c8a2-4d0b-a198-ccf03e6b12db\"}\n";
        adTrainData = JsonUtils.toObject(s,AdTrainData.class);

        ResponseObject responseObject2 = aiTrainService.editAdTrainStatus(adTrainData);
        //ResponseObject responseObject3 = aiTrainService.getAdTrainStatus(adTrainData);
    }

    @Test
    public void addAd(){
        AdCfgData adCfgData = new AdCfgData();
        adCfgData.setAdName("test");
        adCfgData.setId("bbabc8d6-8f2c-4977-9c72-8c3e2580ab94");
        adCfgData.setMetricsId("653e1843-3a50-4e76-a05e-5c29aa08c7e3");
        adCfgData.setMetricsName("test");
        adCfgData.setTenantId("1863ff76-8d25-44c7-b9de-a6c15fa27479");
        adCfgData.setTenantName("test");
        adCfgData.setUserId("28ea4e66-0cc3-43fb-8ba3-3df65c8ed0f7");
        List<AdTagCfgData> adTagCfgDataList = new ArrayList<>();
        AdTagCfgData adTagCfgData = new AdTagCfgData();

        adTagCfgData.setAdTagType(1);
        adTagCfgData.setAdConfigId("96bb3803-bdbc-4c2f-a956-7615b4c6fa31");
        adTagCfgData.setTagName("test");
        adTagCfgData.setTagValue("test");
        adTagCfgDataList.add(adTagCfgData);
        adCfgData.setAdTagCfgDataList(adTagCfgDataList);
        ResponseObject responseObject = aiCfgService.addAdCfg(adCfgData);
        ResponseObject responseObject2 = aiCfgService.editAdCfg(adCfgData);
    }


    @Test
    public void hasSameAdName(){
        AdCfgData adCfgData = new AdCfgData();
        adCfgData.setAdName("charles");
        adCfgData.setTenantId("722f8427-1f4b-48d8-b7f8-47bfacb9cc9a");
        //ResponseObject responseObject = AdCfgService.addAdCfg(adCfgData);
        ResponseObject responseObject2 = aiCheckService.checkAdName(adCfgData);
        AdCfgData adCfgData2 = new AdCfgData();
        adCfgData2.setAdName("testall");
        adCfgData2.setTenantId("722f8427-1f4b-48d8-b7f8-47bfacb9cc9a");
        ResponseObject responseObject3 = aiCheckService.checkAdName(adCfgData2);
        adCfgData.setAdName("testall");
    }


    @Test
    public void getAiCfgApi(){
        //aiCfgApiService.getAiCfgApi();
        ResponseObject responseObject = aiCfgApiService.getAiCfgParameters();
    }


    @Test
    public void searchAd(){
        PageQuery<AdQuery> pageQuery = new PageQuery<>();

        //ResponseObject responseObject9 = AdCfgService.getAdCfgById("0860cfc3-e314-4737-bb7a-de25038c46bf");

        pageQuery.setTenantId("1f8cacb9-8769-481f-8ad8-443f18799fa2");
        pageQuery.setPageSize(50);
        pageQuery.setPageIndex(1);
        AdQuery adQuery = new AdQuery();
        adQuery.setAdName("");
        adQuery.setMetricsName("");
        adQuery.setFieldName("us.sum");
        pageQuery.setQueryPara(adQuery);
        ResponseObject responseObject = aiCfgService.searchAdCfg(pageQuery);

        PageQuery<AdQuery> pageQuery2 = new PageQuery<>();
        pageQuery2.setTenantId("1f8cacb9-8769-481f-8ad8-443f18799fa2");
        pageQuery2.setPageSize(3);
        pageQuery2.setPageIndex(1);
        AdQuery adQuery2 = new AdQuery();
        adQuery2.setAdName("");
        adQuery2.setMetricsName("");
        pageQuery2.setQueryPara(adQuery2);
        ResponseObject responseObject2 = aiCfgService.searchAdCfg(pageQuery2);

        PageQuery<AdQuery> pageQuery3 = new PageQuery<>();
        pageQuery3.setTenantId("1f8cacb9-8769-481f-8ad8-443f18799fa2");
        pageQuery3.setPageSize(3);
        pageQuery3.setPageIndex(1);
        AdQuery adQuery3 = new AdQuery();
        adQuery3.setAdName("");
        adQuery3.setMetricsName("charles_test");
        pageQuery3.setQueryPara(adQuery3);
        ResponseObject responseObject3 = aiCfgService.searchAdCfg(pageQuery3);


        PageQuery<AdQuery> pageQuery4 = new PageQuery<>();
        pageQuery4.setTenantId("1f8cacb9-8769-481f-8ad8-443f18799fa2");
        pageQuery4.setPageSize(5);
        pageQuery4.setPageIndex(1);
        AdQuery adQuery4 = new AdQuery();
        adQuery4.setAdStatus(true);
        pageQuery4.setQueryPara(adQuery4);
        ResponseObject responseObject4 = aiCfgService.searchAdCfg(pageQuery4);

        PageQuery<AdQuery> pageQuery5 = new PageQuery<>();
        pageQuery5.setTenantId("1f8cacb9-8769-481f-8ad8-443f18799fa2");
        pageQuery5.setPageSize(5);
        pageQuery5.setPageIndex(1);
        AdQuery adQuery5 = new AdQuery();
        adQuery5.setAdName("test");
        adQuery5.setMetricsName("charles_test");
        adQuery5.setAdStatus(true);
        pageQuery5.setQueryPara(adQuery5);
        ResponseObject responseObject5 = aiCfgService.searchAdCfg(pageQuery5);
        ResponseObject responseObject6 = aiCfgService.searchAdCfg(pageQuery5);
    }

}
