package us.zoom.cube.site.lib.output.trace.queryrange;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import us.zoom.cube.site.lib.input.trace.BuilderQuery.AttributeKey;

import java.util.List;

/**
 * @author: eason.jia
 * @date: 2024/8/18
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FilterAttributeResponse {
    private List<AttributeKey> attributeKeys;
}
