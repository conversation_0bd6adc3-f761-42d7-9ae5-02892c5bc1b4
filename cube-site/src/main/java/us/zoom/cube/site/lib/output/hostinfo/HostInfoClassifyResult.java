package us.zoom.cube.site.lib.output.hostinfo;

import lombok.Data;

import java.util.HashMap;
import java.util.Map;

@Data
public class HostInfoClassifyResult {
    private int total = 0;
    private Map<String,Integer> items = new HashMap<>();

    public HostInfoClassifyResult(int totalValue, Map<String, Integer> itemsValue) {
        total = totalValue;
        items = itemsValue;
    }

    public HostInfoClassifyResult() {
    }
}
