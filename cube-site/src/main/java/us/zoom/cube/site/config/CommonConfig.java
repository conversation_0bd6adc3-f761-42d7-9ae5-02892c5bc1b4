package us.zoom.cube.site.config;

import com.fasterxml.jackson.databind.Module;
import com.fasterxml.jackson.datatype.hibernate5.Hibernate5Module;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import us.zoom.cube.site.api.filter.TrackingIdFilter;
import us.zoom.cube.site.api.intercept.WebServletRequestReplacedFilter;

/**
 * @author: <PERSON><PERSON> Ding
 * @date: 2022/5/30 09:34
 * @desc:
 */
@Configuration
public class CommonConfig {

    @Bean
    public FilterRegistrationBean httpServletRequestReplacedRegistration() {
        FilterRegistrationBean registration = new FilterRegistrationBean();
        registration.setFilter(new WebServletRequestReplacedFilter());
        registration.addUrlPatterns("/*");
        registration.addInitParameter("paramName", "paramValue");
        registration.setName("httpServletRequestReplacedFilter");
        registration.setOrder(1);
        return registration;
    }


    @Bean
    public FilterRegistrationBean<TrackingIdFilter> trackingIdFilter() {
        FilterRegistrationBean<TrackingIdFilter> registrationBean = new FilterRegistrationBean<>();
        registrationBean.setFilter(new TrackingIdFilter());
        registrationBean.addUrlPatterns("/*");

        registrationBean.setName("trackingIdFilter");
        registrationBean.setOrder(FilterRegistrationBean.HIGHEST_PRECEDENCE);
        return registrationBean;
    }


    @Bean
    public Module hibernate5Module() {
        Hibernate5Module module = new Hibernate5Module();
        module.disable(Hibernate5Module.Feature.USE_TRANSIENT_ANNOTATION);
        module.enable(Hibernate5Module.Feature.SERIALIZE_IDENTIFIER_FOR_LAZY_NOT_LOADED_OBJECTS);
        return module;
    }

}
