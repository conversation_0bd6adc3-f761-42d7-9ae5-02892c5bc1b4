package us.zoom.cube.site.lib.output.config.metrics;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MetricsTags {
    String name;

    Object value;

    boolean pii;

    public MetricsTags(String name, boolean pii) {
        this.name = name;
        this.pii = pii;
    }
}