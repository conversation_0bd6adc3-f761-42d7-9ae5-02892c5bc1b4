package us.zoom.cube.site.biz;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.site.lib.ResponseObject;
import us.zoom.cube.site.lib.common.ValueText;
import us.zoom.cube.site.lib.query.ClusterRegionQuery;
import us.zoom.cube.lib.utils.JsonUtils;
import java.util.List;

public class DashboardServiceTest extends BaseTest {


    @Autowired
    private  DashboardService dashboardService;

    @Test
    public void listClusterZoneHost(){
        ClusterRegionQuery clusterZoneQuery=new ClusterRegionQuery();
        clusterZoneQuery.setTenantId("WEB1");
        ResponseObject responseObject= dashboardService.listClusters(clusterZoneQuery);
        System.out.println(JsonUtils.toJsonString(responseObject));

        if(null == responseObject.getData()){
            return;
        }
        List<ValueText> clusterIds= (List<ValueText>) responseObject.getData();
        if(CollectionUtils.isEmpty(clusterIds)){
            return;
        }
        clusterZoneQuery.setClusterId(clusterIds.get(0).getText());
        responseObject=dashboardService.listRegionIds(clusterZoneQuery);
        System.out.println(JsonUtils.toJsonString(responseObject));

        if(null == responseObject.getData()){
            return;
        }
        List<ValueText> regionIds= (List<ValueText>) responseObject.getData();
        if(CollectionUtils.isEmpty(regionIds)){
            return;
        }
        clusterZoneQuery.setRegionId(regionIds.get(0).getText());
        responseObject=dashboardService.listHosts(clusterZoneQuery);
        System.out.println(JsonUtils.toJsonString(responseObject));

    }


}
