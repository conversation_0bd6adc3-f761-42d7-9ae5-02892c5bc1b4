package us.zoom.cube.site.core.parser.process.core.processor;

import org.apache.commons.lang3.text.WordUtils;
import us.zoom.cube.lib.hub.BaseProcessorCfg;
import us.zoom.cube.lib.hub.ProcessorTypeEnum;
import us.zoom.cube.site.core.parser.process.core.common.enums.MonitoringLogType;
import us.zoom.cube.site.core.parser.process.core.monitoring.MonitoringLogUtils;

import java.util.Map;

public abstract class Processor {
    private MonitoringLogUtils monitoringLogUtils = new MonitoringLogUtils();
    public MonitoringLogType type;

    public abstract ProcessorResp process(Map<String, Object> messageMap, BaseProcessorCfg processorCfg);

    protected void logException(Exception e, BaseProcessorCfg processorCfg, ProcessorResp resp) {
        String shortCodeByName = ProcessorTypeEnum.findShortCodeByName(processorCfg.getType());
        String type = WordUtils.capitalizeFully(shortCodeByName);
        String failMessage = String.format("Error in processor [ID: %s, Name: %s, Type: %s]: %s", processorCfg.getId(), processorCfg.getName(), type, e.getMessage());
        resp.setContinue(false);
        resp.setFailMessage(failMessage);
        //fail do not log outPutMap
    }


    protected  void logSuccess(Map<String, Object> messageMap, ProcessorResp resp){
        resp.setOutputMap(messageMap);
    }

    protected  void logProcessor(BaseProcessorCfg processorCfg, ProcessorResp resp){
        String shortCodeByName = ProcessorTypeEnum.findShortCodeByName(processorCfg.getType());
        String type = WordUtils.capitalizeFully(shortCodeByName);
        resp.setId(processorCfg.getId());
        resp.setType(type);
        resp.setOrder(processorCfg.getOrder());
        resp.setName(processorCfg.getName());
    }
}
