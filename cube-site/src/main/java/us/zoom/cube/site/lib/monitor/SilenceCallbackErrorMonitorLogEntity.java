package us.zoom.cube.site.lib.monitor;

import lombok.Builder;
import lombok.Data;
import us.zoom.cube.sdk.annotation.CubeMonitorLog;
import us.zoom.cube.sdk.annotation.Field;
import us.zoom.cube.sdk.annotation.Tag;

@CubeMonitorLog(measure = "SilenceCallbackErrorMonitorLogEntity")
@Data
@Builder
public class SilenceCallbackErrorMonitorLogEntity {

    @Tag
    private String ip;

    @Tag
    private String tid;

    @Tag
    private String silenceId;

    @Tag
    private String userName;

    @Tag
    private String serviceName;

    @Field
    private String error;

}
