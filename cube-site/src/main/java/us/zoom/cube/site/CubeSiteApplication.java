package us.zoom.cube.site;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.annotation.Import;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import us.zoom.cloud.secrets.spring.context.CSMSBeanDefinitionRegistrar;

//Whether Spring Security or OKTA Configuration load depends on JwtAuthProperties and SiteConfig
@Slf4j
@SpringBootApplication
@Import({CSMSBeanDefinitionRegistrar.class})
@EnableGlobalMethodSecurity(prePostEnabled = true, securedEnabled = true)
@ComponentScan(basePackages = {
        "us.zoom.cube.site",
        "us.zoom.infra.es",
        "us.zoom.infra.notification",
        "us.zoom.infra.influx",
        "us.zoom.infra.dao",
        "us.zoom.infra.syspara",
        "us.zoom.infra.asyncmq",
        "us.zoom.cube.notice",
        "com.zoom.op",
        "us.zoom.infra.loader",
        "us.zoom.infra.redis"
})
@EnableAspectJAutoProxy(exposeProxy = true)
public class CubeSiteApplication {

    public static void main(String[] args) {
        SpringApplication.run(CubeSiteApplication.class, args);
    }
}
