package us.zoom.cube.site.core;

import com.zoom.op.monitor.domain.alarm.AlarmBoardSetting;
import com.zoom.op.monitor.domain.alarm.AlarmBoardSettingItem;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import us.zoom.cube.lib.common.CubeConstants;
import us.zoom.infra.dao.model.TenantDO;
import us.zoom.infra.dao.service.AlarmBoardSettingDAO;

import java.util.ArrayList;
import java.util.List;

@Component
@Slf4j
public class AlarmBoardSettingHandler {

    @Autowired
    AlarmBoardSettingDAO alarmBoardSettingDAO;

    public void save(AlarmBoardSetting alarmBoardSetting) {
        alarmBoardSettingDAO.save(alarmBoardSetting);
    }

    public AlarmBoardSetting getSettingByServiceId(String serviceId) {
        return alarmBoardSettingDAO.getSettingByServiceId(serviceId);
    }

    public void update(AlarmBoardSetting alarmBoardSetting) {
        alarmBoardSettingDAO.update(alarmBoardSetting);
    }

    public AlarmBoardSetting getById(String id) {
        return alarmBoardSettingDAO.getById(id);
    }

    public void saveItem(AlarmBoardSettingItem alarmBoardSettingItem) {
        alarmBoardSettingDAO.saveItem(alarmBoardSettingItem);
    }

    public AlarmBoardSettingItem getItemByName(String alarmBoardSettingId, String name) {
        return alarmBoardSettingDAO.getItemByName(alarmBoardSettingId, name);
    }

    public AlarmBoardSettingItem getItemById(String id) {
        return alarmBoardSettingDAO.getItemById(id);
    }

    public List<AlarmBoardSettingItem> getItemBySettingId(String settingId) {
        return alarmBoardSettingDAO.getItemBySettingId(settingId);
    }

    public void deleteItemBySettingId(String settingId) {
        alarmBoardSettingDAO.deleteItemBySettingId(settingId);
    }

    public List<AlarmBoardSettingItem> getItemInOrdInerByServiceId(String serviceId) {
        return alarmBoardSettingDAO.getItemOrderlyByServiceId(serviceId);
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveDefaultSetting(AlarmBoardSetting alarmBoardSetting, List<AlarmBoardSettingItem> alarmBoardSettingItemList) {
        alarmBoardSettingDAO.save(alarmBoardSetting);
        if (!CollectionUtils.isEmpty(alarmBoardSettingItemList)) {
            alarmBoardSettingItemList.forEach(alarmBoardSettingItem -> {
                alarmBoardSettingDAO.saveItem(alarmBoardSettingItem);
            });
        }
    }

    public List<String> getAll() {
        List<String> all = new ArrayList<>();
        long count = alarmBoardSettingDAO.getAllCount();
        int pageSize = CubeConstants.CUBE_SQL_BATCH_QUERY_SIZE;
        long pageNum = count / pageSize + (count % pageSize == 0 ? 0 : 1);
        for (int i = 1 ; i <= pageNum ; i++) {
            all.addAll(alarmBoardSettingDAO.getAllSetting(pageSize * (i - 1),pageSize));
        }
        return all;
    }
}
