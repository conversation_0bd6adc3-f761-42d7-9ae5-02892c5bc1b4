package us.zoom.cube.site.core;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import us.zoom.infra.dao.model.CapacityDashDO;
import us.zoom.infra.dao.service.CapacityDashDAO;

import jakarta.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-07-06 17:05
 */
@Component
public class CapacityDashHandler {

    @Autowired
    private CapacityDashDAO capacityDashDAO;

    @Transactional(rollbackFor = Exception.class)
    public String add(@Valid CapacityDashDO capacityDashDO) {
        capacityDashDAO.add(capacityDashDO);
        return capacityDashDO.getId();
    }

    @Transactional(rollbackFor = Exception.class)
    public void edit(@Valid CapacityDashDO capacityDashDO) {
        capacityDashDAO.edit(capacityDashDO);
    }

    @Transactional(rollbackFor = Exception.class)
    public void delete(String id) {
        capacityDashDAO.del(id);
    }

    public List<CapacityDashDO> getServiceAll() {
        List<CapacityDashDO> capacityDashDOList = capacityDashDAO.getServiceAll();
        return capacityDashDOList;
    }

    public List<CapacityDashDO> getServiceById(String id) {
        List<CapacityDashDO> capacityDashDOList = capacityDashDAO.getServiceById(id);
        return capacityDashDOList;
    }

    public List<CapacityDashDO> getServiceAllByOpen() {
        List<CapacityDashDO> capacityDashDOList = capacityDashDAO.getServiceAllbyOpen();
        return capacityDashDOList;
    }

    public List<CapacityDashDO> getServiceAllByClose() {
        List<CapacityDashDO> capacityDashDOList = capacityDashDAO.getServiceAllbyClose();
        return capacityDashDOList;
    }
}
