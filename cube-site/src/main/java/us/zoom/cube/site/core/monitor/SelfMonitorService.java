package us.zoom.cube.site.core.monitor;

import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Component;
import us.zoom.cube.lib.common.SelfMonitorInput;
import us.zoom.cube.lib.common.StaticsModel;
import us.zoom.cube.lib.integrations.Metrics;
import us.zoom.cube.lib.integrations.MetricsField;
import us.zoom.cube.lib.integrations.MetricsFieldTypeEnum;
import us.zoom.cube.lib.utils.JsonUtils;
import us.zoom.cube.site.biz.SysParaService;
import us.zoom.cube.site.biz.model.CubeMetricsStorageStragety;
import us.zoom.cube.site.biz.syspara.CubeMetricsStorageParaService;
import us.zoom.cube.site.core.config.ClickhouseHandlerFactory;
import us.zoom.cube.site.lib.ResponseObject;
import us.zoom.infra.clickhouse.ClickhouseSqlUtil;
import us.zoom.infra.enums.MonitorLogTypeEnum;
import us.zoom.infra.influx.service.InfluxService;
import us.zoom.infra.model.LogResult;

import java.io.InputStream;
import java.sql.Timestamp;
import java.util.*;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class SelfMonitorService implements InitializingBean {
    @Autowired
    private SelfMonitorHandler selfMonitorHandler;
    @Autowired
    private InfluxService influxService;

    @Autowired
    private ClickhouseHandlerFactory clickhouseHandlerFactory;

    @Autowired
    private SysParaService sysParaService;

    @Autowired
    private CubeMetricsStorageParaService cubeMetricsStorageParaService;

    @Autowired
    private ResourceLoader resourceLoader;

    private String version;

    private Logger monitorLog = LoggerFactory.getLogger("Monitor");

    private String noData="{\"noData\":true}";
    private String getCfgError="get cfg from stie error";
    private String split="-->";
    private String maxTimeKey="max";
    private static final Long maxSecongd=10L;
    public ResponseObject sendMsg(SelfMonitorInput serverInput){
        monitorLog.info(LogResult.getSuccessLogResult(MonitorLogTypeEnum.selfMonitorServiceInput.name(), serverInput, new HashMap<>()));
        if(null == serverInput || StringUtils.isBlank(serverInput.getMsg())){
            return ResponseObject.success(true);
        }

        if (!needSend(serverInput.getMsg())) {
            return ResponseObject.success(true);
        }

        return ResponseObject.success(selfMonitorHandler.send(null,serverInput.getMsg(),null,null,null));
    }

    private boolean needSend(String msg ) {
        if(StringUtils.isNotBlank(msg) && (msg.trim().startsWith(noData))){
            log.info(msg);
            return false;
        }

        if(StringUtils.isNotBlank(msg) && msg.trim().startsWith(getCfgError)){
            log.error(msg);
            return false;
        }


        int index=msg.indexOf(split);
        JsonNode jsonNode = JsonUtils.getValueFromJson(msg.substring(0,index), Collections.singletonList(maxTimeKey));
        Long maxTime = Optional.ofNullable(jsonNode).map(JsonNode::asLong).orElse(null);
        if(maxTime == null || maxTime < maxSecongd){
            log.info(msg);
            return false;
        }
        return true;
    }


//    public static void main(String []gs){
//        String msg="{\"avg\":1824,\"count\":2990,\"dataCount\":2990,\"max\":6295,\"maxKey\":\"febde932-3ad8-46ff-81aa-5081362e76d8\",\"min\":626,\"noData\":false}-->from cube 10.0.37.49";
//        new SelfMonitorService().needSend(msg);
//        msg="{\"noData\":true}-->from hub 10.0.24.29";
//        new SelfMonitorService().needSend(msg);
//    }

    private static final String site_service="Infra_Monitor_Cube_Site";
    private static final String statics_table="cube_access_statics";

    private void handleCompatible(Map<String,String> tags, StaticsModel staticsModel){
        if(staticsModel.getOther2Tag() != null){
            tags.put("other2Tag",staticsModel.getOther2Tag());
        }
        if(staticsModel.getFutureCount() == null){
            staticsModel.setFutureCount(0L);
        }
        if(staticsModel.getpFutureLevel() == null){
            staticsModel.setpFutureLevel(0L);
        }
        if(staticsModel.getpLarge5mCount() == null){
            staticsModel.setpLarge5mCount(0L);
        }
        if(staticsModel.getpLarge5mLevel() == null){
            staticsModel.setpLarge5mLevel(0L);
        }
        if(StringUtils.isNotBlank(staticsModel.getEnvironment())) {
            tags.put("environment", staticsModel.getEnvironment());
        }
    }

    public ResponseObject statics(List<StaticsModel> staticsModels) {
        monitorLog.info(LogResult.getSuccessLogResult(MonitorLogTypeEnum.selfMonitorServiceStaticsModels.name(), staticsModels, new HashMap<>()));
        if(CollectionUtils.isEmpty(staticsModels)){
            return ResponseObject.success(true);
        }

        for(StaticsModel staticsModel:staticsModels){
            Map<String,String> tags=new HashMap<>();
            tags.put("tid",staticsModel.getTid()+"");
            tags.put("ip",staticsModel.getIp());
            tags.put("system",staticsModel.getSystem());
            tags.put("otherTag",staticsModel.getOtherTag());
            tags.put("version","1.0");

            handleCompatible(tags, staticsModel);

            Map<String,Object> values=new HashMap<>();
            values.put("maxTime",staticsModel.getMaxTime());
            values.put("minTime",staticsModel.getMinTime());
            values.put("count",staticsModel.getCount());
            values.put("avgTime",staticsModel.getAvgTime());
            values.put("p5Level",staticsModel.getP5Level());
            values.put("p10Level",staticsModel.getP10Level());
            values.put("p20Level",staticsModel.getP20Level());
            values.put("p25Level",staticsModel.getP25Level());
            values.put("p30Level",staticsModel.getP30Level());
            values.put("p35Level",staticsModel.getP35Level());
            values.put("p40Level",staticsModel.getP40Level());
            values.put("p45Level",staticsModel.getP45Level());
            values.put("p50Level",staticsModel.getP50Level());
            values.put("p55Level",staticsModel.getP55Level());
            values.put("p60Level",staticsModel.getP60Level());
            values.put("otherLevel",staticsModel.getOtherLevel());

            // future metrics stat data
            values.put("futureMaxTime",staticsModel.getFutureMaxTime());
            values.put("futureMinTime",staticsModel.getFutureMinTime());
            values.put("futureCount",staticsModel.getFutureCount());
            //values.put("futureLevel",staticsModel.getpFutureLevel());
            values.put("futureAvgTime",staticsModel.getFutureAvgTime());

            // delay 5 minutes metrics stat data
            values.put("pLarge5mMaxTime",staticsModel.getpLarge5mMaxTime());
            values.put("pLarge5mMinTime",staticsModel.getpLarge5mMinTime());
            values.put("pLarge5mCount",staticsModel.getpLarge5mCount());
            values.put("pLarge5mAvgTime",staticsModel.getpLarge5mAvgTime());


            //InfluxDBUtil.insert(site_service.toLowerCase(),null,statics_table,tags,values,staticsModel.getPeriod(), TimeUnit.MILLISECONDS);
            Metrics metrics = new Metrics();
            metrics.setTags(tags);
            metrics.setTs(staticsModel.getPeriod());
            List<MetricsField> fields = Lists.newArrayList();
            fields.add(new MetricsField("maxTime",staticsModel.getMaxTime().doubleValue(), MetricsFieldTypeEnum.number));
            fields.add(new MetricsField("minTime",staticsModel.getMinTime().doubleValue(), MetricsFieldTypeEnum.number));
            fields.add(new MetricsField("count",staticsModel.getCount().doubleValue(), MetricsFieldTypeEnum.number));
            fields.add(new MetricsField("avgTime",staticsModel.getAvgTime(), MetricsFieldTypeEnum.number));
            fields.add(new MetricsField("p5Level",staticsModel.getP5Level().doubleValue(), MetricsFieldTypeEnum.number));
            fields.add(new MetricsField("p10Level",staticsModel.getP10Level().doubleValue(), MetricsFieldTypeEnum.number));
            fields.add(new MetricsField("p20Level",staticsModel.getP20Level().doubleValue(), MetricsFieldTypeEnum.number));
            fields.add(new MetricsField("p25Level",staticsModel.getP25Level().doubleValue(), MetricsFieldTypeEnum.number));
            fields.add(new MetricsField("p30Level",staticsModel.getP30Level().doubleValue(), MetricsFieldTypeEnum.number));
            fields.add(new MetricsField("p35Level",staticsModel.getP35Level().doubleValue(), MetricsFieldTypeEnum.number));
            fields.add(new MetricsField("p40Level",staticsModel.getP40Level().doubleValue(), MetricsFieldTypeEnum.number));
            fields.add(new MetricsField("p45Level",staticsModel.getP45Level().doubleValue(), MetricsFieldTypeEnum.number));
            fields.add(new MetricsField("p50Level",staticsModel.getP50Level().doubleValue(), MetricsFieldTypeEnum.number));
            fields.add(new MetricsField("p55Level",staticsModel.getP55Level().doubleValue(), MetricsFieldTypeEnum.number));
            fields.add(new MetricsField("p35Level",staticsModel.getP35Level().doubleValue(), MetricsFieldTypeEnum.number));
            fields.add(new MetricsField("p60Level",staticsModel.getP60Level().doubleValue(), MetricsFieldTypeEnum.number));
            fields.add(new MetricsField("otherLevel",staticsModel.getOtherLevel().doubleValue(), MetricsFieldTypeEnum.number));

            // future metrics stat data
            // cast Long to double
            fields.add(new MetricsField("futureMaxTime",staticsModel.getFutureMaxTime().doubleValue(), MetricsFieldTypeEnum.number));
            fields.add(new MetricsField("futureMinTime",staticsModel.getFutureMinTime().doubleValue(), MetricsFieldTypeEnum.number));
            fields.add(new MetricsField("futureCount",staticsModel.getFutureCount().doubleValue(), MetricsFieldTypeEnum.number));
            fields.add(new MetricsField("futureAvgTime",staticsModel.getFutureAvgTime(), MetricsFieldTypeEnum.number));

            // delay 5 minutes metrics stat data
            // cast Long to double
            fields.add(new MetricsField("pLarge5mMaxTime",staticsModel.getpLarge5mMaxTime().doubleValue(), MetricsFieldTypeEnum.number));
            fields.add(new MetricsField("pLarge5mMinTime",staticsModel.getpLarge5mMinTime().doubleValue(), MetricsFieldTypeEnum.number));
            fields.add(new MetricsField("pLarge5mCount",staticsModel.getpLarge5mCount().doubleValue(), MetricsFieldTypeEnum.number));
            fields.add(new MetricsField("pLarge5mAvgTime",staticsModel.getpLarge5mAvgTime(), MetricsFieldTypeEnum.number));

            metrics.setFields(fields);
//            try {
                monitorLog.info(LogResult.getSuccessLogResult(MonitorLogTypeEnum.startAddInfluxDb.name(), metrics, new HashMap<>()));
//                influxService.addInfluxData(site_service, statics_table, metrics);
                writeToCh(metrics);
                log.info(LogResult.getSuccessLogResult(MonitorLogTypeEnum.successAddInfluxDb.name(), "statics data succeed", new HashMap<>()));
//            } catch (IOException e) {
//                log.error("save statics error! ", e);
//            }
        }
        log.info(LogResult.getSuccessLogResult(MonitorLogTypeEnum.endAddInfluxDb.name(), "save statics over", new HashMap<>()));
        return ResponseObject.success(true);
    }

    private void writeToCh(Metrics metrics) {
        CubeMetricsStorageStragety cubeMetricsStorageStragety =  cubeMetricsStorageParaService.getCubeMetricsStorageStragety();
        Map<String,Object> columns = new HashMap<>();
        for (Map.Entry<String, String> stringStringEntry : metrics.getTags().entrySet()) {
            if (stringStringEntry.getValue() != null) {
                columns.put(stringStringEntry.getKey(), stringStringEntry.getValue());
            }
        }
        for (MetricsField e : metrics.getFields()) {
            if (e.getFieldValue() != null) {
                columns.put(e.getFieldName(), e.getFieldValue());
            }
        }
        log.info("site_monitor_data_to_ch, ts={}", metrics.getTs());
        boolean success = clickhouseHandlerFactory.getClickhouseWriter().write(ClickhouseSqlUtil.toClickhouseName(cubeMetricsStorageStragety.getServicName()),
                ClickhouseSqlUtil.toClickhouseName(cubeMetricsStorageStragety.getMetricsName()),
                columns, new Timestamp(metrics.getTs()),cubeMetricsStorageStragety.getRecordMemorySize(),true,true);

        if(!success){
            log.error("write to ch error data={}", JsonUtils.toJsonStringIgnoreExp(metrics));
        }
    }

    public String getVersion() {
        return version;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        try {
            Resource resource = resourceLoader.getResource("classpath:version.txt");
            if (!resource.exists()) {
                log.warn("package version file not exists");
                return;
            }

            InputStream inputStream = resource.getInputStream();
            byte[] bytes = inputStream.readNBytes(1000);
            version = new String(bytes);
        } catch (Exception e) {
            log.warn("package version file fetch error,", e);
        }
    }
}
