package us.zoom.cube.site.lib.output.clickhouse;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.util.List;

/**
 * @author: canyon.li
 * @date: 2024/07/16
 **/
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class TagValueOutput {

    /**
     * tag value list
     */
    List<String> values;

    /**
     * total count;
     */
    Long count;

    boolean fullResult = true;
}
