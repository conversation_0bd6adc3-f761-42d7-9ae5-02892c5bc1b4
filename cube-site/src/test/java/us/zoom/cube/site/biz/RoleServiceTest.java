package us.zoom.cube.site.biz;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.util.Assert;
import us.zoom.cube.site.CubeSiteApplication;
import us.zoom.cube.site.biz.role.RoleMenuRelaService;
import us.zoom.cube.site.biz.role.UserRoleService;
import us.zoom.cube.site.core.auth.TenantUserRelaHandler;
import us.zoom.cube.site.lib.ResponseObject;
import us.zoom.cube.site.lib.input.role.RoleMenuRelaInput;
import us.zoom.cube.site.lib.input.role.UserRoleInput;
import us.zoom.cube.site.lib.output.PageResult;
import us.zoom.cube.site.lib.query.NameQuery;
import us.zoom.cube.site.lib.query.PageQuery;
import us.zoom.infra.dao.model.RoleMenuRelaDO;
import us.zoom.infra.dao.model.UserRoleDO;

import java.util.Arrays;
import java.util.List;

@SpringBootTest(classes = CubeSiteApplication.class,webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)//springboot
public class RoleServiceTest extends  BaseTest {

    @Autowired
    UserRoleService userRoleService;

    @Autowired
    private RoleMenuRelaService roleMenuRelaService;

    @Autowired
    private TenantUserRelaHandler tenantUserRelaHandler;


    @Test
    public void test(){
//        assighAuth();
        switchToAdmin();
        switchApiPath("/api/role/deleteRoleByName");
        String roleName="test_role_1";
        String desc = "test_desc";
        UserRoleInput userRoleInput = mock(roleName,true,true,desc);
        userRoleService.deleteRoleByName(userRoleInput);
        switchApiPath("/api/role/addRole");
        ResponseObject responseObject = userRoleService.addRole(userRoleInput);

        switchApiPath("/api/role/editRole");
        userRoleInput.setRoleDesc("test_desc2");
        userRoleInput.setCrossService(false);
        userRoleInput.setId(responseObject.getData().toString());
        userRoleService.editRole(userRoleInput);

        switchApiPath("/api/role/getRoleByName");
        responseObject = userRoleService.getRoleByName(userRoleInput);
        UserRoleDO userRoleDO = (UserRoleDO) responseObject.getData();
        Assert.isTrue(userRoleDO.getRoleDesc().equals("test_desc2") && false == userRoleDO.getCrossService()," fail");

        switchApiPath("/api/role/queryRole");
        PageQuery<NameQuery> pageQuery = new PageQuery<>();
        NameQuery nameQuery = new NameQuery();
        nameQuery.setName(roleName);
        pageQuery.setQueryPara(nameQuery);
        responseObject =  userRoleService.queryRole(pageQuery);
        PageResult<UserRoleDO> pageResult = (PageResult<UserRoleDO>) responseObject.getData();
        Assert.isTrue(pageResult.getTotal() == 1 && pageResult.getItems().size() == 1 && pageResult.getItems().get(0).getRole().equals(roleName)," fail");

        nameQuery.setName("234234234ljk2342lj3lk23j4k23");
        responseObject =  userRoleService.queryRole(pageQuery);
        pageResult = (PageResult<UserRoleDO>) responseObject.getData();
        Assert.isTrue(pageResult.getTotal() == 0 && pageResult.getItems().size() == 0," fail");

        switchApiPath("/api/role/editPiiAccess");
        userRoleInput.setPiiAccess(false);
        responseObject = userRoleService.editPiiAccess(userRoleInput);

        switchApiPath("/api/role/getRoleByName");
        responseObject = userRoleService.getRoleByName(userRoleInput);
        userRoleDO = (UserRoleDO) responseObject.getData();
        Assert.isTrue(userRoleDO.getPiiAccess()== false ," fail");


        RoleMenuRelaInput roleMenuRelaInput = new RoleMenuRelaInput();
        List<String> resIds = Arrays.asList("r1","r2","r3");
        roleMenuRelaInput.setMenuResIds(resIds);
        roleMenuRelaInput.setRoleName(roleName);
        roleMenuRelaService.assignMenu(roleMenuRelaInput);

        List<RoleMenuRelaDO> relaDOList = roleMenuRelaService.getRelaByRoleFromDB(roleName);


        Assert.isTrue(relaDOList != null && relaDOList.size() ==3 ," fail");
        Assert.isTrue(resIds.contains(relaDOList.get(0).getMenuResId()) ," fail");
        Assert.isTrue(resIds.contains(relaDOList.get(1).getMenuResId()) ," fail");
        Assert.isTrue(resIds.contains(relaDOList.get(2).getMenuResId()) ," fail");

        switchApiPath("/api/role/deleteRoleByName");
        userRoleService.deleteRoleByName(userRoleInput);
    }

    private RoleMenuRelaInput mockRela(String roleName) {
        RoleMenuRelaInput relaInput = new RoleMenuRelaInput();
        relaInput.setMenuResIds(Arrays.asList("r1","r2","r3"));
        relaInput.setRoleName(roleName);
        return relaInput;
    }


    private static  UserRoleInput mock(String name, Boolean crossService , Boolean pii, String desc) {
        UserRoleInput userRoleInput = new UserRoleInput();
        userRoleInput.setRole(name);
        userRoleInput.setCrossService(crossService);
        userRoleInput.setPiiAccess(pii);
        userRoleInput.setRoleDesc(desc);
        return userRoleInput;
    }


}
