package us.zoom.cube.site.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.interceptor.AsyncUncaughtExceptionHandler;

import java.lang.reflect.Method;
import java.util.Arrays;

@Slf4j
public class CustomAsyncExceptionHandler implements AsyncUncaughtExceptionHandler {

    @Override
    public void handleUncaughtException(Throwable ex, Method method, Object... params) {
        log.error("Exception occurred while executing async method - Method: {}, Parameters: {}", method.getName(), Arrays.toString(params), ex);
        // TODO: Email alerting can be integrated here

    }
}

