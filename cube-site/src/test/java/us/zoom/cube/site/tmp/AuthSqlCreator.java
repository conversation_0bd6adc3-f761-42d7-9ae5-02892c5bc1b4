package us.zoom.cube.site.tmp;

import org.apache.commons.io.FileUtils;
import us.zoom.cube.site.lib.ResponseObject;
import us.zoom.cube.lib.utils.JsonUtils;
import java.util.List;
import java.io.File;
import java.io.IOException;

public class AuthSqlCreator {

    public static void main(String []gs){
        try {
            String conteng = FileUtils.readFileToString(new File("D:\\\\zoom\\\\online_sql.txt"),"UTF-8");
            ResponseObject<List> sqls = JsonUtils.toObject(conteng,ResponseObject.class);
            sqls.getData().forEach(item->System.out.println(item));
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

}
