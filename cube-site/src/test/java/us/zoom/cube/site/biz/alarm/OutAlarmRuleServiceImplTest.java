package us.zoom.cube.site.biz.alarm;

import com.google.common.collect.Lists;
import com.zoom.op.monitor.dao.alarm.AlarmDefinitionDao;
import com.zoom.op.monitor.domain.alarm.*;
import org.junit.jupiter.api.Test;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import us.zoom.cube.site.CubeSiteApplication;
import us.zoom.cube.site.biz.AuthService;
import us.zoom.cube.site.biz.TenantService;
import us.zoom.cube.site.core.MetricsHandler;
import us.zoom.cube.site.lib.input.OutAlarmRuleInput;
import us.zoom.cube.site.lib.output.alarm.OutAlarmRuleFieldCondition;
import us.zoom.cube.site.lib.output.alarm.OutAlarmRuleOutput;
import us.zoom.cube.site.lib.output.alarm.OutAlarmRuleTagCondition;
import us.zoom.cube.site.lib.output.config.metrics.FieldOrTag;
import us.zoom.cube.site.lib.query.OutAlarmRuleQuery;
import us.zoom.infra.dao.model.MetricsDO;
import us.zoom.infra.dao.model.TenantDO;

import java.util.List;
import java.util.Optional;


import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.when;

/**
 * @author: Anthony.Zhou
 * @date: 2021/5/20
 */
@SpringBootTest(classes = CubeSiteApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class OutAlarmRuleServiceImplTest {

    @Autowired
    OutAlarmRuleService test;

    @MockBean
    AuthService authService;

    @MockBean
    TenantService tenantService;

    @MockBean
    MetricsHandler metricsHandler;

    @MockBean
    AlarmDefinitionDao alarmDefinitionDao;

    @Test
    public void list() {
        TenantDO mockTenantDO = new TenantDO();
        mockTenantDO.setId("100");
        mockTenantDO.setName("mockTenanatName");
        when(tenantService.getTenantByName("mockTenanatName")).thenReturn(mockTenantDO);

//        doAnswer(new Answer() {
//            public Object answer(InvocationOnMock invocation) {
//                Object[] args = invocation.getArguments();
//                return "called with arguments: " + args;
//            }}).when(authService).mustTenantAdminOrAdmin("1", "100");

        MetricsDO mockMetricsDO = new MetricsDO();
        mockMetricsDO.setMetricsName("ip_access");
        mockMetricsDO.setId("1000");
        when(metricsHandler.getMetricsByNameOfTenant("100", "ip_access")).thenReturn(mockMetricsDO);

        Optional<AlarmDefinition> mockAlarmDefinition = Optional.of(
            new AlarmDefinition() {{
                setId("10000");
                setTenantId("100");
                setUserId("1");
                setName("mockAlarmName");
                List<AlarmRule> alarmRuleList = Lists.newArrayList();
                AlarmRule alarmRule = new AlarmRule();
                alarmRule.setId("10000");
                alarmRule.setLevel(AlarmLevel.ERROR);
                alarmRule.setNeedHits(1);

                RuleCondition fieldConditon = new RuleCondition();
                fieldConditon.setName("testField");
                fieldConditon.setConditionType(ConditionType.FIELD);
                fieldConditon.setThreshold("1.0");
                fieldConditon.setOperator(">=");
                RuleCondition tagConditon = new RuleCondition();
                tagConditon.setConditionType(ConditionType.TAG);
                tagConditon.setName("url");
                tagConditon.setOperator("in");
                tagConditon.setThreshold("/aa,/bb");
                RuleCondition expressionConditon = new RuleCondition();
                expressionConditon.setConditionType(ConditionType.EXPRESSION);
                expressionConditon.setExpression("isAttack(\"clusterId,remoteIp,uri\", \"remoteIp.count\", 3, 2, 4.0, 5.0, 1)");
                List<RuleCondition> ruleConditionList = Lists.newArrayList(fieldConditon, tagConditon, expressionConditon);

                alarmRule.setConditions(ruleConditionList);
                alarmRuleList.add(alarmRule);
                setRules(alarmRuleList);
            }}
        );
        when(alarmDefinitionDao.findByNameAndTenantId("mockAlarmName", "100")).thenReturn(mockAlarmDefinition);


        OutAlarmRuleQuery outAlarmRuleQuery = new OutAlarmRuleQuery();
        outAlarmRuleQuery.setUserId("1");
        outAlarmRuleQuery.setServiceName("mockTenanatName");
        outAlarmRuleQuery.setMetricsName("ip_access");
        outAlarmRuleQuery.setAlarmName("mockAlarmName");

        OutAlarmRuleOutput expect = new OutAlarmRuleOutput();
        expect.setType(2);
        expect.setRuleId("10000");
        OutAlarmRuleFieldCondition fieldCondition = new OutAlarmRuleFieldCondition();
        fieldCondition.setFieldName("testField");
        fieldCondition.setFieldValue(1.0);
        fieldCondition.setOperate(">=");
        expect.setFieldCondition(Lists.newArrayList(fieldCondition));
        expect.setQueryDataCount(2.0);
        expect.setMinDataCount(3.0);
        expect.setMaxErrorDataCountRate(4.0);
        expect.setRateScope(5.0);
        OutAlarmRuleTagCondition condition = new OutAlarmRuleTagCondition();
        condition.setTagName("url");
        condition.setTagValue(Lists.newArrayList("/aa", "/bb"));
        condition.setOperate(OutAlarmRuleServiceImpl.TagConditionOperatorEnum.IN.getValue());
        expect.setTagCondition(Lists.newArrayList(condition));
        List<OutAlarmRuleOutput> expectList = Lists.newArrayList(expect);
        assertEquals(expectList, test.list(outAlarmRuleQuery));
    }

    @Test
    public void addRule() {
        TenantDO mockTenantDO = new TenantDO();
        mockTenantDO.setId("100");
        mockTenantDO.setName("mockTenanatName");
        when(tenantService.getTenantByName("mockTenanatName")).thenReturn(mockTenantDO);
//
//        doAnswer(new Answer() {
//            public Object answer(InvocationOnMock invocation) {
//                Object[] args = invocation.getArguments();
//                return "called with arguments: " + args;
//            }}).when(authService).mustTenantAdminOrAdmin("1", "100");

        MetricsDO mockMetricsDO = new MetricsDO();
        mockMetricsDO.setMetricsName("ip_access");
        mockMetricsDO.setId("1000");
        when(metricsHandler.getMetricsByNameOfTenant("100", "ip_access")).thenReturn(mockMetricsDO);

        AlarmDefinition mockAlarmDefinition =
                new AlarmDefinition() {{
                    setId("10000");
                    setTenantId("100");
                    setUserId("1");
                    setName("mockAlarmName");
                    List<AlarmRule> alarmRuleList = Lists.newArrayList();
                    setRules(alarmRuleList);
                }};
        when(alarmDefinitionDao.findByNameAndTenantId("mockAlarmName", "100")).thenReturn(Optional.of(mockAlarmDefinition));

        FieldOrTag field = new FieldOrTag();
        field.setMetaType(FieldOrTag.fieldMetaType);
        field.setFieldOrTagName("remoteIp.count");

        FieldOrTag tag = new FieldOrTag();
        tag.setMetaType(FieldOrTag.tagMetaType);
        tag.setFieldOrTagName("remoteIp");
        List<FieldOrTag> fieldOrTagList = Lists.newArrayList(field, tag);
        when(metricsHandler.getTagAndField("1000")).thenReturn(fieldOrTagList);

        AlarmDefinition saveAlarmDefinition = new AlarmDefinition();
        saveAlarmDefinition.setId("10000");
        saveAlarmDefinition.setTenantId("100");
        saveAlarmDefinition.setUserId("1");
        saveAlarmDefinition.setName("mockAlarmName");
        AlarmRule alarmRule = new AlarmRule();
        alarmRule.setLevel(AlarmLevel.ERROR);
        alarmRule.setNeedHits(1);

        RuleCondition fieldCondition = new RuleCondition();
        fieldCondition.setName("remoteIp.count");
        fieldCondition.setConditionType(ConditionType.FIELD);
        fieldCondition.setOperator(">=");
        fieldCondition.setThreshold(String.valueOf(1.0));

        RuleCondition tagConditon = new RuleCondition();
        tagConditon.setName("remoteIp");
        tagConditon.setConditionType(ConditionType.TAG);
        tagConditon.setOperator("in");
        tagConditon.setThreshold("127.0.0.1");

        RuleCondition expressionConditon = new RuleCondition();
        expressionConditon.setExpression("isAttack(\"remoteIp\", \"remoteIp.count\", 3, 2, 4.0, 5.0, 1)");
        expressionConditon.setConditionType(ConditionType.EXPRESSION);

        alarmRule.setConditions(Lists.newArrayList(fieldCondition, tagConditon, expressionConditon));
        saveAlarmDefinition.setRules(Lists.newArrayList(alarmRule));

        doAnswer(new Answer() {
            public Object answer(InvocationOnMock invocation) {
                Object[] args = invocation.getArguments();
                AlarmDefinition alarmDefinition = (AlarmDefinition) args[0];
                alarmDefinition.getRules().get(alarmDefinition.getRules().size() - 1).setId("10001");
                return alarmDefinition;
            }}).when(alarmDefinitionDao).save(saveAlarmDefinition);

        OutAlarmRuleInput input = new OutAlarmRuleInput();
        input.setType(2);
        input.setUserId("1");
        input.setServiceName("mockTenanatName");
        input.setMetricsName("ip_access");
        input.setAlarmName("mockAlarmName");
        OutAlarmRuleFieldCondition outAlarmRuleFieldCondition = new OutAlarmRuleFieldCondition();
        outAlarmRuleFieldCondition.setFieldName("remoteIp.count");
        outAlarmRuleFieldCondition.setFieldValue(1.0);
        outAlarmRuleFieldCondition.setOperate(OutAlarmRuleServiceImpl.FieldConditionOperatorEnum.GE.getValue());
        input.setFieldCondition(Lists.newArrayList(outAlarmRuleFieldCondition));
        input.setQueryDataCount(3);
        input.setMinDataCount(2);
        input.setMaxErrorDataCountRate(4.0);
        input.setRateScope(5.0);
        OutAlarmRuleTagCondition outAlarmRuleTagCondition = new OutAlarmRuleTagCondition();
        outAlarmRuleTagCondition.setOperate(OutAlarmRuleServiceImpl.TagConditionOperatorEnum.IN.getValue());
        outAlarmRuleTagCondition.setTagValue(Lists.newArrayList("127.0.0.1"));
        outAlarmRuleTagCondition.setTagName("remoteIp");
        input.setTagCondition(Lists.newArrayList(outAlarmRuleTagCondition));
        assertEquals("10001", test.addRule(input));
    }
}
