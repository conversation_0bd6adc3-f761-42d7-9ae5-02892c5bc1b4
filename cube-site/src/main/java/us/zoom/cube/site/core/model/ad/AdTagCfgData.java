package us.zoom.cube.site.core.model.ad;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-07-06 10:55
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AdTagCfgData {
    private String id;
    private String adConfigId;
    private String tagName;
    private String tagValue;
    private Integer adTagType;
    List<AdTagHolidayCfgData> tagHolidayCfgDataList;
}
