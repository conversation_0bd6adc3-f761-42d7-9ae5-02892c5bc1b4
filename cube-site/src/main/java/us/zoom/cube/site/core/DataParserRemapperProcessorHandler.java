package us.zoom.cube.site.core;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import us.zoom.infra.dao.model.DataParserGrokProcessorDO;
import us.zoom.infra.dao.model.DataParserRemapperProcessorDO;
import us.zoom.infra.dao.service.DataParserGrokProcessorDAO;
import us.zoom.infra.dao.service.DataParserRemapperProcessorDAO;

import java.util.Arrays;
import java.util.List;

@Component
public class DataParserRemapperProcessorHandler {


    @Autowired
    private DataParserRemapperProcessorDAO dataParserRemapperProcessorDAO;


    public void addRemapperProcessor(DataParserRemapperProcessorDO remapperProcessorDO) {
        Assert.notNull(remapperProcessorDO,"remapper processor is null !");
        dataParserRemapperProcessorDAO.add(remapperProcessorDO);
    }



    public void editRemapperProcessor(DataParserRemapperProcessorDO remapperProcessorDO) {
        Assert.notNull(remapperProcessorDO,"remappter processor   is null !");
        dataParserRemapperProcessorDAO.edit(remapperProcessorDO);
    }

    public void delRemapperProcessor(String id) {
        Assert.notNull(id,"id is null !");
        dataParserRemapperProcessorDAO.del(id);
    }


    public DataParserRemapperProcessorDO getRemapperProcessorById(String id) {
        Assert.notNull(id,"id is null !");
        return dataParserRemapperProcessorDAO.getRemapperProcessorById(id);
    }

    public List<DataParserRemapperProcessorDO> listByPipelineIds(List<String> pipeLineIds) {
        return dataParserRemapperProcessorDAO.listByPipelineIds(pipeLineIds);
    }

    public void delRemapperProcessorByPipelineId(String pipelineId) {
        Assert.notNull(pipelineId,"pipelineId is null !");
        dataParserRemapperProcessorDAO.delByPipelindIds(Arrays.asList(pipelineId));
    }

    public void delRemapperProcessorByPipelineIds(List<String> pipelineIds) {
        if(CollectionUtils.isEmpty(pipelineIds)){
            return;
        }
        dataParserRemapperProcessorDAO.delByPipelindIds(pipelineIds);
    }
}
