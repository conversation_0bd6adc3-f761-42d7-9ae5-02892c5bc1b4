package us.zoom.cube.site.core.parser.process.core.common;

import groovy.lang.GroovyShell;
import groovy.lang.Script;
import org.codehaus.groovy.runtime.InvokerHelper;
import us.zoom.cube.site.core.parser.process.core.common.enums.MonitoringLogType;
import us.zoom.cube.site.core.parser.process.core.common.exception.GroovyException;
import us.zoom.cube.site.core.parser.process.core.monitoring.MonitoringLogUtils;
import us.zoom.cube.site.infra.enums.WebCodeEnum;
import us.zoom.cube.site.infra.utils.Md5Utils;
import us.zoom.cube.site.lib.SiteException;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class GroovyHandler {
    private final static GroovyShell groovyShell = new GroovyShell();
    private final static Map<String, Script> scriptCache = new ConcurrentHashMap<>();
    private String function;
    private Script script;
    private ErrorTags errorTags;
    private MonitoringLogUtils monitoringLogUtils = new MonitoringLogUtils();
    private String scriptText;
    private String md5;

    public GroovyHandler(String function, String scriptText) {
        this.function = function;
        this.scriptText = scriptText;
        try {
            this.md5 = Md5Utils.getMD5(scriptText);
        } catch (Exception e) {
            throw new GroovyException(String.format("init data parser name: [%s] create md5 error, scriptText:%s", errorTags.getName(), scriptText), e);
        }
    }

    public <T> T invoke(Object... objects) {
        try {
            if (scriptCache.containsKey(md5)) {
                this.script = scriptCache.get(md5);
            } else {
                this.script = initGroovyScript(scriptText);
                scriptCache.put(md5, script);
            }
            return (T) InvokerHelper.invokeMethod(script, function, objects);
        } catch (Exception e) {
            monitoringLogUtils.printErrorLog(errorTags, MonitoringLogType.groovyFail, e);
            throw new SiteException(WebCodeEnum.InnerError.getCode(),e.getMessage());
        }
    }

    public Script initGroovyScript(String scriptText) {
        try {
            return groovyShell.parse(scriptText);
        } catch (Exception e) {
            monitoringLogUtils.printErrorLog(errorTags, MonitoringLogType.groovyInitFail, e);
            throw new GroovyException("init groovy script fail", e);
        }
    }

    protected static Map<String, Script> getScriptCache() {
        return scriptCache;
    }
}