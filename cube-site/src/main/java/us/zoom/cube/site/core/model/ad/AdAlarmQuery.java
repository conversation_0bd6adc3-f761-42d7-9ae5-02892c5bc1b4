package us.zoom.cube.site.core.model.ad;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;
import us.zoom.cube.site.lib.BasePara;

/**
 * <AUTHOR>
 * @date 2024-01-17 16:00
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AdAlarmQuery extends BasePara {
    private String id;
    private String metricsId;

    public void check() {
        Assert.isTrue(StringUtils.isNotBlank(id), "adAlarmQuery id can not be empty");
        Assert.isTrue(StringUtils.isNotBlank(metricsId), "adAlarmQuery metricsId can not be empty");
    }
}
