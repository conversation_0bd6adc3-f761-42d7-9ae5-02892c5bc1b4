package us.zoom.cube.site.infra.enums.catalog;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 */
public enum SourceType {
    LOG_ERROR("LogError"),
    TRACE_ERROR("TraceError"),
    TRACE("Trace");

    private final String value;

    SourceType(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public static SourceType fromString(String value) {
        for (SourceType type : SourceType.values()) {
            if (StringUtils.equals(type.getValue(), value)) {
                return type;
            }
        }
        return null;
    }

    public static boolean isValid(String value) {
        return fromString(value) != null;
    }
} 