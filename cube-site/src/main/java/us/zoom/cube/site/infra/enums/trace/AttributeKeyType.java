package us.zoom.cube.site.infra.enums.trace;

import org.apache.commons.lang3.StringUtils;

/**
 * @author: eason.jia
 * @date: 2024/8/18
 */
public enum AttributeKeyType {
    UNSPECIFIED(""),
    TAG("tag"),
    RESOURCE("resource");

    private final String type;

    AttributeKeyType(String type) {
        this.type = type;
    }

    public String getType() {
        return type;
    }

    public static boolean validate(String keyType) {
        for (AttributeKeyType type : AttributeKeyType.values()) {
            if (StringUtils.equals(type.getType(), keyType)) {
                return true;
            }
        }
        return false;
    }
}
