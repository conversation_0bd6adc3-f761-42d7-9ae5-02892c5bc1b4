package us.zoom.cube.site.lib.output.alarm;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: <PERSON><PERSON>
 * @date: 2022/11/21 13:35
 * @desc:
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AlarmFullViewOutput {

    private String name;

    private String status;

    private String content;

    private Long count;

    private Long ts;

    private Long gmtCreate;

}
