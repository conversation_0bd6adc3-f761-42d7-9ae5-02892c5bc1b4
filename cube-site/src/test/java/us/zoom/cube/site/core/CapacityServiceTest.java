package us.zoom.cube.site.core;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import us.zoom.cube.site.CubeSiteApplicationTests;
import us.zoom.cube.site.biz.CapacityDashService;
import us.zoom.cube.site.biz.CapacityServerService;
import us.zoom.cube.site.lib.*;
import us.zoom.cube.site.lib.output.PageResult;
import us.zoom.cube.site.lib.query.CapacityQuery;
import us.zoom.cube.site.lib.query.NameQuery;
import us.zoom.cube.site.lib.query.PageQuery;
import us.zoom.infra.dao.model.CapacityServiceDO;
import us.zoom.cube.lib.utils.JsonUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022-07-11 13:24
 */
public class CapacityServiceTest extends CubeSiteApplicationTests {

    @Autowired
    CapacityDashService capacityDashService;

    @Autowired
    CapacityServerService capacityServerService;


    @Test
    public void addCapacityDashServiceTest(){
        CapacityDashCfg capacityDashCfg = new CapacityDashCfg();
        capacityDashCfg.setDashName("test2");
        capacityDashCfg.setStatus(1);
        capacityDashCfg.setMetricsField("cpu");
        capacityDashCfg.setTags("cluster,region");
        capacityDashCfg.setSimplePeriod(14);
        capacityDashCfg.setValueType(1);
        CapacityClacCfg capacityClacCfg = new CapacityClacCfg();
        capacityClacCfg.setCalcOpen("true");
        capacityClacCfg.setAverageTime(5);
        capacityClacCfg.setDataBaseName("ds_web");
        capacityClacCfg.setTableName("charlesTest2");
        capacityClacCfg.setOneA(1);
        capacityClacCfg.setTwoA(2);
        capacityClacCfg.setThreeA(3);
        capacityDashCfg.setCapacityClacCfg(capacityClacCfg);
        String test = JsonUtils.toJsonString(capacityDashCfg);
        capacityDashService.add(capacityDashCfg);

    }

    @Test
    public void editCapacityDashServiceTest(){
        CapacityDashCfg capacityDashCfg = new CapacityDashCfg();
        capacityDashCfg.setId("3caeb3c4-f1fc-4732-a053-d8015f09eef2");
        capacityDashCfg.setDashName("test3_edit");
        capacityDashCfg.setStatus(1);
        capacityDashCfg.setMetricsField("cpu");
        capacityDashCfg.setTags("cluster,region");
        capacityDashCfg.setDefaultTags("cluster");
        capacityDashCfg.setSimplePeriod(14);
        capacityDashCfg.setValueType(1);
        CapacityClacCfg capacityClacCfg = new CapacityClacCfg();
        capacityClacCfg.setCalcOpen("true");
        capacityClacCfg.setAverageTime(5);
        capacityClacCfg.setDataBaseName("ds_web");
        capacityClacCfg.setTableName("charlesTest2");
        capacityClacCfg.setOneA(1);
        capacityClacCfg.setTwoA(2);
        capacityClacCfg.setThreeA(3);
        capacityDashCfg.setCapacityClacCfg(capacityClacCfg);
        String test = JsonUtils.toJsonString(capacityDashCfg);
        capacityDashService.edit(capacityDashCfg);

    }

    @Test
    public void delCapacityDashServiceTest(){
        IdPara idPara = new IdPara();
        idPara.setId("1");
        capacityDashService.delete(idPara);

    }


    @Test
    public void addCapacityServiceTest(){
        CapacityServiceCfg capacityServiceCfg = new CapacityServiceCfg();
        capacityServiceCfg.setUpper(60);
        capacityServiceCfg.setLower(10);
        capacityServiceCfg.setCapacityDashId("3caeb3c4-f1fc-4732-a053-d8015f09eef2");
        capacityServiceCfg.setNormal(30);
        capacityServiceCfg.setServiceName("Infra_Monitor_Cube-Alarm");
        String test = JsonUtils.toJsonString(capacityServiceCfg);
        capacityServerService.add(capacityServiceCfg);
    }

    @Test
    public void editCapacityServiceTest(){
        CapacityServiceCfg capacityServiceCfg = new CapacityServiceCfg();
        capacityServiceCfg.setId("fcda9437-20a6-420d-840f-0267cabcdf90");
        capacityServiceCfg.setUpper(60);
        capacityServiceCfg.setLower(10);
        capacityServiceCfg.setCapacityDashId("3caeb3c4-f1fc-4732-a053-d8015f09eef2");
        capacityServiceCfg.setNormal(35);
        capacityServiceCfg.setServiceName("Meeting_Web_nacos1");
        String test = JsonUtils.toJsonString(capacityServiceCfg);
        capacityServerService.edit(capacityServiceCfg);
    }

    @Test
    public void delCapacityServiceTest(){
        IdPara idPara = new IdPara();
        idPara.setId("e389b525-5c09-4fcc-a82b-b4fa81faf119");
        capacityServerService.delete(idPara);
    }

    @Test
    public void searchHaveTest(){
        PageQuery<NameQuery> pageQuery=new PageQuery<>();
        pageQuery.setPageIndex(1);
        pageQuery.setPageSize(1);
        NameQuery nameQuery=new NameQuery();
        nameQuery.setName(null);
        nameQuery.setId("3caeb3c4-f1fc-4732-a053-d8015f09eef2");
        pageQuery.setQueryPara(nameQuery);
        String test = JsonUtils.toJsonString(pageQuery);
        ResponseObject<PageResult<List<CapacityServiceDO>>> responseObject=capacityServerService.searchService(pageQuery);
        Assertions.assertNotNull(responseObject);
    }


    @Test
    public void getAllTest(){
        CapacityStatus capacityStatus = new CapacityStatus();
        String test3 = JsonUtils.toJsonString(capacityStatus);
        List <CapacityDashCfg> capacityDashCfgList = capacityDashService.getServiceAll(capacityStatus);
        String test = JsonUtils.toJsonString(capacityDashCfgList);
        String test2 = JsonUtils.toJsonString(capacityDashCfgList.get(0));
        Assertions.assertNotNull(capacityDashCfgList);
    }

    @Test
    public void getAllTest1(){
        CapacityStatus capacityStatus = new CapacityStatus();
        capacityStatus.setStatus(1);
        List <CapacityDashCfg> capacityDashCfgList = capacityDashService.getServiceAll(capacityStatus);
        String test = JsonUtils.toJsonString(capacityDashCfgList);
        String test2 = JsonUtils.toJsonString(capacityDashCfgList.get(0));
        Assertions.assertNotNull(capacityDashCfgList);
    }

    @Test
    public void getAllTest2(){
        CapacityStatus capacityStatus = new CapacityStatus();
        capacityStatus.setStatus(0);
        List <CapacityDashCfg> capacityDashCfgList = capacityDashService.getServiceAll(capacityStatus);
        String test = JsonUtils.toJsonString(capacityDashCfgList);
        String test2 = JsonUtils.toJsonString(capacityDashCfgList.get(0));
        Assertions.assertNotNull(capacityDashCfgList);
    }

    @Test
    public void getAllByListTest(){
        List <CapacityListCfg> capacityListCfgList = capacityDashService.getServiceByList();
        String test = JsonUtils.toJsonString(capacityListCfgList);
        String test2 = JsonUtils.toJsonString(capacityListCfgList.get(0));
        Assertions.assertNotNull(capacityListCfgList);
    }

    @Test
    public void getAllTest3(){
        CapacityQuery capacityQuery = new CapacityQuery();
        capacityQuery.setId("3caeb3c4-f1fc-4732-a053-d8015f09eef2");
        capacityQuery.setOperatType(1);
        capacityQuery.setServiceId("3176bf77-6bf4-454c-a928-b0f83716acba");
        //capacityQuery.setServiceName("Infra_Monitor_Cube-Alarm");
        capacityQuery.setTimeEnd("2022-11-21 23:59:59");
        capacityQuery.setTimeStart("2022-11-08 00:00:00");
        Map<String, String> tagsMap = new HashMap<>();
        tagsMap.put("regionId","VA");
        tagsMap.put("clusterId","ds01");
        capacityQuery.setTagsMap(tagsMap);

        String t1 = "{\"id\":\"564c9a46-8f35-4e1b-86fb-846a535e7c6c\",\"serviceId\":\"fb5b6506-a713-43b3-92c8-193f524c841d\",\"tagsMap\":{\"regionId\":\"va\",\"clusterId\":\"devep\"},\"operatType\":1,\"timeStart\":\"2022-11-10 00:00:00\",\"timeEnd\":\"2022-11-29 23:59:59\"}";
        CapacityQuery capacityQuery2 =JsonUtils.toObject(t1,CapacityQuery.class);
        ResponseObject res = capacityServerService.searchCapacityData(capacityQuery2);
        String t = JsonUtils.toJsonString(capacityQuery);
        Assertions.assertNotNull(res);
    }
    @Test
    public void getAllTest4(){
        CapacityQuery capacityQuery = new CapacityQuery();
        capacityQuery.setId("3caeb3c4-f1fc-4732-a053-d8015f09eef2");
        capacityQuery.setOperatType(2);
        capacityQuery.setServiceId("3176bf77-6bf4-454c-a928-b0f83716acba");
        //capacityQuery.setServiceName("Infra_Monitor_Cube-Alarm");
        capacityQuery.setTimeEnd("2022-11-21 23:59:59");
        capacityQuery.setTimeStart("2022-11-08 00:00:00");
        Map<String, String> tagsMap = new HashMap<>();
        tagsMap.put("regionId","VA");
        tagsMap.put("clusterId","ds01");
        capacityQuery.setTagsMap(tagsMap);
        String t = JsonUtils.toJsonString(capacityQuery);
        String t1 = "{\"id\":\"564c9a46-8f35-4e1b-86fb-846a535e7c6c\",\"serviceId\":\"fb5b6506-a713-43b3-92c8-193f524c841d\",\"tagsMap\":{\"regionId\":\"va\",\"clusterId\":\"devep\"},\"operatType\":2,\"timeStart\":\"2022-11-10 00:00:00\",\"timeEnd\":\"2022-11-29 23:59:59\"}";
        CapacityQuery capacityQuery2 =JsonUtils.toObject(t1,CapacityQuery.class);
        ResponseObject res = capacityServerService.searchCapacityData(capacityQuery2);
        Assertions.assertNotNull(res);
    }
    @Test
    public void getAllTest5(){
        CapacityQuery capacityQuery = new CapacityQuery();
        capacityQuery.setId("3caeb3c4-f1fc-4732-a053-d8015f09eef2");
        capacityQuery.setOperatType(3);
        capacityQuery.setServiceId("3176bf77-6bf4-454c-a928-b0f83716acba");
        capacityQuery.setTimeEnd("2023-04-00 23:59:59");
        capacityQuery.setTimeStart("2023-04-16 00:00:00");
        ResponseObject res = capacityServerService.searchCapacityData(capacityQuery);
        String t = JsonUtils.toJsonString(capacityQuery);
        Assertions.assertNotNull(res);
    }
}
