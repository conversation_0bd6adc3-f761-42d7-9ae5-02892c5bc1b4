package us.zoom.cube.site.infra.utils;

import us.zoom.cube.lib.common.DataParserPurposeEnum;

/**
 * <AUTHOR>
 */
public class EnvUtils {

    private static final String GROUP_ID_JOINER = "_aa_";

    public static String generateGroupId(String groupId, int purpose, String env) {
        if (purpose == DataParserPurposeEnum.forward.getValue()) {
            return groupId;
        } else {
            return groupId + GROUP_ID_JOINER + env;
        }
    }

    public static String generateGroupId(String groupId, String env) {
        return generateGroupId(groupId, DataParserPurposeEnum.handle.getValue(), env);
    }
}
