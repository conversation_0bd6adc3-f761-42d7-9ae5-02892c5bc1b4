package us.zoom.cube.site.lib.monitor;

import lombok.Builder;
import lombok.Data;
import us.zoom.cube.sdk.annotation.CubeMonitorLog;
import us.zoom.cube.sdk.annotation.Field;
import us.zoom.cube.sdk.annotation.Tag;


@Data
@Builder
@CubeMonitorLog(measure = "get_workbench_dash")
public class GetWorkbenchDashMonitor {
    @Tag
    private String ip;

    @Field
    private long totalCost;

    @Field
    private long recentCreatedCost;

    @Field
    private long favoriteListCost;

    @Field
    private long topDashCost;


}
