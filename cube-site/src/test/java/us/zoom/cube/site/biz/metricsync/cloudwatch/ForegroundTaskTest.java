package us.zoom.cube.site.biz.metricsync.cloudwatch;

import com.google.common.collect.Lists;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @author: tobey.zhu
 * @date: /2023/06/16
 * @description:
 */
public class ForegroundTaskTest {

    @Test
    public void testIsLegal() {
        ForegroundTask foregroundTask = new ForegroundTask();
        foregroundTask.setMetricList(Lists.newArrayList("cpu"));
        foregroundTask.setMetricType("test_metrics_type");
        foregroundTask.setNamespace("1");
        foregroundTask.setPeriod(60);
        List<Map<String, String>> dimensionList = new ArrayList<>();
        Map<String, String> map = new HashMap<>();
        map.put("region", "us-east-1");
        map.put("DBInstanceIdentifier", "devlog");
        dimensionList.add(map);
        foregroundTask.setDimensionList(dimensionList);

        MetricTypeTemplate metricTypeTemplate = new MetricTypeTemplate();
        List<Map<String, Object>> dimensionFields = new ArrayList<>();
        Map<String, Object> dimensionItem1 = new HashMap<>();
        dimensionItem1.put("dimension", "DBInstanceIdentifier");
        dimensionItem1.put("type", "text");
        dimensionItem1.put("required", true);
        Map<String, Object> dimensionItem2 = new HashMap<>();
        dimensionItem2.put("dimension", "region");
        dimensionItem2.put("type", "enum");
        dimensionItem2.put("required", true);
        dimensionItem2.put("value", Lists.newArrayList("us-east-1"));
        dimensionFields.add(dimensionItem1);
        dimensionFields.add(dimensionItem2);
        metricTypeTemplate.setDimensionFields(dimensionFields);
        foregroundTask.isLegal(metricTypeTemplate);
    }
}
