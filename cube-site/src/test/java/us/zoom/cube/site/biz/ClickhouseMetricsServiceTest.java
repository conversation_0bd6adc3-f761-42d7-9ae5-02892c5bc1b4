package us.zoom.cube.site.biz;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import us.zoom.cube.site.biz.clickhouse.ClickhouseMetricsService;
import us.zoom.cube.site.lib.ResponseObject;
import us.zoom.cube.site.lib.query.influx.TagValueQuery;
import us.zoom.cube.site.lib.query.influx.TagValueQueryV2;

/**
 * @author: canyon.li
 * @date: 2023/12/11
 **/
public class ClickhouseMetricsServiceTest extends BaseTest {

    @Autowired
    ClickhouseMetricsService clickhouseMetricsService;

    @Test
    public void searchTagValuesV2() {
        try {
            for (int i = 0; i < 5; i++) {
                TagValueQueryV2 tagValueQuery = new TagValueQueryV2();
                tagValueQuery.setDatabase("Infra_Monitor_Cube-Alarm");
                tagValueQuery.setMeasurement("cpu");
                tagValueQuery.setTag("instanceId");
                tagValueQuery.setSecond((long)(3600 * 24));
                tagValueQuery.setLimit(100);
                tagValueQuery.setUseCache(false);
                ResponseObject res = clickhouseMetricsService.searchTagValuesV2(tagValueQuery);
                System.out.println(res);
            }
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
    }

    @Test
    public void searchTagValuesV3() {
        try {
            TagValueQueryV2 tagValueQuery = new TagValueQueryV2();
            tagValueQuery.setDatabase("ds_web");
            tagValueQuery.setMeasurement("cpu");
            tagValueQuery.setTag("instanceId");
            tagValueQuery.setSecond((long)(3600 * 24));
            tagValueQuery.setLimit(20);
            tagValueQuery.setUseCache(false);
            ResponseObject res = clickhouseMetricsService.searchTagValuesV2(tagValueQuery);
            System.out.println(res);
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
    }

    @Test
    public void showClickhouseTableSchema() {
        clickhouseMetricsService.showClickhouseTableSchema("fred_test_phone", "fred_test");
    }
}
