package us.zoom.cube.site.core;

import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import us.zoom.infra.dao.model.InspectionInstanceDO;
import us.zoom.infra.dao.service.InspectionInsDAO;

import java.util.List;

/**
 * @Description:
 * @author: <PERSON>
 * @date: 2023/04/11 10:27
 */
@Service
public class InspectionInsHandler {

    @Autowired
    public InspectionInsDAO inspectionInsDAO;

    public int add(InspectionInstanceDO inspectionInstanceDO) {
        return inspectionInsDAO.add(inspectionInstanceDO);
    }

    public int update(InspectionInstanceDO inspectionInstanceDO) {
        return inspectionInsDAO.update(inspectionInstanceDO);
    }

    public int deleteById(String id) {
        return inspectionInsDAO.deleteById(id);
    }

    public InspectionInstanceDO selectById(String id) {
        return inspectionInsDAO.selectById(id);
    }

    public List<InspectionInstanceDO> selectByIds(List<String> ids) {
        return inspectionInsDAO.selectByIds(ids);
    }
}
