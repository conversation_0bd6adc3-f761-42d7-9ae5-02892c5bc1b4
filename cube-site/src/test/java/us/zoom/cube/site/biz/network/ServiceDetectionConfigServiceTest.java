package us.zoom.cube.site.biz.network;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import us.zoom.cube.site.biz.BaseTest;
import us.zoom.cube.site.biz.ServiceDetectionConfigService;
import us.zoom.cube.site.lib.input.network.DetectionConfigBaseData;
import us.zoom.cube.site.lib.input.network.DetectionConfigInput;
import us.zoom.cube.site.lib.input.network.DetectionConfigQuery;
import us.zoom.cube.site.lib.input.network.ServiceDetectionConfigInput;
import us.zoom.cube.site.lib.query.PageQuery;
import us.zoom.infra.thread.ThreadLocalStore;

import java.util.Arrays;

//@FixMethodOrder(MethodSorters.JVM)
public class ServiceDetectionConfigServiceTest extends BaseTest {
    @Autowired
    private ServiceDetectionConfigService serviceDetectionConfigService;

    private String userId = "bdf9bf43-2376-4ade-abd1-bd0fab887009";

    @BeforeEach
    public void before() {
        ThreadLocalStore.setUserInfoLocal(userId);
    }

    @Test
    public void  addService(){
        ServiceDetectionConfigInput serviceDetectionConfigInput = makeServiceDetectionConfigInput();
        serviceDetectionConfigService.addService(serviceDetectionConfigInput);
    }

    @Test
    public void addConfig(){
        DetectionConfigInput detectionConfigInput = new DetectionConfigInput();
        detectionConfigInput.setService("test");
        detectionConfigInput.setProtocol("UDP");
        detectionConfigInput.setPort(39834);
        try {
            serviceDetectionConfigService.addConfig(detectionConfigInput);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void updateConfig(){
        DetectionConfigInput detectionConfigInput = new DetectionConfigInput();
        detectionConfigInput.setId("");
        detectionConfigInput.setService("test");
        detectionConfigInput.setProtocol("TCP");
        detectionConfigInput.setPort(443);
        serviceDetectionConfigService.updateConfig(detectionConfigInput);
    }

    @Test
    public void deleteConfig(){
        DetectionConfigInput detectionConfigInput = new DetectionConfigInput();
        detectionConfigInput.setId("");
        detectionConfigInput.setService("test");
        detectionConfigInput.setProtocol("TCP");
        detectionConfigInput.setPort(443);
        try {
            serviceDetectionConfigService.deleteConfig(detectionConfigInput);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void searchConfig(){
        PageQuery<DetectionConfigQuery> detectionConfigQueryPageQuery = new PageQuery<>();
        DetectionConfigQuery detectionConfigQuery =  new DetectionConfigQuery();
        detectionConfigQuery.setService("test");
        detectionConfigQueryPageQuery.setQueryPara(detectionConfigQuery);
        serviceDetectionConfigService.searchConfig(detectionConfigQueryPageQuery);
    }

    @Test
    public void deleteService(){
        ServiceDetectionConfigInput serviceDetectionConfigInput = makeServiceDetectionConfigInput();
        serviceDetectionConfigService.deleteService(serviceDetectionConfigInput);
    }

    @Test
    public void getProtocol(){
        serviceDetectionConfigService.getProtocol();
    }

    @Test
    public void getAllServiceList(){
        serviceDetectionConfigService.getAllServiceList();
    }

    @Test
    public void getExitServiceList(){
        serviceDetectionConfigService.getExitServiceList();
    }

    private ServiceDetectionConfigInput makeServiceDetectionConfigInput() {
        ServiceDetectionConfigInput serviceDetectionConfigInput = new ServiceDetectionConfigInput();
        serviceDetectionConfigInput.setUserId(userId);
        serviceDetectionConfigInput.setService("test");
        DetectionConfigBaseData detectionConfigBaseData = new DetectionConfigBaseData();
        detectionConfigBaseData.setProtocol("TCP");
        detectionConfigBaseData.setPort(80);
        serviceDetectionConfigInput.setConfigs(Arrays.asList(detectionConfigBaseData));
        return serviceDetectionConfigInput;
    }

}
