package us.zoom.cube.site.biz;

import org.springframework.util.Assert;
import us.zoom.cube.site.infra.utils.IdUtils;
import us.zoom.cube.site.lib.input.menu.MenuItemInput;
import us.zoom.infra.enums.MenuTypeEnum;
import us.zoom.infra.utils.MenuConstants;

import java.util.function.Function;

public class MenuInputTest    {



    public static void main(String []gs){
        checkPara();
    }
    public static void checkPara(){
        MenuItemInput menuItemInput  = mock("test_emnu", MenuConstants.DEFAULT_MENU_ROOT,true,1,"test_res_1","/api/test", MenuTypeEnum.Menu.getCode());
        menuItemInput.checkWhenAdd();

        menuItemInput.setHidden(null);
        String result = checkWhenAdd(menuItemInput);
        Assert.isTrue("Hidden can't be blank.".equals(result),"hidden check error");

        menuItemInput.setHidden(true);
        menuItemInput.setName(null);
        result = checkWhenAdd(menuItemInput);
        Assert.isTrue("Name can't be blank.".equals(result),"name check error");
        menuItemInput.setName("sdfsdf 342 sdf");
        result =  checkWhenAdd(menuItemInput);
        Assert.isTrue("Name can't contain blank char.".equals(result),"name check error");

        menuItemInput.setName("sdf");
        menuItemInput.setType(null);
        result = checkWhenAdd(menuItemInput);
        Assert.isTrue("Menu type  can't be blank.".equals(result),"Menu type check error");

        menuItemInput.setType(90);
        result = checkWhenAdd(menuItemInput);
        Assert.isTrue("Menu type is not wright.".equals(result),"Menu type check error");

        menuItemInput.setType(MenuTypeEnum.Menu.getCode());
        menuItemInput.setResourceId(null);
        result =  checkWhenAdd(menuItemInput);
        Assert.isTrue("ResourceId can't be blank.".equals(result),"ResourceId check error");


        menuItemInput.setResourceId("sdf sdf");
        result = checkWhenAdd(menuItemInput);
        Assert.isTrue("ResourceId can not contains blank char".equals(result),"ResourceId check error");


        menuItemInput.setResourceId(mockStr(3000));
        result =  checkWhenAdd(menuItemInput);
        Assert.isTrue("ResourceId's length must be between 1 and 256".equals(result),"ResourceId check error");

        menuItemInput.setResourceId(mockStr(30));
        menuItemInput.setResourceUrl(mockStr(3000));
        result =   checkWhenAdd(menuItemInput);
        Assert.isTrue("Resource url length must be between 1 and 2048".equals(result),"ResourceId check error");

    }

    private static String mockStr(int length) {
        StringBuilder builder = new StringBuilder();
        for(int i=0;i<length;i++){
            builder.append("a");
        }
        return  builder.toString();
    }

    private static  MenuItemInput mock(String name, String parentResId, Boolean hidden, Integer order, String resId, String resUrl, Integer type) {
        MenuItemInput menuItemDO = new MenuItemInput();
        menuItemDO.setId(IdUtils.generateId());
        menuItemDO.setType(type);
        menuItemDO.setName(name);
        menuItemDO.setParentResId(parentResId);
        menuItemDO.setHidden(hidden);
        menuItemDO.setMenuOrder(order);
        menuItemDO.setResourceId(resId);
        menuItemDO.setResourceUrl(resUrl);
        return menuItemDO;
    }


    public  static  String checkWhenAdd(MenuItemInput menuItemInput ){
        String result = null;
        try{
            menuItemInput.checkWhenAdd();
        }catch (Exception e){
            result = e.getMessage();
            System.out.println(result);
        }
        return result;
    }

}
