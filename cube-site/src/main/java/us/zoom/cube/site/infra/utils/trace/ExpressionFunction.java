package us.zoom.cube.site.infra.utils.trace;

import com.google.common.collect.ImmutableMap;
import org.apache.commons.math3.special.Erf;
import org.apache.commons.math3.special.Gamma;

import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;

/**
 * @author: eason.jia
 * @date: 2024/8/19
 */
public class ExpressionFunction {

    public static Function<Double, Double> exp() {
        return x -> Math.exp(x);
    }

    public static Function<Double, Double> log() {
        return x -> Math.log(x);
    }

    public static Function<Double, Double> ln() {
        return x -> Math.log(x);
    }

    public static Function<Double, Double> exp2() {
        return x -> Math.pow(2, x);
    }

    public static Function<Double, Double> log2() {
        return x -> Math.log(x) / Math.log(2);
    }

    public static Function<Double, Double> exp10() {
        return x -> Math.pow(10, x);
    }

    public static Function<Double, Double> log10() {
        return x -> Math.log10(x);
    }

    public static Function<Double, Double> sqrt() {
        return x -> Math.sqrt(x);
    }

    public static Function<Double, Double> cbrt() {
        return x -> Math.cbrt(x);
    }

    public static Function<Double, Double> erf() {
        return x -> Erf.erf(x);
    }

    public static Function<Double, Double> erfc() {
        return x -> Erf.erfc(x);
    }

    public static Function<Double, Double> lgamma() {
        return x -> Math.log(Gamma.gamma(x));
    }

    public static Function<Double, Double> tgamma() {
        return x -> Gamma.gamma(x);
    }

    public static Function<Double, Double> sin() {
        return x -> Math.sin(x);
    }

    public static Function<Double, Double> cos() {
        return x -> Math.cos(x);
    }

    public static Function<Double, Double> tan() {
        return x -> Math.tan(x);
    }

    public static Function<Double, Double> asin() {
        return x -> Math.asin(x);
    }

    public static Function<Double, Double> acos() {
        return x -> Math.acos(x);
    }

    public static Function<Double, Double> atan() {
        return x -> Math.atan(x);
    }

    public static Function<Double, Double> degrees() {
        return x -> x * 180 / Math.PI;
    }

    public static Function<Double, Double> radians() {
        return x -> x * Math.PI / 180;
    }

    public static Function<Long, Object> now() {
        return x -> TimeUnit.MILLISECONDS.toSeconds(System.currentTimeMillis());
    }

    public static Map<String, Function> functionMap() {
        return ImmutableMap.<String, Function>builder()
                .put("exp", exp())
                .put("log", log())
                .put("ln", ln())
                .put("exp2", exp2())
                .put("log2", log2())
                .put("log10", log10())
                .put("exp10", exp10())
                .put("log10", log10())
                .put("sqrt", sqrt())
                .put("cbrt", cbrt())
                .put("erf", erf())
                .put("erfc", erfc())
                .put("tgamma", tgamma())
                .put("sin", sin())
                .put("cos", cos())
                .put("tan", tan())
                .put("asin", asin())
                .put("acos", acos())
                .put("atan", atan())
                .put("degrees", degrees())
                .put("radians", radians())
                .put("now", now())
                .build();
    }
}
