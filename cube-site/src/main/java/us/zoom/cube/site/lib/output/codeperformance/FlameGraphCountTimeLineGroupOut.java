package us.zoom.cube.site.lib.output.codeperformance;

import lombok.Data;
import java.util.List;

/**
 * @author: tobey.zhu
 * @date: /2023/07/20
 * @description:
 */
@Data
public class FlameGraphCountTimeLineGroupOut {
    private List<FlameGraphCountTimeLineOut> dataListByHost;
    private List<FlameGraphCountTimeLineOut> dataListByClusterId;

    public FlameGraphCountTimeLineGroupOut(List<FlameGraphCountTimeLineOut> dataListByHost, List<FlameGraphCountTimeLineOut> dataListByClusterId) {
        this.dataListByHost = dataListByHost;
        this.dataListByClusterId = dataListByClusterId;
    }
}
