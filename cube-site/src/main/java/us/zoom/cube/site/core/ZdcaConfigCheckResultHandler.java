package us.zoom.cube.site.core;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import us.zoom.cube.lib.common.CubeConstants;
import us.zoom.infra.dao.model.ZdcaConfigCheckResultDO;
import us.zoom.infra.dao.service.ZdcaConfigCheckResultDAO;

import java.util.List;

@Component
@Slf4j
public class ZdcaConfigCheckResultHandler {
    @Autowired
    private ZdcaConfigCheckResultDAO zdcaConfigCheckResultDAO;

    public void batchAdd(List<ZdcaConfigCheckResultDO> toAddZdcaCheckResults) {
        List<List<ZdcaConfigCheckResultDO>> batches = ListUtils.partition(toAddZdcaCheckResults, CubeConstants.CUBE_SQL_BATCH_ADD_SIZE);
        for (List<ZdcaConfigCheckResultDO> batch : batches) {
            zdcaConfigCheckResultDAO.batchInsert(batch);
        }
    }

    public void batchUpdate(List<ZdcaConfigCheckResultDO> toUpdateZdcaCheckResults) {
        List<List<ZdcaConfigCheckResultDO>> batches = ListUtils.partition(toUpdateZdcaCheckResults, CubeConstants.CUBE_SQL_BATCH_UPDATE_SIZE);
        for (List<ZdcaConfigCheckResultDO> batch : batches) {
            zdcaConfigCheckResultDAO.batchUpdate(batch);
        }
    }

    public ZdcaConfigCheckResultDO selectByServerId(String serverId) {
        return zdcaConfigCheckResultDAO.selectByServerId(serverId);
    }
}
