package us.zoom.cube.site.lib.output.panoramic;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
public class AlarmResult implements Serializable {
    @Serial
    private static final long serialVersionUID = -2058168288558314643L;

    private List<AlarmRecordsResult> alarmRecordsList = new ArrayList<>();
    private Long totalCount;
    private LevelResult alarmLevelCount = new LevelResult();
}
