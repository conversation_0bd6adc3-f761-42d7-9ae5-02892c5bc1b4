package us.zoom.cube.site.biz;

import org.influxdb.dto.QueryResult;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import us.zoom.cube.site.biz.dashboard.WebGuardDataProvideService;
import us.zoom.cube.site.core.MetricsHandler;
import us.zoom.cube.site.core.TenantHandler;
import us.zoom.cube.site.lib.query.DashboardViewQuery;
import us.zoom.infra.dao.model.MetricsDO;
import us.zoom.infra.dao.model.TenantDO;
import us.zoom.infra.dao.model.ViewComponentDO;
import us.zoom.infra.enums.MetricDataProvideTypeEnum;
import us.zoom.infra.influx.service.InfluxService;
import us.zoom.infra.model.dashboard.BaseViewComponentCfg;
import us.zoom.infra.model.dashboard.DataProvideCfg;
import us.zoom.cube.lib.utils.JsonUtils;

import java.util.*;

@ExtendWith(MockitoExtension.class)
public class WebGuardDataProvideServiceTest{


    @Mock
    private InfluxService influxService;


    @Mock
    private TenantHandler tenantHandler;

    @Mock
    private MetricsHandler metricsHandler;


    @InjectMocks
    private WebGuardDataProvideService webGuardDataProvideService;

    @Test
    public void test(){
        DashboardViewQuery dashboardViewQuery=new DashboardViewQuery();
        dashboardViewQuery.setTenantId("bb6a0a17-0703-4e06-affc-92834a5d9079");
        TenantDO tenantDO=new TenantDO();
        tenantDO.setId("bb6a0a17-0703-4e06-affc-92834a5d9079");
        tenantDO.setName("iweb");
        Mockito.when(tenantHandler.getTenantFromCacheFirst(Mockito.any())).thenReturn(tenantDO);

        MetricsDO eventMetric=new MetricsDO();
        eventMetric.setMetricsName("web_guard_url_event_agg");
        MetricsDO urlMetric=new MetricsDO();
        urlMetric.setMetricsName("url_app");
        Mockito.when(metricsHandler.getMetricsById("5dfd20af-04b9-446e-a8e3-7c2cb0f4eb44")).thenReturn(eventMetric);
        Mockito.when(metricsHandler.getMetricsById("898cac6a-e0c8-4837-8ba0-692d8796a57b")).thenReturn(urlMetric);


        TreeSet<Double> topEventSet=new TreeSet<>();
        TreeSet<Double> topUrlSet=new TreeSet<>();

        QueryResult eventResult = mockEventResult(topEventSet);
        QueryResult urlResult = mockUrlesult(topUrlSet);

        Mockito.when(influxService.getMetricDisplay(Mockito.any(),Mockito.any())).thenReturn(eventResult,urlResult);


        ViewComponentDO viewComponentDO=new ViewComponentDO();
        BaseViewComponentCfg cfg=new BaseViewComponentCfg();
        DataProvideCfg dataProvideCfg=new DataProvideCfg();
        dataProvideCfg.setType(MetricDataProvideTypeEnum.cumstomer.getCode());
        dataProvideCfg.setDataProvideKey("web_guard");
        Map<String,Object> context=new HashMap<>();
        context.put("eventMetricId","5dfd20af-04b9-446e-a8e3-7c2cb0f4eb44");
        context.put("urlMetricId","898cac6a-e0c8-4837-8ba0-692d8796a57b");
        context.put("eventQuery"," event ='cookie_not_exists_post' or event ='cookie_not_exists_get' or event ='cookie_not_exists_ajax' or event ='cookie_not_exists_client' ");
        context.put("eventGroupby", "uri,event");
        context.put("eventValueField", "uri.count");
        context.put("urlGroupby", "orginUri");
        context.put("urlValueField", "usedTime.count");
        context.put("urlKey", "orginUri");
        context.put("eventKey", "uri");

        Map<String,String> titles= new HashMap<>();
        titles.put("Start","start");
        titles.put("End","end");
        titles.put("URI","uri");
        titles.put("Event Type","eventType");
        titles.put("Event Count","eventCount");
        titles.put("Request Count","requestCount");
        titles.put("Uri Count","urlCount");
        titles.put("Radio","radio");
        context.put("titles",titles);
        cfg.setNeedGroupBy(false);
        cfg.setNeedCompress(false);
        cfg.setX("time");


        dataProvideCfg.setContext(context);
        cfg.setDataProvideCfg(dataProvideCfg);
        System.out.println("cfg is "+JsonUtils.toJsonString(cfg));
        viewComponentDO.setConfigs(JsonUtils.toJsonString(cfg));
        webGuardDataProvideService.getData(dashboardViewQuery,viewComponentDO);
    }

    private QueryResult mockUrlesult(TreeSet<Double> topSet) {
        QueryResult queryResult=new QueryResult();
        queryResult.setResults(new ArrayList<>());
        queryResult.getResults().add(new QueryResult.Result());
        queryResult.getResults().get(0).setSeries(new ArrayList<>());
        System.out.println("url===============");
        for(int i=0;i<50;i++){
            QueryResult.Series series=new QueryResult.Series();
            series.setColumns(Arrays.asList("sumResult"));
            series.setName("url_app");
            Double uriCount=Math.abs(new Random().nextDouble()*100);
            series.setValues(Arrays.asList(Arrays.asList(uriCount)));
            Map<String,String> tags=new HashMap<>();
            tags.put("orginUri","uri"+i);
            series.setTags(tags);
            topSet.add(uriCount);
            queryResult.getResults().get(0).getSeries().add(series);
            System.out.println("uri"+i+","+uriCount);

        }
        return queryResult;
    }

    private QueryResult mockEventResult(TreeSet<Double> uriCountTopSet) {
        QueryResult queryResult=new QueryResult();
        queryResult.setResults(new ArrayList<>());
        queryResult.getResults().add(new QueryResult.Result());
        queryResult.getResults().get(0).setSeries(new ArrayList<>());
        System.out.println("event===============");

        for(int i=0;i<50;i++){
            QueryResult.Series series=new QueryResult.Series();
            series.setColumns(Arrays.asList("sumResult"));
            series.setName("web_guard_url_event_agg");
            Map<String,String> tags=new HashMap<>();
            tags.put("uri","uri"+i);
            tags.put("event","event"+i);

            series.setTags(tags);
            Double uriCount=Math.abs(new Random().nextDouble()*100);
            series.setValues(Arrays.asList(Arrays.asList(uriCount)));
            uriCountTopSet.add(uriCount);
            queryResult.getResults().get(0).getSeries().add(series);
            System.out.println("uri"+i+","+uriCount);
        }
        return queryResult;
    }
}
