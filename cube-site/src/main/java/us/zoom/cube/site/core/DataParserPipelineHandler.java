package us.zoom.cube.site.core;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.lib.common.CubeConstants;
import us.zoom.cube.site.lib.input.pipeline.PipelineBatchUpdateInput;
import us.zoom.cube.site.lib.input.pipeline.PipelineSearchInput;
import us.zoom.cube.site.lib.query.PageQuery;
import us.zoom.cube.site.lib.query.PipelineQuery;
import us.zoom.infra.dao.model.CollectorDO;
import us.zoom.infra.dao.model.DataParserPipelineDO;
import us.zoom.infra.dao.model.DataParserPipelineQueryDO;
import us.zoom.infra.dao.model.PipelineQueryDO;
import us.zoom.infra.dao.service.*;
import us.zoom.infra.enums.PipelineUseStatusEnum;
import us.zoom.infra.utils.Instance;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> @date 2020/3/10
 */
@Component
@Slf4j
public class DataParserPipelineHandler {


    @Autowired
    private DataParserPipelineDAO dataParserPipelineDAO;


    @Autowired
    private CollectorDAO collectorDAO;

    @Autowired
    private CollectorFieldDAO collectorFieldDAO;

    @Autowired
    private DataParserGrokProcessorDAO dataParserGrokProcessorDAO;
    @Autowired
    private DataParserFilterProcessorDAO dataParserFilterProcessorDAO;
    @Autowired
    private DataParserRemapperProcessorDAO dataParserRemapperProcessorDAO;
    @Autowired
    private DataParserEncryptionProcessorDAO dataParserEncryptionProcessorDAO;
    @Autowired
    private DataParserGroovyProcessorDAO dataParserGroovyProcessorDAO;
    @Autowired
    private DataParserLabelMysqlProcessorDAO dataParserLabelMysqlProcessorDAO;
    @Autowired
    private DataParserLabelRedisProcessorDAO dataParserLabelRedisProcessorDAO;
    @Autowired
    private DataParserTimestampProcessorDAO dataParserTimestampProcessorDAO;

    public List<DataParserPipelineQueryDO> search(PageQuery<PipelineSearchInput> psiQuery) {
        PipelineSearchInput input = psiQuery.getQueryPara();
        if (input == null) {
            return Lists.newArrayList();
        }
        input.check();
        return dataParserPipelineDAO.search(input.getName(), input.getTenantId(), input.getSourceType(), input.getSourceId(), input.getAlarmQueueId(), input.getCalcQueueId(),
                input.getUseStatus(), psiQuery.getPageSize() * (psiQuery.getPageIndex() - 1), psiQuery.getPageSize());
    }

    public Integer searchCount(PageQuery<PipelineSearchInput> psiQuery) {
        PipelineSearchInput input = psiQuery.getQueryPara();
        if (psiQuery.getQueryPara() == null) {
            return 0;
        }
        return dataParserPipelineDAO.searchCount(input.getName(), input.getTenantId(), input.getSourceType(), input.getSourceId(), input.getAlarmQueueId(), input.getCalcQueueId(),
                input.getUseStatus());
    }

    public void batchEdit(PipelineBatchUpdateInput updateInput) {
        dataParserPipelineDAO.batchUpdate(updateInput.getPipelineIds(), updateInput.getCalcQueueId(), updateInput.getAlarmQueueId());
    }

    public void addPipeline(DataParserPipelineDO dataParserPipelineDO) {
        Assert.notNull(dataParserPipelineDO, "pipeline   is null !");
//        dataParserPipelineDO.setUseStatus(PipelineUseStatusEnum.use.getValue());
        adjustData(dataParserPipelineDO);

        dataParserPipelineDAO.addPipeline(dataParserPipelineDO);
    }

    private void adjustData(DataParserPipelineDO dataParserPipelineDO) {

        if (dataParserPipelineDO.getUseStatus() == null) {
            dataParserPipelineDO.setUseStatus(PipelineUseStatusEnum.use.getValue());
        }

        if (null == dataParserPipelineDO.getCollectorId()) {
            dataParserPipelineDO.setCollectorId("");
        }
        if (null == dataParserPipelineDO.getParentId()) {
            dataParserPipelineDO.setParentId("");
        }
        if (null == dataParserPipelineDO.getFilterRule()) {
            dataParserPipelineDO.setFilterRule("");
        }

        if (null == dataParserPipelineDO.getTopic()) {
            dataParserPipelineDO.setTopic("");
        }
    }

    public List<DataParserPipelineDO> findByParam(PageQuery<PipelineQuery> pageQuery) {
        PipelineQuery query = pageQuery.getQueryPara();
        return dataParserPipelineDAO.findByParam(query.getTenantId(), query.getDataParserId(), query.getName(),
                pageQuery.getStartIndex(), pageQuery.getPageSize());
    }

    public List<String> findNameByParam(PageQuery<PipelineQuery> pageQuery) {
        PipelineQuery query = pageQuery.getQueryPara();
        return dataParserPipelineDAO.findNameByParam(query.getTenantId(), query.getDataParserId(), query.getName(),
                pageQuery.getStartIndex(), pageQuery.getPageSize());
    }

    public Integer getCountByParam(PipelineQuery query) {
        return dataParserPipelineDAO.getCountByParam(query.getTenantId(), query.getDataParserId(), query.getName());
    }


    @Transactional(rollbackFor = Exception.class)
    public void editPipeline(DataParserPipelineDO dataParserPipelineDO) {
        Assert.notNull(dataParserPipelineDO, "pipeline   is null !");
        adjustData(dataParserPipelineDO);
        dataParserPipelineDAO.edit(dataParserPipelineDO);

        dataParserPipelineDO = dataParserPipelineDAO.getPipelineById(dataParserPipelineDO.getId());

        if (StringUtils.isNotBlank(dataParserPipelineDO.getCollectorId())) {
            collectorDAO.updateTopic(dataParserPipelineDO.getCollectorId(), dataParserPipelineDO.getTopic());
        }

        // update pipeline name also update collector name
        if (StringUtils.isNotBlank(dataParserPipelineDO.getCollectorId()) && StringUtils.isNotBlank(dataParserPipelineDO.getName().trim())) {
            editCollectorName(dataParserPipelineDO);
        }
    }


    public void editCollectorName(DataParserPipelineDO dataParserPipelineDO) {
        try {
            CollectorDO collectorDO = collectorDAO.getCollectorById(dataParserPipelineDO.getCollectorId());
            if (Objects.nonNull(collectorDO)) {
                collectorDAO.updateCollectorName(dataParserPipelineDO.getCollectorId(), dataParserPipelineDO.getName().trim());
            }
        } catch (Exception e) {
            log.error("update collector name error,", e);
        }
    }

    public String getCollectorIdById(String id) {
        Assert.notNull(id, "id   is null !");
        return dataParserPipelineDAO.getCollectorIdById(id);
    }

    public String getCollectorIdByDataParserId(String dataParserId) {
        Assert.notNull(dataParserId, "dataParserId   is null !");
        return dataParserPipelineDAO.getCollectorIdByDataParserId(dataParserId);
    }


    @Transactional(rollbackFor = Exception.class)
    public void delPipeline(String id, List<String> sonsId) {
        List<String> ids = new ArrayList<>();
        ids.add(id);
        ids.addAll(Instance.ofNullable(sonsId));
        dataParserPipelineDAO.delByIds(ids);
        dataParserGrokProcessorDAO.delByPipelindIds(ids);
        dataParserFilterProcessorDAO.delByPipelindIds(ids);
        dataParserRemapperProcessorDAO.delByPipelindIds(ids);
    }

    @Transactional(rollbackFor = Exception.class)
    public void delPipeline(List<String> pipelineIds) {
        List<String> ids = new ArrayList<>();
        ids.addAll(pipelineIds);
        dataParserPipelineDAO.delByIds(ids);
        dataParserGrokProcessorDAO.delByPipelindIds(ids);
        dataParserFilterProcessorDAO.delByPipelindIds(ids);
        dataParserRemapperProcessorDAO.delByPipelindIds(ids);
        dataParserEncryptionProcessorDAO.delByPipelindIds(ids);
        dataParserGroovyProcessorDAO.delByPipeIds(ids);
        dataParserLabelMysqlProcessorDAO.delByPipelindIds(ids);
        dataParserLabelRedisProcessorDAO.delByPipelindIds(ids);
        dataParserTimestampProcessorDAO.delByPipelindIds(ids);
    }

    public List<DataParserPipelineDO> listPipelinesByDataParserId(String dataParserId) {
        Assert.notNull(dataParserId, "dataParserId   is null !");
        return dataParserPipelineDAO.listPipelinesByDataParserId(dataParserId);
    }


    public List<DataParserPipelineDO> listByDataParserIds(List<String> dataParserIds) {
        if (CollectionUtils.isEmpty(dataParserIds)) {
            return Collections.emptyList();
        }
        return dataParserPipelineDAO.listByDataParserIds(dataParserIds);
    }

    public DataParserPipelineDO getPipelineById(String pipelineId) {
        Assert.notNull(pipelineId, "pipelineId   is null !");
        return dataParserPipelineDAO.getPipelineById(pipelineId);
    }

    public List<DataParserPipelineDO> listAllPipeline(Integer useStatus) {
        List<DataParserPipelineDO> dataParserPipelineDOList = new ArrayList<>();
        long counts = dataParserPipelineDAO.queryCountsByStatus(useStatus);
        int pageSize = CubeConstants.CUBE_SQL_BATCH_QUERY_SIZE;
        long pageNum = counts / pageSize + (counts % pageSize == 0 ? 0 : 1);
        for (int i = 1; i <= pageNum; i++) {
            dataParserPipelineDOList.addAll(dataParserPipelineDAO.listBatchDataParserPipelineByStatus(useStatus, pageSize * (i - 1), pageSize));
        }
        return dataParserPipelineDOList;
    }


    public List<DataParserPipelineDO> listAll() {
        List<DataParserPipelineDO> dataParserPipelineDOList = new ArrayList<>();
        long counts = dataParserPipelineDAO.queryCounts();
        int pageSize = CubeConstants.CUBE_SQL_BATCH_QUERY_SIZE;
        long pageNum = counts / pageSize + (counts % pageSize == 0 ? 0 : 1);
        for (int i = 1; i <= pageNum; i++) {
            dataParserPipelineDOList.addAll(dataParserPipelineDAO.listBatchDataParserPipeline(pageSize * (i - 1), pageSize));
        }
        return dataParserPipelineDOList;
    }

    public void updateCollectorId(String destPipeLineId, String destCollectorId) {
        dataParserPipelineDAO.updateCollectorId(destCollectorId, destPipeLineId);
    }

    public DataParserPipelineDO getByTenantIdAndName(String tenantId, String name) {
        return dataParserPipelineDAO.getByTenantIdAndName(tenantId, name);
    }

    public List<DataParserPipelineDO> queryExistTenantsByPipelineNameAndTenantIds(PipelineQueryDO pipelineQueryDO) {
        return dataParserPipelineDAO.queryExistTenantsByPipelineNameAndTenantIds(pipelineQueryDO);
    }

    public DataParserPipelineDO getPipelineByCollectorId(String collectorId) {
        return dataParserPipelineDAO.getPipelineByCollectorId(collectorId);
    }


    public List<DataParserPipelineDO> selectByDataParserIdAndName(String dataParserId, String pipelineName) {
        return dataParserPipelineDAO.selectByDataParserIdAndName(dataParserId, pipelineName);
    }

    public List<String> listAllMetricsName(String tenantId) {
        return dataParserPipelineDAO.listAllPipelineName(tenantId);
    }

    public List<DataParserPipelineDO> listByPipelineIds(List<String> pipelineIdList) {
        return dataParserPipelineDAO.listByPipelineIds(pipelineIdList);
    }

    public void batchUpdateCollectorId(List<DataParserPipelineDO> parserPipelineDOSToUpdate) {
        dataParserPipelineDAO.batchUpdateCollectorId(parserPipelineDOSToUpdate);
    }

    public List<DataParserPipelineDO> listAllInfraTenant(List<String> names) {
        if (CollectionUtils.isEmpty(names)) {
            return new ArrayList<>();
        }
        return dataParserPipelineDAO.listAllInfraTenant(names);
    }

    public List<DataParserPipelineDO> listByCollectorIds(List<String> collectorIds) {
        if (CollectionUtils.isEmpty(collectorIds)) {
            return new ArrayList<>();
        }
        return dataParserPipelineDAO.listByCollectorIds(collectorIds);
    }

    public List<DataParserPipelineDO> getExistInOtherDataParsers(String tenantId, List<String> names, String dataParserId){
        return dataParserPipelineDAO.getExistInOtherDataParsers(names, tenantId, dataParserId);
    }
}
