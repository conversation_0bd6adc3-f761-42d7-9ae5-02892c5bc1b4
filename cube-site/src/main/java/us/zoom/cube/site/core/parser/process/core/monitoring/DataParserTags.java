package us.zoom.cube.site.core.parser.process.core.monitoring;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;

@Data
@Builder
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
public class DataParserTags {
    private String service;
    private String aaEnv;
    private String dfName = "";
    private String dfId = "";
    private String dpName;
    private String dpId;
    private String unit;
    private String input;
    private String inputDc;
    private String gId;
    private String logType;
    private String alarm;
    private String calc;

    private Map<String, Object> cacheMap;

    public Map<String, Object> toMap() {
        if (cacheMap == null) {
            cacheMap = new HashMap<>(12);
            cacheMap.put("service", service);
            cacheMap.put("aaEnv", aaEnv);
            cacheMap.put("dfName", dfName);
            cacheMap.put("dfId", dfId);
            cacheMap.put("dpName", dpName);
            cacheMap.put("dpId", dpId);
            cacheMap.put("unit", unit);
            cacheMap.put("input", input);
            cacheMap.put("inputDc", inputDc);
            cacheMap.put("gId", gId);
            cacheMap.put("alarm", alarm);
            cacheMap.put("calc", calc);
            cacheMap.put("logType", logType);
        }
        return cacheMap;
    }
}