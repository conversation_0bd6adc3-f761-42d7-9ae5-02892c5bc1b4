package us.zoom.cube.site.lib.output.panoramic;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

@Data
public class IncidentSearchResult implements Serializable {
    @Serial
    private static final long serialVersionUID = -4823124568173917536L;
    private Integer pageNum;
    private Integer pageSize;
    private Long total;
    private List<IncidentSearchDetailResult> result;
}
