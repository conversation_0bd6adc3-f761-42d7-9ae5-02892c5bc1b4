package us.zoom.cube.site.core.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.EnvironmentAware;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

/**
 * Environment param in application properties
 *
 * @author: canyon.li
 * @date: 2023/03/30
 **/
@Component
@Slf4j
@Configuration
public class EnvironmentParamCache implements EnvironmentAware {

    private Environment environment;

    private String currConfigEnv;

    @Override
    public void setEnvironment(Environment environment) {
        this.environment = environment;
        this.currConfigEnv = environment.getProperty("spring.profiles.active","");
        log.info("current config env is:{}", currConfigEnv);
    }

    public String getCurrConfigEnv() {
        return currConfigEnv;
    }
}
