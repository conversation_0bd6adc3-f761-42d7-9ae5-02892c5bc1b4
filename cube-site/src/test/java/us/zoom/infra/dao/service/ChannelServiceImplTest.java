package us.zoom.infra.dao.service;

import com.zoom.op.monitor.dao.alarm.AlarmDefinitionDao;
import com.zoom.op.monitor.dao.alarm.ChannelDao;
import com.zoom.op.monitor.domain.alarm.Channel;
import com.zoom.op.monitor.domain.alarm.ChannelParameter;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import us.zoom.cube.lib.common.ChannelEngineEnum;
import us.zoom.cube.site.biz.alarm.ChannelServiceImpl;
import us.zoom.cube.site.core.RsaService;
import us.zoom.cube.site.lib.ResponseObject;
import us.zoom.cube.lib.utils.JsonUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

@ExtendWith(MockitoExtension.class)
public class ChannelServiceImplTest   {


    @InjectMocks
    private ChannelServiceImpl channelService;

    @Mock
    private ChannelDao channelDao;

    @Mock
    private AlarmDefinitionDao alarmDefinitionDao;
    @Mock
    private RsaService rsaService;

    @Test
    public void testGetTenantIdImChannelWithDecrypt() throws Exception {
        Mockito.doReturn(mockChannel()).when(channelDao).findByTenantIdIn(Arrays.asList("1"));
        //Mockito.doReturn("").when(rsaService).decrypt("");  //Unnecessary Stub


        ResponseObject<Map<String, Channel>> responseObject= channelService.getTenantIdImChannelWithDecrypt(Arrays.asList("1"));
        Map<String,Channel> channelMap = responseObject.getData();
        Assertions.assertTrue(channelMap.get("0").getName().equals("0_0"));
        Assertions.assertTrue(channelMap.get("1").getName().equals("1_1"));
        Assertions.assertTrue(channelMap.get("2").getName().equals("2_2"));
        Assertions.assertTrue(channelMap.get("3").getName().equals("3_0"));
        Assertions.assertTrue(channelMap.get("4").getName().equals("4_0"));

    }

    private List<Channel> mockChannel() {
        List<Channel> results = new ArrayList<>();
        String tenandId ="0";
        //default first
        doMockChanel(results, tenandId,0);

        tenandId ="1";
        //default middle
        doMockChanel(results, tenandId,1);

        tenandId ="2";
        //default last
        doMockChanel(results, tenandId,2);

        tenandId ="3";
        //no default
        doMockChanel(results, tenandId,3);


        tenandId="4";
        Channel channel = new Channel();
        channel.setIsDefault(0);
        channel.setName(tenandId+"_0");
        channel.setId(tenandId+"_0");
        channel.setTenantId(tenandId);
        channel.setEngineName(ChannelEngineEnum.ZOOM_CHAT.getName());
        List<ChannelParameter> parameters = new ArrayList<>();
        ChannelParameter parameter = new ChannelParameter();

        parameter.setName("hello");
        parameter.setValue("va1");
        parameters.add(parameter);
        channel.setParameters(parameters);
        results.add(channel);

        return results;
    }

    private void doMockChanel(List<Channel> results, String tenandId, int defaultIndex) {
        for(int i=0;i<3;i++){
            Channel channel = new Channel();
            channel.setIsDefault(defaultIndex == i ? 1: 0);
            channel.setName(tenandId+"_"+i);
            channel.setId(tenandId+"_"+i);
            channel.setTenantId(tenandId);
            channel.setEngineName(ChannelEngineEnum.ZOOM_CHAT.getName());
            results.add(channel);

            List<ChannelParameter> parameters = new ArrayList<>();
            ChannelParameter parameter = new ChannelParameter();

            parameter.setName("hello");
            parameter.setValue("va1");
            parameters.add(parameter);
            channel.setParameters(parameters);
        }
    }

    @Test
    public void testCheckEndpoint() {
        String str = "{\n" +
                "    \"userId\": \"c793c210-9be2-48f0-8cf5-dd857eafdc40\",\n" +
                "    \"tenantId\": \"29224fd6-2b8b-41c8-bba5-b58903f56d6f\",\n" +
                "    \"createTime\": \"2022-10-13T07:08:33.001+0000\",\n" +
                "    \"modifyTime\": \"2022-10-13T07:08:33.001+0000\",\n" +
                "    \"id\": \"5a4bb0dc-73b0-4249-9cc1-b258a5b33b72\",\n" +
                "    \"name\": \"123\",\n" +
                "    \"engineName\": \"Zoom Chat\",\n" +
                "    \"isDefault\": 0,\n" +
                "    \"parameters\": [\n" +
                "        {\n" +
                "            \"id\": \"1077377b-baf1-4856-a65d-f22ae66e8e7c\",\n" +
                "            \"name\": \"Endpoint\",\n" +
                "            \"isSecret\": \"false\",\n" +
                "            \"value\": \"456\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"id\": \"ce18cbe2-6500-416d-9afc-5f00eb51e10e\",\n" +
                "            \"name\": \"Verification Token\",\n" +
                "            \"isSecret\": \"true\",\n" +
                "            \"value\": \"******\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"id\": \"ead9466e-0d8c-40ca-9ef9-a1eef426eebb\",\n" +
                "            \"name\": \"Is Default\",\n" +
                "            \"isSecret\": \"false\",\n" +
                "            \"value\": \"false\"\n" +
                "        }\n" +
                "    ]\n" +
                "}";
        Channel channel = JsonUtils.toObject(str, Channel.class);
        Channel channel1 = channelService.checkDefaultValue(channel, ChannelServiceImpl.UPDATE);
        Assertions.assertEquals("456?format=fields", channel1.getParameters().get(0).getValue());

        String str1 = "{\n" +
                "    \"userId\": \"c793c210-9be2-48f0-8cf5-dd857eafdc40\",\n" +
                "    \"tenantId\": \"29224fd6-2b8b-41c8-bba5-b58903f56d6f\",\n" +
                "    \"createTime\": \"2022-10-13T07:08:33.001+0000\",\n" +
                "    \"modifyTime\": \"2022-10-13T07:08:33.001+0000\",\n" +
                "    \"id\": \"5a4bb0dc-73b0-4249-9cc1-b258a5b33b72\",\n" +
                "    \"name\": \"123\",\n" +
                "    \"engineName\": \"Zoom Chat\",\n" +
                "    \"isDefault\": 0,\n" +
                "    \"parameters\": [\n" +
                "        {\n" +
                "            \"id\": \"1077377b-baf1-4856-a65d-f22ae66e8e7c\",\n" +
                "            \"name\": \"Endpoint\",\n" +
                "            \"isSecret\": \"false\",\n" +
                "            \"value\": \"k/fm3STUHQITHa8mZDRXES8ZWO?format=fields\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"id\": \"ce18cbe2-6500-416d-9afc-5f00eb51e10e\",\n" +
                "            \"name\": \"Verification Token\",\n" +
                "            \"isSecret\": \"true\",\n" +
                "            \"value\": \"******\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"id\": \"ead9466e-0d8c-40ca-9ef9-a1eef426eebb\",\n" +
                "            \"name\": \"Is Default\",\n" +
                "            \"isSecret\": \"false\",\n" +
                "            \"value\": \"false\"\n" +
                "        }\n" +
                "    ]\n" +
                "}";
        Channel channel2 = JsonUtils.toObject(str1, Channel.class);
        Channel channel3 = channelService.checkDefaultValue(channel2, ChannelServiceImpl.UPDATE);
        Assertions.assertEquals("k/fm3STUHQITHa8mZDRXES8ZWO?format=fields", channel3.getParameters().get(0).getValue());
    }
}
