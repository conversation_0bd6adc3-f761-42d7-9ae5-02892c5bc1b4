package us.zoom.cube.site.core;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import us.zoom.infra.dao.model.NetworkStrategyAgentRelationDO;
import us.zoom.infra.dao.service.NetworkStrategyAgentRelationDAO;

import java.util.List;

@Component
@Transactional
public class NetworkStrategyAgentRelationHandler {
    @Autowired
    private NetworkStrategyAgentRelationDAO networkStrategyAgentRelationDAO;

    public List<NetworkStrategyAgentRelationDO> listAgentsByStrategyId(String strategyId, int type) {
        return networkStrategyAgentRelationDAO.listAgentsByStrategyId(strategyId,type);
    }
}
