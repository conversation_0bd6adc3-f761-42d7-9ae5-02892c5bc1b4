package us.zoom.cube.site.lib.output;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

/**
 * <AUTHOR>
 * @create 2020/5/27 9:04 AM
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class LabelDataSourceMysqlOutput {

    String id;

    String name;

    String type;

    String url;

    String user;

    String password;

    Integer maxActive;

    Integer initialSize;

    Integer maxWait;

    Integer minIdle;
}
