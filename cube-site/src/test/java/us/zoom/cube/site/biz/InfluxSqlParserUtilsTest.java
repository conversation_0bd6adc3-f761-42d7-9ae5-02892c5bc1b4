package us.zoom.cube.site.biz;


import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import us.zoom.cube.site.infra.utils.influx.InfluxSqlParserUtils;
import us.zoom.cube.site.lib.TopStruct;

public class InfluxSqlParserUtilsTest {

    @Test
    public void parseTopN(){
        String sql="SELECT top(\"us\",\"host\",5) AS \"top_us\" FROM ( SELECT max(\"us\") AS \"us\", \"host\",\"clusterId\" FROM \"web\".\"autogen\".\"cpu\" ) GROUP BY \"clusterId\"";
        TopStruct topStruct= InfluxSqlParserUtils.parseTop(sql);
        Assertions.assertTrue(topStruct.isTopn());
        Assertions.assertTrue(topStruct.getTags().size()==1);

        sql="SELECT TOP(\"us\",\"host\",5) AS \"top_us\" FROM ( SELECT max(\"us\") AS \"us\", \"host\",\"clusterId\" FROM \"web\".\"autogen\".\"cpu\" ) GROUP BY \"clusterId\"";
         topStruct= InfluxSqlParserUtils.parseTop(sql);
        Assertions.assertTrue(topStruct.isTopn());
        Assertions.assertTrue(topStruct.getTags().get(0).equals("host"));

        sql="SELECT TOP(\"us\",'host\",5) AS \"top_us\" FROM ( SELECT max(\"us\") AS \"us\", \"host\",\"clusterId\" FROM \"web\".\"autogen\".\"cpu\" ) GROUP BY \"clusterId\"";
        topStruct= InfluxSqlParserUtils.parseTop(sql);
        Assertions.assertTrue(topStruct.isTopn());
        Assertions.assertTrue(topStruct.getTags().get(0).equals("host"));

        sql="SELECT TOP(\"us\",'host\",'name\",5) AS \"top_us\" FROM ( SELECT max(\"us\") AS \"us\", \"host\",\"clusterId\" FROM \"web\".\"autogen\".\"cpu\" ) GROUP BY \"clusterId\"";
        topStruct= InfluxSqlParserUtils.parseTop(sql);
        Assertions.assertTrue(topStruct.isTopn());
        Assertions.assertTrue(topStruct.getTags().get(0).equals("host")&&topStruct.getTags().get(1).equals("name"));
    }
}
