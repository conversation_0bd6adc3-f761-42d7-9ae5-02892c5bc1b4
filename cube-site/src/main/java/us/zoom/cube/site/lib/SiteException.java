package us.zoom.cube.site.lib;

import us.zoom.cube.site.infra.enums.WebCodeEnum;

public class SiteException extends RuntimeException{

    private String code;


    public SiteException(String code, String msg) {
        super(msg);
        this.code = code;
    }

    public SiteException(WebCodeEnum codeEnum) {
        super(codeEnum.getErrMsg());
        this.code = codeEnum.getCode();
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

}
