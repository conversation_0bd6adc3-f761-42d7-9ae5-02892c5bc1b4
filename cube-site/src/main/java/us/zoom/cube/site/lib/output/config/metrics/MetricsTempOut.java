package us.zoom.cube.site.lib.output.config.metrics;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import us.zoom.cube.site.lib.output.MetricsOriginalOutput;
import us.zoom.cube.site.lib.output.agg.AggregationCustomFieldRuleOutput;
import us.zoom.cube.site.lib.output.agg.AggregationFunctionRuleOutput;
import us.zoom.cube.site.lib.output.agg.AggregationHistogramRuleOutput;
import us.zoom.cube.site.lib.output.agg.AggregationPercentileRuleOutput;
import us.zoom.cube.site.lib.output.tag.TagInfoOut;
import us.zoom.infra.enums.MetricsTypeEnum;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * Temporary for supporting Service Montior Dashboard migration.
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class MetricsTempOut {
    String id;

    String metricsName;

    String oldMetricsName;

    List<String> tagNames = new ArrayList<>();

    List<TagDetails> tagDetails = new ArrayList<>();

    Integer type;

    Integer originPeriod;

    String collectorId;

    String tenantId;

    String creator;

    String editor;

    Boolean enabled;

    List<MetricsFieldTempOut> metricsFieldList = new ArrayList<>();

    MetricsAggregationOut metricsAggregation = new MetricsAggregationOut();

    MetricsOriginalOutput metricsOriginalOutput = new MetricsOriginalOutput();

    private List<TagInfoOut> labelInfoList = new ArrayList<>();

    private Boolean hasBatchAggMetric = false;

    private String batchAggMetricId;

    @Data
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class TagDetails {

        String tagName;

        String oldTagName;

        public TagDetails() {
        }

        public TagDetails(String tagName) {
            this.tagName = tagName;
        }

        public TagDetails(String tagName, String oldTagName) {
            this.tagName = tagName;
            this.oldTagName = oldTagName;
        }
    }

    public void sortAggRules() {
        if(!type.equals(MetricsTypeEnum.AGGREGATION.getValue())){
            return;
        }
        List<AggregationFunctionRuleOutput> common = metricsAggregation.getMetricsAggregationRuleCompose().getCommon();
        common.sort((a,b) -> {
            if(a.getAggField().equals(b.getAggField())){
                return a.getIsConditioned().compareTo(b.getIsConditioned());
            }else {
                return a.getAggField().compareTo(b.getAggField());
            }
        });
//        common.forEach(e -> {
//            e.getAggTypes().sort((a,b) -> {
//                return a.getValue().compareTo(b.getValue());
//            });
//        });
        List<AggregationHistogramRuleOutput> histogram = metricsAggregation.getMetricsAggregationRuleCompose().getHistogram();
        histogram.sort((a,b) -> {
            return a.getAggField().compareTo(b.getAggField());
        });
        histogram.forEach(e -> {
            e.getRanges().sort((a,b) -> {
                return a.getLowerLimit().compareTo(b.getLowerLimit());
            });
        });
        List<AggregationPercentileRuleOutput> percentile = metricsAggregation.getMetricsAggregationRuleCompose().getPercentile();
        percentile.sort((a,b) -> {
            return a.getAggField().compareTo(b.getAggField());
        });
        percentile.forEach(e -> {
            e.getPercentileValues().sort((a,b) -> {
                return a.getValue().compareTo(b.getValue());
            });
        });
        List<AggregationCustomFieldRuleOutput> customize = metricsAggregation.getMetricsAggregationRuleCompose().getCustomize();
        customize.sort((a,b) -> {
            return a.getFieldName().compareTo(b.getFieldName());
        });
    }
}
