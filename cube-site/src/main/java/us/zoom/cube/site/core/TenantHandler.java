package us.zoom.cube.site.core;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import us.zoom.cube.lib.common.CloudTypeEnum;
import us.zoom.cube.lib.common.CubeConstants;
import us.zoom.cube.lib.common.ServerTypeEnum;
import us.zoom.cube.site.api.intercept.AuthInterceptor;
import us.zoom.cube.site.biz.SysParaService;
import us.zoom.cube.site.core.auth.AuthHandler;
import us.zoom.cube.site.core.auth.TenantUserRelaHandler;
import us.zoom.cube.site.core.auth.UserRoleHandler;
import us.zoom.cube.site.core.config.CacheLoader;
import us.zoom.cube.site.lib.IdListPara;
import us.zoom.cube.site.lib.input.QueryTenantUserInput;
import us.zoom.cube.site.lib.input.QueryUserRoleInput;
import us.zoom.cube.site.lib.input.UserInCache;
import us.zoom.cube.site.lib.output.PageResult;
import us.zoom.cube.site.lib.output.tenant.TenantUserRoleCount;
import us.zoom.cube.site.lib.query.PageQuery;
import us.zoom.infra.clickhouse.ClickhouseSqlUtil;
import us.zoom.infra.dao.model.*;
import us.zoom.infra.dao.service.TenantDAO;
import us.zoom.infra.dao.service.TenantUserRelaDAO;
import us.zoom.infra.dao.service.UserGroupDAO;
import us.zoom.infra.enums.TenantStatusEnum;
import us.zoom.infra.model.usergroup.RoleCount;
import us.zoom.infra.utils.DateUtils;
import us.zoom.infra.utils.Instance;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static us.zoom.cube.lib.common.CubeConstants.ALL_SERVICES;
import static us.zoom.infra.utils.CommonSplitConstants.COMMA_SPLIT;

@Component
public class TenantHandler implements CacheLoader {

    @Autowired
    private TenantDAO tenantDAO;

    @Autowired
    private TenantUserRelaDAO tenantUserRelaDAO;

    @Autowired
    private TenantUserRelaHandler tenantUserRelaHandler;

    @Autowired
    private UserHandler userHandler;

    @Autowired
    private ChannelHandler channelHandler;

    @Autowired
    private AsyncQueueGroupHandler asyncQueueGroupHandler;

    @Autowired
    private AsyncQueueHandler asyncQueueHandler;

    @Autowired
    private SysParaService sysParaService;

    @Autowired
    private AuthHandler authHandler;

    @Autowired
    private UserRoleHandler userRoleHandler;

    /**
     * Map<tenantId,tenant></tenantId,tenant>
     */
    private final AtomicReference<Map<String, TenantDO>> tenantCache = new AtomicReference<>(new ConcurrentHashMap<>(50));

    /**
     * map<tenantName,Tenant></tenantName,Tenant>
     */
    private final AtomicReference<Map<String, TenantDO>> tenantNameCache = new AtomicReference<>(new ConcurrentHashMap<>(50));

    /**
     * map<tenantName In lower case ,tenantt></tenantName>
     */
    private final AtomicReference<Map<String, TenantDO>> tenantNameInLowerCaseCache = new AtomicReference<>(new ConcurrentHashMap<>(50));
    @Autowired
    private UserGroupDAO userGroupDAO;


    @Override
    public void load() {
        List<TenantDO> tenantDOList = tenantDAO.listAll();
        if (CollectionUtils.isEmpty(tenantDOList)) {
            return;
        }

        Map<String, TenantDO> tenantNameMap = new ConcurrentHashMap<>(50);
        Map<String, TenantDO> tenantNameInLowerCaseMap = new ConcurrentHashMap<>(50);
        Map<String, TenantDO> tenantMap = new ConcurrentHashMap<>(50);
        for (TenantDO tenantDO : tenantDOList) {
            if (null == tenantDO) {
                continue;
            }
            tenantNameMap.put(tenantDO.getName(), tenantDO);
            tenantNameInLowerCaseMap.put(StringUtils.lowerCase(tenantDO.getName()), tenantDO);
            tenantMap.put(tenantDO.getId(), tenantDO);
        }
        tenantCache.set(tenantMap);
        tenantNameCache.set(tenantNameMap);
        tenantNameInLowerCaseCache.set(tenantNameInLowerCaseMap);
    }


    public List<TenantDO> listAll() {
        List<TenantDO> tenantDOList = new ArrayList<>();
        long counts = tenantDAO.queryCounts();
        int pageSize = CubeConstants.CUBE_SQL_BATCH_QUERY_SIZE;
        long pageNum = counts / pageSize + (counts % pageSize == 0 ? 0 : 1);
        for (int i = 1; i <= pageNum; i++) {
            tenantDOList.addAll(tenantDAO.listBatch((long) pageSize * (i - 1), pageSize));
        }
        return tenantDOList;
    }

    public List<TenantDO> listTenantNameLike(String name) {
        return tenantDAO.listTenantNameLike(name);
    }

    public List<TenantDO> myTenants(String userId) {
        return authHandler.getAllTenantUserHas(userId);
    }

    public void addTenant(TenantDO tenantDO) {
        Assert.notNull(tenantDO, "tenant is null ");
        tenantDAO.addTenant(tenantDO);

        // load new tenant into cache
        this.tenantCache.get().put(tenantDO.getId(), tenantDO);
        this.tenantNameCache.get().put(tenantDO.getName(), tenantDO);
        this.tenantNameInLowerCaseCache.get().put(tenantDO.getName().toLowerCase(), tenantDO);

    }

    public void addTenantOnDuplicateKeyUpdate(TenantDO tenantDO) {
        Assert.notNull(tenantDO, "tenant is null ");
        tenantDAO.addTenantOnDuplicateKeyUpdate(tenantDO);
    }

    public void updateServiceKey(String serviceKey, Integer level, String tenantId) {
        Assert.notNull(serviceKey, "serviceKey is null");
        Assert.notNull(tenantId, "tenantId is null");
        tenantDAO.updateServiceKey(serviceKey, level, tenantId);
    }

    public TenantDO getTenantByServiceKey(String serviceKey) {
        Assert.notNull(serviceKey, "serviceKey is null");
        return tenantDAO.getTenantByServiceKey(serviceKey);
    }

    public void clearServiceKey(String tenantId) {
        Assert.notNull(tenantId, "tenantId is null");
        tenantDAO.clearServiceKey(tenantId);
    }

    public TenantDO getTenantByName(String name) {
        Assert.notNull(name, "name is null ");
        return tenantDAO.getTenantByName(name);
    }


    public TenantDO getTenantJustFromCache(String id) {
        return tenantCache.get().get(id);
    }


    public TenantDO getTenantFromCacheFirst(String id) {
        TenantDO tenantDO = tenantCache.get().get(id);
        if (null != tenantDO) {
            return tenantDO;
        }
        tenantDO = getTenantById(id);
        if (null != tenantDO) {
            tenantCache.get().put(id, tenantDO);
        }
        return tenantDO;
    }

    public String matchInfluxDbNameToTenantName(String oriInfluxdbName) {
        return matchInfluxDbNameToTenantName(oriInfluxdbName, true);
    }

    public String matchInfluxDbNameToTenantName(String oriInfluxdbName, boolean reserveQuotes) {
        boolean encloseByDoubleQuote = oriInfluxdbName.startsWith("\"") && oriInfluxdbName.endsWith("\"");
        String influxdbName = encloseByDoubleQuote ? ClickhouseSqlUtil.trimDoubleQuote(oriInfluxdbName) : oriInfluxdbName;
        Optional<String> first = tenantNameCache.get().keySet().stream().filter(u -> u.toLowerCase().equals(influxdbName)).findFirst();
        if (first.isPresent()) {
            return encloseByDoubleQuote && reserveQuotes ? String.format("\"%s\"", first.get()) : first.get();
        }
        return oriInfluxdbName;
    }


    public Set<TenantDO> getTenantByNameFromCacheFirst(String tenantName, int csp) {
        Set<TenantDO> tenantDOList = new HashSet<>();
        String tenantNameSwitched = getTenantNameByAppName(tenantName);
        Set<String> tenantNameSwitchedArray = new HashSet<>(Arrays.asList(tenantNameSwitched.split(",")));
        if (csp == CloudTypeEnum.COLO.getType()) {
            tenantNameSwitchedArray.add(CubeConstants.SYSTEMS_ADMINISTRATION);
        }
        for (String tenant : tenantNameSwitchedArray) {
            TenantDO tenantDO = tenantNameCache.get().get(tenant);
            if (null != tenantDO) {
                tenantDOList.add(tenantDO);
            }
        }
        return tenantDOList;
    }

    public TenantDO getTenantById(String id) {
        Assert.notNull(id, "id is null ");
        return tenantDAO.getTenantById(id);
    }

    public String getTenantNameById(String id) {
        Assert.notNull(id, "id is null ");
        return tenantDAO.getTenantNameById(id);
    }

    public TenantDO getTenantByIdFromCache(String id) {
        Assert.notNull(id, "id is null ");
        return tenantCache.get().get(id);
    }

    public boolean existTenantByName(String name) {
        Assert.notNull(name, "name is null ");
        return tenantNameCache.get().containsKey(name);
    }


    public List<String> existTenantByIdsAndType(List<String> tenantIds, List<Integer> types) {
        return tenantDAO.existTenantByIdsAndType(tenantIds, types);
    }

    public List<String> getTenantByExcludeIds(List<String> tenantIds) {
        return tenantDAO.getTenantByExcludeIds(tenantIds);
    }

    public List<TenantUserRelaDO> getRelationsByUserId(String userId, String tenantId) {
        Assert.notNull(userId, "user id is null");
        Assert.notNull(tenantId, "tenant id is null");
        return tenantUserRelaDAO.getRelationsByUserId(userId, tenantId);
    }

    public List<TenantUserRelaDO> findRelaByUserName(String userName, String tenantId, int pageIndex, int pageSize) {
        return tenantUserRelaDAO.findRelaByUserName(userName, tenantId, pageSize * (pageIndex - 1), pageSize);
    }

    public int getRelaCountByUserName(String userName, String tenantId) {
        return tenantUserRelaDAO.getRelaCountByUserName(userName, tenantId);
    }

    public void editTenantUser(TenantUserRelaDO relationDO) {
        tenantUserRelaDAO.editTenantUser(relationDO);
    }

    public List<TenantDO> findTenantByName(String id, String name, String dataFlowId, int pageIndex, int pageSize) {
        return tenantDAO.findTenantByName(id, name, dataFlowId, pageSize * (pageIndex - 1), pageSize);
    }

    public List<TenantDO> listByIds(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return tenantDAO.listByIds(ids);
    }

    public int getTenantCountByName(String id, String name, String dataFlowId) {
        return tenantDAO.getTenantCountByName(id, name, dataFlowId);

    }


//    public List<TenantDO> getHasTenant(String userId) {
//        return tenantDAO.listTenatByUserId(userId);
//    }

    public TenantDO getTenantByNameIgNoreCase(String appName) {
        return tenantDAO.getTenantByNameIgNoreCase(StringUtils.upperCase(appName), "");

    }

    public synchronized void addTenantAll(List<TenantUserRelaDO> relationDOList) {
        if (CollectionUtils.isEmpty(relationDOList)) {
            return;
        }

        //remove duplicated
        relationDOList = relationDOList.stream().distinct().collect(Collectors.toList());

        //remove exist auth
        List<TenantUserRelaDO> existRelas = tenantUserRelaDAO.queryExistRela(relationDOList);
        Set<String> existRelaSet = new HashSet<>();
        if (CollectionUtils.isNotEmpty(existRelas)) {
            for (TenantUserRelaDO relaDO : existRelas) {
                String key = relaDO.getTenantId() + relaDO.getUserId() + relaDO.getRole();
                existRelaSet.add(key);
            }

            relationDOList = relationDOList.stream().filter(relaDO -> !existRelaSet.contains(relaDO.getTenantId() + relaDO.getUserId() + relaDO.getRole())).collect(Collectors.toList());
        }
        if (CollectionUtils.isNotEmpty(relationDOList)) {
            tenantUserRelaDAO.addAllTenantUserRelation(relationDOList);
        }

    }

    public void delTenantUserRelationByUserIdAndRole(String useId, String role) {
        tenantUserRelaDAO.delTenantUserRelationByUserIdAndRole(useId, role);
    }

    public void delTenantUserRelationByUserId(String userId) {
        tenantUserRelaDAO.delTenantUserRelationByUserId(userId);
    }

    public List<TenantUserRelaDO> findTenantUserRelationByRole(String role) {
        return tenantUserRelaDAO.findTenantUserRelationByRole(role);
    }

    public List<TenantUserRelaDO> findTenantUserRelationByUseId(String userId) {
        return tenantUserRelaDAO.findTenantUserRelationByUseId(userId);
    }

    public List<String> findTenantUserRelationByUseIdAndRole(String userId, List<String> roles) {
        if (null == userId || org.springframework.util.CollectionUtils.isEmpty(roles)) {
            return Collections.emptyList();
        }
        return tenantUserRelaDAO.findTenantUserRelationByUseIdAndRole(userId, roles);
    }

    public List<TenantDO> listUnNoticeTenant(ServerTypeEnum serverTypeEnum, Integer maxNoticeCount, String serviceName) {
        if (ServerTypeEnum.agent == serverTypeEnum) {
            return tenantDAO.listUnNoticeTenantByAgent(maxNoticeCount);
        } else {
            return tenantDAO.listUnNoticeTenantByHub(maxNoticeCount, serviceName);
        }
    }

    private Integer batchUpdateSize = 400;

    public void batchUpdateNoticeInfo(List<TenantDO> updateNoticeTenants, ServerTypeEnum serverTypeEnum) {
        if (updateNoticeTenants.size() > batchUpdateSize) {
            List<List<TenantDO>> tenantBatch = ListUtils.partition(updateNoticeTenants, batchUpdateSize);
            for (List<TenantDO> oneBatch : tenantBatch) {
                if (ServerTypeEnum.agent == serverTypeEnum) {
                    tenantDAO.batchUpdateAgentNotice(oneBatch);
                } else {
                    tenantDAO.batchUpdatHubNotice(oneBatch);
                }
            }
        } else {
            if (ServerTypeEnum.agent == serverTypeEnum) {
                tenantDAO.batchUpdateAgentNotice(updateNoticeTenants);
            } else {
                tenantDAO.batchUpdatHubNotice(updateNoticeTenants);
            }
        }
    }

    public void recoryNotice(Integer maxNoticeInteval, String managerService) {
        Date cmpDate = new Date(DateUtils.addMinute(new Date(), -maxNoticeInteval));
        tenantDAO.recoryAgentNotice(cmpDate);
        tenantDAO.recoryHubNotice(cmpDate, managerService);
    }

    public List<TenantServerCountDO> listTenantServerCount(List<String> toNotifyTenantIds, ServerTypeEnum serverTypeEnum) {
        if (ServerTypeEnum.agent == serverTypeEnum) {
            return tenantDAO.listTenantServerCount(toNotifyTenantIds);
        } else {
            List<TenantServerCountDO> result = tenantDAO.listTenantServerCountByType(serverTypeEnum.name());
            setManagerTenantId(result);
            return result;
        }

    }

    public List<TenantServerCountDO> listTenantServerCountHasOffline(List<String> toNotifyTenantIds, ServerTypeEnum serverTypeEnum) {

        if (ServerTypeEnum.agent == serverTypeEnum) {
            return tenantDAO.listTenantServerCountHasOffline(toNotifyTenantIds);
        } else {
            List<TenantServerCountDO> result = tenantDAO.listTenantServerCountHasOfflineByType(serverTypeEnum.name());
            setManagerTenantId(result);
            return result;
        }
    }


    public void delTenantById(String id) {
        tenantDAO.delTenantById(id);
    }

    public void delTenantUserRelationByTenantId(String tenantId) {
        tenantUserRelaDAO.delTenantUserRelationByTenantId(tenantId);
    }

    public void delTenantUserRelationByTenantIdAndUserId(String tenantId, String userId) {
        tenantUserRelaDAO.delTenantUserRelationByTenantIdAndUserId(tenantId, userId);
    }

    @Transactional(rollbackFor = Exception.class)
    public void delTenant(List<String> tenantIdList, List<String> userIdList, List<String> channelIdList, List<String> inputQueueIdList,
                          List<String> inputQueueGroupIdList) {
        tenantIdList.forEach(id -> {
            delTenantById(id);
            delTenantUserRelationByTenantId(id);
        });
        userIdList.forEach(id -> userHandler.delUser(id));
        channelIdList.forEach(id -> channelHandler.deleteById(id));
        inputQueueIdList.forEach(id -> asyncQueueHandler.deleteById(id));
        inputQueueGroupIdList.forEach(id -> asyncQueueGroupHandler.deleteById(id));
    }


    public List<TenantInfoDO> queryTenantInfoByTenantNames(List<String> tenantNames) {
        return tenantDAO.queryTenantInfoByTenantNames(tenantNames);
    }

    public List<TenantInfoDO> queryTenantInfoByTenantIds(List<String> tenantIds) {
        return tenantDAO.queryTenantInfoByTenantIds(tenantIds);
    }

    public List<TenantServerCountDO> listTenantServerCount(List<TenantDO> toNotifyTenants) {
        return tenantDAO.listTenantServerCount(toNotifyTenants.stream().map(item -> item.getId()).collect(Collectors.toList()));
    }

    private void setManagerTenantId(List<TenantServerCountDO> result) {
        if (CollectionUtils.isNotEmpty(result)) {
            TenantDO tenantDO = tenantDAO.getTenantByName(sysParaService.getManagerService());
            result.forEach(item -> item.setTenantId(tenantDO.getId()));
        }
    }

    public void assignDataFlowIdForIds(List<String> ids, String dataFlowId) {
        tenantDAO.assignDataFlowIdForId(ids, dataFlowId);
    }

    public void deleteDataFlowFormTenant(String dataFlowId) {
        tenantDAO.deleteDataFlowFormTenant(dataFlowId);
    }

    private String getTenantNameByAppName(String appName) {
        if (StringUtils.isNotBlank(appName) && sysParaService.getSwitchAppNameToTenantName().containsKey(appName)) {
            return sysParaService.getSwitchAppNameToTenantName().get(appName);
        }
        return appName;
    }

    public List<String> getNames() {
        return tenantDAO.listNames();
    }

    public List<ServiceMetricsNameDO> listNamesByMetric(String metricsName) {
        return tenantDAO.listNamesByMetrics(metricsName);
    }

    public TenantDO getTenantByNameInLowerCaseFromCache(String tenantName) {
        return tenantNameInLowerCaseCache.get().get(StringUtils.lowerCase(tenantName));
    }

    public List<TenantDO> getAllTenantFromCache() {
        return new ArrayList<>(tenantNameInLowerCaseCache.get().values());
    }

    public PageInfo<TenantDO> listTenantNameLikeAndIdInByPage(String name, List<String> serviceIds, int pageIndex, int pageSize) {
        PageHelper.startPage(pageIndex, pageSize);

        if (CollectionUtils.isEmpty(serviceIds)) {
            return PageInfo.emptyPageInfo();
        }

        List<TenantDO> result = tenantDAO.listTenantNameLikeAndIdIn(name, serviceIds);
        return new PageInfo<>(result);
    }

    public PageResult searchAndPaginate(List<TenantDO> tenantList, String service, int pageIndex, int pageSize) {
        if (StringUtils.isNotBlank(service)) {
            tenantList = tenantList.stream()
                    .filter(tenant -> tenant.getName().contains(service))
                    .collect(Collectors.toList());
        }
        int total = tenantList.size();
        int startIndex = (pageIndex - 1) * pageSize;
        int endIndex = Math.min(startIndex + pageSize, tenantList.size());
        List<TenantDO> paginatedList = tenantList.subList(startIndex, endIndex);
        PageResult result = new PageResult(total, paginatedList);
        return result;
    }

    public void batchDeleteTenant(IdListPara idListPara) {
        tenantDAO.batchUpdateStatus(idListPara.getIds(), TenantStatusEnum.SUSPENDED.getStatus());
    }

    public void batchUpdateTenantStatus(List<String> idListPara, TenantStatusEnum status) {
        tenantDAO.batchUpdateStatus(idListPara, status.getStatus());
    }

    public Set<TenantUserRoleCount> queryUserRole(QueryUserRoleInput queryUserRoleInput) {
        String userId = queryUserRoleInput.getUid();
        String tenantId = queryUserRoleInput.getTid();
        List<String> roles = tenantUserRelaDAO.listRoleByTenantIdAndUserId(tenantId, userId);
        roles = Instance.ofNullable(roles).stream().filter(StringUtils::isNotBlank)
                .filter(role -> null != userRoleHandler.getRoleByNameFromCache(role)).toList();

        Map<String, Set<String>> userTenantRoles = tenantUserRelaHandler.getUserTenantRoles(userId);
        Set<String> crossServiceRoles = Instance.ofNullable(userTenantRoles).values().stream().flatMap(x -> x.stream()).filter(x -> {
            UserRoleDO roleByNameFromCache = userRoleHandler.getRoleByNameFromCache(x);
            if (roleByNameFromCache != null) {
                return roleByNameFromCache.getCrossService();
            }
            return false;
        }).collect(Collectors.toSet());

        List<RoleCount> roleCounts = tenantUserRelaDAO.countTenantRole(userId, tenantId);
        Map<String, Integer> roleCountsMap = Instance.ofNullable(roleCounts).stream().collect(Collectors.toMap(RoleCount::getRole, RoleCount::getCount));

        List<RoleCount> userGroupRoleCounts = userGroupDAO.countUserGroupUser(userId, tenantId);
        Map<String, Integer> userGroupRoleCountsMap = Instance.ofNullable(userGroupRoleCounts).stream().collect(Collectors.toMap(RoleCount::getRole, RoleCount::getCount));


        Set<String> roleSet = new HashSet<>(roles);
        roleSet.addAll(crossServiceRoles);
        Set<TenantUserRoleCount> ret = new HashSet<>();

        for (String role : roleSet) {
            TenantUserRoleCount temp = new TenantUserRoleCount();
            temp.setRole(role);
            Integer count = roleCountsMap.get(role);
            UserRoleDO roleByName = userRoleHandler.getRoleByName(role);
            //if user has cross service role, may not be found tenantUserRela via userId and tenantId
            if (count == null && StringUtils.isNotBlank(tenantId) && roleByName != null && roleByName.getCrossService() && authHandler.hasSuchRole(userId, role)) {
                count = 1;
            }
            temp.setUserCount(count != null ? count : 0);
            temp.setUserGroupCount(userGroupRoleCountsMap.get(role) != null ? userGroupRoleCountsMap.get(role) : 0);
            ret.add(temp);
        }
        return ret;
    }

    public PageResult searchTenantUserByRole(PageQuery<QueryTenantUserInput> queryTenantUserInput) {
        QueryTenantUserInput queryPara = queryTenantUserInput.getQueryPara();
        String role = queryPara.getRole();

        UserRoleDO roleByName = userRoleHandler.getRoleByName(role);
        List<TenantUserRelaDO> tenantUserRelaDOList;
        int count;
        if (roleByName.getCrossService()) {
            tenantUserRelaDOList = tenantUserRelaDAO.findCrossServiceByParam(queryPara.getRole(), null, queryPara.getUid(),
                    queryTenantUserInput.getStartIndex(), queryTenantUserInput.getPageSize());
            count = tenantUserRelaDAO.countCrossServiceByParam(queryPara.getRole(), null, queryPara.getUid());
        } else {
            tenantUserRelaDOList = tenantUserRelaDAO.findByParam(queryPara.getRole(), queryPara.getTid(), queryPara.getUid(),
                    queryTenantUserInput.getStartIndex(), queryTenantUserInput.getPageSize());
            count = tenantUserRelaDAO.countByParam(queryPara.getRole(), queryPara.getTid(), queryPara.getUid());
        }

        Instance.ofNullable(tenantUserRelaDOList).stream().forEach(x -> {
            UserInCache userFromCache = userHandler.getUserFromCache(x.getUserId());
            x.setUserName(null != userFromCache ? userFromCache.getName() : null);

            if (roleByName.getCrossService()) {
                x.setTenantName(ALL_SERVICES);
            } else {
                TenantDO tenantFromCacheFirst = getTenantFromCacheFirst(x.getTenantId());
                x.setTenantName(null != tenantFromCacheFirst ? tenantFromCacheFirst.getName() : null);
            }

        });

        PageResult ps = new PageResult(count, tenantUserRelaDOList);
        return ps;
    }

    public List<String> searchTenantByRoleUser(QueryTenantUserInput queryTenantUserInput) {
        List<TenantUserRelaDO> tenantUserRelaDOList = tenantUserRelaDAO.getRelationsByRoleList(AuthInterceptor.getUserId(), Arrays.asList(queryTenantUserInput.getRole().split(COMMA_SPLIT)));
        List<String> tanantList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(tenantUserRelaDOList)) {
            tanantList = tenantUserRelaDOList.stream()
                    .map(TenantUserRelaDO::getTenantId)
                    .map(this::getTenantFromCacheFirst)
                    .filter(Objects::nonNull)
                    .map(TenantDO::getName)
                    .filter(StringUtils::isNotBlank)
                    .distinct()
                    .collect(Collectors.toList());
        }
        return tanantList;
    }
}
