package us.zoom.cube.site.core.model.common;

import com.zoom.op.monitor.domain.alarm.ConditionType;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import us.zoom.cube.lib.integrations.MetricsFieldTypeEnum;
import us.zoom.infra.dao.model.AlarmDefinitionRuleConditionDO;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

import static us.zoom.infra.utils.AlarmConstants.GREATER_AND_LESS_OPERATOR;

/**
 * @author: canyon.li
 * @date: 2025/04/15
 **/
@Data
public class AlarmConditionThresholdRecModel {

    private String tenantId;

    private String metricId;

    private RuleCondition recCondition;

    private List<RuleCondition> otherConditions;

    @Data
    public static class RuleCondition {

        private ConditionType conditionType;

        private String name;

        private String operator;

        private String valueType;

        private String threshold;
    }

    public boolean isFieldRecValid() {
        return isBaseValid()
                && ConditionType.FIELD.equals(recCondition.getConditionType())
                && GREATER_AND_LESS_OPERATOR.contains(recCondition.getOperator())
                && MetricsFieldTypeEnum.number.name().equals(recCondition.getValueType());
    }

    private boolean isBaseValid() {
        return StringUtils.isNotBlank(tenantId) && StringUtils.isNotBlank(metricId) && Objects.nonNull(recCondition);
    }
}
