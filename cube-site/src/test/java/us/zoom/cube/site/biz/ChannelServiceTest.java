package us.zoom.cube.site.biz;

import com.zoom.op.monitor.domain.alarm.Channel;
import com.zoom.op.monitor.domain.alarm.ChannelParameter;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import us.zoom.cube.site.biz.alarm.ChannelService;
import us.zoom.cube.site.lib.ResponseObject;

import java.util.List;
import java.util.Optional;

/**
 * @author: canyon.li
 * @date: 2023/03/29
 **/
public class ChannelServiceTest extends BaseTest {

    @Autowired
    private ChannelService channelService;

    @Test
    public void checkPagerDuty() {
        Channel channel = Optional.ofNullable(channelService.getById("8d7dde24-4499-4402-ac9c-ca7879b97368")).map(ResponseObject::getData).orElse(new Channel());
        ResponseObject<Object> res = channelService.checkHasLimit(channel, null);
        System.out.println(res);
    }
}
