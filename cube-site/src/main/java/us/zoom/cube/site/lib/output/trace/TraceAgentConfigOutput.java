/*
 * Copyright 2023 Domstoladministrasjonen, Norway
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * SPDX-License-Identifier: Apache-2.0
 */
package us.zoom.cube.site.lib.output.trace;

import lombok.Data;
import lombok.EqualsAndHashCode;
import us.zoom.cube.lib.trace.TraceAgentConfig;

import java.util.Date;


/**
 * @author: adan.zeng
 * @date: 2024/9/12
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class TraceAgentConfigOutput extends TraceAgentConfig {
    private String id;
    private String serviceName;
    private String serviceId;
    private String cluster;
    private String region;
    private Date gmtModify;
    private Date gmtCreate;
    private String lastModifiedUser;
    private String creator;

}
