package us.zoom.cube.site.lib.output.expression;

import lombok.AccessLevel;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.util.Map;
import java.util.List;

/**
 * @author: canyon.li
 * @date: 2023/04/19
 **/
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@Builder
public class ExpressionCheckOutput {
    String content;

    Map<String, Object> params;

    String replaceResult;

    List<Map<String, String>> paramKeyTypes;

}
