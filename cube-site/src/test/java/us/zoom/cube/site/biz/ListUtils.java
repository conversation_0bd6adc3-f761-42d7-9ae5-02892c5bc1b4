package us.zoom.cube.site.biz;

import org.assertj.core.util.Lists;
import org.springframework.beans.BeanUtils;

import java.lang.reflect.InvocationTargetException;
import java.util.List;
import java.util.Objects;

public class ListUtils<T> {
    public List<T> copyList(Object obj, Class<T> classObj) {
        List<T> list2 = Lists.newArrayList();
        if ((!Objects.isNull(obj))) {
            List list1 = (List) obj;
            list1.forEach(item -> {
                try {
                    T data = classObj.newInstance();
                    BeanUtils.copyProperties(item, data);
                    list2.add(data);
                } catch (InstantiationException e) {
                } catch (IllegalAccessException e) {
                }


            });
        }
        return list2;
    }
}

