package us.zoom.cube.site.core;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import us.zoom.infra.dao.model.MetaDataComponentDO;
import us.zoom.infra.dao.service.MetaDataComponentDAO;

import java.util.List;

/**
 * Author: charles.hu
 * Date: 2024/12/10
 * Description:
 */
@Component
@Slf4j
public class MetaDataComponentHandler {

    @Autowired
    private MetaDataComponentDAO metaDataComponentDAO;

    @Transactional(rollbackFor = Exception.class)
    public void batchAddComponent(List<MetaDataComponentDO> metaDataComponentDOList) {
        metaDataComponentDAO.batchInsert(metaDataComponentDOList);
    }

    @Transactional(rollbackFor = Exception.class)
    public void batchUpdateComponent(List<MetaDataComponentDO> metaDataComponentDOList) {
        metaDataComponentDAO.batchUpdate(metaDataComponentDOList);
    }

    @Transactional(rollbackFor = Exception.class)
    public void batchDeleteComponent(List<MetaDataComponentDO> metaDataComponentDOList) {
        metaDataComponentDAO.batchDelete(metaDataComponentDOList);
    }

    public List<MetaDataComponentDO> batchComponentsByKeys(List<String> metaDataComponentDOList) {
        return metaDataComponentDAO.batchByKeys(metaDataComponentDOList);
    }

    public List<MetaDataComponentDO> getAllMetaDataComponents() {
        return metaDataComponentDAO.getAll();
    }
}
