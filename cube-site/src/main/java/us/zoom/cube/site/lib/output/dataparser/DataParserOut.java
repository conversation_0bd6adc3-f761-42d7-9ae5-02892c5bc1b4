package us.zoom.cube.site.lib.output.dataparser;

import us.zoom.cube.site.lib.common.ValueText;
import us.zoom.cube.site.lib.common.dataparser.BaseDataParser;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> @date 2020/3/10
 */
public class DataParserOut extends BaseDataParser {

    private  Map<String, Map<String,List<ValueText>>> aqIdLabels=null;
    private List<DataParserPipelineOut> pipelines;

    public List<DataParserPipelineOut> getPipelines() {
        return pipelines;
    }

    public void setPipelines(List<DataParserPipelineOut> pipelines) {
        this.pipelines = pipelines;
    }

//    @Override
//    public Object getAqId() {
//        String aqlId=super.getAqId() == null ? "" :super.getAqId().toString();
//        return Arrays.asList(new ValueText(aqlId,aqlId));
//    }


    public Map<String, Map<String, List<ValueText>>> getAqIdLabels() {
        return aqIdLabels;
    }

    public void setAqIdLabels(Map<String, Map<String, List<ValueText>>> aqIdLabels) {
        this.aqIdLabels = aqIdLabels;
    }
}
