package us.zoom.cube.site.infra.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.NullNode;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import us.zoom.cube.lib.common.LogTypeEnum;
import us.zoom.cube.lib.hub.HubConstants;
import us.zoom.cube.lib.hub.Measure;

import java.util.*;

@Slf4j
public class HubProcessJsonUtils {
    private ObjectMapper mapper = new ObjectMapper();

    public List<Map<String, Object>> processJson(JsonNode jsonNode) {
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        List<Map<String, Object>> results = new ArrayList<>();
        LogTypeEnum logTypeEnum = getLogType(jsonNode);
        if (logTypeEnum == LogTypeEnum.JSON) {
            results.add(isStandardLog(jsonNode) ?
                    transferMeasure(jsonNode) :
                    transferJson(jsonNode));
        } else {
            // agent common field
            Map<String, Object> commonMap = agentBaseInfoMap(logTypeEnum, jsonNode);
            JsonNode msgJsonNode = jsonNode.get(HubConstants.AGENT_MESSAGE);
            if (msgJsonNode != null && msgJsonNode.isTextual()) {
                String message = msgJsonNode.asText();
                String[] messages = message.split(HubConstants.ENTER);
                for (String msg : messages) {
                    processAgentMessageField(msg, results, commonMap);
                }
            }
        }
        return results;
    }

    private LogTypeEnum getLogType(JsonNode jsonNode) {
        // zdca log contain: message, appName, topType, labelList, logstream
        // zdca base log contain: message, appName, topType, labelList, but not logstream
        // other is json
        if (jsonNode.has(HubConstants.AGENT_MESSAGE)
                && jsonNode.has(HubConstants.AGENT_APP_NAME)
                && jsonNode.has(HubConstants.AGENT_TOP_TYPE)
                && jsonNode.has(HubConstants.AGENT_LABEL_LIST)) {
            if (jsonNode.has(HubConstants.AGENT_LOG_STREAM)) {
                return LogTypeEnum.AGENT_LOG;
            } else {
                return LogTypeEnum.AGENT_BASE_INFO;
            }
        }
        return LogTypeEnum.JSON;
    }

    private boolean isStandardLog(JsonNode jsonNode) {
        if (jsonNode.has(HubConstants.MEASURE) && jsonNode.has(HubConstants.MEASURE_TS)
                && jsonNode.has(HubConstants.MEASURE_TAG) && jsonNode.has(HubConstants.MEASURE_FIELD)) {
            return true;
        }
        return false;
    }

    private Map<String, Object> transferMeasure(JsonNode jsonNode) {
        Measure measure = mapper.convertValue(jsonNode, Measure.class);
        parseMapField(jsonNode, measure);
        return measure.toMap();
    }

    private Map<String, Object> transferMeasure(JsonNode jsonNode, Map<String, Object> commonMap) {
        Measure measure = mapper.convertValue(jsonNode, Measure.class);
        commonMap.forEach((key, value) -> {
            if (!HubConstants.NOT_PUT_MEASURE.contains(key)) {
                measure.getTag().putIfAbsent(key, value);
            }
        });
        Map<String, Object> lastMap = new HashMap<>();
        lastMap.putAll(measure.toMap());
        parseMapField(jsonNode, measure);
        return lastMap;
    }


    private Map<String, Object> transferJson(JsonNode jsonNode, Map<String, Object> commonMap) {
        Map<String, Object> resultMap = new HashMap<>();
        commonMap.forEach((key, value) -> {
            if (!HubConstants.NOT_PUT_JSON.contains(key)) {
                resultMap.put(key, value);
            }
        });
        //support infra json contain labelList
        Map<String, Object> labelMap = labelList(jsonNode.get(HubConstants.AGENT_LABEL_LIST));
        resultMap.putAll(labelMap);
        recursionParseJson(jsonNode, resultMap);
        return resultMap;
    }

    private Map<String, Object> transferJson(JsonNode jsonNode) {
        return transferJson(jsonNode, Maps.newHashMapWithExpectedSize(1));
    }

    private Map<String, Object> labelList(JsonNode jsonNode) {
        try {
            if (jsonNode == null || jsonNode instanceof NullNode) {
                return Maps.newHashMapWithExpectedSize(1);
            }
            return mapper.convertValue(jsonNode, new TypeReference<Map<String, Object>>() {
            });
        } catch (Exception e) {
            return Maps.newHashMapWithExpectedSize(1);
        }
    }

    private void parseMapField(JsonNode jsonNode, Measure measure) {
        JsonNode jn = jsonNode.get(HubConstants.STANDARD_MAP_FIELD);
        if (jn != null && !jn.isNull()) {
            Map<String, Object> mapField = Maps.newHashMap();
            recursionParseJson(jn, mapField);
            measure.setMapField(mapField);
        }
    }

    private void recursionParseJson(JsonNode jsonNode, Map<String, Object> resultMap) {
        Map<String, Object> map = mapper.convertValue(jsonNode, new TypeReference<Map<String, Object>>() {
        });
        map.forEach((key, value) -> {
            if (value instanceof Map) {
                Map<String, Object> mmm = mapper.convertValue(value, new TypeReference<Map<String, Object>>() {
                });
                recursion(key, mmm, resultMap);
            } else if (value instanceof List) {
                try {
                    resultMap.put(key, mapper.writeValueAsString(value));
                } catch (JsonProcessingException e) {
                    log.error("recursion write value as string error", e);
                }
            } else {
                resultMap.put(key, value);
            }
        });
    }

    private void recursion(String prefixKey, Map<String, Object> map, Map<String, Object> resultMap) {
        map.forEach((key, value) -> {
            if (value instanceof Map) {
                Map<String, Object> mmm = mapper.convertValue(value, new TypeReference<Map<String, Object>>() {
                });
                recursion(prefixKey + "." + key, mmm, resultMap);
            } else if (value instanceof List) {
                try {
                    resultMap.put(prefixKey + "." + key, mapper.writeValueAsString(value));
                } catch (JsonProcessingException e) {
                    log.error("recursion write value as string error", e);
                }
            } else {
                resultMap.put(prefixKey + "." + key, value);
            }
        });
    }

    private Map<String, Object> agentBaseInfoMap(LogTypeEnum logTypeEnum, JsonNode jsonNode) {
        Map<String, Object> baseInfoMap = new HashMap<>();
        switch (logTypeEnum) {
            case AGENT_LOG: {
                baseInfoMap.putAll(basicMap(jsonNode));
                baseInfoMap.putAll(labelList(jsonNode.get(HubConstants.AGENT_LABEL_LIST)));
                break;
            }
            case AGENT_BASE_INFO: {
                baseInfoMap.putAll(basicInfoMap(jsonNode));
                baseInfoMap.putAll(labelList(jsonNode.get(HubConstants.AGENT_LABEL_LIST)));
                break;
            }
        }
        return baseInfoMap;
    }

    private Map<String, Object> basicInfoMap(JsonNode jsonNode) {
        Map<String, Object> map = new HashMap<>(13);
        setCommonLongField(map, HubConstants.COMMON_FIELD_TS, HubConstants.AGENT_TS, jsonNode);
        setCommonStringField(map, HubConstants.COMMON_FIELD_APP_NAME, HubConstants.AGENT_APP_NAME, jsonNode);
        setCommonStringField(map, HubConstants.COMMON_FIELD_TOP_TYPE, HubConstants.AGENT_TOP_TYPE, jsonNode);
        setCommonStringField(map, HubConstants.COMMON_FIELD_HOST, HubConstants.AGENT_HOST, jsonNode);
        setCommonStringField(map, HubConstants.COMMON_FIELD_IP, HubConstants.AGENT_IP, jsonNode);
        setCommonStringField(map, HubConstants.COMMON_FIELD_CLUSTER_ID, HubConstants.AGENT_CLUSTER_ID, jsonNode);
        setCommonStringField(map, HubConstants.COMMON_FIELD_REGION_ID, HubConstants.AGENT_REGION_ID, jsonNode);
        setCommonStringField(map, HubConstants.COMMON_FIELD_ZONE_NAME, HubConstants.AGENT_ZONE_NAME, jsonNode);
        setCommonStringField(map, HubConstants.COMMON_FIELD_INSTANCE_ID, HubConstants.AGENT_INSTANCE_ID, jsonNode);
        setCommonStringField(map, HubConstants.COMMON_FIELD_IP_PUBLIC, HubConstants.AGENT_IP_PUBLIC, jsonNode);
        setCommonStringField(map, HubConstants.COMMON_FIELD_INFRA_TYPE, HubConstants.AGENT_INFRA_TYPE, jsonNode);
        setCommonLongField(map, HubConstants.COMMON_FIELD_IS_K8S, HubConstants.AGENT_IS_K8S, jsonNode);
        setCommonStringField(map, HubConstants.COMMON_FIELD_CSP, HubConstants.AGENT_CSP, jsonNode);
        setCommonStringField(map, HubConstants.COMMON_FIELD_CELL, HubConstants.AGENT_CELL, jsonNode);
        setCommonStringField(map, HubConstants.COMMON_FIELD_STACK, HubConstants.AGENT_STACK, jsonNode);
        setCommonStringField(map, HubConstants.COMMON_FIELD_NODE_NAME, HubConstants.AGENT_NODE_NAME, jsonNode);
        setCommonStringField(map, HubConstants.COMMON_FIELD_NAMESPACE, HubConstants.AGENT_NAMESPACE, jsonNode);
        setCommonStringField(map, HubConstants.COMMON_FIELD_POD_NAME, HubConstants.AGENT_POD_NAME, jsonNode);
        setCommonStringField(map, HubConstants.COMMON_FIELD_CONTAINER_NAME, HubConstants.AGENT_CONTAINER_NAME, jsonNode);
        setCommonStringField(map, HubConstants.COMMON_FIELD_CONTAINER_ID, HubConstants.AGENT_CONTAINER_ID, jsonNode);
        return map;
    }

    private Map<String, Object> basicMap(JsonNode jsonNode) {
        Map<String, Object> map = new HashMap<>(9);
        //clusterId、instanceId、hostName、regionId
        if (jsonNode.get(HubConstants.AGENT_LOG_STREAM) != null) {
            String[] basic = jsonNode.get(HubConstants.AGENT_LOG_STREAM).asText().split("_");
            if (basic.length > 2) {
                map.put(HubConstants.COMMON_FIELD_INSTANCE_ID, basic[1]);
                map.put(HubConstants.COMMON_FIELD_HOST_NAME, basic[2]);
            }
            if (basic.length > 3) {
                map.put(HubConstants.COMMON_FIELD_REGION_ID, basic[3]);
            }
        }
        setCommonStringField(map, HubConstants.COMMON_FIELD_CLUSTER_ID, HubConstants.AGENT_APP_CLUSTER, jsonNode);
        setCommonLongField(map, HubConstants.COMMON_FIELD_TS, HubConstants.AGENT_TIMESTAMP, jsonNode);
        setCommonStringField(map, HubConstants.COMMON_FIELD_APP_NAME, HubConstants.AGENT_APP_NAME, jsonNode);
        setCommonStringField(map, HubConstants.COMMON_FIELD_TOP_TYPE, HubConstants.AGENT_TOP_TYPE, jsonNode);
        setCommonStringField(map, HubConstants.COMMON_FIELD_PATH_TEMPLATE, HubConstants.AGENT_PATH_TEMPLATE, jsonNode);
        return map;
    }

    private void setCommonStringField(Map<String, Object> map, String targetKey, String sourceKey, JsonNode field) {
        if (field.get(sourceKey) != null) {
            map.put(targetKey, field.get(sourceKey).asText());
        }
    }

    private void setCommonLongField(Map<String, Object> map, String targetKey, String sourceKey, JsonNode field) {
        if (field.get(sourceKey) != null) {
            map.put(targetKey, field.get(sourceKey).asLong());
        }
    }

    private void processAgentMessageField(String msg, List<Map<String, Object>> results, Map<String, Object> commonMap) {
        JsonNode sonJsonNode = null;
        try {
            sonJsonNode = mapper.readValue(msg, JsonNode.class);
        } catch (JsonProcessingException e) {
            log.error("processAgentMessage error", e);
            return;
        }
        if (sonJsonNode instanceof ArrayNode) {
            Iterator<JsonNode> iterator = sonJsonNode.iterator();
            while (iterator.hasNext()) {
                JsonNode jn = iterator.next();
                results.add(isStandardLog(jn) ?
                        transferMeasure(jn, commonMap) :
                        transferJson(jn, commonMap));
            }
        } else {
            results.add(isStandardLog(sonJsonNode) ?
                    transferMeasure(sonJsonNode, commonMap) :
                    transferJson(sonJsonNode, commonMap));
        }
    }
}
