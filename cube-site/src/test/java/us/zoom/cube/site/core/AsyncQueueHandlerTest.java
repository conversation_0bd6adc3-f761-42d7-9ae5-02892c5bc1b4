package us.zoom.cube.site.core;


import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import us.zoom.cube.lib.common.AuthEncrTypeEnum;
import us.zoom.cube.site.CubeSiteApplicationTests;
import us.zoom.cube.site.infra.utils.IdUtils;
import us.zoom.infra.dao.model.AsyncQueueDO;
import us.zoom.infra.thread.ThreadLocalStore;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2020/8/5 6:27 PM
 */
public class AsyncQueueHandlerTest extends CubeSiteApplicationTests {

    private static final String name = "AsyncQueue_test_name";
    private static final String asyncQueueGroupId = IdUtils.generateId();

    @Autowired
    private AsyncQueueHandler asyncQueueHandler;

    @BeforeEach
    public void init(){
        ThreadLocalStore.setTenantInfoLocal("d8021646-7227-4934-896c-d8a603dcc7f0");
        ThreadLocalStore.setUserInfoLocal("afa89f51-48df-48de-9d3c-acfcaab2e0f7");
    }

    @Test
    public void testEditBasic() throws Exception {
        AsyncQueueDO asyncQueueDO = buildAsyncQueue(asyncQueueGroupId);
        asyncQueueHandler.addAq(asyncQueueDO);
        asyncQueueDO.setClusterId("c_new");
        asyncQueueDO.setRegionId("r_new");
        asyncQueueDO.setTopics("t_new");
        asyncQueueDO.setPollSize(234);
        asyncQueueDO.setThreadCount(34);
        asyncQueueDO.setSessionTimeOut(40000);
        asyncQueueDO.setGroupId("G-new");
        asyncQueueHandler.editAqBasic(asyncQueueDO);

        AsyncQueueDO updateOne =  asyncQueueHandler.getAqById(asyncQueueDO.getId());
        Assertions.assertTrue(updateOne.getClusterId().equals("c_new"));
        Assertions.assertTrue(updateOne.getRegionId().equals("r_new"));
        Assertions.assertTrue(updateOne.getPollSize().equals(234));
        Assertions.assertTrue(updateOne.getThreadCount().equals(34));
        Assertions.assertTrue(updateOne.getSessionTimeOut().equals(40000));
        Assertions.assertTrue(updateOne.getGroupId().equals("G-new"));
        Assertions.assertTrue(updateOne.getTopics().equals("t_new"));

        asyncQueueHandler.deleteById(asyncQueueDO.getId());

    }

    @Test
    public void testEdit() throws Exception {
        AsyncQueueDO asyncQueueDO=new AsyncQueueDO();
        asyncQueueDO.setId("4432a5c3-3d42-456f-954d-d109093d27e5");
        asyncQueueDO.setRegionId("HZ");
        asyncQueueDO.setClusterId("dev");
        asyncQueueDO.setKeyPwd("ssl1234");
        asyncQueueDO.setJaasConfig("");
        asyncQueueDO.setIdenAlgorithm("");
//        asyncQueueDO.setKeystorePath("/home/<USER>/kafka.keystore.jks");
//        asyncQueueDO.setKeystorePwd("ssl1234");
        asyncQueueDO.setTruststorePath("/home/<USER>/kafka.truststore.jks");
        asyncQueueDO.setAsyncQueueGroupId("c5efda8b-0bd1-4e1d-9221-d341626414a0");
        asyncQueueDO.setTruststorePwd("ssl1234");
        asyncQueueDO.setAuthEncrType(1);
        asyncQueueDO.setGroupId("press_no_ssl_consummer");
        asyncQueueDO.setPollSize(100);
        asyncQueueDO.setServers("10.100.112.103:9093");
        asyncQueueDO.setSessionTimeOut(30000);
        asyncQueueDO.setThreadCount(40);
        asyncQueueDO.setTopics("press_test_1_10,press_test_1_deva,press_test_1_devb,press_test_1_devd");
        asyncQueueHandler.editAq(asyncQueueDO);

    }

    @Test
    public void testAllCases() throws Exception {
        AsyncQueueDO asyncQueueDO = buildAsyncQueue(asyncQueueGroupId);
        asyncQueueHandler.addAq(asyncQueueDO);

        boolean hasSameName = asyncQueueHandler.hasSameName(name, asyncQueueGroupId);
        Assertions.assertTrue(hasSameName);

        List<AsyncQueueDO> list = asyncQueueHandler.findByNameLike(asyncQueueDO.getAsyncQueueGroupId(), "test", 1, 5);
        Assertions.assertTrue(list.size() >= 1);
        int count = asyncQueueHandler.getCountByNameLike(asyncQueueDO.getAsyncQueueGroupId(), "test");
        Assertions.assertTrue(count >= 1);

        asyncQueueHandler.deleteById(asyncQueueDO.getId());
    }

    private AsyncQueueDO buildAsyncQueue(String asyncQueueGroupId) {
        AsyncQueueDO asyncQueueDO = new AsyncQueueDO();
        asyncQueueDO.setId(IdUtils.generateId());
        asyncQueueDO.setAsyncQueueGroupId(asyncQueueGroupId);
        asyncQueueDO.setName(name);
        asyncQueueDO.setClusterId("clusterId_test");
        asyncQueueDO.setRegionId("regionId_test");
        asyncQueueDO.setServers("ec2-50-16-132-120.compute-1.amazonaws.com:9093,ec2-3-86-37-86.compute-1.amazonaws.com:9093,ec2-34-227-170-219.compute-1.amazonaws.com:9093");
        asyncQueueDO.setGroupId("hub-test");
        asyncQueueDO.setTopics("web_monitor");
        asyncQueueDO.setPollSize(200);
        asyncQueueDO.setThreadCount(10);
        asyncQueueDO.setSessionTimeOut(50000);
        asyncQueueDO.setGmtCreate(new Date());
        asyncQueueDO.setGmtModify(new Date());
        asyncQueueDO.setAuthEncrType(AuthEncrTypeEnum.no.getValue());
        return asyncQueueDO;
    }
}
