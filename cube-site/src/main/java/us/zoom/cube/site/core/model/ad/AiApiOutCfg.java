package us.zoom.cube.site.core.model.ad;

import com.zoom.op.monitor.domain.alarm.AlarmSourceTypeEnum;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-02-02 10:04
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AiApiOutCfg {
    private String metricsId;
    private String metricsName;
    private Integer aggPeriod;
    private Integer metricsType;
    private String tenantId;
    private String tenantName;
    private Integer extraDelayMin = 5;
    private String dataSourceType = AlarmSourceTypeEnum.DEFAULT.name();
    private Boolean piiStatus;
    private List<AiApiCfg> aiApiCfgList;
    private List<AiAlarmCfg> aiAlarmCfgList;
}