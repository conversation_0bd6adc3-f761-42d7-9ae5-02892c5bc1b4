package us.zoom.cube.site.core;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import us.zoom.cube.site.infra.utils.IdUtils;
import us.zoom.infra.dao.model.EnvironmentTaskRelaDO;
import us.zoom.infra.dao.service.EnvironmentTaskRelaDAO;

import java.util.List;

/**
 * @Author: <PERSON>
 * @ModuleOwner: <PERSON>
 * @Date:08/17/2022 10:58
 * @Description:
 */
@Service
public class EnvironmentTaskRelaHandler {
    @Autowired
    private EnvironmentTaskRelaDAO environmentTaskRelaDAO;

    public boolean addOrUpdate(String taskQueueId, List<EnvironmentTaskRelaDO> queueCluster) {
        environmentTaskRelaDAO.deleteByTaskQueueId(taskQueueId);

        for (EnvironmentTaskRelaDO rela : queueCluster) {
            rela.setId(IdUtils.generateId());
            rela.setTaskQueueId(taskQueueId);
            environmentTaskRelaDAO.add(rela);
        }

        return true;
    }

    public void deleteByTaskQueueId(String taskQueueId){
        environmentTaskRelaDAO.deleteByTaskQueueId(taskQueueId);
    }

    public List<EnvironmentTaskRelaDO> getByTaskQueueId(String taskQueueId){
        return environmentTaskRelaDAO.getByTaskQueueId(taskQueueId);
    }
}
