package us.zoom.cube.site.biz.alarm;


import lombok.Data;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class AlarmBoardCheckServiceImplTest {

    @Test
    public void testRepeat() {
        List<St> list = new ArrayList<>();
        St st1 = new St();
        st1.setName("tang");
        list.add(st1);
        St st2 = new St();
        st2.setName("tang");
        list.add(st2);

        List<String> a = list.stream().map(St::getName).distinct().collect(Collectors.toList());
        Assertions.assertEquals("tang", a.get(0));
    }

    @Data
    public static class St{
        private String name;
    }

}