package us.zoom.cube.site.lib.input.dataparser;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import us.zoom.cube.lib.config.hub.enums.SplitFieldTypeEnum;
import us.zoom.cube.lib.config.hub.processor.SplitEntryCfg;
import us.zoom.cube.lib.config.hub.processor.SplitSchema;
import us.zoom.cube.lib.utils.JsonUtils;

import java.util.Arrays;
import java.util.List;

/**
 * @Author: <EMAIL>
 * @ModuleOwner: <EMAIL>
 * @Date: 04/10/2025 15:42
 * @Description:
 */
public class DataParserSplitProcessorInputTest {
    @Test
    public void test() {
        DataParserSplitProcessorInput input = new DataParserSplitProcessorInput();
        RuntimeException err = Assertions.assertThrows(RuntimeException.class, () -> {
            input.check();
        });
        Assertions.assertEquals("Source Field cannot be empty!", err.getMessage());

        input.setSourceField("hostname");
        RuntimeException err1 = Assertions.assertThrows(RuntimeException.class, () -> {
            input.check();
        });
        Assertions.assertEquals("Split config cannot be empty!", err1.getMessage());

        List<SplitEntryCfg> entries = Arrays.asList(new SplitEntryCfg(1, "host", SplitFieldTypeEnum.string),
                new SplitEntryCfg(1, "name", SplitFieldTypeEnum.string));
        SplitSchema schema = new SplitSchema("", entries);
        input.setSchema(schema);
        input.setSourceField("hostname");
        RuntimeException err3 = Assertions.assertThrows(RuntimeException.class, () -> {
            input.check();
        });
        Assertions.assertEquals("Separator cannot be empty!", err3.getMessage());

        SplitSchema schema1 = new SplitSchema("name", null);
        input.setSchema(schema1);
        input.setSourceField("hostname");
        RuntimeException err4 = Assertions.assertThrows(RuntimeException.class, () -> {
            input.check();
        });
        Assertions.assertEquals("Split field cannot be empty!", err4.getMessage());
    }

    @Test
    public void testJson() {
        SplitSchema schema = new SplitSchema("name",
                Arrays.asList(new SplitEntryCfg(1, "host", SplitFieldTypeEnum.string),
                        new SplitEntryCfg(2, "name", SplitFieldTypeEnum.string)));
        System.out.println(JsonUtils.toJsonString(schema));
    }
}
