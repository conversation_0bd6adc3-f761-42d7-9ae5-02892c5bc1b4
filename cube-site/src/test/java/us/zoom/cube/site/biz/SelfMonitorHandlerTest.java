package us.zoom.cube.site.biz;

import com.google.common.collect.Maps;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.DefaultHttpClient;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import us.zoom.cube.site.core.monitor.SelfMonitorHandler;
import us.zoom.infra.model.alarm.AlarmLevel;
import us.zoom.cube.lib.utils.JsonUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class SelfMonitorHandlerTest extends BaseTest{

    @Autowired
    private SelfMonitorHandler selfMonitorHandler;
    @Test
    public void test(){
//        selfMonitorHandler.sendMsg("hello");
        selfMonitorHandler.send("https://zoomus01sx01cube.zoomsvr.com/influxdbQuerier","FATAL","cpu","cpu_alarm", AlarmLevel.FATAL);
        selfMonitorHandler.send("https://zoomus01sx01cube.zoomsvr.com/influxdbQuerier","ERROR","cpu","cpu_alarm", AlarmLevel.ERROR);
        selfMonitorHandler.send(null,"WARN",null,null, null);

    }

    public static void main(String []gs){
        Map<String, Object> alarmInfo = Maps.newHashMap();
        Map<String, Object> content = Maps.newHashMap();
        Map<String, Object> head = setHead( "jvm ");
        List<Map> body = new ArrayList<>();
        Map<String,Object> record=new HashMap<>();
        Map<String,Object> style=new HashMap<>();
        style.put("color","#8338EC");
        style.put("bold",true);
        style.put("italic",false);
        record.put("type","message");
        record.put("text","");
        record.put("style", style);

        body.add(record);
        record=new HashMap<>();
        record.put("type","select");
        record.put("text","");
        List<Map<String,String>> alarmOptions=new ArrayList<>(2);


//        body.add(record);
        content.put("body", body);
        content.put("head",head);
        alarmInfo.put("content", content);
//        alarmInfo.put("is_markdown_support",true);
        HttpPost post = new HttpPost("https://inbots.zoom.us/incoming/hook/LPL6Oun5iAvT7Mg0_BWp_MeY?format=full");
        try {
            StringEntity stringEntity = new StringEntity(JsonUtils.toJsonString(alarmInfo), "UTF-8");
            stringEntity.setContentType("application/json;charset=utf-8");
            post.setEntity(stringEntity);

            long startTime = System.currentTimeMillis();
            CloseableHttpResponse result = new DefaultHttpClient().execute(post);
            result.toString();
            long endTime = System.currentTimeMillis();
        } catch (Exception e) {
            e.printStackTrace();
        }  finally {
            post.releaseConnection();
        }
    }

    private static Map<String, Object> setHead(String headTxt) {
        Map<String, Object> head = new HashMap<>();
        Map<String,Object> style=new HashMap<>();
        style.put("color","#330000");
        style.put("bold",true);
        style.put("italic",false);
        head.put("style", style);
        head.put("text", headTxt);
        return head;
    }
}
