package us.zoom.cube.site.lib.output.template;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;

/**
 * @Author: <PERSON>
 * @Date:12/20/2022 16:41
 * @Description:
 */
@Data
public class TemplateUserViewOutput {

    private String id;

    private String name;

    private List<Item> showItemList;

    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class Item {

        private String type;

        private List<String> metricsNameList;

        private List<String> alarmNameList;

        private List<String> dashboardNameList;

    }

}
