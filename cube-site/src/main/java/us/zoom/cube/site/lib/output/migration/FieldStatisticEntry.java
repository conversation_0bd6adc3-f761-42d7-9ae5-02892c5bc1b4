package us.zoom.cube.site.lib.output.migration;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: <PERSON><PERSON> Ding
 * @date: 02/08/2025 07:18
 * @desc:
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FieldStatisticEntry {

    //sum or count
    private String function;

    private String sourceField;

    private double sourceValue;

    private String targetField;

    private double targetValue;

    private String fieldType;

}
