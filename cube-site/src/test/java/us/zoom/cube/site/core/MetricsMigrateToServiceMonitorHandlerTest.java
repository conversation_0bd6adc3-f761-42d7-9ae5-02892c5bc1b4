package us.zoom.cube.site.core;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import us.zoom.cube.site.CubeSiteApplicationTests;
import us.zoom.cube.site.core.maintaince.MetricsMigrateToServiceMonitorHandler;
import us.zoom.cube.site.lib.input.MetricsMigrationInput;
import us.zoom.infra.thread.ThreadLocalStore;
import us.zoom.cube.lib.utils.JsonUtils;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2020/7/29 2:04 PM
 */
public class MetricsMigrateToServiceMonitorHandlerTest extends CubeSiteApplicationTests {


    @Autowired
    private MetricsMigrateToServiceMonitorHandler metricsMigrateToServiceMonitorHandler;

    @Test
    public void test(){
        MetricsMigrationInput metricsMigrationInput = new MetricsMigrationInput();
        metricsMigrationInput.setDestServiceName("Migrate_To_Xmpp_From_serviceMonitor");
        ThreadLocalStore.setTenantInfoLocal("237d906a-7307-464f-b3fd-e43f59ff4063");
        ThreadLocalStore.setUserInfoLocal("2269c62a-67c3-42c4-89cf-f64cf61d442e");
        metricsMigrationInput.setNewMetricsCmpPrefix("XMPP_XMS_");
        metricsMigrationInput.setSrcMetricsNameFullMatch(Arrays.asList("XMPP_XMS.XMS.TC100msRedisCall.count","XMPP_XMS.MemoryUsage.Node","XMPP_XMS.xms.url.calls.latency"));
        List<String> result = metricsMigrateToServiceMonitorHandler.migrateFromServiceMonitor(metricsMigrationInput);
        System.out.println(JsonUtils.toJsonString(result));
    }
}
