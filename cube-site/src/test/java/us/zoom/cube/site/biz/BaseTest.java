package us.zoom.cube.site.biz;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import us.zoom.cube.lib.common.RoleTypeEnum;
import us.zoom.cube.site.CubeSiteApplication;
import us.zoom.cube.site.CubeSummary;
import us.zoom.cube.site.core.TenantHandler;
import us.zoom.cube.site.core.UserHandler;
import us.zoom.cube.site.core.auth.*;
import us.zoom.cube.site.infra.utils.IdUtils;
import us.zoom.cube.site.mock.DataMock;
import us.zoom.infra.dao.model.*;
import us.zoom.infra.thread.ThreadLocalStore;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@SpringBootTest(classes = CubeSiteApplication.class,webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)//springboot
public class BaseTest {
    @Autowired
    TenantService tenantService;
    @Autowired
    UserService userService;
    @Autowired
    CollectorService collectorService;

    @Autowired
    UserRoleHandler userRoleHandler;
    @Autowired
    UserHandler userHandler;

    @Autowired
    MenuHandler menuHandler;

    @Autowired
    AlarmService alarmService;

    @Autowired
    RoleMenuRelaHandler roleMenuRelaHandler;

    @Autowired
    TenantUserRelaHandler tenantUserRelaHandler;

    @Autowired
    TenantHandler tenantHandler;

    @Autowired
    AuthHandler authHandler;


    public static  final String TENANT=DataMock.TENANT_NAME;
    public static  final String ADMIN_ROLE="admin_role";
    public static  final String OP_ROLE="test_opOwner";
    public static  final String SERVICE_OWNER="test_applicationOwners";
    public static  final String USER="user1";
    public static  final String ADMIN_USER="admin_user";
    public static  final String OP_USER="op_user";
    public static  final String SERVICE_USER="service_user";
    public static  final String TOPIC="topic";
    public static  final String COLLECTOR="collector";
    public static final String PATTERN="pattern";
    public static  final String AGG="agg";
    public static  final String ALARM="alarm";


    @BeforeEach
    public void init(){
        assighAuth();
    }
    public CubeSummary buildSummary(String phase) throws Exception {
        CubeSummary summary=new CubeSummary();
        TenantDO tenantDO=null;
        UserDO userDO=null;
        TopicDO topicDO=null;
        CollectorDO collectorDO=null;
        AlarmDO alarmDO=null;
        if(TENANT.equals(phase)){
            tenantDO=getTenant();
        }else if(TOPIC.equals(phase)){
            tenantDO=getTenant();
            userDO=getUser();
//            assginUser(tenantDO.getId(),userDO.getId());
        }
        summary.setTenantId(tenantDO != null ? tenantDO.getId():null);
        summary.setUserId(userDO != null ? userDO.getId():null);
        summary.setTopicId(topicDO != null ? topicDO.getId():null);
        summary.setCollectorId(topicDO != null ? topicDO.getId():null);
        summary.setCollectorId(collectorDO != null ? collectorDO.getId() : null);
        summary.setAlarmId( alarmDO != null ? alarmDO.getId() : null);
        return summary;
    }


    public TenantDO getTenant(){
        TenantDO tenantDO= tenantService.getTenantByName(DataMock.TENANT_NAME);
        if(null == tenantDO){
            TenantDO tenantDO1=DataMock.mockTenant();
            tenantService.addTenant(tenantDO1);
            tenantDO= tenantService.getTenantByName(DataMock.TENANT_NAME);
            Assertions.assertNotNull(tenantDO);
        }
        return tenantDO;
    }

    public UserDO getUser(){
        UserDO userDO= userService.getUserByName(DataMock.USER_NAME);
        if(userDO ==null){
//            userService.addUser(DataMock.mockUser());
            userDO= userService.getUserByName(DataMock.USER_NAME);
        }
        return userDO;
    }


//    public boolean assginUser(String tenantId,String userId){
//        boolean success= tenantService.assignUser(tenantId, Arrays.asList(userId));
//        Assertions.assertTrue(success);
//        return success;
//    }

    public boolean isFinishedAuth = false;

    public void assighAuth(){
        if(isFinishedAuth){
            return;
        }
        TenantDO testTenant = getTenant();

        createAllRole();
        UserDO admin= userService.getUserByName(ADMIN_USER);
        if(null == admin){
            admin =  userHandler.addUser(ADMIN_USER,RoleTypeEnum.normal.name());
        }

        UserDO op = userService.getUserByName(OP_USER);
        if(null == op){
            op =  userHandler.addUser(OP_USER,RoleTypeEnum.normal.name());
        }

        UserDO serviceOwner = userService.getUserByName(OP_USER);
        if(null == serviceOwner){
            serviceOwner =  userHandler.addUser(SERVICE_OWNER, RoleTypeEnum.normal.name());
        }
        List<MenuItemDO> allMenus = menuHandler.listAllMenu();
        //assgin admin
        List<RoleMenuRelaDO> roleMenuRelas = allMenus.stream().map(item->{
            RoleMenuRelaDO rela = new RoleMenuRelaDO();
            rela.setRoleName(ADMIN_ROLE);
            rela.setId(IdUtils.generateId());
            rela.setMenuResId(item.getResourceId());
            return rela;
        }).collect(Collectors.toList());
        roleMenuRelaHandler.assignMenu(roleMenuRelas);


        roleMenuRelas = allMenus.stream().map(item->{
            RoleMenuRelaDO rela = new RoleMenuRelaDO();
            rela.setRoleName(OP_ROLE);
            rela.setId(IdUtils.generateId());
            rela.setMenuResId(item.getResourceId());
            return rela;
        }).collect(Collectors.toList());
        roleMenuRelaHandler.assignMenu(roleMenuRelas);


        roleMenuRelas = allMenus.stream().map(item->{
            RoleMenuRelaDO rela = new RoleMenuRelaDO();
            rela.setRoleName(SERVICE_OWNER);
            rela.setId(IdUtils.generateId());
            rela.setMenuResId(item.getResourceId());
            return rela;
        }).collect(Collectors.toList());
        roleMenuRelaHandler.assignMenu(roleMenuRelas);

        List<TenantUserRelaDO> relationDOList = new ArrayList<>();
        TenantUserRelaDO tenantUserRelaDO = new TenantUserRelaDO();
        tenantUserRelaDO.setTenantId(testTenant.getId());
        tenantUserRelaDO.setRole(ADMIN_ROLE);
        tenantUserRelaDO.setUserId(admin.getId());
        tenantUserRelaDO.setId(IdUtils.generateId());
        relationDOList.add(tenantUserRelaDO);

        tenantUserRelaDO = new TenantUserRelaDO();
        tenantUserRelaDO.setTenantId(testTenant.getId());
        tenantUserRelaDO.setRole(OP_ROLE);
        tenantUserRelaDO.setUserId(op.getId());
        tenantUserRelaDO.setId(IdUtils.generateId());
        relationDOList.add(tenantUserRelaDO);

        tenantUserRelaDO = new TenantUserRelaDO();
        tenantUserRelaDO.setTenantId(testTenant.getId());
        tenantUserRelaDO.setRole(SERVICE_OWNER);
        tenantUserRelaDO.setUserId(serviceOwner.getId());
        tenantUserRelaDO.setId(IdUtils.generateId());
        relationDOList.add(tenantUserRelaDO);
        tenantHandler.addTenantAll(relationDOList);

        authHandler.loadAllUserRoleMenuCache();
        isFinishedAuth = true;
    }


    public void createAllRole(){
        UserRoleDO userRoleDO = userRoleHandler.getRoleByName(ADMIN_ROLE);
        if(null == userRoleDO){
            userRoleDO = new UserRoleDO();
            userRoleDO.setRole(ADMIN_ROLE);
            userRoleDO.setPiiAccess(true);
            userRoleDO.setCrossService(true);
            userRoleDO.setCanOperate(true);
            userRoleDO.setId(IdUtils.generateId());
            userRoleHandler.addRole(userRoleDO);
        }
        userRoleDO = userRoleHandler.getRoleByName(OP_ROLE);
        if(null == userRoleDO){
            userRoleDO = new UserRoleDO();
            userRoleDO.setRole(OP_ROLE);
            userRoleDO.setPiiAccess(true);
            userRoleDO.setCanOperate(true);
            userRoleDO.setCrossService(false);
            userRoleDO.setId(IdUtils.generateId());
            userRoleHandler.addRole(userRoleDO);
        }

        userRoleDO = userRoleHandler.getRoleByName(SERVICE_OWNER);
        if(null == userRoleDO){
            userRoleDO = new UserRoleDO();
            userRoleDO.setRole(SERVICE_OWNER);
            userRoleDO.setPiiAccess(false);
            userRoleDO.setCrossService(false);
            userRoleDO.setId(IdUtils.generateId());
            userRoleDO.setCanOperate(true);
            userRoleHandler.addRole(userRoleDO);
        }

    }


    public String switchToAdmin(){
        TenantDO tenantDO = tenantHandler.getTenantByName(TENANT);
        UserDO admin = userHandler.getUserByName(ADMIN_USER);
//        UserDO op = userHandler.getUserByName(OP_USER);
//        UserDO serviceOwner = userHandler.getUserByName(SERVICE_OWNER);
        ThreadLocalStore.setUserInfoLocal(admin.getId());
        ThreadLocalStore.setTenantInfoLocal(tenantDO.getId());
        return admin.getId();
    }

    public String  switchToOP(){
        TenantDO tenantDO = tenantHandler.getTenantByName(TENANT);
//        UserDO admin = userHandler.getUserByName(ADMIN_USER);
        UserDO op = userHandler.getUserByName(OP_USER);
//        UserDO serviceOwner = userHandler.getUserByName(SERVICE_OWNER);
        ThreadLocalStore.setUserInfoLocal(op.getId());
        ThreadLocalStore.setTenantInfoLocal(tenantDO.getId());
        return op.getId();
    }

    public void switchToServiceOwner(){
        TenantDO tenantDO = tenantHandler.getTenantByName(TENANT);
//        UserDO admin = userHandler.getUserByName(ADMIN_USER);
//        UserDO op = userHandler.getUserByName(OP_USER);
        UserDO serviceOwner = userHandler.getUserByName(SERVICE_OWNER);
        ThreadLocalStore.setUserInfoLocal(serviceOwner.getId());
        ThreadLocalStore.setTenantInfoLocal(tenantDO.getId());
    }

    public void switchApiPath(String apiPath){
        ThreadLocalStore.setApiPath(apiPath);
    }


}
