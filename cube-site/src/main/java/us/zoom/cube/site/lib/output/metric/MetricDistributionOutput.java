package us.zoom.cube.site.lib.output.metric;

import lombok.Data;

/**
 * Output object describing the distribution statistics of a metric.
 * Includes properties such as max, min, average, median, count, sparsity, etc.
 * Suitable for data analysis, reporting, and similar scenarios.
 *
 * @author: canyon.li
 * @date: 2025/06/24
 **/
@Data
public class MetricDistributionOutput {

    /** Unique identifier for the metric */
    private String metricId;

    /** Name of the metric */
    private String metricName;

    /** Maximum value of the metric */
    private Double max;

    /** Minimum value of the metric */
    private Double min;

    /** Average value of the metric */
    private Double avg;

    /** Median value of the metric */
    private Double mid;

    /** Number of samples for the metric */
    private Long count;

    /** Sparsity of the metric (0 means no sparsity, 1 means fully sparse) */
    private Double sparsity;

    /** Threshold for sparsity, used to determine if the metric is sparse */
    private Double sparsityThreshold;

    /** Frequency of the metric occurrence */
    private Integer period;
}
