package us.zoom.cube.site.biz;

import org.apache.commons.collections.CollectionUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import us.zoom.cube.site.CubeSiteApplication;
import us.zoom.cube.site.core.auth.AuthHandler;
import us.zoom.cube.site.lib.BasePara;
import us.zoom.infra.dao.model.MenuItemDO;
import us.zoom.infra.dao.model.RoleMenuRelaDO;
import us.zoom.infra.dao.model.TenantDO;
import us.zoom.infra.dao.model.UserRoleDO;
import us.zoom.infra.dao.service.MenuItemDAO;
import us.zoom.infra.dao.service.RoleMenuRelaDAO;
import us.zoom.infra.dao.service.UserRoleDAO;
import us.zoom.infra.model.menu.MenuTree;
import us.zoom.infra.thread.ThreadLocalStore;
import us.zoom.infra.utils.Instance;

import java.util.Arrays;
import java.util.List;

@SpringBootTest(classes = CubeSiteApplication.class,webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)//springboot
public class AuthHandlerTest extends  BaseTest {

    @Autowired
    AuthHandler authHandler;
    @Autowired
    MenuItemDAO menuItemDAO;

    @Autowired
    UserRoleDAO userRoleDAO;

    @Autowired
    RoleMenuRelaDAO roleMenuRelaDAO;

    @Test
    public void test(){
        String adminUserId  = super.switchToAdmin();
        List<TenantDO> tenantDOS = authHandler.getServiceUserCanOperate(adminUserId);
        Assertions.assertTrue(CollectionUtils.isNotEmpty(tenantDOS), "fail");
        String opUserId  = super.switchToOP();
        tenantDOS = authHandler.getServiceUserCanOperate(opUserId);
        Assertions.assertTrue(CollectionUtils.isEmpty(tenantDOS), "fail");

    }

    @Test
    public void listAuthTree(){
        ThreadLocalStore.setUserInfoLocal("afa89f51-48df-48de-9d3c-acfcaab2e0f7");
        ThreadLocalStore.setTenantInfoLocal("1f8cacb9-8769-481f-8ad8-443f18799fa2");
        BasePara basePara = new BasePara();
        MenuTree tree =  authHandler.getMenuTree(basePara);
        System.out.println(tree);
    }


    @Test
    public void migrate(){
        List<MenuItemDO> menuItemDOS = menuItemDAO.listAll();

//        private String resourceUrl;
//
//        private Boolean hidden;
//
//        private String parentResId;
//
//        private Integer menuOrder;
//
//        private Integer type;
//
//        private Date createTime;
//
//        private Date modifyTime;

        List<String> exportRoles = Arrays.asList("applicationGuestors","piiAdmin","applicationOwners","opOwner","admin_role","systemMaintainer");
        List<UserRoleDO> userRoleDOS =  userRoleDAO.listAll();

        Instance.ofNullable(userRoleDOS).forEach(roleDO -> {
            if(exportRoles.contains(roleDO.getRole())) {
                System.out.println("insert into user_role(id,role_desc,role,create_time,modify_time,cross_service,pii_access,can_operate)" +
                        "values('"+roleDO.getId()+"','"+roleDO.getRoleDesc()+"','"+roleDO.getRole()+"',now(),now(),"+(roleDO.getCrossService() != null && roleDO.getCrossService() ? 1 : 0)
                        +","+(roleDO.getPiiAccess() != null && roleDO.getPiiAccess() ? 1 : 0)+","+(roleDO.getCanOperate() != null && roleDO.getCanOperate() ? 1 : 0)+");");
            }
        });
        Instance.ofNullable(menuItemDOS).forEach(menuItemDO -> {
            System.out.println("insert into menu_item(id, name, resource_id, resource_url, hidden,menu_order,parent_res_id, type,create_time, modify_time) " +
                    "values('"+menuItemDO.getId()+"', '"+menuItemDO.getName()+"', '"+menuItemDO.getResourceId()+"'," +
                    " '"+menuItemDO.getResourceUrl()+"',"+menuItemDO.getHidden()+", "+menuItemDO.getMenuOrder()+"," +
                    "'"+menuItemDO.getParentResId()+"',"+menuItemDO.getType()+",now(), now());");
        });


        List<RoleMenuRelaDO> relas = roleMenuRelaDAO.listAll();
        Instance.ofNullable(relas).forEach(rela->{
            if(exportRoles.contains(rela.getRoleName())){
                System.out.println("insert into role_menu_rela(id, role_name, menu_res_id, create_time,  modify_time) " +
                        "values('"+rela.getId()+"', '"+rela.getRoleName()+"', '"+rela.getMenuResId()+"',now(), now());");
            }
        });
    }

    @Test
    public void getMenuTree(){
        BasePara basePara = new BasePara();
        ThreadLocalStore.setUserInfoLocal("82b4e894-edc7-47b8-ae99-58b6cbf58326");
        ThreadLocalStore.setTenantInfoLocal("1863ff76-8d25-44c7-b9de-a6c15fa27479");
        authHandler.getMenuTree(basePara);
    }
}
