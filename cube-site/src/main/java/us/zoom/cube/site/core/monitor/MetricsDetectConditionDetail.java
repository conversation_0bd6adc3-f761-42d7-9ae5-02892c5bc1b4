package us.zoom.cube.site.core.monitor;
import java.util.List;

/**
 * <AUTHOR>
 */
public class MetricsDetectConditionDetail {

    private String fieldName;
    private boolean isNumber;
    private List<Object> values;

    public String getFieldName() {
        return fieldName;
    }

    public void setFieldName(String fieldName) {
        this.fieldName = fieldName;
    }

    public List<Object> getValues() {
        return values;
    }

    public void setValues(List<Object> values) {
        this.values = values;
    }

    public boolean isNumber() {
        return isNumber;
    }

    public void setNumber(boolean number) {
        isNumber = number;
    }
}
