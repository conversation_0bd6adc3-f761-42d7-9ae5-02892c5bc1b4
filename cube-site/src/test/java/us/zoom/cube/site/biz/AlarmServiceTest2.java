package us.zoom.cube.site.biz;

import com.zoom.op.monitor.domain.alarm.ConditionType;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.site.biz.alarm.AlarmDefinitionService;
import us.zoom.cube.site.core.model.common.AlarmConditionThresholdRecModel;
import us.zoom.cube.site.lib.MetricsIdPara;
import us.zoom.cube.site.lib.ResponseObject;
import us.zoom.infra.thread.ThreadLocalStore;
import us.zoom.cube.lib.utils.JsonUtils;

import java.util.Collections;

public class AlarmServiceTest2 extends BaseTest {

    @Autowired
    private AlarmService alarmService;

    @Autowired
    private AlarmDefinitionService alarmDefinitionService;

    String user1 = "265b4be6-652c-48dd-a8fe-a0f22256a4af";
    String user2 = "2565ab89-d345-49e4-9548-9066da3001db";
    String clusterId2 = "clusterId";
    String va2 = "VA2";
    String tenantId = "0ab0fa30-cbdc-456f-89c3-dc5188620edb";


    @Test
    public void getFieldAlarmRelationsByMetricsIdHasAlarms() {
        String metricsId = "03eda5f9-f7c2-40fe-9461-518a916b3d62";
        MetricsIdPara metricsIdPara = new MetricsIdPara();
        metricsIdPara.setMetricsId(metricsId);
        ThreadLocalStore.setUserInfoLocal(user1);
        ThreadLocalStore.setTenantInfoLocal(tenantId);
        ResponseObject responseObject = alarmService.getFieldAlarmRelationsByMetricsId(metricsIdPara);
        System.out.println(JsonUtils.toJsonString(responseObject));
    }

    @Test
    public void getAlarmsByMetricsId() {
        for (int i = 0; i < 10; i++) {
            String metricsId = "b6070b5b-2a6a-4a37-b27d-4f9f8514e21d";
            MetricsIdPara metricsIdPara = new MetricsIdPara();
            metricsIdPara.setMetricsId(metricsId);
            ThreadLocalStore.setUserInfoLocal(user1);
            ThreadLocalStore.setTenantInfoLocal(tenantId);
            ResponseObject responseObject = alarmService.getAlarmsByMetricsId(metricsIdPara);
        }
    }

    @Test
    public void queryFieldRecThreshold() {
        for (int i = 0; i < 5; i++) {
            try {
                AlarmConditionThresholdRecModel alarmConditionThresholdRecModel = new AlarmConditionThresholdRecModel();
                alarmConditionThresholdRecModel.setTenantId("1f8cacb9-8769-481f-8ad8-443f18799fa2");
                alarmConditionThresholdRecModel.setMetricId("0d653ff7-9681-498c-937a-5a96b1aa33ab");
                AlarmConditionThresholdRecModel.RuleCondition recCondition = new AlarmConditionThresholdRecModel.RuleCondition();
                recCondition.setConditionType(ConditionType.FIELD);
                recCondition.setName("us");
                recCondition.setOperator("<");
                recCondition.setValueType("number");
                alarmConditionThresholdRecModel.setRecCondition(recCondition);

                AlarmConditionThresholdRecModel.RuleCondition otherCondition = new AlarmConditionThresholdRecModel.RuleCondition();
                otherCondition.setConditionType(ConditionType.TAG);
                otherCondition.setName("ip");
                otherCondition.setOperator("not-in");
                otherCondition.setValueType("string");
                otherCondition.setThreshold("***********,*************");
                alarmConditionThresholdRecModel.setOtherConditions(Collections.singletonList(otherCondition));

                ResponseObject<AlarmConditionThresholdRecModel> res = alarmDefinitionService.queryFieldRecThreshold(alarmConditionThresholdRecModel);
                System.out.println(JsonUtils.toJsonString(res.getData()));
            } catch (Exception e) {
                System.out.println(e.getMessage());
            }
        }

    }

}
