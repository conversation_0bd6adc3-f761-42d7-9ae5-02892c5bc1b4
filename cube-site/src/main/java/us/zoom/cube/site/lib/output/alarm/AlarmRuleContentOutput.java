package us.zoom.cube.site.lib.output.alarm;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2020/8/12 4:29 PM
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AlarmRuleContentOutput {

    List<AlarmRuleSingleContentOutput> tagRules;

    List<AlarmRuleSingleContentOutput> fieldRules;

    List<AlarmRuleSingleContentOutput> functionRules;
}
