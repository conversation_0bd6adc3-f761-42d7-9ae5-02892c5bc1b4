package us.zoom.cube.site.core.alarm.silence;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import us.zoom.infra.dao.model.AlarmSilenceRecurringScheduleDO;
import us.zoom.infra.dao.service.AlarmSilenceRecurringScheduleDAO;

/**
 * @Author: <EMAIL>
 * @ModuleOwner: <EMAIL>
 * @Date: 01/05/2023 19:33
 * @Description:
 */
@Service
public class AlarmSilenceRecurringScheduleHandler {

    @Autowired
    private AlarmSilenceRecurringScheduleDAO alarmSilenceRecurringScheduleDAO;

    public AlarmSilenceRecurringScheduleDO findById(String id){
        return alarmSilenceRecurringScheduleDAO.findById(id);
    }


    public AlarmSilenceRecurringScheduleDO findByAlarmSilenceId(String alarmSilenceId){
        return alarmSilenceRecurringScheduleDAO.findByAlarmSilenceId(alarmSilenceId);
    }


   public int insert(AlarmSilenceRecurringScheduleDO alarmSilenceRecurringScheduleDO){
        return alarmSilenceRecurringScheduleDAO.insert(alarmSilenceRecurringScheduleDO);
   }

    public int update(AlarmSilenceRecurringScheduleDO alarmSilenceRecurringScheduleDO){
        return alarmSilenceRecurringScheduleDAO.update(alarmSilenceRecurringScheduleDO);
    }


    public void deleteById(String id){
        alarmSilenceRecurringScheduleDAO.deleteById(id);
    }

    public AlarmSilenceRecurringScheduleDAO getAlarmSilenceRecurringScheduleDAO() {
        return alarmSilenceRecurringScheduleDAO;
    }

    public void deleteByAlarmSilenceId(String silenceId){
        alarmSilenceRecurringScheduleDAO.deleteByAlarmSilenceId(silenceId);
    }

}
