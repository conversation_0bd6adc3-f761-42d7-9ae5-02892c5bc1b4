package us.zoom.cube.site.core.dashboard;

import org.jetbrains.annotations.NotNull;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class CardQuerySqlBuilder {

    // ===== Types =====

    public enum VariableTypeEnum {
        text, custom, service
    }

    public static class Variable {
        public String name;
        public VariableTypeEnum type;
        public boolean multi;

        public Variable(String name, VariableTypeEnum type, boolean multi) {
            this.name = name;
            this.type = type;
            this.multi = multi;
        }
    }

    public static class Snippet {
        public String name;
        public String content;

        public Snippet(String name, String content) {
            this.name = name;
            this.content = content;
        }
    }

    public static class TimeRangeValue {
        // "relative" or "absolute"
        public String type;
        // if relative: String like "15m", "2h", "1d"
        // if absolute: List<String> size=2 with ISO or RFC3339 timestamps
        public Object value;

        public TimeRangeValue(String type, Object value) {
            this.type = type;
            this.value = value;
        }
    }

    public static class Active {
        public String db;
        public String rp; // may be null
        public String measurement;
        public String dbType; // "clickhouse" or others
        public String rawText; // may be null
        public String snippet; // may be name or content depending on use
        public String originSnippet; // optional original snippet name
        public String groupByTime; // "auto", "none", "5m", etc.
        public String compareTime; // "none" or like "15m"
        public Set<String> groupByTagKey = new LinkedHashSet<>();
        public List<String> fields = new ArrayList<>();
        public Map<String, Object> tagValues = new HashMap<>();
        public Map<String, Object> functionSelectors = new HashMap<>(); // field -> map func->args[] OR Set
        public Map<String, String> fieldsAlias = new HashMap<>(); // field or func_field -> alias
        public Map<String, List<String>> buckets = new HashMap<>();
        public boolean isMapField;

        public Active copy() {
            Active a = new Active();
            a.db = this.db;
            a.rp = this.rp;
            a.measurement = this.measurement;
            a.dbType = this.dbType;
            a.rawText = this.rawText;
            a.snippet = this.snippet;
            a.originSnippet = this.originSnippet;
            a.groupByTime = this.groupByTime;
            a.compareTime = this.compareTime;
            a.groupByTagKey = new LinkedHashSet<>(this.groupByTagKey);
            a.fields = new ArrayList<>(this.fields);
            a.functionSelectors = new HashMap<>(this.functionSelectors);
            a.fieldsAlias = new HashMap<>(this.fieldsAlias);
            a.buckets = new HashMap<>(this.buckets);
            a.isMapField = this.isMapField;
            return a;
        }
    }

    public static class FilterValue extends HashMap<String, Object> {
        public TimeRangeValue timeRange;

        public FilterValue(TimeRangeValue timeRange) {
            this.timeRange = timeRange;
        }

        public FilterValue(TimeRangeValue timeRange, Map<String, Object> initial) {
            super(initial);
            this.timeRange = timeRange;
        }
    }

    public static class Param {
        public String type; // "string" | "array"
        public Object value;

        public Param(String type, Object value) {
            this.type = type;
            this.value = value;
        }
    }

    public static class SqlReturnObject {
        public String template;
        public String sql;
        public String sqlCompare; // optional
        public Map<String, Param> variableParams; // optional

        public SqlReturnObject template(String v) {
            this.template = v;
            return this;
        }

        public SqlReturnObject sql(String v) {
            this.sql = v;
            return this;
        }

        public SqlReturnObject sqlCompare(String v) {
            this.sqlCompare = v;
            return this;
        }

        public SqlReturnObject variableParams(Map<String, Param> v) {
            this.variableParams = v;
            return this;
        }
    }

    // ===== Constants =====

    // Matches tokens like :cluster: , :region: , :interval: , :dashboardTime:
    private static final Pattern VARIABLE_REG = Pattern.compile(":([a-zA-Z]+?):");
    // Matches ${snippetName}
    private static final Pattern SNIPPER_VARIABLE_REG = Pattern.compile("\\$\\{([^}]+)}");

    // ===== Public API: Converted Functions =====

    public static SqlReturnObject genInfluxQLV2(
            Active active,
            FilterValue filterValue,
            List<Variable> variables,
            List<Snippet> snippets
    ) {
        Map<String, Variable> variablesMap = new HashMap<>();
        for (Variable v : variables) {
            String key = v.name.replace(":", "");
            variablesMap.put(key, v);
        }

        Active newActive = replaceSnippetInActive(active, snippets);
        if (newActive.rawText != null && !newActive.rawText.isEmpty()) {
            return genInfluxQLFromRawSql(newActive, filterValue, variablesMap);
        } else {
            return genInfluxQLFromBuilder(newActive, filterValue, variablesMap);
        }
    }

    public static SqlReturnObject genInfluxQLFromRawSql(
            Active active,
            FilterValue filterValue,
            Map<String, Variable> variables
    ) {
        String rawText = active.rawText;
        String dbType = active.dbType;
        TimeRangeValue timeRange = filterValue.timeRange;

        if (rawText == null) {
            return new SqlReturnObject().template("").sql("");
        }

        Matcher m = Pattern.compile(VARIABLE_REG.pattern()).matcher(rawText);
        boolean anyMatch = m.find();
        if (!anyMatch) {
            return new SqlReturnObject().template(rawText).sql(rawText);
        }

        String populatedSql = rawText;
        Set<String> holders = new LinkedHashSet<>();
        m.reset();
        while (m.find()) {
            holders.add(m.group(0)); // entire token, e.g. :cluster:
        }

        // process :dashboardTime: and :upperDashboardTime:
        if (holders.contains(":dashboardTime:") || holders.contains(":upperDashboardTime:")) {
            if (timeRange != null) {
                if ("relative".equals(timeRange.type)) {
                    String dashboardTime = "now() - " + (String) timeRange.value;
                    if ("clickhouse".equals(active.dbType)) {
                        int cr = timeToMinutes((String) timeRange.value);
                        dashboardTime = "subtractMinutes(now(), " + cr + ")";
                    }
                    populatedSql = populatedSql
                            .replace(":dashboardTime:", dashboardTime)
                            .replace(":upperDashboardTime:", "now()");
                } else {
                    String t1 = formatTimeForDb(dbType, ((List<String>) timeRange.value).get(0));
                    String t2 = formatTimeForDb(dbType, ((List<String>) timeRange.value).get(1));
                    populatedSql = populatedSql
                            .replace(":dashboardTime:", t1)
                            .replace(":upperDashboardTime:", t2);
                }
            }
            holders.remove(":dashboardTime:");
            holders.remove(":upperDashboardTime:");
        }

        // process :interval:
        if (holders.contains(":interval:")) {
            String interval = active.groupByTime;
            if ((interval == null || "auto".equals(interval)) && timeRange != null) {
                interval = "clickhouse".equals(active.dbType)
                        ? timeToClickHouseInterval(calcGroupTime(timeRange))
                        : calcGroupTime(timeRange);
            } else {
                if ("clickhouse".equals(active.dbType) && interval != null) {
                    interval = timeToClickHouseInterval(interval);
                }
            }
            populatedSql = populatedSql.replace(":interval:", interval);
            holders.remove(":interval:");
        }

        // replace remaining variables for non-clickhouse
        if (!"clickhouse".equals(dbType)) {
            for (String holder : holders) {
                String hk = holder.replace(":", "");
                Variable variable = variables.get(hk);
                if (filterValue.containsKey(hk)) {
                    Object v = filterValue.get(hk);
                    boolean useRegular = false;

                    if (variable != null) {
                        if (v == null || (v instanceof Collection && ((Collection<?>) v).isEmpty())) {
                            v = "~/.*/";
                            useRegular = true;
                        } else if (variable.type == VariableTypeEnum.text) {
                            // no fuzzy match anymore
                            v = String.valueOf(v);
                        } else if (variable.type == VariableTypeEnum.service) {
                            v = "\"" + v + "\"";
                            useRegular = true;
                        } else if (v instanceof Collection) {
                            if (((Collection<?>) v).isEmpty()) {
                                v = "~/.*/";
                            } else {
                                String pat = getValidateRegularStringWithStringOrArray((Collection<?>) v, true);
                                v = "~/" + pat + "/"; // exact match union
                            }
                            useRegular = true;
                        }
                    }

                    if (useRegular) {
                        String regex = "['\"]?" + Pattern.quote(holder) + "['\"]?";
                        populatedSql = populatedSql.replaceAll(regex, Matcher.quoteReplacement(String.valueOf(v)));
                    } else {
                        populatedSql = populatedSql.replace(holder, String.valueOf(v));
                    }
                } else if (variable != null && variable.type == VariableTypeEnum.service && filterValue.containsKey("service")) {
                    String regex = "['\"]?" + Pattern.quote(holder) + "['\"]?";
                    populatedSql = populatedSql.replaceAll(regex, "\"" + filterValue.get("service") + "\"");
                } else {
                    String regex = "['\"]?" + Pattern.quote(holder) + "['\"]?";
                    populatedSql = populatedSql.replaceAll(regex, "~/.*/");
                }
            }
        }

        populatedSql = replaceTemplateInterval(populatedSql, timeRange, active.groupByTime);

        Map<String, Param> variableParams = new LinkedHashMap<>();
        for (Map.Entry<String, Variable> e : variables.entrySet()) {
            String key = e.getKey();
            Variable variable = e.getValue();
            Object value = filterValue.get(key);

            if (variable != null && value != null && variable.type == VariableTypeEnum.custom && value instanceof String && ((String) value).contains("|")) {
                value = Arrays.asList(((String) value).split("\\|"));
            } else if (value instanceof Collection) {
                value = parseMultipleValue((Collection<?>) value, variable != null ? variable.type : null);
            }

            if (value != null) {
                if (variable != null && variable.multi) {
                    List<Object> arr = value instanceof Collection ? new ArrayList<>((Collection<?>) value) : Collections.singletonList(value);
                    variableParams.put(key, new Param("array", arr));
                } else if (value instanceof Collection) {
                    variableParams.put(key, new Param("array", new ArrayList<>((Collection<?>) value)));
                } else {
                    variableParams.put(key, new Param("string", value));
                }
            } else {
                variableParams.put(key, new Param(variable != null && variable.multi ? "array" : "string", variable != null && variable.multi ? Collections.emptyList() : null));
            }
        }

        // Append cluster and region for convenience (matches TS behavior)
        for (String key : Arrays.asList("cluster", "region")) {
            Object v = filterValue.get(key);
            variableParams.put(key, new Param("string", v != null ? v : null));
        }

        return new SqlReturnObject()
                .template(rawText)
                .sql(populatedSql)
                .variableParams(variableParams);
    }

    public static SqlReturnObject genInfluxQLFromBuilder(
            Active active,
            FilterValue filterValue,
            Map<String, Variable> variables
    ) {
        String db = active.db;
        String rp = active.rp;
        String measurement = active.measurement;
        String dbType = active.dbType;
        TimeRangeValue timeRange = filterValue.timeRange;

        SelectPartResult selectRet = genSelectPart(active, null, timeRange);

        GroupByPart groupBy = genGroupByPart(
                selectRet.groupBy,
                active.groupByTime,
                active.groupByTagKey,
                timeRange,
                dbType
        );

        String timePart = genTimeRangePart(dbType, timeRange, null);

        TagPart tagPart = genInfluxQLTagPartV2(active, filterValue, variables);

        SqlReturnObject snippetPart = genSnipperWhereCondition(active, filterValue, variables);

        String snipperPartTemplate = active.originSnippet != null ? " ${" + active.originSnippet + "}" : "";

        OrderByPart orderByPart = genOrderByPart(dbType);

        String template = (
                "SELECT " + selectRet.template +
                        " FROM \"" + db + "\"" + genRP(dbType, rp) + "\"" + measurement + "\"" +
                        " WHERE time >= :dashboardTime: AND time < :upperDashboardTime: " +
                        (tagPart.tagPartTemplate.length() > 0 ? "AND " + tagPart.tagPartTemplate : "") +
                        " " + snipperPartTemplate +
                        " " + groupBy.groupByTemplatePart +
                        " " + orderByPart.orderByTemplatePart
        ).replaceAll("\\s+", " ");

        String sql = (
                "SELECT " + selectRet.sql +
                        " FROM \"" + db + "\"" + genRP(dbType, rp) + "\"" + measurement + "\"" +
                        " WHERE " + timePart +
                        (tagPart.tagPartSql.length() > 0 ? " AND " + tagPart.tagPartSql : "") +
                        (snippetPart != null && snippetPart.sql != null && !snippetPart.sql.isEmpty() ? " " + snippetPart.sql : "") +
                        " " + groupBy.groupBySqlPart +
                        " " + orderByPart.orderBySqlPart
        ).replaceAll("\\s+", " ");

        SqlReturnObject ret = new SqlReturnObject().template(template).sql(sql);

        if (snippetPart != null && snippetPart.variableParams != null) {
            ret.variableParams = snippetPart.variableParams;
        }

        if (active.compareTime != null && !"none".equals(active.compareTime)) {
            ret.sqlCompare = genCompareSql(active, timeRange, tagPart.tagPartSql, groupBy.groupBySqlPart, orderByPart.orderBySqlPart);
        }

        return ret;
    }

    // ===== Helpers ported from TS (only those required) =====

    private static String genRP(String dbType, String rp) {
        if ("clickhouse".equals(dbType) || rp == null || rp.isEmpty()) return ".";
        return ".\"" + rp + "\".";
    }

    private static String replaceTemplateInterval(String sql, TimeRangeValue timeRange, String groupByTime) {
        if (groupByTime != null && !"auto".equals(groupByTime) && !"none".equals(groupByTime)) {
            String interval = timeToClickHouseInterval(groupByTime);
            return sql
                    .replace("':INTERVAL:'", interval)
                    .replace(":INTERVAL:", interval)
                    .replace("':interval:'", interval)
                    .replace(":interval:", interval);
        }
        String interval = timeToClickHouseInterval(calcGroupTime(timeRange));
        return sql
                .replace("':INTERVAL:'", interval)
                .replace(":INTERVAL:", interval)
                .replace("':interval:'", interval)
                .replace(":interval:", interval);
    }

    private static String formatTimeForDb(String dbType, String t) {
        if ("clickhouse".equals(dbType)) {
            // to UTC 'YYYY-MM-DD HH:mm:ss'
            ZonedDateTime zdt = ZonedDateTime.parse(t);
            String s = zdt.withZoneSameInstant(ZoneOffset.UTC).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            return "toDateTime('" + s + "')";
        }
        // ISO 8601
        return "'" + Instant.parse(t).toString() + "'";
    }

    public static int timeToMinutes(String spec) {
        if (spec == null || spec.isEmpty()) return 0;
        spec = spec.trim().toLowerCase(Locale.ROOT);
        try {
            if (spec.endsWith("ms")) {
                double ms = Double.parseDouble(spec.substring(0, spec.length() - 2));
                return (int) Math.max(1, Math.round(ms / 1000.0 / 60.0));
            }
            if (spec.endsWith("s")) {
                double s = Double.parseDouble(spec.substring(0, spec.length() - 1));
                return (int) Math.max(1, Math.round(s / 60.0));
            }
            if (spec.endsWith("m")) {
                return Integer.parseInt(spec.substring(0, spec.length() - 1));
            }
            if (spec.endsWith("h")) {
                return Integer.parseInt(spec.substring(0, spec.length() - 1)) * 60;
            }
            if (spec.endsWith("d")) {
                return Integer.parseInt(spec.substring(0, spec.length() - 1)) * 60 * 24;
            }
            if (spec.endsWith("w")) {
                return Integer.parseInt(spec.substring(0, spec.length() - 1)) * 60 * 24 * 7;
            }
            return Integer.parseInt(spec);
        } catch (Exception e) {
            return 1;
        }
    }

    public static String timeToClickHouseInterval(String time) {
        int min = timeToMinutes(time);
        if (min < 1) return (min * 60) + " SECOND";
        if (min < 60) return min + " MINUTE";
        if (min < 60 * 24) return (min / 60) + " HOUR";
        return (min / 60 / 24) + " DAY";
    }

    public static String calcGroupTime(TimeRangeValue timeRange) {
        int totalTime = 5;
        if ("relative".equals(timeRange.type)) {
            totalTime = timeToMinutes((String) timeRange.value);
        } else {
            List<String> vals = (List<String>) timeRange.value;
            Instant start = Instant.parse(vals.get(0));
            Instant end = Instant.parse(vals.get(1));
            totalTime = (int) Math.abs(Duration.between(start, end).toMinutes());
        }
        int groupTime = Math.max(1, Math.round(totalTime / 100.0f));
        return groupTime + "m";
    }

    public static String genTimeRangePart(String dbType, TimeRangeValue timeRange, String compareTime) {
        if ("relative".equals(timeRange.type)) {
            if (compareTime != null && !"none".equals(compareTime)) {
                if ("clickhouse".equals(dbType)) {
                    int tr = timeToMinutes((String) timeRange.value);
                    int ct = timeToMinutes(compareTime);
                    return "time >= subtractMinutes(now(), " + (tr + ct) + ") and time < subtractMinutes(now(), " + ct + ")";
                }
                return "time >= now() - " + timeRange.value + " - " + compareTime + " and time < now() - " + compareTime;
            }
            if ("clickhouse".equals(dbType)) {
                int tr = timeToMinutes((String) timeRange.value);
                return "time >= subtractMinutes(now(), " + tr + ") AND time < now()";
            }
            return "time >= now() - " + timeRange.value;
        } else {
            List<String> vals = (List<String>) timeRange.value;
            String t1 = formatTimeForDb(dbType, vals.get(0));
            String t2 = formatTimeForDb(dbType, vals.get(1));
            return "time >= " + t1 + " and time < " + t2;
        }
    }

    public static class SelectPartResult {
        public String sql;
        public String template;
        public boolean groupBy;
    }

    public static SelectPartResult genSelectPart(
            Active active,
            String compareTime,
            TimeRangeValue timeRange
    ) {
        List<String> select = new ArrayList<>();
        boolean groupBy = false;

        for (String field : active.fields) {
            Object prevFuncs = active.functionSelectors.get(field);

            Map<String, List<String>> funcs = new LinkedHashMap<>();
            if (prevFuncs instanceof Set) {
                for (Object item : ((Set<?>) prevFuncs)) {
                    funcs.put(String.valueOf(item), Collections.emptyList());
                }
            } else if (prevFuncs instanceof Map) {
                Map<?, ?> m = (Map<?, ?>) prevFuncs;
                for (Map.Entry<?, ?> e : m.entrySet()) {
                    List<String> args = new ArrayList<>();
                    Object v = e.getValue();
                    if (v instanceof List) {
                        for (Object a : (List<?>) v) args.add(String.valueOf(a));
                    }
                    funcs.put(String.valueOf(e.getKey()), args);
                }
            }

            if (!funcs.isEmpty()) {
                groupBy = true;
                for (Map.Entry<String, List<String>> fe : funcs.entrySet()) {
                    String func = fe.getKey();
                    List<String> args = fe.getValue();
                    String userAlias = active.fieldsAlias.get(func + "_" + field);
                    String alias = userAlias != null ? userAlias : (func + "_" + field);
                    if (compareTime != null && !"none".equals(compareTime)) {
                        alias = alias + "_" + compareTime;
                    }

                    if (!args.isEmpty()) {
                        if ("histogram_quantile".equals(func)) {
                            // histogram_quantile(sumForEach(field), [arg0], arg1/100)
                            String expr = String.format(
                                    "%s(sumForEach(%s), [%s], %s) AS \"%s\"",
                                    func, field, args.get(0), Double.parseDouble(args.get(1)) / 100.0, alias
                            );
                            select.add(expr);
                        } else {
                            select.add(func + "(\"" + field + "\", " + String.join(", ", args) + ") AS \"" + alias + "\"");
                        }
                    } else {
                        List<String> fieldBuckets = active.buckets.get(field);
                        if (fieldBuckets != null && !fieldBuckets.isEmpty()) {
                            for (int i = 0; i < fieldBuckets.size(); i++) {
                                String bucket = fieldBuckets.get(i);
                                if (bucket != null) {
                                    select.add(func + "(\"" + field + "\"[" + (i + 1) + "]) AS \"" + alias + "_" + bucket + "\"");
                                }
                            }
                        } else {
                            select.add(func + "(\"" + field + "\") AS \"" + alias + "\"");
                        }
                    }
                }
            } else {
                if (active.fieldsAlias.containsKey(field)) {
                    List<String> fieldBuckets = active.buckets.get(field);
                    if (fieldBuckets != null && !fieldBuckets.isEmpty()) {
                        for (int i = 0; i < fieldBuckets.size(); i++) {
                            String bucket = fieldBuckets.get(i);
                            if (bucket != null) {
                                select.add("\"" + field + "\"[" + (i + 1) + "] AS \"" + active.fieldsAlias.get(field) + "_" + bucket + "\"");
                            }
                        }
                    } else {
                        String finalField = active.isMapField ? field : "\"" + field + "\"";
                        select.add(finalField + " AS \"" + active.fieldsAlias.get(field) + "\"");
                    }
                } else {
                    List<String> fieldBuckets = active.buckets.get(field);
                    if (fieldBuckets != null && !fieldBuckets.isEmpty()) {
                        for (int i = 0; i < fieldBuckets.size(); i++) {
                            String bucket = fieldBuckets.get(i);
                            if (bucket != null) {
                                select.add("\"" + field + "\"[" + (i + 1) + "] AS \"" + field + "_" + bucket + "\"");
                            }
                        }
                    } else {
                        String finalField = active.isMapField ? field : "\"" + field + "\"";
                        select.add(finalField);
                    }
                }
            }
        }

        if ("clickhouse".equals(active.dbType)) {
            if (groupBy && !"none".equals(active.groupByTime)) {
                String t = "auto".equals(active.groupByTime)
                        ? ":interval:"
                        : timeToClickHouseInterval(active.groupByTime);
                select.add("toStartOfInterval(time, INTERVAL " + t + ") AS time");
            } else {
                select.add("time");
            }
            for (String tag : active.groupByTagKey) {
                select.add("\"" + tag + "\"");
            }
        }

        String csv = String.join(", ", select);
        SelectPartResult r = new SelectPartResult();
        r.sql = replaceTemplateInterval(csv, timeRange, active.groupByTime);
        r.template = csv;
        r.groupBy = groupBy && !"none".equals(active.groupByTime);
        return r;
    }

    public static class GroupByPart {
        public String groupBySqlPart;
        public String groupByTemplatePart;
    }

    public static GroupByPart genGroupByPart(
            boolean isGroupByTime,
            String groupByTime,
            Set<String> groupByTagKey,
            TimeRangeValue timeRange,
            String dbType
    ) {
        String calculatedTime = groupByTime;
        if ("auto".equals(groupByTime) || "".equals(groupByTime)) {
            calculatedTime = calcGroupTime(timeRange);
        }
        String sqlPart = "";
        String templatePart = "";

        if (isGroupByTime) {
            if ("clickhouse".equals(dbType)) {
                sqlPart = "time";
                templatePart = "time";
            } else {
                sqlPart = "time(" + calculatedTime + ")";
                templatePart = "time(" + ("auto".equals(groupByTime) || "".equals(groupByTime) ? ":interval:" : groupByTime) + ")";
            }
        }
        for (String tagKey : groupByTagKey) {
            sqlPart = sqlPart.length() > 0 ? sqlPart + ", \"" + tagKey + "\"" : "\"" + tagKey + "\"";
            templatePart = templatePart.length() > 0 ? templatePart + ", \"" + tagKey + "\"" : "\"" + tagKey + "\"";
        }
        GroupByPart part = new GroupByPart();
        part.groupBySqlPart = sqlPart.length() > 0 ? "GROUP BY " + sqlPart : "";
        part.groupByTemplatePart = templatePart.length() > 0 ? "GROUP BY " + templatePart : "";
        return part;
    }

    public static class OrderByPart {
        public String orderBySqlPart;
        public String orderByTemplatePart;
    }

    public static OrderByPart genOrderByPart(String dbType) {
        OrderByPart p = new OrderByPart();
        String v = "clickhouse".equals(dbType) ? "ORDER BY time DESC" : "";
        p.orderBySqlPart = v;
        p.orderByTemplatePart = v;
        return p;
    }

    private static String genCompareSql(
            Active active,
            TimeRangeValue timeRange,
            String tagPartSql,
            String groupBySqlPart,
            String orderBySqlPart
    ) {
        String db = active.db;
        String rp = active.rp;
        String measurement = active.measurement;
        String compareTime = active.compareTime;
        String dbType = active.dbType;

        SelectPartResult compareSelectPart = genSelectPart(active, compareTime, timeRange);
        String compareTimePart = genTimeRangePart(dbType, timeRange, compareTime);

        String sql = (
                "SELECT " + compareSelectPart.sql +
                        " FROM \"" + db + "\"" + genRP(dbType, rp) + "\"" + measurement + "\"" +
                        " WHERE " + compareTimePart +
                        (tagPartSql.length() > 0 ? " AND " + tagPartSql : "") +
                        " " + groupBySqlPart +
                        " " + orderBySqlPart
        ).replaceAll("\\s+", " ");

        return sql;
    }

    public static class TagPart {
        public String tagPartTemplate;
        public String tagPartSql;
    }

    public static TagPart genInfluxQLTagPartV2(
            Active active,
            FilterValue filterValue,
            Map<String, Variable> variables
    ) {
        Map<String, Set<String>> tagValues = getStringSetMap(active);

        StringBuilder tagPartTemplate = new StringBuilder();
        StringBuilder tagPartSql = new StringBuilder();

        for (Map.Entry<String, Set<String>> entry : tagValues.entrySet()) {
            String tagKey = entry.getKey();
            Set<String> values = entry.getValue();

            StringBuilder tPart = new StringBuilder();
            StringBuilder sPart = new StringBuilder();

            for (String value : values) {
                Matcher match = VARIABLE_REG.matcher(value);
                boolean matched = match.find();
                if (!matched || !isVariable(variables, match.group(1))) {
                    if (tPart.length() > 0) tPart.append("or ");
                    tPart.append("\"").append(tagKey).append("\"='").append(value).append("' ");
                    if (sPart.length() > 0) sPart.append("or ");
                    sPart.append("\"").append(tagKey).append("\"='").append(value).append("' ");
                } else {
                    String name = match.group(1);
                    Object fv = filterValue.get(name);
                    Variable variable = variables.get(name);
                    Object realValue = fv;

                    if (variable != null && fv != null && variable.type == VariableTypeEnum.custom && fv instanceof String && ((String) fv).contains("|")) {
                        realValue = Arrays.asList(((String) fv).split("\\|"));
                    }
                    if (variable != null && realValue != null) {
                        if (variable.type == VariableTypeEnum.text) {
                            realValue = "'" + realValue + "'";
                        } else if (realValue instanceof Collection) {
                            if (((Collection<?>) realValue).isEmpty()) {
                                if (tPart.length() > 0) tPart.append("or ");
                                tPart.append("\"").append(tagKey).append("\"=").append(match.group(0)).append(" ");
                                continue;
                            }
                            realValue = getVariableValue((Collection<?>) realValue, tagKey, variable.type);
                        } else {
                            realValue = "'" + realValue + "'";
                        }
                    } else {
                        realValue = "'" + realValue + "'";
                    }

                    String operation = "=";
                    if (tPart.length() > 0) tPart.append("or ");
                    tPart.append("\"").append(tagKey).append("\"").append(operation).append(match.group(0)).append(" ");
                    if (sPart.length() > 0) sPart.append("or ");
                    sPart.append("\"").append(tagKey).append("\"").append(operation).append(realValue).append(" ");
                }
            }

            if (tPart.length() > 0) {
                tagPartTemplate.append(tagPartTemplate.length() > 0 ? " AND " : "").append("(").append(tPart).append(")");
            }
            if (sPart.length() > 0) {
                tagPartSql.append(tagPartSql.length() > 0 ? " AND " : "").append("(").append(sPart).append(")");
            }
        }

        TagPart ret = new TagPart();
        ret.tagPartTemplate = tagPartTemplate.toString();
        ret.tagPartSql = tagPartSql.toString();
        return ret;
    }

    @NotNull
    private static Map<String, Set<String>> getStringSetMap(Active active) {
        Map<String, Set<String>> tagValues = new LinkedHashMap<>();
        // Active.tagValues is expected; to keep this self-contained, assume Active.functionSelectors holds tagValues if present
        // In production, pass tagValues via Active or adapt accordingly.
        // Here we try to read an optional field named "tagValues" inside functionSelectors map.
        Object tv = active.tagValues;
        if (tv instanceof Map) {
            Map<?, ?> raw = (Map<?, ?>) tv;
            for (Map.Entry<?, ?> e : raw.entrySet()) {
                String k = String.valueOf(e.getKey());
                Set<String> vs = new LinkedHashSet<>();
                Object val = e.getValue();
                if (val instanceof Collection) {
                    for (Object x : (Collection<?>) val) vs.add(String.valueOf(x));
                }
                tagValues.put(k, vs);
            }
        }
        return tagValues;
    }

    private static SqlReturnObject genSnipperWhereCondition(
            Active active,
            FilterValue filterValue,
            Map<String, Variable> variables
    ) {
        Active tmp = active.copy();
        tmp.rawText = active.snippet != null ? active.snippet : null;
        return genInfluxQLFromRawSql(tmp, filterValue, variables);
    }

    private static Active replaceSnippetInActive(Active active, List<Snippet> snippets) {
        Active a = active.copy();

        if (a.rawText != null && !a.rawText.isEmpty()) {
            String newRawText = a.rawText;
            Matcher m = SNIPPER_VARIABLE_REG.matcher(newRawText);
            boolean found = false;
            StringBuffer sb = new StringBuffer();
            while (m.find()) {
                found = true;
                String name = m.group(1);
                Snippet sn = findSnippet(snippets, name);
                String replacement = sn != null ? " " + sn.content : "";
                m.appendReplacement(sb, Matcher.quoteReplacement(replacement));
            }
            m.appendTail(sb);
            if (found) {
                a.rawText = sb.toString();
            }
            return a;
        } else {
            if (a.snippet != null) {
                Snippet sn = findSnippet(snippets, a.snippet);
                a.snippet = sn != null ? sn.content : a.snippet;
                a.originSnippet = active.snippet;
            }
            return a;
        }
    }

    private static Snippet findSnippet(List<Snippet> list, String name) {
        if (list == null) return null;
        for (Snippet s : list) {
            if (Objects.equals(s.name, name)) return s;
        }
        return null;
    }

    private static boolean isVariable(Map<String, Variable> variableMap, String key) {
        // Assuming all variables must be declared in variableMap for the Java-side
        return variableMap.containsKey(key);
    }

    private static String getValidateRegularStringWithStringOrArray(Collection<?> value, boolean exact) {
        List<String> items = new ArrayList<>();
        for (Object o : value) {
            items.add("(" + getValidateRegularString(String.valueOf(o), exact) + ")");
        }
        return String.join("|", items);
    }

    private static String getValidateRegularString(String str, boolean exact) {
        StringBuilder out = new StringBuilder();
        String special = "*.?+$^[](){}|\\/";
        for (char c : str.toCharArray()) {
            if (special.indexOf(c) >= 0) {
                out.append("\\").append(c);
            } else {
                out.append(c);
            }
        }
        return exact ? "^" + out + "$" : out.toString();
    }

    private static String getVariableValue(Collection<?> values, String tagKey, VariableTypeEnum type) {
        List<String> newValue = parseMultipleValue(values, type);
        String first = newValue.get(0);
        if (newValue.size() <= 1) {
            return "'" + first + "'";
        }
        List<String> conditions = new ArrayList<>();
        for (int i = 1; i < newValue.size(); i++) {
            conditions.add("\"" + tagKey + "\" = '" + newValue.get(i) + "'");
        }
        return "'" + first + "' OR " + String.join(" OR ", conditions);
    }

    private static List<String> parseMultipleValue(Collection<?> values, VariableTypeEnum type) {
        List<String> result = new ArrayList<>();
        for (Object v : values) {
            String s = String.valueOf(v);
            if (type == VariableTypeEnum.custom && s.contains("|")) {
                result.addAll(Arrays.asList(s.split("\\|")));
            } else {
                result.add(s);
            }
        }
        return result;
    }
}