package us.zoom.cube.site.core.preprocess;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import us.zoom.cube.site.core.parser.process.core.common.DataParserContext;
import us.zoom.cube.site.core.parser.process.core.common.DataParserMetrics;
import us.zoom.cube.site.core.parser.process.core.common.ErrorTags;
import us.zoom.cube.site.core.parser.process.core.monitoring.DataParserTags;
import us.zoom.cube.site.core.parser.process.core.preprocess.OtherDataParserPreProcessService;
import us.zoom.cube.site.core.parser.process.core.response.PreResp;


/**
 * @Author: <EMAIL>
 * @ModuleOwner: <EMAIL>
 * @Date: 01/02/2023 11:12
 * @Description:
 */
public class OtherDataParserPreProcessServiceTest {
    private OtherDataParserPreProcessService odppps;
    private DataParserContext context;


    public static String scriptTextStr = "\n" +
            "    public List<Map<String, Object>> parseMessage(String message) {\n" +
            "        List<Map<String, Object>> result = new ArrayList<>();\n" +
            "        List<String> msgList = Arrays.asList(message.split(\",\"))\n" +
            "        msgList.forEach(msg -> {\n" +
            "            Map<String, Object> map = new HashMap<>()\n" +
            "            map.put(\"message\", msg)\n" +
            "            result.add(map)\n" +
            "        })\n" +
            "        return result\n" +
            "    }";

    public static String scriptTextTaskEntityIsFail = "import us.zoom.mq.common.entity.TaskEntity\n" +
            "    public List<Map<String, Object>> parseMessage(String message, TaskEntity<String> taskEntity) {\n" +
            "        List<Map<String, Object>> result = new ArrayList<>();\n" +
            "        List<String> msgList = Arrays.asList(message.split(\",\"))\n" +
            "        msgList.forEach(msg -> {\n" +
            "            Map<String, Object> map = new HashMap<>()\n" +
            "            map.put(\"message\", msg)\n" +
            "            result.add(map)\n" +
            "        })\n" +
            "        int i = 1/0\n" +
            "        return result\n" +
            "    }";

    @BeforeEach
    public void before() {
        context = new DataParserContext(ErrorTags.builder().build(), new DataParserMetrics(new DataParserTags()));
    }

    @Test
    public void test() {
        odppps = new OtherDataParserPreProcessService("parseMessage", scriptTextStr);
        String message = "cc,bb,cc,dd,aa";
        PreResp result = odppps.handle(message);
        Assertions.assertEquals(result.getPreProcessList().size(), 5);
        Assertions.assertEquals(context.getMetrics().getFields().toMap().get("pre"), 1L);
    }

    @Test
    public void testFail() {
        odppps = new OtherDataParserPreProcessService("parseMessage", scriptTextTaskEntityIsFail);
        String message = "cc,bb,cc,dd,aa";
        PreResp result = odppps.handle(message);
        Assertions.assertEquals(result.getPreProcessList().size(), 0L);
        Assertions.assertEquals(context.getMetrics().getFields().toMap().get("preFail"), 1);
    }
}
