package us.zoom.cube.site.lib.output.config.metrics;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import us.zoom.cube.site.lib.BasePara;

/**
 * <AUTHOR>
 * @create 2020/7/19 10:11 AM
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SimpleMetricsAggregationInput extends BasePara {

    String metricsName;

    Integer aggPeriod;

    Integer subPeriod;

    String filterCondition;

}
