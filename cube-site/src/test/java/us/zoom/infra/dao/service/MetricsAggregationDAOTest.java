package us.zoom.infra.dao.service;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import us.zoom.cube.site.BaseSpringTest;
import us.zoom.cube.site.core.MetricsHandler;
import us.zoom.cube.site.infra.utils.IdUtils;
import us.zoom.infra.dao.model.MetricsAggregationDO;
import us.zoom.infra.dao.model.MetricsAggregationRuleDO;
import us.zoom.cube.lib.utils.JsonUtils;

import java.util.ArrayList;
import java.util.List;

public class MetricsAggregationDAOTest extends BaseSpringTest {

    @Autowired
    MetricsHandler metricsHandler;

    @Autowired
    MetricsAggregationRuleDAO metricsAggregationRuleDAO;

    @Test
    public void getByMetricsIds(){
        List<String> metricsIdList = new ArrayList<>();
        List<MetricsAggregationDO> res1 = metricsHandler.listMetricsAggByMetricsIds(metricsIdList);
        System.out.println("res1 = " + JsonUtils.toJsonString(res1));

        metricsIdList.add("355f10cb-0eca-4e68-a7ba-bb7f97c46270");
        List<MetricsAggregationDO> res2 = metricsHandler.listMetricsAggByMetricsIds(metricsIdList);
        System.out.println("res1 = " + JsonUtils.toJsonString(res2));

        metricsIdList.add("355f10cb-0eca-4e68-a7ba-bb7f97c46270");
        metricsIdList.add("4a4c3007-bb8c-40d1-9c77-b68d4453b06d");
        List<MetricsAggregationDO> res3 = metricsHandler.listMetricsAggByMetricsIds(metricsIdList);
        System.out.println("res1 = " + JsonUtils.toJsonString(res3));

    }

    @Test
    public void insert(){
        MetricsAggregationRuleDO metricsAggregationRuleDO = new MetricsAggregationRuleDO();
        metricsAggregationRuleDO.setId(IdUtils.generateId());
        metricsAggregationRuleDO.setAggId("fbea3d5d-55d6-4ed7-8bf4-84d9c690153f");
        metricsAggregationRuleDO.setAggField("timeMills");
        metricsAggregationRuleDO.setAggTypes("");
        metricsAggregationRuleDO.setIsConditioned(0);
        metricsAggregationRuleDO.setConditionalFieldPrefix("");
        metricsAggregationRuleDO.setAggRuleType(2);
        metricsAggregationRuleDO.setVersion(1);
        metricsAggregationRuleDAO.insertMetricsAggregationRule(metricsAggregationRuleDO);
    }

}
