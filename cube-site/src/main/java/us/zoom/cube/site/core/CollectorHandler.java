package us.zoom.cube.site.core;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RestController;
import us.zoom.cube.lib.common.CubeConstants;
import us.zoom.infra.dao.model.*;
import us.zoom.infra.dao.service.CollectorDAO;
import us.zoom.infra.dao.service.CollectorFieldDAO;
import us.zoom.infra.dao.service.CollectorMetricsDAO;
import us.zoom.infra.dao.service.MetricsDAO;
import us.zoom.infra.enums.FieldTypeEnum;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;


/**
 * <AUTHOR>
 */
@Service
@RestController
public class CollectorHandler {
    @Autowired
    private CollectorDAO collectorDAO;
    @Autowired
    private CollectorFieldDAO collectorFieldDAO;
    @Autowired
    private CollectorMetricsDAO collectorMetricsDAO;
    @Autowired
    private MetricsDAO metricsDAO;

    public List<CollectorDO> findByCollectorNameLike(String collectorName ,String tenantId, int pageIndex, int pageSize)throws Exception{
        return collectorDAO.findByCollectorNameLike(collectorName,tenantId,pageSize*(pageIndex-1), pageSize);
    }


//    @Transactional(rollbackFor = Exception.class)
//    public void addCollector(CollectorDO collectorDO, List<CollectorFieldDO> collectorFieldDOList){
//        collectorDAO.insertCollector(collectorDO);
//        collectorFieldDOList.forEach(field -> collectorFieldDAO.insertCollectorField(field));
//    }
//
//
//    private void updateCollector(CollectorDO collectorDO){
//        String collectorId= collectorDO.getId();
//        int collectorType = collectorDO.getCollectorType();
//        String collectorKey = collectorDO.getCollectorKey();
//        String parseType = collectorDO.getParseType();
//        String patternName = collectorDO.getPatternName();
//        String innerPatternName = collectorDO.getInnerPatternName();
//        Date modifyTime = new Date();
//        collectorDAO.updateCollectorById(collectorType,collectorKey,parseType, patternName, innerPatternName,modifyTime,collectorId);
//    }



    public List<CollectorFieldDO> listCollectorFieldsByCollectorIds(List<String> collectorIds){
        return collectorDAO.listCollectorFieldsByIds(collectorIds);
    }

    public int getCountByCollectorNameLike(String collectorName, String tenantId) {
        return collectorDAO.getCountByCollectorNameLike(collectorName,tenantId);
    }

//    public CollectorDO getCollectorById(String id) {
//        return collectorDAO.getCollectorById(id);
//    }

//    @Transactional(rollbackFor = Exception.class)
//    public void editCollector(CollectorInput<CollectorFieldInput> existCollector, CollectorInput<CollectorFieldInput> collectorInput) {
//
//
//        //update collector
//        if(existCollector.isCanEdit()){
//            CollectorDO collectorDO=new CollectorDO();
//            BeanUtils.copyProperties(collectorInput,collectorDO);
//            updateCollector(collectorDO);
//        }
//
//        //del field
//        Set<String> notDelFields=existCollector.getCollectorField().stream().filter(e->!e.isCanEdit()).map(e->e.getId()).collect(Collectors.toSet());
//        Set<String> execludelFields= collectorInput.getCollectorField().stream().filter(e->!StringUtils.isEmpty(e.getId())).map(e->e.getId()).collect(Collectors.toSet());
//        execludelFields.addAll(notDelFields);
//        if(!CollectionUtils.isEmpty(execludelFields)){
//            collectorFieldDAO.delFieldExecludeSome(existCollector.getId(),execludelFields);
//        }else{
//            collectorFieldDAO.delFieldByCollectorId(existCollector.getId());
//        }
//
//        //insert field
//        Instance.ofNullable(collectorInput.getCollectorField()).stream().filter(e-> StringUtils.isEmpty(e.getId())).forEach(field->{
//            field.setId(IdUtils.generateId());
//            field.setCollectorId(existCollector.getId());
//            CollectorFieldDO fieldDO=new CollectorFieldDO();
//            BeanUtils.copyProperties(field,fieldDO);
//            fieldDO.setFieldType(StringUtils.isEmpty(fieldDO.getFieldType())?"string":fieldDO.getFieldType());
//            collectorFieldDAO.insertCollectorField(fieldDO);
//        });
//
//        //update field
//        collectorInput.getCollectorField().stream().filter(e->!StringUtils.isEmpty(e.getId())&&!notDelFields.contains(e.getId())).forEach(field->{
//            CollectorFieldDO collectorFieldDO=new CollectorFieldDO();
//            BeanUtils.copyProperties(field,collectorFieldDO);
//            collectorFieldDAO.updateCollectorFieldById(collectorFieldDO);
//        });
//    }

    public List<CollectorDO> listCollectorByTenant(String tenantId) {
        return collectorDAO.listCollectorByTenant(tenantId);
    }
//
//    public CollectorDO getCollectorByName(String collectorName, String tenantId) {
//        return  collectorDAO.getCollectorByName(collectorName,tenantId);
//    }

    public List<CollectorFieldDO> listFieldByCollectorIds(List<String> collectIds) {
        if(CollectionUtils.isEmpty(collectIds)){
            return Collections.emptyList();
        }
        return  collectorFieldDAO.listFieldByCollectorIds(collectIds);
    }

    public List<CollectorFieldDO> getFiledByCollectorId(String collectId) {
        if(StringUtils.isBlank(collectId)){
            return Collections.emptyList();
        }
        return  collectorFieldDAO.getFiledByCollectorId(collectId);
    }

    public List<MetricsDO> listEnableMetricsByMetricsIds(List<String> metricsIds) {
        if(CollectionUtils.isEmpty(metricsIds)){
            return Collections.emptyList();
        }
        return  metricsDAO.listEnableMetricsByMetricsIds(metricsIds);
    }


    public List<MetricsDO> listMetricsByMetricsIds(List<String> metricsIds) {
        if(CollectionUtils.isEmpty(metricsIds)){
            return Collections.emptyList();
        }
        return  metricsDAO.listMetricsByMetricsIds(metricsIds);
    }

    public List<MetricsFieldDO> listMetricsFieldByMetricsIds(List<String> metricsIds) {
        if(CollectionUtils.isEmpty(metricsIds)){
            return Collections.emptyList();
        }
        return  metricsDAO.listMetricsFieldByMetricsIds(metricsIds);
    }

    public List<CollectorMetricsDO> listMetricsCollectorByCollectorIds(List<String> collectIds) {
        if(CollectionUtils.isEmpty(collectIds)){
            return Collections.emptyList();
        }
        return  collectorMetricsDAO.listMetricsCollectorByCollectorIds(collectIds);
    }

    public List<CollectorMetricsDO> listCollectorByMetricsIds(List<String> metricsIds) {
        if(CollectionUtils.isEmpty(metricsIds)){
            return Collections.emptyList();
        }
        return  collectorMetricsDAO.listCollectorByMetricsIds(metricsIds);
    }

   public int insertCollector(CollectorDO collectorDO) {
        return collectorDAO.insertCollector(collectorDO);
    }
//    public void addField(CollectorFieldDO fieldDO) {
//        Assertions.assertTrue("field type is not string or number",FieldTypeEnum.matchType(fieldDO.getFieldType()));
//        collectorFieldDAO.insertCollectorField(fieldDO);
//    }

    public CollectorFieldDO getFieldByFieldId(String fieldId) {
       return collectorFieldDAO.getFieldByFieldId(fieldId);
    }

    public void editField(CollectorFieldDO fieldDO) {
        Assert.isTrue(FieldTypeEnum.matchType(fieldDO.getFieldType()), "field type is not string or number");
        collectorFieldDAO.editField(fieldDO);
    }

    public void delField(String id) {
        collectorFieldDAO.delField(id);
    }

    public void delFieldByCollectorId(String id) {
        collectorFieldDAO.delFieldByCollectorId(id);
    }

    public List<CollectorDO> listAll() {
        List<CollectorDO> collectorDOList= new ArrayList<>();
        long counts = collectorDAO.queryCounts();
        int pageSize = CubeConstants.CUBE_SQL_BATCH_QUERY_SIZE;
        long pageNum = counts / pageSize + (counts % pageSize == 0 ? 0 : 1);
        for (int i = 1 ; i <= pageNum ; i++){
            collectorDOList.addAll(collectorDAO.listBatch(pageSize * (i - 1),pageSize));
        }
        return  collectorDOList;
    }

    public String getCollectorIdByMetricsId(String metricsId){
        return collectorMetricsDAO.getCollectorIdByMetricsId(metricsId);
    }
    public  CollectorDO getCollectorById(String id){
        return collectorDAO.getCollectorById(id);
    }


    public int insertCollectorField(CollectorFieldDO collectorFieldDO){
        if (collectorFieldDO.getFieldSchema()==null){
            collectorFieldDO.setFieldSchema("");
        }
        return  collectorFieldDAO.insertCollectorField(collectorFieldDO);
    }

    public CollectorMetricsDO getCollectorIdMetricsIdAndCollectorId(String metricsId, String collectorId){
        return collectorMetricsDAO.getCollectorIdMetricsIdAndCollectorId(metricsId,collectorId);
    }

    public void delCollector(String id) {
        collectorDAO.deleteById(id);
    }

    public void delCollectorMetricsByCollectorId(String id) {
        collectorMetricsDAO.deleteByCollectorId(id);
    }

    public void delFieldByCollectorIdAndName(String collectorId, List<String> fieldsToDelete) {
        if (CollectionUtils.isEmpty(fieldsToDelete)){
            return;
        }
        collectorFieldDAO.delFieldByCollectorIdAndName(collectorId,fieldsToDelete);
    }

    public int batchUpdateSchema(List<CollectorFieldDO> collectorFieldUpdateList) {
        if (CollectionUtils.isEmpty(collectorFieldUpdateList)){
            return 0;
        }
        return collectorFieldDAO.batchUpdateSchema(collectorFieldUpdateList);
    }

    public void batchUpdateDescription(List<CollectorFieldDO> collectorFieldDO) {
        if (CollectionUtils.isEmpty(collectorFieldDO)) {
            return;
        }
        collectorFieldDAO.batchUpdateDescription(collectorFieldDO);
    }
}