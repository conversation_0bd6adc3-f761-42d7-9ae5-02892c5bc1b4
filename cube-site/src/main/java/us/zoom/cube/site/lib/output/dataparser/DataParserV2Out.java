package us.zoom.cube.site.lib.output.dataparser;

import lombok.Data;
import us.zoom.cube.lib.hub.DataParserRawDataTypeEnum;
import us.zoom.infra.dao.model.DataParserDO;

import java.util.Date;

/**
 * @Author: luis.zheng
 * @Date: 2021/8/20 10:03
 */
@Data
public class DataParserV2Out {
    private String id;
    private String name;
    private String remark;
    private String tenantId;

    /**
     * @see DataParserRawDataTypeEnum#getCode()
     */
    private Integer rawDataType;
    /**
     * @see us.zoom.cube.lib.hub.RawDataParseTypeEnum
     */
    private String rawDataParseType;
    private String invokeFunction;
    private String rawDataParseRule;
    /**
     * @see us.zoom.cube.lib.common.DataParserUseStatusEnum
     */
    private Integer useStatus;

    private String topicTemplate;
    private Date gmtCreate;
    private Date gmtModify;

    public void transfer(DataParserDO dataParserDO) {
        this.id = dataParserDO.getId();
        this.name = dataParserDO.getName();
        this.remark = dataParserDO.getRemark();
        this.tenantId = dataParserDO.getTenantId();
        this.rawDataType = dataParserDO.getRawDataType();
        this.rawDataParseType = dataParserDO.getRawDataParseType();
        this.invokeFunction = dataParserDO.getInvokeFunction();
        this.rawDataParseRule = dataParserDO.getRawDataParseRule();
        this.useStatus = dataParserDO.getUseStatus();
        this.topicTemplate = dataParserDO.getTopicTemplate();
        this.gmtCreate = dataParserDO.getGmtCreate();
        this.gmtModify = dataParserDO.getGmtModify();
    }
}
