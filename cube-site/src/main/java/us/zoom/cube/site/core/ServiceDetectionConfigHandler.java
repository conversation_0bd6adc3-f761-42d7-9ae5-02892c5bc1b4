package us.zoom.cube.site.core;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import us.zoom.infra.dao.model.ServiceDetectionConfigDO;
import us.zoom.infra.dao.service.ServiceDetectionConfigDAO;

import java.util.List;
import java.util.Set;

@Component
public class ServiceDetectionConfigHandler {
    @Autowired
    private ServiceDetectionConfigDAO serviceDetectionConfigDAO;

    public List<ServiceDetectionConfigDO> listAll() {
        return serviceDetectionConfigDAO.listAll();
    }
    public List<ServiceDetectionConfigDO> listByService(String service) {
        return serviceDetectionConfigDAO.listByService(service);
    }

    public List<String> listService() {
        return serviceDetectionConfigDAO.listService();
    }
}
