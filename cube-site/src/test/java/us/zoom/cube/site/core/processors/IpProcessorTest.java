package us.zoom.cube.site.core.processors;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import us.zoom.geoip.client.City;
import us.zoom.geoip.client.ZoomGeoClient;

/**
 * @Author: <EMAIL>
 * @ModuleOwner: <EMAIL>
 * @Date: 01/22/2024 18:21
 * @Description:
 */
public class IpProcessorTest {
    @Test
    public void test() {
        City city = ZoomGeoClient.findCity("***********");
        City hz = ZoomGeoClient.findCity("*************");
        Assertions.assertEquals(hz.getCityName(), "Hangzhou");
    }
}
