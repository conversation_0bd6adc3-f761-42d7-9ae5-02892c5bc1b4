package us.zoom.cube.site.lib.input.migration;

import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;

import java.util.List;
import java.util.Map;

/**
 * @author: <PERSON><PERSON>g
 * @date: 03/08/2025 23:31
 * @desc:
 */
@Data
public class ServiceMigrationCheckInput {

    public static final String DEFAULT_ORDER_BY = "lastModifiedTime";

    //MMR/CMR
    private String serviceMonitorService;

    private String targetService;

    private String targetDataParser;

    private List<String> skipSourceMetrics;

    private Map<String, String> manualMatchMetrics;

    private String checkTagOrField = "";

    private Integer lastMinute;

    private Long startTime;

    private Long endTime;

    private String orderBy = DEFAULT_ORDER_BY;

    private int pageIndex = 1;

    private int pageSize;


    public void check() {
        Assert.isTrue(StringUtils.isNotBlank(serviceMonitorService), "serviceMonitorService is empty");
        Assert.isTrue(StringUtils.isNotBlank(targetService), "targetService is empty");
        Assert.isTrue(StringUtils.isNotBlank(targetDataParser), "target dataParser is empty");
        if (CollectionUtils.isNotEmpty(skipSourceMetrics) && MapUtils.isNotEmpty(manualMatchMetrics)) {
            manualMatchMetrics.forEach((key, value) -> {
                Assert.isTrue(!skipSourceMetrics.contains(key), "skipSourceMetrics and manualMatchMetrics should not have same source metrics");
            });
        }

        if (null != lastMinute) {
            Assert.isTrue(lastMinute > 3, "lastMinute should be large than 3 minutes, because we will query from lastMinute to latest 3 minutes");
        } else {
            Assert.notNull(startTime, "startTime is empty");
            Assert.notNull(endTime, "endTime is empty");
            Assert.isTrue(startTime < endTime, "startTime should be less than endTime");
        }
        if (pageIndex < 1) {
            pageIndex = 1;
        }

        if (StringUtils.isBlank(orderBy)) {
            orderBy = DEFAULT_ORDER_BY;
        } else {
            Assert.isTrue(DEFAULT_ORDER_BY.equals(orderBy) || "name".equals(orderBy), "orderBy should only be 'lastModifiedTime' or 'name'");
        }
    }

}
