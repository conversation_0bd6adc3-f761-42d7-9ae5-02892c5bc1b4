package us.zoom.cube.site.core;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.site.infra.utils.IdUtils;
import us.zoom.cube.site.lib.output.cmdb.CMDBDeviceInfo;
import us.zoom.infra.dao.model.CmdbNetdeviceDO;
import us.zoom.infra.dao.service.CmdbNetdeviceDAO;

import java.util.ArrayList;
import java.util.List;

@Component
public class CmdbDeviceHandler {

    @Autowired
    private CmdbNetdeviceDAO cmdbNetdeviceDAO;

    public void batchAddCmdbDevice(List<CMDBDeviceInfo> cmdbDeviceInfoBatch) {
        if (CollectionUtils.isEmpty(cmdbDeviceInfoBatch)){
            return;
        }
        List<CmdbNetdeviceDO> cmdbNetdeviceDOList = new ArrayList<>();
        for (CMDBDeviceInfo cmdbDeviceInfo : cmdbDeviceInfoBatch){
            CmdbNetdeviceDO cmdbNetdeviceDO = new CmdbNetdeviceDO();
            BeanUtils.copyProperties(cmdbDeviceInfo,cmdbNetdeviceDO);
            cmdbNetdeviceDO.setId(IdUtils.generateId());
            cmdbNetdeviceDO.setDeviceId(cmdbDeviceInfo.getId());
            cmdbNetdeviceDOList.add(cmdbNetdeviceDO);
        }
        cmdbNetdeviceDAO.batchAdd(cmdbNetdeviceDOList);
    }

    public List<CmdbNetdeviceDO> listAll() {
        return cmdbNetdeviceDAO.listAll();
    }

    public void batchUpdateCmdbDevice(List<CMDBDeviceInfo> cmdbDeviceInfoBatchUpdate) {
        if (CollectionUtils.isEmpty(cmdbDeviceInfoBatchUpdate)){
            return;
        }
        List<CmdbNetdeviceDO> deviceUpdate = new ArrayList<>();
        for (CMDBDeviceInfo cmdbDeviceInfo : cmdbDeviceInfoBatchUpdate){
            CmdbNetdeviceDO cmdbNetdeviceDO = new CmdbNetdeviceDO();
            BeanUtils.copyProperties(cmdbDeviceInfo,cmdbNetdeviceDO);
            cmdbNetdeviceDO.setDeviceId(cmdbDeviceInfo.getId());
            deviceUpdate.add(cmdbNetdeviceDO);
        }
        cmdbNetdeviceDAO.batchUpdate(deviceUpdate);
    }

    public void deleteByDeviceId(String deviceId) {
        cmdbNetdeviceDAO.deleteByDeviceId(deviceId);
    }
}
