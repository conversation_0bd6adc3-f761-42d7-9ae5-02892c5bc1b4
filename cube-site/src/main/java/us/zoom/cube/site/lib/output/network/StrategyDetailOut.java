package us.zoom.cube.site.lib.output.network;


import lombok.Data;

import java.util.List;

@Data
public class StrategyDetailOut {
    private String sourceDc;
    private String sourceService;
    private String sourceCluster;
    private String sourceIp;
    private String destDc;
    private String destService;
    private String destCluster;
    private List<String> destIps;
    private List<ServiceDetectionConfigOut> protocol;
/*    private int interval;
    private int hostCount;*/
}
