package us.zoom.cube.site.lib.monitor;

import lombok.Builder;
import lombok.Data;
import us.zoom.cube.sdk.annotation.CubeMonitorLog;
import us.zoom.cube.sdk.annotation.Field;
import us.zoom.cube.sdk.annotation.Tag;


@Data
@Builder
@CubeMonitorLog(measure = "load_dash_tenant_map")
public class LoadDashTenantMapMonitor {
    @Tag
    private String status;

    @Field
    private long cost;

    @Field
    private long costScanDashTenant;

    @Field
    private long costLoadUserDash;

    @Field
    private long costScanCardTenant;

}
