package us.zoom.cube.site.core;

import com.zoom.op.monitor.dao.alarm.AlarmDefinitionDao;
import com.zoom.op.monitor.domain.INameAndMetricId;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.site.api.intercept.AuthInterceptor;
import us.zoom.cube.site.biz.SysParaService;
import us.zoom.cube.site.core.config.ClickhouseHandlerFactory;
import us.zoom.cube.site.core.config.PiiTableCacheLoader;
import us.zoom.infra.clickhouse.ChTable;
import us.zoom.infra.clickhouse.ClickhouseConst;
import us.zoom.infra.clickhouse.ClickhouseSqlUtil;
import us.zoom.infra.dao.model.MetricsDO;
import us.zoom.infra.utils.CommonSplitConstants;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: <PERSON>ls Ding
 * @date: 2022/10/30 16:55
 * @desc:
 */
@Slf4j
@Component
public class AlarmIndexHelperHandler {

    @Autowired
    private SysParaService sysParaService;
    @Autowired
    private ClickhouseHandlerFactory clickhouseHandlerFactory;
    @Autowired
    private AlarmDefinitionDao alarmDefinitionDao;
    @Autowired
    private MetricsHandler metricsHandler;
    @Autowired
    private PiiTableCacheLoader piiTableLoader;

    public Set<String> getAlarmNamesByTimeRange(String serviceName, long beginSeconds, long endSeconds, boolean isNoticed) {
        return getAlarmNamesByTimeRange(serviceName, beginSeconds, endSeconds, isNoticed, true);
    }

    public Set<String> getAlarmNamesByTimeRange(String serviceName, long beginSeconds, long endSeconds, boolean isNoticed, boolean forceFromService) {
        String managerService = sysParaService.getManagerService();
        boolean queryManager = false;
        String dbName = null;
        if (StringUtils.isNotBlank(managerService) && !forceFromService) {
            queryManager = true;
            dbName = ClickhouseSqlUtil.encodeClickhouseName(managerService);
        } else {
            Assert.notNull(serviceName, "Can't find the manager service and appointed user service either!");
            queryManager = false;
            dbName = ClickhouseSqlUtil.encodeClickhouseName(serviceName);
        }

        Set<String> alarmsInTimeRange = new HashSet<>();
        final String getAlarmInTimeRangeSql = String.format("select distinct __alarmName as alarmName from %s.%s where 1=1 %s and time>=%d and time<=%d %s ",
                dbName,
                queryManager ? ClickhouseConst.ALARM_ALL_RECORD_TABLE_NAME : ClickhouseConst.ALARM_EACH_SERCICE_RECORD_TABLE_NAME,
                queryManager ? String.format(" and __serviceName='%s' ", serviceName) : StringUtils.EMPTY,
                beginSeconds,
                endSeconds,
                isNoticed ? " and __isNoticed='true'" : StringUtils.EMPTY);

        List<Map<String, Object>> alarmsInTimeRangeResult = clickhouseHandlerFactory.get().query(serviceName, getAlarmInTimeRangeSql);
        alarmsInTimeRangeResult.forEach(entry -> alarmsInTimeRange.add(entry.get("alarmName").toString()));
        return alarmsInTimeRange;
    }

    //Map<tagName,Set<Alarm1,Alarm2...>>
    public Map<String, Set<String>> mappingTagWithAlarmNames(String serviceId, String serviceName, List<MetricsDO> metrics, boolean skipPii) {
        Map<String, Set<String>> result = new HashMap<>();
        if (CollectionUtils.isEmpty(metrics)) {
            return result;
        }
        if (skipPii) {
            metrics = metrics.stream().filter(metric -> piiTableLoader.auth(new ChTable(serviceName, metric.getMetricsName()), AuthInterceptor.getUserId(), AuthInterceptor.getRealIp())).collect(Collectors.toList());
        }
        Map<String, List<String>> tagWithMetrics = mappingTagWithCubeMetricNames(metrics);
        Map<String, Set<String>> metricWithAlarms = mappingMetricsWithAlarms(serviceId, serviceName, metrics, skipPii);
        for (Map.Entry<String, List<String>> tagMetricEntry : tagWithMetrics.entrySet()) {
            final String tagName = tagMetricEntry.getKey();
            for (String metricName : tagMetricEntry.getValue()) {
                if (metricWithAlarms.containsKey(metricName)) {
                    result.computeIfAbsent(tagName, value -> new HashSet<>()).addAll(metricWithAlarms.get(metricName));
                }
            }
        }
        return result;
    }

    //Map<tagName,List<Metric1,Metric2...>>
    private Map<String, List<String>> mappingTagWithCubeMetricNames(List<MetricsDO> metricsByTenant) {
        Map<String, List<String>> tagWithMetrics = new HashMap<>();
        if (null == metricsByTenant || 0 == metricsByTenant.size()) {
            return tagWithMetrics;
        }
        for (MetricsDO metricsDO : metricsByTenant) {
            String[] tags = metricsDO.getTagNames().split(CommonSplitConstants.COMMA_SPLIT);
            for (String tag : tags) {
                if (!tagWithMetrics.containsKey(tag)) {
                    tagWithMetrics.put(tag, new ArrayList<>());
                }
                tagWithMetrics.get(tag).add(metricsDO.getMetricsName());
            }
        }
        return tagWithMetrics;
    }

    //Map<Metric,Set<Alarm1,Alarm2...>>
    private Map<String, Set<String>> mappingMetricsWithAlarms(String serviceId, String serviceName, List<MetricsDO> metricsList, boolean skipPii) {
        Map<String, Set<String>> metricAndAlarms = new HashMap<>();
        Map<String, String> metricIdAndNames = metricsList.stream().collect(Collectors.toMap(MetricsDO::getId, MetricsDO::getMetricsName));
        List<INameAndMetricId> alarmNameAndMetricIdList = alarmDefinitionDao.getAlarmNameAndMetricIdByTenant(serviceId);
        for (INameAndMetricId alarmNameAndMetricId : alarmNameAndMetricIdList) {
            final String metricName = metricIdAndNames.get(alarmNameAndMetricId.getMetricId());
            if (!metricAndAlarms.containsKey(metricName)) {
                if(skipPii && !piiTableLoader.auth(new ChTable(serviceName, metricName), AuthInterceptor.getUserId(), AuthInterceptor.getRealIp())) {
                    continue;
                }
                metricAndAlarms.put(metricName, new HashSet<>());
            }
            metricAndAlarms.get(metricName).add(alarmNameAndMetricId.getName());
        }
        return metricAndAlarms;
    }

    //Map<AlarmName,Metric>
    public Map<String, MetricsDO> mappingAlarmWithMetric(String serviceId) {
        List<MetricsDO> metrics = metricsHandler.getMetricsByTenant(serviceId);
        return mappingAlarmWithMetric(serviceId, metrics);
    }

    //Map<AlarmName,Metric>
    public Map<String, MetricsDO> mappingAlarmWithMetric(String serviceId, List<MetricsDO> metrics) {
        final Map<String, MetricsDO> alarmNameAndMetric = new HashMap<>();
        if (CollectionUtils.isEmpty(metrics)) {
            return alarmNameAndMetric;
        }
        Map<String, MetricsDO> metricIdAndNames = metrics.stream().collect(Collectors.toMap(MetricsDO::getId, v -> v));
        alarmDefinitionDao.getAlarmNameAndMetricIdByTenant(serviceId).forEach(alarmNameMetricId -> {
            if (metricIdAndNames.containsKey(alarmNameMetricId.getMetricId())) {
                alarmNameAndMetric.put(alarmNameMetricId.getName(), metricIdAndNames.get(alarmNameMetricId.getMetricId()));
            }
        });
        return alarmNameAndMetric;
    }
}
