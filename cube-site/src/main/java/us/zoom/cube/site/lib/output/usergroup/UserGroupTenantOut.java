package us.zoom.cube.site.lib.output.usergroup;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @Author: <PERSON>
 * @ModuleOwner: <PERSON>
 * @Date:12/19/2022 16:39
 * @Description:
 */
@Data
public class UserGroupTenantOut {
    private String tenantId;

    private String name;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date modifyTime;


}
