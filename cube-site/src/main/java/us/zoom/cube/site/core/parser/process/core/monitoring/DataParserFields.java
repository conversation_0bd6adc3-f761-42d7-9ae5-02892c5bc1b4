package us.zoom.cube.site.core.parser.process.core.monitoring;

import com.tdunning.math.stats.MergingDigest;
import com.tdunning.math.stats.TDigest;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import us.zoom.cube.site.core.parser.process.core.common.constant.MonitoringConst;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

@Data
@Builder
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
public class DataParserFields extends CommonFields {

    private AtomicInteger consumerFailSize = new AtomicInteger();

    private AtomicInteger preSize = new AtomicInteger();
    private AtomicInteger preTotalCost = new AtomicInteger();
    private AtomicInteger preMaxCost = new AtomicInteger();

    private AtomicInteger preFailSize = new AtomicInteger();
    private AtomicInteger preLogFailSize = new AtomicInteger();

    private AtomicInteger preMeasureFailSize = new AtomicInteger();
    private AtomicInteger preJsonFailSize = new AtomicInteger();

    private AtomicInteger filter = new AtomicInteger();
    private AtomicInteger filterTotalCost = new AtomicInteger();

    private AtomicInteger filterFail = new AtomicInteger();
    private AtomicInteger filterFailTotalCost = new AtomicInteger();

    private AtomicInteger sendAlarmSize = new AtomicInteger();
    private AtomicInteger sendAlarmTotalMsgSize = new AtomicInteger();
    private AtomicInteger sendAlarmTotalCost = new AtomicInteger();
    private AtomicInteger sendAlarmMaxCost = new AtomicInteger();

    private AtomicInteger sendAlarmFailSize = new AtomicInteger();
    private AtomicInteger sendAlarmFailTotalMsgSize = new AtomicInteger();
    private AtomicInteger sendAlarmFailTotalCost = new AtomicInteger();
    private AtomicInteger sendAlarmFailMaxCost = new AtomicInteger();

    private AtomicInteger sendCalcSize = new AtomicInteger();
    private AtomicInteger sendCalcTotalMsgSize = new AtomicInteger();
    private AtomicInteger sendCalcTotalCost = new AtomicInteger();
    private AtomicInteger sendCalcMaxCost = new AtomicInteger();

    private AtomicInteger sendCalcFailSize = new AtomicInteger();
    private AtomicInteger sendCalcFailTotalMsgSize = new AtomicInteger();
    private AtomicInteger sendCalcFailTotalCost = new AtomicInteger();
    private AtomicInteger sendCalcFailMaxCost = new AtomicInteger();

    private AtomicInteger jsonSize = new AtomicInteger();
    private AtomicInteger agentJsonSize = new AtomicInteger();
    private AtomicInteger standardSize = new AtomicInteger();
    private AtomicInteger agentStandardSize = new AtomicInteger();
    private AtomicInteger otherSize = new AtomicInteger();

    /**
     * PipelineHandler
     */
    private AtomicInteger pipelineSize = new AtomicInteger();
    private AtomicInteger pipelineFailSize = new AtomicInteger();
    private AtomicInteger pipelineFilterSize = new AtomicInteger();
    private TDigest pipelineDelay = new MergingDigest(MonitoringConst.PERCENTILE_COMPRESSION);
    private AtomicInteger pipelineDelayTotal = new AtomicInteger();
    private AtomicInteger pipelineNotTsSize = new AtomicInteger();
    private AtomicInteger futureTime = new AtomicInteger();
    private AtomicInteger aggNotUsed = new AtomicInteger();
    private AtomicInteger revertPii = new AtomicInteger();

    private AtomicInteger processorSize = new AtomicInteger();
    private AtomicInteger processorFailSize = new AtomicInteger();
    private AtomicInteger processorNotFoundSize = new AtomicInteger();

    public void incrJsonSize() {
        jsonSize.incrementAndGet();
    }

    public void incrAgentJsonSize() {
        agentJsonSize.incrementAndGet();
    }

    public void incrStandardSize() {
        standardSize.incrementAndGet();
    }

    public void incrAgentStandardSize() {
        agentStandardSize.incrementAndGet();
    }

    public void incrOtherSize() {
        otherSize.incrementAndGet();
    }

    public void incrConsumerFailSize() {
        this.consumerFailSize.incrementAndGet();
    }

    public void incrPreCost(int cost) {
        preTotalCost.getAndAdd(cost);
        preSize.incrementAndGet();
        if (cost > preMaxCost.get()) {
            preMaxCost.set(cost);
        }
    }

    public void incrPreFailSize() {
        preFailSize.incrementAndGet();
    }

    public void incrPreLogFailSize() {
        preLogFailSize.incrementAndGet();
    }

    public void incrPreMeasureFailSize() {
        preMeasureFailSize.incrementAndGet();
    }

    public void incrPreJsonFailSize() {
        preJsonFailSize.incrementAndGet();
    }

    public void statSendAlarm(int cost, int msgSize) {
        sendAlarmTotalCost.getAndAdd(cost);
        sendAlarmSize.incrementAndGet();
        if (cost > sendAlarmMaxCost.get()) {
            sendAlarmMaxCost.set(cost);
        }
        sendAlarmTotalMsgSize.getAndAdd(msgSize);
    }

    public void statSendAlarmFail(int cost, int msgSize) {
        sendAlarmFailTotalCost.getAndAdd(cost);
        sendAlarmFailSize.incrementAndGet();
        if (cost > sendAlarmFailMaxCost.get()) {
            sendAlarmFailMaxCost.set(cost);
        }
        sendAlarmFailTotalMsgSize.getAndAdd(msgSize);
    }

    public void statSendCalc(int cost, int msgSize) {
        sendCalcTotalCost.getAndAdd(cost);
        sendCalcSize.incrementAndGet();
        if (cost > sendCalcMaxCost.get()) {
            sendCalcMaxCost.set(cost);
        }
        sendCalcTotalMsgSize.getAndAdd(msgSize);
    }

    public void statSendCalcFail(int cost, int msgSize) {
        sendCalcFailTotalCost.getAndAdd(cost);
        sendCalcFailSize.incrementAndGet();
        if (cost > sendCalcFailMaxCost.get()) {
            sendCalcFailMaxCost.set(cost);
        }
        sendCalcFailTotalMsgSize.getAndAdd(msgSize);
    }

    public void incrFilterFailCost(int cost) {
        this.filterFail.incrementAndGet();
        this.filterFailTotalCost.addAndGet(cost);
    }

    public void incrFilterCost(int cost) {
        this.filter.incrementAndGet();
        this.filterTotalCost.addAndGet(cost);
    }

    /**
     * PipelineHandler method
     */
    public void incrPipelineSize() {
        this.pipelineSize.incrementAndGet();
    }

    public void incrPipelineFailSize() {
        this.pipelineFailSize.incrementAndGet();
    }

    public void incrPipelineFilterSize() {
        this.pipelineFilterSize.incrementAndGet();
    }

    public void pipelineDelay(int delay) {
        synchronized (super.getLock()) {
            pipelineDelay.add(delay);
        }
        pipelineDelayTotal.addAndGet(delay);
    }

    public void incrProcessorSize() {
        this.processorSize.incrementAndGet();
    }

    public void incrProcessorFailSize() {
        this.processorFailSize.incrementAndGet();
    }

    public void incrProcessorNotFoundSize() {
        this.processorNotFoundSize.incrementAndGet();
    }

    public void incrPipelineNotTsSize() {
        this.pipelineNotTsSize.incrementAndGet();
    }

    public void incrFutureTime() {
        this.futureTime.incrementAndGet();
    }

    public void incrAggNotUsed() {
        this.aggNotUsed.incrementAndGet();
    }

    public void incrRevertPii() {
        this.revertPii.incrementAndGet();
    }

    public Map<String, Object> toMap() {
        synchronized (super.getLock()) {
            Map<String, Object> metricsMap = new HashMap<>();
            metricsMap.put("consumerFail", consumerFailSize.get());

            metricsMap.put("pre", preSize.get());
            metricsMap.put("preAvg", super.div(preTotalCost.get(), preSize.get()));
            metricsMap.put("preMax", preMaxCost.get());

            metricsMap.put("preFail", preFailSize.get());
            metricsMap.put("preLogFail", preLogFailSize.get());
            metricsMap.put("preMeasureFail", preMeasureFailSize.get());
            metricsMap.put("preJsonFail", preJsonFailSize.get());

            metricsMap.put("filter", filter.get());
            metricsMap.put("filterAvg", super.div(filterTotalCost.get(), filter.get()));

            metricsMap.put("filterFail", filterFail.get());
            metricsMap.put("filterFailAvg", super.div(filterFailTotalCost.get(), filterFail.get()));

            metricsMap.put("sendAlarm", sendAlarmSize.get());
            metricsMap.put("sendAlarmMax", sendAlarmMaxCost.get());
            metricsMap.put("sendAlarmAvg", super.div(sendAlarmTotalCost.get(), sendAlarmSize.get()));
            metricsMap.put("sendAlarmMsgSize", sendAlarmTotalMsgSize.get());

            metricsMap.put("sendAlarmFail", sendAlarmFailSize.get());
            metricsMap.put("sendAlarmFailMax", sendAlarmFailMaxCost.get());
            metricsMap.put("sendAlarmFailAvg", super.div(sendAlarmFailTotalCost.get(), sendAlarmFailSize.get()));
            metricsMap.put("sendAlarmFailMsgSize", sendAlarmFailTotalMsgSize.get());

            metricsMap.put("sendCalc", sendCalcSize.get());
            metricsMap.put("sendCalcMax", sendCalcMaxCost.get());
            metricsMap.put("sendCalcAvg", super.div(sendCalcTotalCost.get(), sendCalcSize.get()));
            metricsMap.put("sendCalcMsgSize", sendCalcTotalMsgSize.get());

            metricsMap.put("sendCalcFail", sendCalcFailSize.get());
            metricsMap.put("sendCalcFailMax", sendCalcFailMaxCost.get());
            metricsMap.put("sendCalcFailAvg", super.div(sendCalcFailTotalCost.get(), sendCalcFailSize.get()));
            metricsMap.put("sendCalcFailMsgSize", sendCalcFailTotalMsgSize.get());

            metricsMap.put("pl", pipelineSize.get());
            metricsMap.put("plFail", pipelineFailSize.get());
            metricsMap.put("plFilter", pipelineFilterSize.get());
            metricsMap.put("plDelayP99", super.parseDouble(pipelineDelay.quantile(0.99)));
            metricsMap.put("plDelayP95", super.parseDouble(pipelineDelay.quantile(0.95)));
            metricsMap.put("plDelayP90", super.parseDouble(pipelineDelay.quantile(0.90)));
            metricsMap.put("plDelayP75", super.parseDouble(pipelineDelay.quantile(0.75)));
            metricsMap.put("plDelayP50", super.parseDouble(pipelineDelay.quantile(0.50)));
            metricsMap.put("plDelayP30", super.parseDouble(pipelineDelay.quantile(0.30)));
            metricsMap.put("plDelayP10", super.parseDouble(pipelineDelay.quantile(0.10)));
            metricsMap.put("plDelayAvg", super.div(pipelineDelayTotal.get(), (int) pipelineDelay.size()));
            metricsMap.put("plNotTs", pipelineNotTsSize.get());


            metricsMap.put("ps", processorSize.get());
            metricsMap.put("psFail", processorFailSize.get());
            metricsMap.put("psNotFound", processorNotFoundSize.get());

            metricsMap.put("json", jsonSize.get());
            metricsMap.put("agentJson", agentJsonSize.get());
            metricsMap.put("standard", standardSize.get());
            metricsMap.put("agentStandard", agentStandardSize.get());
            metricsMap.put("other", otherSize.get());

            metricsMap.put("ftTime", futureTime.get());
            metricsMap.put("aggNotUsed", aggNotUsed.get());
            metricsMap.put("revertPii", revertPii.get());

            metricsMap.putAll(super.toMap());
            return metricsMap;
        }
    }
}
