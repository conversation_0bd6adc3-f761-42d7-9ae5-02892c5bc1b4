package us.zoom.cube.site.lib.output.dataparser;

public class EncryptionProcessorOut extends BaseProcessorOut {
    private String fields;

    public EncryptionProcessorOut() {

    }

    public EncryptionProcessorOut(String id, String name, Integer order, String dataParserPipelineId, String dataParserId, String type, String fields) {
        super(id, name, order, dataParserPipelineId, dataParserId, type);
        this.fields = fields;
    }

    public String getFields() {
        return fields;
    }

    public void setFields(String fields) {
        this.fields = fields;
    }
}
