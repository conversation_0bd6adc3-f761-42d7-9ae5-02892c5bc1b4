package us.zoom.cube.site.lib.output.template;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class TemplateUserViewV2Output {
    private String id;

    private String name;

    private List<Item> showItemList;

    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class Item {

        private String type;

        private List<SubItem> metricsNameList;

        private List<SubItem> alarmNameList;

        private List<SubItem> dashboardNameList;

        @Data
        @JsonInclude(JsonInclude.Include.NON_NULL)
        public static class SubItem {
            private String id;

            private String name;

            private String description;

            private Boolean status;

            private String documentLink;
        }

    }
}
