package us.zoom.cube.site.core;

import com.fasterxml.jackson.core.type.TypeReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.site.infra.utils.IdUtils;
import us.zoom.cube.site.lib.input.network.DetectionConfigInput;
import us.zoom.infra.dao.model.*;
import us.zoom.infra.dao.service.*;
import us.zoom.infra.enums.DetectionTypeEnum;
import us.zoom.cube.lib.utils.JsonUtils;


import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Component
@Transactional
public class NetworkStrategyDetectionHandler {
    @Autowired
    private NetworkDetectionInfoDAO networkDetectionInfoDAO;

    @Autowired
    private CmdbServerDAO cmdbServerDAO;

    @Autowired
    private NetworkStrategyDAO networkStrategyDAO;

    @Autowired
    private NetworkStrategyDetectionRelationDAO networkStrategyDetectionRelationDAO;

    @Autowired
    private ServiceDetectionConfigDAO serviceDetectionConfigDAO;

    @Autowired
    private NetworkStrategyAgentRelationDAO networkStrategyAgentRelationDAO;


    @Transactional(rollbackFor = Exception.class)
    public void addStrategyAndDetection(List<NetworkDetectionInfoDO> networkDetectionTOAdd, List<NetworkDetectionInfoDO> networkDetectionTOUpdate, NetworkStrategyDO networkStrategyDO, List<CmdbServerDO> cmdbServerTOUpdate, List<NetworkStrategyDetectionRelationDO> networkStrategyDetectionRelationList) throws Exception {
        // networkStrategyDAO.insert(networkStrategyDO);
        List<NetworkStrategyAgentRelationDO> strategyAgentRelationDOS = makeStrategyAgentRelation(networkStrategyDO.getId(),networkStrategyDO.getSourceAgents(), DetectionTypeEnum.source);
        strategyAgentRelationDOS.addAll(makeStrategyAgentRelation(networkStrategyDO.getId(),networkStrategyDO.getDestAgents(), DetectionTypeEnum.dest));
        networkStrategyAgentRelationDAO.batchAdd(strategyAgentRelationDOS);
        if (!CollectionUtils.isEmpty(networkDetectionTOAdd)) {
            networkDetectionInfoDAO.insertBatch(networkDetectionTOAdd);
        }

        if (!CollectionUtils.isEmpty(networkDetectionTOUpdate)) {
            networkDetectionInfoDAO.updateBatch(networkDetectionTOUpdate);
        }
        if (!CollectionUtils.isEmpty(cmdbServerTOUpdate)) {
            cmdbServerDAO.updateLoadRateBatch(cmdbServerTOUpdate);
        }
        if (!CollectionUtils.isEmpty(networkStrategyDetectionRelationList)) {
            networkStrategyDetectionRelationDAO.insertBatch(networkStrategyDetectionRelationList);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteStrategyAndDetection(NetworkStrategyDO networkStrategyDO, List<NetworkDetectionInfoDO> detectionListToDelete, List<NetworkDetectionInfoDO> detectionListToUpdate, List<CmdbServerDO> cmdbServerDOList) throws Exception {
        // networkStrategyDAO.deleteById(networkStrategyDO.getId());
        networkStrategyAgentRelationDAO.deleteByStrategy(networkStrategyDO.getId());
        networkStrategyDetectionRelationDAO.deleteByStrategy(networkStrategyDO.getId());
        if (!CollectionUtils.isEmpty(detectionListToDelete)) {
            networkDetectionInfoDAO.deleteBatch(detectionListToDelete.stream().map(item -> item.getId()).collect(Collectors.toList()));
        }
        if (!CollectionUtils.isEmpty(detectionListToUpdate)) {
            networkDetectionInfoDAO.updateBatch(detectionListToUpdate);
        }
        if (!CollectionUtils.isEmpty(cmdbServerDOList)) {
            cmdbServerDAO.updateLoadRateBatch(cmdbServerDOList);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateIntervalStrategyAndDetection(NetworkStrategyDO networkStrategyDO, List<NetworkDetectionInfoDO> detectionToUpdate, List<CmdbServerDO> cmdbServerToUpdate) {
        networkStrategyDAO.updateIntervalById(networkStrategyDO);
        if (!CollectionUtils.isEmpty(detectionToUpdate)) {
            networkDetectionInfoDAO.updateBatch(detectionToUpdate);
        }
        if (!CollectionUtils.isEmpty(cmdbServerToUpdate)) {
            cmdbServerDAO.updateLoadRateBatch(cmdbServerToUpdate);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void reduceHostCountAndDetection(NetworkStrategyDO strategy, List<NetworkDetectionInfoDO> detectionListToReduce, List<NetworkDetectionInfoDO> detectionListToDelete, List<CmdbServerDO> cmdbServerToUpdate) {
        networkStrategyDAO.updateHostCountAndAgents(strategy);
        networkStrategyAgentRelationDAO.deleteByStrategy(strategy.getId());
        List<NetworkStrategyAgentRelationDO> strategyAgentRelationDOS = makeStrategyAgentRelation(strategy.getId(),strategy.getSourceAgents(), DetectionTypeEnum.source);
        strategyAgentRelationDOS.addAll(makeStrategyAgentRelation(strategy.getId(),strategy.getDestAgents(), DetectionTypeEnum.dest));
        networkStrategyAgentRelationDAO.batchAdd(strategyAgentRelationDOS);
        if (!CollectionUtils.isEmpty(detectionListToReduce)) {
            networkStrategyDetectionRelationDAO.deleteByStrategyAndDetection(strategy.getId(), detectionListToReduce.stream().map(item -> item.getId()).collect(Collectors.toList()));
        }
        if (!CollectionUtils.isEmpty(detectionListToDelete)) {
            networkDetectionInfoDAO.deleteBatch(detectionListToDelete.stream().map(item -> item.getId()).collect(Collectors.toList()));
        }
        if (!CollectionUtils.isEmpty(cmdbServerToUpdate)) {
            cmdbServerDAO.updateLoadRateBatch(cmdbServerToUpdate);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void addHostCountStrategyAndDetection(List<NetworkDetectionInfoDO> networkDetectionTOAdd, List<NetworkDetectionInfoDO> networkDetectionTOUpdate, NetworkStrategyDO strategy, List<CmdbServerDO> cmdbServerTOUpdate, List<NetworkStrategyDetectionRelationDO> networkStrategyDetectionRelationList) {
        networkStrategyDAO.updateHostCountAndAgents(strategy);
        networkStrategyAgentRelationDAO.deleteByStrategy(strategy.getId());
        List<NetworkStrategyAgentRelationDO> strategyAgentRelationDOS = makeStrategyAgentRelation(strategy.getId(),strategy.getSourceAgents(), DetectionTypeEnum.source);
        strategyAgentRelationDOS.addAll(makeStrategyAgentRelation(strategy.getId(),strategy.getDestAgents(), DetectionTypeEnum.dest));
        networkStrategyAgentRelationDAO.batchAdd(strategyAgentRelationDOS);
        if (!CollectionUtils.isEmpty(networkDetectionTOAdd)) {
            networkDetectionInfoDAO.insertBatch(networkDetectionTOAdd);
        }

        if (!CollectionUtils.isEmpty(networkDetectionTOUpdate)) {
            networkDetectionInfoDAO.updateBatch(networkDetectionTOUpdate);
        }
        if (!CollectionUtils.isEmpty(cmdbServerTOUpdate)) {
            cmdbServerDAO.updateLoadRateBatch(cmdbServerTOUpdate);
        }
        if (!CollectionUtils.isEmpty(networkStrategyDetectionRelationList)) {
            networkStrategyDetectionRelationDAO.insertBatch(networkStrategyDetectionRelationList);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteServiceConfig(List<NetworkDetectionInfoDO> networkDetectionListToDelete, List<CmdbServerDO> cmdbServerListToUpdate, DetectionConfigInput detectionConfigInput) {
        if (!CollectionUtils.isEmpty(networkDetectionListToDelete)) {
            networkDetectionInfoDAO.deleteBatch(networkDetectionListToDelete.stream().map(item -> item.getId()).collect(Collectors.toList()));
            networkStrategyDetectionRelationDAO.deleteByDetectionIds(networkDetectionListToDelete.stream().map(item -> item.getId()).collect(Collectors.toList()));
        }
        if (!CollectionUtils.isEmpty(cmdbServerListToUpdate)) {
            cmdbServerDAO.updateLoadRateBatch(cmdbServerListToUpdate);
        }
        serviceDetectionConfigDAO.deleteById(detectionConfigInput.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    public void addServiceConfig(List<NetworkDetectionInfoDO> networkDetectionTOAdd, List<NetworkStrategyDetectionRelationDO> strategyDetectionRelationListToAdd, List<CmdbServerDO> cmdbServerListToUpdate, DetectionConfigInput detectionConfigInput) {
        if (!CollectionUtils.isEmpty(networkDetectionTOAdd)) {
            networkDetectionInfoDAO.insertBatch(networkDetectionTOAdd);
        }
        if (!CollectionUtils.isEmpty(strategyDetectionRelationListToAdd)){
            networkStrategyDetectionRelationDAO.insertBatch(strategyDetectionRelationListToAdd);
        }
        if (!CollectionUtils.isEmpty(cmdbServerListToUpdate)) {
            cmdbServerDAO.updateLoadRateBatch(cmdbServerListToUpdate);
        }
        ServiceDetectionConfigDO detectionConfigDO = new ServiceDetectionConfigDO();
        detectionConfigDO.setId(detectionConfigInput.getId());
        detectionConfigDO.setService(detectionConfigInput.getService());
        detectionConfigDO.setProtocol(detectionConfigInput.getProtocol());
        detectionConfigDO.setPort(detectionConfigInput.getPort());
        serviceDetectionConfigDAO.insert(detectionConfigDO);
    }


    private List<NetworkStrategyAgentRelationDO> makeStrategyAgentRelation(String id, String agents, DetectionTypeEnum type) {
        List<NetworkStrategyAgentRelationDO> agentRelationDOList = new ArrayList<>();
        List<String> agentList = JsonUtils.toObjectByTypeRef(agents, new TypeReference<List<String>>() {});
        for (String agentId : agentList){
            NetworkStrategyAgentRelationDO agentRelationDO = new NetworkStrategyAgentRelationDO();
            agentRelationDO.setId(IdUtils.generateId());
            agentRelationDO.setStrategyId(id);
            agentRelationDO.setAgentId(agentId);
            agentRelationDO.setType(type.getValue());
            agentRelationDOList.add(agentRelationDO);
        }
        return agentRelationDOList;
    }

}
