package us.zoom.cube.site.lib.output.alarm;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

/**
 * <AUTHOR>
 * @create 2020/8/12 4:55 PM
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AlarmRuleSingleContentOutput {

    /**
     * {@link us.zoom.infra.model.alarm.CompareType}
     */
    int type;

    String tag;

    String field;

    /**
     * number, string
     */
    String fieldType;

    /**
     * For map type fields, this field stores the key of the map
     */
    String mapKey;

    /**
     * ==, >, >=, <=, crIncrease, crDecrease
     */
    String operator;

    /**
     * Comparing value with the field value using the operator
     */
    String cmpValue;

    /**
     * Used for defining expressions, e.g, customized functions
     */
    String expression;
}
