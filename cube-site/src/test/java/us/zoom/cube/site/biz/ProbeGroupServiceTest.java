package us.zoom.cube.site.biz;

import com.google.common.collect.Lists;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.site.core.ProbeGroupHandler;
import us.zoom.cube.site.core.ProbeGroupServerHandler;
import us.zoom.cube.site.infra.utils.IdUtils;
import us.zoom.cube.site.lib.IdPara;
import us.zoom.cube.site.lib.ResponseObject;
import us.zoom.cube.site.lib.input.ProbeGroupInput;
import us.zoom.cube.site.lib.output.PageResult;
import us.zoom.cube.site.lib.query.PageQuery;
import us.zoom.cube.site.lib.query.ProbeGroupQuery;
import us.zoom.cube.site.lib.query.ProbePointQuery;
import us.zoom.infra.dao.model.ProbeGroupDO;
import us.zoom.infra.dao.model.ProbePointDO;
import us.zoom.infra.enums.CommonStatusEnum;
import us.zoom.cube.lib.utils.JsonUtils;

import java.util.List;

import static org.mockito.ArgumentMatchers.*;

/**
 * @Author: <EMAIL>
 * @ModuleOwner: <EMAIL>
 * @Date: 07/01/2022 14:37
 * @Description:
 */
public class ProbeGroupServiceTest {
    private ProbeGroupService probeGroupService;

    private ProbeGroupHandler probeGroupHandler;
    private ProbeGroupServerHandler probeGroupServerHandler;
    private AuthService authService;

    @BeforeEach
    public void before() {
        probeGroupHandler = Mockito.mock(ProbeGroupHandler.class);
        authService = Mockito.mock(AuthService.class);
        probeGroupServerHandler = Mockito.mock(ProbeGroupServerHandler.class);
        probeGroupService = new ProbeGroupService(probeGroupHandler, probeGroupServerHandler, authService);
    }

    String id = IdUtils.generateId();

    @Test
    public void testSearch() {
        ProbeGroupQuery probeGroupQuery = new ProbeGroupQuery();
        probeGroupQuery.setId(id);
        probeGroupQuery.setName("name");
        probeGroupQuery.setCluster("us01");
        probeGroupQuery.setRegion("va1");
        probeGroupQuery.setCity("name");
        probeGroupQuery.setCloud("name");
        probeGroupQuery.setCountry("name");
        probeGroupQuery.setOperator("name");
        probeGroupQuery.setTag("e2e");
        probeGroupQuery.setStatus(CommonStatusEnum.enable.name());
        PageQuery<ProbeGroupQuery> query = new PageQuery<>();
        query.setQueryPara(probeGroupQuery);

        Mockito.when(probeGroupHandler.findByParam(
                probeGroupQuery.getId(),
                probeGroupQuery.getName(),
                probeGroupQuery.getCluster(),
                probeGroupQuery.getRegion(),
                probeGroupQuery.getCloud(),
                probeGroupQuery.getCountry(),
                probeGroupQuery.getCity(),
                probeGroupQuery.getOperator(),
                probeGroupQuery.getTag(),
                probeGroupQuery.getStatus(),
                query.getPageIndex(), query.getPageSize()
        )).thenReturn(Lists.newArrayList());

        Mockito.when(probeGroupHandler.getCountByParam(
                probeGroupQuery.getId(),
                probeGroupQuery.getName(),
                probeGroupQuery.getCluster(),
                probeGroupQuery.getRegion(),
                probeGroupQuery.getCloud(),
                probeGroupQuery.getCountry(),
                probeGroupQuery.getCity(),
                probeGroupQuery.getOperator(),
                probeGroupQuery.getTag(),
                probeGroupQuery.getStatus()
        )).thenReturn(0);

        ResponseObject<PageResult<ProbeGroupDO>> responseObject = probeGroupService.searchProbeGroup(query);
        Assertions.assertEquals(CollectionUtils.isEmpty(responseObject.getData().getItems()), true);
        Assertions.assertEquals(responseObject.getData().getTotal(), 0);
    }


    @Test
    public void testSearch1() {
        ProbeGroupQuery probeGroupQuery = new ProbeGroupQuery();
        probeGroupQuery.setId(id);
        probeGroupQuery.setName("name");
        probeGroupQuery.setCluster("us01");
        probeGroupQuery.setRegion("va1");
        probeGroupQuery.setCity("name");
        probeGroupQuery.setCloud("name");
        probeGroupQuery.setCountry("name");
        probeGroupQuery.setOperator("name");
        probeGroupQuery.setTag("e2e");
        probeGroupQuery.setStatus(CommonStatusEnum.enable.name());
        PageQuery<ProbeGroupQuery> query = new PageQuery<>();
        query.setQueryPara(probeGroupQuery);

        ProbeGroupDO probeGroupDO = new ProbeGroupDO();
        probeGroupDO.setTags("*aa*bb*cc*");
        Mockito.when(probeGroupHandler.findByParam(
                probeGroupQuery.getId(),
                probeGroupQuery.getName(),
                probeGroupQuery.getCluster(),
                probeGroupQuery.getRegion(),
                probeGroupQuery.getCloud(),
                probeGroupQuery.getCountry(),
                probeGroupQuery.getCity(),
                probeGroupQuery.getOperator(),
                probeGroupQuery.getTag(),
                probeGroupQuery.getStatus(),
                query.getPageIndex(), query.getPageSize()
        )).thenReturn(Lists.newArrayList(probeGroupDO, new ProbeGroupDO(), new ProbeGroupDO(),
                new ProbeGroupDO(), new ProbeGroupDO(), new ProbeGroupDO(), new ProbeGroupDO(),
                new ProbeGroupDO(), new ProbeGroupDO(), new ProbeGroupDO()));

        Mockito.when(probeGroupHandler.getCountByParam(
                probeGroupQuery.getId(),
                probeGroupQuery.getName(),
                probeGroupQuery.getCluster(),
                probeGroupQuery.getRegion(),
                probeGroupQuery.getCloud(),
                probeGroupQuery.getCountry(),
                probeGroupQuery.getCity(),
                probeGroupQuery.getOperator(),
                probeGroupQuery.getTag(),
                probeGroupQuery.getStatus()
        )).thenReturn(20);

        ResponseObject<PageResult<ProbeGroupDO>> responseObject = probeGroupService.searchProbeGroup(query);
        Assertions.assertEquals(responseObject.getData().getItems().size(), 10);
        Assertions.assertEquals(responseObject.getData().getTotal(), 20);
    }

    @Test
    public void testSearchAllProbeGroups() {
        ProbeGroupDO probeGroupDO1 = new ProbeGroupDO();
        probeGroupDO1.setId(IdUtils.generateId());
        probeGroupDO1.setName("name1");
        probeGroupDO1.setCluster("us01");
        probeGroupDO1.setRegion("va1");
        probeGroupDO1.setCountry("us");
        probeGroupDO1.setCity("va");
        probeGroupDO1.setCloud("aws");
        probeGroupDO1.setOperator("aws");
        probeGroupDO1.setTags("");
        probeGroupDO1.setStatus(CommonStatusEnum.enable.name());

        ProbeGroupDO probeGroupDO2 = new ProbeGroupDO();
        probeGroupDO2.setId(IdUtils.generateId());
        probeGroupDO2.setName("name2");
        probeGroupDO2.setCluster("us01");
        probeGroupDO2.setRegion("oh1");
        probeGroupDO2.setCountry("us");
        probeGroupDO2.setCity("oh");
        probeGroupDO2.setCloud("aws");
        probeGroupDO2.setOperator("aws");
        probeGroupDO2.setTags("{\"service\":[\"cube\"]}");
        probeGroupDO2.setStatus(CommonStatusEnum.enable.name());

        ProbeGroupDO probeGroupDO3 = new ProbeGroupDO();
        probeGroupDO3.setId(IdUtils.generateId());
        probeGroupDO3.setName("name3");
        probeGroupDO3.setCluster("ca01");
        probeGroupDO3.setRegion("va1");
        probeGroupDO3.setCountry("ca");
        probeGroupDO3.setCity("foo");
        probeGroupDO3.setCloud("aws");
        probeGroupDO3.setOperator("aws");
        probeGroupDO3.setTags("{\"service\":[\"cube\", \"probe\"]}");
        probeGroupDO3.setStatus(CommonStatusEnum.enable.name());

        Mockito.when(probeGroupHandler.findByParam(anyString(), anyString(), anyString(), anyString(), anyString(), anyString(), anyString(), anyString(), anyString(), anyString(), anyInt(), anyInt()))
                .thenReturn(Lists.newArrayList(probeGroupDO1, probeGroupDO2, probeGroupDO3));

        ProbeGroupQuery probeGroupQuery = new ProbeGroupQuery();
        probeGroupQuery.setId("");
        probeGroupQuery.setName("");
        probeGroupQuery.setCluster("");
        probeGroupQuery.setRegion("");
        probeGroupQuery.setCloud("");
        probeGroupQuery.setCountry("");
        probeGroupQuery.setCity("");
        probeGroupQuery.setOperator("");
        probeGroupQuery.setTag("");
        probeGroupQuery.setStatus("");
        ResponseObject<List<ProbeGroupDO>> responseObject = probeGroupService.searchAllProbeGroups(probeGroupQuery);
        List<ProbeGroupDO> probeGroupDOList = responseObject.getData();
        Assertions.assertEquals(3, probeGroupDOList.size());

        Mockito.when(probeGroupHandler.findByParam(anyString(), anyString(), anyString(), anyString(), anyString(), anyString(), anyString(), anyString(), anyString(), anyString(), anyInt(), anyInt()))
                .thenReturn(Lists.newArrayList(probeGroupDO1, probeGroupDO2, probeGroupDO3));
        probeGroupQuery.setServiceId("web");
        responseObject = probeGroupService.searchAllProbeGroups(probeGroupQuery);
        probeGroupDOList = responseObject.getData();
        Assertions.assertEquals(1, probeGroupDOList.size());
        Assertions.assertEquals("name1", probeGroupDOList.get(0).getName());


        Mockito.when(probeGroupHandler.findByParam(anyString(), anyString(), anyString(), anyString(), anyString(), anyString(), anyString(), anyString(), anyString(), anyString(), anyInt(), anyInt()))
                .thenReturn(Lists.newArrayList(probeGroupDO1, probeGroupDO2, probeGroupDO3));
        probeGroupQuery.setServiceId("cube");
        responseObject = probeGroupService.searchAllProbeGroups(probeGroupQuery);
        probeGroupDOList = responseObject.getData();
        Assertions.assertEquals(3, probeGroupDOList.size());

        Mockito.when(probeGroupHandler.findByParam(anyString(), anyString(), anyString(), anyString(), anyString(), anyString(), anyString(), anyString(), anyString(), anyString(), anyInt(), anyInt()))
                .thenReturn(Lists.newArrayList(probeGroupDO1, probeGroupDO2, probeGroupDO3));
        probeGroupQuery.setServiceId("probe");
        responseObject = probeGroupService.searchAllProbeGroups(probeGroupQuery);
        probeGroupDOList = responseObject.getData();
        Assertions.assertEquals(2, probeGroupDOList.size());
        Assertions.assertEquals("name1", probeGroupDOList.get(0).getName());
        Assertions.assertEquals("name3", probeGroupDOList.get(1).getName());
    }

    @Test
    public void testSearchProbePoint() {
        ProbePointQuery probePointQuery = new ProbePointQuery();
        probePointQuery.setHost("host");
        probePointQuery.setIp("ip");
        probePointQuery.setGroup("group");
        probePointQuery.setCity("name");
        probePointQuery.setCloud("name");
        probePointQuery.setCountry("name");
        probePointQuery.setOperator("name");
        probePointQuery.setTag("e2e");
        probePointQuery.setStatus(CommonStatusEnum.enable.name());
        PageQuery<ProbePointQuery> query = new PageQuery<>();
        query.setQueryPara(probePointQuery);

        Mockito.when(probeGroupHandler.findProbePointByParam(
                probePointQuery.getHost(),
                probePointQuery.getIp(),
                probePointQuery.getGroup(),
                probePointQuery.getCloud(),
                probePointQuery.getCountry(),
                probePointQuery.getCity(),
                probePointQuery.getOperator(),
                probePointQuery.getTag(),
                probePointQuery.getStatus(),
                query.getPageIndex(), query.getPageSize()
        )).thenReturn(Lists.newArrayList());

        Mockito.when(probeGroupHandler.getCountProbePointByParam(
                probePointQuery.getHost(),
                probePointQuery.getIp(),
                probePointQuery.getGroup(),
                probePointQuery.getCloud(),
                probePointQuery.getCountry(),
                probePointQuery.getCity(),
                probePointQuery.getOperator(),
                probePointQuery.getStatus(),
                probePointQuery.getTag()
        )).thenReturn(0);

        ResponseObject<PageResult<ProbePointDO>> responseObject = probeGroupService.searchProbePoint(query);
        Assertions.assertEquals(CollectionUtils.isEmpty(responseObject.getData().getItems()), true);
        Assertions.assertEquals(responseObject.getData().getTotal(), 0);
    }


    @Test
    public void testSearchProbePoint1() {
        ProbePointQuery probePointQuery = new ProbePointQuery();
        probePointQuery.setHost("host");
        probePointQuery.setIp("ip");
        probePointQuery.setGroup("group");
        probePointQuery.setCity("name");
        probePointQuery.setCloud("name");
        probePointQuery.setCountry("name");
        probePointQuery.setOperator("name");
        probePointQuery.setTag("e2e");
        probePointQuery.setStatus(CommonStatusEnum.enable.name());
        PageQuery<ProbePointQuery> query = new PageQuery<>();
        query.setQueryPara(probePointQuery);

        Mockito.when(probeGroupHandler.findProbePointByParam(
                probePointQuery.getHost(),
                probePointQuery.getIp(),
                probePointQuery.getGroup(),
                probePointQuery.getCloud(),
                probePointQuery.getCountry(),
                probePointQuery.getCity(),
                probePointQuery.getOperator(),
                probePointQuery.getTag(),
                probePointQuery.getStatus(),
                query.getPageIndex(), query.getPageSize()
        )).thenReturn(Lists.newArrayList(new ProbePointDO(), new ProbePointDO(),
                new ProbePointDO(), new ProbePointDO(), new ProbePointDO(), new ProbePointDO(),
                new ProbePointDO(), new ProbePointDO(), new ProbePointDO()));

        Mockito.when(probeGroupHandler.getCountProbePointByParam(
                probePointQuery.getHost(),
                probePointQuery.getIp(),
                probePointQuery.getGroup(),
                probePointQuery.getCloud(),
                probePointQuery.getCountry(),
                probePointQuery.getCity(),
                probePointQuery.getOperator(),
                probePointQuery.getStatus(),
                probePointQuery.getTag()
        )).thenReturn(20);

        ResponseObject<PageResult<ProbePointDO>> responseObject = probeGroupService.searchProbePoint(query);
        Assertions.assertEquals(responseObject.getData().getItems().size(), 9);
        Assertions.assertEquals(responseObject.getData().getTotal(), 20);
    }

    @Test
    public void testAddExistName() {
        ProbeGroupInput input = new ProbeGroupInput();
        input.setName("name");
        input.setCity("us01");
        input.setRegion("va1");
        input.setCity("city");
        input.setCloud("cloud");
        input.setCountry("country");
        input.setOperator("operator");
        Mockito.when(probeGroupHandler.getCountByName(input.getName())).thenReturn(1);
        try {
            probeGroupService.addProbeGroup(input);
        } catch (Exception e) {
            Assertions.assertEquals("name " + input.getName() + " is already exist", e.getMessage());
        }
    }

    @Test
    public void testAdd() {
        ProbeGroupInput input = new ProbeGroupInput();
        input.setName("name");
        input.setCity("us01");
        input.setRegion("va1");
        input.setCity("city");
        input.setCloud("cloud");
        input.setCountry("country");
        input.setOperator("operator");
        System.out.println(JsonUtils.toJsonStringIgnoreExp(input));
        Mockito.when(probeGroupHandler.getCountByName(input.getName())).thenReturn(0);

        ResponseObject<String> responseObject = probeGroupService.addProbeGroup(input);
    }

    @Test
    public void testEditProbeGroup() {
        ProbeGroupInput input = new ProbeGroupInput();
        input.setId(id);
        input.setName("name");
        input.setCity("us01");
        input.setRegion("va1");
        input.setCity("city");
        input.setCloud("cloud");
        input.setCountry("country");
        input.setOperator("operator");
        Mockito.when(probeGroupHandler.getCountByNameNotId(input.getName(), id)).thenReturn(0);

        ResponseObject<Boolean> responseObject = probeGroupService.editProbeGroup(input);
        Assertions.assertEquals(responseObject.getData(), true);
    }

    @Test
    public void testGetProbeGroupById() {
        IdPara idPara = new IdPara();
        idPara.setId(id);
        ProbeGroupDO probeGroupDO = new ProbeGroupDO();
        probeGroupDO.setTags("*bb*cc*");
        Mockito.when(probeGroupHandler.getById(id)).thenReturn(probeGroupDO);
        ResponseObject<ProbeGroupDO> responseObject = probeGroupService.getProbeGroupById(idPara);
        Assertions.assertEquals(responseObject.getData() != null, true);
    }

    @Test
    public void testGetProbeGroupByIdNotExist() {
        IdPara idPara = new IdPara();
        idPara.setId(id);
        Mockito.when(probeGroupHandler.getById(id)).thenReturn(null);
        try {
            ResponseObject<ProbeGroupDO> responseObject = probeGroupService.getProbeGroupById(idPara);
        } catch (Exception e) {
            Assertions.assertEquals(e.getMessage(), "probe group not exist");
        }
    }

    @Test
    public void testDelete() {
        IdPara idPara = new IdPara();
        idPara.setId(id);
        try {
            Mockito.when(probeGroupServerHandler.getCountByProbeGroupId(id)).thenReturn(1);
            ResponseObject<Boolean> responseObject = probeGroupService.deleteProbeGroup(idPara);
        } catch (Exception e) {
            Assertions.assertEquals(e.getMessage(), "probe group used");
        }
    }
}
