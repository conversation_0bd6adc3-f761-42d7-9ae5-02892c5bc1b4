package us.zoom.cube.site.core.model.common;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * @author: canyon.li
 * @date: 2024/07/19
 **/
@Data
public class AgentHeartAsyncMQCfg {

    List<ConfigUnit> agentHeartAsyncConfigs;

    @Data
    public static class ConfigUnit {

        private String topic;

        private Integer threadCount;

        private String group;

        private String asyncClusterId;
    }
}


