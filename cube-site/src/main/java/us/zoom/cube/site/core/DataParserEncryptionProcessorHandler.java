package us.zoom.cube.site.core;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import us.zoom.infra.dao.model.DataParserEncryptionProcessorDO;
import us.zoom.infra.dao.service.DataParserEncryptionProcessorDAO;

import java.util.Arrays;
import java.util.List;

@Component
public class DataParserEncryptionProcessorHandler {

    @Autowired
    private DataParserEncryptionProcessorDAO dataParserEncryptionProcessorDAO;

    public void addEncryptionProcessor(DataParserEncryptionProcessorDO encryptionProcessorDO) {
        Assert.notNull(encryptionProcessorDO, "encryption processor is null !");
        dataParserEncryptionProcessorDAO.add(encryptionProcessorDO);
    }


    public void editEncryptionProcessor(DataParserEncryptionProcessorDO encryptionProcessorDO) {
        Assert.notNull(encryptionProcessorDO, "encryption processor   is null !");
        dataParserEncryptionProcessorDAO.edit(encryptionProcessorDO);
    }

    public void delEncryptionProcessor(String id) {
        Assert.notNull(id, "id is null !");
        dataParserEncryptionProcessorDAO.del(id);
    }

    public void delEncryptionProcessorByDataParserPipelineId(String dataParserPipelineId) {
        Assert.notNull(dataParserPipelineId,"dataParserPipelineId is null !");
        dataParserEncryptionProcessorDAO.delByDataParserPipelineId(dataParserPipelineId);
    }

    public DataParserEncryptionProcessorDO getEncryptionProcessorById(String id) {
        Assert.notNull(id, "id is null !");
        return dataParserEncryptionProcessorDAO.getEncryptionProcessorById(id);
    }

    public List<DataParserEncryptionProcessorDO> listByPipelineIds(List<String> pipeLineIds) {
        return dataParserEncryptionProcessorDAO.listByPipelineIds(pipeLineIds);
    }

    public void delEncryptionProcessorByPipelineId(String pipelineId) {
        Assert.notNull(pipelineId, "pipelineId is null !");
        dataParserEncryptionProcessorDAO.delByPipelindIds(Arrays.asList(pipelineId));
    }

    public void delEncryptionProcessorByPipelineIds(List<String> pipeLineIds) {
        if(CollectionUtils.isEmpty(pipeLineIds)){
            return;
        }
        dataParserEncryptionProcessorDAO.delByPipelindIds(pipeLineIds);
    }
}
