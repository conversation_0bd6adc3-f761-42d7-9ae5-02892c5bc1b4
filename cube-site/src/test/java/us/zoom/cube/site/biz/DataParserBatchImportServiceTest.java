package us.zoom.cube.site.biz;

import com.alibaba.fastjson.JSON;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import us.zoom.cube.site.CubeSiteApplication;
import us.zoom.cube.site.lib.input.alarm.batch.AlarmBatchImportInput;


/**
 * @description:
 * @author: <EMAIL>
 * @date: 2024-07-29 13:32
 **/
@SpringBootTest(classes = CubeSiteApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class DataParserBatchImportServiceTest {

    @Autowired
    private DataParserBatchImportService dataParserBatchImportService;


    @Test
    public void testCheck() {
        String alarmStr = """
                {
                    "alarmList":[
                        {
                                 "metricId":"dwdwdw1",
                                "editor": "<EMAIL>",
                                "creator": "<EMAIL>",
                                "description": "test",
                                "metricId":"89f9a323-034c-475e-9b35-022172dad534",
                                "rules": [
                                    {
                                        "hitCount": 21,
                                        "level": "ERROR",
                                        "needHits": -2,
                                        "conditions": [
                                            {
                                                "name": "callStatus_ring.count212",
                                                "conditionType": "FIELD",
                                                "threshold": "2",
                                                "operator": ">"
                                            },
                                            {
                                                "name": "callStatus_ring.count21",
                                                "conditionType": "FIELD",
                                                "threshold": "2",
                                                "operator": ">"
                                            },
                                            {
                                                "name": "pbxAccountId",
                                                "conditionType": "EXPRESSION",
                                                "expression": "devA",
                                                "operator": "in"
                                            }
                                        ]
                                    },
                                    {
                                        "hitCount": -1,
                                        "level": "ERROR",
                                        "needHits": 2,
                                        "conditions": [
                                            {
                                                "name": "callStatus_ring.count",
                                                "conditionType": "FIELD",
                                                "threshold": "10",
                                                "operator": ">"
                                            },
                                            {
                                                "name": "pbxAccountId",
                                                "conditionType": "TAG",
                                                "threshold": "devA",
                                                "operator": "in"
                                            }
                                        ]
                                    }
                                ],
                                "enabled": true,
                                "timesInPeriod": 322,
                                "alarmMatchMode": 1,
                                "alarmExtensionRelations": [],
                                "metricsName": "NUMBER_OF_WAITING_CALLS2",
                                "name": "2",
                                "levelsSendIncident": "ERROR",
                                "periodInMinutes": 3000,
                                "notifications": [
                                    {
                                        "repeatedSendAsyncmq": false,
                                        "whichLevels": "INFO",
                                        "isMarkdown": false,
                                        "routingRule":"dwdwd",
                                        "channel": {
                                            "editor": "",
                                            "creator": "",
                                            "name": "alarmChannel12",
                                            "engineName": "Kafka"
                                        },
                                        "title": "KafkaEvent2",
                                        "content": "KafkaEvent Notify2",
                                        "pdGroupTags": ""
                                    },
                                    {
                                        "repeatedSendAsyncmq": false,
                                        "whichLevels": "INFO",
                                        "isMarkdown": false,
                                        "routingRule":"dwdwd",
                                        "channel": {
                                            "editor": "",
                                            "creator": "",
                                            "name": "alarmChannel12",
                                            "engineName": "Kafka"
                                        },
                                        "title": "KafkaEvent2",
                                        "content": "KafkaEvent Notify2",
                                        "pdGroupTags": ""
                                    }
                                ]
                            },
                            {
                                 "metricId":"dwdwdw",
                                "editor": "<EMAIL>",
                                "creator": "<EMAIL>",
                                "description": "test",
                                "metricId":"89f9a323-034c-475e-9b35-022172dad534",
                                "rules": [
                                    {
                                        "hitCount": 21,
                                        "level": "ERROR",
                                        "needHits": -2,
                                        "conditions": [
                                            {
                                                "name": "callStatus_ring.count212",
                                                "conditionType": "FIELD",
                                                "threshold": "2",
                                                "operator": ">"
                                            },
                                            {
                                                "name": "callStatus_ring.count21",
                                                "conditionType": "FIELD",
                                                "threshold": "2",
                                                "operator": ">"
                                            },
                                            {
                                                "name": "pbxAccountId",
                                                "conditionType": "EXPRESSION",
                                                "expression": "devA",
                                                "operator": "in"
                                            }
                                        ]
                                    },
                                    {
                                        "hitCount": -1,
                                        "level": "ERROR",
                                        "needHits": 2,
                                        "conditions": [
                                            {
                                                "name": "callStatus_ring.count",
                                                "conditionType": "FIELD",
                                                "threshold": "10",
                                                "operator": ">"
                                            },
                                            {
                                                "name": "pbxAccountId",
                                                "conditionType": "TAG",
                                                "threshold": "devA",
                                                "operator": "in"
                                            }
                                        ]
                                    }
                                ],
                                "enabled": true,
                                "timesInPeriod": 322,
                                "alarmMatchMode": 1,
                                "alarmExtensionRelations": [],
                                "metricsName": "NUMBER_OF_WAITING_CALLS2",
                                "name": "  ",
                                "levelsSendIncident": "ERROR",
                                "periodInMinutes": 3000,
                                "notifications": [
                                    {
                                        "repeatedSendAsyncmq": false,
                                        "whichLevels": "INFO",
                                        "isMarkdown": false,
                                        "routingRule":"dwdwd",
                                        "channel": {
                                            "editor": "",
                                            "creator": "",
                                            "name": "alarmChannel12",
                                            "engineName": "Kafka"
                                        },
                                        "title": "KafkaEvent2",
                                        "content": "KafkaEvent Notify2",
                                        "pdGroupTags": ""
                                    },
                                    {
                                        "repeatedSendAsyncmq": false,
                                        "whichLevels": "INFO",
                                        "isMarkdown": false,
                                        "routingRule":"dwdwd",
                                        "channel": {
                                            "editor": "",
                                            "creator": "",
                                            "name": "alarmChannel12",
                                            "engineName": "Kafka"
                                        },
                                        "title": "KafkaEvent2",
                                        "content": "KafkaEvent Notify2",
                                        "pdGroupTags": ""
                                    }
                                ]
                            },
                            {
                                 "metricId":"dwdwdw",
                                "editor": "<EMAIL>",
                                "creator": "<EMAIL>",
                                "description": "test",
                                "metricId":"89f9a323-034c-475e-9b35-022172dad534",
                                "rules": [
                                    {
                                        "hitCount": 21,
                                        "level": "ERROR",
                                        "needHits": -2,
                                        "conditions": [
                                            {
                                                "name": "callStatus_ring.count212",
                                                "conditionType": "FIELD",
                                                "threshold": "2",
                                                "operator": ">"
                                            },
                                            {
                                                "name": "callStatus_ring.count21",
                                                "conditionType": "FIELD",
                                                "threshold": "2",
                                                "operator": ">"
                                            },
                                            {
                                                "name": "pbxAccountId",
                                                "conditionType": "EXPRESSION",
                                                "expression": "devA",
                                                "operator": "in"
                                            }
                                        ]
                                    },
                                    {
                                        "hitCount": -1,
                                        "level": "ERROR",
                                        "needHits": 2,
                                        "conditions": [
                                            {
                                                "name": "callStatus_ring.count",
                                                "conditionType": "FIELD",
                                                "threshold": "10",
                                                "operator": ">"
                                            },
                                            {
                                                "name": "pbxAccountId",
                                                "conditionType": "TAG",
                                                "threshold": "devA",
                                                "operator": "in"
                                            }
                                        ]
                                    }
                                ],
                                "enabled": true,
                                "timesInPeriod": 322,
                                "alarmMatchMode": 1,
                                "alarmExtensionRelations": [],
                                "metricsName": "NUMBER_OF_WAITING_CALLS2",
                                "name": "2",
                                "levelsSendIncident": "ERROR",
                                "periodInMinutes": 3000,
                                "notifications": [
                                    {
                                        "repeatedSendAsyncmq": false,
                                        "whichLevels": "INFO",
                                        "isMarkdown": false,
                                        "routingRule":"dwdwd",
                                        "channel": {
                                            "editor": "",
                                            "creator": "",
                                            "name": "alarmChannel12",
                                            "engineName": "Kafka"
                                        },
                                        "title": "KafkaEvent2",
                                        "content": "KafkaEvent Notify2",
                                        "pdGroupTags": ""
                                    },
                                    {
                                        "repeatedSendAsyncmq": false,
                                        "whichLevels": "INFO",
                                        "isMarkdown": false,
                                        "routingRule":"dwdwd",
                                        "channel": {
                                            "editor": "",
                                            "creator": "",
                                            "name": "alarmChannel12",
                                            "engineName": "Kafka"
                                        },
                                        "title": "KafkaEvent2",
                                        "content": "KafkaEvent Notify2",
                                        "pdGroupTags": ""
                                    }
                                ]
                            },
                            {
                                 "metricId":"dwdwdw",
                                "editor": "<EMAIL>",
                                "creator": "<EMAIL>",
                                "description": "test",
                                "metricId":"89f9a323-034c-475e-9b35-022172dad534",
                                "rules": [
                                    {
                                        "hitCount": 21,
                                        "level": "ERROR",
                                        "needHits": -2,
                                        "conditions": [
                                            {
                                                "name": "callStatus_ring.count212",
                                                "conditionType": "FIELD",
                                                "threshold": "2",
                                                "operator": ">"
                                            },
                                            {
                                                "name": "callStatus_ring.count21",
                                                "conditionType": "FIELD",
                                                "threshold": "2",
                                                "operator": ">"
                                            },
                                            {
                                                "name": "pbxAccountId",
                                                "conditionType": "EXPRESSION",
                                                "expression": "devA",
                                                "operator": "in"
                                            }
                                        ]
                                    },
                                    {
                                        "hitCount": -1,
                                        "level": "ERROR",
                                        "needHits": 2,
                                        "conditions": [
                                            {
                                                "name": "callStatus_ring.count",
                                                "conditionType": "FIELD",
                                                "threshold": "10",
                                                "operator": ">"
                                            },
                                            {
                                                "name": "pbxAccountId",
                                                "conditionType": "TAG",
                                                "threshold": "devA",
                                                "operator": "in"
                                            }
                                        ]
                                    }
                                ],
                                "enabled": true,
                                "timesInPeriod": 322,
                                "alarmMatchMode": 1,
                                "alarmExtensionRelations": [],
                                "metricsName": "NUMBER_OF_WAITING_CALLS2",
                                "name": "3",
                                "levelsSendIncident": "ERROR",
                                "periodInMinutes": 3000,
                                "notifications": [
                                    {
                                        "repeatedSendAsyncmq": false,
                                        "whichLevels": "INFO",
                                        "isMarkdown": false,
                                        "routingRule":"dwdwd",
                                        "channel": {
                                            "editor": "",
                                            "creator": "",
                                            "name": "alarmChannel12",
                                            "engineName": "Kafka"
                                        },
                                        "title": "KafkaEvent2",
                                        "content": "KafkaEvent Notify2",
                                        "pdGroupTags": ""
                                    },
                                    {
                                        "repeatedSendAsyncmq": false,
                                        "whichLevels": "INFO",
                                        "isMarkdown": false,
                                        "routingRule":"dwdwd",
                                        "channel": {
                                            "editor": "",
                                            "creator": "",
                                            "name": "alarmChannel12",
                                            "engineName": "Kafka"
                                        },
                                        "title": "KafkaEvent2",
                                        "content": "KafkaEvent Notify2",
                                        "pdGroupTags": ""
                                    }
                                ]
                            }
                    ]
                }
                """;
        AlarmBatchImportInput input = JSON.parseObject(alarmStr, AlarmBatchImportInput.class);
//        List<ValidationResult> validationResults = dataParserBatchImportService.check(input);
//        System.out.println(validationResults);

    }
}
