package us.zoom.cube.site.core.model.resource;

import java.util.List;

public class FlinkJarFile {

    private String id;
    private String name;
    private Long uploaded;
    private List<JarEntryInfo> entry;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getUploaded() {
        return uploaded;
    }

    public void setUploaded(Long uploaded) {
        this.uploaded = uploaded;
    }

    public List<JarEntryInfo> getEntry() {
        return entry;
    }

    public void setEntry(List<JarEntryInfo> entry) {
        this.entry = entry;
    }
}
