package us.zoom.cube.site.lib.input.dataparser;


import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import us.zoom.cube.lib.common.SwitchEnum;

/**
 * @Author: luis.zheng
 * @Date: 2021/8/19 7:03 
 */
public class SetDefaultInputTest {
    @Test
    public void testCheckIsDefault() {
        SetDefaultInput input = new SetDefaultInput();
        String isDefault = "sss";
        input.setIsDefault(isDefault);
        input.setId("sss");
        IllegalArgumentException err1 = Assertions.assertThrows(IllegalArgumentException.class, () -> {
            input.check();
        });
        Assertions.assertEquals(err1.getMessage(), "isDefault " + isDefault + " not exist");
    }

    @Test
    public void testCheckId() {
        SetDefaultInput input = new SetDefaultInput();
        String isDefault = SwitchEnum.no.name();
        input.setIsDefault(isDefault);
        input.setId(null);
        IllegalArgumentException err1 = Assertions.assertThrows(IllegalArgumentException.class, () -> {
            input.check();
        });
        Assertions.assertEquals(err1.getMessage(), "id is null");
    }
}
