package us.zoom.cube.site.core.schedulery;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import us.zoom.cube.scheduler.lib.constants.ScheduleConstant;
import us.zoom.cube.scheduler.lib.enums.ChannelMedia;
import us.zoom.cube.scheduler.lib.enums.ExecutorType;
import us.zoom.cube.scheduler.lib.enums.ScheduleType;
import us.zoom.cube.scheduler.lib.model.SchedulerJobInfo;
import us.zoom.cube.scheduler.lib.utils.ScheduleUtil;
import us.zoom.cube.site.api.intercept.AuthInterceptor;
import us.zoom.cube.site.infra.utils.IdUtils;
import us.zoom.infra.dao.service.scheduler.SchedulerExecutorInfoDao;
import us.zoom.infra.dao.service.scheduler.SchedulerJobInfoDao;

import java.util.Date;

/**
 * @author: <PERSON><PERSON> Ding
 * @date: 2025/4/15 16:36
 * @desc:
 */
@Slf4j
@Component
public class ScheduleJobInfoHandler {

    @Autowired
    private SchedulerJobInfoDao jobInfoDao;
    @Autowired
    private SchedulerExecutorInfoDao executorInfoDao;

    @Transactional(rollbackFor = Exception.class)
    public String addJobInfo(SchedulerJobInfo jobInfo) throws Exception {
        commonCheck(jobInfo);
        if (null != jobInfoDao.findByNameAndServiceId(jobInfo.getName(), jobInfo.getTenantId())) {
            throw new IllegalArgumentException("there is already a job with the same name");
        }

        // Generate ID if not set
        if (StringUtils.isBlank(jobInfo.getId())) {
            jobInfo.setId(IdUtils.generateId());
        } else {
            throw new IllegalArgumentException("Scheduler job creation should not have id!");
        }

        //generate next trigger time (Will take effect after 5 seconds, avoiding the pre-read cycle)
        jobInfo.setTriggerNextTime(calcTriggerNextTime(jobInfo));

        // Set timestamps
        Date now = new Date();
        jobInfo.setCreateTime(now);
        jobInfo.setUpdateTime(now);

        // Set creator and editor
        String userName = AuthInterceptor.getUserName();
        jobInfo.setCreator(userName);
        jobInfo.setEditor(userName);

        // Persist to database
        jobInfoDao.insert(jobInfo);
        return jobInfo.getId();
    }

    /**
     * Update an existing job info
     * Attention: we never update these two fields: triggerLastTime and triggerNextTime !
     * @param jobInfo the job info to update
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateJobInfo(SchedulerJobInfo jobInfo) throws Exception {
        commonCheck(jobInfo);
        // Check if job exists
        if (StringUtils.isBlank(jobInfo.getId())) {
            log.error("JobInfoHandler updateJobInfo id is null");
            throw new IllegalArgumentException("id is null");
        }
        SchedulerJobInfo existingJobInfo = jobInfoDao.findById(jobInfo.getId());
        if (null == existingJobInfo) {
            log.error("JobInfoHandler updateJobInfo jobInfo not found, id: {}", jobInfo.getId());
            throw new IllegalArgumentException("jobInfo not found");
        }

        //it's very important to decide whether job needs to update next trigger time
        if(needUpdateNextTriggerTime(jobInfo, existingJobInfo)) {
            jobInfo.setTriggerLastTime(0);
            jobInfo.setTriggerNextTime(calcTriggerNextTime(jobInfo));
        }

        // Update timestamp and editor
        jobInfo.setUpdateTime(new Date());
        jobInfo.setEditor(AuthInterceptor.getUserName());

        // Preserve creation info
        jobInfo.setCreateTime(existingJobInfo.getCreateTime());
        jobInfo.setCreator(existingJobInfo.getCreator());

        // Update in database
        jobInfoDao.update(jobInfo);
    }

    private boolean needUpdateNextTriggerTime(SchedulerJobInfo jobInfo, SchedulerJobInfo previousJobInfo) {
        if(jobInfo.getScheduleType() != previousJobInfo.getScheduleType()) {
            return true;
        }
        if(!StringUtils.equals(jobInfo.getScheduleConf(), previousJobInfo.getScheduleConf())) {
            return true;
        }
        if(jobInfo.isEnabled() && !previousJobInfo.isEnabled()) {
            return true;
        }
        return false;
    }

    /**
     * Delete a job info by id
     *
     * @param id the id of the job info to delete
     */
    public void deleteJobInfo(String id) {
        // Check if id is valid
        if (StringUtils.isBlank(id)) {
            log.error("JobInfoHandler deleteJobInfo id is null");
            throw new IllegalArgumentException("id is null");
        }

        // Check if job exists
        SchedulerJobInfo existingJobInfo = jobInfoDao.findById(id);
        if (null == existingJobInfo) {
            log.error("JobInfoHandler deleteJobInfo jobInfo not found, id: {}", id);
            throw new IllegalArgumentException("jobInfo not found");
        }

        // Delete from database
        jobInfoDao.delete(id);
    }


    public void startJob(String id) throws Exception {
        SchedulerJobInfo existingJobInfo = jobInfoDao.findById(id);
        if (null == existingJobInfo) {
            throw new IllegalArgumentException("jobInfo not found");
        }

        if (existingJobInfo.isEnabled()) {
            throw new IllegalArgumentException("job is already started");
        }

        //generate next trigger time (Will take effect after 5 seconds, avoiding the pre-read cycle)
        Date nextValidTime = ScheduleUtil.generateNextValidTime(existingJobInfo.getScheduleType(), existingJobInfo.getScheduleConf(), new Date(System.currentTimeMillis() + ScheduleConstant.PRE_READ_MS));
        Assert.notNull(nextValidTime, "schedule type is invalid");
        long nextTriggerTime = nextValidTime.getTime();

        existingJobInfo.setEnabled(true);
        existingJobInfo.setTriggerLastTime(0);
        existingJobInfo.setTriggerNextTime(calcTriggerNextTime(existingJobInfo));

        jobInfoDao.update(existingJobInfo);
    }

    public void stopJob(String id) {
        SchedulerJobInfo existingJobInfo = jobInfoDao.findById(id);
        if (null == existingJobInfo) {
            throw new IllegalArgumentException("jobInfo not found");
        }

        if (!existingJobInfo.isEnabled()) {
            throw new IllegalArgumentException("job is already stopped");
        }

        existingJobInfo.setEnabled(false);
        existingJobInfo.setTriggerLastTime(0);
        existingJobInfo.setTriggerNextTime(0);

        jobInfoDao.update(existingJobInfo);
    }


    private void commonCheck(SchedulerJobInfo jobInfo) {
        Assert.isTrue(StringUtils.isNotBlank(jobInfo.getName()), "jobName is blank");

        //check executor
        if (ExecutorType.callback == jobInfo.getExecutorType()) {
            Assert.isTrue(StringUtils.isNotBlank(jobInfo.getExecutorInfoId()), "executorInfoId is blank");
            Assert.notNull(executorInfoDao.findByIdAndServiceId(jobInfo.getExecutorInfoId(), jobInfo.getTenantId()), "executor not found in this service");
        }

        //check channel media
        if (ChannelMedia.AsyncMQ == jobInfo.getChannelMedia()) {
            Assert.isTrue(StringUtils.isNotBlank(jobInfo.getTriggerAsyncmqTopic()), "AsyncmqTopic is blank");
            //todo to call asyncmq api to check if user has producer privilege

        } else if (ChannelMedia.HTTP == jobInfo.getChannelMedia()) {
            Assert.isTrue(StringUtils.isNotBlank(jobInfo.getTriggerExecutorUrl()), "ExecutorUrl is blank");
            Assert.isTrue(StringUtils.isNotBlank(jobInfo.getCsmsApp()), "CsmsApp is blank");

            //todo probe the target url by HTTP HEAD
        }

        //check cron
        checkCronExpression(jobInfo.getScheduleType(), jobInfo.getScheduleConf());
    }

    private void checkCronExpression(ScheduleType scheduleType, String scheduleConf) {
        if (null == scheduleType) {
            log.error("JobInfoHandler checkCronExpression scheduleType is null");
            throw new IllegalArgumentException("scheduleType is null");
        }
        if (ScheduleType.CRON == scheduleType) {
            if (null == scheduleConf || !us.zoom.cube.scheduler.lib.cron.CronExpression.isValidExpression(scheduleConf)) {
                throw new IllegalArgumentException("scheduleConf invalid");
            }
        } else if (ScheduleType.FIXED_RATE == scheduleType) {
            //todo check fixed rate
        } else if (ScheduleType.FIXED_DELAY == scheduleType) {
            //todo check fixed delay
        } else if (ScheduleType.NONE == scheduleType) {
            //do nothing
        } else {
            log.error("JobInfoHandler checkCronExpression scheduleType is not supported");
            throw new IllegalArgumentException("scheduleType is not supported");
        }
    }

    public void deleteByRefInfo(String refId, String busiType) {
        // Check if id is valid
        if (StringUtils.isBlank(refId)) {
            log.error("JobInfoHandler deleteJobInfo id is null");
            throw new IllegalArgumentException("id is null");
        }
        jobInfoDao.deleteByRefInfo(refId, busiType);
    }

    public SchedulerJobInfo findByRefInfo(String refId, String busiType) {
        return jobInfoDao.findByRefInfo(refId, busiType);
    }

    public SchedulerJobInfo findById(String id) {
        return jobInfoDao.findById(id);
    }


    private long calcTriggerNextTime(SchedulerJobInfo jobInfo) throws Exception {
        if (jobInfo.getTriggerInitOffsetSeconds() > 0) {
            return System.currentTimeMillis() / 1000 * 1000 + jobInfo.getTriggerInitOffsetSeconds() * 1000;
        } else {
            //generate next trigger time (Will take effect after 5 seconds, avoiding the pre-read cycle)
            Date nextValidTime = ScheduleUtil.generateNextValidTime(jobInfo.getScheduleType(), jobInfo.getScheduleConf(), new Date(System.currentTimeMillis() + ScheduleConstant.PRE_READ_MS));
            Assert.notNull(nextValidTime, "schedule type is invalid");
            long nextTriggerTime = nextValidTime.getTime();
            return nextTriggerTime;
        }
    }

}
