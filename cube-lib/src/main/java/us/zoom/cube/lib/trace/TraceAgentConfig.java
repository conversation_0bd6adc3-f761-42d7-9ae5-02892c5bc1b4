/*
 * Copyright 2023 Domstoladministrasjonen, Norway
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * SPDX-License-Identifier: Apache-2.0
 */
package us.zoom.cube.lib.trace;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.google.common.collect.Lists;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @author: adan.zeng
 * @date: 2024/9/12
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class TraceAgentConfig {

    public enum SamplerType {
        always_on, always_off, traceidratio, parentbased_always_on, parentbased_always_off, parentbased_traceidratio;

        public static SamplerType fromString(String value) {
            for (SamplerType type : SamplerType.values()) {
                if (type.name().equalsIgnoreCase(value)) {
                    return type;
                }
            }
            return parentbased_traceidratio;
        }
    }

    private SamplerType sampler = SamplerType.parentbased_traceidratio;

    private Double sampleRatio = 0.01;

    private long timestamp = System.currentTimeMillis();

    private List<TraceAgentRule> rules = Lists.newArrayList();

    private Set<String> recordKeys;

    private Boolean zoomPropagatorEnable;

    private Boolean captureErrorSpanEnabled;

    private Boolean recordDroppedSpanEnabled;

    private Boolean captureErrorLogEnabled;

    private Boolean enableMybatisEnhancement;

    private Boolean mybatisEnhancementAsClient;

    private Boolean reportByHttpProtobufEnabled;

    private Integer maxExportBatchSize;

    private Map<String, Set<Integer>> skipErrorLogStackFrameMapping;

    private Set<String> propagableKeys;

    public double getSampleRatio() {
        return sampleRatio;
    }

    public void setSampleRatio(double sampleRatio) {
        this.sampleRatio = sampleRatio;
    }

    public SamplerType getSampler() {
        return sampler;
    }

    public void setSampler(SamplerType sampler) {
        this.sampler = sampler;
    }

    public List<TraceAgentRule> getRules() {
        return rules;
    }

    public void setRules(List<TraceAgentRule> rules) {
        this.rules = rules;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }

    public void setSampleRatio(Double sampleRatio) {
        this.sampleRatio = sampleRatio;
    }

    public Set<String> getRecordKeys() {
        return recordKeys;
    }

    public void setRecordKeys(Set<String> recordKeys) {
        this.recordKeys = recordKeys;
    }

    public Boolean getZoomPropagatorEnable() {
        return zoomPropagatorEnable;
    }

    public void setZoomPropagatorEnable(Boolean zoomPropagatorEnable) {
        this.zoomPropagatorEnable = zoomPropagatorEnable;
    }

    public Boolean getCaptureErrorSpanEnabled() {
        return captureErrorSpanEnabled;
    }

    public void setCaptureErrorSpanEnabled(Boolean captureErrorSpanEnabled) {
        this.captureErrorSpanEnabled = captureErrorSpanEnabled;
    }

    public Boolean getRecordDroppedSpanEnabled() {
        return recordDroppedSpanEnabled;
    }

    public void setRecordDroppedSpanEnabled(Boolean recordDroppedSpanEnabled) {
        this.recordDroppedSpanEnabled = recordDroppedSpanEnabled;
    }

    public Boolean getCaptureErrorLogEnabled() {
        return captureErrorLogEnabled;
    }

    public void setCaptureErrorLogEnabled(Boolean captureErrorLogEnabled) {
        this.captureErrorLogEnabled = captureErrorLogEnabled;
    }

    public Boolean getEnableMybatisEnhancement() {
        return enableMybatisEnhancement;
    }

    public void setEnableMybatisEnhancement(Boolean enableMybatisEnhancement) {
        this.enableMybatisEnhancement = enableMybatisEnhancement;
    }

    public Boolean getMybatisEnhancementAsClient() {
        return mybatisEnhancementAsClient;
    }

    public void setMybatisEnhancementAsClient(Boolean mybatisEnhancementAsClient) {
        this.mybatisEnhancementAsClient = mybatisEnhancementAsClient;
    }

    public Boolean getReportByHttpProtobufEnabled() {
        return reportByHttpProtobufEnabled;
    }

    public void setReportByHttpProtobufEnabled(Boolean reportByHttpProtobufEnabled) {
        this.reportByHttpProtobufEnabled = reportByHttpProtobufEnabled;
    }

    public Integer getMaxExportBatchSize() {
        return maxExportBatchSize;
    }

    public void setMaxExportBatchSize(Integer maxExportBatchSize) {
        this.maxExportBatchSize = maxExportBatchSize;
    }

    public Map<String, Set<Integer>> getSkipErrorLogStackFrameMapping() {
        return skipErrorLogStackFrameMapping;
    }

    public void setSkipErrorLogStackFrameMapping(Map<String, Set<Integer>> skipErrorLogStackFrameMapping) {
        this.skipErrorLogStackFrameMapping = skipErrorLogStackFrameMapping;
    }

    public Set<String> getPropagableKeys() {
        return propagableKeys;
    }

    public void setPropagableKeys(Set<String> propagableKeys) {
        this.propagableKeys = propagableKeys;
    }
}
