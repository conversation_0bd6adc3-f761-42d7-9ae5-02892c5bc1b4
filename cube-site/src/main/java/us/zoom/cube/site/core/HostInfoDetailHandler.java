package us.zoom.cube.site.core;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import us.zoom.cube.lib.common.CubeConstants;
import us.zoom.infra.dao.model.HostInfoDetailDO;
import us.zoom.infra.dao.service.HostInfoDetailDAO;

import java.util.ArrayList;
import java.util.List;

@Component
@Slf4j
public class HostInfoDetailHandler {
    @Autowired
    private HostInfoDetailDAO hostInfoDetailDAO;

    public static final Integer SQL_BATCH_ADD_SIZE = 200;

    public int batchDeleteByServerId(String serverId) {
        return hostInfoDetailDAO.deleteByServerId(serverId);
    }

    public void batchInsert(List<HostInfoDetailDO> hostInfoDetailDOListToAdd) {
        List<List<HostInfoDetailDO>> hostInfoDetailDOListBatch = ListUtils.partition(hostInfoDetailDOListToAdd, SQL_BATCH_ADD_SIZE);
        for (List<HostInfoDetailDO> detailDOS : hostInfoDetailDOListBatch){
            hostInfoDetailDAO.batchInsert(detailDOS);
        }
    }

    public List<HostInfoDetailDO> selectByServerId(String serverId) {
        return hostInfoDetailDAO.selectByServerId(serverId);
    }

    public List<String> queryDistinctFieldList(String hostInfoType) {
        return hostInfoDetailDAO.queryDistinctFieldList(hostInfoType);
    }

    public List<String> queryFieldValueList(String hostInfoType, String field) {
        return hostInfoDetailDAO.queryFieldValueList(hostInfoType,field);
    }

    public List<String> selectServerIdsByParam(String hostInfoType, String field, String value,List<String> tenantIdFilterList) {
        if (CollectionUtils.isEmpty(tenantIdFilterList)){
            return new ArrayList<>();
        }
        return hostInfoDetailDAO.selectServerIdsByParam(hostInfoType,field,value,tenantIdFilterList);
    }

    public int batchDeleteByServerIds(List<String> hostInfosToDelete) {
        if (CollectionUtils.isEmpty(hostInfosToDelete)){
            return 0;
        }
        return hostInfoDetailDAO.batchDeleteByServerIds(hostInfosToDelete);
    }
}
