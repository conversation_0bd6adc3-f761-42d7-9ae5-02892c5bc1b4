package us.zoom.cube.site.core;

import com.google.common.base.Splitter;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Sets;
import jakarta.annotation.PostConstruct;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.lib.common.CubeConstants;
import us.zoom.cube.lib.common.RoleTypeEnum;
import us.zoom.cube.lib.common.UserViewStatisticTypeEnum;
import us.zoom.cube.lib.utils.JsonUtils;
import us.zoom.cube.sdk.util.MonitorLogReporter;
import us.zoom.cube.site.api.intercept.AuthInterceptor;
import us.zoom.cube.site.api.web.StatusEnum;
import us.zoom.cube.site.biz.*;
import us.zoom.cube.site.biz.dashboard.DashSysPara;
import us.zoom.cube.site.biz.dashboard.DashSysParaService;
import us.zoom.cube.site.biz.dashboard.MigrationServiceMonitor;
import us.zoom.cube.site.core.auth.AuthHandler;
import us.zoom.cube.site.core.auth.TenantUserRelaHandler;
import us.zoom.cube.site.core.auth.UserRoleHandler;
import us.zoom.cube.site.core.dashboard.CardConfigsHelper;
import us.zoom.cube.site.core.usergroup.UserGroupHandler;
import us.zoom.cube.site.infra.utils.IdUtils;
import us.zoom.cube.site.lib.BasePara;
import us.zoom.cube.site.lib.IdPara;
import us.zoom.cube.site.lib.input.CardInput;
import us.zoom.cube.site.lib.input.DashInput;
import us.zoom.cube.site.lib.input.FullSearchDashInput;
import us.zoom.cube.site.lib.monitor.LoadDashTenantMapMonitor;
import us.zoom.cube.site.lib.output.PageResult;
import us.zoom.cube.site.lib.output.config.metrics.MetricsTempOut;
import us.zoom.cube.site.lib.query.NameQuery;
import us.zoom.cube.site.lib.query.PageQuery;
import us.zoom.cube.site.lib.query.SearchCanFromPublicQuery;
import us.zoom.cube.site.lib.query.SearchHaveQuery;
import us.zoom.infra.dao.model.*;
import us.zoom.infra.dao.param.DashSearchHaveParam;
import us.zoom.infra.dao.param.SearchCanFromPublicParam;
import us.zoom.infra.dao.service.*;
import us.zoom.infra.enums.*;
import us.zoom.infra.thread.NamedThreadFactory;
import us.zoom.infra.utils.CommonSplitConstants;
import us.zoom.infra.utils.Instance;

import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static us.zoom.cube.site.core.dashboard.CardConfigsHelper.parseConfigs;

/**
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class DashHandler {
    private final Logger monitorLog = LoggerFactory.getLogger("Monitor");
    private static final int MAX_MEDIUM_TEXT_LENGTH = 16777215;
    private static final String ZCP_ADMIN = "zcp_admin";
    public static final String SERVICE_MONITOR = "service_monitor";
    private static final String SERVICE_MONITOR_MIGRATION = "Edited by service_monitor migration tool";
    private static final int PAGE_SIZE = 500;
    private static final Integer MAX_PAGE = 1000000;
    private static final int CORE_POOL_SIZE = 1;
    private static final int INITIAL_DELAY = 2;
    private static final int PERIOD = 2;
    private static final String templateDashSqlKey = "INTERVAL 1 MINUTE";
    private static final String templateDashSqlReplace = "INTERVAL :interval:";
    private static final int DEFAULT_LOOP_COUNT = 10000;
    private static final int DEFAULT_PAGE_SIZE = 100;

    //Map<DashId, Set<TenantId>>
    private Map<String, Set<String>> dashTenantIdMap = new ConcurrentHashMap<>();

    //Map<UserId, Set<DashId>>
    private Map<String, Set<String>> userDashMap = new TreeMap<>();

    private static final AtomicLong index = new AtomicLong(-1);

    private static ScheduledExecutorService scheduler;

    private final LoadingCache<String, List<String>> userGroupUserIdMapCache = CacheBuilder.newBuilder()
            .expireAfterWrite(1, TimeUnit.MINUTES).build(new CacheLoader<>() {
                @Override
                public List<String> load(String key) {
                    return Instance.ofNullable(userGroupHandler.getUserRelaByGroupId(key)).stream()
                            .map(x -> x.getUserId()).collect(Collectors.toList());
                }
            });


    @PostConstruct
    private void startLoad() {
        scheduler = Executors.newScheduledThreadPool(CORE_POOL_SIZE, new NamedThreadFactory("load dash tenant map scheduler "));
        scheduler.scheduleAtFixedRate(() -> loadDashTenantMaps(), INITIAL_DELAY, PERIOD, TimeUnit.MINUTES);

    }

    @Autowired
    private UserGroupHandler userGroupHandler;

    @Autowired
    private AuthService authService;

    @Autowired
    private DashDAO dashDAO;

    @Autowired
    private DashUserRelaDAO dashUserRelaDAO;

    @Autowired
    DashTemplateRelaDAO dashTemplateRelaDAO;

    @Autowired
    private CardDAO cardDAO;

    @Autowired
    private CardHandler cardHandler;

    @Autowired
    private DashTreeHandler dashTreeHandler;

    @Autowired
    private DashTreeItemDAO dashTreeItemDAO;

    @Autowired
    private DashCardRelaDAO dashCardRelaDAO;

    @Autowired
    private DashHistoryDAO dashHistoryDAO;

    @Autowired
    private UserHandler userHandler;

    @Autowired
    private DashSysParaService dashSysParaService;

    @Autowired
    private CardService cardService;

    @Autowired
    private CubeTemplateItemServiceRelaDAO cubeTemplateItemServiceRelaDAO;

    @Autowired
    private DashTemplateService dashTemplateService;

    @Autowired
    private TenantHandler tenantHandler;

    @Autowired
    private MetricsService metricsService;

    @Autowired
    private TenantUserRelaHandler tenantUserRelaHandler;

    @Autowired
    private UserRoleHandler userRoleHandler;

    @Autowired
    private AuthHandler authHandler;

    @Autowired
    private UserViewStatisticHandler userViewStatisticHandler;

    @Autowired
    private DashService dashService;

    @Data
    @AllArgsConstructor
    static private class DashTenantTemp {
         String dashId;
         List<String> tenantId;
    }

    public Set<String> getAllDashHave(String userId){
        Set<String> allDash = userDashMap.get(userId);
        return null == allDash ? new HashSet<>() : ImmutableSet.copyOf(allDash);
    }

    public void loadDashTenantMaps() {
        //improve startup time
        long localIndex = index.incrementAndGet();
        if (localIndex < 0) {
            return;
        }

        log.info("loadDashTenantMaps begin");
        long begin = System.currentTimeMillis();
        LoadDashTenantMapMonitor.LoadDashTenantMapMonitorBuilder builder = LoadDashTenantMapMonitor.builder();
        builder.status(StatusEnum.SUCCESS.getStatus());

        try {
            scanDashTenantMap(builder);
            loadUserDashMap(builder);
            //fixTemplateDashboardSql();
        } catch (Exception e) {
            log.error("loadDashTenantMaps error,", e);
            builder.status(StatusEnum.FAIL.getStatus());
        } finally {
            builder.cost(System.currentTimeMillis() - begin);
            MonitorLogReporter.report(monitorLog, builder.build());
        }

        log.info("loadDashTenantMaps end, cost:{}", System.currentTimeMillis() - begin);
    }

    public int fixDashTenantId() {
        // one-time job
        DashSysPara dashSysPara = dashSysParaService.getDashSysPara();
        if (!dashSysPara.isFixDashTenantId()){
            return -1;
        }

        PageQuery pageQuery = new PageQuery();

        pageQuery.setPageSize(DEFAULT_PAGE_SIZE);

        AtomicInteger count = new AtomicInteger();
        for (int i = 1; i < DEFAULT_LOOP_COUNT; i++) {
            pageQuery.setPageIndex(i);
            List<DashCardRelaDO> cardIdList = cardHandler.findIncorrectTenantIdDash(pageQuery.getPageIndex(), pageQuery.getPageSize());
            if (CollectionUtils.isEmpty(cardIdList)) {
                break;
            }

            Instance.ofNullable(cardIdList).forEach(rela -> {
                CardDO byId = cardHandler.getById(rela.getCardId());
                CardInput cardInput = new CardInput();
                cardInput.setId(rela.getCardId());
                cardInput.setConfigs(JsonUtils.toObject(byId.getConfigs(), Map.class));
                cardInput.setTenantId(null);
                cardHandler.editConfigs(cardInput);
                count.getAndIncrement();
            });

            Instance.ofNullable(cardIdList).stream().map(x->x.getDashId()).distinct().forEach(dashId -> {
                rescanDashTenant(dashId);
            });

        }

        return count.get();
    }

    /**
     * load all the dash, and mapping with user-tenant relation
     * @param builder
     */
    private void loadUserDashMap(LoadDashTenantMapMonitor.LoadDashTenantMapMonitorBuilder builder) {

        long begin = System.currentTimeMillis();

        DashSysPara dashSysPara = dashSysParaService.getDashSysPara();
        if (0 != index.get() % dashSysPara.getDashUserScanInterval()) {
            return;
        }

        Map<String, Map<String, Set<String>>> userIdServiceRoleMap = tenantUserRelaHandler.getUserIdServiceRoleMap();
        Set<String> crossServiceRole = userRoleHandler.getCrossServiceRole(userRoleHandler.getAllRoles());
        Set<String> allDash = dashTenantIdMap.keySet();
        userIdServiceRoleMap.entrySet().stream().forEach((entry) -> {
            String userId = entry.getKey();
            //<TenantId, Set<Role>>
            Map<String, Set<String>> tenantRole = entry.getValue();
            List<Set<String>> userCrossServiceRole = Instance.ofNullable(tenantRole).values().stream().filter(role -> CollectionUtils.containsAny(crossServiceRole, role)).collect(Collectors.toList());

            //cross service user can access all dash
            if (!CollectionUtils.isEmpty(userCrossServiceRole)) {
                userDashMap.put(userId, allDash);
            } else {
                Set<String> dashList = getUserDashList(tenantRole);
                userDashMap.put(userId, dashList);
            }

        });

        builder.costLoadUserDash(System.currentTimeMillis() - begin);

    }

    @NotNull
    private Set<String> getUserDashList(Map<String, Set<String>> value) {
        Set<String> dashList = new HashSet<>();
        Set<String> allTenantUserHave = value.keySet();
        allTenantUserHave.forEach(tenantId -> {
            dashTenantIdMap.entrySet().forEach(dashTenantEntry -> {
                String dashId = dashTenantEntry.getKey();
                Set<String> tenantIdList = dashTenantEntry.getValue();
                if (CollectionUtils.contains(tenantIdList.iterator(), tenantId)) {
                    dashList.add(dashId);
                }
            });
        });
        return dashList;
    }

    /**
     * scan dash tenant by scanning all the card in a dash and keep the map in memory(dashTenantIdMap)
     *
     * @param builder
     */
    private void scanDashTenantMap(LoadDashTenantMapMonitor.LoadDashTenantMapMonitorBuilder builder) {
        DashSysPara dashSysPara = dashSysParaService.getDashSysPara();
        if (0 != index.get() % dashSysPara.getDashScanInterval()) {
            return;
        }

        log.info("scanDashTenantMap begin");
        long begin = System.currentTimeMillis();

        doScanDashTenantMap();

        builder.costScanDashTenant(System.currentTimeMillis() - begin);
    }

    private void doScanDashTenantMap() {
        int i = 0;
        dashTenantIdMap.clear();
        do {
            //scan all the dash by paging
            List<DashDO> dashList = dashDAO.listByPager(DashModuleEnum.GLOBAL.getCode(), i, PAGE_SIZE);
            Map<String, Set<String>> dashTenantListMap =
                    Instance.ofNullable(dashList).stream()
                            .map(x -> {
                                //multiple tenantId separated by comma
                                return new DashTenantTemp(x.getId(), StringUtils.isNotBlank(x.getTenantId())? Splitter.on(CommonSplitConstants.COMMA_SPLIT).splitToList(x.getTenantId()): Lists.newArrayList());
                            })
                            .collect(Collectors.groupingBy(DashTenantTemp::getDashId, HashMap::new,
                                    Collectors.flatMapping(x -> x.getTenantId().stream(), Collectors.toSet())));

            dashTenantListMap.forEach((x, y) -> dashTenantIdMap.merge(x, y, (oldValue, newValue) -> {
                oldValue.addAll(newValue);
                return oldValue;
            }));

            if (CollectionUtils.isEmpty(dashList) || PAGE_SIZE > dashList.size()) {
                break;
            }
            i += PAGE_SIZE;
        } while (i < MAX_PAGE);
    }

    public void rescanDashTenant(String dashId){
        List<CardDO> cardByDashId = cardHandler.getCardByDashId(dashId);
        String tenantIds = cardByDashId.stream()
                .map(c -> StringUtils.isNotBlank(c.getTenantId()) ? Splitter.on(CommonSplitConstants.COMMA_SPLIT).splitToList(c.getTenantId()) : null)
                .filter(x -> null != x)
                .flatMap(x -> x.stream())
                .distinct()
                .sorted()
                .collect(Collectors.joining(CommonSplitConstants.COMMA_SPLIT));

        DashDO dashById = dashDAO.getDashById(dashId);
        if (null != dashById && !StringUtils.equals(dashById.getTenantId(), tenantIds)) {
            dashById.setTenantId(tenantIds);
            dashDAO.edit(dashById);
        }

        setOwner(dashById);
    }

    @Transactional(rollbackFor = Exception.class)
    public void setDefault(String dashId, String userId, int module) {
        dashUserRelaDAO.setUnDefault(userId, module);
        dashUserRelaDAO.add(dashId, userId, IdUtils.generateId(), CubeConstants.CUBE_TRUE_VALUE, module);
    }

    public String getDefault(String userId, int module) {
        DashUserRelaDO aDefault = dashUserRelaDAO.getDefault(userId, module);
        return null != aDefault ? aDefault.getDashId() : null;
    }


    public PageResult<String> getDashTenantList(String tenantName, int startIndex, int pageSize) {
        if (CollectionUtils.isEmpty(dashTenantIdMap.values())) {
            return new PageResult<>(0, new ArrayList<>());
        }

        Stream<String> stream = getStream(tenantName);
        List<String> tenantIds = stream.sorted().skip(startIndex).limit(pageSize).collect(Collectors.toList());

        Stream<String> countStream = getStream(tenantName);
        long count = countStream.count();

        PageResult<String> result = new PageResult<>((int) count, tenantIds);
        return result;
    }

    @NotNull
    private Stream<String> getStream(String tenantName) {
        Stream<String> stream = dashTenantIdMap.values().stream().flatMap(x -> x.stream()).distinct();
        if (StringUtils.isNotBlank(tenantName)) {
            String upperTenantName = tenantName.toUpperCase();
            stream = stream.filter(x -> {
                TenantDO tenantById = tenantHandler.getTenantByIdFromCache(x);
                if (null != tenantById) {
                    return tenantById.getName().toUpperCase().contains(upperTenantName);
                }
                return false;
            });
        }
        return stream;
    }


    public List<DashHasUserRelaDO> searchHave(PageQuery<SearchHaveQuery> pageQuery) {
        SearchHaveQuery queryPara = pageQuery.getQueryPara();
        if (null == queryPara) {
            queryPara = new SearchHaveQuery();
        }
        List<String> allTenantId = null;
        if (!queryPara.isSharedOwner()) {
            allTenantId = getAllTenantUserHave(pageQuery);
            if (CollectionUtils.isEmpty(allTenantId)) {
                return new ArrayList<>();
            }
        }

        return dashDAO.searchHave(new DashSearchHaveParam(queryPara.getName(), pageQuery.getUserId(), pageQuery.getUserName(), queryPara.getOrderBy(), queryPara.getOrderDirection(), queryPara.getTags(), allTenantId, queryPara.isSharedOwner(), DashModuleEnum.GLOBAL.getCode(), pageQuery.getPageSize() * (pageQuery.getPageIndex() - 1), pageQuery.getPageSize()));
    }

    @NotNull
    private List<String> getAllTenantUserHave(PageQuery<SearchHaveQuery> pageQuery) {
        String tenantId = pageQuery.getQueryPara().getTid();
        List<String> allTenantId;
        if (StringUtils.isBlank(tenantId)) {
            List<TenantDO> allTenantUserHas = authHandler.getAllTenantUserHas(AuthInterceptor.getUserId());
            allTenantId = allTenantUserHas.stream().map(x -> x.getId()).collect(Collectors.toList());
        } else {
            allTenantId = List.of(tenantId);
        }
        return allTenantId;
    }

    private String getName(PageQuery<SearchCanFromPublicQuery> pageQuery) {
        return (pageQuery == null || null == pageQuery.getQueryPara()) ? "" : pageQuery.getQueryPara().getName();
    }

    public Integer getSearchHaveCount(PageQuery<SearchHaveQuery> pageQuery) {
        SearchHaveQuery queryPara = pageQuery.getQueryPara();
        if (null == queryPara) {
            queryPara = new SearchHaveQuery();
        }

        List<String> allTenantId = null;
        if (!queryPara.isSharedOwner()) {
            allTenantId = getAllTenantUserHave(pageQuery);
            if (CollectionUtils.isEmpty(allTenantId)) {
                return 0;
            }
        }
        return dashDAO.searchHaveCount(new DashSearchHaveParam(queryPara.getName(), pageQuery.getUserId(), pageQuery.getUserName(), queryPara.getOrderBy(), queryPara.getOrderDirection(), queryPara.getTags(), allTenantId, queryPara.isSharedOwner(), DashModuleEnum.GLOBAL.getCode(), pageQuery.getPageSize() * (pageQuery.getPageIndex() - 1), pageQuery.getPageSize()));
    }

    private void verifyAddDash(DashInput dashInput) {
        dashInput.checkWhenAdd();
        dashInput.checkNameAndDesc();

        if (DashServiceTypeEnum.SINGLE_SERVICE.getCode().equals(dashInput.getServiceCount())) {
            TenantDO tenantByName = tenantHandler.getTenantByName(dashInput.getService());
            Assert.isTrue(null != tenantByName, "Service not exists");

            boolean hasTenant = authService.hasSuchTenant(AuthInterceptor.getUserId(), tenantByName.getId());
            Assert.isTrue(hasTenant, "User don't have this service permission");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public String add(@Valid DashInput dashInput) {

        verifyAddDash(dashInput);
        adjustData(dashInput);
        DashDO dashDO = new DashDO();
        BeanUtils.copyProperties(dashInput, dashDO);
        dashDO.setId(IdUtils.generateId());
        String configs = JsonUtils.toJsonString(dashInput.getConfigs());
        dashDO.setConfigs(configs);
        dashDO.setDashTemplateType(DashtemplateTypeEnum.NOTEMPLATE.getCode());
        dashDO.setCreator(AuthInterceptor.getUserName());
        dashDO.setModifier(AuthInterceptor.getUserName());
        dashDAO.add(dashDO);

        addDash2Tree(dashInput.getUserName(), dashInput.getParentDashTreeItemId(), dashInput.getModule(), dashDO.getId());

        return dashDO.getId();

    }

    public void addDash2Tree(String userName, String parentDashTreeItemId, Integer module, String dashId) {
        String finalParentDashItemId = dashSysParaService.getFinalParentDashItemId(parentDashTreeItemId, module);

        if (StringUtils.isEmpty(finalParentDashItemId)) {
            return;
        }
        List<String> treeItemIdList = Splitter.on(CommonSplitConstants.COMMA_SPLIT).splitToList(finalParentDashItemId);

        Instance.ofNullable(treeItemIdList).forEach(treeItemId -> {
            //dash tree path
            if (treeItemId.startsWith(CommonSplitConstants.SLASH)) {
                dashTreeHandler.linkDash2TreeByPath(userName, dashId, treeItemId, 0);

            } else {
                //dash tree item id
                DashTreeItemDO dashTreeItem = dashTreeHandler.getDashTreeItemFromCacheAndDB(treeItemId);
                if (null != dashTreeItem) {
                    dashTreeHandler.linkDash2Tree(userName, dashId, treeItemId, 0);
                }

            }
        });
    }

    private void setOwner(@Valid DashDO dashInput) {
        Set<String> ownerIds = getOwners(dashInput);
        if (StringUtils.isBlank(dashInput.getTenantId())) {
            return;
        }
        List<String> tenantIds = Splitter.on(CommonSplitConstants.COMMA_SPLIT).splitToList(dashInput.getTenantId());
        if (StringUtils.isNotBlank(dashInput.getSharedUserGroup())) {
            List<String> userGroups = Splitter.on(CommonSplitConstants.COMMA_SPLIT).splitToList(dashInput.getSharedUserGroup());
            userGroups.forEach(x -> {
                try {
                    List<String> userInGroup = userGroupUserIdMapCache.get(x);
                    ownerIds.addAll(userInGroup);
                } catch (ExecutionException e) {
                    log.error("load user group[{}] error", x, e);
                }
            });
        }
        List<TenantUserRelaDO> relationDOList = Lists.newArrayList();
        Instance.ofNullable(ownerIds).forEach(owner -> {

            //build tenant-user role list to create
            buildTenantRoleListToUser(owner, tenantIds, relationDOList);
        });

        tenantHandler.addTenantAll(relationDOList);

    }

    private void buildTenantRoleListToUser(String owner, List<String> tenantIds, List<TenantUserRelaDO> relationDOList) {
        //ignore admin user
        if (authService.canCrossAndOperate(owner) || CollectionUtils.isEmpty(tenantIds)) {
            return;
        }
        List<TenantUserRelaDO> relaByUserId = tenantUserRelaHandler.getRelaByUserId(owner);

        Set<String> userTenantIds = Instance.ofNullable(relaByUserId).stream().map(x -> x.getTenantId()).collect(Collectors.toSet());

        Instance.ofNullable(tenantIds).stream().forEach(tenantId -> {
            if (null == relaByUserId || relaByUserId.isEmpty() || !userTenantIds.contains(tenantId)) {
                TenantUserRelaDO userRelaDO = new TenantUserRelaDO();
                userRelaDO.setId(IdUtils.generateId());
                userRelaDO.setUserId(owner);
                userRelaDO.setRole(RoleTypeEnum.applicationGuests.name());
                userRelaDO.setTenantId(tenantId);
                userRelaDO.setCreateTime(new Date());

                relationDOList.add(userRelaDO);
            }
        });
    }

    private Set<String> getOwners(DashDO dashInput) {
        Set<String> ownerIds = new HashSet<>();
        if (StringUtils.isNotBlank(dashInput.getCreator())) {
            UserDO userByName = userHandler.getUserByName(dashInput.getCreator());
            if (null != userByName) {
                ownerIds.add(userByName.getId());
            }
        }
        String[] owners = StringUtils.split(dashInput.getSharedOwners(), CommonSplitConstants.COMMA_SPLIT);
        if (null != owners) {
            for (int i = 0; i < owners.length; i++) {
                if (StringUtils.isNotBlank(owners[i])) {
                    ownerIds.add(owners[i]);
                }
            }
        }
        return ownerIds;
    }

    private void adjustData(DashInput dashInput) {
        if(StringUtils.isBlank(dashInput.getDescription())){
            dashInput.setDescription("");
        }

        if(null == dashInput.getConfigs()){
            dashInput.setConfigs(Collections.emptyMap());
        }

        if(null == dashInput.getType()){
            dashInput.setType(DashTypeEnum.USER.getCode());
        }
    }

    public List<DashHasUserRelaDO> searchCanUseFromPublic(PageQuery<SearchCanFromPublicQuery> pageQuery) {
        SearchCanFromPublicQuery queryPara = pageQuery.getQueryPara();
        String tenantId = queryPara.getTid();
        Set tenantIds = new HashSet();
        if (!StringUtils.isEmpty(tenantId)) {
            tenantIds.add(tenantId);
        }

        List<DashHasUserRelaDO> dashHasUserRelaDOS = dashDAO.searchCanUseFromPublic(new SearchCanFromPublicParam(getName(pageQuery), pageQuery.getUserId(), pageQuery.getUserName(), queryPara.getTags(), tenantIds, queryPara.isSharedOwner(), pageQuery.getStartIndex(), pageQuery.getPageSize(), queryPara.getOrderBy(), queryPara.getOrderDirection(), DashModuleEnum.GLOBAL.getCode()));
        if (CollectionUtils.isEmpty(dashHasUserRelaDOS)) {
            return Collections.emptyList();
        }

        return dashHasUserRelaDOS;
    }

    public Integer getCanUseFromPublicCount(PageQuery<SearchCanFromPublicQuery> pageQuery) {
        SearchCanFromPublicQuery queryPara = pageQuery.getQueryPara();
        String tenantId = queryPara.getTid();
        Set tenantIds = new HashSet();
        if (!StringUtils.isEmpty(tenantId)) {
            tenantIds.add(tenantId);
        }

        return dashDAO.getCanUseFromPublicCount(new SearchCanFromPublicParam(getName(pageQuery), pageQuery.getUserId(), pageQuery.getUserName(), queryPara.getTags(), tenantIds, queryPara.isSharedOwner(), pageQuery.getPageSize() * (pageQuery.getPageIndex() - 1), pageQuery.getPageSize(), queryPara.getOrderBy(), queryPara.getOrderDirection(), DashModuleEnum.GLOBAL.getCode()));
    }


    @Transactional(rollbackFor = Exception.class)
    public void edit(@Valid DashInput dashInput) {
        Assert.notNull(dashInput.getId(), "id is null");
        adjustData(dashInput);
        DashDO dashDO = new DashDO();
        BeanUtils.copyProperties(dashInput, dashDO);
        dashDO.setModifier(AuthInterceptor.getUserName());
        dashDAO.edit(dashDO);
        rescanDashTenant(dashDO.getId());

        dashTreeHandler.deleteDashTreeItemByDashId(dashInput.getId());
        addDash2Tree(AuthInterceptor.getUserName(), dashInput.getParentDashTreeItemId(), null, dashInput.getId());
        if (StringUtils.isNotBlank(dashInput.getName())) {
            dashTreeItemDAO.updateName(dashInput.getId(), dashInput.getName());
        }
    }

    public DashHasUserRelaDO getDashHasUserRela(String userId, String dashId) {
        return dashDAO.getDashHasUserRela(userId,dashId);
    }

    @Transactional(rollbackFor = Exception.class)
    public void delete(IdPara idPara) {
        String dashId = idPara.getId();
        List<DashUserRelaDO> relaDOS = dashUserRelaDAO.listByDash(dashId, null, null);
        DashDO dashDo = dashDAO.getDashById(dashId);
        String creator = dashDo.getCreator();

        //1.owner is zcp_admin, need sharedOwner to help delete dashboard
        //2.owner is creator, just delete dashboard
        //3.systemMaintainer can delete
        boolean hasMaintainerRole = authService.hasSuchRole(AuthInterceptor.getUserId(), null, RoleTypeEnum.systemMaintainer.name());
        boolean dashCreator = (StringUtils.isNotBlank(creator) && (creator.equals(AuthInterceptor.getUserName()) || creator.equals(ZCP_ADMIN)))
                || hasMaintainerRole;
        CubeTemplateItemServiceRelaDO cubeTemplateItemServiceRelaDO = cubeTemplateItemServiceRelaDAO.queryByServiceIdAndTypeAndDestDataparserId(idPara.getTenantId(), dashId);
        if (dashCreator) {
            dashUserRelaDAO.delByDashUser(dashId, idPara.getUserId());
            dashUserRelaDAO.delByDashUserAll(dashId);
            dashDAO.del(dashId);
            dashTemplateRelaDAO.delByDashId(dashId);
            dashTreeItemDAO.deleteByDashId(dashId);
            userViewStatisticHandler.deleteByTypeAndRelateId(UserViewStatisticTypeEnum.DASH.getValue(), dashId);
            if (null != cubeTemplateItemServiceRelaDO) {
                cubeTemplateItemServiceRelaDAO.delete(cubeTemplateItemServiceRelaDO.getId());
            }
            cubeTemplateItemServiceRelaDAO.deleteByTypeAndDashTemplateRelaId(TemplateItemTypeEnum.DASHBOARD.getCode(), dashId);
        } else if (!CollectionUtils.isEmpty(relaDOS) && relaDOS.size() > 1) {
            //del self
            dashUserRelaDAO.delByDashUser(dashId, idPara.getUserId());
        }

    }

    public void changeConfigs(DashInput dashInput) {
        dashDAO.changeConfigs(dashInput.getId(),JsonUtils.toJsonString(dashInput.getConfigs()));
    }

    public Map<String, DashDO> getRelated(DashInput dashInput) {
        Map<String, DashDO> resMap = new HashMap<>();
        if (StringUtils.isEmpty(dashInput.getTags())) {
            return resMap;
        }
        List<String> tagList = Splitter.on(CommonSplitConstants.COMMA_SPLIT).splitToList((dashInput.getTags()));
        tagList.forEach(tag -> buildTagRelateMap(dashInput, tag, resMap));

        return resMap;
    }

    private void buildTagRelateMap(DashInput dashInput, String tag, Map<String, DashDO> resMap) {
        List<DashDO> related = dashDAO.getRelated(tag);
        related.forEach(dashDO -> doBuildTagRelatedMap(dashInput, tag, resMap, dashDO));
    }

    private void doBuildTagRelatedMap(DashInput dashInput, String tag, Map<String, DashDO> resMap, DashDO dashDO) {
        Splitter.on(CommonSplitConstants.COMMA_SPLIT).splitToList(dashDO.getTags()).forEach(
                string -> {
                    if (!StringUtils.equals(string, tag)) {
                        return;
                    }

                    resMap.put(dashDO.getId(), dashDO);
                }
        );
    }

    @Transactional(rollbackFor = Exception.class)
    public DashDO copyDash(DashInput dashInput) {
        DashDO dashDO = dashDAO.getDashById(dashInput.getId());
        if(null != dashDO){
            dashDO.setId(IdUtils.generateId());
            if(!StringUtils.isEmpty(dashInput.getTags())){
                dashDO.setTags(dashInput.getTags());
            }
            if(StringUtils.isEmpty(dashInput.getTags())){
                dashDO.setTags(null);
            }
            if(!StringUtils.isEmpty(dashInput.getService())){
                dashDO.setService(dashInput.getService());
            }
            if (StringUtils.isEmpty(dashInput.getService())){
                dashDO.setService(null);
            }
            if(null != dashInput.getServiceCount()){
                dashDO.setServiceCount(dashInput.getServiceCount());
            }
            if (!StringUtils.isEmpty(dashInput.getName())){
                dashDO.setName(dashInput.getName());
            }
            if (!StringUtils.isEmpty(dashInput.getSharedOwners())){
                dashDO.setSharedOwners(dashInput.getSharedOwners());
            }
            dashDO.setCreator(AuthInterceptor.getUserName());
            dashDO.setModifier(AuthInterceptor.getUserName());
            dashDAO.add(dashDO);

            addDash2Tree(dashInput.getUserName(), dashInput.getParentDashTreeItemId(), dashInput.getModule(), dashDO.getId());

            return dashDO;
        }
        Assert.notNull(dashDO.getId(),"copydashId is null");
        return dashDO;
    }


    public DashDO getDashById(String dashId) {
        DashDO dashDO = dashDAO.getDashById(dashId);

        return dashDO;
    }

    public List<DashDO> listBy(List<String> ids){
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        List<DashDO> dashes = dashDAO.listBy(ids);
        return dashes;
    }

    public List<DashDO> listByTenantId(String tenantId){
        return dashDAO.listByTenantId(tenantId);
    }

    public List<DashDO> listByService(String service){
        return dashDAO.listByService(service);
    }

    @Transactional(rollbackFor = Exception.class)
    public void delDashUserRela(IdPara idPara) {
        List<DashUserRelaDO> relaDOS= dashUserRelaDAO.listByDash(idPara.getId(), null, null);
        //just use for self
        if(!CollectionUtils.isEmpty(relaDOS) && relaDOS.size()==1 && relaDOS.get(0).getUserId().equals(idPara.getUserId())){
            dashDAO.del(idPara.getId());
        }

        //delete all the rela
        dashUserRelaDAO.delByDashUser(idPara.getId(),idPara.getUserId());
    }

    public List<DashDO> listPublicByTenands(List<String> tenantIds) {
       return dashDAO.listPublicByTenands(tenantIds);
    }

    public DashTemplateRelaDO getTemplateDashRelaById(String id) {
        return  dashTemplateRelaDAO.getTemplateById(id);
    }



    public List<DashHasUserRelaDO> fullSearchByName(FullSearchDashInput input) {
        return dashDAO.fullSearchByName(input.getName(), input.getServiceId());
    }

    public List<DashHistoryDO> getDashHistory(PageQuery<IdPara> pageQuery) {
        return dashHistoryDAO.query(pageQuery.getQueryPara().getId(), pageQuery.getStartIndex(), pageQuery.getPageSize());
    }

    public int countDashHistory(PageQuery<IdPara> pageQuery) {
        return dashHistoryDAO.count(pageQuery.getQueryPara().getId());
    }

    public int deleteOldVersion(String dashId, int versionLessThan) {
        return dashHistoryDAO.deleteOldVersion(dashId, versionLessThan);
    }

    public DashHistoryDO getHistory(String dashId, int version) {
        return dashHistoryDAO.queryVersion(dashId, version);
    }

    public synchronized void saveHistory(String json, String dashId, String comment) {
        try {
            if (json.getBytes(StandardCharsets.UTF_8).length > MAX_MEDIUM_TEXT_LENGTH) {
                log.warn("dash history json length greater than max length,dashId[{}] length[{}]", dashId, json.getBytes(StandardCharsets.UTF_8).length);

                return;
            }

            Integer version = dashHistoryDAO.queryMaxVersion(dashId);
            if (null == version) {
                version = 1;
            } else {
                version++;
            }

            DashHistoryDO history = new DashHistoryDO();
            history.setId(IdUtils.generateId());
            history.setDashId(dashId);
            history.setVersion(version);
            history.setModifier(AuthInterceptor.getUserName());
            history.setJson(json);
            history.setComment(comment);
            dashHistoryDAO.add(history);
        } catch (Exception e) {
            log.error("save history error, dashId:{}", dashId, e);
        }
    }

    public List<DashCardRelaDO> listDashCardRelaByCardIds(List<String> cardIds) {
        if(org.apache.commons.collections.CollectionUtils.isEmpty(cardIds)){
            return Collections.emptyList();
        }
        return dashCardRelaDAO.listDashCardRelaByCardIds(cardIds);
    }

    public List<DashCardRelaDO> listDashCardRelaByDashIds(List<String> dashIds) {
        return dashCardRelaDAO.listDashCardRelaByDashIds(dashIds);
    }

    public List<DashConfigDO> listIdConfigByName(String dashName) {
        return  dashDAO.listIdConfigByName(dashName);
    }

    public void setTopDash(String id, String userId) {
        dashUserRelaDAO.addWithTop(id, userId, IdUtils.generateId(), DashUserRelaTypeEnum.USER.getCode(), DashTopEnum.YES.getCode());
    }

    public void unsetTopDash(String id, String userId) {
        dashUserRelaDAO.addWithTop(id, userId, IdUtils.generateId(), DashUserRelaTypeEnum.USER.getCode(), DashTopEnum.NO.getCode());
    }

    public List<DashHasUserRelaDO> searchTopDash(PageQuery<NameQuery> pageQuery) {
        return dashDAO.searchTopDash(AuthInterceptor.getUserId(), pageQuery.getQueryPara().getName(), DashModuleEnum.GLOBAL.getCode(), pageQuery.getStartIndex(), pageQuery.getPageSize());
    }

    public int countTopDash(PageQuery<NameQuery> pageQuery) {
        return dashDAO.countTopDash(AuthInterceptor.getUserId(), pageQuery.getQueryPara().getName(), DashModuleEnum.GLOBAL.getCode());
    }

    public List<DashHasUserRelaDO> searchByOwner(PageQuery<BasePara> pageQuery) {
        return dashDAO.searchByCreator(pageQuery.getUserName(), pageQuery.getUserId(), DashModuleEnum.GLOBAL.getCode(), pageQuery.getStartIndex(), pageQuery.getPageSize());
    }

    public int countByOwner(PageQuery<BasePara> pageQuery) {
        return dashDAO.countByCreator(pageQuery.getUserName(), pageQuery.getUserId(), DashModuleEnum.GLOBAL.getCode());
    }

    public int countUserTopDash() {
        return dashDAO.countUserTopDash(AuthInterceptor.getUserId());
    }

    public List<DashUserRelaDO> listAllDashUser(String dashId, Integer top){
        return dashUserRelaDAO.listByDash(dashId, null, top);
    }

    public List<DashHasUserRelaDO> listDashRelaBy(List<String> dashList, String userId) {
        return dashDAO.listDashRelaBy(dashList, userId);
    }

    public List<String> getUserGroupCache(String x) {
        try {
            return userGroupUserIdMapCache.get(x);
        } catch (ExecutionException e) {
            log.error("get user group[{}] cache error", x, e);
        }

        return Lists.newArrayList();
    }

    public Set<String> migrationServiceMonitor(MigrationServiceMonitor migrationServiceMonitor) {
        String tenantId = dashSysParaService.getServiceMonitorTenantId();
        Set<String> parsedDashIds = Sets.newHashSet();
        if (StringUtils.isBlank(tenantId)) {
            log.error("migration service monitor tenant id is null");
            return parsedDashIds;
        }

        //String tenantId = "8c5b0dfa-3dcd-4396-b94f-7ecb7bcdf1da";
        PageQuery pageQuery = new PageQuery();
        pageQuery.setPageSize(DEFAULT_PAGE_SIZE);
        for (int i = 1; i < DEFAULT_LOOP_COUNT; i++) {
            pageQuery.setPageIndex(i);
            List<String> dashIds = dashDAO.searchByTenantId(tenantId, pageQuery.getStartIndex(), pageQuery.getPageSize());
            if (CollectionUtils.isEmpty(dashIds)) {
                break;
            }
            scanServiceMonitorDash(migrationServiceMonitor, dashIds, parsedDashIds);

            editDashToMultipleService(dashIds, parsedDashIds);
        }

        return parsedDashIds;
    }

    private void editDashToMultipleService(List<String> dashIds, Set<String> parsedDashIds) {
        Instance.ofNullable(dashIds).forEach(dashId -> {
            if (!parsedDashIds.contains(dashId)) {
                return;
            }

            DashDO dashById = getDashById(dashId);
            if (dashById == null) {
                return;
            }
            if (DashServiceTypeEnum.SINGLE_SERVICE.getCode().equals(dashById.getServiceCount())) {
                dashById.setServiceCount(DashServiceTypeEnum.MULTIPLE_SERVICE.getCode());
                dashById.setService(null);

                dashDAO.edit(dashById);
            }
        });
    }

    private void scanServiceMonitorDash(MigrationServiceMonitor migrationServiceMonitor, List<String> dashIds, Set<String> parsedDashIds) {
        for (String dashId : Instance.ofNullable(dashIds)) {
            if (StringUtils.isNotEmpty(dashSysParaService.getTestDashId()) && !StringUtils.equals(dashId, dashSysParaService.getTestDashId())) {
                continue;
            }
            String currentDashJson = dashService.getCurrentDashJson(dashId);
            List<CardDO> cardByDashId = cardHandler.getCardByDashId(dashId);
            for (CardDO cardDO : cardByDashId) {
                scanMetrics(migrationServiceMonitor, cardDO, dashId, parsedDashIds);
            }

            if (parsedDashIds.contains(dashId)) {
                dashService.saveHistory(currentDashJson, dashId, SERVICE_MONITOR_MIGRATION);
            }
        }
    }

    private void scanMetrics(MigrationServiceMonitor migrationServiceMonitor, CardDO cardDO, String dashId, Set<String> parsedDashIds) {
        Map<String, MetricsTempOut> metricsMap = migrationServiceMonitor.getMetrics().stream()
                .collect(Collectors.toMap(x -> x.getOldMetricsName() != null ? x.getOldMetricsName() : x.getMetricsName(), m -> m));

        Map<String, String> serviceMonitorServiceMap = dashSysParaService.getServiceMonitorServiceMap();
        CardConfigsHelper.ConfigsResult configsResult = parseConfigs(migrationServiceMonitor, metricsMap, cardDO, serviceMonitorServiceMap);

        if (!StringUtils.equals(configsResult.configs(), configsResult.originalConfigs())) {

            CardInput cardinput = new CardInput();
            cardinput.setId(cardDO.getId());
            cardinput.setConfigs(JsonUtils.toObject(configsResult.configs(), Map.class));
            cardinput.setTenantId(cardDO.getTenantId());
            cardHandler.editConfigs(cardinput);
            parsedDashIds.add(dashId);
        }
    }
}
