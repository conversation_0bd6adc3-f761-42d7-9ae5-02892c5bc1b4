package us.zoom.cube.site.core;

import com.zoom.op.monitor.domain.alarm.AlarmBoardSettingItem;
import com.zoom.op.monitor.domain.alarm.AlarmBoardSettingItemRenderOption;
import com.zoom.op.monitor.domain.alarm.AlarmBoardSettingItemSubType;
import com.zoom.op.monitor.domain.alarm.AlarmBoardSettingItemType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;
import us.zoom.cube.site.biz.alarm.AlarmBoardSettingService;
import us.zoom.cube.site.core.config.ClickhouseHandlerFactory;
import us.zoom.cube.site.lib.output.alarm.AlarmBoardFilterSummary;
import us.zoom.cube.site.lib.output.alarm.AlarmBoardFilterSummaryData;
import us.zoom.cube.site.lib.query.TimeRangeQuery;
import us.zoom.infra.clickhouse.ClickhouseConst;
import us.zoom.infra.clickhouse.ClickhouseSqlUtil;
import us.zoom.infra.dao.model.MetricsDO;
import us.zoom.infra.dao.model.TenantDO;
import us.zoom.infra.model.alarm.AlarmLevel;
import us.zoom.infra.utils.CommonSplitConstants;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * @author: Starls Ding
 * @date: 2022/10/13 08:40
 * @desc:
 */
@Slf4j
@Component
public class AlarmBoardFilterHandler {

    @Autowired
    private TenantHandler tenantHandler;
    @Autowired
    private ClickhouseHandlerFactory clickhouseHandlerFactory;
    @Autowired
    private AlarmBoardSettingHandler alarmBoardSettingHandler;
    @Autowired
    private MetricsHandler metricsHandler;
    @Autowired
    private AlarmIndexHelperHandler alarmIndexHelperHandler;

    @Autowired
    private AlarmBoardSettingService alarmBoardSettingService;

    @Autowired
    private SubEnvironmentHandler subEnvironmentHandler;

    private static ExecutorService filterItemThreadPool = Executors.newFixedThreadPool(50);

    private static ExecutorService fetchTagValueThreadPool = Executors.newFixedThreadPool(50);

    public List<AlarmBoardFilterSummary> getFilterAggregationSummary(TimeRangeQuery timeRangeQuery, String itemId) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        final String serviceId = timeRangeQuery.getServiceId();
        TenantDO tenant = tenantHandler.getTenantById(serviceId);
        Assert.notNull(tenant, "Can't find the service by id:" + serviceId);
        final String serviceName = tenant.getName();
        String routedEnv = subEnvironmentHandler.getAlarmInsightEnv(serviceName);
        final String dbName = ClickhouseSqlUtil.encodeClickhouseName(serviceName);
        // insert default alarm setting value
        alarmBoardSettingService.ifNotExistInsert(serviceId);

        List<AlarmBoardSettingItem> itemsInOrder = new ArrayList<>();
        if (StringUtils.isEmpty(itemId)) {
            itemsInOrder = alarmBoardSettingHandler.getItemInOrdInerByServiceId(serviceId);
        } else {
            itemsInOrder.add(alarmBoardSettingHandler.getItemById(itemId));
        }

        List<AlarmBoardFilterSummary> needToGetCountSummaryFilters = new ArrayList<>();
        List<AlarmBoardFilterSummary> alarmBoardFilterSummaries = new ArrayList<>();
        Set<String> allFilterItemTags = new LinkedHashSet<>();

        for (AlarmBoardSettingItem settingItem : itemsInOrder) {
            allFilterItemTags.add(settingItem.getTagName());
            AlarmBoardFilterSummary alarmBoardFilterSummary = new AlarmBoardFilterSummary();
            BeanUtils.copyProperties(settingItem, alarmBoardFilterSummary, "data");
            alarmBoardFilterSummaries.add(alarmBoardFilterSummary);

            if ("Level".equals(settingItem.getName())) {
                getAlarmCountForEachLevel(serviceName, timeRangeQuery, alarmBoardFilterSummary, routedEnv);

            } else if (AlarmBoardSettingItemRenderOption.list.value() == settingItem.getRenderOption()
                    || (AlarmBoardSettingItemRenderOption.dropdown.value() == settingItem.getRenderOption()
                    && settingItem.getType() == AlarmBoardSettingItemType.user.value() && settingItem.getSubType() == AlarmBoardSettingItemSubType.tagvalues.value())) {
                needToGetCountSummaryFilters.add(alarmBoardFilterSummary);
            }
        }
        //
        if (!CollectionUtils.isEmpty(needToGetCountSummaryFilters)) {
            //fetch metrics configuration and how many alarm in this time range.
            List<MetricsDO> metricsList = metricsHandler.getMetricsByTenant(timeRangeQuery.getServiceId());
            if (CollectionUtils.isEmpty(metricsList)) {
                return alarmBoardFilterSummaries;
            }

            //query distinct the alarm names in the query time range for narrowing the range of following query count
            Set<String> alarmsInTimeRange = alarmIndexHelperHandler.getAlarmNamesByTimeRange(serviceName, timeRangeQuery.getBegin() / 1000, timeRangeQuery.getEnd() / 1000, false);

            Map<String, Set<String>> tagWithAlarmNames = alarmIndexHelperHandler.mappingTagWithAlarmNames(serviceId, serviceName, metricsList, true);

            //get count data
            List<Future<?>> filterItemFutures = new ArrayList<>();
            for (AlarmBoardFilterSummary alarmBoardFilterSummary : needToGetCountSummaryFilters) {
                final String finalRoutedEnv = routedEnv;
                Future<?> filterItemFuture = filterItemThreadPool.submit(() -> {

                    final String filterItemTagName = (AlarmBoardSettingItemSubType.tagvalues.value() == alarmBoardFilterSummary.getSubType() ||
                            AlarmBoardSettingItemSubType.select.value() == alarmBoardFilterSummary.getSubType())
                            ? alarmBoardFilterSummary.getTagName() : alarmBoardFilterSummary.getName();
                    final boolean selectSubType = AlarmBoardSettingItemSubType.select.value() == alarmBoardFilterSummary.getSubType();
                    //care for concurrent issue
                    Map<String, Long> optionsOrderWithAlarmCount = new ConcurrentHashMap<>();
                    Map<String, Long> tagValueWithAlarmCount = new ConcurrentHashMap<>();

                    // init for select sub type filter item
                    if (selectSubType) {
                        String optionsOrder = alarmBoardFilterSummary.getOptionsOrder();
                        if (StringUtils.isNotBlank(optionsOrder)) {
                            optionsOrderWithAlarmCount.putAll(Arrays.stream(optionsOrder.split(CommonSplitConstants.COMMA_SPLIT)).collect(Collectors.toMap(String::toString, v -> 0L)));
                        }
                        String configValue = alarmBoardFilterSummary.getConfigValue();
                        if (StringUtils.isNotBlank(configValue)) {
                            tagValueWithAlarmCount.putAll(Arrays.stream(configValue.split(CommonSplitConstants.COMMA_SPLIT)).collect(Collectors.toMap(String::toString, v -> 0L)));
                        }
                    }
                    //for tagValue type filter item, we decide not to fetch tag value from metrics table
                    //for select type filter item, but we will always show the config value even if it doesn't exit in this time range.
                    if (tagWithAlarmNames.containsKey(filterItemTagName)) {
                        final String optionOrderInCondition = filterInCondtion(filterItemTagName, alarmBoardFilterSummary.getOptionsOrder());
                        final String configValueInConditon = selectSubType ? filterInCondtion(filterItemTagName, alarmBoardFilterSummary.getConfigValue()) : StringUtils.EMPTY;

                        List<Future<?>> fetchTagValueFutures = new ArrayList<>();

                        for (String singleAlarm : tagWithAlarmNames.get(filterItemTagName)) {
                            if (alarmsInTimeRange.contains(singleAlarm)) {

                                final String baseSingleTagCountSql = "select %s,count(*) as \"count\" from %s.%s where 1=1 %s and time>=%d and time<=%d group by %s order by \"count\" desc limit 100 ";

                                Future<?> fetchTagValueFuture = fetchTagValueThreadPool.submit(() -> {
                                    final String singleAlarmTable = ClickhouseSqlUtil.toClickhouseName(ClickhouseConst.ALARM_RECORD_TABLE_PREFIX + singleAlarm);
                                    //1. options order
                                    if (StringUtils.isNotBlank(alarmBoardFilterSummary.getOptionsOrder())) {

                                        final String optionsOrderCountSql = String.format(baseSingleTagCountSql,
                                                filterItemTagName, dbName, singleAlarmTable, optionOrderInCondition, timeRangeQuery.getBegin() / 1000, timeRangeQuery.getEnd() / 1000, filterItemTagName);
                                        //log.info("Fetch options order alarm count sql :{}", optionsOrderCountSql);

                                        List<Map<String, Object>> optionsOrderCountResult = clickhouseHandlerFactory.get().query(serviceName, optionsOrderCountSql, finalRoutedEnv);
                                        Map<String, Long> optionsOrderCount = optionsOrderCountResult.stream().collect(Collectors.toMap(e -> e.get(filterItemTagName).toString(), e -> Long.valueOf(e.get("count").toString())));
                                        Arrays.stream(alarmBoardFilterSummary.getOptionsOrder().split(CommonSplitConstants.COMMA_SPLIT))
                                                .forEach(optionValue ->
                                                        optionsOrderWithAlarmCount.compute(optionValue,
                                                                (k, v) -> v == null ?
                                                                        optionsOrderCount.getOrDefault(optionValue, 0L) :
                                                                        v + optionsOrderCount.getOrDefault(optionValue, 0L)));
                                    }
                                    //2.
                                    final String singleTagCountSql = String.format(baseSingleTagCountSql,
                                            filterItemTagName, dbName, singleAlarmTable, configValueInConditon, timeRangeQuery.getBegin() / 1000, timeRangeQuery.getEnd() / 1000, filterItemTagName);
                                    //log.info("Fetch tag value alarm count sql :{}", singleTagCountSql);
                                    List<Map<String, Object>> tagValueAndCountResult = clickhouseHandlerFactory.get().query(serviceName, singleTagCountSql, finalRoutedEnv);
                                    tagValueAndCountResult.forEach(entry -> tagValueWithAlarmCount.compute(entry.get(filterItemTagName).toString(), (k, v) -> v == null ? Long.valueOf(entry.get("count").toString()) : v + Long.valueOf(entry.get("count").toString())));
                                });
                                fetchTagValueFutures.add(fetchTagValueFuture);
                            }
                        }
                        for (Future<?> fetchTagValueFuture : fetchTagValueFutures) {
                            try {
                                fetchTagValueFuture.get();
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                        }

                        if (StringUtils.isNotBlank(alarmBoardFilterSummary.getOptionsOrder())) {
                            Arrays.stream(alarmBoardFilterSummary.getOptionsOrder().split(CommonSplitConstants.COMMA_SPLIT))
                                    .forEach(optionsOrder -> {
                                        tagValueWithAlarmCount.remove(optionsOrder);
                                        alarmBoardFilterSummary.getData().add(new AlarmBoardFilterSummaryData(optionsOrder, optionsOrderWithAlarmCount.get(optionsOrder)));
                                    });

                        }

                        List<AlarmBoardFilterSummaryData> temp = new ArrayList<>();
                        for (Map.Entry<String, Long> entry : tagValueWithAlarmCount.entrySet()) {
                            temp.add(new AlarmBoardFilterSummaryData(entry.getKey(), entry.getValue()));
                        }
                        //sort
                        Collections.sort(temp, Comparator.comparingLong(AlarmBoardFilterSummaryData::getCount).reversed());
                        alarmBoardFilterSummary.getData().addAll(temp);

                    }
                });
                filterItemFutures.add(filterItemFuture);

            }
            for (Future<?> filterItemFuture : filterItemFutures) {
                try {
                    filterItemFuture.get();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

        }
        stopWatch.stop();
        log.info("Get filter aggregation summary cost time: {} seconds", stopWatch.getTotalTimeSeconds());
        return alarmBoardFilterSummaries;
    }

    private String filterInCondtion(String tag, String tagNames) {
        StringBuilder inCondition = new StringBuilder();
        if (StringUtils.isNotBlank(tagNames)) {
            inCondition.append(" and ").append(tag).append(" in ").append("(");
            for (String orderTagValue : tagNames.split(CommonSplitConstants.COMMA_SPLIT)) {
                inCondition.append("'").append(orderTagValue).append("'").append(",");
            }
            inCondition.deleteCharAt(inCondition.length() - 1);
            inCondition.append(") ");
        }
        return inCondition.toString();
    }

    private void getAlarmCountForEachLevel(String serviceName, TimeRangeQuery timeRangeQuery, AlarmBoardFilterSummary alarmBoardFilterSummary, String routedEnv) {
        final String dbName = ClickhouseSqlUtil.encodeClickhouseName(serviceName);
        final String getLevelCountSql = "select `__alarmLevel` as \"level\", count(`__alarmLevel`) as \"count\" from %s.%s where time>=%d and time<=%d group by `__alarmLevel` ";
        List<Map<String, Object>> queryResult = clickhouseHandlerFactory.get().query(serviceName,
                String.format(getLevelCountSql, dbName, ClickhouseConst.ALARM_EACH_SERCICE_RECORD_TABLE_NAME, timeRangeQuery.getBegin() / 1000, timeRangeQuery.getEnd() / 1000),
                routedEnv);

        Map<String, Long> levelCountTransfer = new LinkedHashMap<>();
        queryResult.stream().forEach(entry -> levelCountTransfer.put(entry.get("level").toString(), Long.valueOf(entry.get("count").toString())));

        List<AlarmBoardFilterSummaryData> dataSet = new ArrayList();
        for (AlarmLevel alarmLevel : AlarmLevel.values()) {
            String level = alarmLevel.getLevel();
            dataSet.add(new AlarmBoardFilterSummaryData(alarmLevel.getPeril(), levelCountTransfer.containsKey(level) ? levelCountTransfer.get(level) : 0L));
        }
        alarmBoardFilterSummary.getData().addAll(dataSet);
    }


    private List<String> extractOtherTags(Set<String> allFilterItemTags, Map<String, List<String>> tagWithMetricNames) {
        List<String> otherTags = new ArrayList<>();
        for (Map.Entry<String, List<String>> tagWithMetricNameEntry : tagWithMetricNames.entrySet()) {
            final String tag = tagWithMetricNameEntry.getKey();
            if (!allFilterItemTags.contains(tag)) {
                otherTags.add(tag);
            }
        }
        return otherTags;
    }


}
