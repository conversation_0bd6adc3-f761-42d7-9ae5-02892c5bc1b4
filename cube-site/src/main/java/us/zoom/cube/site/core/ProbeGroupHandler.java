package us.zoom.cube.site.core;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import us.zoom.infra.dao.model.ProbeGroupDO;
import us.zoom.infra.dao.model.ProbePointDO;
import us.zoom.infra.dao.service.ProbeGroupDAO;
import us.zoom.infra.dao.service.ProbeTaskMappingDAO;

import java.util.List;

/**
 * @Author: <EMAIL>
 * @ModuleOwner: <EMAIL>
 * @Date: 06/29/2022 16:43
 * @Description:
 */
@Component
@Slf4j
public class ProbeGroupHandler {
    @Autowired
    ProbeGroupDAO probeGroupDAO;

    @Autowired
    ProbeTaskMappingDAO probeTaskMappingDAO;

    public List<ProbeGroupDO> findByParam(String id, String name, String cluster, String region, String cloud, String country, String city, String operator, String tag, String status, int pageIndex, int pageSize) {
        return probeGroupDAO.findByParam(id, name, cluster, region, cloud, country, city, operator, tag, status, (pageIndex - 1) * pageSize, pageSize);
    }

    public Integer getCountByParam(String id, String name, String cluster, String region, String cloud, String country, String city, String operator, String tag, String status) {
        return probeGroupDAO.getCountByParam(id, name, cluster, region, cloud, country, city, operator, tag, status);
    }

    public int getCountByName(String name) {
        return probeGroupDAO.getCountByName(name);
    }

    public int getCountByNameNotId(String name, String id) {
        return probeGroupDAO.getCountByNameNotId(name, id);
    }

    public void add(ProbeGroupDO probeGroupDO) {
        probeGroupDAO.add(probeGroupDO);
    }

    public void edit(ProbeGroupDO probeGroupDO) {
        probeGroupDAO.edit(probeGroupDO);
    }

    public void delById(String id) {
        probeGroupDAO.delById(id);
    }

    public ProbeGroupDO getById(String id) {
        return probeGroupDAO.getById(id);
    }

    public List<ProbePointDO> findProbePointByParam(String host, String ip, String group, String cloud, String country, String city, String operator, String tag, String status, int pageIndex, int pageSize) {
        return probeGroupDAO.findProbePointByParam(host, ip, group, cloud, country, city, operator, tag, status, (pageIndex - 1) * pageSize, pageSize);
    }

    public Integer getCountProbePointByParam(String host, String ip, String group, String cloud, String country, String city, String operator, String status, String tag) {
        return probeGroupDAO.getCountProbePointByParam(host, ip, group, cloud, country, city, operator, tag, status);
    }

    public int getCountByProbePointGroupId(String probePointGroupId) {
        return probeTaskMappingDAO.getCountByProbePointGroupId(probePointGroupId);
    }

    public List<ProbeGroupDO> getByIdList(List<String> idList) {
        return probeGroupDAO.getByIdList(idList);
    }
}
