package us.zoom.cube.site.lib.output.template;

import lombok.Data;

import java.util.List;

/**
 * @Author: <PERSON>
 * @Date:12/19/2022 17:10
 * @Description:
 */
@Data
public class ListBindServiceItemOutput {

    private String templateGroupId;

    private String templateGroupName;

    private List<TemplateServiceRelationItem> templateServiceRelationList;

    @Data
    public static class TemplateServiceRelationItem{

        private String templateId;

        private String templateName;

        private List<ServiceInfo> serviceList;

    }

    @Data
    public static class ServiceInfo{

        private String id;

        private String name;
    }

}
