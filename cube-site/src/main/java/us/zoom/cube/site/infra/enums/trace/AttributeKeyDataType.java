package us.zoom.cube.site.infra.enums.trace;

import org.apache.commons.lang3.StringUtils;
import java.util.List;

/**
 * @author: eason.jia
 * @date: 2024/8/18
 */
public enum AttributeKeyDataType {
    UNSPECIFIED(""),
    STRING("string"),
    INT64("int64"),
    <PERSON><PERSON><PERSON>64("float64"),
    BOOL("bool"),
    ARRAY_STRING("array(string)"),
    ARRAY_INT64("array(int64)"),
    ARRAY_FLOAT64("array(float64)"),
    ARRAY_BOOL("array(bool)");

    private final String dataType;

    AttributeKeyDataType(String dataType) {
        this.dataType = dataType;
    }

    public String getDataType() {
        return dataType;
    }

    public static AttributeKeyDataType from(String dataType) {
        for (AttributeKeyDataType type : AttributeKeyDataType.values()) {
            if (StringUtils.equals(type.getDataType(), dataType)) {
                return type;
            }
        }
        throw new IllegalArgumentException("illegal dataType: " + dataType);
    }

    public static boolean validate(String dataType) {
        for (AttributeKeyDataType type : AttributeKeyDataType.values()) {
            if (StringUtils.equals(type.getDataType(), dataType)) {
                return true;
            }
        }
        return false;
    }

    public static AttributeKeyDataType fromValue(Object value) {
        if (value == null) {
            return UNSPECIFIED;
        }
        
        if (value instanceof String) {
            return STRING;
        } else if (value instanceof Integer || value instanceof Long) {
            return INT64;
        } else if (value instanceof Float || value instanceof Double) {
            return FLOAT64;
        } else if (value instanceof Boolean) {
            return BOOL;
        } else if (value instanceof List) {
            List<?> list = (List<?>) value;
            if (list.isEmpty()) {
                return ARRAY_STRING; // Default to string array for empty lists
            }
            Object firstElement = list.get(0);
            if (firstElement instanceof String) {
                return ARRAY_STRING;
            } else if (firstElement instanceof Integer || firstElement instanceof Long) {
                return ARRAY_INT64;
            } else if (firstElement instanceof Float || firstElement instanceof Double) {
                return ARRAY_FLOAT64;
            } else if (firstElement instanceof Boolean) {
                return ARRAY_BOOL;
            } else {
                return ARRAY_STRING; // Default to string array for unknown types
            }
        } else {
            return STRING; // Default to string for unknown types
        }
    }
}
