package us.zoom.cube.site.core.config;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import us.zoom.cube.lib.common.InfluxHaStatus;
import us.zoom.cube.site.core.RsaService;
import us.zoom.infra.dao.model.InfluxdbClusterDO;
import us.zoom.infra.dao.model.InfluxdbHaRelationDO;
import us.zoom.infra.dao.model.InfluxdbTenantRelationDO;
import us.zoom.infra.dao.model.TenantDO;
import us.zoom.infra.dao.service.InfluxdbClusterDAO;
import us.zoom.infra.dao.service.InfluxdbHaRelationDAO;
import us.zoom.infra.dao.service.InfluxdbTenantRelationDAO;
import us.zoom.infra.dao.service.TenantDAO;
import us.zoom.infra.influx.config.InfluxHandlerFactory;
import us.zoom.infra.influx.model.cluster.ClusterType;
import us.zoom.infra.influx.service.InfluxHaHandler;
import us.zoom.infra.influx.service.InfluxHandler;
import us.zoom.infra.utils.Instance;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Hong
 * @date 2021/8/10 1:30 PM
 */

@Component
@Slf4j
public class InfluxdbCacheLoader implements CacheLoader {

    @Autowired
    private InfluxHandlerFactory influxHandlerFactory;

    @Autowired
    private InfluxdbClusterDAO influxdbClusterDAO;

    @Autowired
    private InfluxdbTenantRelationDAO influxdbTenantRelationDAO;

    @Autowired
    private TenantDAO tenantDAO;

    @Autowired
    private RsaService rsaService;

    @Autowired
    private ConfigCache configCache;

    @Autowired
    private InfluxdbHaRelationDAO influxdbHaRelationDAO;

    @Override
    public void load() {
        long begin = System.currentTimeMillis();

        try {
            // 1.Prepare data/configurations from/to LocalCache/MySQL.
            List<InfluxdbClusterDO> influxdbClusterDOList = influxdbClusterDAO.listAll();
            List<InfluxdbHaRelationDO> influxdbHaRelations = influxdbHaRelationDAO.getAll();
            List<InfluxdbTenantRelationDO> influxdbTenantRelationDOList = influxdbTenantRelationDAO.listAll();
            List<TenantDO> tenantDOList = tenantDAO.listAll();
            // Build newInfluxdbClusterDOMap: influxDBClusterId -> InfluxdbClusterDO
            Map<String, InfluxdbClusterDO> newInfluxdbClusterDOMap = buildInfluxdbClusterDOMap(influxdbClusterDOList);
            newInfluxdbClusterDOMap = extendInfluxdbClusterDOMap(newInfluxdbClusterDOMap, influxdbHaRelations);
            Map<String, InfluxdbClusterDO> oldInfluxdbClusterDOMap = influxHandlerFactory.getInfluxdbClusterDOMap();
            // Map: influxdbClusterId -> InfluxHandler
            Map<String, InfluxHandler> influxHandlerMapById = influxHandlerFactory.getInfluxHandlerMapOfId();
            InfluxHandler oldDefaultInfluxdbCluster = influxHandlerFactory.getDefaultInfluxHandler();
            // (Cube-site extra:)set Cache map: influx database -> tenantName
            Map<String, String> influxDatabaseToTenantNameMap = Instance.ofNullable(tenantDOList)
                    .stream()
                    .collect(Collectors.toMap(tenantDO -> tenantDO.getName().toLowerCase(), TenantDO::getName));
            configCache.setInfluxDatabaseToTenantNameMap(influxDatabaseToTenantNameMap);

            // 2.Build new InfluxHandler if configurations are changed
            List<String> changedInfluxClusterIdList = Lists.newArrayList();
            List<InfluxHandler> changedInfluxHandlerList = Lists.newArrayList();
            newInfluxdbClusterDOMap.forEach((influxdbClusterId, newInfluxdbClusterDO) -> {
                InfluxdbClusterDO oldInfluxdbClusterDO = oldInfluxdbClusterDOMap.get(influxdbClusterId);
                boolean isConfigChanged = isInfluxClusterChanged(oldInfluxdbClusterDO, newInfluxdbClusterDO);
                if (!isConfigChanged) {
                    return;
                }
                // Influx Cluster configurations are changed.
                InfluxHandler oldInfluxHandler = influxHandlerMapById.get(influxdbClusterId);
                if (oldInfluxHandler != null) {
                    // Collect changed InfluxHandler old links. They will be closed later on.
                    changedInfluxHandlerList.add(oldInfluxHandler);
                    // Collect changed influxdbClusterIds. They will be used to update tenant relations.
                    changedInfluxClusterIdList.add(influxdbClusterId);
                }
                // Build new InfluxHandler and update influxHandlerMapById.
                InfluxHandler newInfluxHandler = buildInfluxHandler(newInfluxdbClusterDO);
                influxHandlerMapById.put(influxdbClusterId, newInfluxHandler);
            });
            influxHandlerFactory.setInfluxdbClusterDOMap(newInfluxdbClusterDOMap);
            // Update default InfluxHandler if configurations are changed
            InfluxdbClusterDO defaultInfluxdbClusterDO = getDefaultInfluxdbCluster(influxdbClusterDOList);
            if (defaultInfluxdbClusterDO == null) {
                log.error("getDefaultInfluxdbCluster does not exist!");
                return;
            }
            String defaultInfluxdbClusterId = defaultInfluxdbClusterDO.getId();
            InfluxHandler defaultInfluxHandler = influxHandlerMapById.get(defaultInfluxdbClusterId);
            if (oldDefaultInfluxdbCluster == null || !oldDefaultInfluxdbCluster.getInfluxdbClusterId().equals(defaultInfluxdbClusterId)) {
                influxHandlerFactory.setDefaultInfluxHandler(defaultInfluxHandler);
            }

            // 3.Update tenant relation of changed InfluxDB cluster
            // Map: tenantName -> InfluxHandler
            Map<String, InfluxHandler> influxHandlerMapOfTenant = influxHandlerFactory.getInfluxHandlerMapOfTenant();
            // Map: tenantId -> tenantName
            Map<String, String> tenantIdToNameMap = Instance.ofNullable(tenantDOList).stream()
                    .collect(Collectors.toMap(TenantDO::getId, TenantDO::getName));
            // Map: tenantName -> influxdbClusterId
            Map<String, String> influxdbTenantRelationMap = Instance.ofNullable(influxdbTenantRelationDOList)
                    .stream().filter(e -> tenantIdToNameMap.get(e.getRelatedTenantId()) != null)
                    .collect(Collectors.toMap(e -> tenantIdToNameMap.get(e.getRelatedTenantId()), InfluxdbTenantRelationDO::getInfluxdbClusterId));
            // Add tenant relation for default InfluxDB cluster
            Instance.ofNullable(tenantDOList).forEach(tenantDO -> {
                String tenantName = tenantDO.getName();
                influxdbTenantRelationMap.putIfAbsent(tenantName, defaultInfluxdbClusterId);
            });
            // Update tenant relations if needed
            influxdbTenantRelationMap.forEach((tenantName, newInfluxdbClusterId) -> {
                InfluxHandler oldInfluxHandler = influxHandlerFactory.getHandlerByTenantName(tenantName);
                InfluxHandler influxHandlerGetById = influxHandlerMapById.get(newInfluxdbClusterId);
                InfluxHandler newInfluxHandler = (influxHandlerGetById != null ? influxHandlerGetById : defaultInfluxHandler);
                // For new added tenant relation, the old InfluxHandler does not exist.
                if (oldInfluxHandler == null) {
                    influxHandlerMapOfTenant.put(tenantName, newInfluxHandler);
                    return;
                }
                // The tenant relation is bound to another InfluxDB cluster.
                String oldInfluxdbClusterId = oldInfluxHandler.getInfluxdbClusterId();
                if (!oldInfluxdbClusterId.equals(newInfluxdbClusterId)) {
                    influxHandlerMapOfTenant.put(tenantName, newInfluxHandler);
                    return;
                }
                // The tenant relation binding is not changed. But the InfluxDB cluster configuration is changed.
                if (changedInfluxClusterIdList.contains(newInfluxdbClusterId)) {
                    influxHandlerMapOfTenant.put(tenantName, newInfluxHandler);
                }
            });

            // 4.Close old InfluxHandler connections
            changedInfluxHandlerList.forEach(InfluxHandler::close);

        } catch (Exception e) {
            log.error("Load InfluxDB configuration cache error! ", e);
        }

        long end = System.currentTimeMillis();
        log.info("load InfluxDB configuration cache time cost: {} ms.", end - begin);
    }

    private InfluxdbClusterDO getDefaultInfluxdbCluster(List<InfluxdbClusterDO> influxdbClusterDOList) {
        Optional<InfluxdbClusterDO> optional = Instance.ofNullable(influxdbClusterDOList)
                .stream().filter(InfluxdbClusterDO::getIsDefault).findFirst();
        return optional.orElse(null);
    }

    private Map<String, InfluxdbClusterDO> buildInfluxdbClusterDOMap(List<InfluxdbClusterDO> influxdbClusterDOList) {
        return Instance.ofNullable(influxdbClusterDOList)
                .stream().collect(Collectors.toMap(InfluxdbClusterDO::getId, influxdbClusterDO -> {
                    try {
                        influxdbClusterDO.setUsername(rsaService.decrypt(influxdbClusterDO.getUsername()));
                        influxdbClusterDO.setPassword(rsaService.decrypt(influxdbClusterDO.getPassword()));
                    } catch (Exception exception) {
                        log.error("Decrypting InfluxdbClusterDO error! Cluster name: {}. ", influxdbClusterDO.getName(), exception);
                    }
                    return influxdbClusterDO;
                }));
    }

    private InfluxHandler buildInfluxHandler(InfluxdbClusterDO influxdbClusterDO) {
        InfluxHandler influxHandler = null;
        try {
            if (!CollectionUtils.isEmpty(influxdbClusterDO.getServerUrls())
                    && InfluxHaStatus.HA_CLUSTERS.getValue().equals(influxdbClusterDO.getHaStatus())) {
                influxHandler = new InfluxHaHandler(
                        influxdbClusterDO.getId(),
                        influxdbClusterDO.getServerUrls(),
                        null,
                        influxdbClusterDO.getUsername(),
                        influxdbClusterDO.getPassword());
                influxHandler.setClusterType(ClusterType.fromTypeCode(influxdbClusterDO.getType()));
            } else {
                influxHandler = new InfluxHandler(
                        influxdbClusterDO.getId(),
                        influxdbClusterDO.getServerUrl(),
                        null,
                        influxdbClusterDO.getUsername(),
                        influxdbClusterDO.getPassword());
                influxHandler.setClusterType(ClusterType.fromTypeCode(influxdbClusterDO.getType()));
            }
            log.info("init {} influxdb  ", influxdbClusterDO.getHaStatus());

        } catch (Exception e) {
            log.error("buildInfluxHandler error! InfluxDB cluster name: {}. ", influxdbClusterDO.getName(), e);
        }
        return influxHandler;
    }

    private boolean isInfluxClusterChanged(InfluxdbClusterDO oldInfluxdbClusterDO, InfluxdbClusterDO newInfluxdbClusterDO) {
        if (oldInfluxdbClusterDO == null) {
            return true;
        }
        if (!oldInfluxdbClusterDO.getServerUrl().equals(newInfluxdbClusterDO.getServerUrl())) {
            return true;
        }
        if (!oldInfluxdbClusterDO.getUsername().equals(newInfluxdbClusterDO.getUsername())) {
            return true;
        }
        //one of relation is null while the other is not null
        if (oldInfluxdbClusterDO.getServerUrls() == null ^ newInfluxdbClusterDO.getServerUrls() == null) {
            return true;
        }
        //both not null but relation list is not equal
        if ((oldInfluxdbClusterDO.getServerUrls() != null && newInfluxdbClusterDO.getServerUrls() != null)
                && !oldInfluxdbClusterDO.getServerUrls().equals(newInfluxdbClusterDO.getServerUrls())) {
            return true;
        }
        return !oldInfluxdbClusterDO.getPassword().equals(newInfluxdbClusterDO.getPassword());
    }


    /**
     * extend with HA Relations
     *
     * @param influxdbClusterDOMap
     * @param relations
     * @return
     */
    private Map<String, InfluxdbClusterDO> extendInfluxdbClusterDOMap(Map<String, InfluxdbClusterDO> influxdbClusterDOMap, List<InfluxdbHaRelationDO> relations) {
        Map<String, List<InfluxdbHaRelationDO>> relationMap = relations.stream().collect(Collectors.groupingBy(InfluxdbHaRelationDO::getInfluxClusterId));
        for (Map.Entry<String, List<InfluxdbHaRelationDO>> en : relationMap.entrySet()) {
            InfluxdbClusterDO influxdbClusterDO = influxdbClusterDOMap.get(en.getKey());
            if (influxdbClusterDO != null) {
                influxdbClusterDO.setServerUrls(en.getValue());
            }
        }
        return influxdbClusterDOMap;
    }
}
