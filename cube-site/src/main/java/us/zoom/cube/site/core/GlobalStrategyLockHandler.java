package us.zoom.cube.site.core;

import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.site.infra.utils.IdUtils;
import us.zoom.cube.site.lib.input.network.DetectionConfigInput;
import us.zoom.infra.dao.model.*;
import us.zoom.infra.dao.service.*;
import us.zoom.infra.enums.MonitorTypeEnum;
import us.zoom.infra.enums.StrategyTypeEnum;
import us.zoom.infra.utils.Instance;
import us.zoom.infra.utils.IpUtils;
import us.zoom.cube.lib.utils.JsonUtils;
import com.fasterxml.jackson.core.type.TypeReference;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class GlobalStrategyLockHandler {
    @Autowired
    private NetworkStrategyDetectionHandler networkStrategyDetectionHandler;

    @Autowired
    private NetworkStrategyDetectionRelationHandler networkStrategyDetectionRelationHandler;

    @Autowired
    private NetworkDetectionInfoHandler networkDetectionInfoHandler;

    @Autowired
    private CmdbServerHandler cmdbServerHandler;

    @Autowired
    private NetworkDetectionInfoDAO networkDetectionInfoDAO;

    @Autowired
    private CmdbServerDAO cmdbServerDAO;

    @Autowired
    private NetworkStrategyDAO networkStrategyDAO;

    @Autowired
    private NetworkStrategyDetectionRelationDAO networkStrategyDetectionRelationDAO;

    @Autowired
    private DistributedLockDAO distributedLockDAO;

    private String lockType = "strategy_global_assigner";

    private Logger monitorLog = LoggerFactory.getLogger("Monitor");

    private String tryLockFail = "tryLockFail";

    private int lockTimeOut = 10;

    private int retrySleepTime = 5000;

    public synchronized Boolean synchronizeDetectionAndStrategy(DetectionConfigInput detectionConfigInput,List<NetworkDetectionInfoDO> networkDetectionInfos, NetworkStrategyDO networkStrategyDO, StrategyTypeEnum typeEnum) throws Exception {
        // 
        long startTime = System.currentTimeMillis();
        while (!tryLock()) {
            //
            if (System.currentTimeMillis() - startTime > 60000) {
                log.info("lock fail from {}" + IpUtils.getLocalIP());
                printMetrics(tryLockFail,IpUtils.getLocalIP(),networkStrategyDO.getId(),typeEnum,System.currentTimeMillis() - startTime);
                return false;
            }
            try {
                Thread.sleep(retrySleepTime);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
        log.info("try get the lock success , local ip is {},strategy type is {}" ,IpUtils.getLocalIP(),typeEnum);
        try {
            if (typeEnum == StrategyTypeEnum.add) {
                addStrategyAndDetection(networkDetectionInfos, networkStrategyDO);
            }else if (typeEnum == StrategyTypeEnum.delete) {
                List<NetworkStrategyDetectionRelationDO> strategyDetectionRelationList = networkStrategyDetectionRelationHandler.selectByStrategyId(networkStrategyDO.getId());
                List<String> detectionIds = Instance.ofNullable(strategyDetectionRelationList).stream().map(item -> item.getDetectionId()).collect(Collectors.toList());
                List<NetworkDetectionInfoDO> networkDetectionInfoDOList = networkDetectionInfoHandler.selectByDetectionIds(detectionIds);
                deleteStrategyAndDetection(networkDetectionInfoDOList, networkStrategyDO);
            } else if (typeEnum == StrategyTypeEnum.updateInterval) {
                updateIntervalStrategyAndDetection(networkDetectionInfos,networkStrategyDO);
            }else if (typeEnum == StrategyTypeEnum.DeleteServiceConfig) {
                deleteServiceConfig(detectionConfigInput);
            } else if (typeEnum == StrategyTypeEnum.AddServiceConfig) {
                addServiceConfig(detectionConfigInput);
            }
            tryUnlock();
            return true;
        } catch (Exception e) {
            tryUnlock();
            throw e;
        }
    }

    private void updateIntervalStrategyAndDetection(List<NetworkDetectionInfoDO> networkDetectionInfos, NetworkStrategyDO networkStrategyDO) throws Exception{
        int interval = networkStrategyDO.getInterval();
        List<NetworkDetectionInfoDO> detectionToUpdate = new ArrayList<>();
        Map<String, Double> loadRateToUpdateMap = new HashMap<>();
        for (NetworkDetectionInfoDO detection : networkDetectionInfos){
             if ((detection.getInterval()<interval && detection.getCounts() == 1)||(detection.getInterval()>interval)){
                 Double loadRate = 1.00/interval -1.00/detection.getInterval();
                 detection.setInterval(interval);
                 detectionToUpdate.add(detection);
                 if (loadRateToUpdateMap.containsKey(detection.getSourceIp())) {
                     loadRateToUpdateMap.put(detection.getSourceIp(), loadRateToUpdateMap.get(detection.getSourceIp()) + loadRate);
                 } else {
                     loadRateToUpdateMap.put(detection.getSourceIp(), loadRate);
                 }
             }
        }
        List<CmdbServerDO> cmdbServerToUpdate = cmdbServerHandler.listByIps(loadRateToUpdateMap.keySet());
        for (CmdbServerDO cmdbServerDO : cmdbServerToUpdate) {
            cmdbServerDO.setLoadRate(cmdbServerDO.getLoadRate() + loadRateToUpdateMap.get(cmdbServerDO.getPublicIpMac()));
        }
        networkStrategyDetectionHandler.updateIntervalStrategyAndDetection(networkStrategyDO, detectionToUpdate, cmdbServerToUpdate);
    }

    private void addStrategyAndDetection(List<NetworkDetectionInfoDO> networkDetectionInfos, NetworkStrategyDO networkStrategyDO) throws Exception {
        // etection list // etection list
        List<NetworkDetectionInfoDO> networkDetectionTOAdd = new ArrayList<>();
        List<NetworkDetectionInfoDO> networkDetectionTOUpdate = new ArrayList<>();
        // serverip,loadRateId>
        Map<String, Double> loadRateToUpdateMap = new HashMap<>();
        // networkStrategy detection
        List<NetworkStrategyDetectionRelationDO> networkStrategyDetectionRelationList = new ArrayList<>();

        List<String> detectionKeyList = Instance.ofNullable(networkDetectionInfos).stream().map(networkDetectionInfo -> networkDetectionInfo.getDetectionKey()).collect(Collectors.toList());
        List<NetworkDetectionInfoDO> detectionsInDB = networkDetectionInfoDAO.findByKeys(detectionKeyList);
        Map<String, NetworkDetectionInfoDO> detectionsInDBMap = Instance.ofNullable(detectionsInDB).stream().collect(Collectors.toMap(NetworkDetectionInfoDO::getDetectionKey, networkDetectionInfo -> networkDetectionInfo));
        for (NetworkDetectionInfoDO networkDetection : networkDetectionInfos) {
            Double loadRateToUpdate = 0.00;
            if (detectionsInDBMap.containsKey(networkDetection.getDetectionKey())) {
                NetworkDetectionInfoDO detectionInDB = detectionsInDBMap.get(networkDetection.getDetectionKey());
                if (detectionInDB.getInterval() > networkDetection.getInterval()) {
                    loadRateToUpdate = 1.00 / networkDetection.getInterval() - 1.00 / detectionInDB.getInterval();
                    detectionInDB.setInterval(networkDetection.getInterval());
                }
                detectionInDB.setCounts(detectionInDB.getCounts()+1);
                networkDetectionTOUpdate.add(detectionInDB);
                networkDetection = detectionInDB;
            } else {
                networkDetectionTOAdd.add(networkDetection);
                loadRateToUpdate = 1.00 / networkDetection.getInterval();
            }
            if (loadRateToUpdateMap.containsKey(networkDetection.getSourceIp())) {
                loadRateToUpdateMap.put(networkDetection.getSourceIp(), loadRateToUpdateMap.get(networkDetection.getSourceIp()) + loadRateToUpdate);
            } else {
                loadRateToUpdateMap.put(networkDetection.getSourceIp(), loadRateToUpdate);
            }
            NetworkStrategyDetectionRelationDO relationDO = new NetworkStrategyDetectionRelationDO();
            relationDO.setId(IdUtils.generateId());
            relationDO.setStrategyId(networkStrategyDO.getId());
            relationDO.setDetectionId(networkDetection.getId());
            networkStrategyDetectionRelationList.add(relationDO);
        }
        List<CmdbServerDO> cmdbServerTOUpdate= cmdbServerHandler.listByIps(loadRateToUpdateMap.keySet());
        for (CmdbServerDO server : cmdbServerTOUpdate){
            Double loadRate = server.getLoadRate() + loadRateToUpdateMap.get(server.getPublicIpMac());
            Assert.isTrue(loadRate < server.getLoadRateThreshold(),"loadRate over Threshold , can not add this strategy , please reduce host count or increase interval");
            server.setLoadRate(loadRate);
        }
        networkStrategyDetectionHandler.addStrategyAndDetection(networkDetectionTOAdd, networkDetectionTOUpdate, networkStrategyDO, cmdbServerTOUpdate, networkStrategyDetectionRelationList);
    }

    private void deleteStrategyAndDetection(List<NetworkDetectionInfoDO> networkDetectionInfos, NetworkStrategyDO networkStrategyDO) throws Exception {
        List<NetworkDetectionInfoDO> detectionListToDelete = new ArrayList<>();
        List<NetworkDetectionInfoDO> detectionListToUpDate = new ArrayList<>();
        Map<String, Double> loadRateToUpdateMap = new HashMap<>();
        for (NetworkDetectionInfoDO detectionInfoDO : networkDetectionInfos) {
            if (detectionInfoDO.getCounts() == 1) {
                detectionListToDelete.add(detectionInfoDO);
                if (loadRateToUpdateMap.containsKey(detectionInfoDO.getSourceIp())) {
                    loadRateToUpdateMap.put(detectionInfoDO.getSourceIp(), loadRateToUpdateMap.get(detectionInfoDO.getSourceIp()) - 1.00 / detectionInfoDO.getInterval());
                } else {
                    loadRateToUpdateMap.put(detectionInfoDO.getSourceIp(), -1.00 / detectionInfoDO.getInterval());
                }
            }else {
                detectionInfoDO.setCounts(detectionInfoDO.getCounts()-1);
                detectionListToUpDate.add(detectionInfoDO);
            }
        }
        List<CmdbServerDO> cmdbServerDOList = cmdbServerHandler.listByIps(loadRateToUpdateMap.keySet());
        for (CmdbServerDO cmdbServerDO : cmdbServerDOList) {
            cmdbServerDO.setLoadRate(cmdbServerDO.getLoadRate() + loadRateToUpdateMap.get(cmdbServerDO.getPublicIpMac()));
        }
        networkStrategyDetectionHandler.deleteStrategyAndDetection(networkStrategyDO, detectionListToDelete, detectionListToUpDate,cmdbServerDOList);
    }

    private void addServiceConfig(DetectionConfigInput detectionConfigInput) {
        List<NetworkStrategyDetectionRelationDO> strategyDetectionRelationListToAdd = new ArrayList<>();
        Map<String, NetworkDetectionInfoDO> detectionInfoDOMap = new HashMap<>();
        List<NetworkStrategyDO> strategyList = networkStrategyDAO.selectByService(detectionConfigInput.getService());
        Set<String> serverIds = new HashSet<>();
        for (NetworkStrategyDO strategy : strategyList) {
            serverIds.addAll(JsonUtils.toObjectByTypeRef(strategy.getSourceAgents(), new TypeReference<List<String>>() {}));
            serverIds.addAll(JsonUtils.toObjectByTypeRef(strategy.getDestAgents(), new TypeReference<List<String>>() {}));
        }
        List<CmdbServerDO> servers = cmdbServerHandler.listByIds((new ArrayList<String>(serverIds)));
        Map<String, CmdbServerDO> serverMap = servers.stream().collect(Collectors.toMap(CmdbServerDO::getId, e -> e));
        for (NetworkStrategyDO strategy : strategyList) {
            List<CmdbServerDO> sourceServers = new ArrayList<>();
            List<String> sourceIds = JsonUtils.toObjectByTypeRef(strategy.getSourceAgents(), new TypeReference<List<String>>() {});
            for (String sourceId : sourceIds) {
                sourceServers.add(serverMap.get(sourceId));
            }
            List<CmdbServerDO> destServers = new ArrayList<>();
            List<String> destIds = JsonUtils.toObjectByTypeRef(strategy.getDestAgents(), new TypeReference<List<String>>() {});
            for (String destId : destIds) {
                destServers.add(serverMap.get(destId));
            }
            if (strategy.getSourceService().equals(detectionConfigInput.getService())) {
                spiltDetection(destServers, sourceServers, detectionConfigInput, strategy, detectionInfoDOMap,strategyDetectionRelationListToAdd);
            }
            if (strategy.getDestService().equals(detectionConfigInput.getService())) {
                spiltDetection(sourceServers, destServers, detectionConfigInput, strategy,detectionInfoDOMap,strategyDetectionRelationListToAdd);
            }
        }
        Map<String, Double> loadRateToUpdateMap = new HashMap<>();
        List<NetworkDetectionInfoDO> detectionInfoDOSToAdd = new ArrayList<>();
        for (Map.Entry<String, NetworkDetectionInfoDO> entry :  detectionInfoDOMap.entrySet()){
            NetworkDetectionInfoDO detectionInfoDO = entry.getValue();
            detectionInfoDOSToAdd.add(detectionInfoDO);
            if (loadRateToUpdateMap.containsKey(detectionInfoDO.getSourceIp())) {
                loadRateToUpdateMap.put(detectionInfoDO.getSourceIp(), loadRateToUpdateMap.get(detectionInfoDO.getSourceIp()) + 1.00 / detectionInfoDO.getInterval());
            } else {
                loadRateToUpdateMap.put(detectionInfoDO.getSourceIp(), 1.00 / detectionInfoDO.getInterval());
            }
        }
        List<CmdbServerDO> cmdbServerListToUpdate = cmdbServerHandler.listByIps(loadRateToUpdateMap.keySet());
        for (CmdbServerDO cmdbServerDO : cmdbServerListToUpdate) {
            cmdbServerDO.setLoadRate(cmdbServerDO.getLoadRate() + loadRateToUpdateMap.get(cmdbServerDO.getPublicIpMac()));
        }
        networkStrategyDetectionHandler.addServiceConfig(detectionInfoDOSToAdd, strategyDetectionRelationListToAdd, cmdbServerListToUpdate,detectionConfigInput);
    }

    private void deleteServiceConfig(DetectionConfigInput detectionConfigInput) {
        List<NetworkStrategyDO> strategyList = networkStrategyDAO.selectByService(detectionConfigInput.getService());
        Set<String> serverIds = new HashSet<>();
        for (NetworkStrategyDO strategy : strategyList) {
            if (strategy.getSourceService().equals(detectionConfigInput.getService())) {
                serverIds.addAll(JsonUtils.toObjectByTypeRef(strategy.getSourceAgents(), new TypeReference<List<String>>() {}));
            }
            if (strategy.getDestService().equals(detectionConfigInput.getService())) {
                serverIds.addAll(JsonUtils.toObjectByTypeRef(strategy.getDestAgents(), new TypeReference<List<String>>() {}));
            }
        }
        List<CmdbServerDO> servers = cmdbServerHandler.listByIds((new ArrayList<String>(serverIds)));
        List<String> serverIps = servers.stream().map(item -> item.getPublicIpMac()).collect(Collectors.toList());
        List<NetworkDetectionInfoDO> networkDetectionListToDelete = networkDetectionInfoDAO.listByServerIdAndProtocol(serverIps, detectionConfigInput.getProtocol());
        Map<String, Double> loadRateToUpdateMap = new HashMap<>();
        for (NetworkDetectionInfoDO detectionInfoDO : networkDetectionListToDelete) {
            if (loadRateToUpdateMap.containsKey(detectionInfoDO.getSourceIp())) {
                loadRateToUpdateMap.put(detectionInfoDO.getSourceIp(), loadRateToUpdateMap.get(detectionInfoDO.getSourceIp()) - 1.00 / detectionInfoDO.getInterval());
            } else {
                loadRateToUpdateMap.put(detectionInfoDO.getSourceIp(), -1.00 / detectionInfoDO.getInterval());
            }
        }
        List<CmdbServerDO> cmdbServerListToUpdate = cmdbServerHandler.listByIps(loadRateToUpdateMap.keySet());
        for (CmdbServerDO cmdbServerDO : cmdbServerListToUpdate) {
            cmdbServerDO.setLoadRate(cmdbServerDO.getLoadRate() + loadRateToUpdateMap.get(cmdbServerDO.getPublicIpMac()));
        }
        networkStrategyDetectionHandler.deleteServiceConfig(networkDetectionListToDelete, cmdbServerListToUpdate, detectionConfigInput);
    }

    private void spiltDetection(List<CmdbServerDO> sourceServerList, List<CmdbServerDO> destServerList, DetectionConfigInput detectionConfigInput, NetworkStrategyDO strategy, Map<String, NetworkDetectionInfoDO> detectionInfoDOMap,List<NetworkStrategyDetectionRelationDO> strategyDetectionRelationListToAdd) {
        for (CmdbServerDO server : sourceServerList) {
            for (CmdbServerDO destServer : destServerList) {
                String detectionKey = server.getPublicIpMac() + "-" + destServer.getPublicIpMac() + "-" + detectionConfigInput.getProtocol() + (detectionConfigInput.getPort() == 0 ? "" : "-"+ detectionConfigInput.getPort());
                if (detectionInfoDOMap.get(detectionKey) != null) {
                    detectionInfoDOMap.get(detectionKey).setCounts(detectionInfoDOMap.get(detectionKey).getCounts() + 1);
                    if (strategy.getInterval()<detectionInfoDOMap.get(detectionKey).getInterval()){
                        detectionInfoDOMap.get(detectionKey).setInterval(strategy.getInterval());
                    }
                } else {
                    NetworkDetectionInfoDO detectionInfo = new NetworkDetectionInfoDO();
                    detectionInfo.setId(IdUtils.generateId());
                    detectionInfo.setDetectionKey(detectionKey);
                    detectionInfo.setSourceIp(server.getPublicIpMac());
                    detectionInfo.setDestIp(destServer.getPublicIpMac());
                    detectionInfo.setProtocol(detectionConfigInput.getProtocol());
                    detectionInfo.setPort(detectionConfigInput.getPort());
                    detectionInfo.setInterval(strategy.getInterval());
                    detectionInfo.setCounts(1);
                    detectionInfoDOMap.put(detectionKey,detectionInfo);
                }
                NetworkStrategyDetectionRelationDO relationDO = new NetworkStrategyDetectionRelationDO();
                relationDO.setId(IdUtils.generateId());
                relationDO.setStrategyId(strategy.getId());
                relationDO.setDetectionId(detectionInfoDOMap.get(detectionKey).getId());
                strategyDetectionRelationListToAdd.add(relationDO);
            }
        }
    }

    private void tryUnlock() {
        List<DistributedLockDO> distributedLockDAOList = distributedLockDAO.listByType(lockType);
        Calendar c = Calendar.getInstance();
        c.setTime(new Date());
        c.add(Calendar.HOUR_OF_DAY, -1);
        Date newDate = c.getTime();
        distributedLockDAOList.get(0).setModifyTime(newDate);
        distributedLockDAO.updatModifyTime(distributedLockDAOList.get(0));
    }

    private boolean tryLock() {
        List<DistributedLockDO> distributedLockDAOList = distributedLockDAO.listByType(lockType);
        if (CollectionUtils.isEmpty(distributedLockDAOList)) {
            return false;
        }
        boolean lockResult = distributedLockDAO.lockWithType(lockType, distributedLockDAOList.get(0).getHandler(), IpUtils.getLocalIP(), lockTimeOut) > 0;
        return lockResult;
    }

    private void printMetrics(String fail , String host, String strategyId, StrategyTypeEnum strategyType, long cost) {
        try {
            Map<String, Object> metrics = new HashMap<>();
            metrics.put("failType",fail);
            metrics.put("cost", cost);
            metrics.put("host", host);
            metrics.put("strategyId", strategyId);
            metrics.put("strategyType", strategyType.name());
            metrics.put("type", MonitorTypeEnum.synchronizeDetectionAndStrategy.name());
            monitorLog.info(JsonUtils.toJsonStringIgnoreExp(metrics));
        } catch (Exception e) {

        }
    }
}
