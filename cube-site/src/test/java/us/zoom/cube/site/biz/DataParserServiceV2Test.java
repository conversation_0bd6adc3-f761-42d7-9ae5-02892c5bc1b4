package us.zoom.cube.site.biz;

import com.google.common.collect.Sets;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.core.env.Environment;
import us.zoom.cube.lib.common.*;
import us.zoom.cube.lib.hub.DataParserRawDataTypeEnum;
import us.zoom.cube.lib.hub.RawDataParseTypeEnum;
import us.zoom.cube.site.biz.syspara.AutoDiscoverParaService;
import us.zoom.cube.site.core.*;
import us.zoom.cube.site.infra.utils.IdUtils;
import us.zoom.cube.site.lib.BasePara;
import us.zoom.cube.site.lib.input.DiscoverItem;
import us.zoom.cube.site.lib.input.OutDiscoverPipelineInput;
import us.zoom.cube.site.lib.input.dataparser.DataParserV2Input;
import us.zoom.cube.lib.utils.JsonUtils;
import us.zoom.mq.client.AsyncMQ;
import us.zoom.mq.client.DefaultAsyncMQ;
import us.zoom.mq.client.pojo.Page;
import us.zoom.mq.common.Result;
import us.zoom.mq.common.entity.TopicInfo;
import us.zoom.mq.common.param.PageParam;
import us.zoom.mq.common.response.TopicResult;

import java.util.*;

/**
 * @Author: luis.zheng
 * @Date: 2021/8/23 1:36
 */
public class DataParserServiceV2Test {
    private DataParserServiceV2 dataParserServiceV2;

    private AuthService authService;
    private UnitTagHandler unitTagHandler;
    private DataParserHandler dataParserHandler;
    private KafkaQueueHandler kafkaQueueHandler;
    private DataParserService dataParserService;
    private AsyncQueueHandler asyncQueueHandler;
    private KafkaClusterHandler kafkaClusterHandler;
    private AsyncMqQueueHandler asyncMqQueueHandler;
    private AsyncQueueGroupService asyncQueueGroupService;
    private DataParserPipelineHandler dataParserPipelineHandler;
    private TaskQueueHandler taskQueueHandler;
    private Environment environment;
    private DataForwardHandler dataForwardHandler;
    private TenantHandler tenantHandler;
    private UserHandler userHandler;
    private ServerHandler serverHandler;
    private AutoDiscoverParaService autoDiscoverParaService;
    private CollectorHandler collectorHandler;
    private AlarmDefinitionHandler alarmDefinitionHandler;
    private MetricsHandler metricsHandler;
    private AutoDiscoverPipeLineHandler autoDiscoverPipeLineHandler;
    private SysParaHandler sysParaHandler;
    private DataParserDeleteService dataParserDeleteService;
    private AsyncmqClusterHandler asyncmqClusterHandler;
    private HubGroovyCheckService hubGroovyCheckService;

    private String userId = "userId";

    @BeforeEach
    public void before() {
        authService = Mockito.mock(AuthService.class);
//        Mockito.doNothing().when(authService).mustAdmin(userId);
        Mockito.doNothing().when(authService).checkAuth(new BasePara());
        unitTagHandler = Mockito.mock(UnitTagHandler.class);
        dataParserHandler = Mockito.mock(DataParserHandler.class);
        kafkaQueueHandler = Mockito.mock(KafkaQueueHandler.class);
        autoDiscoverPipeLineHandler = Mockito.mock(AutoDiscoverPipeLineHandler.class);
        dataParserService = Mockito.mock(DataParserService.class);
        kafkaClusterHandler = Mockito.mock(KafkaClusterHandler.class);
        asyncMqQueueHandler = Mockito.mock(AsyncMqQueueHandler.class);
        asyncQueueGroupService = Mockito.mock(AsyncQueueGroupService.class);
        dataParserPipelineHandler = Mockito.mock(DataParserPipelineHandler.class);
        taskQueueHandler = Mockito.mock(TaskQueueHandler.class);
        asyncQueueHandler = Mockito.mock(AsyncQueueHandler.class);
        environment = Mockito.mock(Environment.class);
        dataForwardHandler = Mockito.mock(DataForwardHandler.class);
        tenantHandler = Mockito.mock(TenantHandler.class);
        userHandler = Mockito.mock(UserHandler.class);
        serverHandler = Mockito.mock(ServerHandler.class);
        autoDiscoverParaService = Mockito.mock(AutoDiscoverParaService.class);
        asyncmqClusterHandler = Mockito.mock(AsyncmqClusterHandler.class);
        hubGroovyCheckService = Mockito.mock(HubGroovyCheckService.class);
        Mockito.when(environment.getProperty("async.mq.endpoint")).thenReturn("endpoint");
        Mockito.when(environment.getProperty("async.mq.username")).thenReturn("username");
        Mockito.when(environment.getProperty("async.mq.password")).thenReturn("password");

        dataParserServiceV2 = new DataParserServiceV2(authService, kafkaQueueHandler, dataParserHandler, autoDiscoverPipeLineHandler, dataParserService, asyncMqQueueHandler, dataParserPipelineHandler, environment, dataForwardHandler, tenantHandler, userHandler, autoDiscoverParaService,
                collectorHandler, alarmDefinitionHandler, metricsHandler, sysParaHandler, dataParserDeleteService, asyncmqClusterHandler, hubGroovyCheckService);
    }

    @Test
    public void testAdd() throws Exception {
        DataParserV2Input v2 = new DataParserV2Input();
        v2.setName("name");

//        List<DataParserQueueInput> inputQueues = new ArrayList<>();
//        inputQueues.add(createInputQueue("topic1"));
//        inputQueues.add(createInputQueue("topic2"));
//        v21.setInputQueues(inputQueues);

        v2.setRawDataType(DataParserRawDataTypeEnum.json.getCode());
        v2.setRawDataParseType(RawDataParseTypeEnum.groovy.name());
        v2.setRawDataParseRule("setRawDataParseRule");
        v2.setInvokeFunction("invokeFunction");
        v2.setUseStatus(DataParserUseStatusEnum.use.getValue());
        System.out.println(JsonUtils.toJsonString(v2));
        dataParserServiceV2.addDataParser(v2);
    }

    @Test
    public void testAsyncMQ() {

    }

    private AsyncMQ createAsyncMQClient() {
        return new DefaultAsyncMQ("https://asyncmq.zoomdev.us", "", "");
    }

    @Test
    public void testAdmin() {
        AsyncMQ asyncMQ = createAsyncMQClient();

        PageParam pageParam = PageParam.of(1, 20);
        Result<Page<TopicResult>> result = asyncMQ.admin().searchTopicByNameLike("", pageParam);
        Set<String> topics = new HashSet<>();
        if (result.isSuccess()) {
            result.getResponse().getContent().forEach(topic -> {
                topics.add(topic.getName());
                System.out.println(topic.getName() + "--->dc name:" + topic.getDcName() + "--->cluster name:" + topic.getChannelName() + "--->partition:" + topic.getChannels());
            });
        }
        System.out.println("--------");
//        Result<Map<String, TopicInfo>> getTopicInfo(Set<String> topics);
        Result<Map<String, TopicInfo>> result1 = asyncMQ.admin().getTopicInfo(topics);
        if (result1.isSuccess()) {
            Map<String, TopicInfo> map = result1.getResponse();
            map.forEach((topicName, info) -> {
                System.out.println(info.getName() + "--->dc name:" + info.getDcName() + "--->cluster name:" + info.getChannelId() + "--->partition:" + info.getChannelId());
            });
            System.out.println(map.size());
        }
    }

    @Test
    public void test() {
        AsyncMQ asyncMQ = createAsyncMQClient();

        Result<Map<String, TopicInfo>> result1 = asyncMQ.admin().getTopicInfo(Sets.newHashSet("ss"));
        if (result1.isSuccess()) {
            result1.getResponse().forEach((topicName, info) -> {
                System.out.println(info.getName() + "--->dc name:" + info.getDcName() + "--->cluster name:" + info.getChannelId() + "--->partition:" + info.getChannelId());
            });
        }
    }

    @Test
    public void testSaveDiscoverPipeline() {
        List<OutDiscoverPipelineInput> outDiscoverPipelineInputList = makeOutDiscoverPipelineInput();
        dataParserServiceV2.saveDiscoverPipeline(outDiscoverPipelineInputList);
    }

    private List<OutDiscoverPipelineInput> makeOutDiscoverPipelineInput() {
        List<OutDiscoverPipelineInput> outDiscoverPipelineInputList = new ArrayList<>();
        OutDiscoverPipelineInput outDiscoverPipelineInput = new OutDiscoverPipelineInput();
        outDiscoverPipelineInput.setDataParserId("0a2ba6c9-3cde-4cdb-a1a6-5c533717faa8");
        outDiscoverPipelineInput.setTenantId("c38c6ff0-e5ff-40de-9737-8f40ed20de11");
        outDiscoverPipelineInput.setStatus("create");
        outDiscoverPipelineInput.setPipelineName("unit-test" + IdUtils.generateId());
        outDiscoverPipelineInput.setFilterRule("measure=='" + outDiscoverPipelineInput.getPipelineName() + "'");
        outDiscoverPipelineInput.getTags().add(makeDiscoverItem("appName", "string", null));
        outDiscoverPipelineInput.getTags().add(makeDiscoverItem("cluster", "string", null));
        outDiscoverPipelineInput.getFields().add(makeDiscoverItem("us", "number", null));
        outDiscoverPipelineInput.getHisFields().add(makeDiscoverItem("duration1", null, "[100, 200,500]"));
        outDiscoverPipelineInput.getSumFields().add(makeDiscoverItem("size", null, "[0.5, 0.9, 0.95]"));
        outDiscoverPipelineInputList.add(outDiscoverPipelineInput);
        return outDiscoverPipelineInputList;
    }

    private DiscoverItem makeDiscoverItem(String key, String type, String schema) {
        DiscoverItem discoverItem = new DiscoverItem();
        discoverItem.setKey(key);
        discoverItem.setType(type);
        discoverItem.setSchema(schema);
        return discoverItem;
    }
}