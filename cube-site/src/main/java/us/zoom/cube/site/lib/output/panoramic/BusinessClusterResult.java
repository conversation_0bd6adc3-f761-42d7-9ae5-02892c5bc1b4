package us.zoom.cube.site.lib.output.panoramic;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

@Data
public class BusinessClusterResult<T> extends BusinessDetailResult implements Serializable {

    @Serial
    private static final long serialVersionUID = 4789081989200339998L;
    private List<ColumnConfigResult> columnNameList;
    private String clusterId;
    private String clusterSla;
    private long totalCount;
    private long errorCount;
}
