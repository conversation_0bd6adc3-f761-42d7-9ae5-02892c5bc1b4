package us.zoom.cube.site.biz;

import com.zoom.op.monitor.domain.alarm.Channel;
import com.zoom.op.monitor.domain.alarm.ChannelParameter;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ActiveProfiles;
import us.zoom.cube.notice.biz.service.AlarmIMSender;
import us.zoom.cube.site.core.ChannelHandler;
import us.zoom.cube.site.core.RsaService;
import us.zoom.cube.site.core.TenantHandler;
import us.zoom.infra.dao.model.TenantDO;
import us.zoom.infra.model.alarm.AlarmLevel;

import java.util.ArrayList;
import java.util.Map;
import java.util.Optional;

@ActiveProfiles("perf_for_local")
public class DataFlowServiceV2Test extends BaseTest {

    @Autowired
    private DataFlowService dataFlowService;

    @Autowired
    private ChannelHandler channelHandler;

    @Autowired
    private TenantHandler tenantHandler;

    @Autowired
    private AlarmIMSender alarmIMSender;

    @Autowired
    private RsaService rsaService;

    @Test
    public void testAlert2ZoomChat() throws Exception {
        TenantDO tenantDO = tenantHandler.getTenantByName("Meeting_Web_asyncmq-application");
        Optional<Channel> channelOptional = channelHandler.findByNameAndTenantId("DEV-Alert-AsyncMQ-P0/P1", tenantDO.getId());
        if (channelOptional.isPresent()) {
            Channel channel = channelOptional.get();

            String token = "";
            String endpoint = "";
            for (ChannelParameter e : channel.getParameters()) {
                if (e.getName().equals("Verification Token")) {
                    token = rsaService.decrypt(e.getValue());
                }
                if (e.getName().equals("Endpoint")) {
                    endpoint = e.getValue();
                }
            }
            Map<String,Object> map = alarmIMSender.send("", new ArrayList(), "this is test title", "this is test alarm name", AlarmLevel.INFO,
                    endpoint, token, false, "", "");
            Assertions.assertTrue((boolean)map.get("status"));
        }
    }
}
