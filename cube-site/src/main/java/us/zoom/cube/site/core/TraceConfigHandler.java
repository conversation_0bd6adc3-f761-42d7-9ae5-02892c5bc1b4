package us.zoom.cube.site.core;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import us.zoom.cube.site.api.intercept.AuthInterceptor;
import us.zoom.cube.site.core.config.CacheLoader;
import us.zoom.cube.site.infra.utils.IdUtils;
import us.zoom.cube.site.lib.input.TraceConfigInput;
import us.zoom.cube.site.lib.query.TraceConfigQuery;
import us.zoom.infra.dao.model.TraceConfigDO;
import us.zoom.infra.dao.model.UnitTagDO;
import us.zoom.infra.dao.service.TraceConfigDAO;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @authoer: eason.jia
 * @createDate: 2024/8/14
 * @description:
 */
@Slf4j
@Component
public class TraceConfigHandler implements CacheLoader {

    @Autowired
    private TraceConfigDAO traceConfigDAO;

    @Autowired
    private UnitTagHandler unitTagHandler;

    private Map<String, List<TraceConfigDO>> traceConfigCacheMap = Maps.newHashMap();


    public String addTraceConfig(TraceConfigInput traceConfigInput) {
        if (StringUtils.isBlank(traceConfigInput.getId())) {
            traceConfigInput.setId(IdUtils.generateId());
        }
        TraceConfigDO traceConfigDO = toDO(traceConfigInput);
        traceConfigDO.setCreator(AuthInterceptor.getUserName());
        traceConfigDAO.add(traceConfigDO);
        return traceConfigDO.getId();
    }

    public TraceConfigDO getTraceConfigById(String id) {
        return traceConfigDAO.getById(id);
    }

    public void deleteTraceConfigById(String id) {
        traceConfigDAO.deleteById(id, AuthInterceptor.getUserName());
    }

    public void modifyTraceConfig(TraceConfigInput traceConfigInput) {
        if (StringUtils.isBlank(traceConfigInput.getId())) {
            throw new IllegalArgumentException("trace config id can not be empty");
        }
        TraceConfigDO traceConfigDO = toDO(traceConfigInput);
        traceConfigDAO.update(traceConfigDO);
    }

    public List<TraceConfigDO> searchByConditions(TraceConfigQuery traceConfigQuery, int pageIndex, int pageSize) {
        if (traceConfigQuery == null) {
            throw new IllegalArgumentException("unitTag/startTime/endTime can not be null");
        }
        String unitTagName = null;
        if (traceConfigQuery.getUnitTag() != null) {
            UnitTagDO unitTag = unitTagHandler.getById(traceConfigQuery.getUnitTag());
            if (unitTag != null) {
                unitTagName = unitTag.getName();
            }
        }
        List<TraceConfigDO> traceConfigDOList = traceConfigDAO.queryByParam(traceConfigQuery.getServiceId(),
                traceConfigQuery.getTopic(),
                null,
                unitTagName,
                null,
                null,
                traceConfigQuery.getStatus(),
                (pageIndex - 1) * pageSize,
                pageSize
        );
        if (traceConfigDOList == null) {
            return new ArrayList<>();
        }
        return traceConfigDOList;
    }

    public int countByConditions(TraceConfigQuery traceConfigQuery) {
        return traceConfigDAO.getCountByParam(
                traceConfigQuery.getServiceId(),
                traceConfigQuery.getTopic(),
                null,
                traceConfigQuery.getUnitTag(),
                null,
                null,
                traceConfigQuery.getStatus()
        );
    }

    public void modifyTraceConfigStatus(String id, String status) {
        if (StringUtils.isBlank(id) || StringUtils.isBlank(status)) {
            throw new IllegalArgumentException("trace config id or status can not be empty");
        }
        TraceConfigDO traceConfigDO = new TraceConfigDO();
        traceConfigDO.setId(id);
        traceConfigDO.setStatus(status);
        traceConfigDO.setLastModifiedUser(AuthInterceptor.getUserName());
        traceConfigDAO.update(traceConfigDO);
    }

    private TraceConfigDO toDO(TraceConfigInput traceConfigInput) {
        UnitTagDO unitTag = unitTagHandler.getById(traceConfigInput.getUnitTag());
        if (unitTag != null) {
            traceConfigInput.setUnitTag(unitTag.getName());
        }
        TraceConfigDO traceConfigDO = new TraceConfigDO();
        traceConfigDO.setId(traceConfigInput.getId());
        traceConfigDO.setServiceId(traceConfigInput.getServiceId());
        traceConfigDO.setTopic(traceConfigInput.getTopic());
        traceConfigDO.setGroupId(traceConfigInput.getGroupId());
        traceConfigDO.setUnitTag(traceConfigInput.getUnitTag());
        traceConfigDO.setThreadCount(traceConfigInput.getThreadCount());
        traceConfigDO.setProtocol(traceConfigInput.getProtocol());
        traceConfigDO.setSamplingRatio(traceConfigInput.getSamplingRatio());
        traceConfigDO.setStatus(traceConfigInput.getStatus());
        traceConfigDO.setLastModifiedUser(AuthInterceptor.getUserName());
        traceConfigDO.setIsDeleted(0);
        return traceConfigDO;
    }

    public List<TraceConfigDO> listAll() {
        return traceConfigDAO.listAll();
    }

    @Override
    public void load() {
        traceConfigCacheMap =  listAll().stream().collect(Collectors.groupingBy(TraceConfigDO::getServiceId));
    }

    public Map<String, List<TraceConfigDO>> getTraceConfigCacheMap() {
        return traceConfigCacheMap;
    }
}
