package us.zoom.cube.site.core;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.site.infra.utils.IdUtils;
import us.zoom.infra.dao.model.TemplateItemRelationDO;
import us.zoom.infra.dao.service.TemplateItemRelationDAO;

import java.util.Collections;
import java.util.List;

/**
 * @Author: <PERSON>
 * @Date:12/14/2022 17:00
 * @Description:
 */
@Service
@RequiredArgsConstructor
public class TemplateItemRelationHandler {

    private final TemplateItemRelationDAO templateItemRelationDAO;

    public int batchDeleteByTemplateId(String templateId) {
        return templateItemRelationDAO.batchDeleteByTemplateId(templateId);
    }

    public List<TemplateItemRelationDO> listByTemplateId(String templateId) {
        return templateItemRelationDAO.batchFindByTemplateId(templateId);
    }

    public int batchAdd(List<TemplateItemRelationDO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return 0;
        }
        for (TemplateItemRelationDO relation : list) {
            relation.setId(IdUtils.generateId());
        }
        return templateItemRelationDAO.batchAddTemplateItemRelation(list);
    }

    public List<TemplateItemRelationDO> listBy(List<String> templateIds) {
        if (CollectionUtils.isEmpty(templateIds)) {
            return Collections.emptyList();
        }
        return templateItemRelationDAO.listBy(templateIds);
    }

}
