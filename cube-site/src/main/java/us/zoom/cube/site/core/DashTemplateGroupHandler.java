package us.zoom.cube.site.core;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.site.infra.utils.IdUtils;
import us.zoom.cube.site.lib.IdPara;
import us.zoom.cube.site.lib.input.DashInput;
import us.zoom.cube.site.lib.input.DashTemplateGroupInput;
import us.zoom.cube.site.lib.input.DashTemplateInput;
import us.zoom.cube.site.lib.query.NameQuery;
import us.zoom.cube.site.lib.query.PageQuery;
import us.zoom.infra.dao.model.*;
import us.zoom.infra.dao.service.DashTemplateGroupDAO;
import us.zoom.infra.dao.service.DashTemplateGroupRelaDAO;
import us.zoom.infra.dao.service.DashTemplateGroupUserRelaDAO;
import us.zoom.infra.dao.service.DashTemplateUserRelaDAO;
import us.zoom.infra.enums.DashUserRelaTypeEnum;
import us.zoom.infra.enums.DashVisibilityEnum;
import us.zoom.infra.utils.CommonSplitConstants;
import us.zoom.infra.utils.Instance;

import jakarta.validation.Valid;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2022-07-04 10:47
 */
@Component
public class DashTemplateGroupHandler {

    @Autowired
    DashTemplateGroupDAO dashTemplateGroupDAO;

    @Autowired
    DashTemplateGroupUserRelaDAO dashTemplateGroupUserRelaDAO;

    @Autowired
    DashTemplateGroupRelaDAO dashTemplateGroupRelaDAO;




    @Transactional(rollbackFor = Exception.class)
    public String add(@Valid DashTemplateGroupInput dashTemplateGroupInput){
        DashTemplateGroupDO dashTemplateGroupDO = new DashTemplateGroupDO();
        dashTemplateGroupDO.setId(IdUtils.generateId());
        dashTemplateGroupDO.setTemplateGroupName(dashTemplateGroupInput.getTemplateGroupName());
        dashTemplateGroupDO.setTemplateGroupSharedOwners(dashTemplateGroupInput.getTemplateGroupSharedOwners());
        dashTemplateGroupDAO.add(dashTemplateGroupDO);
        setOwner(dashTemplateGroupInput,dashTemplateGroupDO.getId());
        return dashTemplateGroupDO.getId();

    }

    @Transactional(rollbackFor = Exception.class)
    public String edit(@Valid DashTemplateGroupInput dashTemplateGroupInput) {
        Assert.notNull(dashTemplateGroupInput.getId(),"id is null");

        DashTemplateGroupDO dashTemplateGroupDO = new DashTemplateGroupDO();
        dashTemplateGroupDO.setId(dashTemplateGroupInput.getId());
        dashTemplateGroupDO.setTemplateGroupName(dashTemplateGroupInput.getTemplateGroupName());
        dashTemplateGroupDO.setTemplateGroupSharedOwners(dashTemplateGroupInput.getTemplateGroupSharedOwners());
        dashTemplateGroupDAO.edit(dashTemplateGroupDO);
        dashTemplateGroupUserRelaDAO.delBytemplateGroupId(dashTemplateGroupInput.getId());
        setOwner(dashTemplateGroupInput,dashTemplateGroupDO.getId());
        return dashTemplateGroupDO.getId();
    }

    @Transactional(rollbackFor = Exception.class)
    public void delete(IdPara idPara) {

        dashTemplateGroupDAO.del(idPara.getId());
        dashTemplateGroupRelaDAO.delRelaByGroup(idPara.getId());
        dashTemplateGroupUserRelaDAO.delBytemplateGroupId(idPara.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    public String addGroupRela(@Valid DashTemplateGroupInput dashTemplateGroupInput){
        DashTemplateGroupRelaDO dashTemplateGroupRelaDO = new DashTemplateGroupRelaDO();
        dashTemplateGroupRelaDO.setId(IdUtils.generateId());
        dashTemplateGroupRelaDO.setTemplateGroupId(dashTemplateGroupInput.getId());
        dashTemplateGroupRelaDO.setTemplateId(dashTemplateGroupInput.getTemplateId());
        List<DashTemplateGroupRelaDO> list = dashTemplateGroupRelaDAO.getRelaByTemplateIdAndGroupId(dashTemplateGroupRelaDO.getTemplateId(),dashTemplateGroupRelaDO.getTemplateGroupId());
        if(CollectionUtils.isEmpty(list)){
            dashTemplateGroupRelaDAO.add(dashTemplateGroupRelaDO);
        }
        return dashTemplateGroupRelaDO.getId();

    }

    @Transactional(rollbackFor = Exception.class)
    public String removeGroupRela(@Valid DashTemplateGroupInput dashTemplateGroupInput){
        DashTemplateGroupRelaDO dashTemplateGroupRelaDO = new DashTemplateGroupRelaDO();
        dashTemplateGroupRelaDO.setTemplateGroupId(dashTemplateGroupInput.getId());
        dashTemplateGroupRelaDO.setTemplateId(dashTemplateGroupInput.getTemplateId());
        dashTemplateGroupRelaDAO.del(dashTemplateGroupRelaDO);
        return dashTemplateGroupRelaDO.getId();

    }

    public List<DashTemplateGroupUserRelaDO> searchHaveByNameLike(PageQuery<NameQuery> pageQuery) {
        return dashTemplateGroupDAO.searchHaveByNameLike(getName(pageQuery),pageQuery.getUserId(),pageQuery.getPageSize()*(pageQuery.getPageIndex()-1), pageQuery.getPageSize());

    }

    public Integer searchHaveByNameLikeCount(PageQuery<NameQuery> pageQuery) {
        return dashTemplateGroupDAO.getSearchHaveCount(getName(pageQuery),pageQuery.getUserId(),pageQuery.getPageSize()*(pageQuery.getPageIndex()-1), pageQuery.getPageSize());

    }


    private void setOwner(@Valid DashTemplateGroupInput dashTemplateGroupInput, String id ) {
        Set<String> ownerIds=getOwners(dashTemplateGroupInput);
        Instance.ofNullable(ownerIds).forEach(owner->{
            dashTemplateGroupUserRelaDAO.add(id,owner, IdUtils.generateId(), DashUserRelaTypeEnum.OWNER.getCode());
        });
    }

    private Set<String> getOwners(DashTemplateGroupInput dashTemplateGroupInput) {
        Set<String> ownerIds=new HashSet<>();
        ownerIds.add(dashTemplateGroupInput.getUserId());
        String[] owners=StringUtils.split(dashTemplateGroupInput.getTemplateGroupSharedOwners(), CommonSplitConstants.COMMA_SPLIT);
        if(null!=owners){
            for(int i=0;i<owners.length;i++){
                if(StringUtils.isNotBlank(owners[i])){
                    ownerIds.add(owners[i]);
                }
            }
        }
        return ownerIds;
    }
    private String getName(PageQuery<NameQuery> pageQuery) {
        return (pageQuery == null || null == pageQuery.getQueryPara()) ? "" : pageQuery.getQueryPara().getName();
    }

}
