package us.zoom.cube.site.core;

import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.site.infra.utils.IdUtils;
import us.zoom.infra.dao.model.TemplateItemDO;
import us.zoom.infra.dao.service.TemplateItemDAO;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: <PERSON>
 * @Date:12/14/2022 17:00
 * @Description:
 */
@Service
@RequiredArgsConstructor
public class TemplateItemHandler {

    private final TemplateItemDAO templateItemDAO;

    public List<String> batchAdd(List<TemplateItemDO> items) {
        if (CollectionUtils.isEmpty(items)) {
            return Collections.emptyList();
        }
        for (TemplateItemDO item : items) {
            item.setId(IdUtils.generateId());
        }
        templateItemDAO.batchAddTemplate(items);
        return items.stream().map(TemplateItemDO::getId).collect(Collectors.toList());
    }

    public int batchDelete(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return 0;
        }
        return templateItemDAO.batchDelete(ids);
    }

    public List<TemplateItemDO> listBy(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return templateItemDAO.listBy(ids);
    }

    public List<TemplateItemDO> listByTemplateId(String templateId) {
        return templateItemDAO.listByTemplateId(templateId);
    }

    public List<TemplateItemDO> listByNameAndType(List<String> cubeTemplate) {
        return templateItemDAO.listByNameAndType(cubeTemplate);
    }

    public TemplateItemDO listByServiceIdAndItemId(String serviceId, String itemId) {
        if (StringUtils.isEmpty(serviceId) || StringUtils.isEmpty(itemId)) {
            return null;
        }
        return templateItemDAO.listByServiceIdAndItemId(serviceId, itemId);
    }

    public List<TemplateItemDO> listByTemplateIds(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }
        return templateItemDAO.listByTemplateIds(ids);

    }

}
