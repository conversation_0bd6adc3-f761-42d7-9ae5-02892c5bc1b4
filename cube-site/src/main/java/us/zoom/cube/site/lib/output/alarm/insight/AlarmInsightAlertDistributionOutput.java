package us.zoom.cube.site.lib.output.alarm.insight;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import us.zoom.cube.site.lib.output.config.metrics.MetricsTags;

import java.util.LinkedList;
import java.util.List;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AlarmInsightAlertDistributionOutput {
    long totalCount;

    List<Series> series;

    @Data
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class Series {
        List<MetricsTags> tags = new LinkedList<>();
        Object values;
    }
}
