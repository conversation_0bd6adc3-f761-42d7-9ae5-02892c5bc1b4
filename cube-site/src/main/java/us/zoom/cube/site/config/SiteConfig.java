package us.zoom.cube.site.config;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import us.zoom.cube.site.api.config.KMSEncryptedEnvironment;
import us.zoom.cube.site.api.filter.AuthFilter;
import us.zoom.cube.site.api.filter.NewAuthFilter;
import us.zoom.cube.site.api.filter.TrackingIdFilter;
import us.zoom.cube.site.api.intercept.AuthInterceptor;
import us.zoom.cube.site.api.intercept.LimiterInterceptor;
import us.zoom.cube.site.config.properties.JwtAuthProperties;
import us.zoom.cube.site.core.SysParaHandler;
import us.zoom.infra.dao.model.SysParaDO;
import us.zoom.infra.thread.NamedThreadFactory;
import us.zoom.cube.lib.utils.JsonUtils;

import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

@ConditionalOnMissingBean(JwtAuthProperties.class)
@Configuration
@Slf4j
public class SiteConfig implements WebMvcConfigurer, InitializingBean {

    @Value("${cors.allowed.url.okta}")
    private String allowedUrlOkta;

    @Autowired
    AuthInterceptor authInterceptor;

    @Autowired
    LimiterInterceptor limiterInterceptor;

    /*@Resource(name = "kmsEnvironment")
    private Environment environment;*/
    @Autowired
    private KMSEncryptedEnvironment environment;

    @Autowired
    private SysParaHandler sysParaHandler;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(authInterceptor).addPathPatterns("/**").
                excludePathPatterns("/user/login", "/user/logout", "/user/add", "/metric/queryRawData",
                        "/static/**", "/cfg/**", "/selfmonitor/**", "/authorization-code/callback",
                        "/out/alarm/addRule", "/out/alarm/editRule", "/out/alarm/deleteRule", "/out/alarm/list", "/out/ta/**");
        registry.addInterceptor(limiterInterceptor).addPathPatterns("/**").excludePathPatterns("/static/**", "/selfmonitor/**", "/index.html");
    }

    @Bean
    public CorsFilter corsFilter() {
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        CorsConfiguration corsConfiguration = new CorsConfiguration();
        corsConfiguration.addAllowedOrigin(allowedUrlOkta);
        corsConfiguration.addAllowedHeader("*");
        corsConfiguration.addAllowedMethod("*");
        corsConfiguration.setAllowCredentials(true);
        source.registerCorsConfiguration("/authorization-code/callback", corsConfiguration);
        return new CorsFilter(source);
    }

    @Bean
    public WebMvcConfigurer corsConfigurer() {
        return new WebMvcConfigurer() {
            @Override
            public void addCorsMappings(CorsRegistry registry) {
                registry.addMapping("/**").allowedOrigins(allowedUrlOkta);
                registry.addMapping("/**").allowedMethods("*");
            }
        };
    }


    @Bean
    @DependsOn("cubeJWTSDKConfigMediator")
    public FilterRegistrationBean<AuthFilter> authFilter() {
        FilterRegistrationBean<AuthFilter> authFilterFilterRegistrationBean = new FilterRegistrationBean<>();
        authFilterFilterRegistrationBean.setFilter(new AuthFilter());
        authFilterFilterRegistrationBean.addUrlPatterns("/cfg/hub/getHubCfg", "/out/alarm/*", "/out/metric/*", "/out/tenant/*",
                "/out/tpl/*", "/out/v2/tenant/*", "/out/v2/tpl/*", "/out/dataParserSource/*", "/out/convenient/*", "/out/asyncmq/*"
                , "/out/dataParser/forward/*", "/out/server/*", "/out/cmdb/*", "/out/zcp/*", "/out/eventcenter/*", "/out/ta/*");

        Map<String, String> initMap = Maps.newHashMap();
        // only support string type param
        initMap.put(AuthFilter.CSMS_JWT_PUBLIC_KEY_PATH_LIST, JsonUtils.toJsonString(getCsmsPathList()));

        authFilterFilterRegistrationBean.setInitParameters(initMap);

        authFilterFilterRegistrationBean.setName("authFilter");
        authFilterFilterRegistrationBean.setOrder(1);
        return authFilterFilterRegistrationBean;
    }

    @Bean
    @DependsOn("cubeJWTSDKConfigMediator")
    public FilterRegistrationBean<NewAuthFilter> newHubCfgAuthFilter() {
        FilterRegistrationBean<NewAuthFilter> authFilterFilterRegistrationBean = new FilterRegistrationBean<>();
        authFilterFilterRegistrationBean.setFilter(new NewAuthFilter());
        authFilterFilterRegistrationBean.addUrlPatterns("/cfg/hub/v2/getHubCfg",
                "/cfg/hub/v3/getHubCfg",
                "/cfg/hub/v4/getHubCfg",
                "/cfg/hub/v5/getHubCfg",
                "/cfg/hub/getAsyncmqAccount",
                "/cfg/probe/getProbeGroupCfg",
                "/cfg/probe/test/callback",
                "/cfg/network/device",
                "/cfg/probe/getProbeSystemParam",
                "/out/pipeline/discover");

        Map<String, String> initMap = Maps.newHashMap();
        initMap.put(NewAuthFilter.CSMS_JWT_PUBLIC_KEY_PATH_LIST, JsonUtils.toJsonString(getCsmsPathList()));
        authFilterFilterRegistrationBean.setInitParameters(initMap);

        authFilterFilterRegistrationBean.setName("newHubCfgAuthFilter");
        authFilterFilterRegistrationBean.setOrder(1);
        return authFilterFilterRegistrationBean;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        Executors.newScheduledThreadPool(1, new NamedThreadFactory("load-jwt-key"))
                .scheduleAtFixedRate(() -> {
                    loadAsymmetricJwtCsmsPathList();
                }, 10, 300, TimeUnit.SECONDS);
    }

    private void loadAsymmetricJwtCsmsPathList() {
        try {
            log.info("begin load asymmetric jwt csms path list");
            this.authFilter().getFilter().registerAsymmetricJwtCsmsPathList(getCsmsPathList());
            log.info("end load asymmetric jwt csms path list");
        } catch (Exception e) {
            log.error("load asymmetric jwt csms path list error", e);
        }
    }

    private List<String> getCsmsPathList() {
        List<SysParaDO> jwtCsmsPathList = sysParaHandler.listByTypeAndParaKey("jwtCsmsPathList", "cube-site");
        if (CollectionUtils.isEmpty(jwtCsmsPathList)) {
            return new LinkedList<>();
        }
        return JsonUtils.toObjectByTypeRef(jwtCsmsPathList.get(0).getValue(), new TypeReference<List<String>>() {
        });
    }
}
