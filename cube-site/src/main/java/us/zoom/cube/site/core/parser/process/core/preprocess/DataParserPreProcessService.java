package us.zoom.cube.site.core.parser.process.core.preprocess;

import com.fasterxml.jackson.core.type.TypeReference;
import us.zoom.cube.site.core.parser.process.core.response.PreResp;

import java.util.Map;

public interface DataParserPreProcessService {
    TypeReference<Map<String, Object>> mapTypeReference = new TypeReference<Map<String, Object>>() {
    };

    PreResp handle(String message);
}
