package us.zoom.cube.site.core;

import com.zoom.op.monitor.domain.alarm.AlarmDefinition;
import com.zoom.op.monitor.domain.alarm.Channel;
import com.zoom.op.monitor.domain.alarm.ChannelParameter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.stubbing.OngoingStubbing;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import us.zoom.cube.site.CubeSiteApplicationTests;
import us.zoom.cube.site.biz.BiAlarmsImportService;
import us.zoom.cube.site.biz.DataParserExportService;
import us.zoom.cube.site.core.maintaince.MetricsMigrateToServiceMonitorHandler;
import us.zoom.cube.site.infra.utils.IdUtils;
import us.zoom.cube.site.lib.biport.BiImportRelationInfo;
import us.zoom.cube.site.lib.input.MetricsMigrationInput;
import us.zoom.infra.dao.model.*;

import java.util.*;

/**
 * <AUTHOR> Junjian
 * @create 2020/7/30 3:44 PM
 */
@ExtendWith(MockitoExtension.class)
public class MetricsMigrateToServiceMonitorHandlerMockTest {


    @InjectMocks
    private MetricsMigrateToServiceMonitorHandler metricsMigrateToServiceMonitorHandler = new MetricsMigrateToServiceMonitorHandler();


    @Mock
    private TenantHandler tenantHandler;

    @Mock
    private MetricsHandler metricsHandler;

    @Mock
    private AlarmHandler alarmHandler;

    @Mock
    private DataParserExportService dataParserExportService;

    @Mock
    private BiAlarmsImportService biAlarmsImportService;

    @Mock
    private ChannelHandler channelHandler;

    @Mock
    private CardHandler cardHandler;

    @Mock
    private DashHandler dashHandler;

    private static final String serviceMonitor = "service_monitor";
    private static final String serviceMonitorId = "123456";

    private static final String metricsNamePattern ="\"metricsName\"";

    private static final String migrationSuffix = "_Migration";

    private static final String destServiceName = "xmpp_xms";
    private static final String destServiceId = "789";

    private static final String srcMetrics1Name ="XMPP.BroadcastPendingNum.Size";
    private static final String srcMetrics2Name ="XPP.ConnectedNodes.Size";
    private static final String srcMetrics1Id ="srcId1";
    private static final String srcMetrics2Id ="srcId2";

    private static final String destMetrics1Name ="XMPP_BroadcastPendingNum_Size";
    private static final String destMetrics2Name ="XPP_ConnectedNodes_Size";
    private static final String destMetrics1Id ="destId1";
    private static final String destMetrics2Id ="destId2";

    private static final String channelId1 = "channelId1";
    private static final String channelId2 = "channelId2";
    private static final String channelName1 = "channelName1";
    private static final String channelName2 = "channelName2";
    private static final String alarmId1 = "alarm1";
    private static final String alarmId2 = "alarm2";
    private static final String alarmName1 = "alarmName1";
    private static final String alarmName2 = "alarmName2";

    private static final String cardId1 ="cardId1";
    private static final String cardId2 ="cardId2";
    private static final String cardName1 ="cardName1";
    private static final String cardName2 ="cardName2";

    private static final String dashId1 = "dashId1";
    private static final String dashId2 = "dashId2";
    private static final String dashName1 = "dashName1";
    private static final String dashName2 = "dashName2";



    @Test
    public void test() throws Exception {
        Mockito.when(tenantHandler.getTenantByNameInLowerCaseFromCache(serviceMonitor)).thenReturn(mockServiceMonitor());
        Mockito.when(tenantHandler.getTenantByNameInLowerCaseFromCache(destServiceName)).thenReturn(mockDestService());
        MetricsMigrationInput metricsMigrationInput = new MetricsMigrationInput();
        metricsMigrationInput.setDestServiceName(destServiceName);


        // case 1: no metrics for service_monitor
        Mockito.when(metricsHandler.getMetricsByTenant(serviceMonitorId)).thenReturn(mockMetricsForServiceMonitor(true));
        List<String> result=  metricsMigrateToServiceMonitorHandler.migrateFromServiceMonitor(metricsMigrationInput);
        Assert.isTrue(resultContailsKeyWord(result,"there is no metrics need to migrate"),"there is no metrics need to migrate");


        // case 1: no full match metrics and no startWith Metrics
        Mockito.when(metricsHandler.getMetricsByTenant(serviceMonitorId)).thenReturn(mockMetricsForServiceMonitor(false));
        metricsMigrationInput.setSrcMetricsNameFullMatch(Collections.emptyList());
        metricsMigrationInput.setSrcMetricsNameStartWith(Collections.emptyList());
        result=  metricsMigrateToServiceMonitorHandler.migrateFromServiceMonitor(metricsMigrationInput);
        Assert.isTrue(resultContailsKeyWord(result,"there is no metrics need to migrate"),"there is no metrics need to migrate");


        //no alarm no dash need to migrate
        metricsMigrationInput.setSrcMetricsNameFullMatch(Arrays.asList("XPP.ConnectedNodes.Size"));
        metricsMigrationInput.setSrcMetricsNameStartWith(Arrays.asList("XMPP"));
        Mockito.when(alarmHandler.listAlarmsByMetricsIdList((Arrays.asList(srcMetrics1Id,srcMetrics2Id)))).thenReturn(mockAlarmForServiceMonitor(true));
//        Mockito.when(cardHandler.listAllCard()).thenReturn(mockCard(true));
        Mockito.when(metricsHandler.getMetricsByTenant(destServiceId)).thenReturn(mockMetricsForDestService(false));
        result=  metricsMigrateToServiceMonitorHandler.migrateFromServiceMonitor(metricsMigrationInput);
        Assert.isTrue(resultContailsKeyWord(result,"there is no alarm to migrate"),"there is no alarm to migrate");

        Mockito.when(alarmHandler.listAlarmsByMetricsIdList((Arrays.asList(srcMetrics1Id,srcMetrics2Id)))).thenReturn(mockAlarmForServiceMonitor(false));
        Mockito.when(alarmHandler.listChannelIdsByAlarmIds(Mockito.anyList())).thenReturn(mockChannelIds());
        Mockito.when(channelHandler.listChannelNotExistInTenantMatchByName(Arrays.asList(channelId1,channelId2),destServiceId)).thenReturn(mockChannel(true));
        result=  metricsMigrateToServiceMonitorHandler.migrateFromServiceMonitor(metricsMigrationInput);
        Assert.isTrue(resultContailsKeyWord(result,"all channel have created"),"all channel have created");


        Mockito.when(channelHandler.listChannelNotExistInTenantMatchByName(Arrays.asList(channelId1,channelId2),destServiceId)).thenReturn(mockChannel(false));
        Mockito.when(channelHandler.batchSave(Mockito.anyList())).thenReturn(Mockito.anyList());
        result=  metricsMigrateToServiceMonitorHandler.migrateFromServiceMonitor(metricsMigrationInput);
        Assert.isTrue(resultContailsKeyWord(result,"add im channel success"),"add im channel success");

        Mockito.when(cardHandler.listAllCard()).thenReturn(mockCard(true));
        Mockito.when(dataParserExportService.exportAlarmWithAll(serviceMonitorId,alarmId1)).thenReturn(mockExportAlarm(alarmId1));
        Mockito.when(dataParserExportService.exportAlarmWithAll(serviceMonitorId,alarmId2)).thenReturn(mockExportAlarm(alarmId2));
        Mockito.when(biAlarmsImportService.importJson(Mockito.any())).thenReturn(mockBiImportRelationInfo());
        result=  metricsMigrateToServiceMonitorHandler.migrateFromServiceMonitor(metricsMigrationInput);
        Assert.isTrue(resultContailsKeyWord(result,"There is no cards to migrate"),"There is no cards to migrate");
        Assert.isTrue(resultContailsKeyWord(result,"import alarm success for "+alarmName1),"import alarm success for "+alarmName1);
        Assert.isTrue(resultContailsKeyWord(result,"import alarm success for "+alarmName2),"import alarm success for "+alarmName2);


        Mockito.when(cardHandler.listAllCard()).thenReturn(mockCard(false));
        Mockito.when(dashHandler.listDashCardRelaByCardIds(Mockito.anyList())).thenReturn(mockDashCardRela(false));
        Mockito.when(dashHandler.listBy(Mockito.anyList())).thenReturn(mockDash(false));
        Mockito.when(dashHandler.listIdConfigByName(Mockito.any())).thenReturn(Collections.emptyList());
//        OngoingStubbing<DashDO> when = Mockito.when(dashHandler.migrateDash(Mockito.anyString(), Mockito.any(), Mockito.anyList()));
        result=  metricsMigrateToServiceMonitorHandler.migrateFromServiceMonitor(metricsMigrationInput);
        Assert.isTrue(resultContailsKeyWord(result,"migrate dash success "+dashName1),"migrate dash success "+dashName1);
        Assert.isTrue(resultContailsKeyWord(result,"migrate dash success "+dashName2),"migrate dash success "+dashName2);


    }

    private BiImportRelationInfo mockBiImportRelationInfo() {
        return new BiImportRelationInfo();
    }

    private String mockExportAlarm(String alarmId) {

        String alarmName = alarmId1.equals(alarmId) ? alarmName1 : alarmName2;
        String channelName = alarmId1.equals(alarmId) ? channelName1 : channelName2;
        String metricsName = alarmId1.equals(alarmId) ? srcMetrics1Name : srcMetrics2Name;
        return "{\"alarms\": [{\"name\": \""+alarmName+"\", \"rules\": [{\"level\": \"ERROR\", \"needHits\": 3, \"hitCount\": 3, \"conditions\": [{\"conditionType\": \"FIELD\", \"name\": \"us\", \"operator\": \">=\", \"threshold\": \"40\"} ] } ], " +
                "\"timesInPeriod\": 2, \"periodInMinutes\": 120, \"enabled\": true, " +
                "\"notifications\": [{\"channel\": {\"name\": \""+channelName+"\", \"engineName\": \"Email\", \"isDefault\": 0, \"creator\": \"\", \"editor\": \"\"}, \"title\": \"cpu is too high\", \"content\": \"${clusterId}\\n${regionId}\\n${instanceId}\\n${us}%\", \"whichLevels\": \"WARN,ERROR,FATAL\", \"repeatedSendAsyncmq\": false } ], \"alarmExtensionRelations\": [], \"creator\": \"<EMAIL>\", \"editor\": \"<EMAIL>\", \"alarmMatchMode\": 0, " +
                "\"metricsName\": \""+metricsName+"\"} ] }";

    }

    private List<Channel> mockChannel(boolean blank) {
        if(blank){
            return Collections.emptyList();
        }
        List<Channel> result = new ArrayList<>();
        Channel channel = new Channel();
        channel.setId(channelId1);
        channel.setName(channelName1);
        channel.setTenantId(serviceMonitorId);
        List<ChannelParameter> parameters = new ArrayList<>();
        ChannelParameter channelParameter = new ChannelParameter();
        channelParameter.setId("1");
        parameters.add(channelParameter);
        channel.setParameters(parameters);
        result.add(channel);

        channel = new Channel();
        channel.setId(channelId2);
        channel.setName(channelName2);
        channel.setTenantId(serviceMonitorId);
        parameters = new ArrayList<>();
        channelParameter = new ChannelParameter();
        channelParameter.setId("2");
        parameters.add(channelParameter);
        channel.setParameters(parameters);
        result.add(channel);
        return result;
    }

    private List<String> mockChannelIds( ) {
        return Arrays.asList(channelId1,channelId2,channelId2);
    }

    private List<MetricsDO> mockMetricsForDestService(boolean blank) {
        if(blank){
            return Collections.emptyList();
        }
        List<MetricsDO> metricsDOS = new ArrayList<>();
        MetricsDO metricsDO = new MetricsDO();
        metricsDO.setId(destMetrics1Id);
        metricsDO.setMetricsName(destMetrics1Name);
        metricsDO.setTenantId(destServiceId);
        metricsDOS.add(metricsDO);
        metricsDO = new MetricsDO();
        metricsDO.setId(destMetrics2Id);
        metricsDO.setMetricsName(destMetrics2Name);
        metricsDO.setTenantId(destServiceId);
        metricsDOS.add(metricsDO);
        return metricsDOS;
    }

    private List<CardDO> mockCard(boolean blank) {
        if(blank){
            return Collections.emptyList();
        }

        List<CardDO> cardDOS = new ArrayList<>();
        List<String> metricsIds = Arrays.asList(srcMetrics1Id,srcMetrics2Id);
        for(String metricsId:metricsIds){
            String cardId = srcMetrics1Id.equals(metricsId) ? cardId1 : cardId2;
            String cardName= srcMetrics1Id.equals(metricsId) ? cardName1 : cardName2;
            String metricsName = srcMetrics1Id.equals(metricsId) ? srcMetrics1Name : srcMetrics2Name;
            CardDO cardDO = new CardDO();
            cardDO.setId(cardId);
            cardDO.setName(cardName);
            cardDO.setTenantId(serviceMonitorId);
            cardDO.setConfigs("{\"visualConfigs\":{\"timeAxis\":\"vertical\",\"timeFormat\":\"YYYY-MM-DD HH:mm:ss\",\"customizeFields\":[]}," +
                    "\"queryConfig\":{\"rawText\":\"SELECT DISTINCT metricsType, enabled FROM \\\"service_monitor\\\".\\\""+metricsName
                    +"\\\" WHERE time > subtractMinutes(now(), 2880) AND time < now() AND ( \\\"clusterId\\\"= :clusterId: ) " +
                    "AND ( \\\"enabled\\\"='true' ) GROUP BY metricsType, enabled\",\"dbType\":\"clickhouse\",\"compareTime\":" +
                    "\"none\",\"fill\":\"none\",\"db\":\"service_monitor\",\"rp\":\"autogen\"},\"type\":\"table\",\"gridPos\":{\"x\":0,\"y\":82,\"h\":8,\"w\":6}}");
            cardDOS.add(cardDO);
            cardDO = new CardDO();
            cardDO.setId(cardId);
            cardDO.setName(cardName);
            cardDO.setTenantId(serviceMonitorId);
            cardDO.setConfigs("just test");
            cardDOS.add(cardDO);
        }
        return cardDOS;
    }


    List<DashCardRelaDO> mockDashCardRela(boolean blank){
        if(blank){
            return Collections.emptyList();
        }
        List<DashCardRelaDO> result = new ArrayList<>();
        DashCardRelaDO relaDO = new DashCardRelaDO();
        relaDO.setId(IdUtils.generateId());
        relaDO.setCardId(cardId1);
        relaDO.setDashId(dashId1 );
        result.add(relaDO);
        relaDO = new DashCardRelaDO();
        relaDO.setId(IdUtils.generateId());
        relaDO.setCardId(cardId2);
        relaDO.setDashId(dashId2);
        result.add(relaDO);
        return result;
    }

    List<DashDO> mockDash(boolean blank){
        if(blank){
            return Collections.emptyList();
        }
        List<DashDO>  dashDOS = new ArrayList<>();
        DashDO dashDO = new DashDO();
        dashDO.setService(serviceMonitor);
        dashDO.setName(dashName1);
        dashDO.setId(dashId1);
        dashDO.setSharedOwners("a,b,c");
        dashDO.setTenantId(serviceMonitorId);
        String metricsName = srcMetrics1Name;
        dashDO.setConfigs("{\"layout\":[{\"i\":\"91c48061-e3b2-4cc8-9299-71918ac6706e\",\"x\":0,\"y\":0,\"h\":1,\"w\":12},{\"i\":\"d3a7a75b-3338-46bf-989d-1c3424d97398\",\"x\":0,\"y\":1,\"h\":8,\"w\":6},{\"i\":\"dcace874-5a83-42bb-b573-ee932297ce90\",\"x\":6,\"y\":1,\"h\":8,\"w\":6},{\"i\":\"d547a2ee-ca35-43f2-b41c-f3eacd12f4a5\",\"x\":0,\"y\":9,\"h\":8,\"w\":6},{\"i\":\"3dc9726b-c5d4-4303-9750-1dff805461cf\",\"x\":0,\"y\":17,\"h\":6,\"w\":12},{\"i\":\"4e4bb438-225c-4756-bc3c-4920ddd26e09\",\"x\":0,\"y\":23,\"h\":1,\"w\":12},{\"i\":\"f7650a56-2714-4bdc-a1fd-7b3351b27294\",\"x\":0,\"y\":24,\"h\":8,\"w\":6},{\"i\":\"e8da9e10-ad8c-4ff6-8948-510124356370\",\"x\":6,\"y\":24,\"h\":8,\"w\":6},{\"i\":\"3952a505-1fed-48cc-9c56-e7a83f96b985\",\"x\":0,\"y\":32,\"h\":8,\"w\":6},{\"i\":\"ccd0285b-0425-4037-a283-a371b80484c7\",\"x\":0,\"y\":40,\"h\":8,\"w\":12},{\"i\":\"d79abb68-731d-4fea-8b38-fec37f5c7dcc\",\"x\":0,\"y\":48,\"h\":1,\"w\":12},{\"i\":\"841561d0-1d94-40fc-8939-efd6c0c10acc\",\"x\":0,\"y\":49,\"h\":8,\"w\":6},{\"i\":\"2a3919eb-f533-483d-801d-f5eda1150853\",\"x\":6,\"y\":49,\"h\":8,\"w\":6},{\"i\":\"a758bb38-3aec-4c47-8b96-47d751c03ac7\",\"x\":0,\"y\":57,\"h\":8,\"w\":6},{\"i\":\"ebb6326e-4f40-46bf-8809-d1403de5c145\",\"x\":0,\"y\":65,\"h\":6,\"w\":12},{\"i\":\"ab9b3bfe-a655-4efe-97c8-843288874d05\",\"x\":0,\"y\":71,\"h\":1,\"w\":12},{\"i\":\"4e40808b-1340-4d57-b651-720a285817e2\",\"x\":0,\"y\":72,\"h\":8,\"w\":6},{\"i\":\"eda6e9f8-783e-4145-b7d2-855395ba18a5\",\"x\":6,\"y\":72,\"h\":8,\"w\":6},{\"i\":\"1e934bdd-e6e1-4aab-af5b-c9df6cf61813\",\"x\":0,\"y\":80,\"h\":8,\"w\":6},{\"i\":\"7d9952a7-8dde-45a7-b512-14d9f252e3a7\",\"x\":0,\"y\":88,\"h\":7,\"w\":12}],\"defaultTimeRange\":\"30m\",\"autoRefreshOptions\":[\"5s\",\"10s\",\"15s\",\"30s\",\"60s\"]," +
                "\"variables\":[{\"type\":\"values\",\"name\":\":clusterId:\",\"order\":0," +
                "\"tenant\":\""+serviceMonitor+"\",\"measurement\":\""+metricsName+"\",\"keys\":\"clusterId\",\"defaultValue\":\"\"}," +
                "{\"type\":\"values\",\"name\":\":regionId:\",\"order\":1,\"tenant\":\""+serviceMonitor+"\",\"measurement\":\""+metricsName+"\"," +
                "\"keys\":\"regionId\",\"defaultValue\":\"\",\"where\":[{\"arithmeticOperator\":\"=\",\"tagKey\":\"clusterId\",\"variable\":\":clusterId:\"}]}," +
                "{\"type\":\"values\",\"name\":\":zoneName:\",\"order\":2,\"tenant\":\""+serviceMonitor+"\",\"measurement\":\""+metricsName+"\"," +
                "\"keys\":\"zoneName\",\"defaultValue\":\"\",\"where\":[{\"logicalOperator\":\"AND\",\"arithmeticOperator\":\"=\"," +
                "\"tagKey\":\"clusterId\",\"variable\":\":clusterId:\"},{\"arithmeticOperator\":\"=\",\"tagKey\":\"regionId\",\"variable\":\":regionId:\"}]}]}");
        dashDOS.add(dashDO);
        dashDO = new DashDO();
        dashDO.setService(serviceMonitor);
        dashDO.setName(dashName2);
        dashDO.setId(dashId2);
        dashDO.setSharedOwners("e,f");
        dashDO.setTenantId(serviceMonitorId);
        metricsName = srcMetrics2Name;
        dashDO.setConfigs("{\"layout\":[{\"i\":\"91c48061-e3b2-4cc8-9299-71918ac6706e\",\"x\":0,\"y\":0,\"h\":1,\"w\":12},{\"i\":\"d3a7a75b-3338-46bf-989d-1c3424d97398\",\"x\":0,\"y\":1,\"h\":8,\"w\":6},{\"i\":\"dcace874-5a83-42bb-b573-ee932297ce90\",\"x\":6,\"y\":1,\"h\":8,\"w\":6},{\"i\":\"d547a2ee-ca35-43f2-b41c-f3eacd12f4a5\",\"x\":0,\"y\":9,\"h\":8,\"w\":6},{\"i\":\"3dc9726b-c5d4-4303-9750-1dff805461cf\",\"x\":0,\"y\":17,\"h\":6,\"w\":12},{\"i\":\"4e4bb438-225c-4756-bc3c-4920ddd26e09\",\"x\":0,\"y\":23,\"h\":1,\"w\":12},{\"i\":\"f7650a56-2714-4bdc-a1fd-7b3351b27294\",\"x\":0,\"y\":24,\"h\":8,\"w\":6},{\"i\":\"e8da9e10-ad8c-4ff6-8948-510124356370\",\"x\":6,\"y\":24,\"h\":8,\"w\":6},{\"i\":\"3952a505-1fed-48cc-9c56-e7a83f96b985\",\"x\":0,\"y\":32,\"h\":8,\"w\":6},{\"i\":\"ccd0285b-0425-4037-a283-a371b80484c7\",\"x\":0,\"y\":40,\"h\":8,\"w\":12},{\"i\":\"d79abb68-731d-4fea-8b38-fec37f5c7dcc\",\"x\":0,\"y\":48,\"h\":1,\"w\":12},{\"i\":\"841561d0-1d94-40fc-8939-efd6c0c10acc\",\"x\":0,\"y\":49,\"h\":8,\"w\":6},{\"i\":\"2a3919eb-f533-483d-801d-f5eda1150853\",\"x\":6,\"y\":49,\"h\":8,\"w\":6},{\"i\":\"a758bb38-3aec-4c47-8b96-47d751c03ac7\",\"x\":0,\"y\":57,\"h\":8,\"w\":6},{\"i\":\"ebb6326e-4f40-46bf-8809-d1403de5c145\",\"x\":0,\"y\":65,\"h\":6,\"w\":12},{\"i\":\"ab9b3bfe-a655-4efe-97c8-843288874d05\",\"x\":0,\"y\":71,\"h\":1,\"w\":12},{\"i\":\"4e40808b-1340-4d57-b651-720a285817e2\",\"x\":0,\"y\":72,\"h\":8,\"w\":6},{\"i\":\"eda6e9f8-783e-4145-b7d2-855395ba18a5\",\"x\":6,\"y\":72,\"h\":8,\"w\":6},{\"i\":\"1e934bdd-e6e1-4aab-af5b-c9df6cf61813\",\"x\":0,\"y\":80,\"h\":8,\"w\":6},{\"i\":\"7d9952a7-8dde-45a7-b512-14d9f252e3a7\",\"x\":0,\"y\":88,\"h\":7,\"w\":12}],\"defaultTimeRange\":\"30m\",\"autoRefreshOptions\":[\"5s\",\"10s\",\"15s\",\"30s\",\"60s\"]," +
                "\"variables\":[{\"type\":\"values\",\"name\":\":clusterId:\",\"order\":0," +
                "\"tenant\":\""+serviceMonitor+"\",\"measurement\":\""+metricsName+"\",\"keys\":\"clusterId\",\"defaultValue\":\"\"}," +
                "{\"type\":\"values\",\"name\":\":regionId:\",\"order\":1,\"tenant\":\""+serviceMonitor+"\",\"measurement\":\""+metricsName+"\"," +
                "\"keys\":\"regionId\",\"defaultValue\":\"\",\"where\":[{\"arithmeticOperator\":\"=\",\"tagKey\":\"clusterId\",\"variable\":\":clusterId:\"}]}," +
                "{\"type\":\"values\",\"name\":\":zoneName:\",\"order\":2,\"tenant\":\""+serviceMonitor+"\",\"measurement\":\""+metricsName+"\"," +
                "\"keys\":\"zoneName\",\"defaultValue\":\"\",\"where\":[{\"logicalOperator\":\"AND\",\"arithmeticOperator\":\"=\"," +
                "\"tagKey\":\"clusterId\",\"variable\":\":clusterId:\"},{\"arithmeticOperator\":\"=\",\"tagKey\":\"regionId\",\"variable\":\":regionId:\"}]}]}");
        dashDOS.add(dashDO);
        return  dashDOS;
    }
    private List<AlarmDefinition> mockAlarmForServiceMonitor(boolean  blank) {
        if(blank){
            return Collections.emptyList();
        }
        List<AlarmDefinition> alarmDefinitions = new ArrayList<>();
        AlarmDefinition alarmDefinition = new AlarmDefinition();
        alarmDefinition.setMetricId(srcMetrics1Id);
        alarmDefinition.setId(alarmId1);
        alarmDefinition.setName(alarmName1);
        alarmDefinition.setTenantId(serviceMonitorId);
        alarmDefinitions.add(alarmDefinition);

        alarmDefinition = new AlarmDefinition();
        alarmDefinition.setMetricId(srcMetrics2Id);
        alarmDefinition.setId(alarmId2);
        alarmDefinition.setName(alarmName2);
        alarmDefinition.setTenantId(serviceMonitorId);
        alarmDefinitions.add(alarmDefinition);
        return alarmDefinitions;

    }

    private boolean resultContailsKeyWord(List<String>result , String keyword) {
        boolean contains = false;
        if(CollectionUtils.isEmpty(result)){
            return false;
        }
        for(String item:result){
            if(item.contains(keyword)){
                return true;
            }
        }
        return false;
    }

    private List<MetricsDO> mockMetricsForServiceMonitor(boolean blank) {
        if(blank){
            return Collections.emptyList();
        }
        List<MetricsDO> metricsDOS = new ArrayList<>();
        MetricsDO metricsDO = new MetricsDO();
        metricsDO.setId(srcMetrics1Id);
        metricsDO.setMetricsName(srcMetrics1Name);
        metricsDO.setTenantId(serviceMonitorId);
        metricsDOS.add(metricsDO);
        metricsDO = new MetricsDO();
        metricsDO.setId(srcMetrics2Id);
        metricsDO.setMetricsName(srcMetrics2Name);
        metricsDO.setTenantId(serviceMonitorId);
        metricsDOS.add(metricsDO);
        return metricsDOS;
    }

    private TenantDO mockDestService() {
        TenantDO tenantDO = new TenantDO();
        tenantDO.setId(destServiceId);
        tenantDO.setName(destServiceName);
        return tenantDO;
    }

    private TenantDO mockServiceMonitor() {
        TenantDO tenantDO = new TenantDO();
        tenantDO.setId(serviceMonitorId);
        tenantDO.setName(serviceMonitor);
        return tenantDO;
    }
}
