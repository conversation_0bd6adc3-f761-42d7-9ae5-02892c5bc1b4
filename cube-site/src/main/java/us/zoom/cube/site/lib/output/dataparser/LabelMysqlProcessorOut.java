package us.zoom.cube.site.lib.output.dataparser;


/**
 * <AUTHOR> @date 2020/3/10
 */
public class LabelMysqlProcessorOut extends  BaseProcessorOut{

    private String script;
    private String labelDataSourceId;
    private String keyFields;
    private String valueFields;
    private String nameSpace;
    public LabelMysqlProcessorOut() {
    }

    public LabelMysqlProcessorOut(String id, String name, Integer order, String dataParserPipelineId, String dataParserId, String type, String script, String labelDataSourceId, String keyFields, String valueFields, String nameSpace) {
        super(id, name, order, dataParserPipelineId, dataParserId, type);
        this.script = script;
        this.labelDataSourceId = labelDataSourceId;
        this.keyFields = keyFields;
        this.valueFields = valueFields;
        this.nameSpace = nameSpace;
    }

    public String getScript() {
        return script;
    }

    public void setScript(String script) {
        this.script = script;
    }

    public String getLabelDataSourceId() {
        return labelDataSourceId;
    }

    public void setLabelDataSourceId(String labelDataSourceId) {
        this.labelDataSourceId = labelDataSourceId;
    }

    public String getKeyFields() {
        return keyFields;
    }

    public void setKeyFields(String keyFields) {
        this.keyFields = keyFields;
    }

    public String getValueFields() {
        return valueFields;
    }

    public void setValueFields(String valueFields) {
        this.valueFields = valueFields;
    }

    public String getNameSpace() {
        return nameSpace;
    }

    public void setNameSpace(String nameSpace) {
        this.nameSpace = nameSpace;
    }
}
