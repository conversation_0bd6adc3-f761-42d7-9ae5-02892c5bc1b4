package us.zoom.cube.site.core.parser.process.core.preprocess;

import com.google.common.collect.Lists;
import us.zoom.cube.site.core.parser.process.core.common.GroovyHandler;
import us.zoom.mq.common.entity.TaskEntity;

import java.util.List;
import java.util.Map;

/**
 * @Author: <EMAIL>
 * @ModuleOwner: <EMAIL>
 * @Date: 12/28/2022 15:46
 * @Description:
 */
public class GroovyDataFlowPreProcessService implements DataFlowPreProcessService {
    private GroovyHandler groovyHandler;

    public GroovyDataFlowPreProcessService(String function, String scriptText) {
        this.groovyHandler = new GroovyHandler(function, scriptText);
    }

    @Override
    public List<Map<String, Object>> handle(String message, TaskEntity<String> taskEntity) {
        List<Map<String, Object>> monitorLogList = groovyHandler.invoke(message,taskEntity);
        if (monitorLogList == null) {
            return Lists.newArrayListWithCapacity(1);
        }
        return monitorLogList;
    }
}

