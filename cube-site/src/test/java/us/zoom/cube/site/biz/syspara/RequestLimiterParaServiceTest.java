package us.zoom.cube.site.biz.syspara;

import com.google.common.util.concurrent.RateLimiter;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import us.zoom.infra.dao.model.SysParaDO;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author: <EMAIL>
 * @ModuleOwner: <EMAIL>
 * @Date: 12/06/2023 13:26
 * @Description:
 */
public class RequestLimiterParaServiceTest {
    private SysParaDO sysParaDO;
    private RequestLimiterParaService sysPara;

    RateLimiter globalLimiter = null;
    private Map<String, RateLimiter> otherLimiter = new HashMap<>();

    @BeforeEach
    public void before() {
        sysPara = new RequestLimiterParaService();
        sysParaDO = new SysParaDO();
    }

    @Test
    public void testIsLimiter() {
        String uri = "uri";
        //globalLimiter is null
        globalLimiter = null;
        Assertions.assertEquals(isLimit(uri), false);

        //globalLimiter not acquired
        globalLimiter = Mockito.mock(RateLimiter.class);
        Mockito.when(globalLimiter.tryAcquire()).thenReturn(false);
        Assertions.assertEquals(isLimit(uri), true);

        //globalLimiter acquired
        Mockito.when(globalLimiter.tryAcquire()).thenReturn(true);
        Assertions.assertEquals(isLimit(uri), false);

        //globalLimiter acquired, but otherLimiter not acquired
        globalLimiter = Mockito.mock(RateLimiter.class);
        RateLimiter otherLimiter = Mockito.mock(RateLimiter.class);
        this.otherLimiter.put(uri, otherLimiter);
        Mockito.when(globalLimiter.tryAcquire()).thenReturn(true);
        Mockito.when(otherLimiter.tryAcquire()).thenReturn(false);
        Assertions.assertEquals(isLimit(uri), true);

        //globalLimiter acquired, but otherLimiter not acquired, url not limit
        Assertions.assertEquals(isLimit("1uri"), false);

        //globalLimiter acquired, but otherLimiter acquired
        Mockito.when(globalLimiter.tryAcquire()).thenReturn(true);
        Mockito.when(otherLimiter.tryAcquire()).thenReturn(true);
        Assertions.assertEquals(isLimit(uri), false);

        globalLimiter = null;
        Mockito.when(otherLimiter.tryAcquire()).thenReturn(false);
        Assertions.assertEquals(isLimit(uri), true);
        //globalLimiter is null, but otherLimiter not acquired, url not limit
        Assertions.assertEquals(isLimit("1uri"), false);
    }

    public Boolean isLimit(String uri) {
        if (globalLimiter == null) {
            return otherLimit(uri);
        }
        if (globalLimiter.tryAcquire()) {
            return otherLimit(uri);
        } else {
            return true;
        }
    }

    private boolean otherLimit(String uri) {
        if (otherLimiter.containsKey(uri)) {
            if (otherLimiter.get(uri).tryAcquire()) {
                return false;
            } else {
                return true;
            }
        } else {
            return false;
        }
    }

    @Test
    public void testAbnormalInput() {
        SysParaDO sysParaDO = new SysParaDO();
        sysParaDO.setValue("");
        List<SysParaDO> sysParaDOS = Arrays.asList(sysParaDO);
        RequestLimiterParaService sysPara = new RequestLimiterParaService();
        sysPara.onEventWhenHasData(sysParaDOS);
        Assertions.assertEquals(sysPara.getGlobalLimiter(), null);
        Assertions.assertEquals(sysPara.getOtherLimiter().size(), 0);


        sysParaDO.setValue("{}");
        sysPara.onEventWhenHasData(sysParaDOS);
        Assertions.assertEquals(sysPara.getGlobalLimiter(), null);
        Assertions.assertEquals(sysPara.getOtherLimiter().size(), 0);

        sysParaDO.setValue(null);
        sysPara.onEventWhenHasData(sysParaDOS);
        Assertions.assertEquals(sysPara.getGlobalLimiter(), null);
        Assertions.assertEquals(sysPara.getOtherLimiter().size(), 0);
    }

    @Test
    public void testConfigChangeRecover() {
        testNormalJson();

        sysParaDO.setValue("{}");
        List<SysParaDO> sysParaDOS = Arrays.asList(sysParaDO);
        sysPara.onEventWhenHasData(sysParaDOS);
        Assertions.assertEquals(sysPara.getGlobalLimiter(), null);
        Assertions.assertEquals(sysPara.getOtherLimiter().size(), 0);

        testNormalJson();
    }

    @Test
    public void testConfigChangeNotLoad() {
        testNormalJson();
        RateLimiter limiter = sysPara.getGlobalLimiter();
        testNormalJson();
        Assertions.assertEquals(sysPara.getGlobalLimiter().hashCode(), limiter.hashCode());
    }

    @Test
    public void testChangeGlobalNull() {
        sysParaDO.setValue("{\n" +
                "    \"uriLimiterList\":[\n" +
                "        {\n" +
                "            \"uriList\":[\n" +
                "                \"uri3\",\n" +
                "                \"uri4\"\n" +
                "            ],\n" +
                "            \"permitsPerSecond\":20\n" +
                "        },\n" +
                "        {\n" +
                "            \"uriList\":[\n" +
                "                \"uri1\",\n" +
                "                \"uri2\"\n" +
                "            ],\n" +
                "            \"permitsPerSecond\":30\n" +
                "        }\n" +
                "    ]\n" +
                "}");
        List<SysParaDO> sysParaDOS = Arrays.asList(sysParaDO);
        sysPara.onEventWhenHasData(sysParaDOS);
        Assertions.assertEquals(sysPara.getGlobalLimiter(), null);
    }

    @Test
    public void testChangeOtherNull() {
        sysParaDO.setValue("{\n" +
                "    \"uriLimiterList\":[]\n" +
                "}");
        List<SysParaDO> sysParaDOS = Arrays.asList(sysParaDO);
        sysPara.onEventWhenHasData(sysParaDOS);
        Assertions.assertEquals(sysPara.getGlobalLimiter(), null);
        Assertions.assertEquals(sysPara.getOtherLimiter().size(), 0);
    }

    @Test
    public void testChangeGlobalConfig() {
        testNormalJson();
        
        RateLimiter limiter = sysPara.getGlobalLimiter();
        RateLimiter uri3Limiter = sysPara.getOtherLimiter().get("uri3");

        sysParaDO.setValue("{\n" +
                "    \"globalPermitsPerSecond\":500,\n" +
                "    \"uriLimiterList\":[\n" +
                "        {\n" +
                "            \"uriList\":[\n" +
                "                \"uri3\",\n" +
                "                \"uri4\"\n" +
                "            ],\n" +
                "            \"permitsPerSecond\":20\n" +
                "        },\n" +
                "        {\n" +
                "            \"uriList\":[\n" +
                "                \"uri1\",\n" +
                "                \"uri2\"\n" +
                "            ],\n" +
                "            \"permitsPerSecond\":30\n" +
                "        }\n" +
                "    ]\n" +
                "}");
        List<SysParaDO> sysParaDOS = Arrays.asList(sysParaDO);
        sysPara.onEventWhenHasData(sysParaDOS);
        Assertions.assertNotEquals(sysPara.getGlobalLimiter().hashCode(), limiter.hashCode());
        Assertions.assertNotEquals(sysPara.getOtherLimiter().get("uri3").hashCode(), uri3Limiter.hashCode());
        limiter = sysPara.getGlobalLimiter();
        uri3Limiter = sysPara.getOtherLimiter().get("uri3");

        sysPara.onEventWhenHasData(sysParaDOS);
        Assertions.assertEquals(sysPara.getGlobalLimiter().hashCode(), limiter.hashCode());
        Assertions.assertEquals(sysPara.getOtherLimiter().get("uri3").hashCode(), uri3Limiter.hashCode());
    }

    @Test
    public void testChangeOtherConfig() {
        testNormalJson();
        Map<String, RateLimiter> limiter = sysPara.getOtherLimiter();

        sysParaDO.setValue("{\n" +
                "    \"globalPermitsPerSecond\":100,\n" +
                "    \"uriLimiterList\":[\n" +
                "        {\n" +
                "            \"uriList\":[\n" +
                "                \"uri3\",\n" +
                "                \"uri4\",\n" +
                "                \"uri5\"\n" +
                "            ],\n" +
                "            \"permitsPerSecond\":20\n" +
                "        },\n" +
                "        {\n" +
                "            \"uriList\":[\n" +
                "                \"uri1\",\n" +
                "                \"uri2\"\n" +
                "            ],\n" +
                "            \"permitsPerSecond\":30\n" +
                "        }\n" +
                "    ]\n" +
                "}");
        List<SysParaDO> sysParaDOS = Arrays.asList(sysParaDO);
        sysPara.onEventWhenHasData(sysParaDOS);
        Assertions.assertEquals(sysPara.getOtherLimiter().size(), 5);
    }

    private void testNormalJson() {
        sysParaDO.setValue("{\n" +
                "    \"globalPermitsPerSecond\":100,\n" +
                "    \"uriLimiterList\":[\n" +
                "        {\n" +
                "            \"uriList\":[\n" +
                "                \"uri3\",\n" +
                "                \"uri4\"\n" +
                "            ],\n" +
                "            \"permitsPerSecond\":20\n" +
                "        },\n" +
                "        {\n" +
                "            \"uriList\":[\n" +
                "                \"uri1\",\n" +
                "                \"uri2\"\n" +
                "            ],\n" +
                "            \"permitsPerSecond\":30\n" +
                "        }\n" +
                "    ]\n" +
                "}");
        List<SysParaDO> sysParaDOS = Arrays.asList(sysParaDO);
        sysPara.onEventWhenHasData(sysParaDOS);
        Assertions.assertNotEquals(sysPara.getGlobalLimiter(), null);
        Assertions.assertEquals(sysPara.getOtherLimiter().size(), 4);
    }
}