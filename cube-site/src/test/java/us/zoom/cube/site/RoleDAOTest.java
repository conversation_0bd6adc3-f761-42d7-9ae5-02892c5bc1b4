package us.zoom.cube.site;


import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import us.zoom.cube.site.infra.utils.IdUtils;
import us.zoom.infra.dao.model.UserRoleDO;
import us.zoom.infra.dao.service.UserRoleDAO;

import java.util.List;


@SpringBootTest(classes = CubeSiteApplication.class,webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)//springboot
public class RoleDAOTest {

    @Autowired
    private UserRoleDAO userRoleDAO;


    @Test
    public void testWhole(){

        String roleName="test_role";
        String roleDesc="hello auth";
        UserRoleDO roleDO = mock(roleName,roleDesc,true,true);
        userRoleDAO.addRole(roleDO);
        UserRoleDO  roleFromDb =  userRoleDAO.getRoleByName(roleName);

        Assertions.assertTrue(roleFromDb != null
                && roleFromDb.getCrossService().equals(roleDO.getCrossService())
                && roleFromDb.getPiiAccess().equals(roleDO.getPiiAccess())
                && roleFromDb.getRole().equals(roleDO.getRole()), "add fail ");

//        userRoleDAO.editPiiAccess(roleName,false);
        userRoleDAO.editPiiAccess(roleName,false, "<EMAIL>");

        roleFromDb =  userRoleDAO.getRoleByName(roleName);
        Assertions.assertTrue(roleFromDb.getPiiAccess() == false, "edit pii  fail ");

        roleDO.setRoleDesc("no_desc");
        roleDO.setCrossService(false);
        userRoleDAO.editRole(roleDO);
        roleFromDb =  userRoleDAO.getRoleByName(roleName);

        Assertions.assertTrue(roleFromDb.getRoleDesc().equals("no_desc")  && roleFromDb.getCrossService() == false, "edit auth  fail ");

        List<UserRoleDO> roleDOS = userRoleDAO.findByNameLike(roleName,0,100);

        Assertions.assertTrue(roleDOS.size() >= 1, "findByNameLike   fail ");

        Integer roleCount = userRoleDAO.getCountByNameLike(roleName);
        Assertions.assertTrue(roleCount >= 1, "getCountByNameLike   fail ");

        userRoleDAO.deleteRoleByName(roleName);
        roleFromDb =  userRoleDAO.getRoleByName(roleName);

        Assertions.assertTrue(null==roleFromDb ,"deleteRoleByName   fail ");



//        roleMenuRelaDAO.deleteRelaByMenuResId(menuRes1);
//        roleMenuRelaDAO.deleteRelaByMenuResId(menuRes2);
//
//        roleMenuRelaDOS =   roleMenuRelaDAO.listByRoleName(auth);
//        Assertions.assertTrue("add or listByRoleName  fail ", CollectionUtils.isEmpty(roleMenuRelaDOS));

    }

    private UserRoleDO mock(String roleName, String desc,boolean crossService,boolean accessPii) {
        UserRoleDO role = new UserRoleDO();
        role.setId(IdUtils.generateId());
        role.setRoleDesc(desc);
        role.setRole(roleName);
        role.setCrossService(crossService);
        role.setPiiAccess(accessPii);
        return role;
    }
}
