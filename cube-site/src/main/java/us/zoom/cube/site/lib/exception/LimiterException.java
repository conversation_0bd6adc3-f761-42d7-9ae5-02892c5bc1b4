package us.zoom.cube.site.lib.exception;

public class LimiterException extends RuntimeException {
    private static final long serialVersionUID = 1L;

    private String errorCode;

    private String message;

    public LimiterException() {
        super();
    }

    public LimiterException(String message) {
        super();
        this.message = message;
    }

    public LimiterException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
        this.message = message;
    }

    @Override
    public String getMessage() {
        return message;
    }
}
