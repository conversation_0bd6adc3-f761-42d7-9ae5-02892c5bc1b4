package us.zoom.cube.site.lib;

import lombok.Data;

import java.util.List;

/**
 * @author: <PERSON><PERSON>
 * @date: 2024/9/25 14:03
 * @desc:
 */
@Data
public class DiffTagsFieldsList {

    private String metricsId;

    private List<String> inputTags;

    private List<String> existedTags;

    private List<String> toDeleteTags;


    private List<String> inputFullFieldNames;

    private List<String> existedFieldNames;

    private List<String> toDeleteFields;

}
