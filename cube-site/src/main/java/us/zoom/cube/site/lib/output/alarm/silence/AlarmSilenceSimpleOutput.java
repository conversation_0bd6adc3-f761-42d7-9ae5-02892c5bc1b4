package us.zoom.cube.site.lib.output.alarm.silence;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AlarmSilenceSimpleOutput {
    String id;
    String name;
    String alarmGroupId;
    String alarmGroupName;
    Integer type;
    Integer status;
    String silenceTime;
    String silenceStartTime;
    String silenceEndTime;
    String comment;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    Date createTime;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    Date modifyTime;
    String creator;
    String editor;
    String silenceTimeZone;
    List<String> serviceNameList = new ArrayList<>();
    Integer visibility;
    Integer createType;
    Integer autoDelete;
}
