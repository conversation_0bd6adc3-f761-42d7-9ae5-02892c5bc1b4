package us.zoom.cube.site.biz.servicemonitor;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import us.zoom.cube.site.biz.SysParaService;
import us.zoom.cube.site.core.SubEnvironmentHandler;
import us.zoom.cube.site.core.config.ClickhouseHandlerFactory;
import us.zoom.infra.clickhouse.ClickhouseSqlUtil;
import us.zoom.infra.clickhouse.QueryMonitor;
import us.zoom.infra.enums.QueryStatus;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: <PERSON>ls Ding
 * @date: 30/07/2025 23:10
 * @desc:
 */
@Slf4j
@Service
public class MockClickhouseMetricsService {

    @Autowired
    private SubEnvironmentHandler subEnvironmentHandler;
    @Autowired
    private SysParaService sysParaService;
    @Autowired
    private ClickhouseHandlerFactory clickhouseHandlerFactory;

    public static final String COMMON_SQL_FORMAT = "select %s from %s.%s where time >= %d and time <= %d";


    private List<Map<String, Object>> baseExecuteQuery(String serviceName, String sql) {
        String routedEnv = subEnvironmentHandler.getDataQueryEnv(serviceName);
        if (StringUtils.isEmpty(routedEnv)) {
            routedEnv = sysParaService.getClickhouseEnvRoute(serviceName);
        }
        String finalRoutedEnv = routedEnv;
        QueryMonitor queryMonitor = new QueryMonitor();
        List<Map<String, Object>> queryResult = clickhouseHandlerFactory.get().query(serviceName, sql, finalRoutedEnv, queryMonitor);
        if (QueryStatus.QUERY_FAIL.name().equals(queryMonitor.getStatus())) {
            //throw new RuntimeException(queryMonitor.getMsg());
            //retry 3 times
            for (int i = 0; i < 3; i++) {
                queryResult = clickhouseHandlerFactory.get().query(serviceName, sql, finalRoutedEnv, queryMonitor);
                if (QueryStatus.QUERY_FAIL.name().equals(queryMonitor.getStatus())) {
                    try {
                        Thread.sleep(10);
                    } catch (InterruptedException e) {
                    }
                } else {
                    break;
                }
            }
            if (QueryStatus.QUERY_FAIL.name().equals(queryMonitor.getStatus())) {
                throw new RuntimeException(queryMonitor.getMsg());
            }
        }

        try {
            Thread.sleep(5);
        } catch (InterruptedException e) {
        }

        return queryResult;
    }

    public Map<String, Long> getDistinctCountOfTag(String serviceName, String metricName, List<String> tagNameList, long startSeconds, long endSeconds, boolean isDebugMode) {
        String dbName = ClickhouseSqlUtil.toClickhouseName(serviceName);
        String table = ClickhouseSqlUtil.toClickhouseName(metricName);


        String tagUniqBuilder = tagNameList.stream().map(tagName -> "uniqExact(\"" + tagName + "\") as \"" + tagName + "\"").collect(Collectors.joining(", "));
        String sql = String.format(COMMON_SQL_FORMAT, tagUniqBuilder, dbName, table, startSeconds, endSeconds);
        if(isDebugMode) {
            log.info("MockClickhouseMetricsService getDistinctCountOfTag sql:{}", sql);
        }
        try {
            List<Map<String, Object>> queryResult = baseExecuteQuery(serviceName, sql);

            Map<String, Long> distinctTagCount = new HashMap<>(queryResult.size());
            queryResult.forEach(row -> {
                distinctTagCount.put(row.entrySet().stream().findFirst().get().getKey(), Long.valueOf(row.entrySet().stream().findFirst().get().getValue().toString()));
            });
            return distinctTagCount;
        } catch (Exception e) {
            log.error("getDistinctCountOfTag with clickhouse encounter error={} with sql={}", e, sql);
            throw new RuntimeException("getDistinctCountOfTag with clickhouse encounter error:" + e.getMessage() + ", sql:" + sql, e);
        }
    }

    public List<String> getDistinctValueOfTag(String serviceName, String metricName, String tagName, long startSeconds, long endSeconds, boolean isDebugMode) {
        String dbName = ClickhouseSqlUtil.toClickhouseName(serviceName);
        String table = ClickhouseSqlUtil.toClickhouseName(metricName);

        String distinctTagBuilder = "distinct(\"" + tagName + "\") as \"" + tagName + "\"";
        String sql = String.format(COMMON_SQL_FORMAT, distinctTagBuilder, dbName, table, startSeconds, endSeconds);
        if(isDebugMode) {
            log.info("MockClickhouseMetricsService getDistinctValueOfTag sql:{}", sql);
        }
        try {
            List<Map<String, Object>> queryResult = baseExecuteQuery(serviceName, sql);
            return queryResult.stream().map(row -> row.get(tagName).toString()).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("getDistinctValueOfTag with clickhouse encounter error={} with sql={}", e, sql);
            throw new RuntimeException("getDistinctValueOfTag with clickhouse encounter error:" + e.getMessage() + ", sql:" + sql, e);
        }
    }


    public Map<String, Double> getNumberFieldSumAndStringFieldCount(String serviceName, String metricName, List<String> numberFieldList, List<String> stringFieldList, long startSeconds, long endSeconds, boolean isDebugMode) {
        Assert.isTrue(CollectionUtils.isNotEmpty(numberFieldList) || CollectionUtils.isNotEmpty(stringFieldList), "numberFieldList and stringFieldList can not be empty at the same time");

        String dbName = ClickhouseSqlUtil.toClickhouseName(serviceName);
        String table = ClickhouseSqlUtil.toClickhouseName(metricName);

        String numberFieldSumBuilder = numberFieldList.stream().map(numberFieldName -> "ifNull(sum(\"" + numberFieldName + "\"),0) as \"" + numberFieldName + "\"").collect(Collectors.joining(", "));
        //How to count non-empty string field? Expression: countIf((stringField IS NOT NULL) and (stringField != ''))
        String stringFieldNotNullCountBuilder = stringFieldList.stream().map(stringFieldName -> "countIf((\"" + stringFieldName + "\" IS NOT NULL) and (\"" + stringFieldName + "\" != '')) as \"" + stringFieldName + "\"").collect(Collectors.joining(", "));

        String finalFieldBuilder;
        if (StringUtils.isEmpty(stringFieldNotNullCountBuilder)) {
            finalFieldBuilder = numberFieldSumBuilder;
        } else if (StringUtils.isEmpty(numberFieldSumBuilder)) {
            finalFieldBuilder = stringFieldNotNullCountBuilder;
        } else {
            finalFieldBuilder = numberFieldSumBuilder + "," + stringFieldNotNullCountBuilder;
        }

        String sql = String.format(COMMON_SQL_FORMAT, finalFieldBuilder, dbName, table, startSeconds, endSeconds);
        if(isDebugMode) {
            log.info("MockClickhouseMetricsService getNumberFieldSumAndStringFieldCount sql:{}", sql);
        }
        try {
            List<Map<String, Object>> queryResult = baseExecuteQuery(serviceName, sql);

            Map<String, Double> fieldValueSumAndCount = new HashMap<>(queryResult.size());
            queryResult.forEach(row -> {
                row.entrySet().forEach(entry -> {
                    fieldValueSumAndCount.put(entry.getKey(), Double.valueOf(entry.getValue().toString()));
                });
            });
            return fieldValueSumAndCount;
        } catch (Exception e) {
            log.error("getNumberFieldSumAndStringFieldCount with clickhouse encounter error={} with sql={}", e, sql);
            throw new RuntimeException("getNumberFieldSumAndStringFieldCount with clickhouse encounter error:" + e.getMessage() + ", sql:" + sql, e);
        }
    }
}











