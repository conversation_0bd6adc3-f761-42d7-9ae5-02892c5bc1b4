package us.zoom.cube.site.core;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.lib.common.AuthEncrTypeEnum;
import us.zoom.cube.lib.common.KafkaZipTypeEnum;
import us.zoom.cube.lib.common.SaslMechanismEnum;
import us.zoom.infra.dao.model.OutputAqDO;
import us.zoom.infra.dao.service.OutputAqDAO;
import us.zoom.cube.lib.utils.JsonUtils;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@Component
public class OutputAqHandler {
    @Autowired
    private OutputAqDAO outputAqDAO;

    @Autowired
    private RsaService rsaService;

    private static final Logger LOG= LoggerFactory.getLogger(OutputAqHandler.class.getName());


    public List<OutputAqDO> listAllByTenant(String tenantId) throws Exception {
        return getOutputAqForDecrypt(outputAqDAO.listAllByTenant(tenantId));
    }

    public List<OutputAqDO> listAll( ) throws Exception {
        return outputAqDAO.listAll();
    }

    private List<OutputAqDO> getOutputAqForDecrypt(List<OutputAqDO> outputAqDOS) throws Exception {
        if (CollectionUtils.isEmpty(outputAqDOS)) {
            return Collections.emptyList();
        }
        for (OutputAqDO outputAqDO : outputAqDOS) {
            try {
                decrypt(outputAqDO);
            }catch (Exception e){
                LOG.error("decryput the output error,cfg is "+ JsonUtils.toJsonString(outputAqDO));
                throw  e;
            }
        }
        return outputAqDOS;
    }

    public List<OutputAqDO> findByNameLike(String name, String tenantId, int pageIndex, int pageSize) throws Exception {
        List<OutputAqDO> outputAqDOS=  outputAqDAO.findByNameLike( name,tenantId,pageSize*(pageIndex-1), pageSize);
        return getOutputAqForDecrypt(outputAqDOS);
    }

    public int getCountByNameLike(String name, String tenantId) {
        return  outputAqDAO.getCountByNameLike( name,tenantId);
    }

    public void addAq(OutputAqDO outputAqDO) throws Exception {
        adjustData(outputAqDO);
        encrypt(outputAqDO);
        outputAqDAO.addAq(outputAqDO);
    }


    private void encrypt(OutputAqDO outputAqDO) throws Exception {
        if(StringUtils.isNotBlank(outputAqDO.getKeyPwd())){
            outputAqDO.setKeyPwd(rsaService.encrypt(outputAqDO.getKeyPwd()));
        }

        if(StringUtils.isNotBlank(outputAqDO.getKeystorePwd())){
            outputAqDO.setKeystorePwd(rsaService.encrypt(outputAqDO.getKeystorePwd()));
        }


        if(StringUtils.isNotBlank(outputAqDO.getTruststorePwd())){
            outputAqDO.setTruststorePwd(rsaService.encrypt(outputAqDO.getTruststorePwd()));
        }

        if(StringUtils.isNotBlank(outputAqDO.getJaasConfig())){
            outputAqDO.setJaasConfig(rsaService.encrypt(outputAqDO.getJaasConfig()));
        }
    }


    public void editAq(OutputAqDO outputAqDO) throws Exception {
        adjustData(outputAqDO);
        encrypt(outputAqDO);
        outputAqDAO.editAq(outputAqDO);
    }


    private void decrypt(OutputAqDO outputAqDO) throws Exception {
        if(StringUtils.isNotBlank(outputAqDO.getKeyPwd())){
            outputAqDO.setKeyPwd(rsaService.decrypt(outputAqDO.getKeyPwd()));
        }

        if(StringUtils.isNotBlank(outputAqDO.getKeystorePwd())){
            outputAqDO.setKeystorePwd(rsaService.decrypt(outputAqDO.getKeystorePwd()));
        }

        if(StringUtils.isNotBlank(outputAqDO.getTruststorePwd())){
            outputAqDO.setTruststorePwd(rsaService.decrypt(outputAqDO.getTruststorePwd()));
        }

        if(StringUtils.isNotBlank(outputAqDO.getJaasConfig())){
            outputAqDO.setJaasConfig(rsaService.decrypt(outputAqDO.getJaasConfig()));
        }
    }


    private void adjustData(OutputAqDO outputAqDO) {



        if(StringUtils.isBlank(outputAqDO.getKeystorePath())){
            outputAqDO.setKeystorePath("");
        }

        if(StringUtils.isBlank(outputAqDO.getKeystorePwd())){
            outputAqDO.setKeystorePwd("");
        }

        if(StringUtils.isBlank(outputAqDO.getKeyPwd())){
            outputAqDO.setKeyPwd("");
        }

        if(null == outputAqDO.getZipType()){
            outputAqDO.setZipType(KafkaZipTypeEnum.no.getCode());
        }

        if(StringUtils.isBlank( outputAqDO.getIdenAlgorithm())){
            outputAqDO.setIdenAlgorithm("");
        }

        if(StringUtils.isBlank(outputAqDO.getKeyPwd())){
            outputAqDO.setKeyPwd("");
        }

        if(null == outputAqDO.getSaslMechanism()){
            outputAqDO.setSaslMechanism(SaslMechanismEnum.PLAIN.getCode());
        }

        if(outputAqDO.getAuthEncrType() != null && AuthEncrTypeEnum.no.getValue()==outputAqDO.getAuthEncrType()){
            outputAqDO.setIdenAlgorithm("");
            outputAqDO.setKeyPwd("");
            outputAqDO.setKeystorePath("");
            outputAqDO.setKeystorePwd("");
            outputAqDO.setTruststorePath("");
            outputAqDO.setTruststorePwd("");
            outputAqDO.setJaasConfig("");
            return;
        }

        if(outputAqDO.getAuthEncrType() != null && AuthEncrTypeEnum.ssl.getValue()==outputAqDO.getAuthEncrType()){
            outputAqDO.setJaasConfig("");
            return;
        }



    }
    public OutputAqDO getAqById(String id) throws Exception {
        OutputAqDO outputAqDO=   outputAqDAO.getAqById(id);
        decrypt(outputAqDO);
        return  outputAqDO;
    }


    public void deleteById(String id) {
        outputAqDAO.deleteById(id);
    }

    public boolean hasSameName(String name,String tenantId) {
        return outputAqDAO.getCountByName(name,tenantId) > 0;
    }
}
