package us.zoom.cube.site.biz.influx;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import us.zoom.cube.site.BaseSpringTest;
import us.zoom.cube.site.lib.ResponseObject;
import us.zoom.cube.site.lib.input.influxadmin.BucketInput;
import us.zoom.cube.site.lib.input.influxadmin.DbrpMappingInput;
import us.zoom.cube.site.lib.input.influxadmin.InfluxAdminInput;

/**
 * <AUTHOR>
 * @date 2021/10/9 2:51 PM
 */
public class InfluxManageServiceTest extends BaseSpringTest {

    @Autowired
    private InfluxManageService influxManageService;

    @Test
    public void showBuckets() {
        InfluxAdminInput influxAdminInput = new InfluxAdminInput();
        influxAdminInput.setInfluxdbClusterId("3c1a8bf0-1722-47c5-9fde-97c3ee1cf596");
        ResponseObject result = influxManageService.showBuckets(influxAdminInput);
    }

    @Test
    public void addBucket() {
        BucketInput bucketInput = new BucketInput();
        bucketInput.setBucketName("add-test");
        bucketInput.setInfluxdbClusterId("3c1a8bf0-1722-47c5-9fde-97c3ee1cf596");
        bucketInput.setRetentionInDays(30);
        influxManageService.addBucket(bucketInput);
    }

    @Test
    public void addDbrpMapping() {
        DbrpMappingInput dbrpMappingInput = new DbrpMappingInput();
        dbrpMappingInput.setInfluxdbClusterId("3c1a8bf0-1722-47c5-9fde-97c3ee1cf596");
        dbrpMappingInput.setBucketId("360d79ed2ddd7bee");
        dbrpMappingInput.setDatabaseName("add-test");
        dbrpMappingInput.setRetentionPolicyName("autogen");
        influxManageService.addDbrpMapping(dbrpMappingInput);
    }
}
