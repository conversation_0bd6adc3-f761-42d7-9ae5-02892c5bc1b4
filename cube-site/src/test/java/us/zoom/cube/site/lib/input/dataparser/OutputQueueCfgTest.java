package us.zoom.cube.site.lib.input.dataparser;

import com.google.gson.Gson;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import us.zoom.cube.lib.common.AuthEncrTypeEnum;
import us.zoom.cube.lib.common.KafkaZipTypeEnum;
import us.zoom.cube.lib.common.QueueTypeEnum;
import us.zoom.cube.site.infra.utils.IdUtils;

/**
 * @Author: luis.zheng
 * @Date: 2021/10/27 1:58 PM
 */
public class OutputQueueCfgTest {
    @Test
    public void testJSON() {
        OutputQueueCfg outputQueueCfg = new OutputQueueCfg();
        outputQueueCfg.setOutputQueueType(QueueTypeEnum.async_mq.name());
        outputQueueCfg.setOutputTopic("topica");
        outputQueueCfg.setOutputCompressType(KafkaZipTypeEnum.gzip.name());
        outputQueueCfg.setOutputKafkaClusterId(IdUtils.generateId());
        outputQueueCfg.setOutputKafkaProtocol(AuthEncrTypeEnum.saslSsl.name());

        Gson gson = new Gson();
        String str = gson.toJson(outputQueueCfg);
        System.out.println(str);
        OutputQueueCfg formToStr = gson.fromJson(str, OutputQueueCfg.class);
        Assertions.assertEquals(str, gson.toJson(formToStr));
    }

    @Test
    public void testEmptyJSON() {
        Gson gson = new Gson();
        OutputQueueCfg outputQueueCfg = gson.fromJson("", OutputQueueCfg.class);
        Assertions.assertEquals(null, outputQueueCfg);
    }
}
