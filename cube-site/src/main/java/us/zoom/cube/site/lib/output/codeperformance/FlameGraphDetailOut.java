package us.zoom.cube.site.lib.output.codeperformance;

import lombok.Data;

import java.util.List;

/**
 * @author: tobey.zhu
 * @date: /2023/07/20
 * @description:
 */
@Data
public class FlameGraphDetailOut {
    private Long linkSamples;
    private List<StackLine> stackList;


    @Data
    public static class StackLine {
        private String symbol;
        private String parentLabel;
    }

    public FlameGraphDetailOut(Long linkSamples, List<StackLine> stackList) {
        this.linkSamples = linkSamples;
        this.stackList = stackList;
    }
}
