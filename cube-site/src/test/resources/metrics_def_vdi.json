{"dataParser": {"name": "VDI", "rawDataType": 1, "rawDataParseType": "groovy", "invokeFunction": "parseMessage", "rawDataParseRule": "import groovy.json.JsonSlurper\nimport java.text.SimpleDateFormat;\n\n    def parseMessage(String message) {\n        def jsonSlurper = new JsonSlurper()\n        List<Map<String, Object>> messageJson = jsonSlurper.parseText(message)\n        List<Map<String, Object>> result = new ArrayList(messageJson.size())\n        SimpleDateFormat format = new SimpleDateFormat(\"MM/dd/yyyy HH:mm:ss:SSS\")\n\n        for (Map<String, Object> map : messageJson) {\n            String log = map.message\n            String[] logArray = log.split(\",\")\n            Long collectTime = format.parse(logArray[1]).time\n\n            Map<String, Object> eleMap = new HashMap()\n\n            eleMap.put(\"region\", map.region)\n            eleMap.put(\"hostname\", map.hostname)\n            eleMap.put(\"regionId\", parseZoneAndRegion(map.regionId))\n            eleMap.put(\"zoneName\", parseZoneAndRegion(map.zoneName))\n            eleMap.put(\"mmc\", map.mmc)\n            eleMap.put(\"capacity\", map.capacity)\n            eleMap.put(\"kafkaRegion\", map.kafka_region)\n\n            String instance = getInstance(map.source)\n            eleMap.put(\"instance\", instance)\n            String publicIp = getPublicIpFromInstanceId(map.instance_id)\n            eleMap.put(\"publicIp\", publicIp)\n            String instanceId = publicIp + \"#\" + instance\n            eleMap.put(\"instanceId\", instanceId)\n            String cluster = getCluster(map.cluster_env)\n            eleMap.put(\"cluster\", cluster)\n\n            eleMap.put(\"clusterEnv\", map.cluster_env)\n\n\n            eleMap.put(\"ts\", collectTime)\n            eleMap.put(\"meetingID\", getMeetingID(logArray[2]))\n            eleMap.put(\"dataType\", logArray[3])\n\n            // Added 04-20,  missing ENV and Zone\n            eleMap.put(\"Region\", map.region)\n            eleMap.put(\"Hostname\", map.hostname)\n            eleMap.put(\"Instance\", getInstance(map.source))\n            eleMap.put(\"InstanceId\", instanceId)\n            eleMap.put(\"MMC\", map.mmc)\n            eleMap.put(\"Cluster\", cluster)\n            eleMap.put(\"PublicIp\", publicIp)\n            eleMap.put(\"MeetingId\", getMeetingID(logArray[2]))\n            eleMap.put(\"Capacity\", map.capacity)\n\n            eleMap.put(\"message\", log.substring(log.indexOf(logArray[3]) + logArray[3].length() + 1))\n\n            result.add(eleMap)\n        }\n        return result\n    }\n\n    public String getInstance(String source) {\n        if (source==null || source.isBlank()) {\n            return source;\n        }\n        int endIndex = source.length();\n        while (endIndex > 0 && source.charAt(endIndex - 1) == '/') {\n            endIndex--;\n        }\n        int startIndex = endIndex - 1\n        while (startIndex > 0 && source.charAt(startIndex - 1) != '/') {\n            startIndex--\n        }\n        int first = source.indexOf(\"_\", startIndex + 1);\n        if (first <= 0) {\n            return null\n        }\n        int second = source.indexOf(\"_\", first + 1)\n//        if (StringUtils.containsAny(source, \"monitor_lttgw_srvr\", \"e2e_server\")) {\n        if (source.contains(\"monitor_lttgw_srvr\") || source.contains(\"e2e_server\")) {\n            second = source.indexOf(\"_\", second + 1)\n        }\n        if (second <= 0) {\n            return null\n        }\n        return source.substring(first + 1, second)\n    }\n\n    public String getPublicIpFromInstanceId(String instance) {\n        if (instance==null || instance.isBlank()) {\n            return instance\n    }\n        int index = instance.indexOf(\".\")\n        if (index <= 0) {\n            return instance\n    }\n        index = instance.indexOf(\".\", index + 1)\n        if (index <= 0) {\n            return instance\n    }\n        index = instance.indexOf(\".\", index + 1)\n        if (index <= 0) {\n            return instance\n    }\n        char ch = 0\n        for (index++; index < instance.length(); index++) {\n            ch = instance.charAt(index)\n            if (ch < '0' || ch > '9') {\n                return instance.substring(0, index);\n        }\n    }\n        return instance\n}\n\n    public String getCluster(String env) {\n        if (env != null && env.contains(\",\")) {\n            String[] array = env.split(\",\")\n            return array[0]\n        }\n        return env\n    }\n\n    public String getMeetingID(String meetingIdStr) {\n        if (meetingIdStr==null || meetingIdStr.isBlank()) {\n            return \"\"\n        }\n        int i = meetingIdStr.indexOf(\" \")\n        if (i != -1) {\n            return meetingIdStr.substring(0, i)\n        }\n        return meetingIdStr\n    }\n\n    public String parseZoneAndRegion(String input) {\n        if (input==null || input.isBlank()) {\n            return input;\n        }\n        return substringBefore(substringBefore(input, \";\"), \"#\").trim();\n    }\n\n    public String substringBefore(final String str, final String separator) {\n        final int pos = str.indexOf(separator);\n        return pos == -1 ? str : str.substring(0, pos);\n    }\n\n    public String parseClusterEnv(String env) {\n        if (env==null || env.isBlank()) {\n            return env\n        }\n        return env.replace(\",\", \"|\")\n    }", "useStatus": 1}, "pipeline": [{"name": "VDI_VDIGW_STAT", "filterRule": "dataType=='VDIGW-STAT'", "useStatus": 1, "order": 0, "fields": [{"fieldName": "cluster", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "hostname", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "mmc", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "instance", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "instanceid", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "zone", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "vdigw_load_score", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "env", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "region", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "vdigw_client_count", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "vdigw_conf_count", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}], "processores": [{"name": "split", "order": 0, "type": "split", "sourceField": "message", "schema": {"separator": ",", "entries": [{"index": 0, "name": "conf_count", "type": "number"}, {"index": 1, "name": "apacClientCount", "type": "number"}, {"index": 2, "name": "webclientliteCount", "type": "number"}, {"index": 3, "name": "apiclientCount", "type": "number"}, {"index": 4, "name": "ccclientCount", "type": "number"}, {"index": 5, "name": "rmcclientCount", "type": "number"}, {"index": 6, "name": "ozclientCount", "type": "number"}, {"index": 7, "name": "mobileclientCount", "type": "number"}, {"index": 8, "name": "pwaCount", "type": "number"}, {"index": 9, "name": "load_score", "type": "number"}]}}, {"name": "expression", "order": 1, "type": "expression", "schema": {"entries": [{"name": "instanceid", "expression": "instanceId", "type": "string"}, {"name": "vdigw_client_count", "expression": "webclientliteCount", "type": "number"}, {"name": "instance", "expression": "instance", "type": "string"}, {"name": "hostname", "expression": "hostname", "type": "string"}, {"name": "zone", "expression": "zoneName", "type": "string"}, {"name": "vdigw_conf_count", "expression": "conf_count", "type": "number"}, {"name": "env", "expression": "env", "type": "string"}, {"name": "mmc", "expression": "mmc", "type": "string"}, {"name": "vdigw_load_score", "expression": "load_score", "type": "number"}, {"name": "cluster", "expression": "cluster", "type": "string"}, {"name": "region", "expression": "region", "type": "string"}]}}]}, {"name": "VDI_SERVER_STATUS", "filterRule": "dataType=='SERVER-STATUS'", "useStatus": 1, "order": 1, "fields": [{"fieldName": "cluster", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "hostname", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "mmc", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "instance", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "instanceid", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "zone", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "env", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "region", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "status", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}], "processores": [{"name": "split", "order": 0, "type": "split", "sourceField": "message", "schema": {"separator": ",", "entries": [{"index": 0, "name": "status", "type": "number"}]}}, {"name": "expression", "order": 1, "type": "expression", "schema": {"entries": [{"name": "status", "expression": "status", "type": "number"}, {"name": "instanceid", "expression": "instanceId", "type": "string"}, {"name": "instance", "expression": "instance", "type": "string"}, {"name": "hostname", "expression": "hostname", "type": "string"}, {"name": "zone", "expression": "zoneName", "type": "string"}, {"name": "env", "expression": "env", "type": "string"}, {"name": "mmc", "expression": "mmc", "type": "string"}, {"name": "cluster", "expression": "cluster", "type": "string"}, {"name": "region", "expression": "region", "type": "string"}]}}]}, {"name": "VDI_TP_PACKET_DROPPED", "filterRule": "dataType=='TP_PACKET_DROPPED'", "useStatus": 1, "order": 2, "fields": [{"fieldName": "cluster", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "hostname", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "mmc", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "instanceid", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "instance", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "zone", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "dropp_numbers", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "region", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}], "processores": [{"name": "split", "order": 0, "type": "split", "sourceField": "message", "schema": {"separator": ",", "entries": [{"index": 0, "name": "conf_id", "type": "string"}, {"index": 1, "name": "channel_id", "type": "number"}, {"index": 2, "name": "dropp_numbers", "type": "number"}]}}, {"name": "expression", "order": 1, "type": "expression", "schema": {"entries": [{"name": "instanceid", "expression": "instanceId", "type": "string"}, {"name": "dropp_numbers", "expression": "dropp_numbers", "type": "number"}, {"name": "instance", "expression": "hostname+':'+instance", "type": "string"}, {"name": "zone", "expression": "zoneName", "type": "string"}, {"name": "hostname", "expression": "hostname", "type": "string"}, {"name": "mmc", "expression": "mmc", "type": "string"}, {"name": "cluster", "expression": "cluster", "type": "string"}, {"name": "region", "expression": "region", "type": "string"}]}}]}, {"name": "VDI_THREAD_BLOCK", "filterRule": "dataType=='THREAD-BLOCK'", "useStatus": 1, "order": 3, "fields": [{"fieldName": "cluster", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "hostname", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "mmc", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "instanceid", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "instance", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "zone", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "logger_thread_block_count", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "region", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "block_thread_timing", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}], "processores": [{"name": "split", "order": 0, "type": "split", "sourceField": "message", "schema": {"separator": ",", "entries": [{"index": 0, "name": "self_pid", "type": "number"}, {"index": 1, "name": "thread_id", "type": "number"}, {"index": 2, "name": "block_time", "type": "number"}, {"index": 3, "name": "thread_name", "type": "string"}]}}, {"name": "expression", "order": 1, "type": "expression", "schema": {"entries": [{"name": "instanceid", "expression": "instanceId", "type": "string"}, {"name": "instance", "expression": "hostname+':'+instance", "type": "string"}, {"name": "logger_thread_block_count", "expression": "threadName=='logger_thread'?1:0", "type": "number"}, {"name": "block_thread_timing", "expression": "block_time", "type": "number"}, {"name": "zone", "expression": "zoneName", "type": "string"}, {"name": "hostname", "expression": "hostname", "type": "string"}, {"name": "mmc", "expression": "mmc", "type": "string"}, {"name": "cluster", "expression": "cluster", "type": "string"}, {"name": "region", "expression": "region", "type": "string"}]}}]}, {"name": "VDI_ZONE_VDIC_STATUS", "filterRule": "dataType=='ZONE-VDIC-STATUS'", "useStatus": 1, "order": 4, "fields": [{"fieldName": "cluster", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "mmc", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "instance", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "suspend_vdigw_count", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "attendees", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "zone_usage", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "env", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "attendees_in_suspend", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "ping_conf", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "zone", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "total_rwg_count", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "region", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "conferences", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "total_capacity", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}], "processores": [{"name": "split", "order": 0, "type": "split", "sourceField": "message", "schema": {"separator": ",", "entries": [{"index": 0, "name": "zone_site", "type": "string"}, {"index": 1, "name": "vdic_domain", "type": "string"}, {"index": 2, "name": "total_vdigw_count", "type": "number"}, {"index": 3, "name": "suspend_vdigw_count", "type": "number"}, {"index": 4, "name": "total_capacity", "type": "number"}, {"index": 5, "name": "conferences", "type": "number"}, {"index": 6, "name": "ping_conf", "type": "number"}, {"index": 7, "name": "attendees", "type": "number"}, {"index": 8, "name": "attendees_in_suspend", "type": "number"}]}}, {"name": "expression", "order": 1, "type": "expression", "schema": {"entries": [{"name": "ping_conf", "expression": "ping_conf", "type": "number"}, {"name": "instance", "expression": "instance", "type": "string"}, {"name": "zone", "expression": "zoneName", "type": "string"}, {"name": "total_capacity", "expression": "total_capacity", "type": "number"}, {"name": "region", "expression": "region", "type": "string"}, {"name": "cluster", "expression": "cluster", "type": "string"}, {"name": "attendees_in_suspend", "expression": "attendees_in_suspend", "type": "number"}, {"name": "suspend_vdigw_count", "expression": "suspend_vdigw_count", "type": "number"}, {"name": "zone_usage", "expression": "((attendees*1000)/total_capacity)/10", "type": "number"}, {"name": "attendees", "expression": "attendees", "type": "number"}, {"name": "conferences", "expression": "conferences", "type": "number"}, {"name": "total_rwg_count", "expression": "total_vdigw_count", "type": "number"}, {"name": "env", "expression": "env", "type": "string"}, {"name": "mmc", "expression": "mmc", "type": "string"}]}}]}], "metrics": [{"metricsName": "VDI_SERVER_STATUS", "oldMetricsName": "VDI-SERVER-STATUS", "tagNames": ["zone", "region", "mmc", "instanceid", "instance", "hostname", "env", "cluster"], "tagDetails": [{"tagName": "zone", "oldTagName": "Zone"}, {"tagName": "region", "oldTagName": "Region"}, {"tagName": "mmc", "oldTagName": "MMC"}, {"tagName": "instanceid", "oldTagName": "InstanceId"}, {"tagName": "instance", "oldTagName": "Instance"}, {"tagName": "hostname", "oldTagName": "Hostname"}, {"tagName": "env", "oldTagName": "ENV"}, {"tagName": "cluster", "oldTagName": "Cluster"}], "type": 2, "enabled": true, "metricsFieldList": [{"fieldName": "status", "oldFieldName": "STATUS", "fieldType": 1}], "metricsAggregation": {"metricsAggregationRuleCompose": {"common": [], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "VDI_SERVER_STATUS"}, {"metricsName": "VDI_VDIGW_STAT", "oldMetricsName": "VDI-VDIGW-STAT", "tagNames": ["zone", "region", "mmc", "instanceid", "instance", "hostname", "env", "cluster"], "tagDetails": [{"tagName": "zone", "oldTagName": "Zone"}, {"tagName": "region", "oldTagName": "Region"}, {"tagName": "mmc", "oldTagName": "MMC"}, {"tagName": "instanceid", "oldTagName": "InstanceId"}, {"tagName": "instance", "oldTagName": "Instance"}, {"tagName": "hostname", "oldTagName": "Hostname"}, {"tagName": "env", "oldTagName": "ENV"}, {"tagName": "cluster", "oldTagName": "Cluster"}], "type": 2, "enabled": true, "metricsFieldList": [{"fieldName": "vdigw_conf_count", "fieldType": 1}, {"fieldName": "vdigw_client_count", "fieldType": 1}, {"fieldName": "vdigw_load_score", "fieldType": 1}], "metricsAggregation": {"metricsAggregationRuleCompose": {"common": [], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "VDI_VDIGW_STAT"}, {"metricsName": "VDI_THREAD_BLOCK", "oldMetricsName": "VDI_THREAD-BLOCK", "tagNames": ["instance", "mmc", "region", "instanceid", "hostname", "zone", "cluster"], "tagDetails": [{"tagName": "instance", "oldTagName": "Instance"}, {"tagName": "mmc", "oldTagName": "MMC"}, {"tagName": "region", "oldTagName": "Region"}, {"tagName": "instanceid", "oldTagName": "InstanceId"}, {"tagName": "hostname", "oldTagName": "Hostname"}, {"tagName": "zone", "oldTagName": "Zone"}, {"tagName": "cluster", "oldTagName": "Cluster"}], "type": 1, "enabled": true, "metricsFieldList": [{"fieldName": "block_thread_timing.max", "oldFieldName": "block.thread.timing", "fieldType": 1}, {"fieldName": "logger_thread_block_count.sum", "oldFieldName": "logger.thread.block.count", "fieldType": 1}], "metricsAggregation": {"aggPeriod": 60, "waitTime": 0, "filterCondition": "", "aggregationResultFilter": "", "metricsAggregationRuleCompose": {"common": [{"aggField": "block_thread_timing", "fieldType": "number", "aggTypes": [{"value": "max", "condition": "", "fieldName": "block_thread_timing.max"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "block_thread_timing"}, {"aggField": "logger_thread_block_count", "fieldType": "number", "aggTypes": [{"value": "sum", "condition": "", "fieldName": "logger_thread_block_count.sum"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "logger_thread_block_count"}], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "VDI_THREAD_BLOCK"}, {"metricsName": "VDI_TP_PACKET_DROPPED", "tagNames": ["instance", "mmc", "region", "instanceid", "hostname", "zone", "cluster"], "tagDetails": [{"tagName": "instance", "oldTagName": "Instance"}, {"tagName": "mmc", "oldTagName": "MMC"}, {"tagName": "region", "oldTagName": "Region"}, {"tagName": "instanceid", "oldTagName": "InstanceId"}, {"tagName": "hostname", "oldTagName": "Hostname"}, {"tagName": "zone", "oldTagName": "Zone"}, {"tagName": "cluster", "oldTagName": "Cluster"}], "type": 1, "enabled": true, "metricsFieldList": [{"fieldName": "dropp_numbers.sum", "fieldType": 1}], "metricsAggregation": {"aggPeriod": 60, "waitTime": 0, "filterCondition": "", "aggregationResultFilter": "", "metricsAggregationRuleCompose": {"common": [{"aggField": "dropp_numbers", "fieldType": "number", "aggTypes": [{"value": "sum", "condition": "", "fieldName": "dropp_numbers.sum"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "dropp_numbers"}], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "VDI_TP_PACKET_DROPPED"}, {"metricsName": "ZONE_VDIC_STATUS", "oldMetricsName": "ZONE-VDIC-STATUS", "tagNames": ["vdic_domain", "cluster", "env", "instance", "region", "zone", "mmc"], "tagDetails": [{"tagName": "vdic_domain"}, {"tagName": "cluster", "oldTagName": "Cluster"}, {"tagName": "env", "oldTagName": "ENV"}, {"tagName": "instance", "oldTagName": "Instance"}, {"tagName": "region", "oldTagName": "Region"}, {"tagName": "zone", "oldTagName": "Zone"}, {"tagName": "mmc", "oldTagName": "MMC"}], "type": 2, "enabled": true, "metricsFieldList": [{"fieldName": "total_rwg_count", "fieldType": 1}, {"fieldName": "suspend_vdigw_count", "fieldType": 1}, {"fieldName": "total_capacity", "fieldType": 1}, {"fieldName": "conferences", "fieldType": 1}, {"fieldName": "ping_conf", "fieldType": 1}, {"fieldName": "attendees", "fieldType": 1}, {"fieldName": "attendees_in_suspend", "fieldType": 1}, {"fieldName": "zone_usage", "fieldType": 1}], "metricsAggregation": {"metricsAggregationRuleCompose": {"common": [], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "VDI_ZONE_VDIC_STATUS"}], "alarms": [{"name": "VDIC_CAPACITY_ALERT", "rules": [{"level": "FATAL", "needHits": 3, "hitCount": 3, "timeWindow": 180, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "zone_usage", "operator": ">=", "threshold": "95"}]}, {"level": "ERROR", "needHits": 3, "hitCount": 3, "timeWindow": 180, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "zone_usage", "operator": ">=", "threshold": "90"}]}, {"level": "WARN", "needHits": 3, "hitCount": 3, "timeWindow": 180, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "zone_usage", "operator": ">=", "threshold": "70"}]}], "timesInPeriod": 3, "periodInMinutes": 30, "enabled": false, "notifications": [{"channel": {"name": "VDIGW alert channel", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "VDIC ZONE CAPACITY ALERT ", "content": "Region: ${region}\nAvailable Capacity: ${available_capacity}\nCurrent Usage: ${current_usage}\nZone Usage: ${zone_usage}", "whichLevels": "WARN,ERROR,FATAL", "repeatedSendAsyncmq": false, "isMarkdown": true, "mentions": "[{\"displayName\":\"<PERSON><PERSON>\",\"email\":\"deepman.<PERSON><PERSON><PERSON>@zoom.us\"},{\"displayName\":\"<PERSON><PERSON>\",\"email\":\"<EMAIL>\"},{\"displayName\":\"<PERSON>\",\"email\":\"<EMAIL>\"},{\"displayName\":\"<PERSON>\",\"email\":\"<EMAIL>\"},{\"displayName\":\"Light Song\",\"email\":\"<EMAIL>\"},{\"displayName\":\"<PERSON>\",\"email\":\"<EMAIL>\"},{\"displayName\":\"<PERSON><PERSON><PERSON>\",\"email\":\"<EMAIL>\"}]", "pdGroupTags": ""}, {"channel": {"name": "VOIP DEVOPS P0 Cube Alerts", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "VDIC ZONE CAPACITY ALERT ", "content": "Region: ${region}\nAvailable Capacity: ${available_capacity}\nCurrent Usage: ${current_usage}\nZone Usage: ${zone_usage}", "whichLevels": "FATAL", "repeatedSendAsyncmq": false, "isMarkdown": false, "mentions": "[{\"displayName\":\"<PERSON><PERSON>\",\"email\":\"deepman.<PERSON><PERSON><PERSON>@zoom.us\"},{\"displayName\":\"<PERSON><PERSON>\",\"email\":\"<EMAIL>\"},{\"displayName\":\"<PERSON>\",\"email\":\"<EMAIL>\"},{\"displayName\":\"<PERSON>\",\"email\":\"<EMAIL>\"},{\"displayName\":\"Light Song\",\"email\":\"<EMAIL>\"},{\"displayName\":\"<PERSON>\",\"email\":\"<EMAIL>\"},{\"displayName\":\"<PERSON><PERSON><PERSON>\",\"email\":\"<EMAIL>\"}]", "pdGroupTags": ""}, {"channel": {"name": "VDI ALERT", "engineName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isDefault": 0}, "title": "VDIC ZONE CAPACITY ALERT ", "content": "Region: ${region}\nAvailable Capacity: ${available_capacity}\nCurrent Usage: ${current_usage}\nZone Usage: ${zone_usage}", "whichLevels": "FATAL", "repeatedSendAsyncmq": false, "isMarkdown": false, "mentions": "[]", "pdGroupTags": ""}], "alarmExtensionRelations": [{"relationType": "dashboard", "relationName": "VDIC-CAPACITY-INFO-ZONE", "configValue": ""}], "description": "VDIC ZONE CAPACITY ALERT ", "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "groupTags": "", "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "ZONE_VDIC_STATUS"}, {"name": "VDIGW-STAT-ALTER", "rules": [{"level": "WARN", "needHits": 3, "hitCount": 3, "timeWindow": 180, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "vdigw_client_count", "operator": ">=", "threshold": "280"}]}, {"level": "WARN", "needHits": 3, "hitCount": 3, "timeWindow": 180, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "vdigw_load_score", "operator": ">=", "threshold": "6400"}]}, {"level": "ERROR", "needHits": 3, "hitCount": 3, "timeWindow": 180, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "vdigw_client_count", "operator": ">=", "threshold": "315"}]}, {"level": "ERROR", "needHits": 3, "hitCount": 3, "timeWindow": 180, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "vdigw_load_score", "operator": ">=", "threshold": "7200"}]}, {"level": "ERROR", "needHits": 1, "hitCount": 1, "timeWindow": 60, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "vdigw_client_count", "operator": ">=", "threshold": "350"}]}, {"level": "ERROR", "needHits": 1, "hitCount": 1, "timeWindow": 60, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "vdigw_load_score", "operator": ">=", "threshold": "8000"}]}], "timesInPeriod": 3, "periodInMinutes": 30, "enabled": false, "notifications": [{"channel": {"name": "VDIGW alert channel", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "VDIGW Cube alert: Client: ${vdigw_client_count}, Load: ${vdigw_load_score}", "content": "Level: ${alarmLevel}\nMetric: ${alarmMetricName}\nTime: ${alarmTime}\nClient Cnt: ${vdigw_client_count}\nLoad Score: ${vdigw_load_score}", "whichLevels": "ERROR,FATAL", "repeatedSendAsyncmq": false, "isMarkdown": false, "mentions": "[]", "pdGroupTags": ""}], "alarmExtensionRelations": [], "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "VDI_VDIGW_STAT"}]}