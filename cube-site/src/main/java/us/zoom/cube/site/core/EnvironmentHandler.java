package us.zoom.cube.site.core;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import us.zoom.infra.dao.model.EnvironmentDO;
import us.zoom.infra.dao.service.EnvironmentDAO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-08-09 16:20
 */
@Component
public class EnvironmentHandler {

    @Autowired
    private EnvironmentDAO environmentDAO;

    @Transactional(rollbackFor = Exception.class)
    public void addEnvironment(EnvironmentDO environmentDO){
        environmentDAO.add(environmentDO);
    }

    @Transactional(rollbackFor = Exception.class)
    public void editEnvironment(EnvironmentDO environmentDO){
        environmentDAO.edit(environmentDO);
    }

    public List<EnvironmentDO> searchEnvironment(){
        return environmentDAO.searchAll();

    }

    public void delEnvironment(String id){
        environmentDAO.del(id);

    }


}
