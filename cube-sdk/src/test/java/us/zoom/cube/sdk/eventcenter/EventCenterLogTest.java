package us.zoom.cube.sdk.eventcenter;

import org.testng.Assert;
import org.testng.annotations.Test;
import us.zoom.cube.sdk.eventcenter.v2.CategoryEnum;
import us.zoom.cube.sdk.eventcenter.v2.EventCenterLogV2;
import us.zoom.cube.sdk.eventcenter.v2.StackEnum;

import java.util.HashMap;
import java.util.Map;

public class EventCenterLogTest {

    @Test
    public void testBuildMonitorLogV2() {
        Map<String, String> detail = new HashMap<>();
        detail.put("a", "aa");
        detail.put("b", "bb");
        EventCenterLogV2 logV2 = EventCenterLogV2.builder()
                .withClusterId("us01")
                .withAppName("cube-site")
                .withCategory(CategoryEnum.service)
//                .withDetailType("nacos.configUpdate")
                .withEventName("updateConfigComplete")
                .withOperator("<EMAIL>")
                .withSubCategory("cube-site")
                .withEventSource("cube-site")
                .withDetail(detail)
                .withSummary("toby modify value in nacos")
                .withDetailPii(detail)
                .withEventType("configUpdate")
                .withDc("SJC")
                .withStack(StackEnum.stage)
                .withSet("blue")
                .withZone("SJC_ROOM")
                .build();

        Assert.assertEquals(logV2.getClusterId(), "us01");
        Assert.assertEquals(logV2.getCategory(), CategoryEnum.service.name());
//        Assert.assertEquals(logV2.getDetailType(), "nacos.configUpdate");
        Assert.assertEquals(logV2.getEventName(), "updateConfigComplete");
        Assert.assertEquals(logV2.getOperator(), "<EMAIL>");
        Assert.assertEquals(logV2.getSubCategory(), "cube-site");
        Assert.assertEquals(logV2.getEventSource(), "cube-site");
        Assert.assertEquals(logV2.getDetail(), detail);
        Assert.assertEquals(logV2.getSummary(), "toby modify value in nacos");
        Assert.assertEquals(logV2.getDetailPii(), detail);
        Assert.assertEquals(logV2.getEventType(), "configUpdate");
        Assert.assertEquals(logV2.getDc(), "SJC");
        Assert.assertEquals(logV2.getStack(), "stage");
        Assert.assertEquals(logV2.getSet(), "blue");
        Assert.assertEquals(logV2.getZone(), "SJC_ROOM");


    }

    @Test
    public void testBuildMonitorLog() {
        EventCenterLog monitorLog = EventCenterLog.builder()
                .withAffectLevel(AffectLevelEnum.Service)
                .withSeverityLevel(SeverityLevelEnum.Error)
                .withZoneName("zoneName")
                .withClusterId("us01")
                .withRegionId("va")
                .withContent("{\"test\":\"tst\"}")
                .withEventLevel1(PlatformTypeEnum.SAAS)
                .withEventLevel2(TopTypeEnum.service)
                .withEventLevel3("level3")
                .withEventLevel4("level4")
                .withEventLevel5("level5")
                .withEventLevel6("level6")
                .withEventLevel7("level7")
                .withEventLevel8("level8")
                .withEventLevel9("level9")
                .withEventLevel10("level10")
                .withEventLevel11("level11")
                .withEventLevel12("level12")
                .withEventCreatedBy("cube")
                .withEventSrcType(EventSrcTypeEnum.AccountId)
                .withEventSrc("<EMAIL>")
                .withEventTarget("<EMAIL>")
                .withEventTargetType(EventTargetTypeEnum.Global)
                .withEndTime(1723187890000L)
                .build();

        Assert.assertEquals(AffectLevelEnum.Service.getCode(), monitorLog.getAffectLevel());
        Assert.assertEquals(SeverityLevelEnum.Error.getCode(), monitorLog.getSeverityLevel());
        Assert.assertEquals("{\"test\":\"tst\"}", monitorLog.getContent());
        Assert.assertEquals("SAAS", monitorLog.getEventLevel1());
        Assert.assertEquals("service", monitorLog.getEventLevel2());
        Assert.assertEquals("level3", monitorLog.getEventLevel3());
        Assert.assertEquals("level4", monitorLog.getEventLevel4());
        Assert.assertEquals("level5", monitorLog.getEventLevel5());
        Assert.assertEquals("level6", monitorLog.getEventLevel6());
        Assert.assertEquals("level7", monitorLog.getEventLevel7());
        Assert.assertEquals("level8", monitorLog.getEventLevel8());
        Assert.assertEquals("level9", monitorLog.getEventLevel9());
        Assert.assertEquals("level10", monitorLog.getEventLevel10());
        Assert.assertEquals("level11", monitorLog.getEventLevel11());
        Assert.assertEquals("level12", monitorLog.getEventLevel12());
        Assert.assertEquals("cube", monitorLog.getEventCreatedBy());
        Assert.assertEquals("zoneName", monitorLog.getZoneName());
        Assert.assertEquals("us01", monitorLog.getClusterId());
        Assert.assertEquals("va", monitorLog.getRegionId());
        Assert.assertEquals(EventSrcTypeEnum.AccountId.getCode(), monitorLog.getEventSrcType());
        Assert.assertEquals(EventTargetTypeEnum.Global.getCode(), monitorLog.getEventTargetType());
        Assert.assertEquals("<EMAIL>", monitorLog.getEventSrc());
        Assert.assertEquals("<EMAIL>", monitorLog.getEventTarget());


        Assert.assertTrue(monitorLog.getBeginTime()> 1723187890000L);
    }


}
