package us.zoom.cube.site.infra.enums.trace;

import org.apache.commons.lang3.StringUtils;

/**
 * @author: eason.jia
 * @date: 2024/8/18
 */
public enum PanelType {

    VALUE("value"),

    /**
     * time series
     */
    GRAPH("graph"),

    /**
     * table view
     */
    TABLE("table"),

    /**
     * list view
     */
    LIST("list"),

    /**
     * traces
     */
    TRACE("trace");

    private final String type;

    PanelType(String type) {
        this.type = type;
    }

    public String getType() {
        return type;
    }

    public static boolean validate(String panelType) {
        for (PanelType type : PanelType.values()) {
            if (StringUtils.equalsIgnoreCase(type.getType(), panelType)) {
                return true;
            }
        }
        return false;
    }

    public static PanelType from(String type) {
        for (PanelType panelType : PanelType.values()) {
            if (StringUtils.equalsIgnoreCase(panelType.getType(), type)) {
                return panelType;
            }
        }
        throw new IllegalArgumentException("illegal panel type: " + type);
    }
}
