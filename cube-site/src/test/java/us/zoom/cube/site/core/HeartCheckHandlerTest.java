package us.zoom.cube.site.core;

import org.apache.commons.collections4.CollectionUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import us.zoom.cube.lib.common.CloudTypeEnum;
import us.zoom.cube.lib.common.ServerTypeEnum;
import us.zoom.cube.site.CubeSiteApplicationTests;
import us.zoom.cube.site.biz.SysParaService;
import us.zoom.cube.site.core.model.common.TenantHeartCheckModel;
import us.zoom.cube.site.core.monitor.HeartCheckHandler;
import us.zoom.cube.site.lib.input.ServerInput;
import us.zoom.cube.site.lib.query.PageQuery;
import us.zoom.cube.site.lib.query.ServerQuery;
import us.zoom.infra.dao.model.ServerDO;
import us.zoom.infra.dao.model.TenantDO;
import us.zoom.infra.dao.model.TenantServerCountDO;
import us.zoom.infra.thread.ThreadLocalStore;
import us.zoom.infra.utils.DateUtils;
import us.zoom.cube.lib.utils.JsonUtils;

import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class HeartCheckHandlerTest extends CubeSiteApplicationTests {

    @Autowired
    private HeartCheckHandler heartCheckHandler;

    @Autowired
    private TenantHandler tenantHandler;

    @Autowired
    private ServerHandler serverHandler;

    @Autowired
    private SysParaService sysParaService;


    List<String> toDelhost = new ArrayList<>();

    private void mockData() {
        //mock 4 offline server for every service
        List<TenantDO> tenantDOS = tenantHandler.listAll();
        if (CollectionUtils.isEmpty(tenantDOS)) {
            return;
        }
        Date upstatusDate = new Date(DateUtils.addMinute(new Date(), -sysParaService.getHeartCheckInternalInMins() - 1));
        for (TenantDO tenantDO : tenantDOS) {
            for (int i = 0; i < 4; i++) {
                ServerInput serverInput = new ServerInput();
                serverInput.setTenantId(tenantDO.getId());
                serverInput.setRegionId("va");
                serverInput.setClusterId("dev");
                serverInput.setHost("testHostForOffline" + i);
                serverInput.setIp(Math.abs(new Random().nextInt() % 255) + "." + Math.abs(new Random().nextInt() % 255) + "." + Math.abs(new Random().nextInt() % 255) + "." + Math.abs(new Random().nextInt() % 255));
                serverInput.setType(ServerTypeEnum.agent.name());
                serverInput.setCloudType(CloudTypeEnum.COLO.getType());
                serverInput.setUpStatusTime(upstatusDate);
                serverInput.setAgentKey("");
                ThreadLocalStore.setTenantInfoLocal(tenantDO.getId());
                toDelhost.add(serverHandler.addServer(serverInput));
            }
        }
    }

    @Test
    public void clearData() {
        PageQuery<ServerQuery> pageQuery = new PageQuery<>();
        pageQuery.setPageIndex(1);
        pageQuery.setPageSize(20000);
        ServerQuery serverQuery = new ServerQuery();
        serverQuery.setName("testHostForOffline");
        serverQuery.setType(ServerTypeEnum.agent.name());
        pageQuery.setQueryPara(serverQuery);
        List<ServerDO> serverDOS = serverHandler.findByNameLike(pageQuery);
        serverDOS.forEach(item -> serverHandler.delServer(item.getId()));
    }


    @Test
    public void checkHeart() {
        mockData();
        try {
            serverHandler.offlineServer(sysParaService.getHeartCheckInternalInMins());

            List<TenantDO> tenantDOS = tenantHandler.listAll();
            if (CollectionUtils.isEmpty(tenantDOS)) {
                return;
            }
            List<String> tenantIds = tenantDOS.stream().map(item -> item.getId()).collect(Collectors.toList());
            List<TenantServerCountDO> tenantServerCountHasOfflineDOS = tenantHandler.listTenantServerCountHasOffline(tenantIds, ServerTypeEnum.agent);
            for (TenantServerCountDO tenantServerCountDO : tenantServerCountHasOfflineDOS) {
                Assertions.assertTrue(tenantServerCountDO.getServerCount() >= 4, JsonUtils.toJsonStringIgnoreExp(tenantServerCountDO));
            }

            List<ServerDO> servers = serverHandler.listServerNoHeartLimitCount(tenantIds, ServerTypeEnum.agent, 3);
            Map<String, List<ServerDO>> tenantServerMap = servers.stream().collect(Collectors.groupingBy(ServerDO::getTenantId));
            Assertions.assertTrue(tenantServerMap.size() == tenantIds.size());
            tenantServerMap.forEach((tenantId, serverDOS) -> {
                Assertions.assertTrue(serverDOS.size() >= 2, "tenantId is " + tenantId + " size " + serverDOS.size());
            });

            List<TenantHeartCheckModel> toCheckTenants = tenantDOS.stream().map(item -> {
                TenantHeartCheckModel dest = new TenantHeartCheckModel();
                BeanUtils.copyProperties(item, dest);
                dest.setDispatchTime(System.currentTimeMillis());
                return dest;
            }).collect(Collectors.toList());
            heartCheckHandler.checkHeart(toCheckTenants, ServerTypeEnum.agent);
        } finally {
            clearData();
        }
    }


    @Test
    public void filterOverdueHeartCheck() {
        List<TenantHeartCheckModel> toCheckTenants = new ArrayList<>();
        TenantHeartCheckModel tenantHeartCheckModel = new TenantHeartCheckModel();
        Date disTime = new Date();
        tenantHeartCheckModel.setDispatchTime(disTime.getTime());
        toCheckTenants.add(tenantHeartCheckModel);


        tenantHeartCheckModel = new TenantHeartCheckModel();
        tenantHeartCheckModel.setDispatchTime(DateUtils.addMinute(disTime, -2 * sysParaService.getHeartCheckInternalInMins() - 1));
        toCheckTenants.add(tenantHeartCheckModel);


        tenantHeartCheckModel = new TenantHeartCheckModel();
        tenantHeartCheckModel.setDispatchTime(DateUtils.addMinute(disTime, -2 * sysParaService.getHeartCheckInternalInMins() + 1));
        toCheckTenants.add(tenantHeartCheckModel);

        toCheckTenants = heartCheckHandler.filterOverdueHeartCheck(toCheckTenants);

        Assertions.assertTrue(toCheckTenants.size() == 2);
    }

    public void testIsTimeNotMatch() throws ParseException {
        Calendar calendar = Calendar.getInstance();
        Date date = DateUtils.parseDate("2021-12-20 01:00:23", DateUtils.FORMART1);
        calendar.setTime(date);
        boolean isMatch = heartCheckHandler.isTimeNotMatch(calendar);
        Assertions.assertTrue(isMatch);


        date = DateUtils.parseDate("2021-12-20 01:05:23", DateUtils.FORMART1);
        calendar.setTime(date);
        isMatch = heartCheckHandler.isTimeNotMatch(calendar);
        Assertions.assertTrue(isMatch);


        date = DateUtils.parseDate("2021-12-20 01:11:01", DateUtils.FORMART1);
        calendar.setTime(date);
        isMatch = heartCheckHandler.isTimeNotMatch(calendar);
        Assertions.assertTrue(!isMatch);


        date = DateUtils.parseDate("2021-12-20 00:59:01", DateUtils.FORMART1);
        calendar.setTime(date);
        isMatch = heartCheckHandler.isTimeNotMatch(calendar);
        Assertions.assertTrue(!isMatch);
    }

}
