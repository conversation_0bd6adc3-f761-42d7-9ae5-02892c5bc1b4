package us.zoom.cube.site.biz;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import us.zoom.cube.site.lib.input.ExpressionCheckInput;
import us.zoom.cube.site.lib.output.expression.ExpressionCheckOutput;
import us.zoom.infra.enums.ExpressionCheckTypeEnum;
import us.zoom.cube.lib.utils.JsonUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @author: canyon.li
 * @date: 2023/04/25
 **/
public class ExpressionCheckServiceTest extends BaseTest {

    /**
     * #auth address
     * https://github.com/killme2008/aviator
     * # develop address
     * https://www.yuque.com/boyan-avfmj/aviatorscript/cpow90
     */
    @Autowired
    ExpressionCheckService expressionCheckService;

    @Test
    public void test() {
        parseParams();
        calcExpression();
    }

    @Test
    public void parseParams() {

        try {
            //Test content
            ExpressionCheckInput expressionCheckInput = new ExpressionCheckInput();
            expressionCheckInput.setText("This is ${name} personal test, total = ${total}, curr_num = ${curr_num}, divide = ${curr_num / total}, notExist = ${notExist}, rand = rand()");
            expressionCheckInput.setType(ExpressionCheckTypeEnum.content.name());
            ExpressionCheckOutput output = expressionCheckService.parseParams(expressionCheckInput);
            if (output != null) {
                System.out.println(JsonUtils.toJsonString(output));
            }

            //Test expression
            expressionCheckInput.setText("curr_num / total");
            expressionCheckInput.setType(ExpressionCheckTypeEnum.expression.name());
            ExpressionCheckOutput output2 = expressionCheckService.parseParams(expressionCheckInput);
            if (output2 != null) {
                System.out.println(JsonUtils.toJsonString(output2));
            }
        }catch (Exception e) {
            System.out.println(e);
        }
    }


    @Test
    public void calcExpression() {

        try {
            //Test content
            ExpressionCheckInput expressionCheckInput = new ExpressionCheckInput();
//            expressionCheckInput.setText("This is ${name} personal test, total = ${total}, curr_num = ${curr_num}, divide = ${curr_num / total}, notExist = ${notExist}, rand = rand()");
//            expressionCheckInput.setType(ExpressionCheckTypeEnum.content.name());
//            Map<String, Object> objectMap = new HashMap<>();
//            objectMap.put("name", "canyon");
//            objectMap.put("total", "5");
//            objectMap.put("curr_num", "3");
//            expressionCheckInput.setParams(objectMap);
//            ExpressionCheckOutput output = expressionCheckService.calcExpression(expressionCheckInput);
//            if (output != null) {
//                System.out.println("output = " + JsonUtils.toJsonString(output));
//            }

            //Test expression
            expressionCheckInput.setText("string.startsWith(num, '33')");
            Map<String, Object> objectMap = new HashMap<>();
            objectMap.put("num", "33.5");
            expressionCheckInput.setParams((List<Map<String, String>>) objectMap);
            expressionCheckInput.setType(ExpressionCheckTypeEnum.expression.name());
            ExpressionCheckOutput output2 = expressionCheckService.calcExpression(expressionCheckInput);
            if (output2 != null) {
                System.out.println("output2 = " + JsonUtils.toJsonString(output2));
            }
        }catch (Exception e) {
            System.out.println(e);
        }
    }
}
