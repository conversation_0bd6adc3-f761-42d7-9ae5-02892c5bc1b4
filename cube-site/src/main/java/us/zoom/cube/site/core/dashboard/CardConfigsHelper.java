package us.zoom.cube.site.core.dashboard;

import org.apache.commons.lang3.StringUtils;
import us.zoom.cube.site.biz.dashboard.MigrationServiceMonitor;
import us.zoom.cube.site.lib.output.agg.AggregationFunctionItemOutput;
import us.zoom.cube.site.lib.output.agg.AggregationFunctionRuleOutput;
import us.zoom.cube.site.lib.output.agg.MetricsAggregationRuleComposeOutput;
import us.zoom.cube.site.lib.output.config.metrics.MetricsAggregationOut;
import us.zoom.cube.site.lib.output.config.metrics.MetricsFieldTempOut;
import us.zoom.cube.site.lib.output.config.metrics.MetricsTempOut;
import us.zoom.infra.dao.model.CardDO;
import us.zoom.infra.utils.CommonSplitConstants;

import java.util.List;
import java.util.Map;

import static us.zoom.cube.site.core.DashHandler.SERVICE_MONITOR;

public class CardConfigsHelper {

    public static ConfigsResult parseConfigs(MigrationServiceMonitor migrationServiceMonitor, Map<String, MetricsTempOut> metricsMap, CardDO cardDO, Map<String, String> serviceMap) {
        String configs = cardDO.getConfigs();
        String originalConfigs = configs;
        for (Map.Entry<String, MetricsTempOut> entry : metricsMap.entrySet()) {
            //fix bug for same subString, like 'A-Metrics-A-B' and 'B-A-Metrics-A-B-C'
            if (!containsStr(configs, entry.getKey())) {
                continue;
            }
            if (null == migrationServiceMonitor.getDataParser()) {
                continue;
            }
            String oldService = migrationServiceMonitor.getDataParser().getName();
            if (StringUtils.isBlank(oldService)) {
                continue;
            }
            String newService = serviceMap.get(oldService);
            if (StringUtils.isBlank(newService)) {
                continue;
            }

            MetricsTempOut value = entry.getValue();

            //replace tags
            for (MetricsTempOut.TagDetails tag : value.getTagDetails()) {
                if (StringUtils.isBlank(tag.getOldTagName()) ||
                        !containsStr(configs, tag.getOldTagName())) {
                    continue;
                }
                configs = replaceStr(configs, tag.getOldTagName(), tag.getTagName());
            }

            //replace fields
            for (MetricsFieldTempOut field : value.getMetricsFieldList()) {
                if (StringUtils.isBlank(field.getOldFieldName()) ||
                        !containsStr(configs, field.getOldFieldName())) {
                    continue;
                }
                configs = replaceStr(configs, field.getOldFieldName(), field.getFieldName());
            }


            String oldMetricsName = StringUtils.isNotEmpty(value.getOldMetricsName()) ? value.getOldMetricsName() : value.getMetricsName();
            //replace metrics
            if (StringUtils.isNotBlank(oldMetricsName)) {
                //sql editor
                if (configs.contains("rawText") && !configs.contains("measurement")) {
                    //replace by service.measurement
                    configs = replaceStr(configs, buildServiceMetrics(oldMetricsName, SERVICE_MONITOR), buildServiceMetrics(value.getMetricsName(), newService));
                    configs = configs.replace("\"db\":\"service_monitor\",", String.format("\"db\":\"%s\",", newService));

                } else {
                    //replace by separate
                    configs = replaceStr(configs, oldMetricsName, value.getMetricsName());
                    configs = configs.replace(SERVICE_MONITOR, newService);

                }
            }

            //replace agg fields
            MetricsAggregationOut metricsAggregation = value.getMetricsAggregation();
            if (metricsAggregation != null && metricsAggregation.getMetricsAggregationRuleCompose() != null) {
                MetricsAggregationRuleComposeOutput metricsAggregationRuleCompose = metricsAggregation.getMetricsAggregationRuleCompose();
                List<AggregationFunctionRuleOutput> common = metricsAggregationRuleCompose.getCommon();

                for (AggregationFunctionRuleOutput agg : common) {
                    for (AggregationFunctionItemOutput aggFunction : agg.getAggTypes()) {
                        if (StringUtils.isBlank(agg.getAggField()) ||
                                !containsStr(configs, agg.getAggField())) {
                            continue;
                        }
                        configs = replaceStr(configs, agg.getAggField(), aggFunction.getFieldName());
                    }
                }
            }

        }
        ConfigsResult configsResult = new ConfigsResult(configs, originalConfigs);
        return configsResult;
    }

    private static String buildServiceMetrics(String oldMetricsName, String serviceName) {
        return CommonSplitConstants.BACKSLASH +
                CommonSplitConstants.ESCAPE_QUOTATION
                + serviceName
                + CommonSplitConstants.BACKSLASH
                + CommonSplitConstants.ESCAPE_QUOTATION
                + CommonSplitConstants.DOT
                + CommonSplitConstants.BACKSLASH
                + CommonSplitConstants.ESCAPE_QUOTATION
                + oldMetricsName
                + CommonSplitConstants.BACKSLASH
                + CommonSplitConstants.ESCAPE_QUOTATION;
    }

    private static boolean containsStr(String configs, String seq) {
        if (StringUtils.isBlank(configs) || StringUtils.isBlank(seq)) {
            return false;
        }

        // "seq" or \"seq\"
        if (isContainsWithBackslash(configs, seq)
                || isContainsWithSpaceComma(configs, seq)
                || isContainsWithCommaSpace(configs, seq)
                || isContainsWithSpace(configs, seq)
                || isContainsWithComma(configs, seq)
                || isContainsWithQuotation(configs, seq)
                || isContainsWithSpaceCR(configs, seq)
                || isContainsWithPeriodCR(configs, seq)
                || isContainsWithPeriodSpace(configs, seq)
                || isContainsWithEqual(configs, seq)
                || isContainsWithPeriodComma(configs, seq)
                || isContainsWithSpaceCN(configs, seq)
                || isContainsWithPeriodCN(configs, seq)) {
            return true;
        }

        return false;
    }


    private static boolean isContainsWithBackslash(String configs, String seq) {
        return configs.contains(CommonSplitConstants.ESCAPE_QUOTATION + seq + CommonSplitConstants.BACKSLASH);
    }

    private static boolean isContainsWithSpaceComma(String configs, String seq) {
        return configs.contains(CommonSplitConstants.SPACE + seq + CommonSplitConstants.COMMA_SPLIT);
    }

    private static boolean isContainsWithCommaSpace(String configs, String seq) {
        return configs.contains(CommonSplitConstants.COMMA_SPLIT + seq + CommonSplitConstants.SPACE);
    }

    private static boolean isContainsWithSpace(String configs, String seq) {
        return configs.contains(CommonSplitConstants.SPACE + seq + CommonSplitConstants.SPACE);
    }

    private static boolean isContainsWithComma(String configs, String seq) {
        return configs.contains(CommonSplitConstants.COMMA_SPLIT + seq + CommonSplitConstants.COMMA_SPLIT);
    }

    private static boolean isContainsWithQuotation(String configs, String seq) {
        return configs.contains(CommonSplitConstants.ESCAPE_QUOTATION + seq + CommonSplitConstants.ESCAPE_QUOTATION);
    }


    private static boolean isContainsWithSpaceCR(String configs, String seq) {
        return configs.contains(CommonSplitConstants.SPACE + seq + CommonSplitConstants.CARRIAGE_BACK);
    }

    private static boolean isContainsWithSpaceCN(String configs, String seq) {
        return configs.contains(CommonSplitConstants.SPACE + seq + CommonSplitConstants.CARRIAGE_NEXT);
    }

    private static boolean isContainsWithPeriodCN(String configs, String seq) {
        return configs.contains(CommonSplitConstants.DOT + seq + CommonSplitConstants.CARRIAGE_NEXT);
    }

    private static boolean isContainsWithPeriodCR(String configs, String seq) {
        return configs.contains(CommonSplitConstants.DOT + seq + CommonSplitConstants.CARRIAGE_BACK);
    }

    private static boolean isContainsWithPeriodSpace(String configs, String seq) {
        return configs.contains(CommonSplitConstants.DOT + seq + CommonSplitConstants.SPACE);
    }

    private static boolean isContainsWithPeriodComma(String configs, String seq) {
        return configs.contains(CommonSplitConstants.DOT + seq + CommonSplitConstants.COMMA_SPLIT);
    }

    private static boolean isContainsWithEqual(String configs, String seq) {
        return configs.contains(CommonSplitConstants.DOT + seq + CommonSplitConstants.EQUAL);
    }

//    private static boolean isContainsAgg(String configs, AggregationFunctionRuleOutput agg, AggregationFunctionItemOutput aggFunction) {
//        return configs.contains(aggFunction.getValue() + CommonSplitConstants.LEFT_PARENTHESES
//                + CommonSplitConstants.BACKSLASH + CommonSplitConstants.ESCAPE_QUOTATION + agg.getAggField()
//                + CommonSplitConstants.BACKSLASH + CommonSplitConstants.ESCAPE_QUOTATION + CommonSplitConstants.RIGHT_PARENTHESES);
//    }

    private static String replaceStr(String configs, String str, String replaceStr) {
        if (StringUtils.isBlank(str) || StringUtils.isBlank(replaceStr)) {
            return configs;
        }

        if (isContainsWithQuotation(configs, str)) {
            configs = configs.replace(CommonSplitConstants.ESCAPE_QUOTATION + str + CommonSplitConstants.ESCAPE_QUOTATION,
                    CommonSplitConstants.ESCAPE_QUOTATION + replaceStr + CommonSplitConstants.ESCAPE_QUOTATION);
        }
        if (isContainsWithBackslash(configs, str)) {
            configs = configs.replace(CommonSplitConstants.ESCAPE_QUOTATION + str + CommonSplitConstants.BACKSLASH,
                    CommonSplitConstants.ESCAPE_QUOTATION + replaceStr + CommonSplitConstants.BACKSLASH);
        }
        if (isContainsWithSpaceComma(configs, str)) {
            configs = configs.replace(CommonSplitConstants.SPACE + str + CommonSplitConstants.COMMA_SPLIT,
                    CommonSplitConstants.SPACE + replaceStr + CommonSplitConstants.COMMA_SPLIT);
        }

        if (isContainsWithSpaceCN(configs, str)) {
            configs = configs.replace(CommonSplitConstants.SPACE + str + CommonSplitConstants.CARRIAGE_NEXT,
                    CommonSplitConstants.SPACE + replaceStr + CommonSplitConstants.CARRIAGE_NEXT);
        }

        if (isContainsWithPeriodCN(configs, str)) {
            configs = configs.replace(CommonSplitConstants.DOT + str + CommonSplitConstants.CARRIAGE_NEXT,
                    CommonSplitConstants.DOT + replaceStr + CommonSplitConstants.CARRIAGE_NEXT);
        }

        if (isContainsWithCommaSpace(configs, str)) {
            configs = configs.replace(CommonSplitConstants.COMMA_SPLIT + str + CommonSplitConstants.SPACE,
                    CommonSplitConstants.COMMA_SPLIT + replaceStr + CommonSplitConstants.SPACE);
        }
        if (isContainsWithComma(configs, str)) {
            configs = configs.replace(CommonSplitConstants.COMMA_SPLIT + str + CommonSplitConstants.COMMA_SPLIT,
                    CommonSplitConstants.COMMA_SPLIT + replaceStr + CommonSplitConstants.COMMA_SPLIT);
        }
        if (isContainsWithSpace(configs, str)) {
            configs = configs.replace(CommonSplitConstants.SPACE + str + CommonSplitConstants.SPACE,
                    CommonSplitConstants.SPACE + replaceStr + CommonSplitConstants.SPACE);
        }

        if (isContainsWithSpaceCR(configs, str)) {
            configs = configs.replace(CommonSplitConstants.SPACE + str + CommonSplitConstants.CARRIAGE_BACK,
                    CommonSplitConstants.SPACE + replaceStr + CommonSplitConstants.CARRIAGE_BACK);
        }

        if (isContainsWithPeriodCR(configs, str)) {
            configs = configs.replace(CommonSplitConstants.DOT + str + CommonSplitConstants.CARRIAGE_BACK,
                    CommonSplitConstants.DOT + replaceStr + CommonSplitConstants.CARRIAGE_BACK);
        }

        if (isContainsWithPeriodSpace(configs, str)) {
            configs = configs.replace(CommonSplitConstants.DOT + str + CommonSplitConstants.SPACE,
                    CommonSplitConstants.DOT + replaceStr + CommonSplitConstants.SPACE);
        }

        if (isContainsWithPeriodComma(configs, str)) {
            configs = configs.replace(CommonSplitConstants.DOT + str + CommonSplitConstants.COMMA_SPLIT,
                    CommonSplitConstants.DOT + replaceStr + CommonSplitConstants.COMMA_SPLIT);
        }

        if (isContainsWithEqual(configs, str)) {
            configs = configs.replace(CommonSplitConstants.DOT + str + CommonSplitConstants.EQUAL,
                    CommonSplitConstants.DOT + replaceStr + CommonSplitConstants.EQUAL);
        }


        return configs;
    }

    private static String replaceAgg(String configs, AggregationFunctionRuleOutput agg, AggregationFunctionItemOutput aggFunction) {
        return configs.replace(aggFunction.getValue() + CommonSplitConstants.LEFT_PARENTHESES
                        + CommonSplitConstants.BACKSLASH + CommonSplitConstants.ESCAPE_QUOTATION + agg.getAggField() + CommonSplitConstants.BACKSLASH
                        + CommonSplitConstants.ESCAPE_QUOTATION + CommonSplitConstants.RIGHT_PARENTHESES,
                CommonSplitConstants.BACKSLASH + CommonSplitConstants.ESCAPE_QUOTATION + aggFunction.getFieldName()
                        + CommonSplitConstants.BACKSLASH + CommonSplitConstants.ESCAPE_QUOTATION);
    }

    public record ConfigsResult(String configs, String originalConfigs) {
    }
}
