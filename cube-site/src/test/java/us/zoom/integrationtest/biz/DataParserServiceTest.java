package us.zoom.integrationtest.biz;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import us.zoom.cube.lib.hub.ProcessorTypeEnum;
import us.zoom.cube.site.CubeSummary;
import us.zoom.cube.site.biz.BaseTest;
import us.zoom.cube.site.biz.DataParserService;
import us.zoom.cube.site.lib.ResponseObject;
import us.zoom.cube.site.lib.input.dataparser.*;
import us.zoom.cube.site.mock.DataMock;
import us.zoom.infra.dao.model.DataParserDO;
import us.zoom.infra.dao.model.DataParserFilterProcessorDO;
import us.zoom.infra.dao.model.DataParserPipelineDO;
import us.zoom.infra.dao.model.DataParserRemapperProcessorDO;
import us.zoom.infra.utils.CommonSplitConstants;
import us.zoom.cube.lib.utils.JsonUtils;

import java.util.List;
import java.util.Random;

public class DataParserServiceTest extends BaseTest {

    @Autowired
    private DataParserService dataParserService;


    @Test
    public void add() throws Exception {

        CubeSummary summary= super.buildSummary(USER);
        DataParserDO dataParserDO= dataParserService.getDataParserByTenantIdAndName(summary.getTenantId(),DataMock.TENANT_NAME+ CommonSplitConstants.SPLIT+DataMock.DATA_PARSE_NAME);
        String parserId=null;
        ResponseObject<String> responseObject=null;
        DataParserInput dataParserInput=null;
        dataParserInput= DataMock.mockDataParserInput(summary);
        System.out.println(JsonUtils.toJsonStringIgnoreExp(dataParserInput));
        if(null == dataParserDO){
             dataParserInput= DataMock.mockDataParserInput(summary);
             responseObject=dataParserService.addDataParser(dataParserInput);
             parserId=responseObject.getData() ;
            Assertions.assertTrue(dataParserInput.getName().equals(dataParserDO.getName()));
            Assertions.assertTrue(dataParserInput.getTenantId().equals(dataParserDO.getTenantId()));
            Assertions.assertTrue(dataParserInput.getRawDataType().equals(dataParserDO.getRawDataType()));
            dataParserDO= dataParserService.getDataParserByTenantIdAndName(summary.getTenantId(),DataMock.TENANT_NAME+ CommonSplitConstants.SPLIT+DataMock.DATA_PARSE_NAME);
        }else {
            parserId=dataParserDO.getId();
            dataParserInput=new DataParserInput();
            BeanUtils.copyProperties(dataParserDO,dataParserInput);
        }

        //edit data parser
        dataParserInput.setFilePath("testPath");
        dataParserInput.setId(parserId);
        dataParserInput.setRawDataType(12);
        dataParserInput.setDataType(12);
        dataParserInput.setLanguageType(12);
        dataParserInput.setExcludeRule("testExclude");
        dataParserInput.setCollectType(12);
        dataParserInput.setIncludeRule(null);
        dataParserInput.setRawDataParseRule("testRaw");
//        dataParserInput.setUserId(summary.getUserId());
        dataParserService.editDataParser(dataParserInput);
        dataParserDO= dataParserService.getDataParserByTenantIdAndName(summary.getTenantId(),DataMock.TENANT_NAME+ CommonSplitConstants.SPLIT+DataMock.DATA_PARSE_NAME);
        Assertions.assertTrue(dataParserDO.getFilePath().equals("testPath"));
        Assertions.assertTrue(dataParserDO.getExcludeRule().equals("testExclude"));
        Assertions.assertTrue(dataParserDO.getRawDataParseRule().equals("testRaw"));
        Assertions.assertTrue(dataParserDO.getIncludeRule()==null);
        Assertions.assertTrue(dataParserDO.getRawDataType()==12);
        Assertions.assertTrue(dataParserDO.getDataType()==12);
        Assertions.assertTrue(dataParserDO.getLanguageType()==12);
        Assertions.assertTrue(dataParserDO.getCollectType()==12);
        dataParserInput= DataMock.mockDataParserInput(summary);
        dataParserInput.setId(parserId);
        dataParserService.editDataParser(dataParserInput);




        List<DataParserPipelineDO> pipelines=dataParserService.listPipelinesByDataParserId(parserId);
        Assertions.assertTrue(pipelines.size()>0);


        //edit pipeline
        DataParserPipelineDO pipelineDO=pipelines.get(0);
        pipelineDO.setFilterRule("tmp");
        pipelineDO.setOrder(21);
        DataParserPipelineInput pipelineInput=new DataParserPipelineInput();
//        pipelineInput.setUserId(summary.getUserId());
        pipelineInput.setTenantId(summary.getTenantId());
        BeanUtils.copyProperties(pipelineDO,pipelineInput);
        dataParserService.editPipeline(pipelineInput);

        pipelineDO=  dataParserService.getPipelineById(pipelineDO.getId());
        Assertions.assertTrue(pipelineDO.getOrder()==21);
        Assertions.assertTrue(pipelineDO.getFilterRule().equals("tmp"));


        //add pipeline
        String name="randomName"+ new Random().nextInt();
        int order=new Random().nextInt();
        pipelineInput=DataMock.mockDataParsePipeLine(name,order);
        pipelineInput.setDataParserId(parserId);
        responseObject=dataParserService.addPipeline(pipelineInput);
        String pipelineId=responseObject.getData();
        pipelineDO= dataParserService.getPipelineById(pipelineId);
        Assertions.assertNotNull(pipelineDO);
        Assertions.assertTrue(pipelineDO.getName().equals(DataMock.DATA_PARSE_PIPELINE_NAME_PREFIX+ CommonSplitConstants.SPLIT+name));
        Assertions.assertTrue(pipelineDO.getFilterRule().equals(DataMock.DATA_PARSE_PIPELINE_FILTER));
        Assertions.assertTrue(pipelineDO.getOrder()==order);

        //add filter processor
        String filterName="ifilter";
        List<DataParserFilterProcessorInput> filterProcessorInputs=DataMock.mockFilterProcessor(filterName,20);
        DataParserFilterProcessorInput filterProcessorInput=filterProcessorInputs.get(0);
        filterProcessorInput.setDataParserPipelineId(pipelineDO.getId());
        responseObject=dataParserService.addFilterProcessor(filterProcessorInput);
        DataParserFilterProcessorDO filterProcessorDO= dataParserService.getFilterProcessorById(responseObject.getData());
        Assertions.assertNotNull(filterProcessorDO);
        Assertions.assertTrue(filterProcessorDO.getName().equals(filterName+CommonSplitConstants.SPLIT+DataMock.DATA_PARSE_PIPELINE_FILTER_PROCESSOR_NAME));
        Assertions.assertTrue(filterProcessorDO.getFilterRule().equals(DataMock.DATA_PARSE_PIPELINE_FILTER2));
        Assertions.assertTrue(filterProcessorDO.getOrder()==20);
        Assertions.assertTrue(filterProcessorDO.getDataParserPipelineId().equals(pipelineDO.getId()));



        //edit filter
        filterProcessorInput.setId(filterProcessorDO.getId());
        filterProcessorInput.setFilterRule("tmp grok");
        filterProcessorInput.setOrder(21);
        filterProcessorInput.setName("hello");
//        filterProcessorInput.setUserId(summary.getUserId());
        filterProcessorInput.setDataParserId(dataParserDO.getId());
        dataParserService.editFilterProcessor(filterProcessorInput);
        filterProcessorDO= dataParserService.getFilterProcessorById(filterProcessorInput.getId());
        Assertions.assertTrue(filterProcessorDO.getFilterRule().equals("tmp grok"));
        Assertions.assertTrue(filterProcessorDO.getOrder()==21);
        Assertions.assertTrue(filterProcessorDO.getName().equals("hello"));


        //del filter
        filterProcessorInput.setId(filterProcessorDO.getId());
//        filterProcessorInput.setUserId(summary.getUserId());
        filterProcessorInput.setDataParserId(dataParserDO.getId());
        filterProcessorInput.setDataParserPipelineId(pipelineDO.getId());
        ProcessorId processorId=new ProcessorId( ProcessorTypeEnum.FilterProcessorCfg.getShortCode(),filterProcessorInput.getId(),filterProcessorInput.getDataParserId());
        dataParserService.delProcessor(processorId);
        filterProcessorDO=dataParserService.getFilterProcessorById(filterProcessorInput.getId());
        Assertions.assertNull(filterProcessorDO);
        responseObject= dataParserService.addFilterProcessor(filterProcessorInput);
        String filterId=responseObject.getData();




        //add remapper processor
        String remapperName="iremapper";
        List<DataParserRemapperProcessorInput> remapperProcessorInputs= DataMock.mockRemapperProcessor(remapperName,30);
        DataParserRemapperProcessorInput dataParserRemapperProcessorInput=remapperProcessorInputs.get(0);
        dataParserRemapperProcessorInput.setDataParserPipelineId(pipelineDO.getId());
        responseObject=dataParserService.addRemapperProcessor(dataParserRemapperProcessorInput);
        DataParserRemapperProcessorDO remapperProcessorDO=dataParserService.getRemapperProcessorById(responseObject.getData());
        Assertions.assertNotNull(remapperProcessorDO);
        Assertions.assertTrue(remapperProcessorDO.getName().equals(remapperName+CommonSplitConstants.SPLIT+DataMock.DATA_PARSE_PIPELINE_REMAPPER_PROCESSOR_NAME));
        Assertions.assertTrue(remapperProcessorDO.getSourceFileds().equals(DataMock.DATA_PARSE_PIPELINE_REMAPPER_PROCESSOR_SOURCE_FIELDS));
        Assertions.assertTrue(remapperProcessorDO.getTargetFields().equals(DataMock.DATA_PARSE_PIPELINE_REMAPPER_PROCESSOR_TARGET_FIELDS));
        Assertions.assertTrue(remapperProcessorDO.getDataParserPipelineId().equals(pipelineDO.getId()));
        Assertions.assertTrue(remapperProcessorDO.getOrder()==30);




        //edit remapper
        dataParserRemapperProcessorInput.setId(remapperProcessorDO.getId());
        dataParserRemapperProcessorInput.setSourceFileds("s1,s1,s3");
        dataParserRemapperProcessorInput.setTargetFields("t1,t2,t3");
        dataParserRemapperProcessorInput.setOrder(21);
        dataParserRemapperProcessorInput.setName("hello");
//        dataParserRemapperProcessorInput.setUserId(summary.getUserId());
        dataParserRemapperProcessorInput.setDataParserId(dataParserDO.getId());
        dataParserService.editRemapperProcessor(dataParserRemapperProcessorInput);
        remapperProcessorDO= dataParserService.getRemapperProcessorById(dataParserRemapperProcessorInput.getId());
        Assertions.assertTrue(remapperProcessorDO.getSourceFileds().equals("s1,s1,s3"));
        Assertions.assertTrue(remapperProcessorDO.getTargetFields().equals("t1,t2,t3"));
        Assertions.assertTrue(remapperProcessorDO.getOrder()==21);
        Assertions.assertTrue(remapperProcessorDO.getName().equals("hello"));

        //del remapper
        dataParserRemapperProcessorInput.setId(remapperProcessorDO.getId());
//        dataParserRemapperProcessorInput.setUserId(summary.getUserId());
        dataParserRemapperProcessorInput.setDataParserId(dataParserDO.getId());
        dataParserRemapperProcessorInput.setDataParserPipelineId(pipelineDO.getId());
        processorId=new ProcessorId( ProcessorTypeEnum.RemapperProcessorCfg.getShortCode(),dataParserRemapperProcessorInput.getId(),dataParserRemapperProcessorInput.getDataParserId());
        dataParserService.delProcessor(processorId);
        remapperProcessorDO=dataParserService.getRemapperProcessorById(remapperProcessorDO.getId());
        Assertions.assertNull(remapperProcessorDO);
        responseObject= dataParserService.addRemapperProcessor(dataParserRemapperProcessorInput);
        String remapperId=responseObject.getData();


        //del pipeline
        pipelineInput.setId(pipelineDO.getId());
//        pipelineInput.setUserId(summary.getUserId());
        pipelineInput.setTenantId(summary.getTenantId());
        dataParserService.delPipeline(pipelineInput);
        pipelineDO= dataParserService.getPipelineById(pipelineId);
        Assertions.assertNull(pipelineDO);
        remapperProcessorDO=dataParserService.getRemapperProcessorById(remapperId);
        Assertions.assertNull(remapperProcessorDO);
        filterProcessorDO=dataParserService.getFilterProcessorById(filterId);
        Assertions.assertNull(filterProcessorDO);







    }

}
