package us.zoom.cube.site.lib.output.alarm.insight;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.util.List;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AlarmInsightAlertCountOutput {
    List<String> columns;


    List<Values> values;

    @Data
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class Values {
        List<String> value;
    }
}
