package us.zoom.cube.site.lib.output.dataparser;

/**
 * @Author: luis.zheng
 * @Date: 2021/8/30 5:30
 */
public class KafkaClusterTestConnectionOut {
    private String plainResult = "N/A";
    private String sslResult = "N/A";
    private String sslSaslResult = "N/A";
    private String saslResult ="N/A";

    public String getPlainResult() {
        return plainResult;
    }

    public void setPlainResult(String plainResult) {
        this.plainResult = plainResult;
    }

    public String getSslResult() {
        return sslResult;
    }

    public void setSslResult(String sslResult) {
        this.sslResult = sslResult;
    }

    public String getSslSaslResult() {
        return sslSaslResult;
    }

    public void setSslSaslResult(String sslSaslResult) {
        this.sslSaslResult = sslSaslResult;
    }

    public String getSaslResult() {
        return saslResult;
    }

    public void setSaslResult(String saslResult) {
        this.saslResult = saslResult;
    }
}
