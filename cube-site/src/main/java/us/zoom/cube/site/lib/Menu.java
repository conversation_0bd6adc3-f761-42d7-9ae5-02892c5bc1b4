package us.zoom.cube.site.lib;

import java.util.List;

/**
 * <AUTHOR>
 */
public class Menu {
    private String key;
    private String type;
    private String text;
    private String icon;
    private List<Menu> children;

    public Menu(String key, String type, String text, String icon,List<Menu> children) {
        this.key = key;
        this.type = type;
        this.text = text;
        this.icon = icon;
        this.children=children;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public List<Menu> getChildren() {
        return children;
    }

    public void setChildren(List<Menu> children) {
        this.children = children;
    }
}
