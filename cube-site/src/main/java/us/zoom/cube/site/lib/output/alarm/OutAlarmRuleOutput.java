package us.zoom.cube.site.lib.output.alarm;

import lombok.Data;

import java.util.List;

/**
 * @author: <PERSON><PERSON>
 * @date: 2021/5/14
 */
@Data
public class OutAlarmRuleOutput {
    private String ruleId;

    private Integer type;

    private Double queryDataCount;

    private Double minDataCount;

    private Double maxErrorDataCountRate;

    private Double rateScope;

    private List<OutAlarmRuleTagCondition> tagCondition;

    private List<OutAlarmRuleFieldCondition> fieldCondition;
}
