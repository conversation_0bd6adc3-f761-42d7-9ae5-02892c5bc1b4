package us.zoom.cube.site.core.config;

import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cglib.beans.BeanCopier;
import org.springframework.stereotype.Component;
import us.zoom.cloud.secrets.spring.annotation.SecretValue;
import us.zoom.cube.lib.agent.AgentHeartMonitorTypeEnum;
import us.zoom.cube.sdk.util.MonitorLogReporter;
import us.zoom.cube.site.core.AgentHeartBeatConsumerHandler;
import us.zoom.cube.site.core.model.common.AgentHeartAsyncMQCfg;
import us.zoom.infra.asyncmq.AsyncMqConsumerClusterCache;
import us.zoom.infra.asyncmq.AsyncmqClusterCfg;
import us.zoom.infra.asyncmq.ConsumerKey;
import us.zoom.infra.asyncmq.CubeConsumerMqCluster;
import us.zoom.infra.dao.model.AsyncmqClusterDO;
import us.zoom.infra.dao.model.SysParaDO;
import us.zoom.infra.dao.service.AsyncmqClusterDAO;
import us.zoom.infra.dao.service.SysParaDAO;
import us.zoom.infra.enums.SysParaEnums;
import us.zoom.infra.monitor.AgentHeartMonitor;
import us.zoom.cube.lib.utils.JsonUtils;
import us.zoom.infra.utils.RSAUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: canyon.li
 * @date: 2024/07/19
 **/
@Component
@Slf4j
public class AsyncMQLoader implements CacheLoader {

    @Autowired
    private AsyncmqClusterDAO asyncmqClusterDAO;

    @Autowired
    private SysParaDAO sysParaDAO;

    @Autowired
    private AgentHeartBeatConsumerHandler agentHeartBeatConsumerHandler;

    private static final String AGENT_HEART_ASYNCMQ_SYS_CONFIG = "agentHeartAsyncMQCfg";

    private static final Set<ConsumerKey> AGENT_HEART_CONSUMER_SET = new HashSet<>();

    private static final BeanCopier ASYNCMQ_COPIER = BeanCopier.create(AsyncmqClusterDO.class, AsyncmqClusterCfg.class, false);

    private final Logger monitorLog = LoggerFactory.getLogger("Monitor");

    @SecretValue("common_private_key")
    private String commonPrivateKey;

    @Value("${agent.heart.beat.enabled:true}")
    private boolean agentHeartbeatEnabled;


    @Override
    public void load() {
        long begin = System.currentTimeMillis();
        log.info("Begin load asyncmq to configCache!");
        //load asyncmq cluster
        loadAsyncMQCluster();
        //load asyncmq for agent heart
        if (agentHeartbeatEnabled) {
            loadAgentHeartAsync();
        }
        log.info("load asyncmq over, cost = {}", System.currentTimeMillis() - begin);
    }

    private void loadAgentHeartAsync() {
        try {
            List<SysParaDO> sysParaDOList = sysParaDAO.listByTypeAndKey(SysParaEnums.alarmSite.name(), AGENT_HEART_ASYNCMQ_SYS_CONFIG);
            if (CollectionUtils.isEmpty(sysParaDOList)) {
                log.error("load agentHeart asyncmq failed, sysParam is empty!");
                MonitorLogReporter.report(monitorLog, new AgentHeartMonitor(AgentHeartMonitorTypeEnum.loadAgentHeartTopic.name(), null, null, 0, null, "sysParam is empty"));
                return;
            }
            String value = sysParaDOList.get(0).getValue();
            AgentHeartAsyncMQCfg agentHeartAsyncMQCfg = JsonUtils.toObjectByTypeRef(value, new TypeReference<>() {
            });
            if (Objects.isNull(agentHeartAsyncMQCfg) || CollectionUtils.isEmpty(agentHeartAsyncMQCfg.getAgentHeartAsyncConfigs())) {
                log.error("load agentHeart asyncmq failed, sysParam is empty!");
                MonitorLogReporter.report(monitorLog, new AgentHeartMonitor(AgentHeartMonitorTypeEnum.loadAgentHeartTopic.name(), null, null, 0, null, "sysParam is empty"));
                return;
            }
            for (AgentHeartAsyncMQCfg.ConfigUnit configUnit : agentHeartAsyncMQCfg.getAgentHeartAsyncConfigs()) {
                try {
                    CubeConsumerMqCluster mqCluster = AsyncMqConsumerClusterCache.getMqCluster(configUnit.getAsyncClusterId());
                    ConsumerKey consumerKey = new ConsumerKey(
                            configUnit.getAsyncClusterId(),
                            configUnit.getTopic(),
                            configUnit.getGroup()
                    );
                    //already registered
                    if (AGENT_HEART_CONSUMER_SET.contains(consumerKey)) {
                        continue;
                    }
                    mqCluster.shutdownConsumer(configUnit.getTopic(), configUnit.getGroup());
                    mqCluster.registerConsumerForString(
                            configUnit.getTopic(),
                            configUnit.getGroup(),
                            configUnit.getThreadCount(),
                            agentHeartBeatConsumerHandler
                    );
                    AGENT_HEART_CONSUMER_SET.add(consumerKey);
                    MonitorLogReporter.report(monitorLog, new AgentHeartMonitor(AgentHeartMonitorTypeEnum.loadAgentHeartTopic.name()
                            , configUnit.getTopic(), null, 1
                            , null));
                } catch (Exception e) {
                    log.error("load agentHeart asyncmq failed, config is:{}", JsonUtils.toJsonStringIgnoreExp(configUnit), e);
                    MonitorLogReporter.report(monitorLog, new AgentHeartMonitor(AgentHeartMonitorTypeEnum.loadAgentHeartTopic.name()
                            , configUnit.getTopic(), null, 0, null
                            , e.getMessage()));
                }

            }

        } catch (Exception e) {
            log.error("load agentHeart asyncmq error", e);
            MonitorLogReporter.report(monitorLog, new AgentHeartMonitor(AgentHeartMonitorTypeEnum.loadAgentHeartTopic.name(), null, null, 0, null, e.getMessage()));
        }
    }

    /**
     * load asyncmq cluster
     */
    private void loadAsyncMQCluster() {
        List<AsyncmqClusterDO> asyncmqClusters = asyncmqClusterDAO.listAll();
        if (CollectionUtils.isEmpty(asyncmqClusters)) {
            return;
        }

        Map<String, AsyncmqClusterCfg> asyncClusterMap = asyncmqClusters.stream().map(e -> {
            try {
                AsyncmqClusterCfg cfg = new AsyncmqClusterCfg();
                ASYNCMQ_COPIER.copy(e, cfg, null);
                cfg.setPassword(RSAUtils.decryptByPrivateKey(cfg.getPassword(), commonPrivateKey));
                return cfg;
            } catch (Exception ex) {
                log.error("load async cluster error, name:{}", e.getName(), ex);
                return null;
            }
        }).filter(Objects::nonNull).collect(Collectors.toMap(AsyncmqClusterCfg::getId, asyncmqClusterCfg -> asyncmqClusterCfg));

        //set asyncMQ cluster cache
        AsyncMqConsumerClusterCache.initCluster(asyncClusterMap);
    }

    public void shutdownAsyncMQCluster() {
        AsyncMqConsumerClusterCache.shutdown();
    }
}
