package us.zoom.cube.site.core.processors;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import us.zoom.cube.lib.hub.FilterProcessorCfg;
import us.zoom.cube.lib.hub.ProcessorTypeEnum;
import us.zoom.cube.site.core.parser.process.core.processor.Processor;
import us.zoom.cube.site.core.parser.process.core.processor.ProcessorFactory;
import us.zoom.cube.site.core.parser.process.core.processor.ProcessorResp;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author: <EMAIL>
 * @ModuleOwner: <EMAIL>
 * @Date: 01/11/2023 17:36
 * @Description:
 */
public class FilterProcessorTest {
    @Test
    public void test() {
        Processor processor = ProcessorFactory.getInstance(ProcessorTypeEnum.FilterProcessorCfg.name());
        Map<String, Object> map = new HashMap<>();
        map.put("hostName", "scl-c26-r23-svr01");
        map.put("cluster_id", "dev-scl-ai");
        map.put("instanceId", "scl-c26-r23-svr01");
        map.put("", "scl-c26-r23-svr01");
        map.put("hostName", "scl-c26-r23-svr01");
        map.put("hostName", "scl-c26-r23-svr01");
        map.put("hostName", "scl-c26-r23-svr01");
        map.put("hostName", "scl-c26-r23-svr01");
        map.put("hostName", "scl-c26-r23-svr01");
        map.put("hostName", "scl-c26-r23-svr01");
        map.put("hostName", "scl-c26-r23-svr01");
        FilterProcessorCfg processorCfg = new FilterProcessorCfg();
        processorCfg.setFilterRule("measure=='test'");
        ProcessorResp resp = processor.process(map, processorCfg);

        Assertions.assertEquals(resp.isContinue(), true);
    }

    @Test
    public void testFail() {
        Processor processor = ProcessorFactory.getInstance(ProcessorTypeEnum.FilterProcessorCfg.name());
        Map<String, Object> map = new HashMap<>();
        map.put("measure", "test");
        FilterProcessorCfg processorCfg = new FilterProcessorCfg();
        processorCfg.setFilterRule("import groovy.json.JsonSlurper\n" +
                "\n" +
                "\n" +
                "def processInnerJson(Map map){\n" +
                "    def jsonSlurper = new JsonSlurper()\n" +
                "    try{\n" +
                "        if (map.data != null ) {\n" +
                "            Map<String, Object> dataMap = jsonSlurper.parseText(map.data)\n" +
                "            if(dataMap.get(\"ops\") != null){\n" +
                "                if(dataMap.get(\"ops\").get(\"size\") != null){\n" +
                "                    map.put(\"size\", dataMap.get(\"ops\").get(\"size\"))\n" +
                "                }\n" +
                "                if(dataMap.get(\"ops\").get(\"duration\") != null){\n" +
                "                    map.put(\"duration\", dataMap.get(\"ops\").get(\"duration\"))\n" +
                "                }\n" +
                "                if(dataMap.get(\"ops\") != null){\n" +
                "                    map.put(\"ops\", dataMap.get(\"ops\"))\n" +
                "                }\n" +
                "            }\n" +
                "        }\n" +
                "        List<Map<String, Object>> opsList = jsonSlurper.parseText(dataMap.get(\"ops\"))\n" +
                "        if(opsList != null ){\n" +
                "            map.put(\"opsList\", opsList)\n" +
                "        }\n" +
                "    }catch(Exception e){\n" +
                "    }\n" +
                "    \n" +
                "}");
        ProcessorResp resp = processor.process(map, processorCfg);
        Assertions.assertEquals(resp.isContinue(), true);
    }
}
