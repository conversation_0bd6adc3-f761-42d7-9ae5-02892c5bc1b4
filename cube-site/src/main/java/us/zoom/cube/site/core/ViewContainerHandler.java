package us.zoom.cube.site.core;

import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import us.zoom.infra.dao.model.ViewComponentDO;
import us.zoom.infra.dao.model.ViewContainerDO;
import us.zoom.infra.dao.model.ViewContainerWithOrderDO;
import us.zoom.infra.dao.service.ViewComponentDAO;
import us.zoom.infra.dao.service.ViewContainerDAO;

import java.util.List;

@Component
public class ViewContainerHandler {

    @Autowired
    private ViewContainerDAO viewContainerDAO;

   public List<ViewContainerWithOrderDO> listByDashIdWithOrder(String tenantId, String dashId){
        Assert.notNull(tenantId,"tenantId is null!");
        Assert.notNull(dashId,"dashboard id is null!");
        return viewContainerDAO.listByDashIdWithOrder(tenantId,dashId);
    }


}
