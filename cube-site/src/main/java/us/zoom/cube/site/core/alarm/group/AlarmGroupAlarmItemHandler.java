package us.zoom.cube.site.core.alarm.group;

import com.okta.commons.lang.Collections;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import us.zoom.infra.dao.model.AlarmGroupAlarmItemDO;
import us.zoom.infra.dao.service.AlarmGroupAlarmItemDAO;
import us.zoom.infra.dao.service.AlarmGroupServiceItemDAO;
import us.zoom.infra.dao.service.AlarmGroupTagItemDAO;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: <EMAIL>
 * @ModuleOwner: <EMAIL>
 * @Date: 01/05/2023 19:33
 * @Description:
 */
@Service
public class AlarmGroupAlarmItemHandler {

    @Autowired
    private AlarmGroupAlarmItemDAO alarmGroupAlarmItemDAO;


    public List<AlarmGroupAlarmItemDO> findByAlarmGroupServiceItemIdList(List<String> alarmGroupServiceItemIdList){
        if(Collections.isEmpty(alarmGroupServiceItemIdList)){
            return new ArrayList<>();
        }
        return alarmGroupAlarmItemDAO.findByAlarmGroupServiceItemIdList(alarmGroupServiceItemIdList);
    }


    public void deleteByAlarmGroupServiceItemId(String serviceItemId){
        alarmGroupAlarmItemDAO.deleteByAlarmGroupServiceItemId(serviceItemId);
    }



    public int batchInsert(List<AlarmGroupAlarmItemDO> alarmGroupAlarmItemDOList){
        if(Collections.isEmpty(alarmGroupAlarmItemDOList)){
            return 0;
        }
        return alarmGroupAlarmItemDAO.batchInsert(alarmGroupAlarmItemDOList);
    }

    public int batchUpdate(List<AlarmGroupAlarmItemDO> alarmGroupAlarmItemDOList){
        if(Collections.isEmpty(alarmGroupAlarmItemDOList)){
            return 0;
        }
        return alarmGroupAlarmItemDAO.batchUpdate(alarmGroupAlarmItemDOList);
    }

    public int batchDelete(List<String> idList){
        if(Collections.isEmpty(idList)){
            return 0;
        }
        return alarmGroupAlarmItemDAO.batchDelete(idList);
    }


}
