package us.zoom.cube.site.core;

import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import us.zoom.infra.dao.model.PlanInstanceRelaDO;
import us.zoom.infra.dao.service.PlanInsRelaDAO;

import java.util.List;

/**
 * @Description:
 * @author: <PERSON>
 * @date: 2023/04/11 10:30
 */
@Service
public class PlanInsRelaHandler {

    @Autowired
    private PlanInsRelaDAO planInsRelaDAO;

    public int add(PlanInstanceRelaDO planInstanceRelaDO) {
        return planInsRelaDAO.add(planInstanceRelaDO);
    }

    public int update(PlanInstanceRelaDO planInstanceRelaDO) {
        return planInsRelaDAO.update(planInstanceRelaDO);
    }

    public List<PlanInstanceRelaDO> findInstanceIdsByPlanId(String planId) {
        return planInsRelaDAO.findInstanceIdsByPlanId(planId);
    }

    public int deleteById(String id) {
        return planInsRelaDAO.deleteById(id);
    }
}
