package us.zoom.cube.site.biz.network;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import us.zoom.cube.site.biz.BaseTest;
import us.zoom.cube.site.biz.NetworkDashboardService;
import us.zoom.cube.site.lib.input.network.NetworkOverviewInput;
import us.zoom.cube.site.lib.input.network.NetworkPathHopsInput;
import us.zoom.cube.site.lib.input.network.NetworkPathInput;
import us.zoom.cube.site.lib.input.network.PathHistoryInput;
import us.zoom.infra.thread.ThreadLocalStore;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

//@FixMethodOrder(MethodSorters.JVM)
public class NetworkDashboardServiceTest extends BaseTest {
    @Autowired
    private NetworkDashboardService networkDashboardService;

    private String userId = "bdf9bf43-2376-4ade-abd1-bd0fab887009";

    @BeforeEach
    public void before() {
        ThreadLocalStore.setUserInfoLocal(userId);
    }

    @Test
    public void getOverview(){
        NetworkOverviewInput networkOverviewInput = new NetworkOverviewInput();
        networkOverviewInput.setUserId(userId);
        networkOverviewInput.setTime(new Date().getTime());
        networkOverviewInput.setPastMin(30);
        networkDashboardService.getOverview(networkOverviewInput);
    }

    @Test
    public void getNetPath(){
        NetworkPathInput networkPathInput = new NetworkPathInput();
        networkPathInput.setSIp("*************");
        networkPathInput.setDIp("*************");
        networkPathInput.setPastMin(30);
        networkPathInput.setTime(new Date().getTime());
        networkPathInput.setUserId(userId);
        networkDashboardService.getNetPath(networkPathInput);
    }

    @Test
    public void getNetPathHops(){
        NetworkPathHopsInput networkPathHopsInput = new NetworkPathHopsInput();
        networkPathHopsInput.setPath("*************,*************");
        networkPathHopsInput.setPastMin(30);
        networkPathHopsInput.setTime(new Date().getTime());
        networkPathHopsInput.setUserId(userId);
        networkDashboardService.getNetPathHops(networkPathHopsInput);
    }

    @Test
    public void getPathHistory(){
        PathHistoryInput pathHistoryInput = new PathHistoryInput();
        List<String> paths = new ArrayList<>();
        paths.add("*************,*************");
        pathHistoryInput.setPaths(paths);
        pathHistoryInput.setType("latency");
        pathHistoryInput.setTime(new Date().getTime());
        pathHistoryInput.setPastMin(30);
        pathHistoryInput.setUserId(userId);
        networkDashboardService.getPathHistory(pathHistoryInput);
    }
}
