package us.zoom.cube.site.core;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ActiveProfiles;
import us.zoom.cube.site.biz.BaseTest;
import us.zoom.infra.dao.model.AsyncmqAccountMappingDO;

import java.util.Date;
import java.util.UUID;

@ActiveProfiles("perf_for_local")
public class AsyncmqLoginTokenHandleTest extends BaseTest {

    @Autowired
    private AsyncmqLoginTokenHandle asyncmqLoginTokenHandle;

    @Test
    public void testMethod() {
        AsyncmqAccountMappingDO asyncmqAccountMappingDO = new AsyncmqAccountMappingDO();
        asyncmqAccountMappingDO.setAsyncmqId(UUID.randomUUID().toString());
        asyncmqAccountMappingDO.setAsyncmqName("cube_test");
        asyncmqAccountMappingDO.setModifyTime(new Date());
        asyncmqAccountMappingDO.setCreateTime(new Date());
        asyncmqAccountMappingDO.setId(UUID.randomUUID().toString());
        asyncmqAccountMappingDO.setAsyncmqPassword("first");
        asyncmqLoginTokenHandle.addOrUpdate(asyncmqAccountMappingDO);

        AsyncmqAccountMappingDO second = asyncmqLoginTokenHandle.getAsyncmqInfo("cube_test");
        second.setAsyncmqPassword("secondPassword");
        asyncmqLoginTokenHandle.addOrUpdate(second);

        AsyncmqAccountMappingDO latest = asyncmqLoginTokenHandle.getAsyncmqInfo("cube_test");
        Assertions.assertEquals("secondPassword", latest.getAsyncmqPassword());
    }

}