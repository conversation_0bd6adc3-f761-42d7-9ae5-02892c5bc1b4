package us.zoom.cube.site.core.parser.process.core.processor;

import us.zoom.cube.lib.hub.BaseProcessorCfg;
import us.zoom.cube.lib.hub.TimestampProcessorCfg;
import us.zoom.cube.site.core.parser.process.core.common.enums.MonitoringLogType;

import java.text.SimpleDateFormat;
import java.util.Map;

/**
 * @Author: <EMAIL>
 * @ModuleOwner: <EMAIL>
 * @Date: 01/11/2023 16:36
 * @Description:
 */
public class TimestampProcessor extends Processor {

    public TimestampProcessor() {
        super.type = MonitoringLogType.timestampProcessorFail;
    }

    @Override
    public ProcessorResp process(Map<String, Object> messageMap, BaseProcessorCfg processorCfg) {
        ProcessorResp resp = new ProcessorResp(true,messageMap);
        try {
            TimestampProcessorCfg timestampProcessorCfg = (TimestampProcessorCfg) processorCfg;
            String dateFormat = timestampProcessorCfg.getDateFormat();
            String sourceField = timestampProcessorCfg.getSourceField();
            String targetField = timestampProcessorCfg.getTargetField();

            SimpleDateFormat simpleDateFormat = new SimpleDateFormat(dateFormat);
            messageMap.put(targetField, simpleDateFormat.parse((String) messageMap.get(sourceField)).getTime());
            logSuccess(messageMap,resp);
        } catch (Exception e) {
            logException(e, processorCfg,resp);
        }finally {
            logProcessor(processorCfg,resp);
        }
        return resp;
    }
}
