package us.zoom.cube.site.infra.enums.trace;

import org.apache.commons.lang3.StringUtils;

/**
 * @author: eason.jia
 * @date: 2024/8/18
 */
public enum QueryType {
    UNKNOWN("unknown"),
    <PERSON><PERSON>LDER("builder"),
    CLICKHOUSE_SQL("clickhouse_sql"),
    PROMQL("promql");

    private final String type;

    QueryType(String type) {
        this.type = type;
    }

    public String getType() {
        return type;
    }

    public static boolean validate(String queryType) {
        for (QueryType type : QueryType.values()) {
            if (StringUtils.equalsIgnoreCase(type.getType(), queryType)) {
                return true;
            }
        }
        return false;
    }
}
