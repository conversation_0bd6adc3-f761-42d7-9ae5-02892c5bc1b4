package us.zoom.infra.dao.service;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import us.zoom.cube.site.BaseSpringTest;
import us.zoom.infra.dao.model.AggregationPercentileItemDO;
import us.zoom.cube.lib.utils.JsonUtils;

import java.util.List;

public class AggregationPercentileItemDAOTest extends BaseSpringTest {


    @Autowired
    AggregationPercentileItemDAO  aggregationPercentileItemDAO;

    @Test
    public void insert(){
        String id = "1234";
        AggregationPercentileItemDO aggregationPercentileItemDO = new AggregationPercentileItemDO();
        aggregationPercentileItemDO.setId(id);
        aggregationPercentileItemDO.setPercentileValue(95);
        aggregationPercentileItemDO.setMetricsAggregationRuleId("ruleId1234");
        aggregationPercentileItemDO.setTargetFieldId("targetFieldId1234");
        int insertRes = aggregationPercentileItemDAO.insertAggregationPercentile(aggregationPercentileItemDO);
        System.out.println("insertRes = " + insertRes);
    }

    @Test
    public void crud(){
        String id = "1234";
        AggregationPercentileItemDO aggregationPercentileItemDO = new AggregationPercentileItemDO();
        aggregationPercentileItemDO.setId(id);
        aggregationPercentileItemDO.setPercentileValue(95);
        aggregationPercentileItemDO.setMetricsAggregationRuleId("ruleId1234");
        aggregationPercentileItemDO.setTargetFieldId("targetFieldId1234");
        int insertRes = aggregationPercentileItemDAO.insertAggregationPercentile(aggregationPercentileItemDO);
        System.out.println("insertRes = " + insertRes);


        AggregationPercentileItemDO queryed = aggregationPercentileItemDAO.getById(id);
        System.out.println("queryed = " + JsonUtils.toJsonString(queryed));

        Assertions.assertNotNull(queryed);

        List<AggregationPercentileItemDO> listed = aggregationPercentileItemDAO.listAll();
        System.out.println("listed = " + JsonUtils.toJsonString(listed));
        aggregationPercentileItemDAO.deleteById(id);

        AggregationPercentileItemDO queryed1 = aggregationPercentileItemDAO.getById(id);
        Assertions.assertNull(queryed1);

    }

}
