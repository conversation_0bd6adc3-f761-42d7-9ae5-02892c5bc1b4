package us.zoom.cube.site.core.parser.process.core.processor;

import groovy.lang.GroovyShell;
import groovy.lang.Script;
import org.codehaus.groovy.runtime.InvokerHelper;
import us.zoom.cube.lib.hub.BaseProcessorCfg;
import us.zoom.cube.lib.hub.GroovyProcessorCfg;
import us.zoom.cube.site.core.parser.process.core.common.enums.MonitoringLogType;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @Author: <EMAIL>
 * @ModuleOwner: <EMAIL>
 * @Date: 01/11/2023 16:36
 * @Description:
 */
public class GroovyProcessor extends Processor {

    private static final GroovyShell groovyShell = new GroovyShell();

    private static final Map<String, Script> scriptCache = new ConcurrentHashMap<>();

    public GroovyProcessor() {
        super.type = MonitoringLogType.groovyProcessorFail;
    }

    @Override
    public ProcessorResp process(Map<String, Object> messageMap, BaseProcessorCfg processorCfg) {
        ProcessorResp resp = new ProcessorResp(true,messageMap);
        try {
            GroovyProcessorCfg groovyProcessorCfg = (GroovyProcessorCfg) processorCfg;
            String scriptText = groovyProcessorCfg.getParseRule();
            String invokeFunction = groovyProcessorCfg.getInvokeFunction();

            Script script;
            if (scriptCache.containsKey(scriptText)) {
                script = scriptCache.get(scriptText);
            } else {
                script = groovyShell.parse(scriptText);
                scriptCache.put(scriptText, script);
            }
            InvokerHelper.invokeMethod(script, invokeFunction, messageMap);
            logSuccess(messageMap,resp);
        } catch (Exception e) {
            logException(e, processorCfg,resp);
        }finally {
            logProcessor(processorCfg,resp);
        }
        return resp;
    }


}
