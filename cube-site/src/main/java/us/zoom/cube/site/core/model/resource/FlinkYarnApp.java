package us.zoom.cube.site.core.model.resource;


import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;
import java.io.Serializable;

@XmlAccessorType(XmlAccessType.FIELD)
// XML
@XmlRootElement(name = "app")
// JAXB 
@XmlType(propOrder = {
        "id",
        "user",
        "name",
        "queue",
        "state",
        "finalStatus",
        "progress",
        "trackingUI",
        "trackingUrl",
        "diagnostics",
        "clusterId",
        "applicationType",
        "applicationTags",
        "priority",
        "startedTime",
        "finishedTime",
        "elapsedTime",
        "allocatedMB",
        "runningContainers",
        "vcoreSeconds",
        "memorySeconds",
        "clusterUsagePercentage",
        "queueUsagePercentage",
})
public class FlinkYarnApp  implements Serializable {

    private String id;
    private String user;
    private String name;
    private String queue;
    private String state;
    private String finalStatus;
    private String progress;
    private String trackingUI;
    private String trackingUrl;
    private String diagnostics;
    private String clusterId;
    private String applicationType;
    private String applicationTags;
    private String priority;
    private Long startedTime;
    private Long finishedTime;
    private Long elapsedTime;
    private Long  allocatedMB;
    private Integer  runningContainers;
    private Integer vcoreSeconds;
    private Integer memorySeconds;
    private Double clusterUsagePercentage;
    private Double queueUsagePercentage;

    public FlinkYarnApp(){}

    public FlinkYarnApp(String id, String user, String name, String queue, String state, String finalStatus, String progress, String trackingUI, String trackingUrl, String diagnostics, String clusterId, String applicationType, String applicationTags, String priority, Long startedTime, Long finishedTime, Long elapsedTime, Long allocatedMB, Integer runningContainers, Integer vcoreSeconds, Integer memorySeconds, Double clusterUsagePercentage, Double queueUsagePercentage) {
        this.id = id;
        this.user = user;
        this.name = name;
        this.queue = queue;
        this.state = state;
        this.finalStatus = finalStatus;
        this.progress = progress;
        this.trackingUI = trackingUI;
        this.trackingUrl = trackingUrl;
        this.diagnostics = diagnostics;
        this.clusterId = clusterId;
        this.applicationType = applicationType;
        this.applicationTags = applicationTags;
        this.priority = priority;
        this.startedTime = startedTime;
        this.finishedTime = finishedTime;
        this.elapsedTime = elapsedTime;
        this.allocatedMB = allocatedMB;
        this.runningContainers = runningContainers;
        this.vcoreSeconds = vcoreSeconds;
        this.memorySeconds = memorySeconds;
        this.clusterUsagePercentage = clusterUsagePercentage;
        this.queueUsagePercentage = queueUsagePercentage;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getUser() {
        return user;
    }

    public void setUser(String user) {
        this.user = user;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getQueue() {
        return queue;
    }

    public void setQueue(String queue) {
        this.queue = queue;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getFinalStatus() {
        return finalStatus;
    }

    public void setFinalStatus(String finalStatus) {
        this.finalStatus = finalStatus;
    }

    public String getProgress() {
        return progress;
    }

    public void setProgress(String progress) {
        this.progress = progress;
    }

    public String getTrackingUI() {
        return trackingUI;
    }

    public void setTrackingUI(String trackingUI) {
        this.trackingUI = trackingUI;
    }

    public String getTrackingUrl() {
        return trackingUrl;
    }

    public void setTrackingUrl(String trackingUrl) {
        this.trackingUrl = trackingUrl;
    }

    public String getDiagnostics() {
        return diagnostics;
    }

    public void setDiagnostics(String diagnostics) {
        this.diagnostics = diagnostics;
    }

    public String getClusterId() {
        return clusterId;
    }

    public void setClusterId(String clusterId) {
        this.clusterId = clusterId;
    }

    public String getApplicationType() {
        return applicationType;
    }

    public void setApplicationType(String applicationType) {
        this.applicationType = applicationType;
    }

    public String getApplicationTags() {
        return applicationTags;
    }

    public void setApplicationTags(String applicationTags) {
        this.applicationTags = applicationTags;
    }

    public String getPriority() {
        return priority;
    }

    public void setPriority(String priority) {
        this.priority = priority;
    }

    public Long getStartedTime() {
        return startedTime;
    }

    public void setStartedTime(Long startedTime) {
        this.startedTime = startedTime;
    }

    public Long getFinishedTime() {
        return finishedTime;
    }

    public void setFinishedTime(Long finishedTime) {
        this.finishedTime = finishedTime;
    }

    public Long getElapsedTime() {
        return elapsedTime;
    }

    public void setElapsedTime(Long elapsedTime) {
        this.elapsedTime = elapsedTime;
    }

    public Long getAllocatedMB() {
        return allocatedMB;
    }

    public void setAllocatedMB(Long allocatedMB) {
        this.allocatedMB = allocatedMB;
    }

    public Integer getRunningContainers() {
        return runningContainers;
    }

    public void setRunningContainers(Integer runningContainers) {
        this.runningContainers = runningContainers;
    }

    public Integer getVcoreSeconds() {
        return vcoreSeconds;
    }

    public void setVcoreSeconds(Integer vcoreSeconds) {
        this.vcoreSeconds = vcoreSeconds;
    }

    public Integer getMemorySeconds() {
        return memorySeconds;
    }

    public void setMemorySeconds(Integer memorySeconds) {
        this.memorySeconds = memorySeconds;
    }

    public Double getClusterUsagePercentage() {
        return clusterUsagePercentage;
    }

    public void setClusterUsagePercentage(Double clusterUsagePercentage) {
        this.clusterUsagePercentage = clusterUsagePercentage;
    }

    public Double getQueueUsagePercentage() {
        return queueUsagePercentage;
    }

    public void setQueueUsagePercentage(Double queueUsagePercentage) {
        this.queueUsagePercentage = queueUsagePercentage;
    }
    //<memorySeconds>0</memorySeconds>
//<vcoreSeconds>0</vcoreSeconds>
//<queueUsagePercentage>0.0</queueUsagePercentage>
//<clusterUsagePercentage>0.0</clusterUsagePercentage>
//<preemptedResourceMB>0</preemptedResourceMB>
//<preemptedResourceVCores>0</preemptedResourceVCores>
//<numNonAMContainerPreempted>0</numNonAMContainerPreempted>
//<numAMContainerPreempted>0</numAMContainerPreempted>
//<preemptedMemorySeconds>0</preemptedMemorySeconds>
//<preemptedVcoreSeconds>0</preemptedVcoreSeconds>
//<logAggregationStatus>NOT_START</logAggregationStatus>
//<unmanagedApplication>false</unmanagedApplication>
//<amNodeLabelExpression/>
}
