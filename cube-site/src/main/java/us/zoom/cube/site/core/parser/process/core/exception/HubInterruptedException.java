package us.zoom.cube.site.core.parser.process.core.exception;

public class HubInterruptedException extends RuntimeException {
    public HubInterruptedException() {
    }

    public HubInterruptedException(String msg) {
        super(msg);
    }

    public HubInterruptedException(String msg, Throwable cause) {
        super(msg, cause);
    }

    public HubInterruptedException(Throwable cause) {
        super(cause);
    }
}