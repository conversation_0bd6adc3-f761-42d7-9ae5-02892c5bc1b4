package us.zoom.cube.site.core.capacity;

import lombok.extern.slf4j.Slf4j;
import org.influxdb.dto.QueryResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import us.zoom.cube.lib.integrations.Metrics;
import us.zoom.cube.site.biz.CapacityDashService;
import us.zoom.cube.site.biz.syspara.CapacityParaService;
import us.zoom.cube.site.core.CapacityDashHandler;
import us.zoom.cube.site.core.CapacityServiceHandler;
import us.zoom.cube.site.core.TenantHandler;
import us.zoom.cube.site.core.model.common.CapacityModel;
import us.zoom.cube.site.lib.CapacityDashCfg;
import us.zoom.cube.site.lib.CapacityServiceCfg;
import us.zoom.infra.dao.model.CapacityDashDO;
import us.zoom.infra.dao.model.CapacityServiceDO;
import us.zoom.infra.dao.service.DistributedLockDAO;
import us.zoom.infra.utils.DateUtils;
import us.zoom.infra.utils.IpUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022-05-24 10:53
 */
@Component
@Slf4j
@EnableScheduling
public class CapacitySchedule {

    @Autowired
    DistributedLockDAO distributedLockDAO;
    @Autowired
    TenantHandler tenantHandler;
    @Autowired
    private CapacityParaService calcCapacityParaService;

    @Autowired
    private CapacitySqlParseService capacitySqlParseService;

    @Autowired
    private CapacityDataConvertService capacityDataConvertService;

    @Autowired
    private CapacityService capacityService;

    @Autowired
    private CapacityDashService capacityDashService;

    @Autowired
    private CapacityDashHandler capacityDashHandler;

    @Autowired
    private CapacityServiceHandler capacityServiceHandler;

    private static final String calcIp = "calcIp";

    private static String localIP = IpUtils.getLocalIP();

    @Value("${capacity.distributedLock.type}")
    private String lockType;

    @Scheduled(cron = "0 05 00 * * ?")
    //@Scheduled(cron = "0 05 03 * * ?")
    //@Scheduled(cron = "0 0/04 * * * ?")

    public void asignTasks() {
        long startTime = DateUtils.getTodayStartTime().getTime();
        String checkCalcIp = calcCapacityParaService.getCapacityIpParam();
        Boolean check=checkCalcIp(checkCalcIp );
        if(!check){
            log.info("[CapacityService # asignTasks] this is not calc ip ! ip : {},calc ip : {} ", IpUtils.getLocalIP(),checkCalcIp);
            return;
        }
        log.info("[CapacityService # asignTasks] this is calc ip ! ip : {},calc ip : {} ", IpUtils.getLocalIP(),checkCalcIp);
        List<CapacityDashCfg> capacityDashCfgList = getServiceAll();
        for (CapacityDashCfg capacityDashCfg  : capacityDashCfgList) {
            if (Boolean.parseBoolean(capacityDashCfg.getCapacityClacCfg().getCalcOpen()) != true) {
                log.warn("[CapacityService#CalcOpen()] asignTasks is close!");
                return;
            }
            for (CapacityServiceCfg capacityServiceCfg : capacityDashCfg.getCapacityServiceCfgList()){
                try {
                    CapacityModel capacityModel =new CapacityModel();
                    String sqlMax = capacitySqlParseService.sqlMaxTasks(capacityServiceCfg.getServiceName(), startTime, capacityDashCfg.getSimplePeriod(),
                            capacityDashCfg.getCapacityClacCfg().getAverageTime(),capacityDashCfg.getTags());
                    QueryResult resMax = capacityDataConvertService.getData(sqlMax,capacityServiceCfg.getServiceName());
                    if(capacityService.checkQueryResult(resMax)==false){
                        continue;
                    }
                    String sqlNum = capacitySqlParseService.sqlNumTasks(capacityServiceCfg.getServiceName(),capacityDashCfg.getTags());
                    QueryResult resNum = capacityDataConvertService.getData(sqlNum,capacityServiceCfg.getServiceName());
                    List<Metrics> resMaxMetrics = capacityDataConvertService.transferQueryResult2Metrics(resMax);
                    List<Metrics> resNumMetrics = capacityDataConvertService.transferQueryResult2Metrics(resNum);
                    Map<String, List<Metrics>> resMetrics = capacityService.preProcess(resMaxMetrics, resNumMetrics,capacityModel);
                    List<Metrics> resList = capacityService.process(resMetrics, startTime, capacityServiceCfg.getServiceName(),capacityServiceCfg,capacityDashCfg.getCapacityClacCfg(),capacityModel);
                    for (Metrics metrics : resList) {
                        capacityService.addDataToClickhouse(metrics,capacityDashCfg.getCapacityClacCfg());
                        capacityService.addMetricsToInfluxDB(metrics,capacityDashCfg.getCapacityClacCfg());
                    }

                } catch (Exception e) {
                    log.error("metricsField calc error!", e);
                }
            }
        }

    }
    private Boolean checkCalcIp(String checkCalcIp){
        if(!StringUtils.isEmpty(checkCalcIp)){
            List<String> stringList = Arrays.asList(checkCalcIp.split(","));
            for (String str : stringList ){
                if(localIP.equalsIgnoreCase(str)){
                    return true;
                }
            }
        }
        return false;
    }

    private List<CapacityDashCfg> getServiceAll() {

        List<CapacityDashCfg> capacityDashCfgList = new ArrayList<>();
        List<CapacityDashDO> capacityDashDOList = capacityDashHandler.getServiceAll();
        if(!CollectionUtils.isEmpty(capacityDashDOList)){
            for (CapacityDashDO capacityDashDO : capacityDashDOList){
                CapacityDashCfg capacityDashCfg = capacityDashService.processCapacityDashCfg(capacityDashDO);
                List<CapacityServiceCfg>  capacityServiceCfgList = new ArrayList<>();
                List<CapacityServiceDO> capacityServiceDOList = capacityServiceHandler.getServiceByDashId(capacityDashDO.getId());
                for (CapacityServiceDO capacityServiceDO : capacityServiceDOList){
                    CapacityServiceCfg capacityServiceCfg = capacityDashService.processServiceCfg(capacityServiceDO);
                    capacityServiceCfgList.add(capacityServiceCfg);
                }
                capacityDashCfg.setCapacityServiceCfgList(capacityServiceCfgList);
                capacityDashCfgList.add(capacityDashCfg);
            }
        }
        return capacityDashCfgList;
    }
}
