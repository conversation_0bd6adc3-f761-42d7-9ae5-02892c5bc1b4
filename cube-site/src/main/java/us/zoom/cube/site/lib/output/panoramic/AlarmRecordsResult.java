package us.zoom.cube.site.lib.output.panoramic;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
public class AlarmRecordsResult implements Serializable {
    @Serial
    private static final long serialVersionUID = -6565034336509796081L;
    /**
     * 1 cube 2 incident
     */
    private AlarmTypeEnum type;
    private Long alarmCount;
    private String serviceName;
    private String alarmName;
    private String alarmId;
    @JsonIgnore
    private String alarmLevel;
    private String alertUrl;
    /**
     * ack resolve Triggered
     */
    @JsonIgnore
    private String status;

}
