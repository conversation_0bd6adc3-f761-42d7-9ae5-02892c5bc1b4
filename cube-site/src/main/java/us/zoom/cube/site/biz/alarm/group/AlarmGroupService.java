package us.zoom.cube.site.biz.alarm.group;

import com.google.common.collect.Lists;
import com.zoom.op.monitor.dao.alarm.AlarmDefinitionDao;
import com.zoom.op.monitor.domain.IdAndName;
import com.zoom.op.monitor.domain.alarm.AlarmDefinition;
import com.zoom.op.monitor.domain.alarm.v2.AlarmDefinitionDO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.site.api.intercept.AuthInterceptor;
import us.zoom.cube.site.biz.AuthService;
import us.zoom.cube.site.biz.SysParaService;
import us.zoom.cube.site.biz.syspara.AlarmGroupParaService;
import us.zoom.cube.site.core.AlarmDefinitionHandler;
import us.zoom.cube.site.core.MetricsHandler;
import us.zoom.cube.site.core.TenantHandler;
import us.zoom.cube.site.core.UserHandler;
import us.zoom.cube.site.core.alarm.group.AlarmGroupAlarmItemHandler;
import us.zoom.cube.site.core.alarm.group.AlarmGroupHandler;
import us.zoom.cube.site.core.alarm.group.AlarmGroupServiceItemHandler;
import us.zoom.cube.site.core.alarm.group.AlarmGroupTagItemHandler;
import us.zoom.cube.site.core.alarm.silence.AlarmSilenceHandler;
import us.zoom.cube.site.core.auth.AuthHandler;
import us.zoom.cube.site.infra.utils.IdUtils;
import us.zoom.cube.site.lib.ResponseObject;
import us.zoom.cube.site.lib.input.alarm.group.*;
import us.zoom.cube.site.lib.output.PageResult;
import us.zoom.cube.site.lib.output.alarm.group.*;
import us.zoom.cube.site.lib.output.config.metrics.FieldOrTag;
import us.zoom.cube.site.lib.query.*;
import us.zoom.infra.dao.model.*;
import us.zoom.infra.dao.result.alarmgroup.AddAlarmGroupResult;
import us.zoom.infra.dao.service.AlarmDefinitionV2DAO;
import us.zoom.infra.enums.alarm.AlarmGroupConditionTypeEnum;

import java.util.*;
import java.util.stream.Collectors;

import static us.zoom.infra.utils.AlarmGroupConstants.*;


 @Component
 @Slf4j
public class AlarmGroupService {
     @Autowired
     private AlarmGroupHandler alarmGroupHandler;

     @Autowired
     private AlarmGroupServiceItemHandler alarmGroupServiceItemHandler;

     @Autowired
     private AlarmGroupAlarmItemHandler alarmGroupAlarmItemHandler;

     @Autowired
     private AlarmGroupTagItemHandler alarmGroupTagItemHandler;

     @Autowired
     private AlarmDefinitionV2DAO alarmDefinitionV2Dao;

     @Autowired
     private AlarmGroupUtilService alarmGroupUtilService;

     @Autowired
     private TenantHandler tenantHandler;

     @Autowired
     private MetricsHandler metricsHandler;

     @Autowired
     private SysParaService sysParaService;

     @Autowired
     private AlarmGroupParaService alarmGroupParaService;

     @Autowired
     private AuthHandler authHandler;

     @Autowired
     private AlarmSilenceHandler alarmSilenceHandler;

     @Autowired
     private AlarmDefinitionHandler alarmDefinitionHandler;

     @Autowired
     private AuthService authService;

     @Autowired
     private UserHandler userHandler;

     @Autowired
     private AlarmDefinitionDao alarmDefinitionDao;

     @Autowired
    private AlarmGroupServiceHandler alarmGroupServiceHandler;

     public static final String V1 = "v1";

     public static final String V2 = "v2";

     public ResponseObject getAlarmGroupById(AlarmGroupIdQuery alarmGroupIdQuery){
         authService.checkAuthIgnoreTenant(alarmGroupIdQuery);
         AlarmGroupOutput alarmGroupOutput = getAlarmGroupById(alarmGroupIdQuery.getId(), alarmGroupIdQuery.getUserId());
        ResponseObject responseObject = ResponseObject.success(alarmGroupOutput);
         return responseObject;
     }

     public AlarmGroupOutput getAlarmGroupById(String alarmGroupId, String userId){
         AlarmGroupDO alarmGroupDO = alarmGroupHandler.findById(alarmGroupId);
         if(alarmGroupDO == null){
             Assert.isTrue(false, "The AlarmGroup is not exist");
         }

         List<AlarmGroupServiceItemDO> alarmGroupServiceItemDOList = alarmGroupServiceItemHandler.findByAlarmGroupIdList(Lists.newArrayList(alarmGroupDO.getId()));

         AlarmGroupOutput alarmGroupOutput = new AlarmGroupOutput();
         BeanUtils.copyProperties(alarmGroupDO, alarmGroupOutput);

         List<AlarmGroupServiceItemOutput> currentAlarmGroupServiceItemOutputList = new ArrayList<>();
         alarmGroupServiceItemDOList.forEach(t -> {
             AlarmGroupServiceItemOutput alarmGroupServiceItemOutput = new AlarmGroupServiceItemOutput();
             BeanUtils.copyProperties(t, alarmGroupServiceItemOutput);
             currentAlarmGroupServiceItemOutputList.add(alarmGroupServiceItemOutput);
         });
         alarmGroupOutput.setServiceItemList(currentAlarmGroupServiceItemOutputList);
         List<String> checkServiceIdList = alarmGroupOutput.getServiceItemList().stream().map(e -> e.getServiceId()).collect(Collectors.toList());
         if(!alarmGroupUtilService.canOperateAlarmGroup(userId, checkServiceIdList)){
             Assert.isTrue(false, "The user has no permission to access this AlarmGroup");
         }
         List<String> serviceItemIdList = new ArrayList<>();
         List<String> serviceIdList = new ArrayList<>();

         alarmGroupOutput.getServiceItemList().forEach(t -> {
             serviceItemIdList.add(t.getId());
             serviceIdList.add(t.getServiceId());
         });

         Map<String, TenantDO> serviceDOMap = tenantHandler.listByIds(serviceIdList).stream().collect(Collectors.toMap(TenantDO::getId, e -> e));
         List<AlarmGroupAlarmItemDO> alarmGroupAlarmItemList = alarmGroupAlarmItemHandler.findByAlarmGroupServiceItemIdList(serviceItemIdList);
         List<String> alarmIdList = alarmGroupAlarmItemList.stream().map(e -> e.getAlarmId()).collect(Collectors.toList());
         List<IdAndName> alarmList = alarmDefinitionHandler.findByIdList(alarmIdList);
         Map<String, IdAndName> alarmMap = alarmList.stream().collect(Collectors.toMap(IdAndName::getId, e->e));

         Map<String, List<AlarmGroupAlarmItemDO>> alarmGroupAlarmItemDOMap = alarmGroupAlarmItemList.stream().collect(Collectors.groupingBy(AlarmGroupAlarmItemDO::getAlarmGroupServiceItemId, Collectors.toList()));
         Map<String, List<AlarmGroupTagItemDO>> alarmGroupTagItemDOMap = alarmGroupTagItemHandler.findByAlarmGroupServiceItemIdList(serviceItemIdList).stream().collect(Collectors.groupingBy(AlarmGroupTagItemDO::getAlarmGroupServiceItemId, Collectors.toList()));

         alarmGroupOutput.getServiceItemList().forEach(t -> {
             String serviceId = t.getServiceId();
             if(ALL_SERVICE_ID.equals(serviceId)){
                 t.setServiceName(ALL_SERVICE_NAME);
             }else {
                 TenantDO tenantDO = serviceDOMap.get(serviceId);
                 if (tenantDO == null) {
                     log.error("AlarmGroupService the tenant {} is not found", serviceId);
                     return;
                 }
                 t.setServiceName(tenantDO.getName());
             }

             List<AlarmGroupAlarmItemDO> alarmGroupAlarmItemDOList = Optional.ofNullable(alarmGroupAlarmItemDOMap.get(t.getId())).orElse(new ArrayList<>());
             boolean hasInvalidAlarmItem = alarmGroupAlarmItemDOList.stream().anyMatch(k -> {
                 return !ALL_ALARM_ID.equals(k.getAlarmId()) && !alarmMap.containsKey(k.getAlarmId());
             });
             if(hasInvalidAlarmItem){
                 alarmGroupHandler.deleteInvalidAlarmItemList();
             }
             alarmGroupAlarmItemDOList = alarmGroupAlarmItemDOList.stream().filter(k -> {
                 return ALL_ALARM_ID.equals(k.getAlarmId()) || alarmMap.containsKey(k.getAlarmId());
             }).collect(Collectors.toList());
             alarmGroupAlarmItemDOList.forEach(v -> {
                 AlarmGroupAlarmItemOutput alarmGroupAlarmItemOutput = new AlarmGroupAlarmItemOutput();
                 BeanUtils.copyProperties(v, alarmGroupAlarmItemOutput);
                 if(ALL_ALARM_ID.equals(alarmGroupAlarmItemOutput.getAlarmId())){
                     alarmGroupAlarmItemOutput.setAlarmName(ALL_ALARM_NAME);
                 }else {
                     alarmGroupAlarmItemOutput.setAlarmName(alarmMap.get(alarmGroupAlarmItemOutput.getAlarmId()).getName());
                 }
                 t.getAlarmItemList().add(alarmGroupAlarmItemOutput);
             });
             List<AlarmGroupTagItemDO> alarmGroupTagItemDOList = Optional.ofNullable(alarmGroupTagItemDOMap.get(t.getId())).orElse(new ArrayList<>());
             alarmGroupTagItemDOList.forEach(v -> {
                 AlarmGroupTagItemOutput alarmGroupTagItemOutput = new AlarmGroupTagItemOutput();
                 BeanUtils.copyProperties(v, alarmGroupTagItemOutput);
                 t.getGroupTagItemList().add(alarmGroupTagItemOutput);
             });

         });
         return alarmGroupOutput;
     }

    @Transactional
    public ResponseObject showAlarmGroupList(PageQuery<AlarmGroupQuery> pageQuery){
        authService.checkAuthIgnoreTenant(pageQuery);
        String userId = pageQuery.getUserId();
        int pageIndex = pageQuery.getPageIndex();
        int pageSize = pageQuery.getPageSize();
        AlarmGroupQuery alarmGroupQuery = Optional.ofNullable(pageQuery.getQueryPara()).orElse(new AlarmGroupQuery());
        pageQuery.setQueryPara(alarmGroupQuery);
        Boolean isQueryServiceName = false;
        String queryServiceId = null;
        if(!StringUtils.isEmpty(alarmGroupQuery.getServiceName())){
            isQueryServiceName = true;
            if(ALL_SERVICE_NAME.equals(alarmGroupQuery.getServiceName())){
                queryServiceId = ALL_SERVICE_ID;
            }else {
                TenantDO queryService = tenantHandler.getTenantByName(alarmGroupQuery.getServiceName());
                Assert.isTrue(queryService != null, "The service in query condition is not existed");
                queryServiceId = queryService.getId();
            }

        }
        final Boolean isQueryServiceNameFinal = isQueryServiceName;
        final String queryServiceIdFinal = queryServiceId;

        List<AlarmGroupDO> alarmGroupDOList = alarmGroupHandler.findByLikeParam(alarmGroupQuery.getName());
        alarmGroupDOList = alarmGroupDOList.stream().filter(e -> Integer.valueOf(1).equals(e.getVisibility())).collect(Collectors.toList());
        List<String> alarmGroupIdList = alarmGroupDOList.stream().map(e -> e.getId()).collect(Collectors.toList());
        List<AlarmGroupServiceItemDO> alarmGroupServiceItemDOList = alarmGroupServiceItemHandler.findByAlarmGroupIdList(alarmGroupIdList);
        Map<String, List<AlarmGroupServiceItemDO>> alarmGroupServiceItemDOMap = alarmGroupServiceItemDOList.stream().collect(Collectors.groupingBy(AlarmGroupServiceItemDO::getAlarmGroupId, Collectors.toList()));

        List<AlarmGroupOutput> alarmGroupOutputList = new ArrayList<>();
        alarmGroupDOList.forEach(e -> {
            AlarmGroupOutput alarmGroupOutput = new AlarmGroupOutput();
            BeanUtils.copyProperties(e, alarmGroupOutput);
            List<AlarmGroupServiceItemDO> currentAlarmGroupServiceItemDOList = alarmGroupServiceItemDOMap.get(e.getId());
            List<AlarmGroupServiceItemOutput> currentAlarmGroupServiceItemOutputList = new ArrayList<>();
            if (!CollectionUtils.isEmpty(currentAlarmGroupServiceItemDOList)) {
                currentAlarmGroupServiceItemDOList.forEach(t -> {
                    AlarmGroupServiceItemOutput alarmGroupServiceItemOutput = new AlarmGroupServiceItemOutput();
                    BeanUtils.copyProperties(t, alarmGroupServiceItemOutput);
                    currentAlarmGroupServiceItemOutputList.add(alarmGroupServiceItemOutput);
                });
            }
            alarmGroupOutput.setServiceItemList(currentAlarmGroupServiceItemOutputList);
            alarmGroupOutputList.add(alarmGroupOutput);
        });


        List<AlarmGroupOutput> filterAlarmGroupOutputList = alarmGroupOutputList.stream().filter(e -> {
            List<String> checkServiceIdList = e.getServiceItemList().stream().map(t -> t.getServiceId()).collect(Collectors.toList());
            if(!alarmGroupUtilService.canOperateAlarmGroup(userId, checkServiceIdList)){
                return false;
            }

            if(!isQueryServiceNameFinal){
                return true;
            }

            if(!checkServiceIdList.contains(queryServiceIdFinal)){
                return false;
            }
            return true;

        }).collect(Collectors.toList());
        int total = filterAlarmGroupOutputList.size();
        int pageStart = (pageIndex-1)*pageSize;
        int pageEnd = pageStart + pageSize;
        if(pageStart > filterAlarmGroupOutputList.size()){
            Assert.isTrue(false, "the page index is too large");
        }
        if(pageEnd > filterAlarmGroupOutputList.size()){
            pageEnd = filterAlarmGroupOutputList.size();
        }

        List<AlarmGroupOutput> pageAlarmGroupOutputList = filterAlarmGroupOutputList.subList(pageStart,pageEnd);

        List<String> serviceItemIdList = new ArrayList<>();
        List<String> serviceIdList = new ArrayList<>();
        pageAlarmGroupOutputList.forEach(e -> {
            e.getServiceItemList().forEach(t -> {
                serviceItemIdList.add(t.getId());
                serviceIdList.add(t.getServiceId());
            });
        });
        Map<String, TenantDO> serviceDOMap = tenantHandler.listByIds(serviceIdList).stream().collect(Collectors.toMap(TenantDO::getId, e -> e));
        List<AlarmGroupAlarmItemDO> alarmGroupAlarmItemList = alarmGroupAlarmItemHandler.findByAlarmGroupServiceItemIdList(serviceItemIdList);
        List<String> alarmIdList = alarmGroupAlarmItemList.stream().map(e -> e.getAlarmId()).collect(Collectors.toList());
        List<IdAndName> alarmList = alarmDefinitionHandler.findByIdList(alarmIdList);
        Map<String, IdAndName> alarmMap = alarmList.stream().collect(Collectors.toMap(IdAndName::getId, e->e));

        Map<String, List<AlarmGroupAlarmItemDO>> alarmGroupAlarmItemDOMap = alarmGroupAlarmItemList.stream().collect(Collectors.groupingBy(AlarmGroupAlarmItemDO::getAlarmGroupServiceItemId, Collectors.toList()));
        Map<String, List<AlarmGroupTagItemDO>> alarmGroupTagItemDOMap = alarmGroupTagItemHandler.findByAlarmGroupServiceItemIdList(serviceItemIdList).stream().collect(Collectors.groupingBy(AlarmGroupTagItemDO::getAlarmGroupServiceItemId, Collectors.toList()));
        final BooleanWrap cleanInvalidAlarmItem = new BooleanWrap(false);
        pageAlarmGroupOutputList.forEach(e -> {
            e.getServiceItemList().forEach(t -> {
                String serviceId = t.getServiceId();
                if(ALL_SERVICE_ID.equals(serviceId)){
                    t.setServiceName(ALL_SERVICE_NAME);
                }else {
                    TenantDO tenantDO = serviceDOMap.get(serviceId);
                    if (tenantDO == null) {
                        log.error("AlarmGroupService the tenant {} is not found", serviceId);
                        return;
                    }
                    t.setServiceName(tenantDO.getName());
                }
                List<AlarmGroupAlarmItemDO> alarmGroupAlarmItemDOList = Optional.ofNullable(alarmGroupAlarmItemDOMap.get(t.getId())).orElse(new ArrayList<>());
                boolean hasInvalidAlarmItem = alarmGroupAlarmItemDOList.stream().anyMatch(k -> {
                    return !ALL_ALARM_ID.equals(k.getAlarmId()) && !alarmMap.containsKey(k.getAlarmId());
                });
                if(hasInvalidAlarmItem && !cleanInvalidAlarmItem.getValue()){
                    alarmGroupHandler.deleteInvalidAlarmItemList();
                    cleanInvalidAlarmItem.setValue(true);
                }
                alarmGroupAlarmItemDOList = alarmGroupAlarmItemDOList.stream().filter(k -> {
                    return ALL_ALARM_ID.equals(k.getAlarmId()) || alarmMap.containsKey(k.getAlarmId());
                }).collect(Collectors.toList());

                alarmGroupAlarmItemDOList.forEach(v -> {
                    AlarmGroupAlarmItemOutput alarmGroupAlarmItemOutput = new AlarmGroupAlarmItemOutput();
                    BeanUtils.copyProperties(v, alarmGroupAlarmItemOutput);
                    if(ALL_ALARM_ID.equals(alarmGroupAlarmItemOutput.getAlarmId())){
                        alarmGroupAlarmItemOutput.setAlarmName(ALL_ALARM_NAME);
                    }else {
                        alarmGroupAlarmItemOutput.setAlarmName(alarmMap.get(alarmGroupAlarmItemOutput.getAlarmId()).getName());
                    }
                    t.getAlarmItemList().add(alarmGroupAlarmItemOutput);
                });
                List<AlarmGroupTagItemDO> alarmGroupTagItemDOList = Optional.ofNullable(alarmGroupTagItemDOMap.get(t.getId())).orElse(new ArrayList<>());
                alarmGroupTagItemDOList.forEach(v -> {
                    AlarmGroupTagItemOutput alarmGroupTagItemOutput = new AlarmGroupTagItemOutput();
                    BeanUtils.copyProperties(v, alarmGroupTagItemOutput);
                    t.getGroupTagItemList().add(alarmGroupTagItemOutput);
                });

            });
        });
        ResponseObject responseObject = ResponseObject.success(pageQuery.getOperId(), new PageResult<>(total, pageAlarmGroupOutputList));
        return responseObject;
    }

    @Transactional
    public ResponseObject addAlarmGroupV2(AlarmGroupInput alarmGroupInput){
        authService.checkAuthIgnoreTenant(alarmGroupInput);
        alarmGroupUtilService.checkAddAlarmGroupParameter(alarmGroupInput, V2);
        alarmGroupUtilService.adjustOrderIndex(alarmGroupInput.getServiceItemList());
        AlarmGroupDO alarmGroupDO = buildAlarmGroupDO(alarmGroupInput);
        AddAlarmGroupResult addAlarmGroupResult = buildAlarmGroupResult(alarmGroupInput,alarmGroupDO.getId());
        insert2DB(alarmGroupDO, addAlarmGroupResult);
        return ResponseObject.success(alarmGroupDO.getId());
    }

    public AlarmGroupDO buildAlarmGroupDO(AlarmGroupInput alarmGroupInput) {
        AlarmGroupDO alarmGroupDO = new AlarmGroupDO();
        BeanUtils.copyProperties(alarmGroupInput, alarmGroupDO);
        alarmGroupDO.setId(IdUtils.generateId());
        String userName = userHandler.getNameById(alarmGroupInput.getUserId());
        alarmGroupDO.setCreator(userName);
        alarmGroupDO.setEditor(userName);
        return alarmGroupDO;
    }

    public AddAlarmGroupResult buildAlarmGroupResult(AlarmGroupInput alarmGroupInput, String alarmGroupId) {
        AddAlarmGroupResult addAlarmGroupResult = new AddAlarmGroupResult();
        List<AlarmGroupServiceItemDO> alarmGroupServiceItemDOList = new ArrayList<>();
        List<AlarmGroupAlarmItemDO> alarmGroupAlarmItemDOList = new ArrayList<>();
        List<AlarmGroupTagItemDO> alarmGroupTagItemDOList = new ArrayList<>();

        for(AlarmGroupServiceItemInput alarmGroupServiceItemInput: alarmGroupInput.getServiceItemList()){
            AlarmGroupServiceItemDO alarmGroupServiceItemDO = new AlarmGroupServiceItemDO();
            BeanUtils.copyProperties(alarmGroupServiceItemInput, alarmGroupServiceItemDO);
            alarmGroupServiceItemDO.setId(IdUtils.generateId());
            alarmGroupServiceItemDO.setAlarmGroupId(alarmGroupId);
            alarmGroupServiceItemDOList.add(alarmGroupServiceItemDO);

            for(AlarmGroupAlarmItemInput alarmGroupAlarmItemInput: alarmGroupServiceItemInput.getAlarmItemList()){
                AlarmGroupAlarmItemDO alarmGroupAlarmItemDO = new AlarmGroupAlarmItemDO();
                BeanUtils.copyProperties(alarmGroupAlarmItemInput, alarmGroupAlarmItemDO);
                alarmGroupAlarmItemDO.setAlarmGroupServiceItemId(alarmGroupServiceItemDO.getId());
                alarmGroupAlarmItemDO.setId(IdUtils.generateId());
                alarmGroupAlarmItemDOList.add(alarmGroupAlarmItemDO);
            }

            for(AlarmGroupTagItemInput alarmGroupTagItemInput: alarmGroupServiceItemInput.getGroupTagItemList()){
                AlarmGroupTagItemDO alarmGroupTagItemDO = new AlarmGroupTagItemDO();
                BeanUtils.copyProperties(alarmGroupTagItemInput, alarmGroupTagItemDO);
                alarmGroupTagItemDO.setAlarmGroupServiceItemId(alarmGroupServiceItemDO.getId());
                alarmGroupTagItemDO.setId(IdUtils.generateId());
                alarmGroupTagItemDOList.add(alarmGroupTagItemDO);
            }
        }
        addAlarmGroupResult.setAlarmGroupTagItemDOList(alarmGroupTagItemDOList);
        addAlarmGroupResult.setAlarmGroupAlarmItemDOList(alarmGroupAlarmItemDOList);
        addAlarmGroupResult.setAlarmGroupServiceItemDOList(alarmGroupServiceItemDOList);
        return addAlarmGroupResult;
    }

    @Transactional
    public void insert2DB(AlarmGroupDO alarmGroupDO, AddAlarmGroupResult addAlarmGroupResult) {
        alarmGroupHandler.insert(alarmGroupDO);
        alarmGroupServiceItemHandler.batchInsert(addAlarmGroupResult.getAlarmGroupServiceItemDOList());
        alarmGroupAlarmItemHandler.batchInsert(addAlarmGroupResult.getAlarmGroupAlarmItemDOList());
        alarmGroupTagItemHandler.batchInsert(addAlarmGroupResult.getAlarmGroupTagItemDOList());
    }

    @Transactional
    public ResponseObject addAlarmGroup(AlarmGroupInput alarmGroupInput){
        authService.checkAuthIgnoreTenant(alarmGroupInput);
        alarmGroupUtilService.checkAddAlarmGroupParameter(alarmGroupInput, V1);
        AlarmGroupDO alarmGroupDO = buildAlarmGroupDO(alarmGroupInput);
        AddAlarmGroupResult addAlarmGroupResult = buildAlarmGroupResult(alarmGroupInput,alarmGroupDO.getId());
        insert2DB(alarmGroupDO, addAlarmGroupResult);
        return ResponseObject.success(alarmGroupDO.getId());
    }

    @Transactional
    public ResponseObject updateAlarmGroupV2(AlarmGroupInput alarmGroupInput){
        return updateAlarmGroup(alarmGroupInput, V2);
    }

    @Transactional
    public ResponseObject updateAlarmGroup(AlarmGroupInput alarmGroupInput, String version){
        authService.checkAuthIgnoreTenant(alarmGroupInput);
        alarmGroupUtilService.checkUpdateAlarmGroupParameter(alarmGroupInput, version);
        alarmGroupUtilService.adjustOrderIndex(alarmGroupInput.getServiceItemList());
        String alarmGroupId = alarmGroupInput.getId();
        String userId = alarmGroupInput.getUserId();
        AlarmGroupOutput alarmGroupOutput = getAlarmGroupById(alarmGroupId, userId);
        AlarmGroupDO alarmGroupDO = alarmGroupHandler.findById(alarmGroupId);
        BeanUtils.copyProperties(alarmGroupInput, alarmGroupDO);
        String userName = userHandler.getNameById(userId);
        alarmGroupDO.setEditor(userName);

        List<AlarmGroupServiceItemDO> addAlarmGroupServiceItemDOList = new ArrayList<>();
        List<AlarmGroupServiceItemDO> updateAlarmGroupServiceItemDOList = new ArrayList<>();
        List<String> deleteAlarmGroupServiceItemIdList = new ArrayList<>();

        List<AlarmGroupAlarmItemDO> addAlarmGroupAlarmItemDOList = new ArrayList<>();
        List<AlarmGroupAlarmItemDO> updateAlarmGroupAlarmItemDOList = new ArrayList<>();
        List<String> deleteAlarmGroupAlarmItemIdList = new ArrayList<>();

        List<AlarmGroupTagItemDO> addAlarmGroupTagItemDOList = new ArrayList<>();
        List<AlarmGroupTagItemDO> updateAlarmGroupTagItemDOList = new ArrayList<>();
        List<String> deleteAlarmGroupTagItemIdList = new ArrayList<>();

        alarmGroupInput.getServiceItemList().forEach(e -> {
            if(StringUtils.isEmpty(e.getId())){
                // add service
                AlarmGroupServiceItemDO alarmGroupServiceItemDO = new AlarmGroupServiceItemDO();
                BeanUtils.copyProperties(e, alarmGroupServiceItemDO);
                String serviceItemId = IdUtils.generateId();
                alarmGroupServiceItemDO.setId(serviceItemId);
                alarmGroupServiceItemDO.setAlarmGroupId(alarmGroupId);
                addAlarmGroupServiceItemDOList.add(alarmGroupServiceItemDO);

                e.getAlarmItemList().forEach(t -> {
                    AlarmGroupAlarmItemDO alarmGroupAlarmItemDO = new AlarmGroupAlarmItemDO();
                    BeanUtils.copyProperties(t, alarmGroupAlarmItemDO);
                    alarmGroupAlarmItemDO.setId(IdUtils.generateId());
                    alarmGroupAlarmItemDO.setAlarmGroupServiceItemId(serviceItemId);
                    addAlarmGroupAlarmItemDOList.add(alarmGroupAlarmItemDO);

                });

                e.getGroupTagItemList().forEach(t -> {
                    AlarmGroupTagItemDO alarmGroupTagItemDO = new AlarmGroupTagItemDO();
                    BeanUtils.copyProperties(t, alarmGroupTagItemDO);
                    alarmGroupTagItemDO.setId(IdUtils.generateId());
                    alarmGroupTagItemDO.setAlarmGroupServiceItemId(serviceItemId);
                    addAlarmGroupTagItemDOList.add(alarmGroupTagItemDO);
                });
            } else {
                // update service
                AlarmGroupServiceItemDO alarmGroupServiceItemDO = new AlarmGroupServiceItemDO();
                BeanUtils.copyProperties(e, alarmGroupServiceItemDO);
                alarmGroupServiceItemDO.setAlarmGroupId(alarmGroupId);
                updateAlarmGroupServiceItemDOList.add(alarmGroupServiceItemDO);

                // process alarmItem
                List<AlarmGroupAlarmItemInput> newAlarmGroupAlarmItemInputList = e.getAlarmItemList();
                Optional<AlarmGroupServiceItemOutput> alarmGroupServiceItemOutputOptional = alarmGroupOutput.getServiceItemList().stream().filter(t -> t.getId().equals(e.getId())).findFirst();
                if(!alarmGroupServiceItemOutputOptional.isPresent()){
                    Assert.isTrue(false, "service [" + e.getServiceName() + "] is not exist in AlarmGroup");
                }
                AlarmGroupServiceItemOutput alarmGroupServiceItemOutput = alarmGroupServiceItemOutputOptional.get();
                List<AlarmGroupAlarmItemOutput> oldAlarmGroupAlarmItemOutputList = alarmGroupServiceItemOutput.getAlarmItemList();

                newAlarmGroupAlarmItemInputList.forEach(t -> {
                    if(StringUtils.isEmpty(t.getId())){
                        AlarmGroupAlarmItemDO alarmGroupAlarmItemDO = new AlarmGroupAlarmItemDO();
                        BeanUtils.copyProperties(t, alarmGroupAlarmItemDO);
                        alarmGroupAlarmItemDO.setId(IdUtils.generateId());
                        alarmGroupAlarmItemDO.setAlarmGroupServiceItemId(alarmGroupServiceItemDO.getId());
                        addAlarmGroupAlarmItemDOList.add(alarmGroupAlarmItemDO);
                    } else {
                        AlarmGroupAlarmItemDO alarmGroupAlarmItemDO = new AlarmGroupAlarmItemDO();
                        BeanUtils.copyProperties(t, alarmGroupAlarmItemDO);
                        alarmGroupAlarmItemDO.setAlarmGroupServiceItemId(alarmGroupServiceItemDO.getId());
                        updateAlarmGroupAlarmItemDOList.add(alarmGroupAlarmItemDO);
                    }
                });
                Set<String> newAlarmItemIdList = newAlarmGroupAlarmItemInputList.stream().filter(t -> !StringUtils.isEmpty(t.getId())).map(t -> t.getId()).collect(Collectors.toSet());
                List<String> needDeleteAlarmItemIdList = oldAlarmGroupAlarmItemOutputList.stream().filter(t -> !newAlarmItemIdList.contains(t.getId())).map(t -> t.getId()).collect(Collectors.toList());
                deleteAlarmGroupAlarmItemIdList.addAll(needDeleteAlarmItemIdList);

                // process tagItem
                List<AlarmGroupTagItemInput> newAlarmGroupTagItemInputList = e.getGroupTagItemList();
                List<AlarmGroupTagItemOutput> oldAlarmGroupTagItemOutputList = alarmGroupServiceItemOutput.getGroupTagItemList();

                newAlarmGroupTagItemInputList.forEach(t -> {
                    if(StringUtils.isEmpty(t.getId())){
                        AlarmGroupTagItemDO alarmGroupTagItemDO = new AlarmGroupTagItemDO();
                        BeanUtils.copyProperties(t, alarmGroupTagItemDO);
                        alarmGroupTagItemDO.setId(IdUtils.generateId());
                        alarmGroupTagItemDO.setAlarmGroupServiceItemId(alarmGroupServiceItemDO.getId());
                        addAlarmGroupTagItemDOList.add(alarmGroupTagItemDO);
                    } else {
                        AlarmGroupTagItemDO alarmGroupTagItemDO = new AlarmGroupTagItemDO();
                        BeanUtils.copyProperties(t, alarmGroupTagItemDO);
                        alarmGroupTagItemDO.setAlarmGroupServiceItemId(alarmGroupServiceItemDO.getId());
                        updateAlarmGroupTagItemDOList.add(alarmGroupTagItemDO);
                    }
                });
                Set<String> newTagItemIdList = newAlarmGroupTagItemInputList.stream().filter(t -> !StringUtils.isEmpty(t.getId())).map(t -> t.getId()).collect(Collectors.toSet());
                List<String> needDeleteTagItemIdList = oldAlarmGroupTagItemOutputList.stream().filter(t -> !newTagItemIdList.contains(t.getId())).map(t -> t.getId()).collect(Collectors.toList());
                deleteAlarmGroupTagItemIdList.addAll(needDeleteTagItemIdList);
            }
        });

        // delete service
        Set<String> newServiceItemIdSet = alarmGroupInput.getServiceItemList().stream().filter(t -> !StringUtils.isEmpty(t.getId())).map(t -> t.getId()).collect(Collectors.toSet());
        List<AlarmGroupServiceItemOutput> oldServiceItemList = alarmGroupOutput.getServiceItemList();
        List<AlarmGroupServiceItemOutput> needDeleteServiceItemList = oldServiceItemList.stream().filter(t -> !newServiceItemIdSet.contains(t.getId())).collect(Collectors.toList());
        List<String> needDeleteServiceItemIdList = needDeleteServiceItemList.stream().map(t -> t.getId()).collect(Collectors.toList());
        deleteAlarmGroupServiceItemIdList.addAll(needDeleteServiceItemIdList);
        needDeleteServiceItemList.forEach(t -> {
            List<String> needDeleteAlarmItemIdList0 = t.getAlarmItemList().stream().map(v -> v.getId()).collect(Collectors.toList());
            deleteAlarmGroupAlarmItemIdList.addAll(needDeleteAlarmItemIdList0);
            List<String> needDeleteTagItemIdList0 = t.getGroupTagItemList().stream().map(v -> v.getId()).collect(Collectors.toList());
            deleteAlarmGroupTagItemIdList.addAll(needDeleteTagItemIdList0);
        });

        alarmGroupHandler.update(alarmGroupDO);

        alarmGroupServiceItemHandler.batchInsert(addAlarmGroupServiceItemDOList);
        alarmGroupServiceItemHandler.batchUpdate(updateAlarmGroupServiceItemDOList);
        alarmGroupServiceItemHandler.batchDelete(deleteAlarmGroupServiceItemIdList);

        alarmGroupAlarmItemHandler.batchInsert(addAlarmGroupAlarmItemDOList);
        alarmGroupAlarmItemHandler.batchUpdate(updateAlarmGroupAlarmItemDOList);
        alarmGroupAlarmItemHandler.batchDelete(deleteAlarmGroupAlarmItemIdList);

        alarmGroupTagItemHandler.batchInsert(addAlarmGroupTagItemDOList);
        alarmGroupTagItemHandler.batchUpdate(updateAlarmGroupTagItemDOList);
        alarmGroupTagItemHandler.batchDelete(deleteAlarmGroupTagItemIdList);

        ResponseObject responseObject = ResponseObject.success(alarmGroupDO.getId());
        return responseObject;
    }

    @Transactional
    public ResponseObject delAlarmGroupById(AlarmGroupIdQuery alarmGroupIdQuery) {
        authService.checkAuthIgnoreTenant(alarmGroupIdQuery);
        String userId = alarmGroupIdQuery.getUserId();
        String alarmGroupId = alarmGroupIdQuery.getId();
        AlarmGroupDO alarmGroupDO = alarmGroupHandler.findById(alarmGroupId);
        if (alarmGroupDO == null) {
            Assert.isTrue(false, "The AlarmGroup is not exist");
        }

        List<AlarmSilenceDO> alarmSilenceDOList = alarmSilenceHandler.findByAlarmGroupId(alarmGroupId);
        return ResponseObject.success(alarmGroupServiceHandler.delAlarmGroup(alarmSilenceDOList, alarmGroupDO, userId));
    }


    public ResponseObject listService(AlarmGroupServiceQuery alarmGroupServiceQuery){
        authService.checkAuthIgnoreTenant(alarmGroupServiceQuery);
        String userId = alarmGroupServiceQuery.getUserId();

        List<TenantDO> tenantDOList = authHandler.getServiceUserCanOperate(userId);

        List<AlarmGroupSimpleServiceOutput> serviceList = new ArrayList<>();
        tenantDOList.stream().forEach(e -> {
            AlarmGroupSimpleServiceOutput alarmGroupSimpleServiceOutput = new AlarmGroupSimpleServiceOutput();
            BeanUtils.copyProperties(e, alarmGroupSimpleServiceOutput);
            serviceList.add(alarmGroupSimpleServiceOutput);
        });

        if(alarmGroupParaService.getAllowAllService() && authHandler.canCrossAndOperate(userId)){
            AlarmGroupSimpleServiceOutput allService = new AlarmGroupSimpleServiceOutput();
            allService.setId(ALL_SERVICE_ID);
            allService.setName(ALL_SERVICE_NAME);
            serviceList.add(0,allService);
        }

        List<AlarmGroupSimpleServiceOutput> finalServiceList = serviceList;

        if(!StringUtils.isEmpty(alarmGroupServiceQuery.getServiceName())){
            String serviceName = alarmGroupServiceQuery.getServiceName().toLowerCase();
            finalServiceList = finalServiceList.stream()
                    .filter(e -> e.getName().toLowerCase().contains(serviceName))
                    .collect(Collectors.toList());
        }
        return ResponseObject.success(finalServiceList);
    }

    public ResponseObject listAlarm(AlarmGroupAlarmQuery alarmGroupAlarmQuery){
        authService.checkAuthIgnoreTenant(alarmGroupAlarmQuery);
        alarmGroupAlarmQuery.check();
        String userId = alarmGroupAlarmQuery.getUserId();
        List<AlarmGroupSimpleAlarmOutput> alarmList = new ArrayList<>();
        if(ALL_SERVICE_NAME.equals(alarmGroupAlarmQuery.getServiceName())){
            if(!authHandler.canCrossAndOperate(userId)){
                Assert.isTrue(false, "this user do not have ALL_SERVICE permission");
            }
            if(StringUtils.isEmpty(alarmGroupAlarmQuery.getAlarmName()) || ALL_ALARM_NAME.contains(alarmGroupAlarmQuery.getAlarmName())){
                AlarmGroupSimpleAlarmOutput alarmGroupSimpleAlarmOutput = new AlarmGroupSimpleAlarmOutput();
                alarmGroupSimpleAlarmOutput.setId(ALL_ALARM_ID);
                alarmGroupSimpleAlarmOutput.setName(ALL_ALARM_NAME);
                alarmList.add(alarmGroupSimpleAlarmOutput);
            }
            ResponseObject responseObject = ResponseObject.success(alarmList);
            return responseObject;
        }

        List<TenantDO> tenantDOList = authHandler.getServiceUserCanOperate(userId);
        Boolean hasPermission = tenantDOList.stream().anyMatch(e -> e.getName().equals(alarmGroupAlarmQuery.getServiceName()));
        Assert.isTrue(hasPermission, "the user do not have the permission of this service " + alarmGroupAlarmQuery.getServiceName());
        TenantDO tenantDO = tenantDOList.stream().filter(e -> e.getName().equals(alarmGroupAlarmQuery.getServiceName())).findFirst().get();
        List<AlarmDefinitionDO> alarmDefinitionDOList = alarmDefinitionV2Dao.findByNameLike(tenantDO.getId(), alarmGroupAlarmQuery.getAlarmName());
        alarmDefinitionDOList.forEach(e -> {
            AlarmGroupSimpleAlarmOutput alarmGroupSimpleAlarmOutput = new AlarmGroupSimpleAlarmOutput();
            BeanUtils.copyProperties(e, alarmGroupSimpleAlarmOutput);
            alarmList.add(alarmGroupSimpleAlarmOutput);
        });

        if(StringUtils.isEmpty(alarmGroupAlarmQuery.getAlarmName()) || ALL_ALARM_NAME.contains(alarmGroupAlarmQuery.getAlarmName())){
            AlarmGroupSimpleAlarmOutput alarmGroupSimpleAlarmOutput = new AlarmGroupSimpleAlarmOutput();
            alarmGroupSimpleAlarmOutput.setId(ALL_ALARM_ID);
            alarmGroupSimpleAlarmOutput.setName(ALL_ALARM_NAME);
            alarmList.add(0, alarmGroupSimpleAlarmOutput);
        }

        ResponseObject responseObject = ResponseObject.success(alarmList);
        return responseObject;
    }

     public ResponseObject listTagAndFiled(AlarmGroupTagQuery alarmGroupTagQuery) {
        authService.checkAuthIgnoreTenant(alarmGroupTagQuery);
        validateRequest(alarmGroupTagQuery);
        String userId = alarmGroupTagQuery.getUserId();
        String serviceName = alarmGroupTagQuery.getServiceName();
        try {
            if (ALL_SERVICE_NAME.equals(serviceName)) {
                return ResponseObject.fail("ALL_SERVICE operations are not supported");
            }

            TenantDO tenant = getTenantWithPermissionCheck(serviceName, userId);
            List<MetricsDO> metrics = getMetricsByAlarmNames(alarmGroupTagQuery.getAlarmNameList(), tenant.getId());
            Set<FieldOrTag> fieldAndTagSet = new LinkedHashSet<>();
            for (MetricsDO metricsDO : metrics) {
                List<FieldOrTag> fieldOrTags = metricsHandler.getTagAndField(metricsDO.getId());
                fieldAndTagSet.addAll(fieldOrTags);
            }
            List<FieldOrTag> fieldAndTagList = new ArrayList<>(fieldAndTagSet);
            return ResponseObject.success(fieldAndTagList);
        } catch (Exception e) {
            log.error("Failed to get field and tag info for service: {}, user: {}", serviceName, userId, e);
            throw new RuntimeException("Failed to get field and tag info for service: " + serviceName + ", error: "+ e.getMessage());
        }
    }

    private void validateRequest(AlarmGroupTagQuery query) {
        Assert.isTrue(!StringUtils.isEmpty(query.getServiceName()), "Service name cannot be empty");
        Assert.notEmpty(query.getAlarmNameList(), "Alarm name list cannot be empty");
        Assert.isTrue(!StringUtils.isEmpty(query.getUserId()), "User ID cannot be empty");
    }

    private TenantDO getTenantWithPermissionCheck(String serviceName, String userId) {
        TenantDO tenant = Optional.ofNullable(tenantHandler.getTenantByName(serviceName))
                .orElseThrow(() -> new IllegalArgumentException("Service '" + serviceName + "' does not exist"));

        boolean hasPermission = authHandler.getServiceUserCanOperate(userId)
                .stream()
                .anyMatch(t -> Objects.equals(t.getName(), serviceName));

        if (!hasPermission) {
            throw new IllegalArgumentException("User does not have permission for service: " + serviceName);
        }

        return tenant;
    }

    private List<MetricsDO> getMetricsByAlarmNames(List<String> alarmNames, String tenantId) {
        List<String> metricIds = new ArrayList<>();

        if (alarmNames.contains(ALL_ALARM_NAME)) {
            alarmDefinitionDao.getAlarmNameAndMetricIdByTenant(tenantId)
                    .forEach(alarm -> metricIds.add(alarm.getMetricId()));
        } else {
            metricIds.addAll(alarmDefinitionDao.findByAlarmNamesAndTenantId(alarmNames, tenantId));
        }
        return metricIds.isEmpty() ? Collections.emptyList() : metricsHandler.listMetricsByIds(metricIds);
    }

    public ResponseObject listGroupTagV2(AlarmGroupTagQuery alarmGroupTagQuery) {
        authService.checkAuthIgnoreTenant(alarmGroupTagQuery);
        String userId = alarmGroupTagQuery.getUserId();
        if (ALL_SERVICE_NAME.equals(alarmGroupTagQuery.getServiceName())) {
            List<String> allServiceTagList = alarmGroupParaService.getAllServiceTagList();
            return ResponseObject.success(allServiceTagList);
        }
        TenantDO tenantDO = tenantHandler.getTenantByName(alarmGroupTagQuery.getServiceName());
        Assert.notNull(tenantDO, "service is not exist");
        List<TenantDO> tenantDOList = authHandler.getServiceUserCanOperate(userId);
        Boolean hasPermission = tenantDOList.stream().anyMatch(e -> e.getName().equals(alarmGroupTagQuery.getServiceName()));
        Assert.isTrue(hasPermission, "the user do not have the permission of this service " + alarmGroupTagQuery.getServiceName());

        List<String> alarmNameList = alarmGroupTagQuery.getAlarmNameList();
        List<MetricsDO> list = new ArrayList<>();
        if (alarmNameList.contains(ALL_ALARM_NAME)) {
            List<String> metricIds = new ArrayList<>();
            alarmDefinitionDao.getAlarmNameAndMetricIdByTenant(tenantDO.getId()).forEach(alarmNameMetricId -> {
                metricIds.add(alarmNameMetricId.getMetricId());
            });
            list = metricsHandler.listMetricsByIds(metricIds);
        } else {
            list = metricsHandler.listMetricsByIds(alarmDefinitionDao.findByAlarmNamesAndTenantId(alarmGroupTagQuery.getAlarmNameList(), tenantDO.getId()));
        }
        List<String> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(list)) {
            return ResponseObject.success(result);
        }
        Optional.ofNullable(list).orElse(Collections.emptyList()).forEach(m -> {
            String[] tag = m.getTagNames().split(",");
            result.addAll(Arrays.asList(tag));
        });
        return ResponseObject.success(result.stream().distinct().collect(Collectors.toList()));
    }

    public ResponseObject listGroupTag(AlarmGroupTagQuery alarmGroupTagQuery){
        authService.checkAuthIgnoreTenant(alarmGroupTagQuery);
        String userId = alarmGroupTagQuery.getUserId();
        List<String> tagList = new ArrayList<>();
        if(ALL_SERVICE_NAME.equals(alarmGroupTagQuery.getServiceName())){
            List<String> allServiceTagList = alarmGroupParaService.getAllServiceTagList();
            ResponseObject responseObject = ResponseObject.success(allServiceTagList);
            return responseObject;
            // Assert.isTrue(false, "not support listGroupTag for ALL_SERVICE");
        }

        TenantDO tenantDO = tenantHandler.getTenantByName(alarmGroupTagQuery.getServiceName());
        Assert.notNull(tenantDO, "service is not exist");

        List<TenantDO> tenantDOList = authHandler.getServiceUserCanOperate(userId);
        Boolean hasPermission = tenantDOList.stream().anyMatch(e -> e.getName().equals(alarmGroupTagQuery.getServiceName()));
        Assert.isTrue(hasPermission, "the user do not have the permission of this service " + alarmGroupTagQuery.getServiceName());




        List<MetricsDO> metricsDOS = metricsHandler.getMetricsByTenant(tenantDO.getId());
        List<String> result = new ArrayList<>();
        Optional.ofNullable(metricsDOS).orElse(Collections.emptyList()).forEach(m -> {
            String[] tag = m.getTagNames().split(",");
            result.addAll(Arrays.asList(tag));
        });

        List<String> allowTagList = alarmGroupParaService.getAllowTagList();
        List<String> noLimitServiceList = alarmGroupParaService.getNoLimitServiceList();
        if (noLimitServiceList.contains(tenantDO.getName())) {
            tagList = result.stream().distinct().collect(Collectors.toList());
        } else {
            tagList = result.stream()
                    .distinct()
                    .filter(tag -> allowTagList.contains(tag))
                    .collect(Collectors.toList());
        }

        ResponseObject responseObject = ResponseObject.success(tagList);
        return responseObject;
    }



    public ResponseObject showAlarmGroupSimpleList(AlarmGroupNameQuery alarmGroupNameQuery){
        authService.checkAuthIgnoreTenant(alarmGroupNameQuery);
        String userId = alarmGroupNameQuery.getUserId();
        List<AlarmGroupDO> alarmGroupDOList = alarmGroupHandler.findByLikeParam(alarmGroupNameQuery.getName());
        alarmGroupDOList = alarmGroupDOList.stream().filter(e -> Integer.valueOf(1).equals(e.getVisibility())).collect(Collectors.toList());
        List<String> alarmGroupIdList = alarmGroupDOList.stream().map(e -> e.getId()).collect(Collectors.toList());
        List<AlarmGroupServiceItemDO> alarmGroupServiceItemDOList = alarmGroupServiceItemHandler.findByAlarmGroupIdList(alarmGroupIdList);
        Map<String, List<AlarmGroupServiceItemDO>> alarmGroupServiceItemDOMap = alarmGroupServiceItemDOList.stream().collect(Collectors.groupingBy(AlarmGroupServiceItemDO::getAlarmGroupId, Collectors.toList()));

        List<AlarmGroupOutput> alarmGroupOutputList = new ArrayList<>();
        alarmGroupDOList.forEach(e -> {
            AlarmGroupOutput alarmGroupOutput = new AlarmGroupOutput();
            BeanUtils.copyProperties(e, alarmGroupOutput);
            List<AlarmGroupServiceItemDO> currentAlarmGroupServiceItemDOList = alarmGroupServiceItemDOMap.get(e.getId());
            List<AlarmGroupServiceItemOutput> currentAlarmGroupServiceItemOutputList = new ArrayList<>();
            if(!CollectionUtils.isEmpty(currentAlarmGroupServiceItemDOList)){
                currentAlarmGroupServiceItemDOList.forEach(t -> {
                    AlarmGroupServiceItemOutput alarmGroupServiceItemOutput = new AlarmGroupServiceItemOutput();
                    BeanUtils.copyProperties(t, alarmGroupServiceItemOutput);
                    currentAlarmGroupServiceItemOutputList.add(alarmGroupServiceItemOutput);
                });
            }
            alarmGroupOutput.setServiceItemList(currentAlarmGroupServiceItemOutputList);
            alarmGroupOutputList.add(alarmGroupOutput);
        });

        List<AlarmGroupOutput> filterAlarmGroupOutputList = alarmGroupOutputList.stream().filter(e -> {
            List<String> checkServiceIdList = e.getServiceItemList().stream().map(t -> t.getServiceId()).collect(Collectors.toList());
            return alarmGroupUtilService.canOperateAlarmGroup(userId, checkServiceIdList);
        }).collect(Collectors.toList());

        List<AlarmGroupSimpleOutput> alarmGroupSimpleOutputList = new ArrayList<>();
        filterAlarmGroupOutputList.stream().forEach(e -> {
            AlarmGroupSimpleOutput alarmGroupSimpleOutput = new AlarmGroupSimpleOutput();
            BeanUtils.copyProperties(e, alarmGroupSimpleOutput);
            alarmGroupSimpleOutputList.add(alarmGroupSimpleOutput);
        });
        ResponseObject responseObject = ResponseObject.success(alarmGroupSimpleOutputList);
        return responseObject;
    }

    @Transactional
    public ResponseObject addAlarmList(AddAlarmList2Group addAlarmList2Group) {
        authService.checkAuthIgnoreTenant(addAlarmList2Group);
        alarmGroupUtilService.checkAddAlarmListParameter(addAlarmList2Group);
        List<String> alarmIdList = addAlarmList2Group.getAlarmIdList();
        String alarmGroupId = addAlarmList2Group.getAlarmGroupId();
        for(String alarmId: alarmIdList){
            if(!alarmGroupUtilService.isAlarmInAlarmGroup(alarmId, alarmGroupId)){
                alarmGroupUtilService.addAlarm2AlarmGroup(alarmId, alarmGroupId);
            }

        }
        ResponseObject responseObject = ResponseObject.success(null);
        return responseObject;
    }

    @Data
    @AllArgsConstructor
    private static class BooleanWrap {
         private Boolean value;
    }

    public ResponseObject checkExist(AlarmGroupNameQuery alarmGroupNameQuery) {
        alarmGroupNameQuery.check();
        authService.checkAuthFromAuthInterceptorIgnoreTenant();
        AlarmGroupDO alarmGroupDO = alarmGroupHandler.findByName(alarmGroupNameQuery.getName());
        if (alarmGroupDO == null) {
            return ResponseObject.success(false);
        } else {
            return ResponseObject.success(true);
        }
    }

    public ResponseObject getAlarmGroupByName(AlarmGroupNameQuery alarmGroupNameQuery) {
        alarmGroupNameQuery.check();
        authService.checkAuthFromAuthInterceptorIgnoreTenant();
        AlarmGroupDO alarmGroupDO = alarmGroupHandler.findByName(alarmGroupNameQuery.getName());
        if (null == alarmGroupDO) {
            return ResponseObject.fail("The alarm group is not existed! ");
        }
        AlarmGroupOutput alarmGroupOutput = getAlarmGroupById(alarmGroupDO.getId(), AuthInterceptor.getUserId());
        return ResponseObject.success(alarmGroupOutput);
    }

    @Transactional
    public ResponseObject addOutAlarmGroup(AlarmGroupInput alarmGroupInput) {
        alarmGroupUtilService.checkOutAddAlarmGroupParameter(alarmGroupInput);
        authService.checkAuthFromAuthInterceptorIgnoreTenant();

        AlarmGroupDO newAlarmGroupDO = new AlarmGroupDO();
        BeanUtils.copyProperties(alarmGroupInput, newAlarmGroupDO);
        newAlarmGroupDO.setId(IdUtils.generateId());
        String userName = userHandler.getNameById(AuthInterceptor.getUserId());
        newAlarmGroupDO.setEditor(userName);
        newAlarmGroupDO.setCreator(userName);

        List<AlarmGroupServiceItemDO> alarmGroupServiceItemDOList = new ArrayList<>();
        List<AlarmGroupAlarmItemDO> alarmGroupAlarmItemDOList = new ArrayList<>();
        List<AlarmGroupTagItemDO> alarmGroupTagItemDOList = new ArrayList<>();

        List<TenantDO> allTenantList = tenantHandler.getAllTenantFromCache();
        Map<String, String> allTenantMap = allTenantList.stream().collect(Collectors.toMap(TenantDO::getName, TenantDO::getId));

        for (AlarmGroupServiceItemInput outAlarmGroupServiceItemInput : alarmGroupInput.getServiceItemList()) {
            outAlarmGroupServiceItemInput.check();
            String serviceId = allTenantMap.get(outAlarmGroupServiceItemInput.getServiceName());
            AlarmGroupServiceItemDO alarmGroupServiceItemDO = new AlarmGroupServiceItemDO();
            BeanUtils.copyProperties(outAlarmGroupServiceItemInput, alarmGroupServiceItemDO);
            alarmGroupServiceItemDO.setId(IdUtils.generateId());
            alarmGroupServiceItemDO.setAlarmGroupId(newAlarmGroupDO.getId());
            alarmGroupServiceItemDO.setServiceId(serviceId);
            alarmGroupServiceItemDOList.add(alarmGroupServiceItemDO);
            for (AlarmGroupAlarmItemInput alarmGroupAlarmItemInput : outAlarmGroupServiceItemInput.getAlarmItemList()) {
                if (ALL_ALARM_NAME.equals(alarmGroupAlarmItemInput.getAlarmName()) && ALL_ALARM_ID.equals(alarmGroupAlarmItemInput.getAlarmId())) {
                    AlarmGroupAlarmItemDO alarmGroupAlarmItemDO = new AlarmGroupAlarmItemDO();
                    BeanUtils.copyProperties(alarmGroupAlarmItemInput, alarmGroupAlarmItemDO);
                    alarmGroupAlarmItemDO.setAlarmGroupServiceItemId(alarmGroupServiceItemDO.getId());
                    alarmGroupAlarmItemDO.setId(IdUtils.generateId());
                    alarmGroupAlarmItemDOList.add(alarmGroupAlarmItemDO);
                } else {
                    Optional<AlarmDefinition> alarmDefinitionDO = alarmDefinitionDao.findByNameAndTenantId(alarmGroupAlarmItemInput.getAlarmName(), serviceId);
                    alarmDefinitionDO.ifPresent(alarmDefinition -> buildAlarmGroupAlarmItemDO(alarmGroupAlarmItemInput, alarmGroupAlarmItemDOList, alarmGroupServiceItemDO, alarmDefinition));
                }
            }

            for (AlarmGroupTagItemInput alarmGroupTagItemInput : outAlarmGroupServiceItemInput.getGroupTagItemList()) {
                builderAlarmGroupTagItemDO(alarmGroupTagItemInput, alarmGroupTagItemDOList, alarmGroupServiceItemDO);
            }
        }

        alarmGroupHandler.insert(newAlarmGroupDO);
        alarmGroupServiceItemHandler.batchInsert(alarmGroupServiceItemDOList);
        alarmGroupAlarmItemHandler.batchInsert(alarmGroupAlarmItemDOList);
        alarmGroupTagItemHandler.batchInsert(alarmGroupTagItemDOList);
        ResponseObject responseObject = ResponseObject.success(newAlarmGroupDO.getId());
        return responseObject;
    }

    public void builderAlarmGroupTagItemDO(AlarmGroupTagItemInput alarmGroupTagItemInput,
                                           List<AlarmGroupTagItemDO> alarmGroupTagItemDOList,
                                           AlarmGroupServiceItemDO alarmGroupServiceItemDO) {
        AlarmGroupTagItemDO alarmGroupTagItemDO = new AlarmGroupTagItemDO();
        BeanUtils.copyProperties(alarmGroupTagItemInput, alarmGroupTagItemDO);
        alarmGroupTagItemDO.setAlarmGroupServiceItemId(alarmGroupServiceItemDO.getId());
        alarmGroupTagItemDO.setId(IdUtils.generateId());
        alarmGroupTagItemDO.setConditionType(alarmGroupTagItemInput.getConditionType() != null ? alarmGroupTagItemInput.getConditionType() : AlarmGroupConditionTypeEnum.TAG_CONDITION.getValue());
        alarmGroupTagItemDOList.add(alarmGroupTagItemDO);
    }

    public void buildAlarmGroupAlarmItemDO(AlarmGroupAlarmItemInput alarmGroupAlarmItemInput,
                                           List<AlarmGroupAlarmItemDO> alarmGroupAlarmItemDOList,
                                           AlarmGroupServiceItemDO alarmGroupServiceItemDO,
                                           AlarmDefinition alarmDefinitionDO) {
        AlarmGroupAlarmItemDO alarmGroupAlarmItemDO = new AlarmGroupAlarmItemDO();
        BeanUtils.copyProperties(alarmGroupAlarmItemInput, alarmGroupAlarmItemDO);
        alarmGroupAlarmItemDO.setAlarmGroupServiceItemId(alarmGroupServiceItemDO.getId());
        alarmGroupAlarmItemDO.setId(IdUtils.generateId());
        alarmGroupAlarmItemDO.setAlarmId(alarmDefinitionDO.getId());
        alarmGroupAlarmItemDOList.add(alarmGroupAlarmItemDO);
    }

    @Transactional
    public ResponseObject updateOutAlarmGroup(AlarmGroupInput alarmGroupInput) {
        alarmGroupUtilService.checkOutUpdateAlarmGroupParameter(alarmGroupInput);
        authService.checkAuthFromAuthInterceptorIgnoreTenant();
        String userId = AuthInterceptor.getUserId();
        AlarmGroupDO alarmGroupDO = alarmGroupHandler.findByName(alarmGroupInput.getName());
        if (null == alarmGroupDO) {
            return ResponseObject.fail("The alarm group is not exist! ");
        }
        AlarmGroupOutput alarmGroupOutput = getAlarmGroupById(alarmGroupDO.getId(), userId);
        alarmGroupServiceHandler.updateAlarmGroupCore(alarmGroupInput, alarmGroupOutput, alarmGroupDO);
        return ResponseObject.success(alarmGroupDO.getId());
    }

    @Transactional
    public ResponseObject delAlarmGroupByName(AlarmGroupInput alarmGroupInput) {
        alarmGroupInput.check();
        authService.checkAuthFromAuthInterceptorIgnoreTenant();

        String userId = AuthInterceptor.getUserId();
        AlarmGroupDO alarmGroupDO = alarmGroupHandler.findByName(alarmGroupInput.getName());
        if (null == alarmGroupDO) {
            return ResponseObject.fail("The alarm group is not exist! ");
        }
        List<AlarmSilenceDO> alarmSilenceDOList = alarmSilenceHandler.findByAlarmGroupId(alarmGroupDO.getId());
        return ResponseObject.success(alarmGroupServiceHandler.delAlarmGroup(alarmSilenceDOList, alarmGroupDO, userId));
    }
}
