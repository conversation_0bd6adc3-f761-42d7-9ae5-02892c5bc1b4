package us.zoom.cube.site.core.parser.process.core.processor;

import us.zoom.cube.lib.hub.BaseProcessorCfg;
import us.zoom.cube.lib.hub.IpProcessorCfg;
import us.zoom.cube.site.core.parser.process.core.common.enums.MonitoringLogType;
import us.zoom.geoip.client.City;
import us.zoom.geoip.client.ZoomGeoClient;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author: <EMAIL>
 * @ModuleOwner: <EMAIL>
 * @Date: 01/11/2023 16:36
 * @Description:
 */
public class IpProcessor extends Processor {

    public IpProcessor() {
        super.type = MonitoringLogType.ipProcessorFail;
    }

    @Override
    public ProcessorResp process(Map<String, Object> messageMap, BaseProcessorCfg processorCfg) {
        ProcessorResp resp = new ProcessorResp(true, messageMap);
        try {
            IpProcessorCfg ipProcessorCfg = (IpProcessorCfg) processorCfg;
            String ipField = ipProcessorCfg.getIpField();
            List<String> geoFields = ipProcessorCfg.getGeoFields();
            City city = ZoomGeoClient.findCity((String) messageMap.get(ipField));
            if (city == null) {
                resp.setOutputMap(messageMap);
                return resp;
            }
            Map<String, String> cityMap = new HashMap<>();
            cityMap.put("ContinentCode", city.getContinentCode());
            cityMap.put("ContinentName", city.getContinentName());
            cityMap.put("CountryCode", city.getCountryCode());
            cityMap.put("CountryName", city.getCountryName());
            cityMap.put("RegionCode", city.getRegionCode());
            cityMap.put("RegionName", city.getRegionName());
            cityMap.put("CityName", city.getCityName());
            cityMap.put("PostalCode", city.getPostalCode());
            geoFields.forEach(geoField -> {
                messageMap.put(ipProcessorCfg.getPrefix() + geoField, cityMap.get(geoField));
            });
            logSuccess(messageMap,resp);
        } catch (Exception e) {
            logException(e, processorCfg,resp);
        }finally {
            logProcessor(processorCfg,resp);
        }
        return resp;
    }
}
