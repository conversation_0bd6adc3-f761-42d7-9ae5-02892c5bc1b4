package us.zoom.infra.dao.service;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import us.zoom.cube.site.BaseSpringTest;
import us.zoom.infra.dao.model.AggregationFunctionItemDO;
import us.zoom.cube.lib.utils.JsonUtils;

import java.util.List;

public class AggregationFunctionItemDAOTest  extends BaseSpringTest {
    private static final Logger logger= LoggerFactory.getLogger(AggregationFunctionItemDAOTest.class.getName());

    @Autowired
    AggregationFunctionItemDAO  aggregationFunctionItemDAO;

    @Test
    public void insert(){
        String id = "1234";
        AggregationFunctionItemDO aggregationFunctionItemDO = new AggregationFunctionItemDO();
        aggregationFunctionItemDO.setId(id);
        aggregationFunctionItemDO.setAggType("sum");
        aggregationFunctionItemDO.setMetricsAggregationRuleId("4e423486-c5ea-48b0-b087-2f5cf895cefd");
        aggregationFunctionItemDO.setTargetFieldId("targetFieldId1234");
        int insertRes = aggregationFunctionItemDAO.insertAggregationFunction(aggregationFunctionItemDO);
        System.out.println("insertRes = " + insertRes);
    }


    @Test
    public void crud(){
        String id = "1234";
        AggregationFunctionItemDO aggregationFunctionItemDO = new AggregationFunctionItemDO();
        aggregationFunctionItemDO.setId(id);
        aggregationFunctionItemDO.setAggType("sum");
        aggregationFunctionItemDO.setMetricsAggregationRuleId("ruleId1234");
        aggregationFunctionItemDO.setTargetFieldId("targetFieldId1234");
        int insertRes = aggregationFunctionItemDAO.insertAggregationFunction(aggregationFunctionItemDO);
        System.out.println("insertRes = " + insertRes);


        AggregationFunctionItemDO queryed = aggregationFunctionItemDAO.getById(id);
        System.out.println("queryed = " + JsonUtils.toJsonString(queryed));

        Assertions.assertNotNull(queryed);

        List<AggregationFunctionItemDO> listed = aggregationFunctionItemDAO.listAll();
        System.out.println("listed = " + JsonUtils.toJsonString(listed));
        aggregationFunctionItemDAO.deleteById(id);

        AggregationFunctionItemDO queryed1 = aggregationFunctionItemDAO.getById(id);
        Assertions.assertNull(queryed1);

    }

}
