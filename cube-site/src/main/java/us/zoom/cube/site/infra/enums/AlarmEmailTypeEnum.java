package us.zoom.cube.site.infra.enums;

public enum AlarmEmailTypeEnum {
    TEST("Test", "This is a test email"),
    CREATE("Create", "A new notice channel is created"),
    UPDATE("Update", "Notice channel is updated");

    private String type;
    private String content;

    AlarmEmailTypeEnum(String type, String content) {
        this.type = type;
        this.content = content;
    }

    public String getType() { return type; }
    public String getContent() { return content; }

    public static String getContentByType(String type){
        for (AlarmEmailTypeEnum item: AlarmEmailTypeEnum.values()) {
            if (item.getType().equals(type)) {
                return item.getContent();
            }
        }
        return null;
    }
}
