package us.zoom.cube.site.core;

import com.google.common.collect.Lists;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import us.zoom.cube.site.CubeSiteApplicationTests;
import us.zoom.cube.site.infra.utils.IdUtils;
import us.zoom.infra.dao.model.AsyncQueueDO;
import us.zoom.infra.dao.model.AsyncQueueGroupDO;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2020/8/5 6:31 PM
 */
public class AsyncQueueGroupHandlerTest extends CubeSiteApplicationTests {

    private static final String tenantId = "testTenantId";

    @Autowired
    private AsyncQueueGroupHandler asyncQueueGroupHandler;

    @Autowired
    private AsyncQueueHandler asyncQueueHandler;

    @Test
    public void testAllCases() throws Exception {
        AsyncQueueGroupDO asyncQueueGroupDO = buildAsyncQueueGroupDO();
        asyncQueueGroupHandler.addAqGroup(asyncQueueGroupDO);
        asyncQueueGroupDO.setName("testAsyncQueueGroup_name_update");
        asyncQueueGroupHandler.editAqGroup(asyncQueueGroupDO);
        AsyncQueueGroupDO updatedAsyncQueueGroupDO = asyncQueueGroupHandler.getAqGroupById(asyncQueueGroupDO.getId());
        Assertions.assertEquals("testAsyncQueueGroup_name_update", updatedAsyncQueueGroupDO.getName());
        boolean hasSameAqGroupName = asyncQueueGroupHandler.hasSameAqGroupName("testAsyncQueueGroup_name_update", Lists.newArrayList(tenantId));
        Assertions.assertTrue(hasSameAqGroupName);

        AsyncQueueDO asyncQueueDO = buildAsyncQueue(asyncQueueGroupDO.getId());
        asyncQueueHandler.addAq(asyncQueueDO);
         asyncQueueDO=asyncQueueHandler.getAqById(asyncQueueDO.getId());

        Assertions.assertTrue(asyncQueueDO.getKeyPwd().equals("keyPwd"));
        Assertions.assertTrue(asyncQueueDO.getJaasConfig().equals("jaas"));
        Assertions.assertTrue(asyncQueueDO.getKeystorePath().equals("keyName"));
        Assertions.assertTrue(asyncQueueDO.getKeystorePwd().equals("keyPwd"));
        Assertions.assertTrue(asyncQueueDO.getTruststorePath().equals("trustName"));
        Assertions.assertTrue(asyncQueueDO.getTruststorePwd().equals("trustPwd"));
        Assertions.assertTrue(asyncQueueDO.getAuthEncrType().equals(3));

        asyncQueueDO.setKeyPwd("keyPwd1");
        asyncQueueDO.setJaasConfig("jaas1");
        asyncQueueDO.setKeystorePath("keyName1");
        asyncQueueDO.setKeystorePwd("keyPwd1");
        asyncQueueDO.setTruststorePath("trustName1");
        asyncQueueDO.setTruststorePwd("trustPwd1");
        asyncQueueDO.setAuthEncrType(1);
        asyncQueueHandler.editAq(asyncQueueDO);
        asyncQueueDO=asyncQueueHandler.getAqById(asyncQueueDO.getId());

        Assertions.assertTrue(asyncQueueDO.getJaasConfig().equals(""));
        Assertions.assertTrue(asyncQueueDO.getKeyPwd().equals("keyPwd1"));
        Assertions.assertTrue(asyncQueueDO.getKeystorePath().equals("keyName1"));
        Assertions.assertTrue(asyncQueueDO.getKeystorePwd().equals("keyPwd1"));
        Assertions.assertTrue(asyncQueueDO.getTruststorePath().equals("trustName1"));
        Assertions.assertTrue(asyncQueueDO.getTruststorePwd().equals("trustPwd1"));
        Assertions.assertTrue(asyncQueueDO.getAuthEncrType().equals(1));

        AsyncQueueGroupDO asyncQueueGroupDO2 = buildAsyncQueueGroupDO2();
        asyncQueueGroupHandler.addAqGroup(asyncQueueGroupDO2);


        asyncQueueGroupHandler.editAqGroup(asyncQueueGroupDO2);
        List<AsyncQueueGroupDO> result = asyncQueueGroupHandler.findByNameLike("", Lists.newArrayList(tenantId), 1, 5);
        Assertions.assertTrue(result.size() >= 2);
        result = asyncQueueGroupHandler.findByNameLike("test", Lists.newArrayList(tenantId), 1, 5);
        Assertions.assertTrue(result.size() >= 2);
        int count = asyncQueueGroupHandler.getCountByNameLike("test", Lists.newArrayList(tenantId));
        Assertions.assertEquals(2, count);
        result = asyncQueueGroupHandler.findByNameLike("test", Lists.newArrayList("Fgtsd"), 1, 5);
        Assertions.assertEquals(0, result.size());

        result = asyncQueueGroupHandler.listAllForTenant(tenantId);
        Assertions.assertTrue(result.size() >= 2);
        result = asyncQueueGroupHandler.listAllForTenant("tenantId2");
        Assertions.assertEquals(0, result.size());
        result = asyncQueueGroupHandler.listAll();
        Assertions.assertTrue(result.size() >= 2);

        asyncQueueGroupHandler.deleteAqGroupById(asyncQueueGroupDO.getId());
        asyncQueueGroupHandler.deleteAqGroupById(asyncQueueGroupDO2.getId());
    }

    private AsyncQueueGroupDO buildAsyncQueueGroupDO() {
        AsyncQueueGroupDO asyncQueueGroupDO = new AsyncQueueGroupDO();
        asyncQueueGroupDO.setId(IdUtils.generateId());
        asyncQueueGroupDO.setName("testAsyncQueueGroup_name");
        asyncQueueGroupDO.setCorrelationStrategy("clusterId:regionId");
        asyncQueueGroupDO.setTenantId(tenantId);
        asyncQueueGroupDO.setCreateTime(new Date());
        asyncQueueGroupDO.setModifyTime(new Date());
        return asyncQueueGroupDO;
    }

    private AsyncQueueGroupDO buildAsyncQueueGroupDO2() {
        AsyncQueueGroupDO asyncQueueGroupDO = new AsyncQueueGroupDO();
        asyncQueueGroupDO.setId(IdUtils.generateId());
        asyncQueueGroupDO.setName("testAsyncQueueGroup_name2");
        asyncQueueGroupDO.setCorrelationStrategy("clusterId");
        asyncQueueGroupDO.setTenantId(tenantId);
        asyncQueueGroupDO.setCreateTime(new Date());
        asyncQueueGroupDO.setModifyTime(new Date());
        return asyncQueueGroupDO;
    }


    private AsyncQueueDO buildAsyncQueue(String asyncQueueGroupId) {
        AsyncQueueDO asyncQueueDO = new AsyncQueueDO();
        asyncQueueDO.setId(IdUtils.generateId());
        asyncQueueDO.setAsyncQueueGroupId(asyncQueueGroupId);
        asyncQueueDO.setName("AsyncQueue_test_name");
        asyncQueueDO.setClusterId("clusterId_test");
        asyncQueueDO.setRegionId("RegionId_test");
        asyncQueueDO.setServers("ec2-50-16-132-120.compute-1.amazonaws.com:9093,ec2-3-86-37-86.compute-1.amazonaws.com:9093,ec2-34-227-170-219.compute-1.amazonaws.com:9093");
        asyncQueueDO.setGroupId("hub-test");
        asyncQueueDO.setTopics("web_monitor");
        asyncQueueDO.setPollSize(200);
        asyncQueueDO.setThreadCount(10);
        asyncQueueDO.setSessionTimeOut(50000);
        asyncQueueDO.setGmtCreate(new Date());
        asyncQueueDO.setGmtModify(new Date());
        asyncQueueDO.setKeyPwd("keyPwd");
        asyncQueueDO.setKeystorePath("keyName");
        asyncQueueDO.setKeystorePwd("keyPwd");
        asyncQueueDO.setTruststorePath("trustName");
        asyncQueueDO.setTruststorePwd("trustPwd");
        asyncQueueDO.setJaasConfig("jaas");
        asyncQueueDO.setAuthEncrType(3);
        asyncQueueDO.setIdenAlgorithm("");
        return asyncQueueDO;
    }
}
