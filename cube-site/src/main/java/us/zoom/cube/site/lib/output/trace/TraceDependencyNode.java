package us.zoom.cube.site.lib.output.trace;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TraceDependencyNode {
    private String name;
    private double incomingCallRate;
    private double outcomingCallRate;
    private double errorRate;
    private String type; // service | mq | database
    private String id;
} 