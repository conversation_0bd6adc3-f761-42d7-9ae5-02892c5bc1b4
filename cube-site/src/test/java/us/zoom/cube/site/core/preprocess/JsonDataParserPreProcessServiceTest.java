package us.zoom.cube.site.core.preprocess;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import us.zoom.cube.lib.utils.JsonUtils;
import us.zoom.cube.site.core.parser.process.core.common.DataParserLogTypeEnum;
import us.zoom.cube.site.core.parser.process.core.common.constant.Constants;
import us.zoom.cube.site.core.parser.process.core.monitoring.Measure;
import us.zoom.cube.site.core.parser.process.core.preprocess.JsonDataParserPreProcessService;
import us.zoom.cube.site.core.parser.process.core.response.PreProcessDO;
import us.zoom.cube.site.core.parser.process.core.response.PreResp;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

/**
 * @Author: <EMAIL>
 * @ModuleOwner: <EMAIL>
 * @Date: 12/27/2022 10:31
 * @Description:
 */
public class JsonDataParserPreProcessServiceTest extends BaseTest {
    JsonDataParserPreProcessService ajpps;

    ObjectMapper mapper = new ObjectMapper();

    @BeforeEach
    public void before() {
        ajpps = new JsonDataParserPreProcessService();
    }

    @Test
    public void testSingleAgentLogAndSingleMeasureAndLabelList() {
        Measure measure = getMeasure("request");
        String message = JsonUtils.toJsonStringIgnoreExp(getSingleAgentMsgSingleMeasure(labelList(), measure));
        PreResp resp = ajpps.handle(message);
        Map<String, Object> map = commonMap();
        map.remove("path_template");
        map.remove("topType");

        map.putAll(measure.toMap());
        map.putAll(labelList());

        String ss1 = JsonUtils.toJsonStringIgnoreExp(new TreeMap<>(resp.getPreProcessList().get(0).getMap()));
        String ss2 = JsonUtils.toJsonStringIgnoreExp(new TreeMap<>(map));
        Assertions.assertEquals(ss1, ss2);
    }

    @Test
    public void testErrorSingleAgentLogAndSingleMeasureAndLabelList() {
        Measure measure = getErrorMeasure("request");
        String message = JsonUtils.toJsonStringIgnoreExp(getSingleAgentMsgSingleMeasure(labelList(), measure));
        PreResp resp = ajpps.handle(message);
        Assertions.assertEquals(resp.getPreProcessList().size(), 1);

    }

    @Test
    public void testSingleAgentLogAndSingleMeasureAndNotLabelList() {
        Measure measure = getMeasure("request");
        Map<String, Object> messageMap = getSingleAgentMsgSingleMeasure(null, measure);
        String message = JsonUtils.toJsonStringIgnoreExp(messageMap);
        PreResp resp = ajpps.handle(message);
        Map<String, Object> map = commonMap();
        map.remove("path_template");
        map.remove("topType");
        map.putAll(measure.toMap());

        String ss1 = JsonUtils.toJsonStringIgnoreExp(new TreeMap<>(resp.getPreProcessList().get(0).getMap()));
        String ss2 = JsonUtils.toJsonStringIgnoreExp(new TreeMap<>(map));
        Assertions.assertEquals(ss1, ss2);

        Map<String, Object> messageMap1 = getSingleAgentMsgSingleMeasure(null, measure);
        String message1 = JsonUtils.toJsonStringIgnoreExp(messageMap1);
        PreResp resp1 = ajpps.handle(message1);
        Map<String, Object> map1 = commonMap();
        map1.putAll(measure.toMap());

        String ss11 = JsonUtils.toJsonStringIgnoreExp(new TreeMap<>(resp1.getPreProcessList().get(0).getMap()));
        String ss21 = JsonUtils.toJsonStringIgnoreExp(new TreeMap<>(map));
        Assertions.assertEquals(ss11, ss21);
    }

    @Test
    public void testSingleAgentLogAndMultiMeasure() {
        Measure measure1 = getMeasure("request1");
        Measure measure2 = getMeasure("request2");
        Measure measure3 = getMeasure("request3");
        String message = JsonUtils.toJsonStringIgnoreExp(getSingleAgentMsgSingleMeasure(labelList(), measure1, measure2, measure3));
        PreResp resp = ajpps.handle(message);
        Map<String, Object> map = commonMap();
        map.remove("path_template");
        map.remove("topType");

        map.putAll(measure1.toMap());
        map.putAll(labelList());
        String ss1 = JsonUtils.toJsonStringIgnoreExp(new TreeMap<>(resp.getPreProcessList().get(0).getMap()));
        String ss2 = JsonUtils.toJsonStringIgnoreExp(new TreeMap<>(map));
        Assertions.assertEquals(ss1, ss2);
    }

    @Test
    public void testSingleAgentLogAndMultiMeasure1() throws JsonProcessingException {
        Map<String, Object> tag = new HashMap<>();
        tag.put("unit1", "aa");
        tag.put("zoom", "bb");
        tag.put("dataParser", "cc");
        Map<String, Object> field = new HashMap<>();
        field.put("cost", 1);
        field.put("size", 2);
        field.put("maxCost", 3);

        Map<String, Object> measure = new HashMap<>();
        measure.put("tag", tag);
        measure.put("field", field);
        measure.put("ts", System.currentTimeMillis());

//        measure.put("measure", "request1");
//        Measure measure1 = JsonUtils.parse(JsonUtils.toJsonStringIgnoreExp(measure), Measure.class);
//        measure.put("measure", "request2");
//        Measure measure2 = JsonUtils.parse(JsonUtils.toJsonStringIgnoreExp(measure), Measure.class);
//        measure.put("measure", "request3");
//        Measure measure3 = JsonUtils.parse(JsonUtils.toJsonStringIgnoreExp(measure), Measure.class);

//        String message = JsonUtils.toJsonStringIgnoreExp(getSingleAgentMsgSingleMeasure(labelList(), measure1, measure2, measure3));
//        PreResp resp = ajpps.handle(message);
        Map<String, Object> map = commonMap();
        map.remove("path_template");
        map.remove("topType");

//        map.putAll(measure1.toMap());
        map.putAll(labelList());
//        String ss1 = JsonUtils.toJsonStringIgnoreExp(new TreeMap<>(resp.getPreProcessList().get(0).getMap()));
        String ss2 = JsonUtils.toJsonStringIgnoreExp(new TreeMap<>(map));
//        Assertions.assertEquals(ss1, ss2);
    }

    @Test
    public void testMap() {
        Map<String, String> map = new HashMap<>();
        map.put("zoom", "zoom");
        map.putIfAbsent("zoom", "zoom1");
        Assertions.assertEquals(map.get("zoom"), "zoom");
    }

    @Test
    public void testMultiAgentLogAndSingleMeasure() {
        Measure measure = getMeasure("request");
        Map<String, Object> agentMap = getSingleAgentMsgSingleMeasure(labelList(), measure);
        String message = JsonUtils.toJsonStringIgnoreExp(Arrays.asList(agentMap, agentMap, agentMap));
        PreResp resp = ajpps.handle(message);
        Map<String, Object> map = commonMap();
        map.remove("path_template");
        map.remove("topType");

        map.putAll(measure.toMap());
        map.putAll(labelList());
        String ss1 = JsonUtils.toJsonStringIgnoreExp(new TreeMap<>(resp.getPreProcessList().get(0).getMap()));
        String ss2 = JsonUtils.toJsonStringIgnoreExp(new TreeMap<>(map));
        Assertions.assertEquals(ss1, ss2);

        Assertions.assertEquals(resp.getPreProcessList().size(), 3);
    }

    @Test
    public void testMultiAgentLogAndMultiMeasure() {
        Measure measure1 = getMeasure("request1");
        Measure measure2 = getMeasure("request2");
        Measure measure3 = getMeasure("request3");
        Map<String, Object> agentMap = getSingleAgentMsgSingleMeasure(labelList(), measure1, measure2, measure3);
        String message = JsonUtils.toJsonStringIgnoreExp(Arrays.asList(agentMap, agentMap, agentMap));
        PreResp resp = ajpps.handle(message);
        Map<String, Object> map = commonMap();
        map.remove("path_template");
        map.remove("topType");

        Assertions.assertEquals(resp.getPreProcessList().size(), 9);
        map.putAll(measure1.toMap());
        map.putAll(labelList());

        String ss1 = JsonUtils.toJsonStringIgnoreExp(new TreeMap<>(resp.getPreProcessList().get(0).getMap()));
        String ss2 = JsonUtils.toJsonStringIgnoreExp(new TreeMap<>(map));
        Assertions.assertEquals(ss1, ss2);

        Assertions.assertEquals(resp.getPreProcessList().size(), 9);
    }

    private Map<String, Object> commonMap() {
        Map<String, Object> map = new HashMap<>();
        map.put("clusterId", "devep");
        map.put("instanceId", "i-666666666a775f555");
        map.put("hostName", "ip-10-10-10-110.ec2.internal");
        map.put("path_template", "/data/logs/*/*/*/zoom_middleware/*/*/metrics.log");
        map.put("topType", "file");
        map.put("appName", "nws");
        return map;
    }


    @Test
    public void testSingleAgentLogAndSingleMeasureNotMessage() {
        Measure measure = getMeasure("request");
        Map<String, Object> agentMap = getSingleAgentMsgSingleMeasure(labelList(), measure);
        agentMap.put(Constants.AGENT_MESSAGE, "");
        String message = JsonUtils.toJsonStringIgnoreExp(agentMap);
        PreResp resp = ajpps.handle(message);
        Assertions.assertEquals(resp.getPreProcessList().size(), 0);
        agentMap.remove(Constants.AGENT_MESSAGE);
        message = JsonUtils.toJsonStringIgnoreExp(agentMap);
        PreResp resp1 = ajpps.handle(message);
        Assertions.assertEquals(resp1.getPreProcessList().size(), 1);
    }

    @Test
    public void testAnalysisJson() throws JsonProcessingException {
        Map<String, Object> map1 = new HashMap<>();
        map1.put("1clusterId", "devep");
        map1.put("1instanceId", "i-666666666a775f555");
        map1.put("1hostName", "ip-10-10-10-110.ec2.internal");
        map1.put("1list", Arrays.asList("aa", "bb", "cc"));

        Map<String, Object> map2 = new HashMap<>();
        map2.put("2clusterId", "devep");
        map2.put("2instanceId", "i-666666666a775f555");
        map2.put("2hostName", "ip-10-10-10-110.ec2.internal");
        map2.put("2map1", map1);
        map2.put("2list", Arrays.asList("aa", "bb", "cc"));

        Map<String, Object> map3 = new HashMap<>();
        map3.put("3clusterId", "devep");
        map3.put("3instanceId", "i-666666666a775f555");
        map3.put("3hostName", "ip-10-10-10-110.ec2.internal");
        map3.put("3map2", map2);
        map3.put("3list", Arrays.asList("aa", "bb", "cc"));

        Map<String, Object> map4 = new HashMap<>();
        map4.put("4clusterId", "devep");
        map4.put("4instanceId", "i-666666666a775f555");
        map4.put("4hostName", "ip-10-10-10-110.ec2.internal");
        map4.put("4map3", map3);
        map4.put("4list", Arrays.asList("aa", "bb", "cc"));
        String json = JsonUtils.toJsonStringIgnoreExp(map4);
        JsonNode jn = mapper.readValue(json, JsonNode.class);
        PreProcessDO preProcessDO = ajpps.transferJson(DataParserLogTypeEnum.agent_json, jn);

        Assertions.assertEquals(preProcessDO.getMap().size(), 16);
        Assertions.assertEquals(preProcessDO.getMap().get("4map3.3map2.2map1.1clusterId"), "devep");
    }

    @Test
    public void testLabelList() throws JsonProcessingException {
        Map<String, Object> labelList = labelList();
        JsonNode jsonNode = mapper.readValue(JsonUtils.toJsonStringIgnoreExp(labelList), JsonNode.class);
        Map<String, Object> tmpLabelList = ajpps.labelList(jsonNode);
        Assertions.assertEquals(tmpLabelList, labelList);

        JsonNode jsonNode1 = mapper.readValue("[]", JsonNode.class);
        Map<String, Object> tmpLabelList1 = ajpps.labelList(jsonNode1);
        Assertions.assertEquals(tmpLabelList1, Maps.newHashMapWithExpectedSize(1));
    }

    @Test
    public void testBasicMap() throws JsonProcessingException {
        Measure measure1 = getMeasure("request1");
        Map<String, Object> agentMsg = getSingleAgentMsgSingleMeasure(labelList(), measure1);
        JsonNode jsonNode1 = mapper.readValue(JsonUtils.toJsonStringIgnoreExp(agentMsg), JsonNode.class);
        Map<String, Object> basicMap = ajpps.basicMap(jsonNode1);
        Assertions.assertEquals(basicMap.get(Constants.COMMON_FIELD_INSTANCE_ID), "i-666666666a775f555");
        Assertions.assertEquals(basicMap.get(Constants.COMMON_FIELD_HOST_NAME), "ip-10-10-10-110.ec2.internal");
        Assertions.assertEquals(basicMap.get(Constants.COMMON_FIELD_REGION_ID) == null, true);
        Assertions.assertEquals(basicMap.get(Constants.COMMON_FIELD_TS) != null, true);
        Assertions.assertEquals(basicMap.get(Constants.COMMON_FIELD_CLUSTER_ID), "devep");
        Assertions.assertEquals(basicMap.get(Constants.COMMON_FIELD_APP_NAME), "nws");
        Assertions.assertEquals(basicMap.get(Constants.COMMON_FIELD_TOP_TYPE), "file");
        Assertions.assertEquals(basicMap.get(Constants.COMMON_FIELD_PATH_TEMPLATE), "/data/logs/*/*/*/zoom_middleware/*/*/metrics.log");
    }

    @Test
    public void testZdcaBaseInfo() {
        String message = "[\n" +
                "    {\n" +
                "        \"appName\":\"\",\n" +
                "        \"agentKey\":\"\",\n" +
                "        \"host\":\"system-zdca-metrics-579b78c448-45c4r\",\n" +
                "        \"ip\":\"***********\",\n" +
                "        \"version\":\"1.2.2.20230206.1300.676aaed2.20230206\",\n" +
                "        \"ts\":1675912259196,\n" +
                "        \"topType\":\"scraper\",\n" +
                "        \"infraType\":\"scraper\",\n" +
                "        \"collectType\":11,\n" +
                "        \"clusterId\":\"main\",\n" +
                "        \"regionId\":\"va1\",\n" +
                "        \"zoneName\":\"\",\n" +
                "        \"csp\":\"AWS\",\n" +
                "        \"instanceId\":\"i-078649dc5ca5d787a\",\n" +
                "        \"ipPublic\":\"\",\n" +
                "        \"instanceType\":\"m5a.xlarge\",\n" +
                "        \"diskType\":-1,\n" +
                "        \"isK8S\":0,\n" +
                "        \"labelList\":{\n" +
                "\n" +
                "        },\n" +
                "        \"message\":\"{\\\"measure\\\":\\\"api\\\",\\\"tag\\\":{\\\"appName\\\":\\\"system-kube-apiserver\\\",\\\"clusterId\\\":\\\"\\\",\\\"component\\\":\\\"apiserver\\\",\\\"dry_run\\\":\\\"\\\",\\\"group\\\":\\\"apps\\\",\\\"ip\\\":\\\"***********\\\",\\\"le\\\":\\\"0.8\\\",\\\"podName\\\":\\\"\\\",\\\"regionId\\\":\\\"\\\",\\\"resource\\\":\\\"daemonsets\\\",\\\"scope\\\":\\\"namespace\\\",\\\"subresource\\\":\\\"\\\",\\\"url\\\":\\\"https://***********:443/metrics\\\",\\\"verb\\\":\\\"LIST\\\",\\\"version\\\":\\\"v1\\\"},\\\"field\\\":{\\\"apiserver_request_duration_seconds_histogram\\\":45},\\\"ts\\\":1675912258281,\\\"cubeVer\\\":\\\"1.0.0\\\",\\\"pii\\\":null}\"\n" +
                "    },\n" +
                "    {\n" +
                "        \"appName\":\"\",\n" +
                "        \"agentKey\":\"\",\n" +
                "        \"host\":\"system-zdca-metrics-579b78c448-45c4r\",\n" +
                "        \"ip\":\"***********\",\n" +
                "        \"version\":\"1.2.2.20230206.1300.676aaed2.20230206\",\n" +
                "        \"ts\":1675912259196,\n" +
                "        \"topType\":\"scraper\",\n" +
                "        \"infraType\":\"scraper\",\n" +
                "        \"collectType\":11,\n" +
                "        \"clusterId\":\"main\",\n" +
                "        \"regionId\":\"va1\",\n" +
                "        \"zoneName\":\"\",\n" +
                "        \"csp\":\"AWS\",\n" +
                "        \"instanceId\":\"i-078649dc5ca5d787a\",\n" +
                "        \"ipPublic\":\"\",\n" +
                "        \"instanceType\":\"m5a.xlarge\",\n" +
                "        \"diskType\":-1,\n" +
                "        \"isK8S\":0,\n" +
                "        \"labelList\":{\n" +
                "\n" +
                "        },\n" +
                "        \"message\":\"{\\\"measure\\\":\\\"api\\\",\\\"tag\\\":{\\\"appName\\\":\\\"system-kube-apiserver\\\",\\\"clusterId\\\":\\\"\\\",\\\"component\\\":\\\"apiserver\\\",\\\"dry_run\\\":\\\"\\\",\\\"group\\\":\\\"\\\",\\\"ip\\\":\\\"***********\\\",\\\"le\\\":\\\"0.2\\\",\\\"podName\\\":\\\"\\\",\\\"regionId\\\":\\\"\\\",\\\"resource\\\":\\\"replicationcontrollers\\\",\\\"scope\\\":\\\"cluster\\\",\\\"subresource\\\":\\\"\\\",\\\"url\\\":\\\"https://***********:443/metrics\\\",\\\"verb\\\":\\\"WATCH\\\",\\\"version\\\":\\\"v1\\\"},\\\"field\\\":{\\\"apiserver_request_duration_seconds_histogram\\\":1},\\\"ts\\\":1675912258281,\\\"cubeVer\\\":\\\"1.0.0\\\",\\\"pii\\\":null}\"\n" +
                "    }\n" +
                "]";
        PreResp resp = ajpps.handle(message);
        resp.getPreProcessList().forEach(preProcessDO -> {
            Assertions.assertEquals(preProcessDO.getMeasure().getTag().get(Constants.COMMON_FIELD_TS), null);
        });
        Assertions.assertEquals(resp.getPreProcessList().size(), 2);
    }

    @Test
    public void testZdcaBaseInfo2() {
        Map<String, Object> map1 = new HashMap<>();
        map1.put("1clusterId", "devep");
        map1.put("1instanceId", "i-666666666a775f555");
        map1.put("1hostName", "ip-10-10-10-110.ec2.internal");
        map1.put("1list", Arrays.asList("aa", "bb", "cc"));

        Map<String, Object> baseInfoLog = new HashMap<>();
        baseInfoLog.put("appName", "Infra_BigData_emr");
        baseInfoLog.put("agentKey", "");
        baseInfoLog.put("host", "ip-10-0-9-148.ec2.internal");
        baseInfoLog.put("ip", "**********");
        baseInfoLog.put("version", "");
        baseInfoLog.put("ts", 1672992258284L);
        baseInfoLog.put("topType", "middleware");
        baseInfoLog.put("infraType", "spark.executor_id");
        baseInfoLog.put("collectType", 6);
        baseInfoLog.put("clusterId", "j-37EV2T5E32CJR");
        baseInfoLog.put("regionId", "us-east-1");
        baseInfoLog.put("zoneName", "");
        baseInfoLog.put("csp", "AWS");
        baseInfoLog.put("instanceId", "i-0670ecd4c39640470");
        baseInfoLog.put("ipPublic", "");
        baseInfoLog.put("instanceType", "m5.4xlarge");
        baseInfoLog.put("diskType", 0);
        baseInfoLog.put("labelList", null);
        baseInfoLog.put("message", JsonUtils.toJsonStringIgnoreExp(map1));
        PreResp resp = ajpps.handle(JsonUtils.toJsonStringIgnoreExp(Arrays.asList(baseInfoLog, baseInfoLog)));
        Assertions.assertEquals(resp.getPreProcessList().size(), 2);
    }

    @Test
    public void testInfraJson() {
        String message = "[\n" +
                "    {\n" +
                "        \"appName\": \"ds_cube_hub\",\n" +
                "        \"agentKey\": \"\",\n" +
                "        \"host\": \"perf-hub-02\",\n" +
                "        \"ip\": \"***********\",\n" +
                "        \"version\": \"\",\n" +
                "        \"ts\": 1650264952917,\n" +
                "        \"topType\": \"infra\",\n" +
                "        \"infraType\": \"infra.memory\",\n" +
                "        \"collectType\": 2,\n" +
                "        \"clusterId\": \"ds01\",\n" +
                "        \"regionId\": \"VA\",\n" +
                "        \"zoneName\": \"\",\n" +
                "        \"cloudType\": 1,\n" +
                "        \"instanceId\": \"perf-hub-02\",\n" +
                "        \"ipPublic\": \"**********\",\n" +
                "        \"instanceType\": \"c5.4xlarge\",\n" +
                "        \"diskType\": 0,\n" +
                "        \"total\": 31959300,\n" +
                "        \"used\": 3867960,\n" +
                "        \"free\": 20772156,\n" +
                "        \"usable\": 26142816,\n" +
                "        \"buffers\": 2088,\n" +
                "        \"cached\": 7317096,\n" +
                "        \"usedPercent\": 12.102768,\n" +
                "        \"swapTotal\": 0,\n" +
                "        \"swapUsed\": 0,\n" +
                "        \"swapFree\": 0,\n" +
                "        \"swapCached\": 0,\n" +
                "        \"labelList\":{\n" +
                "            \"_mwAppName_\":\"Meeting_Web_billing-coupon\"\n" +
                "        }\n" +
                "    }\n" +
                "]";

        PreResp resp = ajpps.handle(message);
        Assertions.assertEquals(resp.getPreProcessList().size(), 1);
    }


    @Test
    public void testInfraJsonRepeat() {
        String message = "[\n" +
                "    {\n" +
                "        \"appName\": \"ds_cube_hub\",\n" +
                "        \"agentKey\": \"\",\n" +
                "        \"host\": \"perf-hub-02\",\n" +
                "        \"ip\": \"***********\",\n" +
                "        \"version\": \"\",\n" +
                "        \"ts\": 1650264952917,\n" +
                "        \"topType\": \"infra\",\n" +
                "        \"infraType\": \"infra.memory\",\n" +
                "        \"collectType\": 2,\n" +
                "        \"clusterId\": \"ds01\",\n" +
                "        \"regionId\": \"VA\",\n" +
                "        \"zoneName\": \"\",\n" +
                "        \"cloudType\": 1,\n" +
                "        \"instanceId\": \"perf-hub-02\",\n" +
                "        \"ipPublic\": \"**********\",\n" +
                "        \"instanceType\": \"c5.4xlarge\",\n" +
                "        \"diskType\": 0,\n" +
                "        \"total\": 31959300,\n" +
                "        \"used\": 3867960,\n" +
                "        \"free\": 20772156,\n" +
                "        \"usable\": 26142816,\n" +
                "        \"buffers\": 2088,\n" +
                "        \"cached\": 7317096,\n" +
                "        \"usedPercent\": 12.102768,\n" +
                "        \"swapTotal\": 0,\n" +
                "        \"swapUsed\": 0,\n" +
                "        \"swapFree\": 0,\n" +
                "        \"swapCached\": 0,\n" +
                "        \"labelList\":{\n" +
                "            \"ipPublic\":\"0.0.0.0.repeat\"\n" +
                "        }\n" +
                "    }\n" +
                "]";

        PreResp resp = ajpps.handle(message);
        Assertions.assertEquals(resp.getPreProcessList().size(), 1);
        Assertions.assertEquals(resp.getPreProcessList().get(0).getMap().get("ipPublic"), "**********");
    }

    @Test
    public void testHistogram() {
        String message = "[\n" +
                "    {\n" +
                "        \"appName\":\"netpath\",\n" +
                "        \"agentKey\":\"\",\n" +
                "        \"host\":\"work-node1\",\n" +
                "        \"ip\":\"**************\",\n" +
                "        \"version\":\"1.2.12.1463.c2a3810e.20230419\",\n" +
                "        \"ts\":1681873247728,\n" +
                "        \"topType\":\"scraper\",\n" +
                "        \"infraType\":\"scraper\",\n" +
                "        \"collectType\":11,\n" +
                "        \"clusterId\":\"devhz\",\n" +
                "        \"regionId\":\"KVM01\",\n" +
                "        \"zoneName\":\"\",\n" +
                "        \"csp\":\"Colo\",\n" +
                "        \"instanceId\":\"work-node1\",\n" +
                "        \"ipPublic\":\"\",\n" +
                "        \"instanceType\":\"\",\n" +
                "        \"diskType\":0,\n" +
                "        \"isK8S\":0,\n" +
                "        \"labelList\":{\n" +
                "\n" +
                "        },\n" +
                "        \"message\":\"{\\\"measure\\\":\\\"measure_9\\\",\\\"tag\\\":{\\\"tag_0\\\":\\\"val_5\\\",\\\"tag_1\\\":\\\"val_1\\\",\\\"tag_2\\\":\\\"val_2\\\"},\\\"field\\\":{\\\"histogram_metric_19_count\\\":14,\\\"histogram_metric_19_sum\\\":99.07999999999811,\\\"histogram_metric_29_count\\\":14,\\\"histogram_metric_29_sum\\\":60.2400000000016,\\\"histogram_metric_39_count\\\":14,\\\"histogram_metric_39_sum\\\":65.42000000000007,\\\"histogram_metric_49_count\\\":14,\\\"histogram_metric_49_sum\\\":90.80999999999949,\\\"histogram_metric_9_count\\\":14,\\\"histogram_metric_9_sum\\\":76.27999999999884},\\\"hisSchema\\\":{\\\"histogram_metric_29\\\":[1,2,3.0,3.42,19999,2.33333]},\\\"ts\\\":1681873247627,\\\"cubeVer\\\":\\\"1.0.1\\\",\\\"pii\\\":null}\"\n" +
                "    },\n" +
                "    {\n" +
                "        \"appName\":\"netpath\",\n" +
                "        \"agentKey\":\"\",\n" +
                "        \"host\":\"work-node1\",\n" +
                "        \"ip\":\"**************\",\n" +
                "        \"version\":\"1.2.12.1462.805ca784.20230418\",\n" +
                "        \"ts\":1681870414983,\n" +
                "        \"topType\":\"scraper\",\n" +
                "        \"infraType\":\"scraper\",\n" +
                "        \"collectType\":11,\n" +
                "        \"clusterId\":\"devhz\",\n" +
                "        \"regionId\":\"KVM01\",\n" +
                "        \"zoneName\":\"\",\n" +
                "        \"csp\":\"Colo\",\n" +
                "        \"instanceId\":\"work-node1\",\n" +
                "        \"ipPublic\":\"\",\n" +
                "        \"instanceType\":\"\",\n" +
                "        \"diskType\":0,\n" +
                "        \"isK8S\":0,\n" +
                "        \"labelList\":{\n" +
                "\n" +
                "        },\n" +
                "        \"message\":\"{\\\"measure\\\":\\\"measure_2\\\",\\\"tag\\\":{\\\"tag_0\\\":\\\"val_4\\\",\\\"tag_1\\\":\\\"val_1\\\",\\\"tag_2\\\":\\\"val_2\\\"},\\\"field\\\":{\\\"summary_metric_12_count\\\":15,\\\"summary_metric_12_sum\\\":4.737249631230952,\\\"summary_metric_22_count\\\":15,\\\"summary_metric_22_sum\\\":8.187299966542923,\\\"summary_metric_2_count\\\":14,\\\"summary_metric_2_sum\\\":6.905363060162927,\\\"summary_metric_32_count\\\":15,\\\"summary_metric_32_sum\\\":9.231907753528503,\\\"summary_metric_42_count\\\":15,\\\"summary_metric_42_sum\\\":6.5582066175556974},\\\"sumField\\\":{\\\"summary_metric_12\\\":[0.25480401013758186,0.2963180199077964,0.5007518089428359,0.7428070795328073,0.8011144595654384,0.856985521607526,0.9088028442040668,0.9493394163161591,0.9924098791646591,0.9991885447868977],\\\"summary_metric_2\\\":[0.24628960790548696,0.2933832832643216,0.49475496388513385,0.7419994672347409,0.8065273700136845,0.8587474353743614,0.9079739670167442,0.9608595924597813,0.9924476724781021,0.9995216005126333],\\\"summary_metric_22\\\":[0.27163588429529184,0.332452619065863,0.5000728873493723,0.7670567968539912,0.8300789756852713,0.8664534636824667,0.9105895261171288,0.9511897348899159,0.995127328595803,0.9999509651204248],\\\"summary_metric_32\\\":[0.23667466900027956,0.2883969079750982,0.4691734753393094,0.7283038384448715,0.7961290781178043,0.8369308271885947,0.8870329178810423,0.9415822063379478,0.9906144107899955,0.9991326074475814],\\\"summary_metric_42\\\":[0.243074213696148,0.2846217509934903,0.5130121618883839,0.7618970555949024,0.8029474099937949,0.8575772526751165,0.9057695247377178,0.9435162958103306,0.9853342692153898,0.9994236949234637]},\\\"ts\\\":1681870414812,\\\"cubeVer\\\":\\\"1.0.1\\\",\\\"pii\\\":null}\"\n" +
                "    }\n" +
                "]";
        PreResp resp = ajpps.handle(message);
        List<Object> list = Lists.newArrayList(1, 2, 3.0, 3.42, 19999, 2.33333);
        Assertions.assertEquals(true, resp.getPreProcessList().get(0).getMeasure().getHisSchema().get("histogram_metric_29").equals(list));
    }

    @Test
    public void testMapField() {
        String message = "{\"measure\":\"report_pipeline_metrics\",\"tag\":{\"unitTag\":\"ds03\",\"url\":\"[\\\"https://cube-perf.zoomdev.us:8443/out/pipeline/discover\\\"]\",\"cubeStatus\":\"success\",\"statusCode\":200},\"ts\":1682086086563,\"cubeVer\":\"1.0.0\",\"field\":{\"cost\":44,\"addPipelineFail\":0,\"pipelineSize\":25},\"pii\":[],\"mapField\":null,\"hisField\":null,\"hisSchema\":null,\"sumField\":null,\"sumSchema\":null}";
        PreResp preResp = ajpps.handle(message);
        Assertions.assertEquals(preResp.getPreProcessList().get(0).getMeasure().getMapField() == null, true);
    }

    @Test
    public void testMapField1() {
        String message = "[{\"app\":\"app_asyncmq\",\"topic\":\"dev_pbx_web_phone_offline_alert_job_topic\",\"ts\":1682383800000,\"available\":1,\"topic_count\":340,\"app_count\":1},{\"app\":\"sba_admin\",\"topic\":\"devcolo_register_server_resolver_topic\",\"ts\":1682384100000,\"available\":1,\"topic_count\":19,\"app_count\":1},{\"app\":\"sba_admin\",\"topic\":\"devcolo_register_server_resolver_topic\",\"ts\":1682383800000,\"available\":1,\"topic_count\":19,\"app_count\":1},{\"app\":\"sba_admin\",\"topic\":\"devcolo_register_server_resolver_topic\",\"ts\":1682383500000,\"available\":1,\"topic_count\":19,\"app_count\":1},{\"app\":\"sba_admin\",\"topic\":\"devcolo_register_server_resolver_topic\",\"ts\":1682383200000,\"available\":1,\"topic_count\":19,\"app_count\":1},{\"app\":\"sba_admin\",\"topic\":\"devcolo_register_server_resolver_topic\",\"ts\":1682382900000,\"available\":1,\"topic_count\":19,\"app_count\":1},{\"app\":\"sba_admin\",\"topic\":\"devcolo_register_server_resolver_topic\",\"ts\":1682382600000,\"available\":1,\"topic_count\":19,\"app_count\":1},{\"app\":\"sba_admin\",\"topic\":\"devcolo_register_server_resolver_topic\",\"ts\":1682382000000,\"available\":1,\"topic_count\":19,\"app_count\":1},{\"app\":\"sba_admin\",\"topic\":\"devcolo_register_server_resolver_topic\",\"ts\":1682382300000,\"available\":1,\"topic_count\":19,\"app_count\":1},{\"app\":\"sba_admin\",\"topic\":\"devcolo_register_server_resolver_topic\",\"ts\":1682381700000,\"available\":1,\"topic_count\":19,\"app_count\":1},{\"app\":\"sba_admin\",\"topic\":\"devcolo_register_server_resolver_topic\",\"ts\":1682381400000,\"available\":1,\"topic_count\":19,\"app_count\":1},{\"app\":\"sba_admin\",\"topic\":\"devcolo_register_server_resolver_topic\",\"ts\":1682381100000,\"available\":1,\"topic_count\":19,\"app_count\":1},{\"app\":\"sba_admin\",\"topic\":\"devcolo_register_server_resolver_topic\",\"ts\":1682380800000,\"available\":1,\"topic_count\":19,\"app_count\":1},{\"app\":\"app_logservice\",\"topic\":\"dev_topic_in_meeting_events\",\"ts\":1682383800000,\"available\":1,\"topic_count\":25,\"app_count\":1},{\"app\":\"app_asyncmq\",\"topic\":\"dev_cci_job_customer_expired\",\"ts\":1682383800000,\"available\":1,\"topic_count\":340,\"app_count\":1},{\"app\":\"app_asyncmq\",\"topic\":\"qa_zva_job_bot_engagement_timeout\",\"ts\":1682383800000,\"available\":1,\"topic_count\":340,\"app_count\":1},{\"app\":\"app_asyncmq\",\"topic\":\"zcs_job_topic\",\"ts\":1682383800000,\"available\":1,\"topic_count\":340,\"app_count\":1},{\"app\":\"app_asyncmq\",\"topic\":\"dev01_cci_job_live_video_pending_recording_retry\",\"ts\":1682383800000,\"available\":1,\"topic_count\":340,\"app_count\":1},{\"app\":\"app_asyncmq\",\"topic\":\"dev_cci_job_bot_timeout\",\"ts\":1682383800000,\"available\":1,\"topic_count\":340,\"app_count\":1},{\"app\":\"app_tnsse_uuid\",\"topic\":\"dev_va1_tnsse_uuid_topic\",\"ts\":1682384280000,\"available\":1,\"topic_count\":7,\"app_count\":1},{\"app\":\"app_tnsse_uuid\",\"topic\":\"dev_va1_tnsse_uuid_topic\",\"ts\":1682384160000,\"available\":1,\"topic_count\":7,\"app_count\":1},{\"app\":\"app_tnsse_uuid\",\"topic\":\"dev_va1_tnsse_uuid_topic\",\"ts\":1682384040000,\"available\":1,\"topic_count\":7,\"app_count\":1},{\"app\":\"app_tnsse_uuid\",\"topic\":\"dev_va1_tnsse_uuid_topic\",\"ts\":1682383860000,\"available\":1,\"topic_count\":7,\"app_count\":1},{\"app\":\"app_tnsse_uuid\",\"topic\":\"dev_va1_tnsse_uuid_topic\",\"ts\":1682383740000,\"available\":1,\"topic_count\":7,\"app_count\":1},{\"app\":\"app_tnsse_uuid\",\"topic\":\"dev_va1_tnsse_uuid_topic\",\"ts\":1682383260000,\"available\":1,\"topic_count\":7,\"app_count\":1},{\"app\":\"app_tnsse_uuid\",\"topic\":\"dev_va1_tnsse_uuid_topic\",\"ts\":1682383140000,\"available\":1,\"topic_count\":7,\"app_count\":1},{\"app\":\"app_tnsse_uuid\",\"topic\":\"dev_va1_tnsse_uuid_topic\",\"ts\":1682381220000,\"available\":1,\"topic_count\":7,\"app_count\":1},{\"app\":\"app_tnsse_uuid\",\"topic\":\"dev_va1_tnsse_uuid_topic\",\"ts\":1682380800000,\"available\":1,\"topic_count\":7,\"app_count\":1},{\"app\":\"app_asyncmq\",\"topic\":\"qa_cci_job_live_video_pending_recording_retry\",\"ts\":1682383800000,\"available\":1,\"topic_count\":340,\"app_count\":1},{\"app\":\"app_web\",\"topic\":\"devcolo_WEB_CACHE_SYNC_UP_TOPIC_MEM\",\"ts\":1682384340000,\"available\":1,\"topic_count\":170,\"app_count\":1},{\"app\":\"app_web\",\"topic\":\"devcolo_WEB_CACHE_SYNC_UP_TOPIC_MEM\",\"ts\":1682384280000,\"available\":1,\"topic_count\":170,\"app_count\":1},{\"app\":\"app_web\",\"topic\":\"devcolo_WEB_CACHE_SYNC_UP_TOPIC_MEM\",\"ts\":1682384220000,\"available\":1,\"topic_count\":170,\"app_count\":1},{\"app\":\"app_web\",\"topic\":\"devcolo_WEB_CACHE_SYNC_UP_TOPIC_MEM\",\"ts\":1682384160000,\"available\":1,\"topic_count\":170,\"app_count\":1},{\"app\":\"app_web\",\"topic\":\"devcolo_WEB_CACHE_SYNC_UP_TOPIC_MEM\",\"ts\":1682384100000,\"available\":1,\"topic_count\":170,\"app_count\":1},{\"app\":\"app_web\",\"topic\":\"devcolo_WEB_CACHE_SYNC_UP_TOPIC_MEM\",\"ts\":1682383980000,\"available\":1,\"topic_count\":170,\"app_count\":1},{\"app\":\"app_web\",\"topic\":\"devcolo_WEB_CACHE_SYNC_UP_TOPIC_MEM\",\"ts\":1682384040000,\"available\":1,\"topic_count\":170,\"app_count\":1},{\"app\":\"app_web\",\"topic\":\"devcolo_WEB_CACHE_SYNC_UP_TOPIC_MEM\",\"ts\":1682383920000,\"available\":1,\"topic_count\":170,\"app_count\":1},{\"app\":\"app_web\",\"topic\":\"devcolo_WEB_CACHE_SYNC_UP_TOPIC_MEM\",\"ts\":1682383860000,\"available\":1,\"topic_count\":170,\"app_count\":1},{\"app\":\"app_web\",\"topic\":\"devcolo_WEB_CACHE_SYNC_UP_TOPIC_MEM\",\"ts\":1682383800000,\"available\":1,\"topic_count\":170,\"app_count\":1},{\"app\":\"app_web\",\"topic\":\"devcolo_WEB_CACHE_SYNC_UP_TOPIC_MEM\",\"ts\":1682383740000,\"available\":1,\"topic_count\":170,\"app_count\":1},{\"app\":\"app_web\",\"topic\":\"devcolo_WEB_CACHE_SYNC_UP_TOPIC_MEM\",\"ts\":1682383680000,\"available\":1,\"topic_count\":170,\"app_count\":1},{\"app\":\"app_web\",\"topic\":\"devcolo_WEB_CACHE_SYNC_UP_TOPIC_MEM\",\"ts\":1682383620000,\"available\":1,\"topic_count\":170,\"app_count\":1},{\"app\":\"app_web\",\"topic\":\"devcolo_WEB_CACHE_SYNC_UP_TOPIC_MEM\",\"ts\":1682383560000,\"available\":1,\"topic_count\":170,\"app_count\":1},{\"app\":\"app_web\",\"topic\":\"devcolo_WEB_CACHE_SYNC_UP_TOPIC_MEM\",\"ts\":1682383500000,\"available\":1,\"topic_count\":170,\"app_count\":1},{\"app\":\"app_web\",\"topic\":\"devcolo_WEB_CACHE_SYNC_UP_TOPIC_MEM\",\"ts\":1682383440000,\"available\":1,\"topic_count\":170,\"app_count\":1},{\"app\":\"app_web\",\"topic\":\"devcolo_WEB_CACHE_SYNC_UP_TOPIC_MEM\",\"ts\":1682383380000,\"available\":1,\"topic_count\":170,\"app_count\":1},{\"app\":\"app_web\",\"topic\":\"devcolo_WEB_CACHE_SYNC_UP_TOPIC_MEM\",\"ts\":1682383320000,\"available\":1,\"topic_count\":170,\"app_count\":1},{\"app\":\"app_web\",\"topic\":\"devcolo_WEB_CACHE_SYNC_UP_TOPIC_MEM\",\"ts\":1682383260000,\"available\":1,\"topic_count\":170,\"app_count\":1},{\"app\":\"app_web\",\"topic\":\"devcolo_WEB_CACHE_SYNC_UP_TOPIC_MEM\",\"ts\":1682383200000,\"available\":1,\"topic_count\":170,\"app_count\":1},{\"app\":\"app_web\",\"topic\":\"devcolo_WEB_CACHE_SYNC_UP_TOPIC_MEM\",\"ts\":1682383140000,\"available\":1,\"topic_count\":170,\"app_count\":1},{\"app\":\"app_web\",\"topic\":\"devcolo_WEB_CACHE_SYNC_UP_TOPIC_MEM\",\"ts\":1682383080000,\"available\":1,\"topic_count\":170,\"app_count\":1},{\"app\":\"app_web\",\"topic\":\"devcolo_WEB_CACHE_SYNC_UP_TOPIC_MEM\",\"ts\":1682382960000,\"available\":1,\"topic_count\":170,\"app_count\":1},{\"app\":\"app_web\",\"topic\":\"devcolo_WEB_CACHE_SYNC_UP_TOPIC_MEM\",\"ts\":1682383020000,\"available\":1,\"topic_count\":170,\"app_count\":1},{\"app\":\"app_web\",\"topic\":\"devcolo_WEB_CACHE_SYNC_UP_TOPIC_MEM\",\"ts\":1682382900000,\"available\":1,\"topic_count\":170,\"app_count\":1},{\"app\":\"app_web\",\"topic\":\"devcolo_WEB_CACHE_SYNC_UP_TOPIC_MEM\",\"ts\":1682382840000,\"available\":1,\"topic_count\":170,\"app_count\":1},{\"app\":\"app_web\",\"topic\":\"devcolo_WEB_CACHE_SYNC_UP_TOPIC_MEM\",\"ts\":1682382780000,\"available\":1,\"topic_count\":170,\"app_count\":1},{\"app\":\"app_web\",\"topic\":\"devcolo_WEB_CACHE_SYNC_UP_TOPIC_MEM\",\"ts\":1682382720000,\"available\":1,\"topic_count\":170,\"app_count\":1},{\"app\":\"app_web\",\"topic\":\"devcolo_WEB_CACHE_SYNC_UP_TOPIC_MEM\",\"ts\":1682382660000,\"available\":1,\"topic_count\":170,\"app_count\":1},{\"app\":\"app_web\",\"topic\":\"devcolo_WEB_CACHE_SYNC_UP_TOPIC_MEM\",\"ts\":1682382600000,\"available\":1,\"topic_count\":170,\"app_count\":1},{\"app\":\"app_web\",\"topic\":\"devcolo_WEB_CACHE_SYNC_UP_TOPIC_MEM\",\"ts\":1682382540000,\"available\":1,\"topic_count\":170,\"app_count\":1},{\"app\":\"app_web\",\"topic\":\"devcolo_WEB_CACHE_SYNC_UP_TOPIC_MEM\",\"ts\":1682382480000,\"available\":1,\"topic_count\":170,\"app_count\":1},{\"app\":\"app_web\",\"topic\":\"devcolo_WEB_CACHE_SYNC_UP_TOPIC_MEM\",\"ts\":1682382420000,\"available\":1,\"topic_count\":170,\"app_count\":1},{\"app\":\"app_web\",\"topic\":\"devcolo_WEB_CACHE_SYNC_UP_TOPIC_MEM\",\"ts\":1682382360000,\"available\":1,\"topic_count\":170,\"app_count\":1},{\"app\":\"app_web\",\"topic\":\"devcolo_WEB_CACHE_SYNC_UP_TOPIC_MEM\",\"ts\":1682382180000,\"available\":1,\"topic_count\":170,\"app_count\":1},{\"app\":\"app_web\",\"topic\":\"devcolo_WEB_CACHE_SYNC_UP_TOPIC_MEM\",\"ts\":1682382000000,\"available\":1,\"topic_count\":170,\"app_count\":1},{\"app\":\"app_web\",\"topic\":\"devcolo_WEB_CACHE_SYNC_UP_TOPIC_MEM\",\"ts\":1682382240000,\"available\":1,\"topic_count\":170,\"app_count\":1},{\"app\":\"app_web\",\"topic\":\"devcolo_WEB_CACHE_SYNC_UP_TOPIC_MEM\",\"ts\":1682382060000,\"available\":1,\"topic_count\":170,\"app_count\":1},{\"app\":\"app_web\",\"topic\":\"devcolo_WEB_CACHE_SYNC_UP_TOPIC_MEM\",\"ts\":1682381880000,\"available\":1,\"topic_count\":170,\"app_count\":1},{\"app\":\"app_web\",\"topic\":\"devcolo_WEB_CACHE_SYNC_UP_TOPIC_MEM\",\"ts\":1682382300000,\"available\":1,\"topic_count\":170,\"app_count\":1},{\"app\":\"app_web\",\"topic\":\"devcolo_WEB_CACHE_SYNC_UP_TOPIC_MEM\",\"ts\":1682382120000,\"available\":1,\"topic_count\":170,\"app_count\":1},{\"app\":\"app_web\",\"topic\":\"devcolo_WEB_CACHE_SYNC_UP_TOPIC_MEM\",\"ts\":1682381940000,\"available\":1,\"topic_count\":170,\"app_count\":1},{\"app\":\"app_web\",\"topic\":\"devcolo_WEB_CACHE_SYNC_UP_TOPIC_MEM\",\"ts\":1682381820000,\"available\":1,\"topic_count\":170,\"app_count\":1},{\"app\":\"app_web\",\"topic\":\"devcolo_WEB_CACHE_SYNC_UP_TOPIC_MEM\",\"ts\":1682381760000,\"available\":1,\"topic_count\":170,\"app_count\":1},{\"app\":\"app_web\",\"topic\":\"devcolo_WEB_CACHE_SYNC_UP_TOPIC_MEM\",\"ts\":1682381700000,\"available\":1,\"topic_count\":170,\"app_count\":1},{\"app\":\"app_web\",\"topic\":\"devcolo_WEB_CACHE_SYNC_UP_TOPIC_MEM\",\"ts\":1682381640000,\"available\":1,\"topic_count\":170,\"app_count\":1},{\"app\":\"app_web\",\"topic\":\"devcolo_WEB_CACHE_SYNC_UP_TOPIC_MEM\",\"ts\":1682381580000,\"available\":1,\"topic_count\":170,\"app_count\":1},{\"app\":\"app_web\",\"topic\":\"devcolo_WEB_CACHE_SYNC_UP_TOPIC_MEM\",\"ts\":1682381520000,\"available\":1,\"topic_count\":170,\"app_count\":1},{\"app\":\"app_web\",\"topic\":\"devcolo_WEB_CACHE_SYNC_UP_TOPIC_MEM\",\"ts\":1682381460000,\"available\":1,\"topic_count\":170,\"app_count\":1},{\"app\":\"app_web\",\"topic\":\"devcolo_WEB_CACHE_SYNC_UP_TOPIC_MEM\",\"ts\":1682381400000,\"available\":1,\"topic_count\":170,\"app_count\":1},{\"app\":\"app_web\",\"topic\":\"devcolo_WEB_CACHE_SYNC_UP_TOPIC_MEM\",\"ts\":1682381340000,\"available\":1,\"topic_count\":170,\"app_count\":1},{\"app\":\"app_web\",\"topic\":\"devcolo_WEB_CACHE_SYNC_UP_TOPIC_MEM\",\"ts\":1682381280000,\"available\":1,\"topic_count\":170,\"app_count\":1},{\"app\":\"app_web\",\"topic\":\"devcolo_WEB_CACHE_SYNC_UP_TOPIC_MEM\",\"ts\":1682381220000,\"available\":1,\"topic_count\":170,\"app_count\":1},{\"app\":\"app_web\",\"topic\":\"devcolo_WEB_CACHE_SYNC_UP_TOPIC_MEM\",\"ts\":1682381100000,\"available\":1,\"topic_count\":170,\"app_count\":1},{\"app\":\"app_web\",\"topic\":\"devcolo_WEB_CACHE_SYNC_UP_TOPIC_MEM\",\"ts\":1682381160000,\"available\":1,\"topic_count\":170,\"app_count\":1},{\"app\":\"app_web\",\"topic\":\"devcolo_WEB_CACHE_SYNC_UP_TOPIC_MEM\",\"ts\":1682381040000,\"available\":1,\"topic_count\":170,\"app_count\":1},{\"app\":\"app_web\",\"topic\":\"devcolo_WEB_CACHE_SYNC_UP_TOPIC_MEM\",\"ts\":1682380980000,\"available\":1,\"topic_count\":170,\"app_count\":1},{\"app\":\"app_web\",\"topic\":\"devcolo_WEB_CACHE_SYNC_UP_TOPIC_MEM\",\"ts\":1682380920000,\"available\":1,\"topic_count\":170,\"app_count\":1},{\"app\":\"app_web\",\"topic\":\"devcolo_WEB_CACHE_SYNC_UP_TOPIC_MEM\",\"ts\":1682380860000,\"available\":1,\"topic_count\":170,\"app_count\":1},{\"app\":\"app_web\",\"topic\":\"devcolo_WEB_CACHE_SYNC_UP_TOPIC_MEM\",\"ts\":1682380800000,\"available\":1,\"topic_count\":170,\"app_count\":1},{\"app\":\"app_tnsse_uuid\",\"topic\":\"dev_sgp02_tnsse_uuid_topic\",\"ts\":1682383500000,\"available\":1,\"topic_count\":7,\"app_count\":1},{\"app\":\"app_tnsse_uuid\",\"topic\":\"dev_sgp02_tnsse_uuid_topic\",\"ts\":1682383140000,\"available\":1,\"topic_count\":7,\"app_count\":1},{\"app\":\"app_tnsse_uuid\",\"topic\":\"dev_sgp02_tnsse_uuid_topic\",\"ts\":1682381520000,\"available\":1,\"topic_count\":7,\"app_count\":1},{\"app\":\"app_tnsse_uuid\",\"topic\":\"dev_sgp02_tnsse_uuid_topic\",\"ts\":1682381460000,\"available\":1,\"topic_count\":7,\"app_count\":1},{\"app\":\"app_tnsse_uuid\",\"topic\":\"dev_sgp02_tnsse_uuid_topic\",\"ts\":1682381280000,\"available\":1,\"topic_count\":7,\"app_count\":1},{\"app\":\"app_tnsse_uuid\",\"topic\":\"dev_sgp02_tnsse_uuid_topic\",\"ts\":1682381040000,\"available\":1,\"topic_count\":7,\"app_count\":1},{\"app\":\"app_asyncmq\",\"topic\":\"dev_stable_marketplace_async_task\",\"ts\":1682381760000,\"available\":1,\"topic_count\":340,\"app_count\":2},{\"app\":\"async\",\"topic\":\"test\",\"ts\":1682382840000,\"available\":1,\"topic_count\":25,\"app_count\":1},{\"app\":\"async\",\"topic\":\"test\",\"ts\":1682382780000,\"available\":1,\"topic_count\":25,\"app_count\":1},{\"app\":\"async\",\"topic\":\"test\",\"ts\":1682382720000,\"available\":1,\"topic_count\":25,\"app_count\":1},{\"app\":\"async\",\"topic\":\"test\",\"ts\":1682382660000,\"available\":1,\"topic_count\":25,\"app_count\":1}]";
        PreResp preResp = ajpps.handle(message);
        Assertions.assertEquals(preResp.getPreProcessList().get(0).getMeasure() == null, true);
    }

    @Test
    public void testJson() {
        String message = "{\n" +
                "    \"cubeVer\":\"1.0.0\",\n" +
                "    \"measure\":\"cpu\",\n" +
                "    \"tag\":{\n" +
                "        \"app\":\"cube-sdk\",\n" +
                "        \"host\":\"{E}\",\n" +
                "        \"ip\":\"{E}\"\n" +
                "    },\n" +
                "    \"ts\":1687922347300,\n" +
                "    \"field\":null,\n" +
                "    \"pii\":[\n" +
                "        \"host\",\n" +
                "        \"ip\"\n" +
                "    ],\n" +
                "    \"mapField\":{\n" +
                "        \"RS\":{\n" +
                "            \"TT\":3,\n" +
                "            \"TC\":6\n" +
                "        }\n" +
                "    }\n" +
                "}";
        PreResp preResp = ajpps.handle(message);
        Assertions.assertEquals(preResp.getPreProcessList().get(0).getMeasure().getMapField() != null, true);
    }

    @Test
    public void testMeasureFieldIsNull() {
        String message = "{\n" +
                "    \"cubeVer\":\"1.0.0\",\n" +
                "    \"measure\":\"cpu\",\n" +
                "    \"tag\":{\n" +
                "        \"app\":\"cube-sdk\",\n" +
                "        \"host\":\"{E}abc.ec2{/E}\",\n" +
                "        \"ip\":\"{E}***********{/E}\"\n" +
                "    },\n" +
                "    \"ts\":1687922347300,\n" +
                "    \"field\":null,\n" +
                "    \"pii\":[\n" +
                "        \"host\",\n" +
                "        \"ip\"\n" +
                "    ],\n" +
                "    \"mapField\":{\n" +
                "        \"RS\":{\n" +
                "            \"TT\":3,\n" +
                "            \"TC\":6\n" +
                "        }\n" +
                "    }\n" +
                "}";
        PreResp preResp = ajpps.handle(message);
        Assertions.assertEquals(preResp.getPreProcessList().get(0).getMeasure().getField() != null, true);
    }



    @Test
    public void testJsonToMap() {
        String message = "{\n" +
                "    \"cubeVer\":\"1.0.0\",\n" +
                "    \"measure1\":\"cpu\",\n" +
                "    \"tag1\":{\n" +
                "        \"app\":\"cube-sdk\",\n" +
                "        \"host\":\"{E}abc.ec2{/E}\",\n" +
                "        \"ip\":\"{E}***********{/E}\"\n" +
                "    },\n" +
                "    \"ts1\":1687922347300,\n" +
                "    \"field1\":null,\n" +
                "    \"pi1i\":[\n" +
                "        \"host\",\n" +
                "        \"ip\"\n" +
                "    ],\n" +
                "    \"mapFi1eld\":{\n" +
                "        \"RS\":{\n" +
                "            \"TT\":3,\n" +
                "            \"TC\":6\n" +
                "        }\n" +
                "    }\n" +
                "}";
        PreResp preResp = ajpps.handle(message);
        Assertions.assertEquals(preResp.getPreProcessList().get(0).getMeasure().getField() != null, true);
    }

    @Test
    public void testMeasureTagFieldIsNull() {
        String message = "{\n" +
                "    \"cubeVer\":\"1.0.0\",\n" +
                "    \"measure\":\"cpu\",\n" +
                "    \"tag\":null,\n" +
                "    \"ts\":1687922347300,\n" +
                "    \"field\":null,\n" +
                "    \"pii\":[\n" +
                "        \"host\",\n" +
                "        \"ip\"\n" +
                "    ],\n" +
                "    \"mapField\":{\n" +
                "        \"RS\":{\n" +
                "            \"TT\":3,\n" +
                "            \"TC\":6\n" +
                "        }\n" +
                "    }\n" +
                "}";
        PreResp preResp = ajpps.handle(message);
        Assertions.assertEquals(preResp.getPreProcessList().get(0).getMeasure().getField().size() == 0, true);
        Assertions.assertEquals(preResp.getPreProcessList().get(0).getMeasure().getTag().size() == 0, true);
    }

    @Test
    public void testSupportMap() {
        String message = "[{\"timestamp\":1709617503775,\"source\":\"file:/var/log/compute-cost/emr_compute_cost.json|excludeFields|includeFields\",\"lambdaTimestamp\":1709617503775,\"loggroup\":\"bigdata_bdp_infra_cost\",\"appcluster\":\"Infra_BigData_bdp_superset\",\"logstream\":\"trace_i-0f4a1aab33b134dd7_ip-10-151-41-220_prod\",\"message\":\"{\\\"timestamp\\\": \\\"2024-03-05 05:45:03\\\", \\\"emrClusterId\\\": \\\"j-15B8MCKHDEZHH\\\", \\\"awsRegion\\\": \\\"us-east-1\\\", \\\"costPercentageChange\\\": 0.0}\",\"id\":\"84174513994083658828707431301175304988\",\"unique_id\":\"ip-10-151-41-220_file:/var/log/compute-cost/emr_compute_cost.json|excludeFields|includeFields_1026\",\"path_template\":\"/var/log/compute-cost/*\",\"appName\":\"Infra_BigData_emr\",\"row_num\":\"1026\",\"topType\":\"file\",\"business\":\"\",\"mwName\":\"\",\"labelList\":{}},{\"timestamp\":1709617503775,\"source\":\"file:/var/log/compute-cost/emr_compute_cost.json|excludeFields|includeFields\",\"lambdaTimestamp\":1709617503775,\"loggroup\":\"bigdata_bdp_infra_cost\",\"appcluster\":\"Infra_BigData_bdp_superset\",\"logstream\":\"trace_i-0f4a1aab33b134dd7_ip-10-151-41-220_prod\",\"message\":\"{\\\"timestamp\\\": \\\"2024-03-05 05:45:03\\\", \\\"emrClusterId\\\": \\\"j-166KV0G5G7EZE\\\", \\\"awsRegion\\\": \\\"us-east-2\\\", \\\"costPercentageChange\\\": 0.0}\",\"id\":\"115843143513605909022827114331648110099\",\"unique_id\":\"ip-10-151-41-220_file:/var/log/compute-cost/emr_compute_cost.json|excludeFields|includeFields_1027\",\"path_template\":\"/var/log/compute-cost/*\",\"appName\":\"Infra_BigData_emr\",\"row_num\":\"1027\",\"topType\":\"file\",\"business\":\"\",\"mwName\":\"\",\"labelList\":{}},{\"timestamp\":1709617503775,\"source\":\"file:/var/log/compute-cost/emr_compute_cost.json|excludeFields|includeFields\",\"lambdaTimestamp\":1709617503775,\"loggroup\":\"bigdata_bdp_infra_cost\",\"appcluster\":\"Infra_BigData_bdp_superset\",\"logstream\":\"trace_i-0f4a1aab33b134dd7_ip-10-151-41-220_prod\",\"message\":\"{\\\"timestamp\\\": \\\"2024-03-05 05:45:03\\\", \\\"emrClusterId\\\": \\\"j-17A9HPVHR34I\\\", \\\"awsRegion\\\": \\\"us-east-2\\\", \\\"costPercentageChange\\\": 0.0}\",\"id\":\"1140174497303277265314917902263510824990\",\"unique_id\":\"ip-10-151-41-220_file:/var/log/compute-cost/emr_compute_cost.json|excludeFields|includeFields_1028\",\"path_template\":\"/var/log/compute-cost/*\",\"appName\":\"Infra_BigData_emr\",\"row_num\":\"1028\",\"topType\":\"file\",\"business\":\"\",\"mwName\":\"\",\"labelList\":{}},{\"timestamp\":1709617503775,\"source\":\"file:/var/log/compute-cost/emr_compute_cost.json|excludeFields|includeFields\",\"lambdaTimestamp\":1709617503775,\"loggroup\":\"bigdata_bdp_infra_cost\",\"appcluster\":\"Infra_BigData_bdp_superset\",\"logstream\":\"trace_i-0f4a1aab33b134dd7_ip-10-151-41-220_prod\",\"message\":\"{\\\"timestamp\\\": \\\"2024-03-05 05:45:03\\\", \\\"emrClusterId\\\": \\\"j-17BMSRV0RD7MZ\\\", \\\"awsRegion\\\": \\\"eu-central-1\\\", \\\"costPercentageChange\\\": 0.0}\",\"id\":\"1295565438358832715915086896873341327591\",\"unique_id\":\"ip-10-151-41-220_file:/var/log/compute-cost/emr_compute_cost.json|excludeFields|includeFields_1029\",\"path_template\":\"/var/log/compute-cost/*\",\"appName\":\"Infra_BigData_emr\",\"row_num\":\"1029\",\"topType\":\"file\",\"business\":\"\",\"mwName\":\"\",\"labelList\":{}},{\"timestamp\":1709617503775,\"source\":\"file:/var/log/compute-cost/emr_compute_cost.json|excludeFields|includeFields\",\"lambdaTimestamp\":1709617503775,\"loggroup\":\"bigdata_bdp_infra_cost\",\"appcluster\":\"Infra_BigData_bdp_superset\",\"logstream\":\"trace_i-0f4a1aab33b134dd7_ip-10-151-41-220_prod\",\"message\":\"{\\\"timestamp\\\": \\\"2024-03-05 05:45:03\\\", \\\"emrClusterId\\\": \\\"j-1BKNWU9SLSFZK\\\", \\\"awsRegion\\\": \\\"us-east-1\\\", \\\"costPercentageChange\\\": 0.0}\",\"id\":\"146829931380025622015282206296432494354\",\"unique_id\":\"ip-10-151-41-220_file:/var/log/compute-cost/emr_compute_cost.json|excludeFields|includeFields_1030\",\"path_template\":\"/var/log/compute-cost/*\",\"appName\":\"Infra_BigData_emr\",\"row_num\":\"1030\",\"topType\":\"file\",\"business\":\"\",\"mwName\":\"\",\"labelList\":{}},{\"timestamp\":1709617503775,\"source\":\"file:/var/log/compute-cost/emr_compute_cost.json|excludeFields|includeFields\",\"lambdaTimestamp\":1709617503775,\"loggroup\":\"bigdata_bdp_infra_cost\",\"appcluster\":\"Infra_BigData_bdp_superset\",\"logstream\":\"trace_i-0f4a1aab33b134dd7_ip-10-151-41-220_prod\",\"message\":\"{\\\"timestamp\\\": \\\"2024-03-05 05:45:03\\\", \\\"emrClusterId\\\": \\\"j-1E158W9FRDNTC\\\", \\\"awsRegion\\\": \\\"us-east-1\\\", \\\"costPercentageChange\\\": 0.0}\",\"id\":\"34396636947801942444110682739287205801\",\"unique_id\":\"ip-10-151-41-220_file:/var/log/compute-cost/emr_compute_cost.json|excludeFields|includeFields_1031\",\"path_template\":\"/var/log/compute-cost/*\",\"appName\":\"Infra_BigData_emr\",\"row_num\":\"1031\",\"topType\":\"file\",\"business\":\"\",\"mwName\":\"\",\"labelList\":{}},{\"timestamp\":1709617503775,\"source\":\"file:/var/log/compute-cost/emr_compute_cost.json|excludeFields|includeFields\",\"lambdaTimestamp\":1709617503775,\"loggroup\":\"bigdata_bdp_infra_cost\",\"appcluster\":\"Infra_BigData_bdp_superset\",\"logstream\":\"trace_i-0f4a1aab33b134dd7_ip-10-151-41-220_prod\",\"message\":\"{\\\"timestamp\\\": \\\"2024-03-05 05:45:03\\\", \\\"emrClusterId\\\": \\\"j-1G9LT5UP61CPH\\\", \\\"awsRegion\\\": \\\"ca-central-1\\\", \\\"costPercentageChange\\\": 0.0}\",\"id\":\"118958812516347779574863596544046226980\",\"unique_id\":\"ip-10-151-41-220_file:/var/log/compute-cost/emr_compute_cost.json|excludeFields|includeFields_1032\",\"path_template\":\"/var/log/compute-cost/*\",\"appName\":\"Infra_BigData_emr\",\"row_num\":\"1032\",\"topType\":\"file\",\"business\":\"\",\"mwName\":\"\",\"labelList\":{}},{\"timestamp\":1709617503775,\"source\":\"file:/var/log/compute-cost/emr_compute_cost.json|excludeFields|includeFields\",\"lambdaTimestamp\":1709617503775,\"loggroup\":\"bigdata_bdp_infra_cost\",\"appcluster\":\"Infra_BigData_bdp_superset\",\"logstream\":\"trace_i-0f4a1aab33b134dd7_ip-10-151-41-220_prod\",\"message\":\"{\\\"timestamp\\\": \\\"2024-03-05 05:45:03\\\", \\\"emrClusterId\\\": \\\"j-27VY6GGPX32T9\\\", \\\"awsRegion\\\": \\\"us-east-2\\\", \\\"costPercentageChange\\\": 0.0}\",\"id\":\"167626446633200108095659817061592263182\",\"unique_id\":\"ip-10-151-41-220_file:/var/log/compute-cost/emr_compute_cost.json|excludeFields|includeFields_1033\",\"path_template\":\"/var/log/compute-cost/*\",\"appName\":\"Infra_BigData_emr\",\"row_num\":\"1033\",\"topType\":\"file\",\"business\":\"\",\"mwName\":\"\",\"labelList\":{}},{\"timestamp\":1709617503775,\"source\":\"file:/var/log/compute-cost/emr_compute_cost.json|excludeFields|includeFields\",\"lambdaTimestamp\":1709617503775,\"loggroup\":\"bigdata_bdp_infra_cost\",\"appcluster\":\"Infra_BigData_bdp_superset\",\"logstream\":\"trace_i-0f4a1aab33b134dd7_ip-10-151-41-220_prod\",\"message\":\"{\\\"timestamp\\\": \\\"2024-03-05 05:45:03\\\", \\\"emrClusterId\\\": \\\"j-2AAROXERFAQG0\\\", \\\"awsRegion\\\": \\\"us-east-1\\\", \\\"costPercentageChange\\\": 0.0}\",\"id\":\"90396456666634646256427600130692612719\",\"unique_id\":\"ip-10-151-41-220_file:/var/log/compute-cost/emr_compute_cost.json|excludeFields|includeFields_1034\",\"path_template\":\"/var/log/compute-cost/*\",\"appName\":\"Infra_BigData_emr\",\"row_num\":\"1034\",\"topType\":\"file\",\"business\":\"\",\"mwName\":\"\",\"labelList\":{}},{\"timestamp\":1709617503775,\"source\":\"file:/var/log/compute-cost/emr_compute_cost.json|excludeFields|includeFields\",\"lambdaTimestamp\":1709617503775,\"loggroup\":\"bigdata_bdp_infra_cost\",\"appcluster\":\"Infra_BigData_bdp_superset\",\"logstream\":\"trace_i-0f4a1aab33b134dd7_ip-10-151-41-220_prod\",\"message\":\"{\\\"timestamp\\\": \\\"2024-03-05 05:45:03\\\", \\\"emrClusterId\\\": \\\"j-2C1YHLXI5HNKF\\\", \\\"awsRegion\\\": \\\"ap-southeast-1\\\", \\\"costPercentageChange\\\": 0.0}\",\"id\":\"1543346560282573062914297887984319382931\",\"unique_id\":\"ip-10-151-41-220_file:/var/log/compute-cost/emr_compute_cost.json|excludeFields|includeFields_1035\",\"path_template\":\"/var/log/compute-cost/*\",\"appName\":\"Infra_BigData_emr\",\"row_num\":\"1035\",\"topType\":\"file\",\"business\":\"\",\"mwName\":\"\",\"labelList\":{}},{\"timestamp\":1709617503775,\"source\":\"file:/var/log/compute-cost/emr_compute_cost.json|excludeFields|includeFields\",\"lambdaTimestamp\":1709617503775,\"loggroup\":\"bigdata_bdp_infra_cost\",\"appcluster\":\"Infra_BigData_bdp_superset\",\"logstream\":\"trace_i-0f4a1aab33b134dd7_ip-10-151-41-220_prod\",\"message\":\"{\\\"timestamp\\\": \\\"2024-03-05 05:45:03\\\", \\\"emrClusterId\\\": \\\"j-2C6BF3BNO162Y\\\", \\\"awsRegion\\\": \\\"us-east-2\\\", \\\"costPercentageChange\\\": 0.0}\",\"id\":\"89326869225272811047078655261940308172\",\"unique_id\":\"ip-10-151-41-220_file:/var/log/compute-cost/emr_compute_cost.json|excludeFields|includeFields_1036\",\"path_template\":\"/var/log/compute-cost/*\",\"appName\":\"Infra_BigData_emr\",\"row_num\":\"1036\",\"topType\":\"file\",\"business\":\"\",\"mwName\":\"\",\"labelList\":{}},{\"timestamp\":1709617503775,\"source\":\"file:/var/log/compute-cost/emr_compute_cost.json|excludeFields|includeFields\",\"lambdaTimestamp\":1709617503775,\"loggroup\":\"bigdata_bdp_infra_cost\",\"appcluster\":\"Infra_BigData_bdp_superset\",\"logstream\":\"trace_i-0f4a1aab33b134dd7_ip-10-151-41-220_prod\",\"message\":\"{\\\"timestamp\\\": \\\"2024-03-05 05:45:03\\\", \\\"emrClusterId\\\": \\\"j-2GBHNLHDU4TF2\\\", \\\"awsRegion\\\": \\\"us-east-2\\\", \\\"costPercentageChange\\\": 0.0}\",\"id\":\"702702590198478247112707859372339471564\",\"unique_id\":\"ip-10-151-41-220_file:/var/log/compute-cost/emr_compute_cost.json|excludeFields|includeFields_1037\",\"path_template\":\"/var/log/compute-cost/*\",\"appName\":\"Infra_BigData_emr\",\"row_num\":\"1037\",\"topType\":\"file\",\"business\":\"\",\"mwName\":\"\",\"labelList\":{}},{\"timestamp\":1709617503775,\"source\":\"file:/var/log/compute-cost/emr_compute_cost.json|excludeFields|includeFields\",\"lambdaTimestamp\":1709617503775,\"loggroup\":\"bigdata_bdp_infra_cost\",\"appcluster\":\"Infra_BigData_bdp_superset\",\"logstream\":\"trace_i-0f4a1aab33b134dd7_ip-10-151-41-220_prod\",\"message\":\"{\\\"timestamp\\\": \\\"2024-03-05 05:45:03\\\", \\\"emrClusterId\\\": \\\"j-2GI6PE0Q6P1W1\\\", \\\"awsRegion\\\": \\\"eu-central-1\\\", \\\"costPercentageChange\\\": 0.0}\",\"id\":\"950444923189213009513243316240501695663\",\"unique_id\":\"ip-10-151-41-220_file:/var/log/compute-cost/emr_compute_cost.json|excludeFields|includeFields_1038\",\"path_template\":\"/var/log/compute-cost/*\",\"appName\":\"Infra_BigData_emr\",\"row_num\":\"1038\",\"topType\":\"file\",\"business\":\"\",\"mwName\":\"\",\"labelList\":{}},{\"timestamp\":1709617503775,\"source\":\"file:/var/log/compute-cost/emr_compute_cost.json|excludeFields|includeFields\",\"lambdaTimestamp\":1709617503775,\"loggroup\":\"bigdata_bdp_infra_cost\",\"appcluster\":\"Infra_BigData_bdp_superset\",\"logstream\":\"trace_i-0f4a1aab33b134dd7_ip-10-151-41-220_prod\",\"message\":\"{\\\"timestamp\\\": \\\"2024-03-05 05:45:03\\\", \\\"emrClusterId\\\": \\\"j-2GU9XUGQ6PG8I\\\", \\\"awsRegion\\\": \\\"us-east-2\\\", \\\"costPercentageChange\\\": 0.0}\",\"id\":\"1022785741119436515711416559180207886430\",\"unique_id\":\"ip-10-151-41-220_file:/var/log/compute-cost/emr_compute_cost.json|excludeFields|includeFields_1039\",\"path_template\":\"/var/log/compute-cost/*\",\"appName\":\"Infra_BigData_emr\",\"row_num\":\"1039\",\"topType\":\"file\",\"business\":\"\",\"mwName\":\"\",\"labelList\":{}},{\"timestamp\":1709617503775,\"source\":\"file:/var/log/compute-cost/emr_compute_cost.json|excludeFields|includeFields\",\"lambdaTimestamp\":1709617503775,\"loggroup\":\"bigdata_bdp_infra_cost\",\"appcluster\":\"Infra_BigData_bdp_superset\",\"logstream\":\"trace_i-0f4a1aab33b134dd7_ip-10-151-41-220_prod\",\"message\":\"{\\\"timestamp\\\": \\\"2024-03-05 05:45:03\\\", \\\"emrClusterId\\\": \\\"j-2IGSXIUUJGVU7\\\", \\\"awsRegion\\\": \\\"ap-south-1\\\", \\\"costPercentageChange\\\": 0.0}\",\"id\":\"176983661695546789385193297082340210394\",\"unique_id\":\"ip-10-151-41-220_file:/var/log/compute-cost/emr_compute_cost.json|excludeFields|includeFields_1040\",\"path_template\":\"/var/log/compute-cost/*\",\"appName\":\"Infra_BigData_emr\",\"row_num\":\"1040\",\"topType\":\"file\",\"business\":\"\",\"mwName\":\"\",\"labelList\":{}},{\"timestamp\":1709617503775,\"source\":\"file:/var/log/compute-cost/emr_compute_cost.json|excludeFields|includeFields\",\"lambdaTimestamp\":1709617503775,\"loggroup\":\"bigdata_bdp_infra_cost\",\"appcluster\":\"Infra_BigData_bdp_superset\",\"logstream\":\"trace_i-0f4a1aab33b134dd7_ip-10-151-41-220_prod\",\"message\":\"{\\\"timestamp\\\": \\\"2024-03-05 05:45:03\\\", \\\"emrClusterId\\\": \\\"j-2QRTGBK75UWD8\\\", \\\"awsRegion\\\": \\\"ca-central-1\\\", \\\"costPercentageChange\\\": 0.0}\",\"id\":\"1807133342498589775013888780023920288554\",\"unique_id\":\"ip-10-151-41-220_file:/var/log/compute-cost/emr_compute_cost.json|excludeFields|includeFields_1041\",\"path_template\":\"/var/log/compute-cost/*\",\"appName\":\"Infra_BigData_emr\",\"row_num\":\"1041\",\"topType\":\"file\",\"business\":\"\",\"mwName\":\"\",\"labelList\":{}},{\"timestamp\":1709617503775,\"source\":\"file:/var/log/compute-cost/emr_compute_cost.json|excludeFields|includeFields\",\"lambdaTimestamp\":1709617503775,\"loggroup\":\"bigdata_bdp_infra_cost\",\"appcluster\":\"Infra_BigData_bdp_superset\",\"logstream\":\"trace_i-0f4a1aab33b134dd7_ip-10-151-41-220_prod\",\"message\":\"{\\\"timestamp\\\": \\\"2024-03-05 05:45:03\\\", \\\"emrClusterId\\\": \\\"j-2TC1XGGVDO1O7\\\", \\\"awsRegion\\\": \\\"ap-southeast-2\\\", \\\"costPercentageChange\\\": 0.0}\",\"id\":\"30970597171935934518155169764853388319\",\"unique_id\":\"ip-10-151-41-220_file:/var/log/compute-cost/emr_compute_cost.json|excludeFields|includeFields_1042\",\"path_template\":\"/var/log/compute-cost/*\",\"appName\":\"Infra_BigData_emr\",\"row_num\":\"1042\",\"topType\":\"file\",\"business\":\"\",\"mwName\":\"\",\"labelList\":{}},{\"timestamp\":1709617503775,\"source\":\"file:/var/log/compute-cost/emr_compute_cost.json|excludeFields|includeFields\",\"lambdaTimestamp\":1709617503775,\"loggroup\":\"bigdata_bdp_infra_cost\",\"appcluster\":\"Infra_BigData_bdp_superset\",\"logstream\":\"trace_i-0f4a1aab33b134dd7_ip-10-151-41-220_prod\",\"message\":\"{\\\"timestamp\\\": \\\"2024-03-05 05:45:03\\\", \\\"emrClusterId\\\": \\\"j-2U23YAPKNR0Z7\\\", \\\"awsRegion\\\": \\\"ap-south-1\\\", \\\"costPercentageChange\\\": 0.0}\",\"id\":\"16034073350748798445548797625148326399\",\"unique_id\":\"ip-10-151-41-220_file:/var/log/compute-cost/emr_compute_cost.json|excludeFields|includeFields_1043\",\"path_template\":\"/var/log/compute-cost/*\",\"appName\":\"Infra_BigData_emr\",\"row_num\":\"1043\",\"topType\":\"file\",\"business\":\"\",\"mwName\":\"\",\"labelList\":{}},{\"timestamp\":1709617503775,\"source\":\"file:/var/log/compute-cost/emr_compute_cost.json|excludeFields|includeFields\",\"lambdaTimestamp\":1709617503775,\"loggroup\":\"bigdata_bdp_infra_cost\",\"appcluster\":\"Infra_BigData_bdp_superset\",\"logstream\":\"trace_i-0f4a1aab33b134dd7_ip-10-151-41-220_prod\",\"message\":\"{\\\"timestamp\\\": \\\"2024-03-05 05:45:03\\\", \\\"emrClusterId\\\": \\\"j-3194YR8FTBPG1\\\", \\\"awsRegion\\\": \\\"us-east-2\\\", \\\"costPercentageChange\\\": 0.0}\",\"id\":\"144238272359619156748542645698011771970\",\"unique_id\":\"ip-10-151-41-220_file:/var/log/compute-cost/emr_compute_cost.json|excludeFields|includeFields_1044\",\"path_template\":\"/var/log/compute-cost/*\",\"appName\":\"Infra_BigData_emr\",\"row_num\":\"1044\",\"topType\":\"file\",\"business\":\"\",\"mwName\":\"\",\"labelList\":{}},{\"timestamp\":1709617503775,\"source\":\"file:/var/log/compute-cost/emr_compute_cost.json|excludeFields|includeFields\",\"lambdaTimestamp\":1709617503775,\"loggroup\":\"bigdata_bdp_infra_cost\",\"appcluster\":\"Infra_BigData_bdp_superset\",\"logstream\":\"trace_i-0f4a1aab33b134dd7_ip-10-151-41-220_prod\",\"message\":\"{\\\"timestamp\\\": \\\"2024-03-05 05:45:03\\\", \\\"emrClusterId\\\": \\\"j-36CLIB444VC44\\\", \\\"awsRegion\\\": \\\"us-east-2\\\", \\\"costPercentageChange\\\": 0.0}\",\"id\":\"660119109247262618912312404032771238407\",\"unique_id\":\"ip-10-151-41-220_file:/var/log/compute-cost/emr_compute_cost.json|excludeFields|includeFields_1045\",\"path_template\":\"/var/log/compute-cost/*\",\"appName\":\"Infra_BigData_emr\",\"row_num\":\"1045\",\"topType\":\"file\",\"business\":\"\",\"mwName\":\"\",\"labelList\":{}},{\"timestamp\":1709617503775,\"source\":\"file:/var/log/compute-cost/emr_compute_cost.json|excludeFields|includeFields\",\"lambdaTimestamp\":1709617503775,\"loggroup\":\"bigdata_bdp_infra_cost\",\"appcluster\":\"Infra_BigData_bdp_superset\",\"logstream\":\"trace_i-0f4a1aab33b134dd7_ip-10-151-41-220_prod\",\"message\":\"{\\\"timestamp\\\": \\\"2024-03-05 05:45:03\\\", \\\"emrClusterId\\\": \\\"j-37HY34FXI09QT\\\", \\\"awsRegion\\\": \\\"eu-central-1\\\", \\\"costPercentageChange\\\": 0.0}\",\"id\":\"92033581041387903529684505470904703953\",\"unique_id\":\"ip-10-151-41-220_file:/var/log/compute-cost/emr_compute_cost.json|excludeFields|includeFields_1046\",\"path_template\":\"/var/log/compute-cost/*\",\"appName\":\"Infra_BigData_emr\",\"row_num\":\"1046\",\"topType\":\"file\",\"business\":\"\",\"mwName\":\"\",\"labelList\":{}},{\"timestamp\":1709617503775,\"source\":\"file:/var/log/compute-cost/emr_compute_cost.json|excludeFields|includeFields\",\"lambdaTimestamp\":1709617503775,\"loggroup\":\"bigdata_bdp_infra_cost\",\"appcluster\":\"Infra_BigData_bdp_superset\",\"logstream\":\"trace_i-0f4a1aab33b134dd7_ip-10-151-41-220_prod\",\"message\":\"{\\\"timestamp\\\": \\\"2024-03-05 05:45:03\\\", \\\"emrClusterId\\\": \\\"j-37YSFFBX1ZGT\\\", \\\"awsRegion\\\": \\\"eu-central-1\\\", \\\"costPercentageChange\\\": 0.0}\",\"id\":\"107710139490327548962545225941253338821\",\"unique_id\":\"ip-10-151-41-220_file:/var/log/compute-cost/emr_compute_cost.json|excludeFields|includeFields_1047\",\"path_template\":\"/var/log/compute-cost/*\",\"appName\":\"Infra_BigData_emr\",\"row_num\":\"1047\",\"topType\":\"file\",\"business\":\"\",\"mwName\":\"\",\"labelList\":{}},{\"timestamp\":1709617503775,\"source\":\"file:/var/log/compute-cost/emr_compute_cost.json|excludeFields|includeFields\",\"lambdaTimestamp\":1709617503775,\"loggroup\":\"bigdata_bdp_infra_cost\",\"appcluster\":\"Infra_BigData_bdp_superset\",\"logstream\":\"trace_i-0f4a1aab33b134dd7_ip-10-151-41-220_prod\",\"message\":\"{\\\"timestamp\\\": \\\"2024-03-05 05:45:03\\\", \\\"emrClusterId\\\": \\\"j-3B31DV8GQ0B9H\\\", \\\"awsRegion\\\": \\\"us-east-1\\\", \\\"costPercentageChange\\\": 0.0}\",\"id\":\"157623772380783293145688279896006611596\",\"unique_id\":\"ip-10-151-41-220_file:/var/log/compute-cost/emr_compute_cost.json|excludeFields|includeFields_1048\",\"path_template\":\"/var/log/compute-cost/*\",\"appName\":\"Infra_BigData_emr\",\"row_num\":\"1048\",\"topType\":\"file\",\"business\":\"\",\"mwName\":\"\",\"labelList\":{}},{\"timestamp\":1709617503775,\"source\":\"file:/var/log/compute-cost/emr_compute_cost.json|excludeFields|includeFields\",\"lambdaTimestamp\":1709617503775,\"loggroup\":\"bigdata_bdp_infra_cost\",\"appcluster\":\"Infra_BigData_bdp_superset\",\"logstream\":\"trace_i-0f4a1aab33b134dd7_ip-10-151-41-220_prod\",\"message\":\"{\\\"timestamp\\\": \\\"2024-03-05 05:45:03\\\", \\\"emrClusterId\\\": \\\"j-3BYOR2M1Q320K\\\", \\\"awsRegion\\\": \\\"us-east-2\\\", \\\"costPercentageChange\\\": 0.0}\",\"id\":\"170070027951447644111286052986542951734\",\"unique_id\":\"ip-10-151-41-220_file:/var/log/compute-cost/emr_compute_cost.json|excludeFields|includeFields_1049\",\"path_template\":\"/var/log/compute-cost/*\",\"appName\":\"Infra_BigData_emr\",\"row_num\":\"1049\",\"topType\":\"file\",\"business\":\"\",\"mwName\":\"\",\"labelList\":{}},{\"timestamp\":1709617503775,\"source\":\"file:/var/log/compute-cost/emr_compute_cost.json|excludeFields|includeFields\",\"lambdaTimestamp\":1709617503775,\"loggroup\":\"bigdata_bdp_infra_cost\",\"appcluster\":\"Infra_BigData_bdp_superset\",\"logstream\":\"trace_i-0f4a1aab33b134dd7_ip-10-151-41-220_prod\",\"message\":\"{\\\"timestamp\\\": \\\"2024-03-05 05:45:03\\\", \\\"emrClusterId\\\": \\\"j-3CSZQAP7UI0OU\\\", \\\"awsRegion\\\": \\\"us-east-2\\\", \\\"costPercentageChange\\\": 0.0}\",\"id\":\"105333158687720157926668448824764587235\",\"unique_id\":\"ip-10-151-41-220_file:/var/log/compute-cost/emr_compute_cost.json|excludeFields|includeFields_1050\",\"path_template\":\"/var/log/compute-cost/*\",\"appName\":\"Infra_BigData_emr\",\"row_num\":\"1050\",\"topType\":\"file\",\"business\":\"\",\"mwName\":\"\",\"labelList\":{}},{\"timestamp\":1709617503775,\"source\":\"file:/var/log/compute-cost/emr_compute_cost.json|excludeFields|includeFields\",\"lambdaTimestamp\":1709617503775,\"loggroup\":\"bigdata_bdp_infra_cost\",\"appcluster\":\"Infra_BigData_bdp_superset\",\"logstream\":\"trace_i-0f4a1aab33b134dd7_ip-10-151-41-220_prod\",\"message\":\"{\\\"timestamp\\\": \\\"2024-03-05 05:45:03\\\", \\\"emrClusterId\\\": \\\"j-3DE9RA96QPA72\\\", \\\"awsRegion\\\": \\\"us-east-2\\\", \\\"costPercentageChange\\\": -40.16}\",\"id\":\"124339027019837934773566429871885179205\",\"unique_id\":\"ip-10-151-41-220_file:/var/log/compute-cost/emr_compute_cost.json|excludeFields|includeFields_1051\",\"path_template\":\"/var/log/compute-cost/*\",\"appName\":\"Infra_BigData_emr\",\"row_num\":\"1051\",\"topType\":\"file\",\"business\":\"\",\"mwName\":\"\",\"labelList\":{}},{\"timestamp\":1709617503775,\"source\":\"file:/var/log/compute-cost/emr_compute_cost.json|excludeFields|includeFields\",\"lambdaTimestamp\":1709617503775,\"loggroup\":\"bigdata_bdp_infra_cost\",\"appcluster\":\"Infra_BigData_bdp_superset\",\"logstream\":\"trace_i-0f4a1aab33b134dd7_ip-10-151-41-220_prod\",\"message\":\"{\\\"timestamp\\\": \\\"2024-03-05 05:45:03\\\", \\\"emrClusterId\\\": \\\"j-3FDODDLA46XL9\\\", \\\"awsRegion\\\": \\\"us-east-2\\\", \\\"costPercentageChange\\\": 0.0}\",\"id\":\"687927911592875350513703625838108048578\",\"unique_id\":\"ip-10-151-41-220_file:/var/log/compute-cost/emr_compute_cost.json|excludeFields|includeFields_1052\",\"path_template\":\"/var/log/compute-cost/*\",\"appName\":\"Infra_BigData_emr\",\"row_num\":\"1052\",\"topType\":\"file\",\"business\":\"\",\"mwName\":\"\",\"labelList\":{}},{\"timestamp\":1709617503775,\"source\":\"file:/var/log/compute-cost/emr_compute_cost.json|excludeFields|includeFields\",\"lambdaTimestamp\":1709617503775,\"loggroup\":\"bigdata_bdp_infra_cost\",\"appcluster\":\"Infra_BigData_bdp_superset\",\"logstream\":\"trace_i-0f4a1aab33b134dd7_ip-10-151-41-220_prod\",\"message\":\"{\\\"timestamp\\\": \\\"2024-03-05 05:45:03\\\", \\\"emrClusterId\\\": \\\"j-3FXAGBTUV508Q\\\", \\\"awsRegion\\\": \\\"us-east-2\\\", \\\"costPercentageChange\\\": 0.0}\",\"id\":\"50351676826795977085866173223587475115\",\"unique_id\":\"ip-10-151-41-220_file:/var/log/compute-cost/emr_compute_cost.json|excludeFields|includeFields_1053\",\"path_template\":\"/var/log/compute-cost/*\",\"appName\":\"Infra_BigData_emr\",\"row_num\":\"1053\",\"topType\":\"file\",\"business\":\"\",\"mwName\":\"\",\"labelList\":{}},{\"timestamp\":1709617503775,\"source\":\"file:/var/log/compute-cost/emr_compute_cost.json|excludeFields|includeFields\",\"lambdaTimestamp\":1709617503775,\"loggroup\":\"bigdata_bdp_infra_cost\",\"appcluster\":\"Infra_BigData_bdp_superset\",\"logstream\":\"trace_i-0f4a1aab33b134dd7_ip-10-151-41-220_prod\",\"message\":\"{\\\"timestamp\\\": \\\"2024-03-05 05:45:03\\\", \\\"emrClusterId\\\": \\\"j-3HH2YNM3AO2S8\\\", \\\"awsRegion\\\": \\\"us-east-2\\\", \\\"costPercentageChange\\\": 0.0}\",\"id\":\"93116133302113100557450244996799853465\",\"unique_id\":\"ip-10-151-41-220_file:/var/log/compute-cost/emr_compute_cost.json|excludeFields|includeFields_1054\",\"path_template\":\"/var/log/compute-cost/*\",\"appName\":\"Infra_BigData_emr\",\"row_num\":\"1054\",\"topType\":\"file\",\"business\":\"\",\"mwName\":\"\",\"labelList\":{}},{\"timestamp\":1709617503775,\"source\":\"file:/var/log/compute-cost/emr_compute_cost.json|excludeFields|includeFields\",\"lambdaTimestamp\":1709617503775,\"loggroup\":\"bigdata_bdp_infra_cost\",\"appcluster\":\"Infra_BigData_bdp_superset\",\"logstream\":\"trace_i-0f4a1aab33b134dd7_ip-10-151-41-220_prod\",\"message\":\"{\\\"timestamp\\\": \\\"2024-03-05 05:45:03\\\", \\\"emrClusterId\\\": \\\"j-3JFR82VROYY28\\\", \\\"awsRegion\\\": \\\"us-east-2\\\", \\\"costPercentageChange\\\": 0.0}\",\"id\":\"1402879822440917946511818881250762302335\",\"unique_id\":\"ip-10-151-41-220_file:/var/log/compute-cost/emr_compute_cost.json|excludeFields|includeFields_1055\",\"path_template\":\"/var/log/compute-cost/*\",\"appName\":\"Infra_BigData_emr\",\"row_num\":\"1055\",\"topType\":\"file\",\"business\":\"\",\"mwName\":\"\",\"labelList\":{}},{\"timestamp\":1709617503775,\"source\":\"file:/var/log/compute-cost/emr_compute_cost.json|excludeFields|includeFields\",\"lambdaTimestamp\":1709617503775,\"loggroup\":\"bigdata_bdp_infra_cost\",\"appcluster\":\"Infra_BigData_bdp_superset\",\"logstream\":\"trace_i-0f4a1aab33b134dd7_ip-10-151-41-220_prod\",\"message\":\"{\\\"timestamp\\\": \\\"2024-03-05 05:45:03\\\", \\\"emrClusterId\\\": \\\"j-3N8ODO1352JUC\\\", \\\"awsRegion\\\": \\\"us-east-2\\\", \\\"costPercentageChange\\\": 0.0}\",\"id\":\"1154176495857428699214785514999538853411\",\"unique_id\":\"ip-10-151-41-220_file:/var/log/compute-cost/emr_compute_cost.json|excludeFields|includeFields_1056\",\"path_template\":\"/var/log/compute-cost/*\",\"appName\":\"Infra_BigData_emr\",\"row_num\":\"1056\",\"topType\":\"file\",\"business\":\"\",\"mwName\":\"\",\"labelList\":{}},{\"timestamp\":1709617503775,\"source\":\"file:/var/log/compute-cost/emr_compute_cost.json|excludeFields|includeFields\",\"lambdaTimestamp\":1709617503775,\"loggroup\":\"bigdata_bdp_infra_cost\",\"appcluster\":\"Infra_BigData_bdp_superset\",\"logstream\":\"trace_i-0f4a1aab33b134dd7_ip-10-151-41-220_prod\",\"message\":\"{\\\"timestamp\\\": \\\"2024-03-05 05:45:03\\\", \\\"emrClusterId\\\": \\\"j-3OJST0306A1T1\\\", \\\"awsRegion\\\": \\\"ap-southeast-2\\\", \\\"costPercentageChange\\\": 0.0}\",\"id\":\"1432640240510655071815966460673725331669\",\"unique_id\":\"ip-10-151-41-220_file:/var/log/compute-cost/emr_compute_cost.json|excludeFields|includeFields_1057\",\"path_template\":\"/var/log/compute-cost/*\",\"appName\":\"Infra_BigData_emr\",\"row_num\":\"1057\",\"topType\":\"file\",\"business\":\"\",\"mwName\":\"\",\"labelList\":{}},{\"timestamp\":1709617503775,\"source\":\"file:/var/log/compute-cost/emr_compute_cost.json|excludeFields|includeFields\",\"lambdaTimestamp\":1709617503775,\"loggroup\":\"bigdata_bdp_infra_cost\",\"appcluster\":\"Infra_BigData_bdp_superset\",\"logstream\":\"trace_i-0f4a1aab33b134dd7_ip-10-151-41-220_prod\",\"message\":\"{\\\"timestamp\\\": \\\"2024-03-05 05:45:03\\\", \\\"emrClusterId\\\": \\\"j-3PD48V5BHY0PP\\\", \\\"awsRegion\\\": \\\"ca-central-1\\\", \\\"costPercentageChange\\\": 0.0}\",\"id\":\"28244818084356392768246930434389745152\",\"unique_id\":\"ip-10-151-41-220_file:/var/log/compute-cost/emr_compute_cost.json|excludeFields|includeFields_1058\",\"path_template\":\"/var/log/compute-cost/*\",\"appName\":\"Infra_BigData_emr\",\"row_num\":\"1058\",\"topType\":\"file\",\"business\":\"\",\"mwName\":\"\",\"labelList\":{}},{\"timestamp\":1709617503775,\"source\":\"file:/var/log/compute-cost/emr_compute_cost.json|excludeFields|includeFields\",\"lambdaTimestamp\":1709617503775,\"loggroup\":\"bigdata_bdp_infra_cost\",\"appcluster\":\"Infra_BigData_bdp_superset\",\"logstream\":\"trace_i-0f4a1aab33b134dd7_ip-10-151-41-220_prod\",\"message\":\"{\\\"timestamp\\\": \\\"2024-03-05 05:45:03\\\", \\\"emrClusterId\\\": \\\"j-3RNYD8A0N6GF1\\\", \\\"awsRegion\\\": \\\"us-east-2\\\", \\\"costPercentageChange\\\": 0.0}\",\"id\":\"1812266119338326678816864056466512212401\",\"unique_id\":\"ip-10-151-41-220_file:/var/log/compute-cost/emr_compute_cost.json|excludeFields|includeFields_1059\",\"path_template\":\"/var/log/compute-cost/*\",\"appName\":\"Infra_BigData_emr\",\"row_num\":\"1059\",\"topType\":\"file\",\"business\":\"\",\"mwName\":\"\",\"labelList\":{}},{\"timestamp\":1709617503775,\"source\":\"file:/var/log/compute-cost/emr_compute_cost.json|excludeFields|includeFields\",\"lambdaTimestamp\":1709617503775,\"loggroup\":\"bigdata_bdp_infra_cost\",\"appcluster\":\"Infra_BigData_bdp_superset\",\"logstream\":\"trace_i-0f4a1aab33b134dd7_ip-10-151-41-220_prod\",\"message\":\"{\\\"timestamp\\\": \\\"2024-03-05 05:45:03\\\", \\\"emrClusterId\\\": \\\"j-3S93PTVWTO7A0\\\", \\\"awsRegion\\\": \\\"us-east-2\\\", \\\"costPercentageChange\\\": 0.0}\",\"id\":\"176457432838424380259685741503088621306\",\"unique_id\":\"ip-10-151-41-220_file:/var/log/compute-cost/emr_compute_cost.json|excludeFields|includeFields_1060\",\"path_template\":\"/var/log/compute-cost/*\",\"appName\":\"Infra_BigData_emr\",\"row_num\":\"1060\",\"topType\":\"file\",\"business\":\"\",\"mwName\":\"\",\"labelList\":{}},{\"timestamp\":1709617503775,\"source\":\"file:/var/log/compute-cost/emr_compute_cost.json|excludeFields|includeFields\",\"lambdaTimestamp\":1709617503775,\"loggroup\":\"bigdata_bdp_infra_cost\",\"appcluster\":\"Infra_BigData_bdp_superset\",\"logstream\":\"trace_i-0f4a1aab33b134dd7_ip-10-151-41-220_prod\",\"message\":\"{\\\"timestamp\\\": \\\"2024-03-05 05:45:03\\\", \\\"emrClusterId\\\": \\\"j-CU7TTIA13593\\\", \\\"awsRegion\\\": \\\"us-east-2\\\", \\\"costPercentageChange\\\": 0.0}\",\"id\":\"604889952487237808417820312009936328418\",\"unique_id\":\"ip-10-151-41-220_file:/var/log/compute-cost/emr_compute_cost.json|excludeFields|includeFields_1061\",\"path_template\":\"/var/log/compute-cost/*\",\"appName\":\"Infra_BigData_emr\",\"row_num\":\"1061\",\"topType\":\"file\",\"business\":\"\",\"mwName\":\"\",\"labelList\":{}},{\"timestamp\":1709617503775,\"source\":\"file:/var/log/compute-cost/emr_compute_cost.json|excludeFields|includeFields\",\"lambdaTimestamp\":1709617503775,\"loggroup\":\"bigdata_bdp_infra_cost\",\"appcluster\":\"Infra_BigData_bdp_superset\",\"logstream\":\"trace_i-0f4a1aab33b134dd7_ip-10-151-41-220_prod\",\"message\":\"{\\\"timestamp\\\": \\\"2024-03-05 05:45:03\\\", \\\"emrClusterId\\\": \\\"j-D1XHYZSK7055\\\", \\\"awsRegion\\\": \\\"us-east-2\\\", \\\"costPercentageChange\\\": 0.0}\",\"id\":\"22843960705499385211173300803709534182\",\"unique_id\":\"ip-10-151-41-220_file:/var/log/compute-cost/emr_compute_cost.json|excludeFields|includeFields_1062\",\"path_template\":\"/var/log/compute-cost/*\",\"appName\":\"Infra_BigData_emr\",\"row_num\":\"1062\",\"topType\":\"file\",\"business\":\"\",\"mwName\":\"\",\"labelList\":{}},{\"timestamp\":1709617503775,\"source\":\"file:/var/log/compute-cost/emr_compute_cost.json|excludeFields|includeFields\",\"lambdaTimestamp\":1709617503775,\"loggroup\":\"bigdata_bdp_infra_cost\",\"appcluster\":\"Infra_BigData_bdp_superset\",\"logstream\":\"trace_i-0f4a1aab33b134dd7_ip-10-151-41-220_prod\",\"message\":\"{\\\"timestamp\\\": \\\"2024-03-05 05:45:03\\\", \\\"emrClusterId\\\": \\\"j-H8VLOZ3R29GS\\\", \\\"awsRegion\\\": \\\"ap-southeast-2\\\", \\\"costPercentageChange\\\": 0.0}\",\"id\":\"17473430532446375946287697644026349420\",\"unique_id\":\"ip-10-151-41-220_file:/var/log/compute-cost/emr_compute_cost.json|excludeFields|includeFields_1063\",\"path_template\":\"/var/log/compute-cost/*\",\"appName\":\"Infra_BigData_emr\",\"row_num\":\"1063\",\"topType\":\"file\",\"business\":\"\",\"mwName\":\"\",\"labelList\":{}},{\"timestamp\":1709617503775,\"source\":\"file:/var/log/compute-cost/emr_compute_cost.json|excludeFields|includeFields\",\"lambdaTimestamp\":1709617503775,\"loggroup\":\"bigdata_bdp_infra_cost\",\"appcluster\":\"Infra_BigData_bdp_superset\",\"logstream\":\"trace_i-0f4a1aab33b134dd7_ip-10-151-41-220_prod\",\"message\":\"{\\\"timestamp\\\": \\\"2024-03-05 05:45:03\\\", \\\"emrClusterId\\\": \\\"j-QCT622426A7\\\", \\\"awsRegion\\\": \\\"us-east-2\\\", \\\"costPercentageChange\\\": 0}\",\"id\":\"143762392930455220625379975248789945518\",\"unique_id\":\"ip-10-151-41-220_file:/var/log/compute-cost/emr_compute_cost.json|excludeFields|includeFields_1064\",\"path_template\":\"/var/log/compute-cost/*\",\"appName\":\"Infra_BigData_emr\",\"row_num\":\"1064\",\"topType\":\"file\",\"business\":\"\",\"mwName\":\"\",\"labelList\":{}},{\"timestamp\":1709617503775,\"source\":\"file:/var/log/compute-cost/emr_compute_cost.json|excludeFields|includeFields\",\"lambdaTimestamp\":1709617503775,\"loggroup\":\"bigdata_bdp_infra_cost\",\"appcluster\":\"Infra_BigData_bdp_superset\",\"logstream\":\"trace_i-0f4a1aab33b134dd7_ip-10-151-41-220_prod\",\"message\":\"{\\\"timestamp\\\": \\\"2024-03-05 05:45:03\\\", \\\"emrClusterId\\\": \\\"j-WPQAN0UQKCYQ\\\", \\\"awsRegion\\\": \\\"us-east-2\\\", \\\"costPercentageChange\\\": 0.0}\",\"id\":\"38146805726037081111959323749784543401\",\"unique_id\":\"ip-10-151-41-220_file:/var/log/compute-cost/emr_compute_cost.json|excludeFields|includeFields_1065\",\"path_template\":\"/var/log/compute-cost/*\",\"appName\":\"Infra_BigData_emr\",\"row_num\":\"1065\",\"topType\":\"file\",\"business\":\"\",\"mwName\":\"\",\"labelList\":{}},{\"timestamp\":1709617503775,\"source\":\"file:/var/log/compute-cost/emr_compute_cost.json|excludeFields|includeFields\",\"lambdaTimestamp\":1709617503775,\"loggroup\":\"bigdata_bdp_infra_cost\",\"appcluster\":\"Infra_BigData_bdp_superset\",\"logstream\":\"trace_i-0f4a1aab33b134dd7_ip-10-151-41-220_prod\",\"message\":\"{\\\"timestamp\\\": \\\"2024-03-05 05:45:03\\\", \\\"emrClusterId\\\": \\\"j-XKTZL37WM4MF\\\", \\\"awsRegion\\\": \\\"us-east-2\\\", \\\"costPercentageChange\\\": 0.0}\",\"id\":\"465909441804963723616236246578732889768\",\"unique_id\":\"ip-10-151-41-220_file:/var/log/compute-cost/emr_compute_cost.json|excludeFields|includeFields_1066\",\"path_template\":\"/var/log/compute-cost/*\",\"appName\":\"Infra_BigData_emr\",\"row_num\":\"1066\",\"topType\":\"file\",\"business\":\"\",\"mwName\":\"\",\"labelList\":{}}]";
        PreResp preResp = ajpps.handle(message);
        Assertions.assertEquals(preResp.getPreProcessList().get(0).getMap().size() == 18, true);
        ajpps.handle(JsonUtils.toJsonStringIgnoreExp(getSingleAgentMsgSingleMessage(Maps.newHashMap(), message)));
        Assertions.assertEquals(preResp.getPreProcessList().get(0).getMap().size() == 18, true);
    }
}