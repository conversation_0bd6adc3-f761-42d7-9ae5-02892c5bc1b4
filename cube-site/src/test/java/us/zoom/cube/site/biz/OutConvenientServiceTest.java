package us.zoom.cube.site.biz;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ActiveProfiles;
import us.zoom.cube.lib.utils.JsonUtils;
import us.zoom.cube.site.lib.input.FixMlarxDataInput;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;

@ActiveProfiles("perf_for_local")
public class OutConvenientServiceTest extends BaseTest {

    @Autowired
    private OutConvenientService outConvenientService;

    @Test
    public void fixMlarxData() {
        FixMlarxDataInput fixMlarxDataInput = new FixMlarxDataInput();
        fixMlarxDataInput.setBegin(1742965200000L);
        fixMlarxDataInput.setEnd(1742967682919L);
        fixMlarxDataInput.setServiceName("Infra_Monitor_Mlarx");
        fixMlarxDataInput.setTableName("infoLog");
        fixMlarxDataInput.setTopic("us_zdca_zn_mmr_monitor_mlarx_dev_dev");
        fixMlarxDataInput.setType("55F269B4-730F-4DD9-98BE-138DB7F12D5C");
        outConvenientService.fixMlarxData(fixMlarxDataInput);
    }

    @Test
    public void testByte() {
        String finalMessage = "03/26/2025 05:13:54:272,55F269B4-730F-4DD9-98BE-138DB7F12D5C dev-2 gzqiuaIzK8Gk/fpXN4hgXSNDp7z4ecNCWtWE7J9uKSc= EI/xFDN0G8rqjxICer5pt71TRRxZ5qKUpV2XgwyBrlA= -1:M|4:M|8:M|13:M|20:M,SDDK,88888888,,13:13:52:491,27,EVT_ON_CONNECT,DI4iAAAAAAAAAAAIAAAAAAAA/wsC2pTpxipfGt2dKupqmnRjnbaJpXUMKOlA7w43pFAF/ESMeIvXh4w0suhY+YJyvp7TgNqf7c3BR8X+EjfBinBPl0+V9JFtP+h7+DbK00IKdemdQO5Pv18qxxjodnr0/OVanWtS+4UsIEOLj++SKja7mbFUYEDVojIRDvBheDaMUHjeQU0CgahYywCjE53oUGtyy1H7GC9tXsZ/5yNtUOoWGtqsOdYpXh0+cCb4RAsjyn6K2XBgzL2F+WiI2HQRfJI0tMRkjf36mABi1b4DZsgUqfB6BrLFqiwhM0ss1n5tibDptG3vOKZ9z9JHwQm2RUp+M+vysQhY8tNa+DB4mEyWXNzqKajt+S2h4NwLu8o=,,,6.4.0.50765,64cd120c1e154ea09cedd583192f0f74,DI4iAAAAAAAAAAANAAAAAAAALJfamM/zkAJAshUnsvtwMQGUBv+i/bkCRsYvQ5LjVXEGXxXK3MfdqYNJNmnlIRz+pLcrM2bypuljopo+gg==,1,2,-335671717,9,0,-335671717,DI4iAAAAAAAAAAAUAAAAAAAAFB9a15w8utaKxbpvHLeT7RHOg0hq+gj7Z3shYrzT0TlJDWAUpQ==,16778240,,,,,,,,,";
        List<Map<String, String>> result = new ArrayList<>();
        Map<String, String> payload = new HashMap<>();
        payload.put("message", finalMessage);
        payload.put("topType", "file");
        result.add(payload);
        String encodedString = Base64.getEncoder().encodeToString((JsonUtils.toJsonStringIgnoreExp(result).getBytes()));
        System.out.println(encodedString);
    }
}