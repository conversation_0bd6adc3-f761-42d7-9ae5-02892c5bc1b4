package us.zoom.cube.site.biz;

import com.google.common.collect.Lists;
import com.zoom.op.monitor.domain.AggregationFunctionType;
import com.zoom.op.monitor.domain.alarm.Channel;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ActiveProfiles;
import us.zoom.cube.notice.biz.service.AlarmIMSender;
import us.zoom.cube.site.core.ChannelHandler;
import us.zoom.cube.site.core.RsaService;
import us.zoom.cube.site.core.TenantHandler;
import us.zoom.cube.site.core.config.ConfigCache;
import us.zoom.cube.site.lib.IdPara;
import us.zoom.cube.site.lib.ResponseObject;
import us.zoom.cube.site.lib.input.*;
import us.zoom.cube.site.lib.input.expression.ExpressionParseInput;
import us.zoom.cube.site.lib.input.metrics.MetricDistributionInput;
import us.zoom.cube.site.lib.output.agg.AggregationCustomFieldRuleOutput;
import us.zoom.cube.site.lib.output.agg.AggregationFunctionRuleOutput;
import us.zoom.cube.site.lib.output.agg.AggregationHistogramRuleOutput;
import us.zoom.cube.site.lib.output.agg.AggregationPercentileRuleOutput;
import us.zoom.cube.site.lib.output.config.metrics.MetricsOut;
import us.zoom.cube.site.lib.query.MetricQuery;
import us.zoom.cube.site.lib.query.MetricsQuery;
import us.zoom.cube.site.lib.query.PageQuery;
import us.zoom.infra.dao.model.MetricsDO;
import us.zoom.infra.dao.model.MetricsFieldDO;
import us.zoom.infra.dao.model.TenantDO;
import us.zoom.infra.dao.service.MetricsDAO;
import us.zoom.infra.enums.MetricsTypeEnum;
import us.zoom.infra.model.alarm.AlarmLevel;
import us.zoom.infra.thread.ThreadLocalStore;
import us.zoom.cube.lib.utils.JsonUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
@ActiveProfiles("perf_for_local")
public class MetricsServiceTest2 extends BaseTest {
    @Autowired
    private MetricsService metricsService;

    @Autowired
    private AlarmIMSender alarmIMSender;

    @Autowired
    private RsaService rsaService;

    @Autowired
    private ChannelHandler channelHandler;

    @Autowired
    private TenantHandler tenantHandler;

    @Autowired
    private MetricsDAO metricsDAO;

    @Autowired
    private DerivedMetricService derivedMetricService;

    String user1="265b4be6-652c-48dd-a8fe-a0f22256a4af";
    String user2="2565ab89-d345-49e4-9548-9066da3001db";
    String clusterId2="clusterId";
    String va2="VA2";
    String tenantId="39caa432-8f3f-4679-ad39-3f254a203be4";


    @Test
    public void calcMetricsPeriod() {
        try {
//            Double result = metricsService.calcMetricsPeriod("ds_web", "cpu", "instanceId,ip,clusterId,host,ipPublic,regionId,version");

            Integer result = metricsService.calcPeriod("1c0bf925-1826-4611-86c9-623bd00fb81b","f3b444ff-b4f7-4388-928a-a5ca389537d4");
            System.out.println(result);
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
    }

    @Test
    public void parseVariablesFromExpression() {
        try {
            ExpressionParseInput expressionParseInput = new ExpressionParseInput();
            expressionParseInput.setMetricsId("b2fa8b8d-de8b-4ee3-aa45-77a847311c7f");
            expressionParseInput.setExpression("buffers - swapTotal + var > 0");
            ResponseObject result = metricsService.parseVariablesFromExpression(expressionParseInput);
            System.out.println(JsonUtils.toJsonString(result));
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
    }

    @Test
    public void testMetricDistribution() {
        try {
            IdPara idPara = new IdPara();
            MetricDistributionInput metricDistributionInput = new MetricDistributionInput();
            metricDistributionInput.setMetricId("0d653ff7-9681-498c-937a-5a96b1aa33ab");
            metricDistributionInput.setTenantId("1f8cacb9-8769-481f-8ad8-443f18799fa2");
            metricDistributionInput.setFieldName("us");
            ResponseObject res = metricsService.getMetricDistribution(metricDistributionInput);
//            Double res = metricsService.calcSparsity("0d653ff7-9681-498c-937a-5a96b1aa33ab", "1f8cacb9-8769-481f-8ad8-443f18799fa2");
            System.out.println(JsonUtils.toJsonStringIgnoreExp(res));
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
    }

    @Test
    public void derivedMetricGetTagAndField() {
        try {
            derivedMetricService.getTagAndField("09d29f9a-6999-4b3a-9be7-205203defd89");
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
    }

    @Test
    public void testIMSender() throws Exception {
        List<String> contentList = Lists.newArrayList("error content");
        TenantDO tenantDO = tenantHandler.getTenantByName("Cube_Template_Manager");
        Channel channel = channelHandler.findByNameAndTenantId("cube_template_manager_im",tenantDO.getId()).get();
        String endpoint = channel.getParameters().stream().filter(t -> t.getName().equals("Endpoint")).findFirst().get().getValue();
        String encryptAuthorization = channel.getParameters().stream().filter(t -> t.getName().equals("Verification Token")).findFirst().get().getValue();
        String decryptAuthorization = rsaService.decrypt(encryptAuthorization);
        alarmIMSender.send(null,contentList,"error title","apply tmp error alarm name", AlarmLevel.ERROR,endpoint,decryptAuthorization);
    }

    @Test
    public void test() {
        ThreadLocalStore.setUserInfoLocal(user1);
        ThreadLocalStore.setTenantInfoLocal(tenantId);
        MetricQuery metricQuery = new MetricQuery();
//        metricQuery.setQuery("show measurements");
//        ResponseObject res= metricsService.getDbSchema(metricQuery);
        metricQuery.setQuery("  SELECT top(\"usedTime.count\", \"orginUri\", 5) AS \"top_usedTime.count\" FROM \"iweb\".\"autogen\".\"url_app\" WHERE time > now() + 30m GROUP BY time(1m) ");
        metricQuery.setDatabase("iweb");
        //metricQuery.setScene("1");
        ResponseObject res = metricsService.getMetricDisplay(metricQuery);
        metricQuery.setQuery("show tag keys from \"cpu\"");
        res = metricsService.getDbSchema(metricQuery);
        System.out.println(JsonUtils.toJsonString(res));
    }

    @Test
    public void searchMetrics() {
        for (int i = 0; i < 20; i++) {
            PageQuery<MetricsQuery> pageQuery = new PageQuery<>();
            MetricsQuery metricsQuery = new MetricsQuery();
            metricsQuery.setMetricsType(3);
            metricsQuery.setCollectorName("memory_dp_1");
            pageQuery.setPageSize(10);
            pageQuery.setPageIndex(1);
            pageQuery.setQueryPara(metricsQuery);
            ThreadLocalStore.setUserInfoLocal("265b4be6-652c-48dd-a8fe-a0f22256a4af");
            ThreadLocalStore.setTenantInfoLocal(tenantId);
            ResponseObject responseObject = metricsService.searchMetrics(pageQuery);
            System.out.println("responseObject = " + JsonUtils.toJsonString(responseObject));
        }

    }

    @Test
    public void searchMigratedMetrics() {

        PageQuery<MetricsQuery> pageQuery = new PageQuery<>();
        MetricsQuery metricsQuery = new MetricsQuery();
        metricsQuery.setMetricsType(3);
        metricsQuery.setMetricsName("test_sjj_20210414_001");
        pageQuery.setQueryPara(metricsQuery);
        ThreadLocalStore.setUserInfoLocal("265b4be6-652c-48dd-a8fe-a0f22256a4af");
        ThreadLocalStore.setTenantInfoLocal(tenantId);
        ResponseObject responseObject = metricsService.searchMetrics(pageQuery);
        System.out.println("responseObject = " + JsonUtils.toJsonString(responseObject));


    }

    @Test
    public void addMetrics() {
        String time = String.valueOf(System.currentTimeMillis());
        String userId = "265b4be6-652c-48dd-a8fe-a0f22256a4af";
        ThreadLocalStore.setUserInfoLocal(userId);
        ThreadLocalStore.setTenantInfoLocal(tenantId);
        MetricsInput metricsInput = new MetricsInput();
        metricsInput.setTagNames(Lists.newArrayList("host", "cluster"));
        metricsInput.setType(MetricsTypeEnum.AGGREGATION.getValue());

        metricsInput.setMetricsName("luis-roy-metricsName-20210413-" + time);
        metricsInput.setCollectorId("sjj-ut-collectorId1");

        MetricsAggregationInput metricsAggregationInput = buildMetricsAggregationInput();
        metricsInput.setMetricsAggregation(metricsAggregationInput);

        ResponseObject result = metricsService.addMetrics(metricsInput);
        String metricsId = (String) result.getData();
        ResponseObject metricsOut = metricsService.getMetricsById(userId, tenantId,"/api/metric/getMetricDefinitionById", metricsId);
        System.out.println("metricsOut = " + JsonUtils.toJsonString(metricsOut));

    }


    @Test
    public void addMetricsForCustomRule() {
        String time = String.valueOf(System.currentTimeMillis());
        String userId = "265b4be6-652c-48dd-a8fe-a0f22256a4af";
        ThreadLocalStore.setUserInfoLocal(userId);
        ThreadLocalStore.setTenantInfoLocal(tenantId);
        MetricsInput metricsInput = new MetricsInput();
        metricsInput.setTagNames(Lists.newArrayList("host", "cluster"));
        metricsInput.setType(MetricsTypeEnum.AGGREGATION.getValue());

        metricsInput.setMetricsName("sjj-metricsName-20210421-" + time);
        metricsInput.setCollectorId("sjj-ut-collectorId1");

        MetricsAggregationInput metricsAggregationInput = buildMetricsAggregationInputForCustomFieldRule();
        metricsInput.setMetricsAggregation(metricsAggregationInput);

        ResponseObject result = metricsService.addMetrics(metricsInput);
        String metricsId = (String) result.getData();
        ResponseObject metricsOut = metricsService.getMetricsById(userId, tenantId,"/api/metric/getMetricDefinitionById", metricsId);
        System.out.println("metricsOut = " + JsonUtils.toJsonString(metricsOut));

    }

    @Test
    public void addMetricsForPressureTest() {
        String time = String.valueOf(System.currentTimeMillis());
        String userId = "265b4be6-652c-48dd-a8fe-a0f22256a4af";
        ThreadLocalStore.setUserInfoLocal(userId);
        ThreadLocalStore.setTenantInfoLocal(tenantId);
        MetricsInput metricsInput = new MetricsInput();
        metricsInput.setTagNames(Lists.newArrayList("tag1"));
        metricsInput.setType(MetricsTypeEnum.AGGREGATION.getValue());

        metricsInput.setMetricsName("metrics_optimization_pressure_test_20210423_" + time);
        metricsInput.setCollectorId("collector_20210423_" + time);

        MetricsAggregationInput metricsAggregationInput = buildMetricsAggregationInputForPressureTest();
        metricsInput.setMetricsAggregation(metricsAggregationInput);

        ResponseObject result = metricsService.addMetrics(metricsInput);
        String metricsId = (String) result.getData();
        ResponseObject metricsOut = metricsService.getMetricsById(userId, tenantId,"/api/metric/getMetricDefinitionById", metricsId);
        System.out.println("metricsOut = " + JsonUtils.toJsonString(metricsOut));

    }


    @Test
    public void delMetrics() {
        ThreadLocalStore.setUserInfoLocal("265b4be6-652c-48dd-a8fe-a0f22256a4af");
        ThreadLocalStore.setTenantInfoLocal(tenantId);
        IdPara idPara = new IdPara();
        idPara.setId("b837e34c-23c5-4ae3-a1c5-44d48f7996f3");
        metricsService.deleteMetrics(idPara);
    }

    @Test
    public void addAndDelMetrics() {
        ThreadLocalStore.setUserInfoLocal("265b4be6-652c-48dd-a8fe-a0f22256a4af");
        ThreadLocalStore.setTenantInfoLocal(tenantId);
        String time = String.valueOf(System.currentTimeMillis());
        for (int i = 0; i < 3; i++) {
            MetricsInput metricsInput = new MetricsInput();
            metricsInput.setTagNames(Lists.newArrayList("host", "cluster"));
            metricsInput.setType(MetricsTypeEnum.AGGREGATION.getValue());

            metricsInput.setMetricsName("sjj-ut-metricsName-20210407-" + time + "-" + i);
            metricsInput.setCollectorId("sjj-ut-collectorId1");

            MetricsAggregationInput metricsAggregationInput = buildMetricsAggregationInput();
            metricsInput.setMetricsAggregation(metricsAggregationInput);

            ResponseObject result = metricsService.addMetrics(metricsInput);
            System.out.println("result = " + JsonUtils.toJsonString(result));

            IdPara idPara = new IdPara();
            idPara.setId((String) result.getData());
            metricsService.deleteMetrics(idPara);
        }

    }


    @Test
    public void getMetricsById() {

        ThreadLocalStore.setUserInfoLocal("265b4be6-652c-48dd-a8fe-a0f22256a4af");
        ThreadLocalStore.setTenantInfoLocal(tenantId);
        String userId = "265b4be6-652c-48dd-a8fe-a0f22256a4af";
        String tenantId = "bb6a0a17-0703-4e06-affc-92834a5d9079";
        String metricsId = "92903b26-4fa3-4e26-85c8-12dc845a807a";
        ResponseObject responseObject = metricsService.getMetricsById(userId, tenantId, "/api/metric/getMetricDefinitionById",metricsId);
        MetricsOut metricsOut = (MetricsOut) responseObject.getData();
        System.out.println("metricsOut = " + JsonUtils.toJsonString(metricsOut));


    }

    @Test
    public void getMetricsByIdWithOriginal() {
        for (int i = 0; i < 3; i++) {
            ThreadLocalStore.setUserInfoLocal("265b4be6-652c-48dd-a8fe-a0f22256a4af");
            ThreadLocalStore.setTenantInfoLocal(tenantId);
            String userId = "265b4be6-652c-48dd-a8fe-a0f22256a4af";
            String tenantId = "bb6a0a17-0703-4e06-affc-92834a5d9079";
            String metricsId = "12f49c0c-2ddc-4e82-b48e-77f7da3bc466";
            ResponseObject responseObject = metricsService.getMetricsById(userId, tenantId, "/api/metric/getMetricDefinitionById",metricsId);
            MetricsOut metricsOut = (MetricsOut) responseObject.getData();
            System.out.println("metricsOut = " + JsonUtils.toJsonString(metricsOut));
        }

    }


    @Test
    public void editMetrics() {
        String time = String.valueOf(System.currentTimeMillis());
        String userId = "265b4be6-652c-48dd-a8fe-a0f22256a4af";
        String metricsId = "5ab00d33-c42c-48c5-a2e6-6476017a514a";
        String metricsName = "sjj-ut-metricsName-20210407-1617784884246";
        ThreadLocalStore.setUserInfoLocal(userId);
        ThreadLocalStore.setTenantInfoLocal(tenantId);
        MetricsOut metricsOut = (MetricsOut) metricsService.getMetricsById(userId, tenantId, "/api/metric/getMetricDefinitionById",metricsId).getData();
        MetricsInput metricsInput = copyFrom(metricsOut);
        metricsInput.setMetricsFieldList(Lists.newArrayList());

        // update tag
        metricsInput.getTagNames().add("tag1");
        // update aggPeriod
        metricsInput.getMetricsAggregation().setAggPeriod(30);
        // update filterCondition
        metricsInput.getMetricsAggregation().setFilterCondition("condition001");

        // update common
        metricsInput.getMetricsAggregation().getMetricsAggregationRuleCompose().getCommon().get(0).getAggTypes().add(new AggregationFunctionItemInput(null, AggregationFunctionType.max.name(),""));
        metricsInput.getMetricsAggregation().getMetricsAggregationRuleCompose().getCommon().get(0).setFilterCondition("condition002");


        AggregationFunctionRuleInput aggregationFunctionRuleInput1 = new AggregationFunctionRuleInput();
        aggregationFunctionRuleInput1.setAggField("load1");
        aggregationFunctionRuleInput1.setConditionalFieldPrefix("load1_2");
        aggregationFunctionRuleInput1.setIsConditioned(1);

        aggregationFunctionRuleInput1.setAggTypes(Lists.newArrayList(new AggregationFunctionItemInput(null, "min",""), new AggregationFunctionItemInput(null, "max","")));
        metricsInput.getMetricsAggregation().getMetricsAggregationRuleCompose().getCommon().add(aggregationFunctionRuleInput1);

        AggregationFunctionRuleInput aggregationFunctionRuleInput2 = new AggregationFunctionRuleInput();
        aggregationFunctionRuleInput2.setAggField("load2");
        aggregationFunctionRuleInput2.setIsConditioned(1);

        aggregationFunctionRuleInput2.setFilterCondition("condition003");
        aggregationFunctionRuleInput2.setConditionalFieldPrefix("load2_1");
        aggregationFunctionRuleInput2.setAggTypes(Lists.newArrayList(new AggregationFunctionItemInput(null, "min",""), new AggregationFunctionItemInput(null, "max","")));
        metricsInput.getMetricsAggregation().getMetricsAggregationRuleCompose().getCommon().add(aggregationFunctionRuleInput2);

        AggregationFunctionRuleInput aggregationFunctionRuleInput3 = new AggregationFunctionRuleInput();
        aggregationFunctionRuleInput3.setAggField("load2");
        aggregationFunctionRuleInput2.setFilterCondition("condition004");
        aggregationFunctionRuleInput3.setIsConditioned(0);

        aggregationFunctionRuleInput3.setAggTypes(Lists.newArrayList(new AggregationFunctionItemInput(null, "min",""), new AggregationFunctionItemInput(null, "max","")));
        metricsInput.getMetricsAggregation().getMetricsAggregationRuleCompose().getCommon().add(aggregationFunctionRuleInput3);


        // update histogram
        // update range
        metricsInput.getMetricsAggregation().getMetricsAggregationRuleCompose().getHistogram().get(0).getRanges().get(0).setResultLabel("load_10_30");
        metricsInput.getMetricsAggregation().getMetricsAggregationRuleCompose().getHistogram().get(0).getRanges().get(0).setLowerLimit(10.0);
        metricsInput.getMetricsAggregation().getMetricsAggregationRuleCompose().getHistogram().get(0).getRanges().get(0).setLowerIntervalNotation(0);
        // add and delete range
        metricsInput.getMetricsAggregation().getMetricsAggregationRuleCompose().getHistogram().get(0).getRanges().get(1).setId(null);

        // add histogram rule
        AggregationHistogramRuleInput aggregationHistogramRuleInput1 = new AggregationHistogramRuleInput();
        aggregationHistogramRuleInput1.setAggField("totalTime");
        aggregationHistogramRuleInput1.setAggId("d356c646-6f32-4ce5-af10-2b543ecc83ce");
        aggregationHistogramRuleInput1.setFilterCondition("condition005");

        aggregationHistogramRuleInput1.setRanges(Lists.newArrayList(new AggregationHistogramRangeItemInput(null, 60.0, 50.0, 1, 1, "totalTime_50_60"), new AggregationHistogramRangeItemInput(null, 50.0, 40.0, 0, 1, "totalTime_40_50")));
        metricsInput.getMetricsAggregation().getMetricsAggregationRuleCompose().getHistogram().add(aggregationHistogramRuleInput1);

        // update percentile
        // update percentileValueItem
        metricsInput.getMetricsAggregation().getMetricsAggregationRuleCompose().getPercentile().get(0).getPercentileValues().get(0).setValue(25);
        // add and delete percentileValueItem
        metricsInput.getMetricsAggregation().getMetricsAggregationRuleCompose().getPercentile().get(0).getPercentileValues().get(1).setId(null);
        metricsInput.getMetricsAggregation().getMetricsAggregationRuleCompose().getPercentile().get(0).getPercentileValues().add(new AggregationPercentileItemInput(null, 99));

        // add percentile rule
        AggregationPercentileRuleInput aggregationPercentileRuleInput1 = new AggregationPercentileRuleInput();
        aggregationPercentileRuleInput1.setAggField("percentileTime");
        aggregationPercentileRuleInput1.setAggId("d356c646-6f32-4ce5-af10-2b543ecc83ce");
        aggregationPercentileRuleInput1.setFilterCondition("condition006");

        aggregationPercentileRuleInput1.setPercentileValues(Lists.newArrayList(new AggregationPercentileItemInput(null, 70), new AggregationPercentileItemInput(null, 50)));
        metricsInput.getMetricsAggregation().getMetricsAggregationRuleCompose().getPercentile().add(aggregationPercentileRuleInput1);
        metricsService.editMetrics(metricsInput);
    }


    @Test
    public void addAndEditAndDeleteAggMetrics() {
        String time = String.valueOf(System.currentTimeMillis());
        String userId = "265b4be6-652c-48dd-a8fe-a0f22256a4af";
        ThreadLocalStore.setUserInfoLocal(userId);
        ThreadLocalStore.setTenantInfoLocal(tenantId);
        MetricsInput metricsInput0 = new MetricsInput();
        metricsInput0.setTagNames(Lists.newArrayList("host", "cluster"));
        metricsInput0.setType(MetricsTypeEnum.AGGREGATION.getValue());

        metricsInput0.setMetricsName("sjj-ut-metricsName-20210407-" + time);
        metricsInput0.setCollectorId("sjj-ut-collectorId1");

        MetricsAggregationInput metricsAggregationInput = buildMetricsAggregationInput();
        metricsInput0.setMetricsAggregation(metricsAggregationInput);

        ResponseObject result = metricsService.addMetrics(metricsInput0);
        String metricsId = (String) result.getData();

        MetricsOut metricsOut = (MetricsOut) metricsService.getMetricsById(userId, tenantId,"/api/metric/getMetricDefinitionById", metricsId).getData();
        System.out.println("before edit metricsOut = " + JsonUtils.toJsonString(metricsOut));
        MetricsInput metricsInput = copyFrom(metricsOut);
        metricsInput.setMetricsFieldList(Lists.newArrayList());

        // update tag
        metricsInput.getTagNames().add("tag1");
        // update aggPeriod
        metricsInput.getMetricsAggregation().setAggPeriod(30);
        // update filterCondition
        metricsInput.getMetricsAggregation().setFilterCondition("condition001");

        // update common
        metricsInput.getMetricsAggregation().getMetricsAggregationRuleCompose().getCommon().get(0).getAggTypes().add(new AggregationFunctionItemInput(null, AggregationFunctionType.max.name(),""));
        metricsInput.getMetricsAggregation().getMetricsAggregationRuleCompose().getCommon().get(0).setFilterCondition("condition002");


        AggregationFunctionRuleInput aggregationFunctionRuleInput1 = new AggregationFunctionRuleInput();
        aggregationFunctionRuleInput1.setAggField("load1");
        aggregationFunctionRuleInput1.setConditionalFieldPrefix("load1_2");
        aggregationFunctionRuleInput1.setIsConditioned(1);

        aggregationFunctionRuleInput1.setAggTypes(Lists.newArrayList(new AggregationFunctionItemInput(null, "min",""), new AggregationFunctionItemInput(null, "max","")));
        metricsInput.getMetricsAggregation().getMetricsAggregationRuleCompose().getCommon().add(aggregationFunctionRuleInput1);

        AggregationFunctionRuleInput aggregationFunctionRuleInput2 = new AggregationFunctionRuleInput();
        aggregationFunctionRuleInput2.setAggField("load2");
        aggregationFunctionRuleInput2.setIsConditioned(1);

        aggregationFunctionRuleInput2.setFilterCondition("condition003");
        aggregationFunctionRuleInput2.setConditionalFieldPrefix("load2_1");
        aggregationFunctionRuleInput2.setAggTypes(Lists.newArrayList(new AggregationFunctionItemInput(null, "min",""), new AggregationFunctionItemInput(null, "max","")));
        metricsInput.getMetricsAggregation().getMetricsAggregationRuleCompose().getCommon().add(aggregationFunctionRuleInput2);

        AggregationFunctionRuleInput aggregationFunctionRuleInput3 = new AggregationFunctionRuleInput();
        aggregationFunctionRuleInput3.setAggField("load2");
        aggregationFunctionRuleInput3.setFilterCondition("condition004");
        aggregationFunctionRuleInput3.setIsConditioned(0);

        aggregationFunctionRuleInput3.setAggTypes(Lists.newArrayList(new AggregationFunctionItemInput(null, "min",""), new AggregationFunctionItemInput(null, "max","")));
        metricsInput.getMetricsAggregation().getMetricsAggregationRuleCompose().getCommon().add(aggregationFunctionRuleInput3);


        // update histogram
        // update range
        metricsInput.getMetricsAggregation().getMetricsAggregationRuleCompose().getHistogram().get(0).getRanges().get(0).setResultLabel("load_10_30");
        metricsInput.getMetricsAggregation().getMetricsAggregationRuleCompose().getHistogram().get(0).getRanges().get(0).setLowerLimit(10.0);
        metricsInput.getMetricsAggregation().getMetricsAggregationRuleCompose().getHistogram().get(0).getRanges().get(0).setLowerIntervalNotation(0);
        // add and delete range
        metricsInput.getMetricsAggregation().getMetricsAggregationRuleCompose().getHistogram().get(0).getRanges().get(1).setId(null);

        // add histogram rule
        AggregationHistogramRuleInput aggregationHistogramRuleInput1 = new AggregationHistogramRuleInput();
        aggregationHistogramRuleInput1.setAggField("totalTime");
        aggregationHistogramRuleInput1.setAggId("d356c646-6f32-4ce5-af10-2b543ecc83ce");
        aggregationHistogramRuleInput1.setFilterCondition("condition005");

        aggregationHistogramRuleInput1.setRanges(Lists.newArrayList(new AggregationHistogramRangeItemInput(null, 60.0, 50.0, 1, 1, "totalTime_50_60"), new AggregationHistogramRangeItemInput(null, 50.0, 40.0, 0, 1, "totalTime_40_50")));
        metricsInput.getMetricsAggregation().getMetricsAggregationRuleCompose().getHistogram().add(aggregationHistogramRuleInput1);

        // update percentile
        // update percentileValueItem
        metricsInput.getMetricsAggregation().getMetricsAggregationRuleCompose().getPercentile().get(0).getPercentileValues().get(0).setValue(25);
        // add and delete percentileValueItem
        metricsInput.getMetricsAggregation().getMetricsAggregationRuleCompose().getPercentile().get(0).getPercentileValues().get(1).setId(null);
        metricsInput.getMetricsAggregation().getMetricsAggregationRuleCompose().getPercentile().get(0).getPercentileValues().add(new AggregationPercentileItemInput(null, 99));

        // add percentile rule
        AggregationPercentileRuleInput aggregationPercentileRuleInput1 = new AggregationPercentileRuleInput();
        aggregationPercentileRuleInput1.setAggField("percentileTime");
        aggregationPercentileRuleInput1.setAggId("d356c646-6f32-4ce5-af10-2b543ecc83ce");
        aggregationPercentileRuleInput1.setFilterCondition("condition006");

        aggregationPercentileRuleInput1.setPercentileValues(Lists.newArrayList(new AggregationPercentileItemInput(null, 70), new AggregationPercentileItemInput(null, 50)));
        metricsInput.getMetricsAggregation().getMetricsAggregationRuleCompose().getPercentile().add(aggregationPercentileRuleInput1);
        metricsService.editMetrics(metricsInput);
        MetricsOut metricsOutAfterEdit = (MetricsOut) metricsService.getMetricsById(userId, tenantId, "/api/metric/getMetricDefinitionById",metricsId).getData();
        System.out.println("after edit metricsOut = " + JsonUtils.toJsonString(metricsOutAfterEdit));


        IdPara idPara = new IdPara();
        idPara.setId(metricsId);
        metricsService.deleteMetrics(idPara);
    }

    @Test
    public void editAggMetricsVersion0() {
        String time = String.valueOf(System.currentTimeMillis());
        String userId = "265b4be6-652c-48dd-a8fe-a0f22256a4af";
        ThreadLocalStore.setUserInfoLocal(userId);
        ThreadLocalStore.setTenantInfoLocal(tenantId);

        String metricsId = "4af05ce5-ebf5-495f-81dd-0d5458a37ef2";

        MetricsOut metricsOut = (MetricsOut) metricsService.getMetricsById(userId, tenantId, "/api/metric/getMetricDefinitionById",metricsId).getData();
        System.out.println("before edit metricsOut = " + JsonUtils.toJsonString(metricsOut));
        MetricsInput metricsInput = copyFrom(metricsOut);
        metricsInput.setMetricsFieldList(Lists.newArrayList());

        // update tag
        metricsInput.getTagNames().add("tag1");
        // update aggPeriod
        metricsInput.getMetricsAggregation().setAggPeriod(30);
        // update filterCondition
        metricsInput.getMetricsAggregation().setFilterCondition("condition001");

        // update common
        metricsInput.getMetricsAggregation().getMetricsAggregationRuleCompose().getCommon().get(0).getAggTypes().add(new AggregationFunctionItemInput(null, AggregationFunctionType.min.name(),""));
        metricsInput.getMetricsAggregation().getMetricsAggregationRuleCompose().getCommon().get(0).setFilterCondition("condition002");


        AggregationFunctionRuleInput aggregationFunctionRuleInput1 = new AggregationFunctionRuleInput();
        aggregationFunctionRuleInput1.setAggField("fields_2");
        aggregationFunctionRuleInput1.setConditionalFieldPrefix("fields_2_1");
        aggregationFunctionRuleInput1.setIsConditioned(1);

        aggregationFunctionRuleInput1.setAggTypes(Lists.newArrayList(new AggregationFunctionItemInput(null, "min",""), new AggregationFunctionItemInput(null, "max","")));
        metricsInput.getMetricsAggregation().getMetricsAggregationRuleCompose().getCommon().add(aggregationFunctionRuleInput1);

        AggregationFunctionRuleInput aggregationFunctionRuleInput2 = new AggregationFunctionRuleInput();
        aggregationFunctionRuleInput2.setAggField("load3");
        aggregationFunctionRuleInput2.setIsConditioned(1);

        aggregationFunctionRuleInput2.setFilterCondition("condition003");
        aggregationFunctionRuleInput2.setConditionalFieldPrefix("load3_1");
        aggregationFunctionRuleInput2.setAggTypes(Lists.newArrayList(new AggregationFunctionItemInput(null, "min",""), new AggregationFunctionItemInput(null, "max","")));
        metricsInput.getMetricsAggregation().getMetricsAggregationRuleCompose().getCommon().add(aggregationFunctionRuleInput2);

        AggregationFunctionRuleInput aggregationFunctionRuleInput3 = new AggregationFunctionRuleInput();
        aggregationFunctionRuleInput3.setAggField("load3");
        aggregationFunctionRuleInput3.setFilterCondition("condition004");
        aggregationFunctionRuleInput3.setIsConditioned(0);

        aggregationFunctionRuleInput3.setAggTypes(Lists.newArrayList(new AggregationFunctionItemInput(null, "min",""), new AggregationFunctionItemInput(null, "max","")));
        metricsInput.getMetricsAggregation().getMetricsAggregationRuleCompose().getCommon().add(aggregationFunctionRuleInput3);


        // add histogram rule
        AggregationHistogramRuleInput aggregationHistogramRuleInput1 = new AggregationHistogramRuleInput();
        aggregationHistogramRuleInput1.setAggField("totalTime2");
        aggregationHistogramRuleInput1.setAggId("d356c646-6f32-4ce5-af10-2b543ecc83ce");
        aggregationHistogramRuleInput1.setFilterCondition("condition005");

        aggregationHistogramRuleInput1.setRanges(Lists.newArrayList(new AggregationHistogramRangeItemInput(null, 60.0, 50.0, 1, 1, "totalTime2_50_60"), new AggregationHistogramRangeItemInput(null, 50.0, 40.0, 0, 1, "totalTime2_40_50")));
        metricsInput.getMetricsAggregation().getMetricsAggregationRuleCompose().getHistogram().add(aggregationHistogramRuleInput1);

        // add percentile rule
        AggregationPercentileRuleInput aggregationPercentileRuleInput1 = new AggregationPercentileRuleInput();
        aggregationPercentileRuleInput1.setAggField("percentileTime2");
        aggregationPercentileRuleInput1.setAggId("d356c646-6f32-4ce5-af10-2b543ecc83ce");
        aggregationPercentileRuleInput1.setFilterCondition("condition006");

        aggregationPercentileRuleInput1.setPercentileValues(Lists.newArrayList(new AggregationPercentileItemInput(null, 70), new AggregationPercentileItemInput(null, 50)));
        metricsInput.getMetricsAggregation().getMetricsAggregationRuleCompose().getPercentile().add(aggregationPercentileRuleInput1);
        metricsService.editMetrics(metricsInput);
        MetricsOut metricsOutAfterEdit = (MetricsOut) metricsService.getMetricsById(userId, tenantId,"/api/metric/getMetricDefinitionById", metricsId).getData();
        System.out.println("after edit metricsOut = " + JsonUtils.toJsonString(metricsOutAfterEdit));


        IdPara idPara = new IdPara();
        idPara.setId(metricsId);
        //metricsService.deleteMetrics(idPara);
    }

    @Test
    public void editAggMetricsVersion0Simple() {
        String userId = "265b4be6-652c-48dd-a8fe-a0f22256a4af";
        ThreadLocalStore.setUserInfoLocal(userId);
        ThreadLocalStore.setTenantInfoLocal(tenantId);

        String metricsId = "7742ebd6-ad4b-43cf-82d3-8f49c30c7ee5";

        MetricsOut metricsOut = (MetricsOut) metricsService.getMetricsById(userId, tenantId, "/api/metric/getMetricDefinitionById",metricsId).getData();
        System.out.println("before edit metricsOut = " + JsonUtils.toJsonString(metricsOut));
        MetricsInput metricsInput = copyFrom(metricsOut);
        metricsInput.setMetricsFieldList(Lists.newArrayList());

        // update common
        metricsInput.getMetricsAggregation().getMetricsAggregationRuleCompose().getCommon().get(0).getAggTypes().add(new AggregationFunctionItemInput(null, AggregationFunctionType.min.name(),""));

        AggregationFunctionRuleInput aggregationFunctionRuleInput1 = new AggregationFunctionRuleInput();
        aggregationFunctionRuleInput1.setAggField("fields_2");
        aggregationFunctionRuleInput1.setConditionalFieldPrefix("fields_2_1");
        aggregationFunctionRuleInput1.setIsConditioned(1);
        aggregationFunctionRuleInput1.setFilterCondition("condition002");

        aggregationFunctionRuleInput1.setAggTypes(Lists.newArrayList(new AggregationFunctionItemInput(null, "min",""), new AggregationFunctionItemInput(null, "max","")));
        metricsInput.getMetricsAggregation().getMetricsAggregationRuleCompose().getCommon().add(aggregationFunctionRuleInput1);

        AggregationFunctionRuleInput aggregationFunctionRuleInput2 = new AggregationFunctionRuleInput();
        aggregationFunctionRuleInput2.setAggField("fields_2");
        aggregationFunctionRuleInput2.setConditionalFieldPrefix("fields_2_2");
        aggregationFunctionRuleInput2.setIsConditioned(1);
        aggregationFunctionRuleInput2.setFilterCondition("condition003");

        aggregationFunctionRuleInput2.setAggTypes(Lists.newArrayList(new AggregationFunctionItemInput(null, "min",""), new AggregationFunctionItemInput(null, "max","")));
        metricsInput.getMetricsAggregation().getMetricsAggregationRuleCompose().getCommon().add(aggregationFunctionRuleInput2);


        AggregationFunctionRuleInput aggregationFunctionRuleInput3 = new AggregationFunctionRuleInput();
        aggregationFunctionRuleInput3.setAggField("fields_3");
        aggregationFunctionRuleInput3.setIsConditioned(0);
        aggregationFunctionRuleInput3.setFilterCondition("condition004");

        aggregationFunctionRuleInput3.setAggTypes(Lists.newArrayList(new AggregationFunctionItemInput(null, "min",""), new AggregationFunctionItemInput(null, "max","")));
        metricsInput.getMetricsAggregation().getMetricsAggregationRuleCompose().getCommon().add(aggregationFunctionRuleInput3);


        metricsService.editMetrics(metricsInput);
        MetricsOut metricsOutAfterEdit = (MetricsOut) metricsService.getMetricsById(userId, tenantId, "/api/metric/getMetricDefinitionById",metricsId).getData();
        System.out.println("after edit metricsOut = " + JsonUtils.toJsonString(metricsOutAfterEdit));


        IdPara idPara = new IdPara();
        idPara.setId(metricsId);
        //metricsService.deleteMetrics(idPara);
    }

    @Test
    public void addAndEditAndDeleteOriginalMetrics() {
        String time = String.valueOf(System.currentTimeMillis());
        String userId = "265b4be6-652c-48dd-a8fe-a0f22256a4af";
        ThreadLocalStore.setUserInfoLocal(userId);
        ThreadLocalStore.setTenantInfoLocal(tenantId);
        MetricsInput metricsInput0 = new MetricsInput();
        metricsInput0.setTagNames(Lists.newArrayList("host", "cluster"));
        metricsInput0.setType(MetricsTypeEnum.ORIGINAL.getValue());

        metricsInput0.setMetricsName("sjj-ut-metricsName-20210407-" + time);
        metricsInput0.setCollectorId("sjj-ut-collectorId1");
        metricsInput0.setMetricsFieldList(Lists.newArrayList(new MetricsFieldInput(null, null, "field1", 1, "")));

        ResponseObject result = metricsService.addMetrics(metricsInput0);
        String metricsId = (String) result.getData();

        MetricsOut metricsOut = (MetricsOut) metricsService.getMetricsById(userId, tenantId, "/api/metric/getMetricDefinitionById",metricsId).getData();
        System.out.println("before edit metricsOut = " + JsonUtils.toJsonString(metricsOut));
        MetricsInput metricsInput = copyFrom(metricsOut);
        metricsInput.getMetricsFieldList().remove(0);
        metricsInput.getMetricsFieldList().add(new MetricsFieldInput(null, null, "field2", 2, ""));

        // update tag
        // metricsInput.getTagNames().remove(1);
        metricsInput.getTagNames().add("tag1");

        metricsService.editMetrics(metricsInput);
        MetricsOut metricsOutAfterEdit = (MetricsOut) metricsService.getMetricsById(userId, tenantId,"/api/metric/getMetricDefinitionById", metricsId).getData();
        System.out.println("after edit metricsOut = " + JsonUtils.toJsonString(metricsOutAfterEdit));


        IdPara idPara = new IdPara();
        idPara.setId(metricsId);
        // metricsService.deleteMetrics(idPara);
    }

    private MetricsInput copyFrom(MetricsOut metricsOut) {
        MetricsInput metricsInput = new MetricsInput();
        BeanUtils.copyProperties(metricsOut, metricsInput);
        metricsInput.setMetricsFieldList(new ListUtils<MetricsFieldInput>().copyList(metricsOut.getMetricsFieldList(), MetricsFieldInput.class));
        BeanUtils.copyProperties(metricsOut.getMetricsAggregation(), metricsInput.getMetricsAggregation());
        BeanUtils.copyProperties(metricsOut.getMetricsAggregation().getMetricsAggregationRuleCompose(), metricsInput.getMetricsAggregation().getMetricsAggregationRuleCompose());


        metricsInput.getMetricsAggregation().getMetricsAggregationRuleCompose().setCommon(new ListUtils<AggregationFunctionRuleInput>().copyList(metricsOut.getMetricsAggregation().getMetricsAggregationRuleCompose().getCommon(), AggregationFunctionRuleInput.class));
        metricsInput.getMetricsAggregation().getMetricsAggregationRuleCompose().setHistogram(new ListUtils<AggregationHistogramRuleInput>().copyList(metricsOut.getMetricsAggregation().getMetricsAggregationRuleCompose().getHistogram(), AggregationHistogramRuleInput.class));
        metricsInput.getMetricsAggregation().getMetricsAggregationRuleCompose().setPercentile(new ListUtils<AggregationPercentileRuleInput>().copyList(metricsOut.getMetricsAggregation().getMetricsAggregationRuleCompose().getPercentile(), AggregationPercentileRuleInput.class));
        metricsInput.getMetricsAggregation().getMetricsAggregationRuleCompose().setCustomize(new ListUtils<AggregationCustomFieldRuleInput>().copyList(metricsOut.getMetricsAggregation().getMetricsAggregationRuleCompose().getCustomize(), AggregationCustomFieldRuleInput.class));

        int commonSize = metricsInput.getMetricsAggregation().getMetricsAggregationRuleCompose().getCommon().size();
        for (int i = 0; i < commonSize; i++) {
            AggregationFunctionRuleInput input = metricsInput.getMetricsAggregation().getMetricsAggregationRuleCompose().getCommon().get(i);
            AggregationFunctionRuleOutput output = metricsOut.getMetricsAggregation().getMetricsAggregationRuleCompose().getCommon().get(i);
            BeanUtils.copyProperties(output, input);
            input.setAggTypes(new ListUtils<AggregationFunctionItemInput>().copyList(output.getAggTypes(), AggregationFunctionItemInput.class));

        }
        int histogramSize = metricsInput.getMetricsAggregation().getMetricsAggregationRuleCompose().getHistogram().size();
        for (int i = 0; i < histogramSize; i++) {
            AggregationHistogramRuleInput input = metricsInput.getMetricsAggregation().getMetricsAggregationRuleCompose().getHistogram().get(i);
            AggregationHistogramRuleOutput output = metricsOut.getMetricsAggregation().getMetricsAggregationRuleCompose().getHistogram().get(i);
            BeanUtils.copyProperties(output, input);
            input.setRanges(new ListUtils<AggregationHistogramRangeItemInput>().copyList(output.getRanges(), AggregationHistogramRangeItemInput.class));

        }
        int percentileSize = metricsInput.getMetricsAggregation().getMetricsAggregationRuleCompose().getPercentile().size();
        for (int i = 0; i < percentileSize; i++) {
            AggregationPercentileRuleInput input = metricsInput.getMetricsAggregation().getMetricsAggregationRuleCompose().getPercentile().get(i);
            AggregationPercentileRuleOutput output = metricsOut.getMetricsAggregation().getMetricsAggregationRuleCompose().getPercentile().get(i);
            BeanUtils.copyProperties(output, input);
            input.setPercentileValues(new ListUtils<AggregationPercentileItemInput>().copyList(output.getPercentileValues(), AggregationPercentileItemInput.class));

        }
        int customizeSize = metricsInput.getMetricsAggregation().getMetricsAggregationRuleCompose().getCustomize().size();
        for (int i = 0; i < customizeSize; i++) {
            AggregationCustomFieldRuleInput input = metricsInput.getMetricsAggregation().getMetricsAggregationRuleCompose().getCustomize().get(i);
            AggregationCustomFieldRuleOutput output = metricsOut.getMetricsAggregation().getMetricsAggregationRuleCompose().getCustomize().get(i);
            BeanUtils.copyProperties(output, input);
        }
        return metricsInput;
    }

    private MetricsAggregationInput buildMetricsAggregationInputForEdit() {
        MetricsAggregationInput metricsAggregationInput = new MetricsAggregationInput();
        //metricsAggregationInput.setId();
        metricsAggregationInput.setAggPeriod(60);
        metricsAggregationInput.setWaitTime(5000);
        metricsAggregationInput.setFilterCondition("clusterId == 'dev'");
        MetricsAggregationRuleComposeInput metricsAggregationRuleComposeInput = new MetricsAggregationRuleComposeInput();
        List<AggregationFunctionRuleInput> aggregationFunctionRuleInputList = new ArrayList<>();
        AggregationFunctionRuleInput aggregationFunctionRuleInput1 = new AggregationFunctionRuleInput();
        aggregationFunctionRuleInput1.setAggField("load1");
        aggregationFunctionRuleInput1.setFilterCondition("a == b");

        aggregationFunctionRuleInput1.setIsConditioned(0);
        List<AggregationFunctionItemInput> aggregationFunctionItemInputList1 = new ArrayList<>();
        AggregationFunctionItemInput aggregationFunctionItemInput1 = new AggregationFunctionItemInput();
        aggregationFunctionItemInput1.setValue("count");
        aggregationFunctionItemInputList1.add(aggregationFunctionItemInput1);
        AggregationFunctionItemInput aggregationFunctionItemInput2 = new AggregationFunctionItemInput();
        aggregationFunctionItemInput2.setValue("sum");
        aggregationFunctionItemInputList1.add(aggregationFunctionItemInput2);
        aggregationFunctionRuleInput1.setAggTypes(aggregationFunctionItemInputList1);
        aggregationFunctionRuleInputList.add(aggregationFunctionRuleInput1);

        AggregationFunctionRuleInput aggregationFunctionRuleInput2 = new AggregationFunctionRuleInput();
        aggregationFunctionRuleInput2.setAggField("load1");
        aggregationFunctionRuleInput1.setFilterCondition("b == c");

        aggregationFunctionRuleInput2.setIsConditioned(1);
        aggregationFunctionRuleInput2.setConditionalFieldPrefix("load1_1");
        List<AggregationFunctionItemInput> aggregationFunctionItemInputList2 = new ArrayList<>();
        AggregationFunctionItemInput aggregationFunctionItemInput2_1 = new AggregationFunctionItemInput();
        aggregationFunctionItemInput2_1.setValue("count");
        aggregationFunctionItemInputList2.add(aggregationFunctionItemInput2_1);

        aggregationFunctionRuleInput2.setAggTypes(aggregationFunctionItemInputList2);
        aggregationFunctionRuleInputList.add(aggregationFunctionRuleInput2);
        metricsAggregationRuleComposeInput.setCommon(aggregationFunctionRuleInputList);

        List<AggregationHistogramRuleInput> aggregationHistogramRuleInputList = new ArrayList<>();
        AggregationHistogramRuleInput aggregationHistogramRuleInput1 = new AggregationHistogramRuleInput();
        aggregationHistogramRuleInput1.setAggField("load1");

        List<AggregationHistogramRangeItemInput> aggregationHistogramRangeItemInputList = new ArrayList<>();
        AggregationHistogramRangeItemInput aggregationHistogramRangeItemInput1 = new AggregationHistogramRangeItemInput();
        aggregationHistogramRangeItemInput1.setLowerLimit(20.1);
        aggregationHistogramRangeItemInput1.setUpperLimit(30.1);
        aggregationHistogramRangeItemInput1.setLowerIntervalNotation(1);
        aggregationHistogramRangeItemInput1.setUpperIntervalNotation(0);
        aggregationHistogramRangeItemInput1.setResultLabel("load_20_30");
        aggregationHistogramRangeItemInputList.add(aggregationHistogramRangeItemInput1);

        AggregationHistogramRangeItemInput aggregationHistogramRangeItemInput2 = new AggregationHistogramRangeItemInput();
        aggregationHistogramRangeItemInput2.setLowerLimit(30.1);
        aggregationHistogramRangeItemInput2.setUpperLimit(70.0);
        aggregationHistogramRangeItemInput2.setLowerIntervalNotation(1);
        aggregationHistogramRangeItemInput2.setUpperIntervalNotation(0);
        aggregationHistogramRangeItemInput2.setResultLabel("load_30_70");
        aggregationHistogramRangeItemInputList.add(aggregationHistogramRangeItemInput2);
        aggregationHistogramRuleInput1.setRanges(aggregationHistogramRangeItemInputList);
        aggregationHistogramRuleInputList.add(aggregationHistogramRuleInput1);

        AggregationHistogramRuleInput aggregationHistogramRuleInput2 = new AggregationHistogramRuleInput();
        aggregationHistogramRuleInput2.setAggField("usedTime");

        List<AggregationHistogramRangeItemInput> aggregationHistogramRangeItemInputList2 = new ArrayList<>();
        AggregationHistogramRangeItemInput aggregationHistogramRangeItemInput3 = new AggregationHistogramRangeItemInput();
        aggregationHistogramRangeItemInput3.setLowerLimit(0.1);
        aggregationHistogramRangeItemInput3.setUpperLimit(20.1);
        aggregationHistogramRangeItemInput3.setLowerIntervalNotation(1);
        aggregationHistogramRangeItemInput3.setUpperIntervalNotation(0);
        aggregationHistogramRangeItemInput3.setResultLabel("load_0_20");
        aggregationHistogramRangeItemInputList2.add(aggregationHistogramRangeItemInput3);
        AggregationHistogramRangeItemInput aggregationHistogramRangeItemInput4 = new AggregationHistogramRangeItemInput();
        aggregationHistogramRangeItemInput4.setLowerLimit(20.1);
        aggregationHistogramRangeItemInput4.setUpperLimit(21.2);
        aggregationHistogramRangeItemInput4.setLowerIntervalNotation(1);
        aggregationHistogramRangeItemInput4.setUpperIntervalNotation(0);
        aggregationHistogramRangeItemInput4.setResultLabel("load_20_21");
        aggregationHistogramRangeItemInputList2.add(aggregationHistogramRangeItemInput4);
        aggregationHistogramRuleInput2.setRanges(aggregationHistogramRangeItemInputList2);
        aggregationHistogramRuleInputList.add(aggregationHistogramRuleInput2);
        metricsAggregationRuleComposeInput.setHistogram(aggregationHistogramRuleInputList);

        List<AggregationPercentileRuleInput> aggregationPercentileRuleInputList = new ArrayList<>();
        AggregationPercentileRuleInput aggregationPercentileRuleInput = new AggregationPercentileRuleInput();
        aggregationPercentileRuleInput.setAggField("load1");

        List<AggregationPercentileItemInput> aggregationPercentileItemInputList = new ArrayList<>();
        AggregationPercentileItemInput aggregationPercentileItemInput1 = new AggregationPercentileItemInput();
        aggregationPercentileItemInput1.setValue(95);
        aggregationPercentileItemInputList.add(aggregationPercentileItemInput1);
        AggregationPercentileItemInput aggregationPercentileItemInput2 = new AggregationPercentileItemInput();
        aggregationPercentileItemInput2.setValue(15);
        aggregationPercentileItemInputList.add(aggregationPercentileItemInput2);
        aggregationPercentileRuleInput.setPercentileValues(aggregationPercentileItemInputList);
        aggregationPercentileRuleInputList.add(aggregationPercentileRuleInput);
        metricsAggregationRuleComposeInput.setPercentile(aggregationPercentileRuleInputList);
        metricsAggregationInput.setMetricsAggregationRuleCompose(metricsAggregationRuleComposeInput);
        return metricsAggregationInput;
    }

    private MetricsAggregationInput buildMetricsAggregationInput() {
        MetricsAggregationInput metricsAggregationInput = new MetricsAggregationInput();
        metricsAggregationInput.setAggPeriod(60);
        metricsAggregationInput.setWaitTime(5000);
        metricsAggregationInput.setFilterCondition("clusterId == 'dev'");
        MetricsAggregationRuleComposeInput metricsAggregationRuleComposeInput = new MetricsAggregationRuleComposeInput();
        List<AggregationFunctionRuleInput> aggregationFunctionRuleInputList = new ArrayList<>();
        AggregationFunctionRuleInput aggregationFunctionRuleInput1 = new AggregationFunctionRuleInput();
        aggregationFunctionRuleInput1.setAggField("load1");
        aggregationFunctionRuleInput1.setFilterCondition("a == b");

        aggregationFunctionRuleInput1.setIsConditioned(0);
        List<AggregationFunctionItemInput> aggregationFunctionItemInputList1 = new ArrayList<>();
        AggregationFunctionItemInput aggregationFunctionItemInput1 = new AggregationFunctionItemInput();
        aggregationFunctionItemInput1.setValue("count");
        aggregationFunctionItemInputList1.add(aggregationFunctionItemInput1);
        AggregationFunctionItemInput aggregationFunctionItemInput2 = new AggregationFunctionItemInput();
        aggregationFunctionItemInput2.setValue("sum");
        aggregationFunctionItemInputList1.add(aggregationFunctionItemInput2);
        aggregationFunctionRuleInput1.setAggTypes(aggregationFunctionItemInputList1);
        aggregationFunctionRuleInputList.add(aggregationFunctionRuleInput1);

        AggregationFunctionRuleInput aggregationFunctionRuleInput2 = new AggregationFunctionRuleInput();
        aggregationFunctionRuleInput2.setAggField("load1");
        aggregationFunctionRuleInput1.setFilterCondition("b == c");

        aggregationFunctionRuleInput2.setIsConditioned(1);
        aggregationFunctionRuleInput2.setConditionalFieldPrefix("load1_1");
        List<AggregationFunctionItemInput> aggregationFunctionItemInputList2 = new ArrayList<>();
        AggregationFunctionItemInput aggregationFunctionItemInput2_1 = new AggregationFunctionItemInput();
        aggregationFunctionItemInput2_1.setValue("count");
        aggregationFunctionItemInputList2.add(aggregationFunctionItemInput2_1);

        aggregationFunctionRuleInput2.setAggTypes(aggregationFunctionItemInputList2);
        aggregationFunctionRuleInputList.add(aggregationFunctionRuleInput2);
        metricsAggregationRuleComposeInput.setCommon(aggregationFunctionRuleInputList);

        List<AggregationHistogramRuleInput> aggregationHistogramRuleInputList = new ArrayList<>();
        AggregationHistogramRuleInput aggregationHistogramRuleInput1 = new AggregationHistogramRuleInput();
        aggregationHistogramRuleInput1.setAggField("load1");

        List<AggregationHistogramRangeItemInput> aggregationHistogramRangeItemInputList = new ArrayList<>();
        AggregationHistogramRangeItemInput aggregationHistogramRangeItemInput1 = new AggregationHistogramRangeItemInput();
        aggregationHistogramRangeItemInput1.setLowerLimit(20.1);
        aggregationHistogramRangeItemInput1.setUpperLimit(30.1);
        aggregationHistogramRangeItemInput1.setLowerIntervalNotation(1);
        aggregationHistogramRangeItemInput1.setUpperIntervalNotation(0);
        aggregationHistogramRangeItemInput1.setResultLabel("load_20_30");
        aggregationHistogramRangeItemInputList.add(aggregationHistogramRangeItemInput1);

        AggregationHistogramRangeItemInput aggregationHistogramRangeItemInput2 = new AggregationHistogramRangeItemInput();
        aggregationHistogramRangeItemInput2.setLowerLimit(30.1);
        aggregationHistogramRangeItemInput2.setUpperLimit(70.0);
        aggregationHistogramRangeItemInput2.setLowerIntervalNotation(1);
        aggregationHistogramRangeItemInput2.setUpperIntervalNotation(0);
        aggregationHistogramRangeItemInput2.setResultLabel("load_30_70");
        aggregationHistogramRangeItemInputList.add(aggregationHistogramRangeItemInput2);
        aggregationHistogramRuleInput1.setRanges(aggregationHistogramRangeItemInputList);
        aggregationHistogramRuleInputList.add(aggregationHistogramRuleInput1);

        AggregationHistogramRuleInput aggregationHistogramRuleInput2 = new AggregationHistogramRuleInput();
        aggregationHistogramRuleInput2.setAggField("usedTime");

        List<AggregationHistogramRangeItemInput> aggregationHistogramRangeItemInputList2 = new ArrayList<>();
        AggregationHistogramRangeItemInput aggregationHistogramRangeItemInput3 = new AggregationHistogramRangeItemInput();
        aggregationHistogramRangeItemInput3.setLowerLimit(0.1);
        aggregationHistogramRangeItemInput3.setUpperLimit(20.1);
        aggregationHistogramRangeItemInput3.setLowerIntervalNotation(1);
        aggregationHistogramRangeItemInput3.setUpperIntervalNotation(0);
        aggregationHistogramRangeItemInput3.setResultLabel("load_0_20");
        aggregationHistogramRangeItemInputList2.add(aggregationHistogramRangeItemInput3);
        AggregationHistogramRangeItemInput aggregationHistogramRangeItemInput4 = new AggregationHistogramRangeItemInput();
        aggregationHistogramRangeItemInput4.setLowerLimit(20.1);
        aggregationHistogramRangeItemInput4.setUpperLimit(21.2);
        aggregationHistogramRangeItemInput4.setLowerIntervalNotation(1);
        aggregationHistogramRangeItemInput4.setUpperIntervalNotation(0);
        aggregationHistogramRangeItemInput4.setResultLabel("load_20_21");
        aggregationHistogramRangeItemInputList2.add(aggregationHistogramRangeItemInput4);
        aggregationHistogramRuleInput2.setRanges(aggregationHistogramRangeItemInputList2);
        aggregationHistogramRuleInputList.add(aggregationHistogramRuleInput2);
        metricsAggregationRuleComposeInput.setHistogram(aggregationHistogramRuleInputList);

        List<AggregationPercentileRuleInput> aggregationPercentileRuleInputList = new ArrayList<>();
        AggregationPercentileRuleInput aggregationPercentileRuleInput = new AggregationPercentileRuleInput();
        aggregationPercentileRuleInput.setAggField("load1");

        List<AggregationPercentileItemInput> aggregationPercentileItemInputList = new ArrayList<>();
        AggregationPercentileItemInput aggregationPercentileItemInput1 = new AggregationPercentileItemInput();
        aggregationPercentileItemInput1.setValue(95);
        aggregationPercentileItemInputList.add(aggregationPercentileItemInput1);
        AggregationPercentileItemInput aggregationPercentileItemInput2 = new AggregationPercentileItemInput();
        aggregationPercentileItemInput2.setValue(15);
        aggregationPercentileItemInputList.add(aggregationPercentileItemInput2);
        aggregationPercentileRuleInput.setPercentileValues(aggregationPercentileItemInputList);
        aggregationPercentileRuleInputList.add(aggregationPercentileRuleInput);
        metricsAggregationRuleComposeInput.setPercentile(aggregationPercentileRuleInputList);
        metricsAggregationInput.setMetricsAggregationRuleCompose(metricsAggregationRuleComposeInput);
        return metricsAggregationInput;
    }

    private MetricsAggregationInput buildMetricsAggregationInputForCustomFieldRule() {
        MetricsAggregationInput metricsAggregationInput = new MetricsAggregationInput();
        metricsAggregationInput.setAggPeriod(60);
        metricsAggregationInput.setWaitTime(5000);
        metricsAggregationInput.setFilterCondition("clusterId == 'dev'");
        MetricsAggregationRuleComposeInput metricsAggregationRuleComposeInput = new MetricsAggregationRuleComposeInput();
        List<AggregationFunctionRuleInput> aggregationFunctionRuleInputList = new ArrayList<>();
        AggregationFunctionRuleInput aggregationFunctionRuleInput1 = new AggregationFunctionRuleInput();
        aggregationFunctionRuleInput1.setAggField("error");
        aggregationFunctionRuleInput1.setFilterCondition("a == b");

        aggregationFunctionRuleInput1.setIsConditioned(0);
        List<AggregationFunctionItemInput> aggregationFunctionItemInputList1 = new ArrayList<>();
        AggregationFunctionItemInput aggregationFunctionItemInput1 = new AggregationFunctionItemInput();
        aggregationFunctionItemInput1.setValue("count");
        aggregationFunctionItemInputList1.add(aggregationFunctionItemInput1);
        AggregationFunctionItemInput aggregationFunctionItemInput2 = new AggregationFunctionItemInput();
        aggregationFunctionItemInput2.setValue("sum");
        aggregationFunctionItemInputList1.add(aggregationFunctionItemInput2);
        aggregationFunctionRuleInput1.setAggTypes(aggregationFunctionItemInputList1);
        aggregationFunctionRuleInputList.add(aggregationFunctionRuleInput1);

        AggregationFunctionRuleInput aggregationFunctionRuleInput2 = new AggregationFunctionRuleInput();
        aggregationFunctionRuleInput2.setAggField("total");
        aggregationFunctionRuleInput1.setFilterCondition("b == c");

        aggregationFunctionRuleInput2.setIsConditioned(0);
        List<AggregationFunctionItemInput> aggregationFunctionItemInputList2 = new ArrayList<>();
        AggregationFunctionItemInput aggregationFunctionItemInput2_1 = new AggregationFunctionItemInput();
        aggregationFunctionItemInput2_1.setValue("sum");
        aggregationFunctionItemInputList2.add(aggregationFunctionItemInput2_1);

        aggregationFunctionRuleInput2.setAggTypes(aggregationFunctionItemInputList2);
        aggregationFunctionRuleInputList.add(aggregationFunctionRuleInput2);
        metricsAggregationRuleComposeInput.setCommon(aggregationFunctionRuleInputList);

        AggregationCustomFieldRuleInput aggregationCustomFieldRuleInput1 = new AggregationCustomFieldRuleInput();
        aggregationCustomFieldRuleInput1.setFieldName("errorRate");
        aggregationCustomFieldRuleInput1.setExpression("error.sum/ total.sum");

        List<AggregationCustomFieldRuleInput> aggregationCustomFieldRuleInputList = new ArrayList<>();
        aggregationCustomFieldRuleInputList.add(aggregationCustomFieldRuleInput1);
        metricsAggregationRuleComposeInput.setCustomize(aggregationCustomFieldRuleInputList);

        metricsAggregationInput.setMetricsAggregationRuleCompose(metricsAggregationRuleComposeInput);
        return metricsAggregationInput;
    }

    private MetricsAggregationInput buildMetricsAggregationInputForPressureTest() {
        MetricsAggregationInput metricsAggregationInput = new MetricsAggregationInput();
        metricsAggregationInput.setAggPeriod(60);
        metricsAggregationInput.setWaitTime(5000);
        metricsAggregationInput.setFilterCondition("a = 1");
        MetricsAggregationRuleComposeInput metricsAggregationRuleComposeInput = new MetricsAggregationRuleComposeInput();
        List<AggregationFunctionRuleInput> aggregationFunctionRuleInputList = new ArrayList<>();
        // min_field1
        AggregationFunctionRuleInput aggregationFunctionRuleInput1 = new AggregationFunctionRuleInput();
        aggregationFunctionRuleInput1.setAggField("min_field1");
        //aggregationFunctionRuleInput1.setFilterCondition("a == b");

        aggregationFunctionRuleInput1.setIsConditioned(0);
        List<AggregationFunctionItemInput> aggregationFunctionItemInputList1 = new ArrayList<>();
        AggregationFunctionItemInput aggregationFunctionItemInput1 = new AggregationFunctionItemInput();
        aggregationFunctionItemInput1.setValue("min");
        aggregationFunctionItemInputList1.add(aggregationFunctionItemInput1);
        aggregationFunctionRuleInput1.setAggTypes(aggregationFunctionItemInputList1);
        aggregationFunctionRuleInputList.add(aggregationFunctionRuleInput1);

        // max_field2
        AggregationFunctionRuleInput aggregationFunctionRuleInput2 = new AggregationFunctionRuleInput();
        aggregationFunctionRuleInput2.setAggField("max_field2");
        //aggregationFunctionRuleInput1.setFilterCondition("a == b");

        aggregationFunctionRuleInput2.setIsConditioned(0);
        List<AggregationFunctionItemInput> aggregationFunctionItemInputList2 = new ArrayList<>();
        AggregationFunctionItemInput aggregationFunctionItemInput2 = new AggregationFunctionItemInput();
        aggregationFunctionItemInput2.setValue("max");
        aggregationFunctionItemInputList2.add(aggregationFunctionItemInput2);
        aggregationFunctionRuleInput2.setAggTypes(aggregationFunctionItemInputList2);
        aggregationFunctionRuleInputList.add(aggregationFunctionRuleInput2);

        // max_field2_1
        AggregationFunctionRuleInput aggregationFunctionRuleInput3 = new AggregationFunctionRuleInput();
        aggregationFunctionRuleInput3.setAggField("max_field2");
        aggregationFunctionRuleInput3.setFilterCondition("max_field2 < 50");

        aggregationFunctionRuleInput3.setIsConditioned(1);
        aggregationFunctionRuleInput3.setConditionalFieldPrefix("max_field2_1");
        List<AggregationFunctionItemInput> aggregationFunctionItemInputList3 = new ArrayList<>();
        AggregationFunctionItemInput aggregationFunctionItemInput3 = new AggregationFunctionItemInput();
        aggregationFunctionItemInput3.setValue("max");
        aggregationFunctionItemInputList3.add(aggregationFunctionItemInput3);
        aggregationFunctionRuleInput3.setAggTypes(aggregationFunctionItemInputList3);
        aggregationFunctionRuleInputList.add(aggregationFunctionRuleInput3);

        // count_field3
        AggregationFunctionRuleInput aggregationFunctionRuleInput4 = new AggregationFunctionRuleInput();
        aggregationFunctionRuleInput4.setAggField("count_field3");
        //aggregationFunctionRuleInput1.setFilterCondition("a == b");

        aggregationFunctionRuleInput4.setIsConditioned(0);
        List<AggregationFunctionItemInput> aggregationFunctionItemInputList4 = new ArrayList<>();
        AggregationFunctionItemInput aggregationFunctionItemInput4 = new AggregationFunctionItemInput();
        aggregationFunctionItemInput4.setValue("count");
        aggregationFunctionItemInputList4.add(aggregationFunctionItemInput4);
        aggregationFunctionRuleInput4.setAggTypes(aggregationFunctionItemInputList4);
        aggregationFunctionRuleInputList.add(aggregationFunctionRuleInput4);

        // sum_field4
        AggregationFunctionRuleInput aggregationFunctionRuleInput5 = new AggregationFunctionRuleInput();
        aggregationFunctionRuleInput5.setAggField("sum_field4");
        //aggregationFunctionRuleInput1.setFilterCondition("a == b");

        aggregationFunctionRuleInput5.setIsConditioned(0);
        List<AggregationFunctionItemInput> aggregationFunctionItemInputList5 = new ArrayList<>();
        AggregationFunctionItemInput aggregationFunctionItemInput5 = new AggregationFunctionItemInput();
        aggregationFunctionItemInput5.setValue("sum");
        aggregationFunctionItemInputList5.add(aggregationFunctionItemInput5);
        aggregationFunctionRuleInput5.setAggTypes(aggregationFunctionItemInputList5);
        aggregationFunctionRuleInputList.add(aggregationFunctionRuleInput5);

        // avg_field5
        AggregationFunctionRuleInput aggregationFunctionRuleInput6 = new AggregationFunctionRuleInput();
        aggregationFunctionRuleInput6.setAggField("avg_field5");
        //aggregationFunctionRuleInput1.setFilterCondition("a == b");

        aggregationFunctionRuleInput6.setIsConditioned(0);
        List<AggregationFunctionItemInput> aggregationFunctionItemInputList6 = new ArrayList<>();
        AggregationFunctionItemInput aggregationFunctionItemInput6 = new AggregationFunctionItemInput();
        aggregationFunctionItemInput6.setValue("avg");
        aggregationFunctionItemInputList6.add(aggregationFunctionItemInput6);
        AggregationFunctionItemInput aggregationFunctionItemInput6_1 = new AggregationFunctionItemInput();
        aggregationFunctionItemInput6_1.setValue("sum");
        aggregationFunctionItemInputList6.add(aggregationFunctionItemInput6_1);
        AggregationFunctionItemInput aggregationFunctionItemInput6_2 = new AggregationFunctionItemInput();
        aggregationFunctionItemInput6_2.setValue("count");
        aggregationFunctionItemInputList6.add(aggregationFunctionItemInput6_2);
        aggregationFunctionRuleInput6.setAggTypes(aggregationFunctionItemInputList6);
        aggregationFunctionRuleInputList.add(aggregationFunctionRuleInput6);


        // rate_field6
        AggregationFunctionRuleInput aggregationFunctionRuleInput7 = new AggregationFunctionRuleInput();
        aggregationFunctionRuleInput7.setAggField("rate_field6");
        //aggregationFunctionRuleInput1.setFilterCondition("a == b");

        aggregationFunctionRuleInput7.setIsConditioned(0);
        List<AggregationFunctionItemInput> aggregationFunctionItemInputList7 = new ArrayList<>();
        AggregationFunctionItemInput aggregationFunctionItemInput7 = new AggregationFunctionItemInput();
        aggregationFunctionItemInput7.setValue("rate");
        aggregationFunctionItemInputList7.add(aggregationFunctionItemInput7);
        AggregationFunctionItemInput aggregationFunctionItemInput7_1 = new AggregationFunctionItemInput();
        aggregationFunctionItemInput7_1.setValue("sum");
        aggregationFunctionItemInputList7.add(aggregationFunctionItemInput7_1);
        aggregationFunctionRuleInput7.setAggTypes(aggregationFunctionItemInputList7);
        aggregationFunctionRuleInputList.add(aggregationFunctionRuleInput7);

        metricsAggregationRuleComposeInput.setCommon(aggregationFunctionRuleInputList);


        List<AggregationHistogramRuleInput> aggregationHistogramRuleInputList = new ArrayList<>();
        AggregationHistogramRuleInput aggregationHistogramRuleInput1 = new AggregationHistogramRuleInput();
        aggregationHistogramRuleInput1.setAggField("histogram_field7");

        List<AggregationHistogramRangeItemInput> aggregationHistogramRangeItemInputList = new ArrayList<>();
        AggregationHistogramRangeItemInput aggregationHistogramRangeItemInput1 = new AggregationHistogramRangeItemInput();
        aggregationHistogramRangeItemInput1.setLowerLimit(1d);
        aggregationHistogramRangeItemInput1.setUpperLimit(20d);
        aggregationHistogramRangeItemInput1.setLowerIntervalNotation(1);
        aggregationHistogramRangeItemInput1.setUpperIntervalNotation(1);
        aggregationHistogramRangeItemInput1.setResultLabel("label_1_20");
        aggregationHistogramRangeItemInputList.add(aggregationHistogramRangeItemInput1);

        AggregationHistogramRangeItemInput aggregationHistogramRangeItemInput2 = new AggregationHistogramRangeItemInput();
        aggregationHistogramRangeItemInput2.setLowerLimit(20d);
        aggregationHistogramRangeItemInput2.setUpperLimit(40d);
        aggregationHistogramRangeItemInput2.setLowerIntervalNotation(0);
        aggregationHistogramRangeItemInput2.setUpperIntervalNotation(1);
        aggregationHistogramRangeItemInput2.setResultLabel("label_20_40");
        aggregationHistogramRangeItemInputList.add(aggregationHistogramRangeItemInput2);

        AggregationHistogramRangeItemInput aggregationHistogramRangeItemInput3 = new AggregationHistogramRangeItemInput();
        aggregationHistogramRangeItemInput3.setLowerLimit(40d);
        aggregationHistogramRangeItemInput3.setUpperLimit(60d);
        aggregationHistogramRangeItemInput3.setLowerIntervalNotation(0);
        aggregationHistogramRangeItemInput3.setUpperIntervalNotation(1);
        aggregationHistogramRangeItemInput3.setResultLabel("label_40_60");
        aggregationHistogramRangeItemInputList.add(aggregationHistogramRangeItemInput3);

        aggregationHistogramRuleInput1.setRanges(aggregationHistogramRangeItemInputList);
        aggregationHistogramRuleInputList.add(aggregationHistogramRuleInput1);
        metricsAggregationRuleComposeInput.setHistogram(aggregationHistogramRuleInputList);

        List<AggregationPercentileRuleInput> aggregationPercentileRuleInputList = new ArrayList<>();
        AggregationPercentileRuleInput aggregationPercentileRuleInput = new AggregationPercentileRuleInput();
        aggregationPercentileRuleInput.setAggField("percentile_field8");

        List<AggregationPercentileItemInput> aggregationPercentileItemInputList = new ArrayList<>();
        AggregationPercentileItemInput aggregationPercentileItemInput1 = new AggregationPercentileItemInput();
        aggregationPercentileItemInput1.setValue(5);
        aggregationPercentileItemInputList.add(aggregationPercentileItemInput1);
        AggregationPercentileItemInput aggregationPercentileItemInput2 = new AggregationPercentileItemInput();
        aggregationPercentileItemInput2.setValue(50);
        aggregationPercentileItemInputList.add(aggregationPercentileItemInput2);
        AggregationPercentileItemInput aggregationPercentileItemInput3 = new AggregationPercentileItemInput();
        aggregationPercentileItemInput3.setValue(95);
        aggregationPercentileItemInputList.add(aggregationPercentileItemInput3);
        AggregationPercentileItemInput aggregationPercentileItemInput4 = new AggregationPercentileItemInput();
        aggregationPercentileItemInput4.setValue(99);
        aggregationPercentileItemInputList.add(aggregationPercentileItemInput4);
        aggregationPercentileRuleInput.setPercentileValues(aggregationPercentileItemInputList);
        aggregationPercentileRuleInputList.add(aggregationPercentileRuleInput);
        metricsAggregationRuleComposeInput.setPercentile(aggregationPercentileRuleInputList);

        // custom
        AggregationFunctionRuleInput aggregationFunctionRuleInput8 = new AggregationFunctionRuleInput();
        aggregationFunctionRuleInput8.setAggField("error");

        aggregationFunctionRuleInput8.setIsConditioned(0);
        List<AggregationFunctionItemInput> aggregationFunctionItemInputList8 = new ArrayList<>();
        AggregationFunctionItemInput aggregationFunctionItemInput8 = new AggregationFunctionItemInput();
        aggregationFunctionItemInput8.setValue("sum");
        aggregationFunctionItemInputList8.add(aggregationFunctionItemInput8);
        aggregationFunctionRuleInput8.setAggTypes(aggregationFunctionItemInputList8);
        aggregationFunctionRuleInputList.add(aggregationFunctionRuleInput8);

        // custom
        AggregationFunctionRuleInput aggregationFunctionRuleInput9 = new AggregationFunctionRuleInput();
        aggregationFunctionRuleInput9.setAggField("total");

        aggregationFunctionRuleInput9.setIsConditioned(0);
        List<AggregationFunctionItemInput> aggregationFunctionItemInputList9 = new ArrayList<>();
        AggregationFunctionItemInput aggregationFunctionItemInput9 = new AggregationFunctionItemInput();
        aggregationFunctionItemInput9.setValue("sum");
        aggregationFunctionItemInputList9.add(aggregationFunctionItemInput9);
        aggregationFunctionRuleInput9.setAggTypes(aggregationFunctionItemInputList9);
        aggregationFunctionRuleInputList.add(aggregationFunctionRuleInput9);

        AggregationCustomFieldRuleInput aggregationCustomFieldRuleInput1 = new AggregationCustomFieldRuleInput();
        aggregationCustomFieldRuleInput1.setFieldName("errorRate");
        aggregationCustomFieldRuleInput1.setExpression("error.sum/ total.sum");

        List<AggregationCustomFieldRuleInput> aggregationCustomFieldRuleInputList = new ArrayList<>();
        aggregationCustomFieldRuleInputList.add(aggregationCustomFieldRuleInput1);
        metricsAggregationRuleComposeInput.setCustomize(aggregationCustomFieldRuleInputList);
        metricsAggregationInput.setMetricsAggregationRuleCompose(metricsAggregationRuleComposeInput);
        return metricsAggregationInput;
    }

    @Test
    public void batchDelMetrics() {
        ThreadLocalStore.setUserInfoLocal("265b4be6-652c-48dd-a8fe-a0f22256a4af");
        ThreadLocalStore.setTenantInfoLocal(tenantId);
        List<MetricsDO> metricsDOList = metricsDAO.listAll().stream().filter(e -> matchMetricsName(e.getMetricsName())).collect(Collectors.toList());
        metricsDOList.stream().forEach(e -> System.out.println(e.getMetricsName()));
        metricsDOList.stream().forEach(e -> {
            IdPara idPara = new IdPara();
            idPara.setId(e.getId());
            System.out.println("start delete metricsName = " + e.getMetricsName());
            // metricsService.deleteMetrics(idPara);
            System.out.println("end delete metricsName = " + e.getMetricsName());
        });

    }

    private boolean matchMetricsName(String metricsName) {
        List<String> nameList = Lists.newArrayList("test_sjjffewf", "test_sjj_agg_old_version_metrics", "sjj-ut-metricsName", "test_sjj_metrics_original", "sjj-metricsName1");
        return nameList.stream().anyMatch(e -> metricsName.contains(e));
    }

    @Test
    public void addOriginalMetricsForDuplicateTagAndField() {
        String time = String.valueOf(System.currentTimeMillis());
        String userId = "265b4be6-652c-48dd-a8fe-a0f22256a4af";
        ThreadLocalStore.setUserInfoLocal(userId);
        ThreadLocalStore.setTenantInfoLocal(tenantId);
        MetricsInput metricsInput0 = new MetricsInput();
        metricsInput0.setTagNames(Lists.newArrayList("fields_2", "cluster"));
        metricsInput0.setType(MetricsTypeEnum.ORIGINAL.getValue());

        metricsInput0.setMetricsName("sjj-ut-metricsName-20210425-" + time);
        metricsInput0.setCollectorId("sjj-ut-collectorId1");
        metricsInput0.setMetricsFieldList(Lists.newArrayList(new MetricsFieldInput(null, null, "fields_2", 1, "")));

        ResponseObject result = metricsService.addMetrics(metricsInput0);
        String metricsId = (String) result.getData();

        MetricsOut metricsOut = (MetricsOut) metricsService.getMetricsById(userId, tenantId,"/api/metric/getMetricDefinitionById", metricsId).getData();
        System.out.println("metricsOut = " + JsonUtils.toJsonString(metricsOut));

    }

    @Test
    public void extractModifyFieldsForOriginalMetrics() {
        ThreadLocalStore.setUserInfoLocal("c793c210-9be2-48f0-8cf5-dd857eafdc40");
        String input = "{\n" +
                "    \"id\": \"f875772c-2a20-44f3-9227-d3f7f6254156\",\n" +
                "    \"metricsName\": \"fred_test\",\n" +
                "    \"tagNames\": [\n" +
                "        \"appName\"\n" +
                "    ],\n" +
                "    \"type\": 1,\n" +
                "    \"originPeriod\": null,\n" +
                "    \"collectorId\": \"36ac1ad9-7aee-417a-8479-4428569fb836\",\n" +
                "    \"tenantId\": \"7b0da9c0-7040-4e85-b3f5-31b8f2559df4\",\n" +
                "    \"creator\": \"<EMAIL>\",\n" +
                "    \"editor\": \"<EMAIL>\",\n" +
                "    \"enabled\": true,\n" +
                "    \"metricsFieldList\": [\n" +
                "        {\n" +
                "            \"id\": \"7b9ed59a-690a-43f6-b775-c8e34cd9b244\",\n" +
                "            \"metricsId\": \"f875772c-2a20-44f3-9227-d3f7f6254156\",\n" +
                "            \"fieldName\": \"cached.count\",\n" +
                "            \"fieldType\": 1,\n" +
                "            \"description\": \"new description\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"id\": \"975be0c8-0f5e-4970-b7d5-1b741e0785cd\",\n" +
                "            \"metricsId\": \"f875772c-2a20-44f3-9227-d3f7f6254156\",\n" +
                "            \"fieldName\": \"cc\",\n" +
                "            \"fieldType\": 1\n" +
                "        },\n" +
                "        {\n" +
                "            \"id\": \"5547f599-32a8-41f9-8e96-7cb920a40d51\",\n" +
                "            \"metricsId\": \"f875772c-2a20-44f3-9227-d3f7f6254156\",\n" +
                "            \"fieldName\": \"dd\",\n" +
                "            \"fieldType\": 1,\n" +
                "            \"description\": \"new description\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"id\": \"537b97e9-077d-46dc-9fb8-05da9cf23001\",\n" +
                "            \"metricsId\": \"f875772c-2a20-44f3-9227-d3f7f6254156\",\n" +
                "            \"fieldName\": \"ee\",\n" +
                "            \"fieldType\": 1\n" +
                "        },\n" +
                "        {\n" +
                "            \"id\": \"e25535f2-a616-4bf3-8072-cae5a7e76f52\",\n" +
                "            \"metricsId\": \"f875772c-2a20-44f3-9227-d3f7f6254156\",\n" +
                "            \"fieldName\": \"no_log_rate\",\n" +
                "            \"fieldType\": 1\n" +
                "        },\n" +
                "        {\n" +
                "            \"id\": \"f2034fe9-99e5-4e40-a9e2-37b507c819f2\",\n" +
                "            \"metricsId\": \"f875772c-2a20-44f3-9227-d3f7f6254156\",\n" +
                "            \"fieldName\": \"requestFailure.count\",\n" +
                "            \"fieldType\": 1\n" +
                "        },\n" +
                "        {\n" +
                "            \"id\": \"f35e4fff-2ea4-411e-8ee4-a0504f2f4028\",\n" +
                "            \"metricsId\": \"f875772c-2a20-44f3-9227-d3f7f6254156\",\n" +
                "            \"fieldName\": \"requestFailure.sum\",\n" +
                "            \"fieldType\": 1\n" +
                "        },\n" +
                "        {\n" +
                "            \"id\": \"fc64d6fe-085f-4973-996d-114ff7e1328d\",\n" +
                "            \"metricsId\": \"f875772c-2a20-44f3-9227-d3f7f6254156\",\n" +
                "            \"fieldName\": \"total.count\",\n" +
                "            \"fieldType\": 1\n" +
                "        },\n" +
                "        {\n" +
                "            \"id\": \"d125366c-bdcb-4f63-a293-905d3968eb2f\",\n" +
                "            \"metricsId\": \"f875772c-2a20-44f3-9227-d3f7f6254156\",\n" +
                "            \"fieldName\": \"total.sum\",\n" +
                "            \"fieldType\": 1\n" +
                "        },\n" +
                "        {\n" +
                "            \"id\": \"3e4f70d4-5242-48fc-8fd9-70acd17d9b69\",\n" +
                "            \"metricsId\": \"f875772c-2a20-44f3-9227-d3f7f6254156\",\n" +
                "            \"fieldName\": \"usedPercent.count\",\n" +
                "            \"fieldType\": 1\n" +
                "        },\n" +
                "        {\n" +
                "            \"id\": \"f1eccd25-820e-4803-adc4-dda35a892326\",\n" +
                "            \"metricsId\": \"f875772c-2a20-44f3-9227-d3f7f6254156\",\n" +
                "            \"fieldName\": \"usedPercent.p50\",\n" +
                "            \"fieldType\": 1\n" +
                "        },\n" +
                "        {\n" +
                "            \"id\": \"59e63567-b24b-4c91-88ea-fc64b5177038\",\n" +
                "            \"metricsId\": \"f875772c-2a20-44f3-9227-d3f7f6254156\",\n" +
                "            \"fieldName\": \"usedPercent.p90\",\n" +
                "            \"fieldType\": 1\n" +
                "        },\n" +
                "        {\n" +
                "            \"id\": \"ab338a3d-9c49-425d-bd63-b6eb1c8203ce\",\n" +
                "            \"metricsId\": \"f875772c-2a20-44f3-9227-d3f7f6254156\",\n" +
                "            \"fieldName\": \"usedPercent.p95\",\n" +
                "            \"fieldType\": 1\n" +
                "        },\n" +
                "        {\n" +
                "            \"id\": \"\",\n" +
                "            \"metricsId\": \"\",\n" +
                "            \"fieldName\": \"testAddField1\",\n" +
                "            \"fieldType\": 1\n" +
                "        },\n" +
                "        {\n" +
                "            \"id\": \"\",\n" +
                "            \"metricsId\": \"\",\n" +
                "            \"fieldName\": \"testAddField2\",\n" +
                "            \"fieldType\": 1\n" +
                "        },\n" +
                "        {\n" +
                "            \"id\": \"\",\n" +
                "            \"metricsId\": \"\",\n" +
                "            \"fieldName\": \"testAddField3\",\n" +
                "            \"fieldType\": 1\n" +
                "        }\n" +
                "    ]\n" +
                "}";
        MetricsInput metricsInput = JsonUtils.toObject(input, MetricsInput.class);
        MetricsOut metricsOut = (MetricsOut) metricsService.getMetricsById("f875772c-2a20-44f3-9227-d3f7f6254156");
        List< MetricsFieldDO > addFields = new ArrayList<>();
        List<String> deleteFields = new ArrayList<>();
        metricsService.extractModifyFieldsForOriginalMetrics(metricsInput, metricsOut, addFields, deleteFields);

        // delete assert
        Assertions.assertEquals(3, deleteFields.size());
        Assertions.assertEquals(true, deleteFields.contains("718d3105-b010-4942-982c-241db6820f26"));
        Assertions.assertEquals(true, deleteFields.contains("0b2f501a-dfd5-4f5d-a97a-3f8bfaee22ab"));
        Assertions.assertEquals(true, deleteFields.contains("19cf1c6c-9756-433e-a517-7c626ea74637"));

        // add assert
        Assertions.assertEquals(3, addFields.size());
        org.assertj.core.api.Assertions.assertThat(addFields).extracting(MetricsFieldDO::getFieldName).contains("testAddField1");
        org.assertj.core.api.Assertions.assertThat(addFields).extracting(MetricsFieldDO::getFieldName).contains("testAddField2");
        org.assertj.core.api.Assertions.assertThat(addFields).extracting(MetricsFieldDO::getFieldName).contains("testAddField3");
    }


}
