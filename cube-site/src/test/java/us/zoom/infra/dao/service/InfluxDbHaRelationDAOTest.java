package us.zoom.infra.dao.service;


import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import us.zoom.cube.site.BaseSpringTest;
import us.zoom.infra.dao.model.InfluxdbHaRelationDO;

import java.util.Collections;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @create 12/20/21 10:10 AM
 */
public class InfluxDbHaRelationDAOTest extends BaseSpringTest {

    @Autowired
    private InfluxdbHaRelationDAO influxDbHaRelationDAO;

    @Test
    public void add() {
        InfluxdbHaRelationDO ido=new InfluxdbHaRelationDO();
        String rid = UUID.randomUUID().toString();
        ido.setId(rid);
        ido.setInfluxClusterId("1");
        ido.setServerUrl("url");
        ido.setIsPrimary(true);
        influxDbHaRelationDAO.add(ido);

        List<InfluxdbHaRelationDO> byClusterId = influxDbHaRelationDAO.getByClusterId("1");

        Assertions.assertTrue(byClusterId.size() > 0);
        List<InfluxdbHaRelationDO> all = influxDbHaRelationDAO.getAll();
        Assertions.assertTrue(all.size() > 0);
        List<InfluxdbHaRelationDO> influxdbHaRelationDOS = influxDbHaRelationDAO.queryByClusterIds(Collections.singletonList("1"));
        Assertions.assertTrue(influxdbHaRelationDOS.size() > 0);
        InfluxdbHaRelationDO edit=new InfluxdbHaRelationDO();
        edit.setId(rid);
        edit.setIsPrimary(false);
        influxDbHaRelationDAO.edit(edit);
        List<InfluxdbHaRelationDO> toTest = influxDbHaRelationDAO.queryByClusterIds(Collections.singletonList("1"));
        Assertions.assertEquals(false, (boolean) toTest.stream().filter(u -> u.getId().equals(rid)).findFirst().get().getIsPrimary());
        influxDbHaRelationDAO.delete(rid);
        toTest = influxDbHaRelationDAO.queryByClusterIds(Collections.singletonList("1"));
        Assertions.assertEquals(0,   toTest.stream().filter(u -> u.getId().equals(rid)).count());
        influxDbHaRelationDAO.delete("1");
        toTest = influxDbHaRelationDAO.queryByClusterIds(Collections.singletonList("1"));
        Assertions.assertEquals(0,   toTest.size());

    }
}
