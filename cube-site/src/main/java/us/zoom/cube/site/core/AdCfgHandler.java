package us.zoom.cube.site.core;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import us.zoom.cube.site.core.model.ad.*;
import us.zoom.infra.dao.model.AdCfgDO;
import us.zoom.infra.dao.model.AdTagCfgDO;
import us.zoom.infra.dao.model.AdTagHolidayCfgDO;
import us.zoom.infra.dao.model.MetricsDO;
import us.zoom.cube.site.core.tag.ResourceTypeConstant;
import us.zoom.cube.site.core.tag.service.ResourceTagService;
import us.zoom.cube.site.lib.output.tag.TagInfoOut;
import us.zoom.infra.dao.model.*;
import us.zoom.infra.dao.service.AdCfgDAO;
import us.zoom.infra.dao.service.AdTagCfgDAO;
import us.zoom.infra.dao.service.AdTagHolidayCfgDAO;
import us.zoom.infra.dao.service.MetricsDAO;
import us.zoom.infra.enums.AdTrainStatusEnum;
import us.zoom.infra.utils.CommonSplitConstants;
import us.zoom.infra.utils.IdUtils;

import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2023-07-06 10:10
 */
@Component
@Slf4j
public class AdCfgHandler {

    private static final String aiConstants = "ai_batch";
    @Autowired
    private AdCfgDAO adCfgDAO;
    @Autowired
    private AdTagCfgDAO adTagCfgDAO;
    @Autowired
    private AdTagHolidayCfgDAO adTagHolidayCfgDAO;
    @Autowired
    private UserHandler userHandler;
    @Autowired
    private MetricsDAO metricsDAO;
    @Autowired
    private AdTrainHandler adTrainHandler;
    @Autowired
    private MetricsHandler metricsHandler;
    @Autowired
    private ResourceTagService resourceTagService;

    @Transactional(rollbackFor = Exception.class)
    public AdCfgDO addAdCfg(AdCfgData adCfgData) {
        AdCfgDO adCfgDO = new AdCfgDO();
        BeanUtils.copyProperties(adCfgData, adCfgDO);
        adCfgDO.setId(IdUtils.generateId());
        String userName = userHandler.getNameById(adCfgData.getUserId());
        adCfgDO.setCreator(userName);
        adCfgDO.setEditor(userName);
        adCfgDAO.add(adCfgDO);
        batchAdTag(adCfgData, adCfgDO.getId());
        // add label info
        if(!CollectionUtils.isEmpty(adCfgData.getLabelInfoList())){
            List<String> tagIds = adCfgData.getLabelInfoList().stream().map(TagInfoOut::getId).filter(Objects::nonNull).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(tagIds)) {
                resourceTagService.batchAddResourceTag(tagIds, ResourceTypeConstant.RESOURCE_AI_MODEL,
                        adCfgDO.getId(), ResourceTypeConstant.RESOURCE_TAG_OPERATION_ADD);
            }
        }
        return adCfgDO;
    }

    @Transactional(rollbackFor = Exception.class)
    public void batchAdTag(AdCfgData adCfgData, String adCfgId) {
        for (AdTagCfgData adTagCfgData : adCfgData.getAdTagCfgDataList()) {
            AdTagCfgDO adTagCfgDO = new AdTagCfgDO();
            BeanUtils.copyProperties(adTagCfgData, adTagCfgDO);
            adTagCfgDO.setAdConfigId(adCfgId);
            adTagCfgDO.setId(IdUtils.generateId());
            adTagCfgDAO.add(adTagCfgDO);
            batchAdTagHoliday(adTagCfgData, adCfgId, adTagCfgDO.getId());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void batchAdTagHoliday(AdTagCfgData adTagCfgData, String adCfgId, String adCfgTagId) {
        List<AdTagHolidayCfgDO> adTagHolidayCfgDOList = processAdTagHoliday(adTagCfgData, adCfgId, adCfgTagId);
        if (!CollectionUtils.isEmpty(adTagHolidayCfgDOList)) {
            adTagHolidayCfgDAO.batchAdd(adTagHolidayCfgDOList);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public List<AdTagHolidayCfgDO> processAdTagHoliday(AdTagCfgData adTagCfgData, String adCfgId, String adTagCfgId) {
        List<AdTagHolidayCfgDO> adTagHolidayCfgDOList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(adTagCfgData.getTagHolidayCfgDataList())) {
            for (AdTagHolidayCfgData adTagHolidayCfgData : adTagCfgData.getTagHolidayCfgDataList()) {
                AdTagHolidayCfgDO adTagHolidayCfgDO = new AdTagHolidayCfgDO();
                BeanUtils.copyProperties(adTagHolidayCfgData, adTagHolidayCfgDO);
                adTagHolidayCfgDO.setAdConfigId(adCfgId);
                adTagHolidayCfgDO.setId(IdUtils.generateId());
                adTagHolidayCfgDO.setAdTagConfigId(adTagCfgId);
                adTagHolidayCfgDOList.add(adTagHolidayCfgDO);
            }
        }
        return adTagHolidayCfgDOList;
    }

    @Transactional(rollbackFor = Exception.class)
    public void batchAddAdCfg(AdCfgData adCfgData) {
        String[] fieldNameList = adCfgData.getFieldName().split(CommonSplitConstants.COMMA_SPLIT);
        for (String field : fieldNameList) {
            AdCfgData batchAdCfgData = new AdCfgData();
            BeanUtils.copyProperties(adCfgData, batchAdCfgData);
            StringBuilder adName = new StringBuilder();
            adName.append(adCfgData.getMetricsName()).append(CommonSplitConstants.SPLIT).append(field).
                    append(CommonSplitConstants.SPLIT).append(adCfgData.getAggPeriod()).
                    append(CommonSplitConstants.SPLIT).append(aiConstants);
            batchAdCfgData.setFieldName(field);
            adName = getUniqueAdName(adName, Lists.newArrayList(batchAdCfgData.getTenantId()));
            batchAdCfgData.setAdName(adName.toString());
            AdCfgDO adCfgDO = addAdCfg(batchAdCfgData);
            AdTrainData adTrainData = new AdTrainData();
            adTrainData.setMetricsId(adCfgDO.getMetricsId());
            adTrainData.setAdTrainStatus(AdTrainStatusEnum.initialising.getStatus());
            adTrainData.setAdConfigId(adCfgDO.getId());
            adTrainData.setTenantId(adCfgDO.getTenantId());
            adTrainHandler.addAdTrainStatus(adTrainData);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteAdCfg(String id) {
        adCfgDAO.deleteById(id);
        adTagCfgDAO.deleteByAdId(id);
        adTagHolidayCfgDAO.deleteByAdId(id);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateAdCfg(AdCfgData adCfgData) {
        AdCfgDO adCfgDO = new AdCfgDO();
        BeanUtils.copyProperties(adCfgData, adCfgDO);
        String userName = userHandler.getNameById(adCfgData.getUserId());
        adCfgDO.setEditor(userName);
        adCfgDAO.editAd(adCfgDO);
        adTagCfgDAO.deleteByAdId(adCfgData.getId());
        adTagHolidayCfgDAO.deleteByAdId(adCfgData.getId());
        //edit tag
        List<String> currentTags = Optional.ofNullable(adCfgData.getLabelInfoList()).orElse(Collections.emptyList()).stream().
                map(tagInfoOut -> tagInfoOut.getId()).collect(Collectors.toList());
        resourceTagService.handleEditResourceTag(currentTags, adCfgData.getTenantId(), adCfgData.getId(), ResourceTypeConstant.RESOURCE_AI_MODEL);
        batchAdTag(adCfgData, adCfgDO.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteAdCfgByMetricsId(String metricsId) {
        String adId = getAdIdByMetricsId(metricsId);
        if (StringUtils.isNotBlank(adId)) {
            adCfgDAO.deleteByMetricsId(metricsId);
            adTagCfgDAO.deleteByAdId(adId);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void batchDeleteAdCfgByMetricsIds(List<String> metricsIds) {
        List<String> adIds = batchGetAdIdByMetricsId(metricsIds);
        if (CollectionUtil.isNotEmpty(adIds)) {
            adCfgDAO.batchDeleteByMetricsId(metricsIds);
            adTagCfgDAO.batchDeleteByIdList(adIds);
        }
    }

    public boolean checkMetric(String metricsId, String fieldName) {
        if (StringUtils.isNotBlank(metricsId) && StringUtils.isNotBlank(fieldName)) {
            List<MetricsFieldDO> metricsFieldDOList = metricsHandler.listFieldsByMetricsIds(Collections.singletonList(metricsId));
            if(CollectionUtils.isNotEmpty(metricsFieldDOList)){
                return metricsFieldDOList.stream()
                        .filter(Objects::nonNull)
                        .anyMatch(field -> StringUtils.equals(field.getFieldName(), fieldName) &&
                                StringUtils.equals(field.getMetricsId(), metricsId));
            }
        }
        return false;
    }

    public List<String> getAdNameByMetricsId(List<String> metricsIds) {
        return adCfgDAO.getAdNameByMetricsId(metricsIds);
    }

    public String getAdIdByMetricsId(String metricsId) {
        return adCfgDAO.getAdIdByMetricsId(metricsId);
    }

    public List<String> batchGetAdIdByMetricsId(List<String> metricsIds) {
        return adCfgDAO.batchGetAdIdByMetricsIds(metricsIds);
    }

    public List<AdCfgData> getAdCfgByMetricsId(String metricsId) {
        List<AdCfgData> adCfgDatas = new ArrayList<>();
        List<AdCfgDO> adCfgDOList = adCfgDAO.getByMetricsId(metricsId);
        if (!CollectionUtils.isEmpty(adCfgDOList)) {
            for (AdCfgDO adCfgDO : adCfgDOList) {
                AdCfgData adCfgData = new AdCfgData();
                BeanUtils.copyProperties(adCfgDO, adCfgData);
                List<AdTagCfgDO> adTagCfgDOList = adTagCfgDAO.getByAdId(adCfgDO.getId());
                List<AdTagCfgData> adTagCfgDataList = createTagCfgDO(adTagCfgDOList);
                adCfgData.setAdTagCfgDataList(adTagCfgDataList);
                adCfgDatas.add(adCfgData);
            }
        }
        return adCfgDatas;
    }

    public List<AdCfgDO> findByAdNameLike(String adName, String metricsName, String fieldName, Boolean adStatus, List<String> tenantIds, List<String> labelIds, Integer labelSize, String labelMatchType,  int pageIndex, int pageSize) {
        return adCfgDAO.findByAdNameLike(adName, metricsName, fieldName, adStatus, tenantIds, labelIds,labelSize, labelMatchType, (pageIndex - 1) * pageSize, pageSize);
    }

    public int getCountByAdNameLike(String adName, String metricsName, String fieldName, Boolean adStatus, List<String> tenantIds, List<String> labelIds, Integer labelSize, String labelMatchType) {
        return adCfgDAO.getCountByAdNameLike(adName, metricsName, fieldName, adStatus, tenantIds, labelIds,labelSize, labelMatchType );
    }

    public List<String> getFieldNameByMetricsId(String metricsId) {
        List<AdCfgData> adCfgList = getAdCfgByMetricsId(metricsId);
        List<String> fieldNameList = new ArrayList<>();
        List<String> resultList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(adCfgList)) {
            for (AdCfgData adCfgData : adCfgList) {
                fieldNameList.add(adCfgData.getFieldName());
            }
            if (!CollectionUtils.isEmpty(fieldNameList)) {
                resultList = fieldNameList.stream().distinct().collect(Collectors.toList());
            }
        }
        return resultList;
    }

    public List<AdTagCfgData> createTagCfgDO(List<AdTagCfgDO> adTagCfgDOList) {
        List<AdTagCfgData> adTagCfgDataList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(adTagCfgDOList)) {
            for (AdTagCfgDO adTagCfgDO : adTagCfgDOList) {
                AdTagCfgData adTagCfgData = new AdTagCfgData();
                BeanUtils.copyProperties(adTagCfgDO, adTagCfgData);
                adTagCfgData.setTagHolidayCfgDataList(batchTagHolidayByTagId(adTagCfgDO.getId()));
                adTagCfgDataList.add(adTagCfgData);
            }
        }
        return adTagCfgDataList;
    }

    public List<AdTagHolidayCfgData> createTagHolidayCfgDO(List<AdTagHolidayCfgDO> adTagHolidayCfgDOList) {
        List<AdTagHolidayCfgData> adTagHolidayCfgDataList = new ArrayList<>();
        for (AdTagHolidayCfgDO adTagHolidayCfgDO : adTagHolidayCfgDOList) {
            AdTagHolidayCfgData adTagHolidayCfgData = new AdTagHolidayCfgData();
            BeanUtils.copyProperties(adTagHolidayCfgDO, adTagHolidayCfgData);
            adTagHolidayCfgDataList.add(adTagHolidayCfgData);
        }
        return adTagHolidayCfgDataList;
    }

    public List<AdCfgDataOut> getAdCfgById(String id) {
        List<AdCfgDataOut> adCfgDataList = new ArrayList<>();
        List<AdCfgDO> adCfgDOList = adCfgDAO.getById(id);
        if (!CollectionUtils.isEmpty(adCfgDOList)) {
            for (AdCfgDO adCfgDO : adCfgDOList) {
                AdCfgDataOut adCfgData = new AdCfgDataOut();
                BeanUtils.copyProperties(adCfgDO, adCfgData);
                List<AdTagCfgDO> adTagCfgDOList = adTagCfgDAO.getByAdId(adCfgDO.getId());
                List<AdTagCfgData> adTagCfgDataList = createTagCfgDO(adTagCfgDOList);
                adCfgData.setAdTagCfgDataList(adTagCfgDataList);
                adCfgDataList.add(adCfgData);
            }
        }
        return adCfgDataList;
    }

    public List<AdTagHolidayCfgData> batchTagHolidayByTagId(String adTagId) {
        List<AdTagHolidayCfgData> adTagHolidayCfgDataList = new ArrayList<>();
        if (!StringUtils.isEmpty(adTagId)) {
            adTagHolidayCfgDataList = createTagHolidayCfgDO(adTagHolidayCfgDAO.getByAdTagId(adTagId));
        }
        return adTagHolidayCfgDataList;
    }


    public List<AdTagCfgData> batchTagByAdId(List<String> idList) {
        List<AdTagCfgData> adTagCfgDataList = new ArrayList<>();
        if (!org.apache.commons.collections.CollectionUtils.isEmpty(idList)) {
            List<AdTagCfgDO> adTagCfgDOList = adTagCfgDAO.batchAdTagByAdIdList(idList);
            adTagCfgDataList = createTagCfgDO(adTagCfgDOList);
        }
        return adTagCfgDataList;
    }

    public boolean hasSameAdName(String adName, List<String> tenantIds) {
        return adCfgDAO.getCountByAdName(adName, tenantIds) > 0;
    }

    public StringBuilder getUniqueAdName(StringBuilder adName, List<String> tenantIds) {
        String originalAdName = adName.toString();
        StringBuilder checkAdName = new StringBuilder(originalAdName);
        int counter = 0;
        while (hasSameAdName(checkAdName.toString(), tenantIds)) {
            counter++;
            checkAdName.setLength(0);
            checkAdName.append(originalAdName).append(CommonSplitConstants.SPLIT).append(counter);
        }
        return checkAdName;
    }


    public List<AdCfgDO> getAllAdCfg() {
        return adCfgDAO.getAllCfg();
    }

    public List<AdTagCfgDO> getAllAdTagCfg() {
        return adTagCfgDAO.getAll();
    }

    public List<MetricsDO> getMetricByTenant(List<String> tenantIds) {
        return metricsDAO.listMetricsByTenantIds(tenantIds);
    }

    public void addAdCfgTrainStatus(AdCfgDO adCfgDO) {
        AdTrainData adTrainData = new AdTrainData();
        adTrainData.setMetricsId(adCfgDO.getMetricsId());
        adTrainData.setAdTrainStatus(AdTrainStatusEnum.initialising.getStatus());
        adTrainData.setAdConfigId(adCfgDO.getId());
        adTrainData.setTenantId(adCfgDO.getTenantId());
        adTrainHandler.addAdTrainStatus(adTrainData);
    }

    public List<AdCfgDataOut> getAdCfgByTenantId(String tenantId) {
        List<AdCfgDO> adCfgDOList = Optional.ofNullable(adCfgDAO.getByTenantId(tenantId)).orElse(Collections.emptyList());
        return adCfgDOList.stream()
                .map(this::convertToAdCfgDataOut)
                .collect(Collectors.toList());
    }

    private AdCfgDataOut convertToAdCfgDataOut(AdCfgDO adCfgDO) {
        AdCfgDataOut adCfgData = new AdCfgDataOut();
        BeanUtils.copyProperties(adCfgDO, adCfgData);
        List<AdTagCfgDO> adTagCfgDOList = Optional.ofNullable(adTagCfgDAO.getByAdId(adCfgDO.getId()))
                .orElse(Collections.emptyList());
        List<AdTagCfgData> adTagCfgDataList = createTagCfgDO(adTagCfgDOList);
        adCfgData.setAdTagCfgDataList(adTagCfgDataList);
        return adCfgData;
    }
}
