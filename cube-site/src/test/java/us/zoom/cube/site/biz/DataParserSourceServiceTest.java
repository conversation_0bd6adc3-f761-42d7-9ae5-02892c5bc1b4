package us.zoom.cube.site.biz;


import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import us.zoom.cube.lib.hub.DataParserRawDataTypeEnum;
import us.zoom.cube.site.core.*;
import us.zoom.cube.site.infra.utils.IdUtils;
import us.zoom.cube.site.lib.BasePara;
import us.zoom.cube.site.lib.IdPara;
import us.zoom.cube.site.lib.input.dataparser.DataParserSourceInput;
import us.zoom.cube.site.lib.input.dataparser.UpgradeToDataParserSource;
import us.zoom.cube.site.lib.query.DataParserSourceQuery;
import us.zoom.cube.site.lib.query.PageQuery;
import us.zoom.infra.dao.model.DataParserDO;
import us.zoom.infra.dao.model.DataParserSourceDO;
import us.zoom.infra.enums.CommonStatusEnum;

import java.util.Arrays;

/**
 * @Author: <EMAIL>
 * @ModuleOwner: <EMAIL>
 * @Date: 03/11/2022 10:07
 * @Description:
 */
public class DataParserSourceServiceTest {
    private DataParserSourceService dataParserSourceService;

    private AuthService authService;
    private DataParserSourceHandler dataParserSourceHandler;
    private DataParserHandler dataParserHandler;
    private TenantHandler tenantHandler;
    private UserHandler userHandler;
    private  DataParserPipelineHandler dataParserPipelineHandler;
    private DataFlowDataParserRelationHandler dataFlowDataParserRelationHandler;
    private CollectorHandler collectorHandler;
    private AlarmDefinitionHandler alarmDefinitionHandler;
    private MetricsHandler metricsHandler;
    private SysParaHandler sysParaHandler;
    private DataParserDeleteService dataParserDeleteService;

    private DataParserSourceInput input = new DataParserSourceInput();
    private HubGroovyCheckService hubGroovyCheckService;
    private String userId = "userId";
    private String tenantId = "tenantId";

    @BeforeEach
    public void test() {
        authService = Mockito.mock(AuthService.class);
//        Mockito.doNothing().when(authService).mustAdmin(userId);
        Mockito.doNothing().when(authService).checkAuth(new BasePara());
        dataParserSourceHandler = Mockito.mock(DataParserSourceHandler.class);
        dataParserHandler = Mockito.mock(DataParserHandler.class);

        input.setId(IdUtils.generateId());
        input.setTenantId(tenantId);
        input.setName("name");
        input.setSource("source");
        input.setRawDataFormat("RawDataFormat(");
        input.setRawDataParseType("RawDataParseType");
        input.setInvokeFunction("InvokeFunction");
        input.setRawDataParseRule("RawDataParseRule(");
        input.setStatus(CommonStatusEnum.disable.name());
        hubGroovyCheckService = Mockito.mock(HubGroovyCheckService.class);

        dataParserSourceService = new DataParserSourceService(authService, dataParserSourceHandler,
                dataParserHandler, tenantHandler,
                userHandler, dataFlowDataParserRelationHandler,
                dataParserPipelineHandler, collectorHandler,
                alarmDefinitionHandler, dataParserDeleteService,
                metricsHandler, sysParaHandler, hubGroovyCheckService);
    }

    @Test
    public void testSearchDataParserSource() throws Exception {
        DataParserSourceQuery dataParserSourceQuery = new DataParserSourceQuery();
        dataParserSourceQuery.setName("name");
        dataParserSourceQuery.setCollectType("collectType");
        dataParserSourceQuery.setSource("source");
        dataParserSourceQuery.setRawDataFormat(DataParserRawDataTypeEnum.json.name());
        dataParserSourceQuery.setStatus(CommonStatusEnum.enable.name());
        dataParserSourceQuery.setTenantId(tenantId);

        PageQuery<DataParserSourceQuery> pageQuery = new PageQuery<>();
        pageQuery.setQueryPara(dataParserSourceQuery);

        Mockito.when(dataParserSourceHandler.findByParam(
                dataParserSourceQuery.getTenantId(),
                dataParserSourceQuery.getName(),
                dataParserSourceQuery.getCollectType(),
                dataParserSourceQuery.getSource(),
                dataParserSourceQuery.getRawDataFormat(),
                dataParserSourceQuery.getStatus(),
                pageQuery.getPageIndex(), pageQuery.getPageSize())).thenReturn(Arrays.asList(new DataParserSourceDO(), new DataParserSourceDO()));

        Mockito.when(dataParserSourceHandler.getCountByParam(
                dataParserSourceQuery.getTenantId(),
                dataParserSourceQuery.getName(),
                dataParserSourceQuery.getCollectType(),
                dataParserSourceQuery.getSource(),
                dataParserSourceQuery.getRawDataFormat(),
                dataParserSourceQuery.getStatus())).thenReturn(2);

        dataParserSourceService.searchDataParserSource(pageQuery);
    }

    @Test
    public void testAddDataParserSource() {
        Mockito.when(dataParserSourceHandler.getCountByName(input.getTenantId(), input.getName())).thenReturn(1);
        IllegalArgumentException err = Assertions.assertThrows(IllegalArgumentException.class, () -> {
            dataParserSourceService.addDataParserSource(input);
        });
        Assertions.assertEquals("name is exist", err.getMessage());
    }

    @Test
    public void testAddDataParserSource1() {
        Mockito.when(dataParserSourceHandler.getCountByCollectTypeAndSource(input.getTenantId(), input.getCollectType(), input.getSource())).thenReturn(1);
        IllegalArgumentException err = Assertions.assertThrows(IllegalArgumentException.class, () -> {
            dataParserSourceService.addDataParserSource(input);
        });
        Assertions.assertEquals("collectType and source is exist", err.getMessage());
    }

    @Test
    public void testEditDataParserSource1() {
        input.setId(null);
        IllegalArgumentException err = Assertions.assertThrows(IllegalArgumentException.class, () -> {
            dataParserSourceService.editDataParserSource(input);
        });
        Assertions.assertEquals("id is null!", err.getMessage());
    }

    @Test
    public void testEditDataParserSource2() {
        Mockito.when(dataParserSourceHandler.getCountByNameNotId(input.getTenantId(), input.getName(), input.getId())).thenReturn(1);
        IllegalArgumentException err = Assertions.assertThrows(IllegalArgumentException.class, () -> {
            dataParserSourceService.editDataParserSource(input);
        });
        Assertions.assertEquals("name is exist", err.getMessage());
    }

    @Test
    public void testEditDataParserSource3() {
        Mockito.when(dataParserSourceHandler.getCountByCollectTypeAndSourceNotId(input.getTenantId(),
                input.getCollectType(),
                input.getSource(),
                input.getId())).thenReturn(1);
        IllegalArgumentException err = Assertions.assertThrows(IllegalArgumentException.class, () -> {
            dataParserSourceService.editDataParserSource(input);
        });
        Assertions.assertEquals("collectType and source is exist", err.getMessage());
    }

    @Test
    public void testGetById() {
        IdPara idPara = new IdPara();
        idPara.setId(null);
        IllegalArgumentException err = Assertions.assertThrows(IllegalArgumentException.class, () -> {
            dataParserSourceService.getById(idPara);
        });
        Assertions.assertEquals("id is null!", err.getMessage());
    }

    @Test
    public void testDelById() {
        IdPara idPara = new IdPara();
        idPara.setId(null);
        IllegalArgumentException err = Assertions.assertThrows(IllegalArgumentException.class, () -> {
            dataParserSourceService.getById(idPara);
        });
        Assertions.assertEquals("id is null!", err.getMessage());
    }

    @Test
    public void testUpgradeToDataParserSource() {
        UpgradeToDataParserSource upgrade = new UpgradeToDataParserSource();
        upgrade.setSource("source");
        upgrade.setCollectType("file");
        IllegalArgumentException err = Assertions.assertThrows(IllegalArgumentException.class, () -> {
            dataParserSourceService.upgradeToDataParserSource(upgrade);
        });
        Assertions.assertEquals("dataParserId is null!", err.getMessage());
    }

    @Test
    public void testUpgradeToDataParserSource1() {
        UpgradeToDataParserSource upgrade = new UpgradeToDataParserSource();
        upgrade.setDataParserId(IdUtils.generateId());
        upgrade.setSource("source");
        upgrade.setCollectType("file");
        upgrade.setTenantId(tenantId);
        Mockito.when(dataParserSourceHandler.getCountByCollectTypeAndSource(upgrade.getTenantId(), upgrade.getCollectType(), upgrade.getSource())).thenReturn(1);

        IllegalArgumentException err = Assertions.assertThrows(IllegalArgumentException.class, () -> {
            dataParserSourceService.upgradeToDataParserSource(upgrade);
        });
        Assertions.assertEquals("collectType and source is exist", err.getMessage());
    }

    @Test
    public void testUpgradeToDataParserSource2() {
        UpgradeToDataParserSource upgrade = new UpgradeToDataParserSource();
        upgrade.setDataParserId(IdUtils.generateId());
        upgrade.setSource("source");
        upgrade.setCollectType("file");
        Mockito.when(dataParserHandler.getById(upgrade.getDataParserId())).thenReturn(null);

        IllegalArgumentException err = Assertions.assertThrows(IllegalArgumentException.class, () -> {
            dataParserSourceService.upgradeToDataParserSource(upgrade);
        });
        Assertions.assertEquals("dataParser not exist, dataParserId:" + upgrade.getDataParserId(), err.getMessage());
    }

    @Test
    public void testUpgradeToDataParserSource3() {
        UpgradeToDataParserSource upgrade = new UpgradeToDataParserSource();
        upgrade.setDataParserId(IdUtils.generateId());
        upgrade.setSource("source");
        upgrade.setCollectType("file");
        DataParserDO dataParserDO = new DataParserDO();
        dataParserDO.setPurpose(0);
        Mockito.when(dataParserHandler.getById(upgrade.getDataParserId())).thenReturn(dataParserDO);
        IllegalArgumentException err = Assertions.assertThrows(IllegalArgumentException.class, () -> {
            dataParserSourceService.upgradeToDataParserSource(upgrade);
        });
        Assertions.assertEquals("dataParser must be data processing type", err.getMessage());
    }
}
