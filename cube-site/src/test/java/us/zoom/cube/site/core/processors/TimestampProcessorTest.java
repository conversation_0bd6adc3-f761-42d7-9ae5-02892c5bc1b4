package us.zoom.cube.site.core.processors;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import us.zoom.cube.lib.hub.TimestampProcessorCfg;
import us.zoom.cube.site.core.parser.process.core.processor.Processor;
import us.zoom.cube.site.core.parser.process.core.processor.TimestampProcessor;
import us.zoom.infra.utils.DateUtils;

import java.text.ParseException;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * @Author: <EMAIL>
 * @ModuleOwner: <EMAIL>
 * @Date: 02/14/2023 13:36
 * @Description:
 */
public class TimestampProcessorTest {
    @Test
    public void test() {
        Processor processor = new TimestampProcessor();

        Map<String, Object> messageMap = new HashMap<>();
        String dateFormat = "yyyy-MM-dd HH:mm:ssSSS";
        Date date = new Date();
        messageMap.put("time", DateUtils.format(date, dateFormat));

        TimestampProcessorCfg processorCfg = new TimestampProcessorCfg();
        processorCfg.setSourceField("time");
        processorCfg.setTargetField("ts");
        processorCfg.setDateFormat(dateFormat);
        processor.process(messageMap, processorCfg);
        Assertions.assertEquals(date.getTime(), messageMap.get("ts"));


        String time1 = "2024-07-02T05:13:12.851Z";
        String dateFormat1 = "yyyy-MM-dd'T'HH:mm:ss.SSS";
        try {
            System.out.println(DateUtils.parseDate(time1, dateFormat1).getTime());
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }
}
