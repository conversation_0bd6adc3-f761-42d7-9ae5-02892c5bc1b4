package us.zoom.infra.dao.service;


import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;
import us.zoom.infra.dao.model.DashHistoryDO;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
@Repository
public interface DashHistoryDAO {
    @Insert(value = "<script>" +
            "insert into dash_history(id, version, dash_id, json, modifier, comment, create_time, modify_time) " +
            "values(#{id}, #{version}, #{dashId}, #{json}, #{modifier}, #{comment}, now(), now())" +
            "</script>")
    void add(DashHistoryDO dashHistoryDO);

    @Select(value = "<script>" +
            " select max(version)" +
            " from dash_history where dash_id = #{dashId}" +
            "</script>")
    Integer queryMaxVersion(@Param("dashId") String dashId);

    @Select(value = "<script>" +
            " select id, version, dash_id, json, modifier, comment, create_time, modify_time" +
            " from dash_history where dash_id = #{dashId}" +
            " order by version desc" +
            " limit #{pageIndex},#{pageSize} " +
            "</script>")
    List<DashHistoryDO> query(@Param("dashId") String dashId, @Param("pageIndex") long pageIndex, @Param("pageSize") int pageSize);

    @Select(value = "<script>" +
            " select count(id)" +
            " from dash_history where dash_id = #{dashId}" +
            "</script>")
    int count(@Param("dashId") String dashId);

    @Delete(value = "<script>" +
            " delete" +
            " from dash_history where dash_id = #{dashId} and version <![CDATA[ < ]]> #{version}" +
            "</script>")
    int deleteOldVersion(@Param("dashId") String dashId, @Param("version") int version);

    @Select(value = "<script>" +
            " select id, version, dash_id, json, modifier, comment, create_time, modify_time" +
            " from dash_history where dash_id = #{dashId} and version = #{version} " +
            "</script>")
    DashHistoryDO queryVersion(@Param("dashId") String dashId, @Param("version") int version);
}
