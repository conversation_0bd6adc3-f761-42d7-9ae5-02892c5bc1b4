package us.zoom.cube.site.biz;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Maps;
import com.zoom.op.monitor.dao.alarm.AlarmDefinitionDao;
import com.zoom.op.monitor.dao.alarm.ChannelDao;
import com.zoom.op.monitor.domain.IdAndName;
import com.zoom.op.monitor.domain.alarm.*;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.lib.common.AsyncMqQueueSourceTypeEnum;
import us.zoom.cube.lib.common.RoleTypeEnum;
import us.zoom.cube.lib.utils.JsonUtils;
import us.zoom.cube.sdk.util.MonitorLogReporter;
import us.zoom.cube.site.api.intercept.AuthInterceptor;
import us.zoom.cube.site.biz.alarm.ChannelService;
import us.zoom.cube.site.biz.alarm.group.AlarmGroupService;
import us.zoom.cube.site.biz.alarm.silence.AlarmSilenceService;
import us.zoom.cube.site.biz.alarm.silence.AlarmSilenceUtilService;
import us.zoom.cube.site.biz.clickhouse.ClickhouseMetricsService;
import us.zoom.cube.site.biz.inspection.service.AsyncZcpInspectPlanService;
import us.zoom.cube.site.biz.syspara.CubeTemplateParaService;
import us.zoom.cube.site.biz.template.TemplateInnerService;
import us.zoom.cube.site.core.*;
import us.zoom.cube.site.core.alarm.silence.AlarmSilenceHandler;
import us.zoom.cube.site.core.auth.AuthHandler;
import us.zoom.cube.site.core.auth.TenantUserRelaHandler;
import us.zoom.cube.site.external.metadata.enums.ServiceLevelEnum;
import us.zoom.cube.site.infra.enums.WebCodeEnum;
import us.zoom.cube.site.lib.BusinessException;
import us.zoom.cube.site.lib.ResponseObject;
import us.zoom.cube.site.lib.dto.ZcpGetTopicDTO;
import us.zoom.cube.site.lib.dto.template.TemplateAndTenantDTO;
import us.zoom.cube.site.lib.input.UserInCache;
import us.zoom.cube.site.lib.input.alarm.silence.AlarmSilenceInput;
import us.zoom.cube.site.lib.input.alarm.silence.AlarmSilenceStatusByNameInput;
import us.zoom.cube.site.lib.input.alarm.silence.AlarmSilenceStatusInput;
import us.zoom.cube.site.lib.input.alarm.silence.TimeZoneQueryInput;
import us.zoom.cube.site.lib.input.template.InnerTemplateBindInput;
import us.zoom.cube.site.lib.monitor.SilenceCallbackErrorMonitorLogEntity;
import us.zoom.cube.site.lib.output.template.TemplateCheckResultOutput;
import us.zoom.cube.site.lib.output.template.TemplateDataboardInfoOutput;
import us.zoom.cube.site.lib.output.template.ZcpIdAndNameOutput;
import us.zoom.cube.site.lib.query.*;
import us.zoom.cube.site.lib.query.zcp.*;
import us.zoom.infra.dao.model.*;
import us.zoom.infra.dao.service.*;
import us.zoom.infra.enums.*;
import us.zoom.infra.model.LogResult;
import us.zoom.infra.model.template.CubeTemplateConfigSysModel;
import us.zoom.infra.notification.channel.zoomchat.IMChannelEngine;
import us.zoom.infra.thread.ThreadLocalStore;
import us.zoom.infra.utils.IdUtils;
import us.zoom.infra.utils.Instance;
import us.zoom.infra.utils.IpUtils;

import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static us.zoom.cube.site.biz.AsyncmqLoginTokenService.RETRY_REQUEST_ASYNCMQ;
import static us.zoom.cube.site.biz.InputQueueApprovalRecommendService.topic_start_with_eu02;
import static us.zoom.cube.site.biz.template.TemplateConstant.*;
import static us.zoom.infra.utils.AlarmGroupConstants.ALL_ALARM_ID;
import static us.zoom.infra.utils.AlarmGroupConstants.ALL_ALARM_NAME;

@Service
@Slf4j
public class ZcpService {

    @Autowired
    private TenantService tenantService;

    @Autowired
    private TenantHandler tenantHandler;

    @Autowired
    private SysParaService sysParaService;

    @Autowired
    private DashHandler dashHandler;

    @Autowired
    private TemplateHandlerV2 templateHandlerV2;

    @Autowired
    private TemplateItemHandler templateItemHandler;

    @Autowired
    private DataParserHandler dataParserHandler;

    @Autowired
    private TenantUserRelaHandler tenantUserRelaHandler;

    @Autowired
    private UserHandler userHandler;

    @Autowired
    private DataParserSourceHandler dataParserSourceHandler;

    @Autowired
    private DataParserSourceDAO dataParserSourceDAO;

    @Autowired
    private ChannelHandler channelHandler;

    @Autowired
    private DataParserPipelineHandler dataParserPipelineHandler;

    @Autowired
    private CollectorHandler collectorHandler;

    @Autowired
    private AlarmDefinitionDao alarmDefinitionDao;

    @Autowired
    private AuthService authService;

    @Autowired
    private UserDAO userDAO;

    @Autowired
    TemplateInnerService templateInnerService;

    @Autowired
    private ChannelDao channelDao;

    @Autowired
    private Environment environment;

    @Autowired
    private ChannelService channelService;

    @Autowired
    private AsyncZcpInspectPlanService asyncZcpInspectPlanService;

    @Autowired
    private AuthHandler authHandler;

    @Autowired
    private ClickhouseMetricsService clickhouseMetricsService;

    @Autowired
    private AlarmSilenceService alarmSilenceService;

    @Autowired
    private AlarmGroupService alarmGroupService;

    @Autowired
    private DataFlowDataParserRelationDAO dataFlowDataParserRelationDAO;

    @Autowired
    private AsyncMqQueueHandler asyncMqQueueHandler;

    @Autowired
    private CubeTemplateParaService cubeTemplateParaService;

    @Autowired
    private TemplateServiceRelationDAO templateServiceRelationDAO;

    @Autowired
    private CubeTemplateItemServiceRelaDAO cubeTemplateItemServiceRelaDAO;

    @Autowired
    private AlarmSilenceUtilService alarmSilenceUtilService;

    @Autowired
    private AlarmSilenceHandler alarmSilenceHandler;

    @Autowired
    private TenantDAO tenantDAO;

    private Logger monitorLog = LoggerFactory.getLogger("Monitor");

    public ResponseObject getTypeName() {
        String result = sysParaService.getCubeTemplateCache(ZCP_TYPE_NAME);
        if (StringUtils.isEmpty(result)) {
            return ResponseObject.success(null);
        }
        List<TemplateInnerService.TypeTemplateMapping> allTypeTemplateMappings = JsonUtils.toObjectByTypeRef(result, new TypeReference<List<TemplateInnerService.TypeTemplateMapping>>() {
                })
                .stream().filter(t -> !t.getType().equals(cubeTemplateParaService.geFilterType()))
                .collect(Collectors.toList());

        return ResponseObject.success(allTypeTemplateMappings);
    }

    public Map<String, Object> getTopic(ZcpGetTopicDTO zcpGetTopicDTO) {
        Map<String, Object> result = Maps.newHashMap();
        TenantDO tenantDO = tenantService.getTenantByName(zcpGetTopicDTO.getAppName());
        Assert.isTrue(null != tenantDO, "This service does not exist in the cube");

        result.put("appName", zcpGetTopicDTO.getAppName());
        if (CollectionUtils.isEmpty(zcpGetTopicDTO.getTypeList())) {
            return result;
        }
        Map<String, String> topics = new HashMap<>();
        String typeTemplate = sysParaService.getCubeTemplateCache(ZCP_TYPE_NAME);
        List<TemplateInnerService.TypeTemplateMapping> allTypeTemplateMappings = JsonUtils.toObjectByTypeRef(typeTemplate, new TypeReference<List<TemplateInnerService.TypeTemplateMapping>>() {
        });
        Map<String, List<String>> allTypeTemplateMappingMaps = new HashMap<>();
        allTypeTemplateMappings.forEach(e -> {
            allTypeTemplateMappingMaps.put(e.getType(), e.getTemplateList());
        });
        for (String type : zcpGetTopicDTO.getTypeList()) {
            List<String> template = allTypeTemplateMappingMaps.get(type);
            if (!CollectionUtils.isEmpty(template)) {
                template.forEach(t -> {
                    TemplateDO templateDO = templateHandlerV2.getByName(t);
                    if (Objects.nonNull(templateDO)) {
                        List<TemplateItemDO> templateItemDOS = templateItemHandler.listByTemplateId(templateDO.getId());
                        templateItemDOS.forEach(templateItemDO -> {
                            switch (templateItemDO.getType()) {
                                case 1:
                                    DataParserDO dataParserDO = dataParserHandler.getById(templateItemDO.getItemId());
                                    if (null != dataParserDO && !StringUtils.isEmpty(dataParserDO.getTopicTemplate())) {
                                        Map<String, String> topicTemplateMap = JsonUtils.toObjectByTypeRef(dataParserDO.getTopicTemplate(), new TypeReference<Map<String, String>>() {
                                        });
                                        topics.put(type, topicTemplateMap.get("zcp"));
                                    }
                                    break;
                                case 2:
                                    DataParserSourceDO dataParserSourceDO = dataParserSourceHandler.getById(templateItemDO.getItemId());
                                    if (null != dataParserSourceDO) {
                                        String topicTemplate = dataParserSourceDAO.findByTenantIdAndDataparserId(dataParserSourceDO.getTenantId(), dataParserSourceDO.getId());

                                        if (!StringUtils.isEmpty(topicTemplate)) {
                                            Map<String, String> topicTemplateMap = JsonUtils.toObjectByTypeRef(topicTemplate, new TypeReference<Map<String, String>>() {
                                            });
                                            topics.put(type, topicTemplateMap.get("zcp"));
                                        }
                                    }
                                    break;
                                default:
                                    break;
                            }
                        });
                    }
                });
            }
        }
        result.put("topicList", topics);
        return result;
    }

    public static String convert(String topic) {
        // "{dc}_{appName}_monitor_log" convert ~{global.asyncmq.dc}_~{app}_monitor_log
        topic = topic.replace("{appName}", "~{app}");
        topic = topic.replace("{dc}", "~{global.asyncmq.dc}");
        return topic;
    }

    public ResponseObject getDashboard(ZcpDashboardQuery zcpDashboardQuery) {
        List<DashDO> dashes = dashHandler.listByService(zcpDashboardQuery.getAppName());
        List<TemplateDataboardInfoOutput> outputs = dashes.stream().map(dash -> {
            TemplateDataboardInfoOutput output = new TemplateDataboardInfoOutput();
            output.setName(dash.getName());
            output.setId(dash.getId());
            return output;
        }).collect(Collectors.toList());
        return ResponseObject.success(outputs);
    }

    public ResponseObject syncAppOwner(ZcpSyncAppOwnerQuery zcpSyncAppOwnerQuery) {

        log.info("received syncAppOwners msg, [{}]", JsonUtils.toJsonString(zcpSyncAppOwnerQuery));

        zcpSyncAppOwnerQuery.checkWhenAdd();

        String appName = zcpSyncAppOwnerQuery.getAppName();
        String serviceKey = zcpSyncAppOwnerQuery.getServiceKey();
        TenantDO tenantDO = tenantService.getTenantByName(appName);
        if (tenantDO == null) {
            if (serviceKey == null) {
                return ResponseObject.fail("Service not exists and service key is null");
            }
            tenantDO = createNewTenant(appName, serviceKey);
            tenantHandler.addTenant(tenantDO);
            tenantService.connectToClickHouseCluster(tenantDO);
        }
        List<TenantUserRelaDO> relationDOList = new ArrayList<>();

        createRelaList(zcpSyncAppOwnerQuery.getApplicationOwnerList(), RoleTypeEnum.applicationOwners, tenantDO, relationDOList);

        createRelaList(zcpSyncAppOwnerQuery.getOperationOwnerList(), RoleTypeEnum.opOwner, tenantDO, relationDOList);

        createRelaList(zcpSyncAppOwnerQuery.getApplicationViewerList(), RoleTypeEnum.applicationGuests, tenantDO, relationDOList);

        tenantHandler.addTenantAll(relationDOList);

        return ResponseObject.success(null);
    }

    private void createRelaList(List<String> userIds, RoleTypeEnum roleTypeEnum, TenantDO tenantByName, List<TenantUserRelaDO> relationDOList) {
        Instance.ofNullable(userIds).forEach(x -> {
            if (StringUtils.isEmpty(x)) {
                return;
            }

            UserDO userFromCacheByName = userHandler.getUserByName(x);
            if (null == userFromCacheByName) {
                userHandler.addUser(x, RoleTypeEnum.normal.name());
                userFromCacheByName = userHandler.getUserByName(x);
            }
            Map<String, Set<String>> userTenantRoles = tenantUserRelaHandler.getUserTenantRoles(userFromCacheByName.getId());
            if (null != userTenantRoles) {
                Set<String> roles = userTenantRoles.get(tenantByName.getId());
                if (!CollectionUtils.isEmpty(roles) && roles.contains(roleTypeEnum.name())) {
                    return;
                }
            }

            TenantUserRelaDO tenantUserRelaDO = new TenantUserRelaDO();
            tenantUserRelaDO.setId(IdUtils.generateId());
            tenantUserRelaDO.setTenantId(tenantByName.getId());
            tenantUserRelaDO.setRole(roleTypeEnum.name());
            tenantUserRelaDO.setUserId(userFromCacheByName.getId());
            relationDOList.add(tenantUserRelaDO);

        });
    }

    public ResponseObject createService(String serviceName, String serviceKey) {
        if (StringUtils.isNotBlank(serviceKey)) {
            TenantDO existingTenant = tenantHandler.getTenantByServiceKey(serviceKey);
            if (existingTenant != null) {
                if (TenantStatusEnum.NORMAL.getStatus().equals(existingTenant.getStatus())) {
                    log.info("Service key '{}' already exists and tenant '{}' is NORMAL, skipping adding serviceKey", serviceKey, existingTenant.getName());
                    serviceKey = null;
                } else {
                    tenantHandler.clearServiceKey(existingTenant.getId());
                }
            }
        }

        TenantDO tenantDO = tenantHandler.getTenantByName(serviceName);
        if (tenantDO == null) {
            tenantDO = createNewTenant(serviceName, serviceKey);
            tenantHandler.addTenant(tenantDO);
            tenantService.connectToClickHouseCluster(tenantDO);
        } else {
            handleExistingTenant(tenantDO, serviceKey);
        }
        return ResponseObject.success(serviceName);
    }

    private TenantDO createNewTenant(String serviceName, String serviceKey) {
        TenantDO tenantDO = new TenantDO();
        tenantDO.setCreateTime(new Date());
        tenantDO.setModifyTime(new Date());
        tenantDO.setName(serviceName);
        tenantDO.setId(us.zoom.cube.site.infra.utils.IdUtils.generateId());
        tenantDO.setType(0);
        tenantDO.setDataFlowId("");
        tenantDO.setServiceKey(serviceKey);
        tenantDO.setServiceLevel(ServiceLevelEnum.APPLICATION.getLevel());
        return tenantDO;
    }

    private void handleExistingTenant(TenantDO tenantDO, String serviceKey) {
        if (TenantStatusEnum.SUSPENDED.getStatus().equals(tenantDO.getStatus())) {
            tenantHandler.batchUpdateTenantStatus(List.of(tenantDO.getId()), TenantStatusEnum.NORMAL);
            if (StringUtils.isBlank(tenantDO.getServiceKey()) && StringUtils.isNotBlank(serviceKey)) {
                tenantDAO.updateServiceKey(serviceKey, ServiceLevelEnum.APPLICATION.getLevel(), tenantDO.getId());
            }
        } else if (StringUtils.isNotBlank(serviceKey)) {
            tenantDAO.updateServiceKey(serviceKey, null, tenantDO.getId());
        }
    }

    public ResponseObject listChannelByService(ZcpRequestQuery zcpRequestQuery) {
        authService.checkAuthFromAuthInterceptorIgnoreTenant();
        TenantDO tenantDO = tenantService.getTenantByName(zcpRequestQuery.getServiceName());
        List<Map<String, Object>> result = new ArrayList<>();
        if (null == tenantDO) {
            String templateService = cubeTemplateParaService.getTemplateService();
            tenantDO = tenantService.getTenantByName(templateService);
        }

        if (tenantDO == null) {
            return ResponseObject.fail("cube system parameters configuration error");
        }

        List<Channel> channels = channelHandler.findByTenantId(tenantDO.getId());
        if (CollectionUtils.isEmpty(channels)) {
            return ResponseObject.success(new ArrayList<>());
        }

        channels.forEach(c -> {
            Map<String, Object> channel = Maps.newHashMap();
            channel.put("name", c.getName());
            channel.put("engineName", c.getEngineName());
            result.add(channel);
        });
        return ResponseObject.success(result);
    }

    public ResponseObject listAlarmByType(ZcpRequestQuery zcpRequestQuery) {
        zcpRequestQuery.checkServiceName();
        authService.checkAuthFromAuthInterceptorIgnoreTenant();
        Map<String, List<AlarmDto>> result = Maps.newHashMap();

        if (CollectionUtils.isEmpty(zcpRequestQuery.getType())) {
            return ResponseObject.success(result);
        }
        String typeTemplate = sysParaService.getCubeTemplateCache(ZCP_TYPE_NAME);
        Assert.isTrue(null != typeTemplate, "cube config has an error");

        Map<String, List<String>> allTypeTemplateMappingMaps = new HashMap<>();
        JsonUtils.toObjectByTypeRef(typeTemplate, new TypeReference<List<TemplateInnerService.TypeTemplateMapping>>() {
        }).forEach(e -> {
            allTypeTemplateMappingMaps.put(e.getType(), e.getTemplateList());
        });

        for (String type : zcpRequestQuery.getType()) {
            List<String> list = allTypeTemplateMappingMaps.get(type);
            if (CollectionUtils.isEmpty(list)) {
                continue;
            }
            List<AlarmDefinition> alarms = new ArrayList<>();
            for (String subType : list) {
                TemplateDO templateDO = templateHandlerV2.getByName(subType);
                List<TemplateItemDO> templateItemDOS = templateItemHandler.listByTemplateId(templateDO.getId());
                List<DataParserPipelineDO> pipelines = dataParserPipelineHandler.listByDataParserIds(templateItemDOS
                        .stream()
                        .filter(templateItemDO -> templateItemDO.getType() == TemplateItemTypeEnum.NEWDATAPARSER.getCode().intValue() || templateItemDO.getType() == TemplateItemTypeEnum.DATAPARSER.getCode().intValue())
                        .map(TemplateItemDO::getItemId)
                        .collect(Collectors.toList()));
                alarms.addAll(alarmDefinitionDao.getAlarmByMetricsList(collectorHandler.listMetricsCollectorByCollectorIds(pipelines
                                .stream()
                                .map(DataParserPipelineDO::getCollectorId)
                                .collect(Collectors.toList()))
                        .stream()
                        .map(CollectorMetricsDO::getMetricsId)
                        .collect(Collectors.toList())));
            }

            Map<String, List<String>> alarmsWhitelist = cubeTemplateParaService.getfilterAlarms();
            List<String> filterAlarm = alarmsWhitelist.get(type);

            List<AlarmDto> alarmDtos = new ArrayList<>();
            alarms.stream()
                    .filter(a -> CollectionUtils.isEmpty(filterAlarm) || filterAlarm.contains(a.getName()))
                    .forEach(alarm -> {
                        AlarmDto alarmDto = new AlarmDto();
                        alarmDto.setName(alarm.getName());
                        List<AlarmDto.Rules> rules = new ArrayList<>();
                        if (!CollectionUtils.isEmpty(alarm.getRules())) {
                            alarm.getRules().forEach(a -> {
                                AlarmDto.Rules rule = new AlarmDto.Rules();
                                rule.setLevel(a.getLevel());
                                rule.setConditions(a.getConditions());
                                rules.add(rule);
                            });
                        }
                        alarmDto.setRules(rules);
                        alarmDtos.add(alarmDto);
                    });

            result.put(type, alarmDtos);
        }
        return ResponseObject.success(result);
    }

    @Data
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class AlarmDto {
        String name;

        List<Rules> rules;

        @Data
        @FieldDefaults(level = AccessLevel.PRIVATE)
        public static class Rules {
            AlarmLevel level;

            List<RuleCondition> conditions;
        }
    }

    public ResponseObject createUserAndAssignmentPermission(ZcpCreateUserAndSyncPremissionQuery zcpCreateUserAndSyncPremissionQuery) {
        zcpCreateUserAndSyncPremissionQuery.check();
        authService.checkAuthFromAuthInterceptorIgnoreTenant();

        TenantDO tenantDO = tenantService.getTenantByName(zcpCreateUserAndSyncPremissionQuery.getServiceName());
        Assert.isTrue(null != tenantDO, "This service does not exist in the cube");
        if (!CollectionUtils.isEmpty(zcpCreateUserAndSyncPremissionQuery.getUsers())) {
            List<String> existUser = userDAO.listExistUser(zcpCreateUserAndSyncPremissionQuery.getUsers());
            if (!CollectionUtils.isEmpty(existUser)) {
                existUser = existUser.stream().map(String::toLowerCase)
                        .collect(Collectors.toList());
            }
            List<String> finalExistUser = existUser;
            List<String> newUser = zcpCreateUserAndSyncPremissionQuery.getUsers().stream()
                    .filter(u -> StringUtils.isNotBlank(u) && !finalExistUser.contains(u.toLowerCase()))
                    .toList();
            if (!CollectionUtils.isEmpty(newUser)) {
                newUser.forEach(nw -> {
                    userHandler.addUser(nw, RoleTypeEnum.normal.name());
                });
            }
        }

        List<TenantUserRelaDO> relationDOList = new ArrayList<>();
        createRelaList(zcpCreateUserAndSyncPremissionQuery.getApplicationOwnerList(), RoleTypeEnum.applicationOwners, tenantDO, relationDOList);
        createRelaList(zcpCreateUserAndSyncPremissionQuery.getApplicationGuestorsList(), RoleTypeEnum.applicationGuests, tenantDO, relationDOList);
        tenantHandler.addTenantAll(relationDOList);

        return ResponseObject.success(null);
    }

    public ResponseObject zcpSync2cube(ZcpSync2cubeQuery zcpSync2cubeQuery) throws Exception {
        ResponseObject result = new ResponseObject();
        try {
            cubeTemplateParaService.getEnableDistributedLock(zcpSync2cubeQuery);
            if (null != zcpSync2cubeQuery && null != zcpSync2cubeQuery.getTemplate()) {
                setUserInfo(zcpSync2cubeQuery.getTemplate().getOperator());
                result = zcpSync2cubeAlarm(zcpSync2cubeQuery);
            }
            asyncZcpInspectPlanService.syncInspectionPlan(zcpSync2cubeQuery.getChannel(), zcpSync2cubeQuery.getInspection());
        } catch (Exception e) {
            log.error("Template apply distributed lock error, request = {} ", JsonUtils.toJsonStringIgnoreExp(zcpSync2cubeQuery), e);
        }
        return result;
    }

    public TemplateAndTenantDTO basicCheckTemplate(TemplateCheckQueck templateCheckQueck) {
        authService.checkAuthFromAuthInterceptorIgnoreTenant();
        templateCheckQueck.check();
        TenantDO tenantDO = tenantHandler.getTenantByName(templateCheckQueck.getServiceName());
        Assert.isTrue(null != tenantDO, "This service does not exist in the cube");
        TemplateDO templateDO = templateHandlerV2.getByName(templateCheckQueck.getTemplateName());
        Assert.isTrue(null != templateDO, "This template does not exist in the cube");
        return new TemplateAndTenantDTO(tenantDO, templateDO);
    }

    public ResponseObject templateCheck(TemplateCheckQueck templateCheckQueck) {
        try {
            TemplateAndTenantDTO result = basicCheckTemplate(templateCheckQueck);
            return ResponseObject.success(templateServiceRelationDAO.listByServiceIdAndTemplateId(result.getTenantDO().getId(),
                    result.getTemplateDO().getId()) != null);
        } catch (Exception e) {
            log.error("check template error", e);
            return ResponseObject.fail(e.getMessage());
        }
    }

    public ResponseObject templateCheckV2(TemplateCheckQueck templateCheckQueck) {
        TemplateCheckResultOutput templateCheckResultOutput = new TemplateCheckResultOutput();
        try {
            final String containerDashId = cubeTemplateParaService.getContainerDashId();
            if (StringUtils.isBlank(containerDashId)) {
                return ResponseObject.fail("Missing configuration, please check System Parameters");
            }

            TemplateAndTenantDTO result = basicCheckTemplate(templateCheckQueck);
            TenantDO tenantDO = result.getTenantDO();
            TemplateDO templateDO = result.getTemplateDO();
            boolean existTemplate = templateDO != null && StringUtils.isNotBlank(templateDO.getId());

            List<CubeTemplateItemServiceRelaDO> cubeTemplateItemServiceRelaDOS = cubeTemplateItemServiceRelaDAO
                    .queryByServiceIdAndType(tenantDO.getId(), templateDO.getId(), TemplateItemTypeEnum.DASHBOARD.getCode());
            templateCheckResultOutput.setTemplateName(templateCheckQueck.getTemplateName());

            templateCheckResultOutput.setSuccess(existTemplate);
            if (!existTemplate) {
                log.info("template not exists in cube, templateCheckQueck = {}", JsonUtils.toJsonString(templateCheckQueck));
                return ResponseObject.success(templateCheckResultOutput);
            }
            cubeTemplateItemServiceRelaDOS.stream()
                    .filter(r -> containerDashId.equals(r.getSourceTemplateItemId()))
                    .findFirst()
                    .ifPresentOrElse(
                            rela -> {
                                String dashRelaId = rela.getDashTemplateRelaId();
                                DashTemplateRelaDO dashTemplateRelaDO = dashHandler.getTemplateDashRelaById(dashRelaId);
                                templateCheckResultOutput.setDashId(dashTemplateRelaDO.getDashId());
                            },
                            () -> templateCheckResultOutput.setDashId(null)
                    );
            return ResponseObject.success(templateCheckResultOutput);
        } catch (Exception e) {
            log.error("check template v2 error, templateCheckQueck {},templateCheckResultOutput {}  "
                    , JsonUtils.toJsonString(templateCheckQueck), JsonUtils.toJsonString(templateCheckResultOutput), e);
            return ResponseObject.fail(e.getMessage());
        }
    }

    public ResponseObject clusterBindTemplate(List<ClusterBindTemplateQuery> clusterBindTemplateQueryList) {
        authService.checkAuthFromAuthInterceptorIgnoreTenant();
        for (ClusterBindTemplateQuery clusterBindTemplateQuery : clusterBindTemplateQueryList) {
            try {
                clusterBindByTemplate(clusterBindTemplateQuery);
            } catch (Exception e) {
                log.error("bind cluster error:", e);
                return ResponseObject.fail("bind error, " + e.getMessage());
            }
        }
        return ResponseObject.success("success");

    }

    @Transactional
    public ResponseObject updateAlarmSilenceStatus(AlarmSilenceStatusByNameInput alarmSilenceStatusByNameInput) {
        return alarmSilenceService.updateAlarmSilenceStatus(alarmSilenceStatusByNameInput);
    }

    public void checkTopics(Set<String> topics) {
        Set<String> foundPrefixes = new HashSet<>();
        for (String topic : topics) {
            if (!StringUtils.isBlank(topic)) {
                String dc = topic.split("_")[0];
                foundPrefixes.add(dc);
            }
        }

        boolean hasValidPrefix = false;
        List<String> dcs = Arrays.stream(sysParaService.getCubeTemplateCache(ALL_DC_LIST).split(","))
                .collect(Collectors.toList());
        dcs.add(topic_start_with_eu02);
        for (String prefix : dcs) {
            if (foundPrefixes.contains(prefix)) {
                hasValidPrefix = true;
                break;
            }
        }
        Assert.isTrue(hasValidPrefix, "topic name does not match with the rules");
    }

    /**
     * only create topic and add topic to dataflow
     */
    public void clusterBindByTemplate(ClusterBindTemplateQuery clusterBindTemplateQuery) {
        clusterBindTemplateQuery.check();
        checkTopics(clusterBindTemplateQuery.getTopics());

        String templateName = clusterBindTemplateQuery.getTemplateName();
        TemplateDO templateDO = templateHandlerV2.getByName(templateName);
        Assert.isTrue(null != templateDO, "template not exist, template name is " + clusterBindTemplateQuery.getTemplateName());

        // create topics
        List<InnerTemplateBindInput.TopicMapping> topicMappingList = new ArrayList<>();
        InnerTemplateBindInput.TopicMapping topicMapping = new InnerTemplateBindInput.TopicMapping();
        Set<String> topicNames = clusterBindTemplateQuery.getTopics();
        topicMapping.setTopicList(topicNames);
        topicMappingList.add(topicMapping);
        templateInnerService.createTopic(new HashMap<>(), topicMappingList);

        // add topic to dataflow
        List<TemplateItemDO> templateItemDOS = templateItemHandler.listByTemplateId(templateDO.getId());
        TemplateItemDO templateItemDO = templateItemDOS.stream()
                .filter(t -> Objects.equals(TemplateItemTypeEnum.NEWDATAPARSER.getCode(), t.getType()))
                .findFirst()
                .orElse(new TemplateItemDO());
        if (StringUtils.isEmpty(templateItemDO.getItemId())) {
            log.error("skip bind, templateDTO is null, params = {}", JsonUtils.toJsonStringIgnoreExp(clusterBindTemplateQuery));
            throw new BusinessException("skip bind, templateDTO is null , template name is " + clusterBindTemplateQuery.getTemplateName());
        }

        List<DataFlowDataParserRelationDO> dataFlowDataParserRelationDOS = dataFlowDataParserRelationDAO.
                selectByServiceAndDataparserId(templateItemDO.getItemId(), templateDO.getServiceId());
        Assert.isTrue(!CollectionUtils.isEmpty(dataFlowDataParserRelationDOS), "skip bind, template dataParser not have data flow, " +
                "templateName is " + clusterBindTemplateQuery.getTemplateName());
        String dataFlowId = dataFlowDataParserRelationDOS.stream().findFirst().orElse(new DataFlowDataParserRelationDO()).getDataFlowId();
        if (StringUtils.isNotBlank(dataFlowId)) {
            List<AsyncMqQueueDO> asyncMqQueueDOS = new ArrayList<>();
            templateInnerService.buildAndAddAsyncMqQueueDO(topicNames, dataFlowId, AsyncMqQueueSourceTypeEnum.DataFlow.getType(),
                    StringUtils.EMPTY, asyncMqQueueDOS);
            asyncMqQueueHandler.batchAdd(asyncMqQueueDOS);
        }
    }

    private void setUserInfo(String operator) {
        if (StringUtils.isNotBlank(operator)) {
            UserInCache user = userHandler.getUserFromCacheByName(operator);
            if (user != null) {
                ThreadLocalStore.setUserInfoLocal(user.getId());
                ThreadLocalStore.setUserNameLocal(user.getName());
            }
        }
    }


    public void preHandle(ZcpSync2cubeQuery zcpSync2cubeQuery) {
        List<CubeTemplateConfigSysModel.DefaultTemplates> defaultTemplates = cubeTemplateParaService.getDefaultTemplates();
        try {
            if (CollectionUtils.isEmpty(defaultTemplates)) {
                return;
            }
            List<CubeTemplateConfigSysModel.DefaultTemplates> templatesToAdd = new CopyOnWriteArrayList<>();
            InnerTemplateBindInput template = zcpSync2cubeQuery.getTemplate();

            Set<String> existingTypes = new HashSet<>(template.getTypeList());
            defaultTemplates.stream()
                    .filter(t -> !existingTypes.contains(t.getType()))
                    .forEach(templatesToAdd::add);

            if (templatesToAdd.isEmpty()) {
                return;
            }
            List<String> newTypeList = new ArrayList<>(template.getTypeList());
            List<InnerTemplateBindInput.TopicMapping> newTopicList =
                    new ArrayList<>(template.getTopicMappingList());
            templatesToAdd.forEach(t -> {
                newTypeList.add(t.getType());

                InnerTemplateBindInput.TopicMapping mapping = new InnerTemplateBindInput.TopicMapping();
                mapping.setTopicList(t.getTopicList());
                mapping.setPrefixTopic(t.getPrefixTopic());
                mapping.setTemplateTopic(t.getTemplateTopic());
                newTopicList.add(mapping);
            });

            template.setTypeList(newTypeList);
            template.setTopicMappingList(newTopicList);
        } catch (Exception e) {
            log.error("preHandle error: defaultTemplates = {}", JsonUtils.toJsonString(defaultTemplates), e);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public ResponseObject zcpSync2cubeAlarm(ZcpSync2cubeQuery zcpSync2cubeQuery) throws Exception {
        List<Map<String, Object>> result = new ArrayList<>();
        authService.checkAuthFromAuthInterceptorIgnoreTenant();
        if (null != zcpSync2cubeQuery.getTemplate()) {
            preHandle(zcpSync2cubeQuery);
            result = templateInnerService.bindAndUpdateTemplate(zcpSync2cubeQuery.getTemplate(), "zcp");
        }

        Map<String, List<String>> alarmsWhitelist = cubeTemplateParaService.getfilterAlarms();
        Set<String> whiteAlarmList = alarmsWhitelist != null ? 
                alarmsWhitelist.values().stream()
                        .filter(list -> !CollectionUtils.isEmpty(list))
                        .flatMap(List::stream)
                        .collect(Collectors.toSet()) : 
                new HashSet<>();
        if (Objects.nonNull(zcpSync2cubeQuery.getALarm()) && !CollectionUtils.isEmpty(zcpSync2cubeQuery.getALarm().getAlarmName())) {


            Map<String, Object> alarmResult = new HashMap<>();
            TenantDO tenantDO = tenantService.getTenantByName(zcpSync2cubeQuery.getALarm().getServiceName());
            Assert.isTrue(null != tenantDO, "This service does not exist in the cube");
            zcpSync2cubeQuery.getALarm().getAlarmName().forEach(name -> {
                if (CollectionUtils.isEmpty(whiteAlarmList) || whiteAlarmList.contains(name)) {
                    Optional<AlarmDefinition> existedDefinition =
                            alarmDefinitionDao.findByNameAndTenantId(name, tenantDO.getId());
                    existedDefinition.ifPresent(alarmDefinition -> {
                        alarmDefinitionDao.updateAlarmStatus(alarmDefinition.getId(), true, AuthInterceptor.getUserName());
                        alarmResult.put("alarm#_" + existedDefinition.get().getName(), "enable");
                    });
                }
            });
            result.add(alarmResult);
        }

        if (Objects.nonNull(zcpSync2cubeQuery.getChannel()) && StringUtils.isNotEmpty(zcpSync2cubeQuery.getChannel().getServiceName())
                && StringUtils.isNotEmpty(zcpSync2cubeQuery.getChannel().getName())) {
            TenantDO tenantDO = tenantService.getTenantByName(zcpSync2cubeQuery.getChannel().getServiceName());
            Assert.isTrue(null != tenantDO, "This service does not exist in the cube");

            Map<String, Object> channelResult = new HashMap<>();
            Assert.isTrue(null != zcpSync2cubeQuery.getChannel().getName(), "Channel name cannot empty");
            Optional<Channel> channel = channelDao.findByNameAndTenantIdLimit1(zcpSync2cubeQuery.getChannel().getName(), tenantDO.getId());
            channel.ifPresent(c -> {
                if (channel.get().getEngineName().equals(EMAIL_ENGINAME)) {
                    Channel savedChannel = new Channel();
                    String requestEmail = zcpSync2cubeQuery.getChannel().getParameters().get(0).getValue();
                    List<ChannelParameter> sourceParameters = c.getParameters();
                    String existEmail = sourceParameters.get(0).getValue();
                    String status = "N/A";
                    if (!existEmail.equals(requestEmail)) {
                        BeanUtils.copyProperties(c, savedChannel);
                        savedChannel.setEngineName(c.getEngineName());
                        List<ChannelParameter> parameters = new ArrayList<>();
                        ChannelParameter channelParameter = new ChannelParameter();
                        channelParameter.setValue(requestEmail);
                        channelParameter.setName(zcpSync2cubeQuery.getChannel().getParameters().get(0).getName());
                        channelParameter.setIsSecret(sourceParameters.get(0).getIsSecret());
                        channelParameter.setId(sourceParameters.get(0).getId());
                        parameters.add(channelParameter);
                        savedChannel.setParameters(parameters);
                        channelService.update(savedChannel);
                        status = "update";
                    }
                    channelResult.put("channel#_" + c.getName(), status);
                } else if (channel.get().getEngineName().equals(ZOOM_CHAT)) {
                    String templateEndpoint = environment.getProperty("cube.template.endpoint.url");
                    channel.get().getParameters().forEach(p -> {
                        if (p.getName().equals(ENDPOINT) && templateEndpoint.equals(p.getValue())) {
                            Channel savedChannel = new Channel();
                            BeanUtils.copyProperties(channel.get(), savedChannel);
                            savedChannel.setEngineName(zcpSync2cubeQuery.getChannel().getEngineName());
                            List<ChannelParameter> parameters = new ArrayList<>();
                            ChannelParameter channelParameter = new ChannelParameter();
                            channelParameter.setValue(zcpSync2cubeQuery.getChannel().getParameters().get(0).getValue());
                            channelParameter.setName(zcpSync2cubeQuery.getChannel().getParameters().get(0).getName());
                            parameters.add(channelParameter);
                            savedChannel.setParameters(parameters);
                            channelService.update(savedChannel);
                        }
                    });
                    channelResult.put("channel#_" + c.getName(), "update");
                }
            });
            result.add(channelResult);
        }
        return ResponseObject.success(result);
    }

    public ResponseObject<List<String>> listServices(String userName) {
        authService.checkAuthFromAuthInterceptorIgnoreTenant();
        UserDO user = userHandler.getUserByName(userName);
        Assert.isTrue(null != user, "This user does not exist in the cube");
        List<TenantDO> tenantDOS = new ArrayList<>(authHandler.getAllTenantUserHas(user.getId()));
        List<String> result = tenantDOS.stream().map(TenantDO::getName).collect(Collectors.toList());
        return ResponseObject.success(result);
    }

    public ResponseObject listTables(ZcpBaseQuery zcpBaseQuery) {
        authService.checkAuthFromAuthInterceptorIgnoreTenant();
        ClickhouseMetaQuery query = new ClickhouseMetaQuery();
        query.setServiceName(zcpBaseQuery.getServiceName());
        UserDO userDO = userHandler.getUserByName(zcpBaseQuery.getUserName());
        if (null == userDO) {
            return ResponseObject.fail("The user does not exist in cube! ");
        }
        query.setUserId(userDO.getId());
        return clickhouseMetricsService.showClickhouseTables(query);
    }

    public ResponseObject listAlarmSilencePage(PageQuery<ZcpBaseQuery> zcpBaseQuery) {
        authService.checkAuthFromAuthInterceptorIgnoreTenant();
        UserDO user = userHandler.getUserByName(zcpBaseQuery.getUserName());
        Assert.isTrue(null != user, "This user does not exist in the cube");
        PageQuery<AlarmSilenceQuery> pageQuery = new PageQuery<>();
        if (zcpBaseQuery.getPageIndex() != 0) {
            pageQuery.setPageIndex(zcpBaseQuery.getPageIndex());
        }
        if (zcpBaseQuery.getPageSize() != 0) {
            pageQuery.setPageSize(zcpBaseQuery.getPageSize());
        }
        AlarmSilenceQuery alarmSilenceQuery = new AlarmSilenceQuery();
        alarmSilenceQuery.setServiceName(zcpBaseQuery.getQueryPara().getServiceName());
        alarmSilenceQuery.setVisibility(1);
        pageQuery.setQueryPara(alarmSilenceQuery);
        pageQuery.setUserId(user.getId());

        return alarmSilenceService.showAlarmSilenceList(pageQuery);
    }

    public ResponseObject listAlarmSilenceDetail(AlarmSilence2ZcpQuery alarmSilence2ZcpQuery) {
        authService.checkAuthFromAuthInterceptorIgnoreTenant();
        UserDO user = userHandler.getUserByName(alarmSilence2ZcpQuery.getUserName());
        Assert.isTrue(null != user, "This user does not exist in the cube");

        AlarmSilenceIdQuery alarmSilenceIdQuery = new AlarmSilenceIdQuery();
        alarmSilenceIdQuery.setId(alarmSilence2ZcpQuery.getAlarmSilenceId());
        alarmSilenceIdQuery.setUserId(user.getId());
        return alarmSilenceService.getAlarmSilenceById(alarmSilenceIdQuery);
    }

    public ResponseObject listAlarmGroupPage(PageQuery<ZcpBaseQuery> zcpBaseQuery) {
        authService.checkAuthFromAuthInterceptorIgnoreTenant();
        UserDO user = userHandler.getUserByName(zcpBaseQuery.getUserName());
        Assert.isTrue(null != user, "This user does not exist in the cube");
        PageQuery<AlarmGroupQuery> pageQuery = new PageQuery<>();
        if (zcpBaseQuery.getPageIndex() != 0) {
            pageQuery.setPageIndex(zcpBaseQuery.getPageIndex());
        }
        if (zcpBaseQuery.getPageSize() != 0) {
            pageQuery.setPageSize(zcpBaseQuery.getPageSize());
        }
        AlarmGroupQuery alarmGroupQuery = new AlarmGroupQuery();
        alarmGroupQuery.setServiceName(zcpBaseQuery.getQueryPara().getServiceName());
        pageQuery.setQueryPara(alarmGroupQuery);
        pageQuery.setUserId(user.getId());
        return alarmGroupService.showAlarmGroupList(pageQuery);
    }

    public ResponseObject createAlarmSilence(ZcpAlarmSilenceInput zcpAlarmSilenceInput) {
        authService.checkAuthFromAuthInterceptorIgnoreTenant();
        UserDO user = userHandler.getUserByName(zcpAlarmSilenceInput.getUserName());
        Assert.isTrue(null != user, "This user does not exist in the cube");
        AlarmSilenceInput alarmSilenceInput = new AlarmSilenceInput();
        BeanUtils.copyProperties(zcpAlarmSilenceInput, alarmSilenceInput);
        alarmSilenceInput.setUserId(user.getId());
        ResponseObject result = alarmSilenceService.addAlarmSilence(alarmSilenceInput);
        monitorLog.info(LogResult.getSuccessLogResult(MonitorLogTypeEnum.zcpAlarmSilenceRecord.name(), "", createMonitorLog(CREATE, zcpAlarmSilenceInput)));
        return result;
    }

    public Map<String, Object> createMonitorLog(String type, ZcpAlarmSilenceInput zcpAlarmSilenceInput) {
        Map<String, Object> success = Maps.newHashMap();
        success.put("service", zcpAlarmSilenceInput.getServiceName());
        success.put("operation", type);
        success.put("silenceName", zcpAlarmSilenceInput.getName());
        success.put("alarmGroupId", zcpAlarmSilenceInput.getAlarmGroupId());
        success.put("requestParams", JsonUtils.toJsonString(zcpAlarmSilenceInput));
        success.put("gmtCreate", new Timestamp(System.currentTimeMillis()));
        return success;
    }

    public ResponseObject editAlarmSilence(ZcpAlarmSilenceInput zcpAlarmSilenceInput) {
        authService.checkAuthFromAuthInterceptorIgnoreTenant();
        UserDO user = userHandler.getUserByName(zcpAlarmSilenceInput.getUserName());
        Assert.isTrue(null != user, "This user does not exist in the cube");

        AlarmSilenceInput alarmSilenceInput = new AlarmSilenceInput();
        BeanUtils.copyProperties(zcpAlarmSilenceInput, alarmSilenceInput);
        alarmSilenceInput.setUserId(user.getId());
        ResponseObject result = alarmSilenceService.updateAlarmSilence(alarmSilenceInput);
        monitorLog.info(LogResult.getSuccessLogResult(MonitorLogTypeEnum.zcpAlarmSilenceRecord.name(), "", createMonitorLog(UPDATE, zcpAlarmSilenceInput)));
        return result;
    }

    public ResponseObject getTimezoneList(ZcpBaseQuery zcpBaseQuery) {
        authService.checkAuthFromAuthInterceptorIgnoreTenant();
        UserDO user = userHandler.getUserByName(zcpBaseQuery.getUserName());
        Assert.isTrue(null != user, "This user does not exist in the cube");
        TimeZoneQueryInput timeZoneQueryInput = new TimeZoneQueryInput();
        timeZoneQueryInput.setUserId(user.getId());
        return alarmSilenceService.getTimezoneList(timeZoneQueryInput);
    }

    public ResponseObject listAlarmGroupDetail(AlarmGroup2ZcpQuery alarmGroup2ZcpQuery) {
        authService.checkAuthFromAuthInterceptorIgnoreTenant();
        UserDO user = checkUserByName(alarmGroup2ZcpQuery.getUserName());
        AlarmGroupIdQuery alarmGroupIdQuery = new AlarmGroupIdQuery();
        alarmGroupIdQuery.setId(alarmGroup2ZcpQuery.getAlarmGroupId());
        alarmGroupIdQuery.setUserId(user.getId());
        return alarmGroupService.getAlarmGroupById(alarmGroupIdQuery);
    }

    public UserDO checkUserByName(String userName) {
        UserDO user = userHandler.getUserByName(userName);
        Assert.notNull(user, "This user does not exist in the cube");
        return user;
    }

    public TenantDO checkTenantByName(String tenantName) {
        TenantDO tenantDO = tenantHandler.getTenantByName(tenantName);
        Assert.notNull(tenantDO, "This tenant does not exist in the cube");
        return tenantDO;
    }

    public ResponseObject listAlarmByServiceName(String userName, String serviceName) {
        authService.checkAuthFromAuthInterceptorIgnoreTenant();
        final UserDO user = checkUserByName(userName);
        final TenantDO tenantDO = tenantHandler.getTenantByName(serviceName);
        if (tenantDO == null) {
            return ResponseObject.success(Collections.emptyList());
        }
        if (!authService.canOperate(user.getId(), tenantDO.getId())) {
            return ResponseObject.success(Collections.emptyList(), WebCodeEnum.UserCannotOperateThisService.getCode(), null);
        }

        try {
            final List<ZcpIdAndNameOutput> alarms = Stream.concat(
                    Stream.of(createAllAlarmEntry()),
                    fetchAlarmDefinitions(tenantDO).stream().map(this::convertToOutput)
            ).collect(Collectors.toList());

            return ResponseObject.success(alarms);
        } catch (Exception e) {
            log.error("Query alarm list failed for tenant: {}", tenantDO.getId(), e);
            return ResponseObject.fail("Failed to retrieve alarm definitions");
        }
    }

    private ZcpIdAndNameOutput createAllAlarmEntry() {
        ZcpIdAndNameOutput entry = new ZcpIdAndNameOutput();
        entry.setId(ALL_ALARM_ID);
        entry.setName(ALL_ALARM_NAME);
        return entry;
    }

    private List<IdAndName> fetchAlarmDefinitions(TenantDO tenant) {
        return alarmDefinitionDao.findByTenantId(tenant.getId());
    }

    private ZcpIdAndNameOutput convertToOutput(IdAndName source) {
        ZcpIdAndNameOutput output = new ZcpIdAndNameOutput();
        output.setId(source.getId());
        output.setName(source.getName());
        return output;
    }

    public ResponseObject listTags(AlarmGroupTagQuery alarmGroupTagQuery) {
        checkUserByName(AuthInterceptor.getUserName());
        return alarmGroupService.listGroupTagV2(alarmGroupTagQuery);
    }

    private void retryCallback(String silenceId, String userName, String serviceName) throws Exception {
        Map<String, Object> retrySendIM = sysParaService.getSwitchFilterDataAndTimeLimitAndRetrySendIM(RETRY_REQUEST_ASYNCMQ);

        long retrySleepTime = Optional.ofNullable(retrySendIM.get(IMChannelEngine.RETRY_SLEEP_TIME))
                .map(Object::toString)
                .map(Long::parseLong)
                .orElse(2000L);

        int retryCount = Optional.ofNullable(retrySendIM.get(IMChannelEngine.RETRY_COUNT))
                .map(Object::toString)
                .map(Integer::parseInt)
                .orElse(3);

        int times = 1;
        while (times <= retryCount) {
            try {
                updateSilenceStatus(silenceId);
                deleteSilence(silenceId);
                return;
            } catch (RuntimeException e) {
                Thread.sleep(retrySleepTime);
                log.error("retryCallback error, silenceId ={}, userName = {}, serviceName = {}", silenceId, userName, serviceName, e);
                if (times >= retryCount) {
                    MonitorLogReporter.report(monitorLog, SilenceCallbackErrorMonitorLogEntity.builder()
                            .silenceId(silenceId)
                            .serviceName(serviceName)
                            .userName(userName)
                            .ip(IpUtils.getLocalIP())
                            .tid(Long.toString(Thread.currentThread().getId()))
                            .error(e.getMessage())
                            .build());
                    throw new RuntimeException(e);
                }
            } catch (Exception e) {
                log.error("retryCallback error! silenceId ={}, userName = {}, serviceName = {}", silenceId, userName, serviceName, e);
            } finally {
                times++;
            }
        }
    }


    @Transactional
    public ResponseObject callbackSilence(String userName, String serviceName, String silenceId) throws Exception {
        authService.checkAuthFromAuthInterceptorIgnoreTenant();
        UserDO user = checkUserByName(userName);
        TenantDO tenantDO = checkTenantByName(serviceName);
        Assert.isTrue(authService.canOperate(user.getId(), tenantDO.getId()), "user can not operate this service");
        retryCallback(silenceId, userName, serviceName);
        return ResponseObject.success(silenceId);
    }

    public void updateSilenceStatus(String silenceId) {
        AlarmSilenceDO alarmSilenceDO = alarmSilenceHandler.findById(silenceId);
        if (alarmSilenceDO == null) {
            return;
        }
        AlarmSilenceStatusInput statusInput = new AlarmSilenceStatusInput();
        statusInput.setId(silenceId);
        statusInput.setStatus(AlarmSilenceStatusEnum.expired.getValue());
        alarmSilenceService.updateAlarmSilenceStatus(statusInput);
    }

    public void deleteSilence(String silenceId) {
        AlarmSilenceIdQuery alarmSilenceIdQuery = new AlarmSilenceIdQuery();
        alarmSilenceIdQuery.setId(silenceId);
        AlarmSilenceDO alarmSilenceDO = alarmSilenceHandler.findById(alarmSilenceIdQuery.getId());
        if (alarmSilenceDO == null) {
            return;
        }
        alarmSilenceService.delAlarmSilenceById(alarmSilenceIdQuery);
    }

    @Transactional
    public ResponseObject addAlarmSilenceV2(AlarmSilenceInput alarmSilenceInput) {
        authService.checkAuthFromAuthInterceptorIgnoreTenant();
        try {
            alarmSilenceInput.setType(AlarmSilenceTypeEnum.ONE_TIME.getValue());
            alarmSilenceUtilService.validateAndProcessZCPParameters(alarmSilenceInput);
            return alarmSilenceService.addAlarmSilenceV2(alarmSilenceInput);
        } catch (Exception e) {
            log.error("addAlarmSilenceV2 error, alarmSilenceInput = {}", JsonUtils.toJsonString(alarmSilenceInput), e);
            return ResponseObject.fail("Create silence error: " + e.getMessage());
        }
    }

    @Transactional
    public ResponseObject updateAlarmSilenceV2(AlarmSilenceInput alarmSilenceInput) {
        authService.checkAuthFromAuthInterceptorIgnoreTenant();
        return alarmSilenceService.updateAlarmSilenceV2(alarmSilenceInput);
    }

}
