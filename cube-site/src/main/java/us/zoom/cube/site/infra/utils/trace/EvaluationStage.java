package us.zoom.cube.site.infra.utils.trace;

import lombok.Data;

/**
 * @author: eason.jia
 * @date: 2024/8/19
 */
@Data
public class EvaluationStage {
//    private ExpressionToken token;
//    private OperatorSymbol symbol;
//    private EvaluationStage leftStage;
//    private EvaluationStage rightStage;
//    private EvaluationOperator operator;
//    private StageTypeCheck leftTypeCheck;
//    private StageTypeCheck rightTypeCheck;
//    private StageCombinedTypeCheck typeCheck;
//    private String typeErrorFormat;

}
