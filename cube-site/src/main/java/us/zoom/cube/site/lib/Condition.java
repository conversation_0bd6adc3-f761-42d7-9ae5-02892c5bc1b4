package us.zoom.cube.site.lib;

public class Condition {
    private String field;
    private String operate;
    private String content;

    public Condition() {
    }

    public Condition(String field, String operate, String content) {
        this.field = field;
        this.operate = operate;
        this.content = content;
    }

    public String getField() {
        return field;
    }

    public void setField(String field) {
        this.field = field;
    }

    public String getOperate() {
        return operate;
    }

    public void setOperate(String operate) {
        this.operate = operate;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
