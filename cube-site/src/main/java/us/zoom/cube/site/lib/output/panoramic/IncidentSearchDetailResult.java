package us.zoom.cube.site.lib.output.panoramic;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

@Data
public class IncidentSearchDetailResult implements Serializable {
    @Serial
    private static final long serialVersionUID = -7013295429296719309L;
    /**
     * incident id - uuid
     */
    private String id;
    /**
     *
     */
    private String messageId;

    private String type;

    private int status;

    /**
     * start time
     */
    private String startTime;

    /**
     * resolveTime
     */
    private String resolveTime;

    private String resolveComment;

    private String subject;

    private String sub;

    private String typeAndSub;

    private String detail;

    private String source;

    private String cluster;

    private String role;

    private List zone;

    private String dc;

    private String ip;

    private String host;

    private String ipGroupNum;

    private String jira;

    private String team;

    private String tag;

    private String serviceNowId;

    private String ignoreId;

    private String noticeId;
    private String preplanMq;
    private String regradeId;
    private String subscribeId;

    private String dns;

    private String sourceType;

    private String month;

    private String action;

    private String nocAction;

    private String actionComment;

    private String actionUser;

    private String actionNocUser;

    private String actionTime;
    private String receiveTime;

    private String actionNocTime;


    /**
     * Only used for zabbix
     */
    private String itemId;

    /**
     * 1 means alarm
     * 0 means recover
     */
    private int alarm;

    private String link;

    private String groupText;

    private String groupId;

    /**
     * last update time, for the common client which has no state change
     */
    private String lupTime;


    /**
     * ttl stored in dynamodb, time + 1 year, this is only used in history table
     */
    private long ttl;

    /**
     * is maintained by zabbix window
     */
    private String maintenanceStatus;

}
