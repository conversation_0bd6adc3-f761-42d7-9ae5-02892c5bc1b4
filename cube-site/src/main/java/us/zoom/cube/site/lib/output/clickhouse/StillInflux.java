package us.zoom.cube.site.lib.output.clickhouse;


/**
 * @author: <PERSON>
 * @date: 2022/9/28 11:10
 */
public class StillInflux {
    private String name;
    private String id;
    private int cardCount;

    public StillInflux() {
    }

    public String getName() {
        return name;
    }

    public StillInflux setName(String name) {
        this.name = name;
        return this;
    }

    public String getId() {
        return id;
    }

    public StillInflux setId(String id) {
        this.id = id;
        return this;
    }

    public int getCardCount() {
        return cardCount;
    }

    public StillInflux setCardCount(int cardCount) {
        this.cardCount = cardCount;
        return this;
    }
}
