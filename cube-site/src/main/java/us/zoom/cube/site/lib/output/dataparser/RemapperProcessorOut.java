package us.zoom.cube.site.lib.output.dataparser;


/**
 * <AUTHOR> @date 2020/3/10
 */
public class RemapperProcessorOut extends  BaseProcessorOut{
    private String sourceFileds;
    private String targetFields;


    public RemapperProcessorOut(){

    }

    public RemapperProcessorOut(String id, String name, Integer order, String dataParserPipelineId, String dataParserId, String type, String sourceFileds, String targetFields) {
        super(id, name, order, dataParserPipelineId, dataParserId, type);
        this.sourceFileds = sourceFileds;
        this.targetFields = targetFields;
    }

    public String getSourceFileds() {
        return sourceFileds;
    }

    public void setSourceFileds(String sourceFileds) {
        this.sourceFileds = sourceFileds;
    }

    public String getTargetFields() {
        return targetFields;
    }

    public void setTargetFields(String targetFields) {
        this.targetFields = targetFields;
    }
}
