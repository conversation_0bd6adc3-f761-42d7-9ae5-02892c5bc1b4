package us.zoom.cube.site.lib.input.dataparser;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import us.zoom.cube.lib.common.DataParserUseStatusEnum;
import us.zoom.cube.lib.hub.DataParserRawDataTypeEnum;

/**
 * @Author: luis.zheng
 * @Date: 2021/8/20 1:40
 */
public class DataParserV2InputTest {

    @Test
    public void testCheck() {
        DataParserV2Input input = new DataParserV2Input();
        input.setName("");
        IllegalArgumentException exception = Assertions.assertThrows(IllegalArgumentException.class, () ->
                input.check());
        Assertions.assertEquals("name is blank", exception.getMessage());
        input.setName(null);
        exception = Assertions.assertThrows(IllegalArgumentException.class, () ->
                input.check());
        Assertions.assertEquals("name is blank", exception.getMessage());
        String name = "";
        for (int i = 0; i <= 100; i++) {
            name = name + "1";
        }
        input.setName(name);
        exception = Assertions.assertThrows(IllegalArgumentException.class, () ->
                input.check());
        Assertions.assertEquals("name length is more than 100", exception.getMessage());

        input.setName("name");
        input.setTenantId("");
        exception = Assertions.assertThrows(IllegalArgumentException.class, () ->
                input.check());
        Assertions.assertEquals("tenantId is blank", exception.getMessage());
        input.setTenantId(null);
        exception = Assertions.assertThrows(IllegalArgumentException.class, () ->
                input.check());
        Assertions.assertEquals("tenantId is blank", exception.getMessage());
        input.setTenantId("tenantId");
        input.check();
        Assertions.assertEquals(input.getUseStatus(), DataParserUseStatusEnum.stop.getValue());
        Assertions.assertEquals(input.getRawDataType(), DataParserRawDataTypeEnum.json.getCode());
    }
}
