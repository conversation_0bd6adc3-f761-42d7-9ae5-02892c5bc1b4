package us.zoom.cube.site.lib.output.metric;

import java.util.List;

/**
 * <AUTHOR>
 */
public class ViewContainerOut {

    private String containerId;
    private String title;
    private Integer colSize;
    private Integer componentSize=0;
    private List<ViewComponentOut> viewComponentes;
    private Integer order;

    public String getContainerId() {
        return containerId;
    }

    public void setContainerId(String containerId) {
        this.containerId = containerId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Integer getColSize() {
        return colSize;
    }

    public void setColSize(Integer colSize) {
        this.colSize = colSize;
    }

    public Integer getComponentSize() {
        return componentSize;
    }

    public void setComponentSize(Integer componentSize) {
        this.componentSize = componentSize;
    }

    public List<ViewComponentOut> getViewComponentes() {
        return viewComponentes;
    }

    public void setViewComponentes(List<ViewComponentOut> viewComponentes) {
        this.viewComponentes = viewComponentes;
    }

    public Integer getOrder() {
        return order;
    }

    public void setOrder(Integer order) {
        this.order = order;
    }
}
