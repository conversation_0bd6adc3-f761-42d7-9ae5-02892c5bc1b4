package us.zoom.cube.site.core.monitor;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.zoom.op.monitor.domain.alarm.Channel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import us.zoom.cube.lib.common.ServerTypeEnum;
import us.zoom.cube.site.biz.SysParaService;
import us.zoom.cube.site.biz.alarm.ChannelService;
import us.zoom.cube.site.core.ServerHandler;
import us.zoom.cube.site.core.TenantHandler;
import us.zoom.cube.site.core.model.common.TenantHeartCheckModel;
import us.zoom.cube.site.lib.common.MonitorTypeEnum;
import us.zoom.infra.dao.model.ServerDO;
import us.zoom.infra.dao.model.TenantDO;
import us.zoom.infra.dao.model.TenantServerCountDO;
import us.zoom.infra.model.alarm.AlarmLevel;
import us.zoom.infra.notification.NotificationHelper;
import us.zoom.infra.notification.channel.zoomchat.IMChannelEngine;
import us.zoom.infra.utils.DateUtils;
import us.zoom.cube.lib.utils.JsonUtils;
import us.zoom.infra.utils.MathUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class HeartCheckHandler {


    @Value("${offline.send.service}")
    private boolean offlineSendService;


    @Autowired
    private TenantHandler tenantHandler;

    @Autowired
    private ServerHandler serverHandler;

    @Autowired
    private ChannelService channelService;

    @Autowired
    private  SelfMonitorHandler selfMonitorHandler;

    @Autowired
    private SysParaService sysParaService;

    @Autowired
    private NotificationHelper notificationHelper;

    private Logger nonitorLog = LoggerFactory.getLogger("Monitor");



    private static Cache<String, TenantServerCountDO> tenantServerCountCache =  CacheBuilder.newBuilder().recordStats()
            .maximumSize(10000)
            //clear the alarm match data where alarm is deleted
            .expireAfterWrite(120, TimeUnit.MINUTES)
            .build();


    private static Cache<String, Channel> channelCache =  CacheBuilder.newBuilder().recordStats()
            .maximumSize(10000)
            //clear the alarm match data where alarm is deleted
            .expireAfterWrite(120, TimeUnit.MINUTES)
            .build();


    private static Cache<String, String> hasDeletedTenantCache =  CacheBuilder.newBuilder().recordStats()
            .maximumSize(10000)
            .expireAfterWrite(300, TimeUnit.MINUTES)
            .build();


    private static final  Integer maxServerCount = 10 ;

    private static final Logger monitorLog = LoggerFactory.getLogger("Monitor");





    public void checkHeart(List<TenantHeartCheckModel> toCheckTenants, ServerTypeEnum  serverTypeEnum) {

        Long begin = System.currentTimeMillis();

        if(CollectionUtils.isEmpty(toCheckTenants)){
            return;
        }

        monitor(begin, toCheckTenants, 0,MonitorTypeEnum.heartCheck,"begin");

        delOverdueServer(toCheckTenants,serverTypeEnum);

        Long stepBegin = System.currentTimeMillis();

        //remove the overdue dispatch data
        List<TenantHeartCheckModel> toNotifyTenants = filterOverdueHeartCheck(toCheckTenants);
        if(CollectionUtils.isEmpty(toNotifyTenants)){
            monitor(stepBegin, toNotifyTenants, 0,MonitorTypeEnum.heartCheck,"overdueCheckMsg");
            return;
        }

        stepBegin = System.currentTimeMillis();
        List<TenantServerCountDO> tenantServerCountDOS =  listServerCountInfo(toNotifyTenants,serverTypeEnum) ;
        if(CollectionUtils.isEmpty(tenantServerCountDOS)){
            monitor(stepBegin, toNotifyTenants, 0,MonitorTypeEnum.heartCheck,"noServer");
            return;
        }


        stepBegin = System.currentTimeMillis();
        List<TenantServerCountDO> tenantServerCountHasOfflineDOS =  tenantHandler.listTenantServerCountHasOffline(toNotifyTenants.stream().map(item->item.getId()).collect(Collectors.toList()), serverTypeEnum);
        if(CollectionUtils.isEmpty(tenantServerCountHasOfflineDOS)){
            monitor(stepBegin, toNotifyTenants, 0,MonitorTypeEnum.heartCheck,"noOfflineServer");
            return;
        }


        List<String> tenantIds = tenantServerCountHasOfflineDOS.stream().map(item ->item.getTenantId()).collect(Collectors.toList());
        stepBegin = System.currentTimeMillis();
        List<ServerDO> servers= serverHandler.listServerNoHeartLimitCount(tenantIds,serverTypeEnum,maxServerCount);
        monitor(stepBegin, toNotifyTenants, null != servers ? servers.size():0 ,MonitorTypeEnum.queryServerOffline,"normal");
        if(CollectionUtils.isNotEmpty(servers)){
            sendAlarm(toNotifyTenants,servers,serverTypeEnum,tenantServerCountDOS,tenantServerCountHasOfflineDOS);
        }

        monitor(begin, toNotifyTenants, null != servers ? servers.size():0 ,MonitorTypeEnum.heartCheck,"normal");
    }

    public List<TenantHeartCheckModel> filterOverdueHeartCheck(List<TenantHeartCheckModel> toCheckTenants) {
        return toCheckTenants.stream().filter(item ->(System.currentTimeMillis() - item.getDispatchTime())  <= 2 * sysParaService.getHeartCheckInternalInMins() * 60 *1000).collect(Collectors.toList());
    }

    public void delOverdueServer(List<TenantHeartCheckModel> toCheckTenants, ServerTypeEnum serverTypeEnum) {
        long begin = System.currentTimeMillis();
        if(ServerTypeEnum.agent != serverTypeEnum){
            return;
        }


        int deledServerSize = 0;
        if( sysParaService.isDelOverdueServerForAllService()){
            List<String> toDelTenants = toCheckTenants.stream().filter(item->hasDeletedTenantCache.getIfPresent(item.getId()) == null).map(item->item.getId()).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(toDelTenants)){
                monitor(begin,toCheckTenants,0,MonitorTypeEnum.deleteOverdueServer,"no_tenants");
                return;
            }
           deledServerSize = serverHandler.delOverdueServer(sysParaService.getServerOverdueDay(),serverTypeEnum,toDelTenants);
          monitor(begin,toCheckTenants,deledServerSize,MonitorTypeEnum.deleteOverdueServer,"normal");
          toCheckTenants.forEach(tenant->hasDeletedTenantCache.put(tenant.getId(),tenant.getName()));
          return;
       }

       //Map<tenantId,overdue day>
       Map<String,Integer> toDelServerTenants = new HashMap<>();

       for (TenantHeartCheckModel tenantHeartCheckModel : toCheckTenants){
          if( sysParaService.getDelOverdueServerForDifferentServiceMap().containsKey(StringUtils.lowerCase(tenantHeartCheckModel.getName())) && hasDeletedTenantCache.getIfPresent(tenantHeartCheckModel.getId()) == null){
              Integer overdueDay =   sysParaService.getDelOverdueServerForDifferentServiceMap().get(StringUtils.lowerCase(tenantHeartCheckModel.getName()));
              overdueDay = null == overdueDay ? sysParaService.getServerOverdueDay() : overdueDay;
              toDelServerTenants.put(tenantHeartCheckModel.getId(),overdueDay);
              hasDeletedTenantCache.put(tenantHeartCheckModel.getId(),tenantHeartCheckModel.getName());
          }
       }

       if(MapUtils.isNotEmpty(toDelServerTenants)){
           deledServerSize =   serverHandler.delOverdueServerForSomeTenant(toDelServerTenants,serverTypeEnum);
       }

        monitor(begin,toCheckTenants,deledServerSize,MonitorTypeEnum.deleteOverdueServer,"normal");



    }

    public boolean isTimeNotMatch(Calendar calendar) {
        int hour = calendar.get(Calendar.HOUR_OF_DAY);
        int mins = calendar.get(Calendar.MINUTE);
        return  ( hour<=1 && (mins <= 10));
    }

    private void monitor(Long begin, List<TenantHeartCheckModel> toNotifyTenants,int serverSize,MonitorTypeEnum monitorTypeEnum,String phase) {
        Map<String,Object> logMap = new HashMap<>(5);
        logMap.put("ts",System.currentTimeMillis());
        logMap.put("type", monitorTypeEnum.name());
        logMap.put("phase",phase);
        logMap.put("cost",System.currentTimeMillis() - begin);
        logMap.put("serviceName",toNotifyTenants.stream().map(item->item.getName()).collect(Collectors.joining(",")));
        logMap.put("serverSize",serverSize );
        logMap.put("timeShow", DateUtils.format(new java.util.Date(),DateUtils.FORMART1));
        monitorLog.info(JsonUtils.toJsonStringIgnoreExp(logMap));
    }



    private List<TenantServerCountDO> listServerCountInfo(List<TenantHeartCheckModel> toNotifyTenants,ServerTypeEnum serverTypeEnum) {

        List<TenantServerCountDO> results = new ArrayList<>();
        List<TenantHeartCheckModel> tenantsNeedQueryFromDB = new ArrayList<>();
        for(TenantHeartCheckModel tenantHeartCheckModel:toNotifyTenants){
            TenantServerCountDO tenantServerCountDO =  tenantServerCountCache.getIfPresent(tenantHeartCheckModel.getId());
            if(null == tenantServerCountDO){
                tenantsNeedQueryFromDB.add(tenantHeartCheckModel);
            }else{
                results.add(tenantServerCountDO);
            }
        }

        if(CollectionUtils.isNotEmpty(tenantsNeedQueryFromDB)){
            List<TenantServerCountDO>  serverCountFromDB=  tenantHandler.listTenantServerCount(toNotifyTenants.stream().map(item->item.getId()).collect(Collectors.toList()),serverTypeEnum);
            if(CollectionUtils.isNotEmpty(serverCountFromDB)){
                results.addAll(serverCountFromDB);
                serverCountFromDB.forEach(item-> tenantServerCountCache.put(item.getTenantId(),item));
            }
        }
        return results;
    }


    private void sendAlarm(List<TenantHeartCheckModel> toNotifyTenants, List<ServerDO> serverDOS, ServerTypeEnum serverTypeEnum, List<TenantServerCountDO> tenantServerCountDOS, List<TenantServerCountDO> tenantServerCountHasOfflineDOS) {

        Map<String,TenantDO> tenantDOMap=toNotifyTenants.stream().collect(Collectors.toMap(TenantDO::getId,f->f,(v1,v2)->v2));
        Map<String,TenantServerCountDO> tenantServerCountMap=tenantServerCountDOS.stream().collect(Collectors.toMap(TenantServerCountDO::getTenantId,tenantServerCountDO->tenantServerCountDO,(v1,v2)->v2));
        Map<String,TenantServerCountDO> tenantServerCountHasOfflineMap=tenantServerCountHasOfflineDOS.stream().collect(Collectors.toMap(TenantServerCountDO::getTenantId,f->f,(v1,v2)->v2));

        List<String> toNotifyTenantIds = tenantServerCountHasOfflineDOS.stream().map(item->item.getTenantId()).collect(Collectors.toList());
        Map<String,List<ServerDO>> tenantServerMap=new HashMap<>();
        for(ServerDO serverDO:serverDOS){
            List<ServerDO> servers=tenantServerMap.computeIfAbsent(serverDO.getTenantId(),f->new ArrayList<>());
            servers.add(serverDO);
        }

        List<TenantDO> updateNoticeTenants=new ArrayList<>(20);

        Map<String, Channel> tenantImMap = getChannelMap(serverTypeEnum, toNotifyTenantIds);

        tenantServerMap.forEach((tenantId,servers)->{
            int totalSize=servers.size();
            AlarmLevel level = getLevel(totalSize);
            List<String> offlineServerInfos=new ArrayList<>();
            TenantDO tenantDO=tenantDOMap.get(tenantId);
            offlineServerInfos.add("checkTime:"+DateUtils.format(new Date(),DateUtils.FORMART1));
            Integer totalServerCount = Integer.valueOf(tenantServerCountMap.get(tenantId).getServerCount());
            Integer offlineServerCount = Integer.valueOf(tenantServerCountHasOfflineMap.get(tenantId).getServerCount());
            offlineServerInfos.add("Service Name ( "+tenantDO.getName() +" ) ");
            offlineServerInfos.add("Total( "+ totalServerCount+" ) Offline( "+offlineServerCount+" ) Offline Ratio( "+MathUtils.div(Double.valueOf(offlineServerCount),Double.valueOf(totalServerCount))*100+"% )");
            offlineServerInfos.add("There are part of them :  ");
            if (ServerTypeEnum.agent == serverTypeEnum){
                offlineServerInfos.add("| Heartbeat       Time  |   Cluster  |  InstanceId  |     IpPublic     ");
                servers.forEach(server->{
                    offlineServerInfos.add("| "+DateUtils.format(server.getUpStatusTime(),DateUtils.FORMART1)+" |  "+server.getClusterId()+"     "+(StringUtils.isBlank(server.getInstanceId())?"NA":server.getInstanceId()) +"      "+(StringUtils.isBlank(server.getIpPublic())?"NA":server.getIpPublic()));
                });
            }else{
                offlineServerInfos.add("| Heartbeat       Time  |   Cluster  |  Region  |     Host     ");
                servers.forEach(server->{
                    offlineServerInfos.add("| "+DateUtils.format(server.getUpStatusTime(),DateUtils.FORMART1)+" |  "+server.getClusterId()+"     "+server.getRegionId() +"      "+server.getHost());
                });
            }
            String msgUrl = buildDetailUrl(serverTypeEnum,tenantDO.getId());
            String channelName = "";
            if(ServerTypeEnum.agent == serverTypeEnum  ){
                Channel channel= tenantImMap.get(tenantId);
                boolean shouldSendToSelf = isSendToSelf(channel,StringUtils.lowerCase(tenantDO.getName()));
                if(shouldSendToSelf){
                    String endpoint=channel.getParameters().stream().filter(para->para.getName().equals(IMChannelEngine.PARAM_NAME_ENDPOINT)).map(para->para.getValue()).collect(Collectors.toList()).get(0);
                    String token=channel.getParameters().stream().filter(para->para.getName().equals(IMChannelEngine.PARAM_NAME_VERIFICATION_TOKEN)).map(para->para.getValue()).collect(Collectors.toList()).get(0);
                    selfMonitorHandler.send(msgUrl,offlineServerInfos,serverTypeEnum.name().toUpperCase()+" offline over "+totalSize+" count(s)","offlineAlarm", level,endpoint,token,true,tenantId, channel.getName());
                }else {
                    selfMonitorHandler.send(msgUrl,offlineServerInfos,serverTypeEnum.name().toUpperCase()+" offline over "+totalSize+" count(s)","offlineAlarm", level, false, tenantId, Objects.nonNull(channel) ? channel.getName() : "");
                }
            }else{
                selfMonitorHandler.send(msgUrl,offlineServerInfos,serverTypeEnum.name().toUpperCase()+" offline over "+totalSize+" count(s)","offlineAlarm", level, false, tenantId, channelName);
            }
            setOfflineInfo(tenantDO,serverTypeEnum);
            updateNoticeTenants.add(tenantDO);
        });

        tenantHandler.batchUpdateNoticeInfo(updateNoticeTenants,serverTypeEnum);

    }

    private String buildDetailUrl(ServerTypeEnum serverTypeEnum, String tenantId) {
        String msgUrl;
        if(ServerTypeEnum.agent == serverTypeEnum){
            msgUrl = notificationHelper.buildAgentDetailUrl(tenantId);
        }else if(ServerTypeEnum.hub == serverTypeEnum){
            msgUrl = notificationHelper.buildHubDetailUrl();
        }else {
            return null;
        }
        return msgUrl;
    }

    private boolean isSendToSelf(Channel channel, String tenantName) {
        if(null == channel || channel.getParameters() == null || channel.getParameters().size() == 0){
            return false;
        }

        if(sysParaService.isSendToSelf(tenantName)){
               return  true;
        }

        return !sysParaService.isSendToCube();
    }

    public Map<String, Channel> getChannelMap(ServerTypeEnum serverTypeEnum, List<String> toNotifyTenantIds) {
        if(ServerTypeEnum.agent != serverTypeEnum){
            return Collections.EMPTY_MAP;
        }

        Map<String,Channel> results = new HashMap<>();
        List<String> tenantIdsChannelNeedQuery = new ArrayList<>();
        for(String tenantId:toNotifyTenantIds){
            Channel channel =   channelCache.getIfPresent(tenantId);
            if(null == channel){
                tenantIdsChannelNeedQuery.add(tenantId);
            }else{
                channelCache.put(tenantId,channel);
                results.put(tenantId,channel);
            }
        }

        if(CollectionUtils.isNotEmpty(tenantIdsChannelNeedQuery)){
          Map<String,Channel>  channelMapFromDB =  channelService.getTenantIdImChannelWithDecrypt(toNotifyTenantIds).getData();
          if(MapUtils.isNotEmpty(channelMapFromDB)){
              results.putAll(channelMapFromDB);
              channelCache.putAll(channelMapFromDB);
          }
        }
        return  results;
    }


    private AlarmLevel getLevel(Integer offlineSize) {
        return AlarmLevel.WARN;
//        AlarmLevel level = null;
//        if(offlineSize <= 2){
//            level=AlarmLevel.WARN;
//        }else if(offlineSize > 2 && offlineSize < 10){
//            level=AlarmLevel.ERROR;
//        }else{
//            level=AlarmLevel.FATAL;
//        }
//        return level;
    }



    private void setOfflineInfo(TenantDO tenantDO, ServerTypeEnum serverTypeEnum) {
        if(ServerTypeEnum.agent == serverTypeEnum){
            if(null == tenantDO.getAgentOfflineNoticeCount()){
                tenantDO.setAgentOfflineNoticeCount(1);
            }else{
                tenantDO.setAgentOfflineNoticeCount(1+tenantDO.getAgentOfflineNoticeCount());
            }
            tenantDO.setAgentOfflineNoticeTime(new Date());
        }else{
            if(null == tenantDO.getHubOfflineNoticeCount()){
                tenantDO.setHubOfflineNoticeCount(1);
            }else{
                tenantDO.setHubOfflineNoticeCount(1+tenantDO.getHubOfflineNoticeCount());
            }
            tenantDO.setHubOfflineNoticeTime(new Date());
        }
    }



}
