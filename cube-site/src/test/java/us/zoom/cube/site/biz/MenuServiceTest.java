package us.zoom.cube.site.biz;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.util.Assert;
import us.zoom.cube.site.CubeSiteApplication;
import us.zoom.cube.site.biz.menu.MenuService;
import us.zoom.cube.site.biz.role.UserRoleService;
import us.zoom.cube.site.core.auth.TenantUserRelaHandler;
import us.zoom.cube.site.infra.utils.IdUtils;
import us.zoom.cube.site.lib.ResponseObject;
import us.zoom.cube.site.lib.input.menu.MenuItemInput;
import us.zoom.infra.dao.model.MenuItemDO;
import us.zoom.infra.enums.MenuTypeEnum;
import us.zoom.infra.utils.MenuConstants;

@SpringBootTest(classes = CubeSiteApplication.class,webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)//springboot
public class MenuServiceTest extends  BaseTest {

    @Autowired
    MenuService menuService;
    @Autowired
    UserRoleService userRoleService;


    @Autowired
    private TenantUserRelaHandler tenantUserRelaHandler;


    @Test
    public void test(){
//        assighAuth();
        switchToAdmin();
        switchApiPath("/api/menu/addMenuItem");
        MenuItemInput menuItemInput = mock("test_emnu", MenuConstants.DEFAULT_MENU_ROOT,true,1,"test_res_1","/api/test", MenuTypeEnum.Menu.getCode());
        ResponseObject responseObject = menuService.addMenuItem(menuItemInput);

        switchApiPath("/api/menu/updateOrder");
        menuItemInput.setId(responseObject.getData().toString());
        menuItemInput.setMenuOrder(23);
        menuService.updateOrder(menuItemInput);

        switchApiPath("/api/menu/getMenu");
        responseObject = menuService.getMenu(menuItemInput);
        MenuItemDO menuItemDO = (MenuItemDO) responseObject.getData();
        Assert.isTrue(menuItemDO.getMenuOrder() == 23,"order fail");

        switchApiPath("/api/menu/editMenu");
        menuItemInput.setResourceUrl("/api/ttt2");
        menuItemInput.setHidden(false);
        menuService.editMenu(menuItemInput);

        switchApiPath("/api/menu/getMenu");
        responseObject = menuService.getMenu(menuItemInput);
         menuItemDO = (MenuItemDO) responseObject.getData();
        Assert.isTrue(menuItemDO.getResourceUrl().equals("/api/ttt2") && false == menuItemDO.getHidden(),"order fail");

        switchApiPath("/api/menu/getMenuTree");
        menuService.getMenuTree(menuItemInput);

        switchApiPath("/api/menu/deleteMenu");
        menuService.deleteMenu(menuItemInput);

    }


    public static void main(String []gs){
        checkPara();
    }
    public static void checkPara(){
        MenuItemInput menuItemInput  = mock("test_emnu", MenuConstants.DEFAULT_MENU_ROOT,true,1,"test_res_1","/api/test", MenuTypeEnum.Menu.getCode());
        menuItemInput.checkWhenAdd();

        menuItemInput.setHidden(null);
        menuItemInput.checkWhenAdd();
        menuItemInput.setHidden(true);
        menuItemInput.setName(null);
        menuItemInput.checkWhenAdd();
        menuItemInput.setName("sdfsdf 342 sdf");
        menuItemInput.checkWhenAdd();
        menuItemInput.setType(null);
        menuItemInput.checkWhenAdd();
        menuItemInput.setType(90);
        menuItemInput.checkWhenAdd();
        menuItemInput.setResourceId(null);
        menuItemInput.checkWhenAdd();
        menuItemInput.setResourceId("sdf sdf");
        menuItemInput.checkWhenAdd();
        menuItemInput.setResourceId("sdfwerw23482934802394888888888888888888888888888888888888888888888888888888888888888888888888888888888888888888888888888888888888888888888888888888888888888888888888sdfwerowerowieuroiweurioweurioweuroiwueroiweuroiwueriowueroipweriowueroiwueroiwueroiwueroiwueroiuwepoiruwoieruoiwer");
        menuItemInput.checkWhenAdd();
        menuItemInput.setResourceUrl(mockStr(3000));
        menuItemInput.checkWhenAdd();

    }

    private static String mockStr(int length) {
        StringBuilder builder = new StringBuilder();
        for(int i=0;i<length;i++){
            builder.append("a");
        }
        return  builder.toString();
    }

    private static  MenuItemInput mock(String name, String parentResId, Boolean hidden, Integer order, String resId, String resUrl, Integer type) {
        MenuItemInput menuItemDO = new MenuItemInput();
        menuItemDO.setId(IdUtils.generateId());
        menuItemDO.setType(type);
        menuItemDO.setName(name);
        menuItemDO.setParentResId(parentResId);
        menuItemDO.setHidden(hidden);
        menuItemDO.setMenuOrder(order);
        menuItemDO.setResourceId(resId);
        menuItemDO.setResourceUrl(resUrl);
        return menuItemDO;
    }


}
