package us.zoom.cube.site.core;

import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.lib.common.ServerTypeEnum;
import us.zoom.cube.site.core.model.common.TenantHeartCheckModel;
import us.zoom.cube.site.core.monitor.HeartCheckHandler;
import us.zoom.cube.lib.utils.JsonUtils;
import us.zoom.mq.client.clients.consumer.RetryableStraw;
import us.zoom.mq.common.entity.TaskEntity;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Component
@Slf4j
public class HubHeartCheckConsumerHandler implements RetryableStraw<String> {
    private final static TypeReference typeReference = new TypeReference<String>() {
    };

    private static final TypeReference<List<TenantHeartCheckModel>> heartCheckModelListType = new TypeReference<List<TenantHeartCheckModel>>() {
    };

    @Autowired
    private HeartCheckHandler heartCheckHandler;

    @Override
    public boolean onMessage(List<TaskEntity<String>> taskEntities) {
        if (CollectionUtils.isEmpty(taskEntities)) {
            return false;
        }
        try {
            List<TenantHeartCheckModel> toCheckTenants = toCheckTenants(taskEntities);
            heartCheckHandler.checkHeart(toCheckTenants, ServerTypeEnum.hub);
        } catch (Exception e) {
            log.info("consumer agent heart beat message error");
        }
        return false;
    }

    private List<TenantHeartCheckModel> toCheckTenants(List<TaskEntity<String>> records) {
        List<TenantHeartCheckModel> results = new ArrayList<>();
        Set<String> tenantNameSet = new HashSet<>();
        for (TaskEntity<String> record : records) {
            List<TenantHeartCheckModel> oneBatch = JsonUtils.toObjectByTypeRef(record.getPayload(), heartCheckModelListType);
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(oneBatch)) {
                oneBatch.forEach(item -> {
                    if (!tenantNameSet.contains(item.getName())) {
                        results.add(item);
                        tenantNameSet.add(item.getName());
                    }
                });
            }
        }
        return results;
    }

    @Override
    public TypeReference<String> type() {
        return typeReference;
    }
}
