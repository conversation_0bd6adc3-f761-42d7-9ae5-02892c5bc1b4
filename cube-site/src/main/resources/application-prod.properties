#log Config
log.sampling.count.info=10000
log.sampling.count.warn=1000
log.sampling.count.error=1000

#lock config
resource.lock.inteval=20

#mysql
dataSource.ssl.enable=true
dataSource.ssl.trustStore={{dataSource_ssl_trustStore}}
# To set in CSMS
#dataSource.ssl.trustStorePassword=

dataSource.url=jdbc:mysql://{{mysql_server}}:3306/zoom_cube?characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&serverTimezone=UTC&allowMultiQueries=true&sslMode=VERIFY_IDENTITY
# To set in CSMS
#cube.dataSource.username=
# To set in CSMS
#cube.dataSource.password=
dataSourceStandby.url=jdbc:mysql://{{mysql_server}}:3306/zoom_cube?characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&serverTimezone=UTC&allowMultiQueries=true&sslMode=VERIFY_IDENTITY
# To set in CSMS
#cube.dataSourceStandby.username=
# To set in CSMS
#cube.dataSourceStandby.password=
dataSource.csms_rotate_username_key= cube.dataSource.username
dataSource.csms_rotate_password_key= cube.dataSource.password
dataSourceStandby.csms_rotate_username_key= cube.dataSourceStandby.username
dataSourceStandby.csms_rotate_password_key= cube.dataSourceStandby.password

#front-source path
spring.web.resources.static-locations=file:/opt/cube-front/static

#HttpClient
httpClient.connection.pool.maxTotal=200
httpClient.connection.pool.maxPerRoute=20
httpClient.connection.request.timeout=2000
httpClient.connect.timeout=2000
httpClient.socket.timeout=10000

#IM alarm
self.monitor.im.url=https://inbots.zoom.us/incoming/hook/Mi9CyGi1OjMODrJZ2bdYpXlD?format=fields
# To set in CSMS
# y5KEKWiHapDxfvIDrcCeHo8U
#self.monitor.im.authorization=

okta.oauth2.issuer={{okta_oauth2_issuer}}
# To set in CSMS
#okta.oauth2.client-id=
# To set in CSMS
#okta.oauth2.client-secret=
okta.oauth2.redirect-uri={{okta_oauth2_redirect-uri}}

logout.url=https://zoom.okta.com/app/UserHome


site.oauth2.filter-url=/cfg/**
site.oauth2.filter-url2=/selfmonitor/**

log.filePath=./logs
apm.log.home=/data/logs/zoom_middleware/apm/Infra_Monitor_Cube-Site
monitor.executorsReplace.enabled=false

server.port=443
server.ssl.protocol=TLS
server.ssl.enabled=true
server.ssl.key-store=file:{{server_ssl_key_store}}
# To set in CSMS
#server.ssl.key-store-password=
server.ssl.keyStoreType={{server_ssl_key_store_type}}
server.ssl.keyAlias={{server_ssl_key_alias}}
server.tomcat.min-spare-threads=600
server.tomcat.max-threads=1800
server.tomcat.accept-count=1000

spring.jpa.show-sql= true
spring.jpa.hibernate.ddl-auto= none
spring.jpa.hibernate.naming.implicit-strategy=org.springframework.boot.orm.jpa.hibernate.SpringImplicitNamingStrategy
spring.jpa.hibernate.naming.physical-strategy=org.hibernate.boot.model.naming.CamelCaseToUnderscoresNamingStrategy
spring.jpa.database= MYSQL
spring.jpa.properties.hibernate.enable_lazy_load_no_trans= true

#spring.session
#spring.session REDIS or JDBC
spring.session.store-type=JDBC

# auth config
auth.issuer.name=cube
auth.audience.name=hub
# To set in CSMS
#auth.secret.key=

auth.common.audience.name=cube

#CORS configs
cors.allowed.url.okta=

dataparser.must.admin=yes

offline.send.service=false

test.kafka.connectivity.timeout={{test.kafka.connectivity.timeout}}

site.okta.redirect-uri=https://cube.zoom.us/oauth2/authorization/okta

cube.incident.url.alert=https://cube.zoom.us/

#config in csms
#aes.key=


#only used in ZCP_CUBE
#cube.authorization.enableJwt=true

async.mq.admin.endpoint=https://asyncmq.zoom.us
async.mq.admin.username=app_cube_admin

server.servlet.session.timeout=24h



#stand
#capacity.distributedLock.type=capacity_assigner_stand
#main
capacity.distributedLock.type=capacity_assigner

async.mq.endpoint=https://asyncmq.zoom.us
alarm.async.mq.username=app_cube

# probe(E2E)
cube.scheduler.url={{cube_scheduler_url}}
scheduler.job.group.name={{scheduler_job_group_name}}
scheduler.job.topic.name={{scheduler_job_topic_name}}

probe.metrics.service.name={{probe_metrics_service_name}}
probe.metrics.table.name={{probe_metrics_table_name}}

probe.task.file.path={{probe_task_file_path}}

cube.template.endpoint.url=https://inbots.zoom.us/incoming/hook/l3mLXxaohl6DMdoq0dvOm_Ca?format=fields

alarm.pii-redirect-url.prefix=/alarm-redirect/dashboard

cube.dataflow.alert.endpoint.url=https://inbots.zoom.us/incoming/hook/-lSSSXrZjGnaHo1Lab6_OgrU?format=fields

webop.api.url={"aw1":"https://op.zoom.us:8443","go":"https://goop.zoom.us:8443","us02":"https://us02op.zoom.us:8443","us04":"https://us04op.zoom.us:8443","us05":"https://us05op.zoom.us:8443","ca01":"https://ca01op.zoom.us:8443","au01":"https://au01op.zoom.us:8443","eu01":"https://eu01op.zoom.us:8443","eu02":"https://eu02op.zoom.us:8443","in01":"https://in01op.zoom.us:8443","us06":"https://us06op.zoom.us:8443","us07":"https://us07op.zoom.us:8443","ch01":"https://ch01op.zoom.us:8443","dg01":"https://dg01op.zoom.us:8443","sa01":"https://sa01op.zoom.us:8443"}

s3.probe.output.region={{s3.probe.output.region}}
s3.probe.output.dir={{s3.probe.output.dir}}
cube.clickhouse.manager.backend.url=http://44.216.144.188:8809

cube.env={{cube.env}}
s3.open.env={{s3.open.env}}
agent.consumer.env={{agent.consumer.env}}
cube.site.e2e.aggregation.consumer.groupId=e2e-aggregation-groupId-prod-001

webhooks.api.endpoint=https://integrations.zoom.us
xms.api.endpoint=https://dg01xmppapi.zoom.us
webop.api.endpoint=https://op.zoom.us:8443