package us.zoom.cube.site.biz;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import us.zoom.cube.site.core.config.AsyncMQLoader;

/**
 * @author: canyon.li
 * @date: 2024/07/20
 **/
public class AgentAsyncConsumerTest extends BaseTest {

    @Autowired
    AsyncMQLoader asyncMQLoader;

    @Test
    public void load() {
        try {
            for (int i = 0; i < 5; i++) {
                asyncMQLoader.load();
            }
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }

    }
}
