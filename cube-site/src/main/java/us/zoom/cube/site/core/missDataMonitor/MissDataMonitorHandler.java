package us.zoom.cube.site.core.missDataMonitor;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import us.zoom.cube.lib.monitor.MonitorWrapper;
import us.zoom.cube.site.core.MetricsHandler;
import us.zoom.cube.site.core.TenantHandler;
import us.zoom.cube.site.core.config.ClickhouseHandlerFactory;
import us.zoom.infra.clickhouse.ClickhouseMultiHandler;
import us.zoom.infra.dao.model.TenantDO;
import us.zoom.infra.utils.DateUtils;
import us.zoom.cube.lib.utils.JsonUtils;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class MissDataMonitorHandler {

    private Logger monitorLog = LoggerFactory.getLogger("Monitor");

    @Autowired
    private ClickhouseHandlerFactory clickhouseHandlerFactory;

    @Autowired
    private TenantHandler tenantHandler;

    @Autowired
    private MetricsHandler metricsHandler;

    public void compareData(Integer oldDataQueryTime,String env){
        ClickhouseMultiHandler clickhouseMultiHandler = clickhouseHandlerFactory.get().getHandlerByEnv(env);
        if(null == clickhouseMultiHandler){
            throw new RuntimeException("There is no ch handler");
        }
        List<TenantDO> tenantDOS = tenantHandler.listAll();
        if(CollectionUtils.isEmpty(tenantDOS)){
            return;
        }
        Integer timePeriod = 10 ;
        long begin = System.currentTimeMillis();
        for(TenantDO tenantDO :tenantDOS){
            try{
                String serviceName = tenantHandler.matchInfluxDbNameToTenantName(tenantDO.getName());
                List<String> metricsList= metricsHandler.listAllMetricsName(tenantDO.getId());
                for(String metrics:metricsList){

                    long beginQuery  = System.currentTimeMillis();
                    java.util.Date oldDateEnd = new Date(DateUtils.addMinute(new Date(),-oldDataQueryTime));
                    java.util.Date oldDateStart = new Date(DateUtils.addMinute(new Date(),- oldDataQueryTime - timePeriod));
                    String queryOldDataSql = "select time from "+serviceName +"."+metrics+" where time >= '"+ DateUtils.format2UTC(oldDateStart,DateUtils.FORMART1)+"' AND time <= '"+DateUtils.format2UTC(oldDateEnd,DateUtils.FORMART1)+"' limit 1";

                    List<Map<String,Object>> oldData = clickhouseMultiHandler.query(serviceName, queryOldDataSql);

                    java.util.Date newDateStart = new Date(DateUtils.addMinute(new Date(), - timePeriod));
                    String queryCurrentDataSql = "select time from "+serviceName+"."+metrics+" where time >= '"+ DateUtils.format2UTC(newDateStart,DateUtils.FORMART1)+"' AND time <= '"+DateUtils.format2UTC(new Date(),DateUtils.FORMART1)+"' limit 1";
                    List<Map<String,Object>> newData = clickhouseMultiHandler.query(serviceName, queryCurrentDataSql);

                    MonitorWrapper monitorWrapper = new MonitorWrapper("metrics_miss_data");
                    monitorWrapper.addTag("service",tenantDO.getName());
                    monitorWrapper.addTag("metrics",metrics);
                    monitorWrapper.addTag("env",env);
                    monitorWrapper.addField("data_loss",CollectionUtils.isNotEmpty(oldData) && CollectionUtils.isEmpty(newData));
                    monitorWrapper.addField("cost",System.currentTimeMillis() - beginQuery);
                    monitorWrapper.setTs(System.currentTimeMillis());
                    monitorLog.info(JsonUtils.toJsonStringIgnoreExp(monitorWrapper));
                }


            }catch (Exception e){
                log.error("query error",e);
            }
        }

        MonitorWrapper monitorWrapper = new MonitorWrapper("metrics_miss_data_check");
        monitorWrapper.addTag("success",true);
        monitorWrapper.addField("cost",System.currentTimeMillis() - begin);
        monitorWrapper.setTs(System.currentTimeMillis());
        monitorLog.info(JsonUtils.toJsonStringIgnoreExp(monitorWrapper));

    }

}
