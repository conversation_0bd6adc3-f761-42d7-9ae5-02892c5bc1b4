package us.zoom.cube.site.lib.output.dataflow;

import com.google.common.collect.Sets;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
public class MigratedDataFlowOut {

    private String dataFlowId;

    private String dataFlowName;

    private Set<String> notExistTopicNames = Sets.newConcurrentHashSet();

    private Set<String> failedTopicNames = Sets.newConcurrentHashSet();

    private int notExistTopicCount;

    private int failedCount;

    private int totalCount;

    private Set<String> successResults = Sets.newConcurrentHashSet();

    private String errorMessage;


    public MigratedDataFlowOut() {
    }

    public String getDataFlowId() {
        return dataFlowId;
    }

    public void setDataFlowId(String dataFlowId) {
        this.dataFlowId = dataFlowId;
    }

    public String getDataFlowName() {
        return dataFlowName;
    }

    public void setDataFlowName(String dataFlowName) {
        this.dataFlowName = dataFlowName;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }


    public Set<String> getNotExistTopicNames() {
        return notExistTopicNames;
    }

    public void setNotExistTopicNames(Set<String> notExistTopicNames) {
        this.notExistTopicNames = notExistTopicNames;
    }

    public int getNotExistTopicCount() {
        return notExistTopicCount;
    }

    public void setNotExistTopicCount(int notExistTopicCount) {
        this.notExistTopicCount = notExistTopicCount;
    }


    public int getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(int totalCount) {
        this.totalCount = totalCount;
    }

    public int getFailedCount() {
        return failedCount;
    }

    public void setFailedCount(int failedCount) {
        this.failedCount = failedCount;
    }

    public Set<String> getFailedTopicNames() {
        return failedTopicNames;
    }

    public void setFailedTopicNames(Set<String> failedTopicNames) {
        this.failedTopicNames = failedTopicNames;
    }

    public Set<String> getSuccessResults() {
        return successResults;
    }

    public void setSuccessResults(Set<String> successResults) {
        this.successResults = successResults;
    }

    public void addSuccessResult(String topicName) {
        successResults.add(topicName);
    }

    public void addSuccessResult(List<String> topicNames) {
        if (topicNames == null) {
            return;
        }
        successResults.addAll(topicNames);
    }

    public void addNotExistTopic(String topicName) {
        notExistTopicNames.add(topicName);
    }

    public void addFailedTopicNames(List<String> topicNames) {
        failedTopicNames.addAll(topicNames);
    }
}
