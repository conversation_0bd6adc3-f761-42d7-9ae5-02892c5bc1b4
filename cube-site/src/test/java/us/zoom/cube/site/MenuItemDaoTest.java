package us.zoom.cube.site;


import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import us.zoom.cube.site.infra.utils.IdUtils;
import us.zoom.infra.dao.model.MenuItemDO;
import us.zoom.infra.dao.service.MenuItemDAO;
import us.zoom.infra.enums.MenuTypeEnum;
import us.zoom.infra.utils.MenuConstants;

@SpringBootTest(classes = CubeSiteApplication.class,webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)//springboot
public class MenuItemDaoTest {

    @Autowired
    private MenuItemDAO menuItemDAO;


    @Test
    public void testWhole(){

        //test add
        MenuItemDO menuItemDO = mock("test_emnu", MenuConstants.DEFAULT_MENU_ROOT,true,1,"test_res_1","/api/test", MenuTypeEnum.Menu.getCode());
        menuItemDAO.deleteMenuBySourceId(menuItemDO.getResourceId());
        menuItemDAO.addMenuItem(menuItemDO);
        MenuItemDO menuFromDB = menuItemDAO.getByResId(menuItemDO.getResourceId());
        Assertions.assertTrue(null != menuFromDB, "add or query fail ");
        Assertions.assertTrue(menuFromDB.equals(menuItemDO),"field not same ");

        //update order
        menuItemDAO.updateOrder(menuFromDB.getResourceId(),2);
        menuFromDB = menuItemDAO.getByResId(menuItemDO.getResourceId());
        Assertions.assertTrue(2 == menuFromDB.getMenuOrder(), "update order fail ");

        // get one son
        MenuItemDO son  =   menuItemDAO.getOneSon(MenuConstants.DEFAULT_MENU_ROOT);
        Assertions.assertTrue(null  != son, "get son fail ");

        //edit
        menuItemDO.setResourceUrl("test_ur11");
        menuItemDO.setHidden(false);
        menuItemDAO.editMenu(menuItemDO);
        MenuItemDO editedMenu = menuItemDAO.getById(menuItemDO.getId());
        Assertions.assertTrue(StringUtils.equals("test_ur11",editedMenu.getResourceUrl()) && false == editedMenu.getHidden(), "editedMenu fail ");


        //deleteMenuBySourceId
        menuItemDAO.deleteMenuBySourceId(menuFromDB.getResourceId());
        menuFromDB = menuItemDAO.getByResId(menuItemDO.getResourceId());
        Assertions.assertTrue(null  == menuFromDB, "getByResId fail ");

    }

    private MenuItemDO mock(String name,String parentResId,Boolean hidden,Integer order,String resId,String resUrl,Integer type) {
        MenuItemDO menuItemDO = new MenuItemDO();
        menuItemDO.setId(IdUtils.generateId());
        menuItemDO.setType(type);
        menuItemDO.setName(name);
        menuItemDO.setParentResId(parentResId);
        menuItemDO.setHidden(hidden);
        menuItemDO.setMenuOrder(order);
        menuItemDO.setResourceId(resId);
        menuItemDO.setResourceUrl(resUrl);
        return menuItemDO;
    }
}
