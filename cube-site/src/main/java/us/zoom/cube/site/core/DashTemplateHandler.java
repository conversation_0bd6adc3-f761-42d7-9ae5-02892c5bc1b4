package us.zoom.cube.site.core;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.site.infra.utils.IdUtils;
import us.zoom.cube.site.lib.IdPara;
import us.zoom.cube.site.lib.input.*;
import us.zoom.cube.site.lib.query.NameQuery;
import us.zoom.cube.site.lib.query.PageQuery;
import us.zoom.infra.dao.model.*;
import us.zoom.infra.dao.service.*;
import us.zoom.infra.enums.DashUserRelaTypeEnum;
import us.zoom.infra.enums.DashtemplateTypeEnum;
import us.zoom.infra.utils.CommonSplitConstants;
import us.zoom.infra.utils.Instance;
import us.zoom.cube.lib.utils.JsonUtils;

import jakarta.validation.Valid;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2022-06-29 4:14 
 */
@Component
public class DashTemplateHandler {
    @Autowired
    DashTemplateDAO dashTemplateDAO;

    @Autowired
    DashTemplateGroupHandler dashTemplateGroupHandler;

    @Autowired
    DashTemplateUserRelaDAO dashTemplateUserRelaDAO;

    @Autowired
    DashTemplateRelaDAO dashTemplateRelaDAO;

    @Autowired
    DashTemplateGroupUserRelaDAO dashTemplateGroupUserRelaDAO;

    @Autowired
    private DashDAO dashDAO;

    @Autowired
    private DashTemplatePonitUserRelaDAO dashTemplatePonitUserRelaDAO;

    @Autowired
    DashTemplateGroupRelaDAO dashTemplateGroupRelaDAO;

    @Autowired
    private DashHandler dashHandler;

    @Autowired
    private CardHandler cardHandler;




    @Transactional(rollbackFor = Exception.class)
    public String addTemplate(@Valid DashTemplateInput dashTemplateInput){
        DashTemplateDO dashTemplateDO = new DashTemplateDO();
        dashTemplateDO.setTemplateName(dashTemplateInput.getTemplateName());
        dashTemplateDO.setId(IdUtils.generateId());
        dashTemplateDO.setDashId(dashTemplateInput.getDashId());
        dashTemplateDO.setTemplateSharedOwners(dashTemplateInput.getTemplateSharedOwners());
        dashTemplateDO.setDescription(dashTemplateInput.getDescription());
        dashTemplateDAO.add(dashTemplateDO);
        if(!StringUtils.isEmpty(dashTemplateInput.getTemplateGroupId())){
            DashTemplateGroupRelaDO dashTemplateGroupRelaDO = new DashTemplateGroupRelaDO();
            dashTemplateGroupRelaDO.setId(IdUtils.generateId());
            dashTemplateGroupRelaDO.setTemplateGroupId(dashTemplateInput.getTemplateGroupId());
            dashTemplateGroupRelaDO.setTemplateId(dashTemplateDO.getId());
            List<DashTemplateGroupRelaDO> list = dashTemplateGroupRelaDAO.getRelaByTemplateIdAndGroupId(dashTemplateGroupRelaDO.getTemplateId(),dashTemplateGroupRelaDO.getTemplateGroupId());
            if(CollectionUtils.isEmpty(list)){
                dashTemplateGroupRelaDAO.add(dashTemplateGroupRelaDO);
            }
            dashTemplateGroupRelaDAO.add(dashTemplateGroupRelaDO);
        }
        setOwner(dashTemplateInput,dashTemplateDO.getId());
        return dashTemplateDO.getId();

    }

    @Transactional(rollbackFor = Exception.class)
    public String addDashBytemplate(@Valid DashTemplateInput dashTemplateInput){
        DashTemplateDO dashTemplateDO = dashTemplateDAO.getDashTemplateById(dashTemplateInput.getTemplateId());
        DashDO dashDORoot = dashDAO.getDashById(dashTemplateDO.getDashId());
        String serviceNameOld = dashDORoot.getService();
        String serviceNameNew = dashTemplateInput.getService();
        DashTemplateRelaDO dashTemplateRelaDO = new DashTemplateRelaDO();
        dashTemplateRelaDO.setId(IdUtils.generateId());
        String resId = null;
        if(null!=dashTemplateDO){
            if(null!=dashDORoot){
                DashInput dashInput = new DashInput();
                BeanUtils.copyProperties(dashDORoot,dashInput);
                Map mapCfg = JsonUtils.toObject(dashDORoot.getConfigs(), Map.class);
                dashInput.setConfigs(mapCfg);
                dashInput.setDashTemplateType(DashtemplateTypeEnum.COPY.getCode());
                dashInput.setName(dashTemplateInput.getDashName());
                dashInput.setDescription(dashTemplateInput.getDescription());
                dashInput.setSharedOwners(dashTemplateInput.getDashSharedOwners());
                dashInput.setService(dashTemplateInput.getService());
                dashInput.setDashTemplateRelaId(dashTemplateRelaDO.getId());
                dashInput.setModule(dashTemplateInput.getModule());
                dashInput.setParentDashTreeItemId(dashTemplateInput.getParentDashTreeItemId());
                 if(dashTemplateInput.getTemplateType().equals(DashtemplateTypeEnum.COPY.getCode())){
                     DashDO dashDO = dashHandler.copyDash(dashInput);
                     HashMap<String,String> idMap = cardHandler.copyCard(dashInput.getId(),dashDO.getId(),true,serviceNameNew,serviceNameOld);
                     List list = new ArrayList();
                     if(!org.springframework.util.StringUtils.isEmpty(dashDO.getConfigs())){
                         if (!org.springframework.util.StringUtils.isEmpty(JsonUtils.toObject(dashDO.getConfigs(), Map.class).get("layout"))){
                             List layoutList =JsonUtils.toObject(JsonUtils.toJsonString(JsonUtils.toObject(dashDO.getConfigs(), Map.class).get("layout")),List.class);
                             for (Object layout : layoutList ){
                                 Map layoutMap = JsonUtils.toObject(JsonUtils.toJsonString(layout),Map.class);
                                 String id = idMap.get(layoutMap.get("i"));
                                 layoutMap.put("i",id);
                                 list.add(layoutMap);
                             }
                             Map map = JsonUtils.toObject(dashDO.getConfigs(), Map.class);
                             DashInput dashInputCfg = new DashInput();
                             dashInputCfg.setConfigs(map);
                             dashInputCfg.getConfigs().put("layout",list);
                             dashInputCfg.setId(dashDO.getId());
                             dashHandler.changeConfigs(dashInputCfg);
                         }
                     }

                     dashTemplateRelaDO.setPointDashId(dashTemplateInput.getDashId());
                     dashTemplateRelaDO.setDashId(dashDO.getId());
                     dashTemplateRelaDO.setDashType(DashtemplateTypeEnum.COPY.getCode());
                     dashTemplateRelaDO.setTemplateId(dashTemplateInput.getTemplateId());
                     dashTemplateRelaDAO.add(dashTemplateRelaDO);
                     resId = dashDO.getId();
                }
            }
        }

        return resId;
    }

    public void addCard(List<CardDO> cardDOList,String dashId){
        List<CardInput> cardInputList=new ArrayList<>();
        if(!CollectionUtils.isEmpty(cardDOList)){
            for (CardDO cardDO : cardDOList){
                CardInput cardInput = new CardInput();
                BeanUtils.copyProperties(cardDO,cardInput);
                cardInput.setConfigs(JsonUtils.toObject(cardDO.getConfigs(), Map.class));
                cardInputList.add(cardInput);
            }
        }
        BatchCardInput batchCardInput = new BatchCardInput();
        batchCardInput.setDashId(dashId);
        batchCardInput.setCardInputs(cardInputList);
        cardHandler.batchAdd(batchCardInput);
    }

    @Transactional(rollbackFor = Exception.class)
    public String editPointDash(@Valid DashInput dashInput) {
        DashTemplateRelaDO dashTemplateRelaDO = new DashTemplateRelaDO();
        dashTemplateRelaDO.setDescription(dashInput.getDescription());
        dashTemplateRelaDO.setDashName(dashInput.getName());
        dashTemplateRelaDO.setService(dashInput.getService());
        dashTemplateRelaDAO.getTemplateByDashId(dashInput.getId());
        return dashInput.getId();
    }

    public List<DashDO> searchByPointDash(@Valid DashInput dashInput) {
        List<DashTemplateRelaDO> dashTemplateRelaDOList =dashTemplateRelaDAO.getTemplateByPointDashId(dashInput.getId());
        List<DashDO> dashDOS = new ArrayList<>();
        for (DashTemplateRelaDO dashTemplateRelaDO:dashTemplateRelaDOList){
            if(dashTemplateRelaDO.getTemplateType().equals(DashtemplateTypeEnum.POINT.getCode())){
                DashDO dashDO = dashDAO.getDashById(dashTemplateRelaDO.getPointDashId());
                if(null!=dashDO){
                    dashDOS.add(dashDO);
                }
            }

        }
        return dashDOS;
    }

    public List<DashTemplateDO> searchByGroupId(IdPara idPara ){
        List<DashTemplateGroupRelaDO> dashTemplateGroupRelaDOS = dashTemplateGroupRelaDAO.getDashTemplateByGroupId(idPara.getId());
        List<DashTemplateDO> dashTemplateDOS = new ArrayList<>();
        for (DashTemplateGroupRelaDO dashTemplateGroupRelaDO : dashTemplateGroupRelaDOS){
            DashTemplateDO dashTemplateDO = dashTemplateDAO.getDashTemplateById(dashTemplateGroupRelaDO.getTemplateId());
            dashTemplateDOS.add(dashTemplateDO);
        }
        return dashTemplateDOS;
    }

    @Transactional(rollbackFor = Exception.class)
    public String edit(@Valid DashTemplateInput dashTemplateInput) {
        DashTemplateDO dashTemplateDO = new DashTemplateDO();
        dashTemplateDO.setId(dashTemplateInput.getId());
        dashTemplateDO.setTemplateName(dashTemplateInput.getTemplateName());
        dashTemplateDO.setTemplateSharedOwners(dashTemplateInput.getTemplateSharedOwners());
        dashTemplateDO.setDescription(dashTemplateInput.getDescription());
        dashTemplateDAO.edit(dashTemplateDO);
        dashTemplateUserRelaDAO.delByTemplateId(dashTemplateInput.getId());
        setOwner(dashTemplateInput,dashTemplateDO.getId());
        return dashTemplateInput.getId();
    }

    @Transactional(rollbackFor = Exception.class)
    public void delete(IdPara idPara) {
        // point creat dash ,template can not delete
        DashTemplateDO dashTemplateDO = dashTemplateDAO.getDashTemplateById(idPara.getId());
        if(null!=dashTemplateDO){
            dashTemplateDAO.del(idPara.getId());
            dashTemplateUserRelaDAO.delByTemplateId(dashTemplateDO.getId());
            dashTemplateGroupRelaDAO.delRelaByTemplate(dashTemplateDO.getId());
        }

    }


    public DashTemplateRelaDO searchPointDash(String dashId) {
        DashTemplateRelaDO dashTemplateRelaDO= dashTemplateRelaDAO.getTemplateByDashId(dashId);
        return dashTemplateRelaDO;
    }


    public  List<String> getDashTemplateByTemplateId(String templateId) {
        List<DashTemplateGroupRelaDO>  dashTemplateGroupRelaDOS = dashTemplateGroupRelaDAO.getDashTemplateByTemplateId(templateId);
        List<String> list = new ArrayList<>();
        for (DashTemplateGroupRelaDO dashTemplateGroupRelaDO : dashTemplateGroupRelaDOS){
            list.add(dashTemplateGroupRelaDO.getTemplateGroupId());
        }
        return list;
    }



    public List<DashTemplateUserRelaDO> searchHaveByNameLike(PageQuery<NameQuery> pageQuery) {
        return dashTemplateDAO.searchHaveByNameLike(getName(pageQuery),pageQuery.getUserId(),pageQuery.getPageSize()*(pageQuery.getPageIndex()-1), pageQuery.getPageSize());

    }

    public Integer searchHaveByNameLikeCount(PageQuery<NameQuery> pageQuery) {
        return dashTemplateDAO.getSearchHaveCount(getName(pageQuery),pageQuery.getUserId(),pageQuery.getPageSize()*(pageQuery.getPageIndex()-1), pageQuery.getPageSize());

    }

    private void setOwner(@Valid DashTemplateInput dashTemplateInput, String id ) {
        Set<String> ownerIds=getOwners(dashTemplateInput);
        Instance.ofNullable(ownerIds).forEach(owner->{
            dashTemplateUserRelaDAO.add(id, owner, IdUtils.generateId(), DashUserRelaTypeEnum.OWNER.getCode());
        });
    }

    private Set<String> getOwners(DashTemplateInput dashTemplateInput) {
        Set<String> ownerIds=new HashSet<>();
        ownerIds.add(dashTemplateInput.getUserId());
        String[] owners=StringUtils.split(dashTemplateInput.getTemplateSharedOwners(), CommonSplitConstants.COMMA_SPLIT);
        if(null!=owners){
            for(int i=0;i<owners.length;i++){
                if(StringUtils.isNotBlank(owners[i])){
                    ownerIds.add(owners[i]);
                }
            }
        }
        return ownerIds;
    }

    private String getName(PageQuery<NameQuery> pageQuery) {
        return (pageQuery == null || null == pageQuery.getQueryPara()) ? "" : pageQuery.getQueryPara().getName();
    }
}
