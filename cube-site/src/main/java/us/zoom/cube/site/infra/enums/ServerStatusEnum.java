package us.zoom.cube.site.infra.enums;

/**
 * <AUTHOR>
 */
public enum ServerStatusEnum {
    online(1),
    offline(2);

    private Integer code;
    ServerStatusEnum(int code){
        this.code=code;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public static ServerStatusEnum getByCode(Integer code) {
        for (ServerStatusEnum status : ServerStatusEnum.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
}
