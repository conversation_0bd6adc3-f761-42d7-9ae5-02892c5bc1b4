package us.zoom.cube.site.biz;


import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import us.zoom.cube.lib.common.SaslMechanismEnum;
import us.zoom.cube.site.core.*;
import us.zoom.cube.site.lib.BasePara;
import us.zoom.cube.site.lib.IdPara;
import us.zoom.cube.site.lib.ResponseObject;
import us.zoom.cube.site.lib.input.KafkaClusterInput;
import us.zoom.cube.site.lib.query.KafkaClusterQuery;
import us.zoom.cube.site.lib.query.PageQuery;
import us.zoom.infra.dao.model.KafkaClusterDO;

import java.util.Date;

/**
 * @Author: luis.zheng
 * @Date: 2021/8/17 10:06
 */
public class KafkaClusterServiceTest {
    private KafkaClusterService kafkaClusterService;
    private AuthService authService;
    private KafkaQueueHandler kafkaQueueHandler;
    private KafkaClusterHandler kafkaClusterHandler;
    private TaskQueueHandler taskQueueHandler;
    private DataParserHandler dataParserHandler;
    private TenantHandler tenantHandler;

    private String userId = "userId-10001";
    private String id = "id-10001";

    @BeforeEach
    public void before() {
        authService = Mockito.mock(AuthService.class);
        kafkaQueueHandler = Mockito.mock(KafkaQueueHandler.class);
        kafkaClusterHandler = Mockito.mock(KafkaClusterHandler.class);
        taskQueueHandler = Mockito.mock(TaskQueueHandler.class);
        tenantHandler = Mockito.mock(TenantHandler.class);
        dataParserHandler = Mockito.mock(DataParserHandler.class);
//        Mockito.doNothing().when(authService).mustAdmin(userId);
        Mockito.doNothing().when(authService).checkAuth(new BasePara());
        Mockito.when(kafkaClusterHandler.hasSameName("name")).thenReturn(true);

        kafkaClusterService = new KafkaClusterService(kafkaClusterHandler, kafkaQueueHandler, authService, taskQueueHandler, dataParserHandler, tenantHandler);
    }

    @Test
    public void testSearch() throws Exception {
        KafkaClusterQuery kafkaClusterQuery = new KafkaClusterQuery();
        kafkaClusterQuery.setName("name");
        kafkaClusterQuery.setClusterId("clusterId");
        kafkaClusterQuery.setRegionId("regionId");
        PageQuery<KafkaClusterQuery> query = new PageQuery<>();
        query.setQueryPara(kafkaClusterQuery);
        kafkaClusterService.search(query);
    }

    @Test
    public void testAddSame() {
        IllegalArgumentException err = Assertions.assertThrows(IllegalArgumentException.class, () -> {
            kafkaClusterService.add(createKafkaClusterInput());
        });
        Assertions.assertEquals(err.getMessage(), "Exists kafka cluster with the same name");
    }

    @Test
    public void testAdd() throws Exception {
        Mockito.when(kafkaClusterHandler.hasSameName("name")).thenReturn(false);
        Mockito.doNothing().when(kafkaClusterHandler).add(Mockito.any(KafkaClusterDO.class));
        ResponseObject responseObject = kafkaClusterService.add(createKafkaClusterInput());
        Assertions.assertNotEquals(responseObject.getData(), null);
    }

    @Test
    public void testEditIdIsNull() {
        IllegalArgumentException err = Assertions.assertThrows(IllegalArgumentException.class, () -> {
            kafkaClusterService.edit(createKafkaClusterInput());
        });
        Assertions.assertEquals(err.getMessage(), "id is null");
    }

    @Test
    public void testEditNotExist() throws Exception {
        KafkaClusterInput input = createKafkaClusterInput();
        input.setId(id);
        Mockito.when(kafkaClusterHandler.getSecurityById(id)).thenReturn(null);
        IllegalArgumentException err1 = Assertions.assertThrows(IllegalArgumentException.class, () -> {
            kafkaClusterService.edit(input);
        });
        Assertions.assertEquals(err1.getMessage(), "oldKafkaClusterDO is null");
    }

    @Test
    public void testEditSame() throws Exception {
        KafkaClusterInput input = createKafkaClusterInput();
        input.setId(id);
        KafkaClusterDO kafkaClusterDO = new KafkaClusterDO();
        kafkaClusterDO.setName("name1");
        kafkaClusterDO.setId(id);
        Mockito.when(kafkaClusterHandler.getSecurityById(id)).thenReturn(kafkaClusterDO);
        Mockito.when(kafkaClusterHandler.hasSameName("name1")).thenReturn(true);
        IllegalArgumentException err2 = Assertions.assertThrows(IllegalArgumentException.class, () -> {
            kafkaClusterService.edit(input);
        });
        Assertions.assertEquals(err2.getMessage(), "Exists kafka cluster with the same name");
    }

    @Test
    public void testGetByIdNotExist() throws Exception {
        KafkaClusterInput input = createKafkaClusterInput();
        input.setId(id);

        IdPara idPara = new IdPara();
        idPara.setId(id);

        Mockito.when(kafkaClusterHandler.getSecurityById(id)).thenReturn(null);
        ResponseObject resp = kafkaClusterService.getById(idPara);
        Assertions.assertEquals(resp.getMessage(), "kafka cluster not exist, id:" + id);
    }

    @Test
    public void testGetById() throws Exception {
        KafkaClusterInput input = createKafkaClusterInput();
        input.setId(id);

        IdPara idPara = new IdPara();
        idPara.setId(id);

        Mockito.when(kafkaClusterHandler.getSecurityById(id)).thenReturn(createKafkaClusterDO());
        ResponseObject resp1 = kafkaClusterService.getById(idPara);
        Assertions.assertEquals(((KafkaClusterDO) resp1.getData()).getSslTruststoreLocation(), null);
        Assertions.assertEquals(((KafkaClusterDO) resp1.getData()).getSslTruststorePassword(), null);
        Assertions.assertEquals(((KafkaClusterDO) resp1.getData()).getSslEndpointIdentificationAlgorithm(), null);
        Assertions.assertEquals(((KafkaClusterDO) resp1.getData()).getSslKeystoreLocation(), null);
        Assertions.assertEquals(((KafkaClusterDO) resp1.getData()).getSslKeystorePassword(), null);
        Assertions.assertEquals(((KafkaClusterDO) resp1.getData()).getSslKeyPassword(), null);
        Assertions.assertEquals(((KafkaClusterDO) resp1.getData()).getSaslJaasConfig(), null);
    }

    @Test
    public void testDeleteById() {
        IdPara idPara = new IdPara();
        idPara.setId(id);

        Mockito.doNothing().when(kafkaClusterHandler).deleteById(id);

        Mockito.when(kafkaQueueHandler.countByKafkaClusterId(id)).thenReturn(0);
        kafkaClusterService.deleteById(idPara);

        Mockito.when(kafkaQueueHandler.countByKafkaClusterId(id)).thenReturn(1);
        IllegalArgumentException err = Assertions.assertThrows(IllegalArgumentException.class, () -> {
            kafkaClusterService.deleteById(idPara);
        });
        Assertions.assertEquals(err.getMessage(), "kafka cluster has been used by data parser");
    }

    private KafkaClusterInput createKafkaClusterInput() {
        KafkaClusterInput input = new KafkaClusterInput();
//        input.setUserId(userId);
        input.setName("name");
        input.setRegionId("VA");
        input.setClusterId("ds01");
        input.setBootstrapServersPlain("ec2-50-16-132-120.compute-1.amazonaws.com:9093");
        input.setBootstrapServersSsl("ec2-50-16-132-120.compute-1.amazonaws.com:9095");
        input.setSslTruststoreLocation("adsfasdf");
        input.setSslTruststorePassword("adsfasdf");
        input.setSslEndpointIdentificationAlgorithm("");
        input.setSslKeystoreLocation("");
        input.setSslKeystorePassword("encryptStr");
        input.setSslKeyPassword("");
        input.setSaslJaasConfig("");
        input.setSaslMechanism(SaslMechanismEnum.GSSAPI.name());
        return input;
    }

    private KafkaClusterDO createKafkaClusterDO() {
        KafkaClusterDO cluster = new KafkaClusterDO();
        cluster.setId("id");
        cluster.setName("name");
        cluster.setRegionId("VA");
        cluster.setClusterId("ds01");
        cluster.setBootstrapServersPlain("setBootstrapServersPlain");
        cluster.setBootstrapServersSsl("setBootstrapServersPlain");
        cluster.setSslTruststoreLocation("adsfasdf");
        cluster.setSslTruststorePassword("adsfasdf");
        cluster.setSslEndpointIdentificationAlgorithm("");
        cluster.setSslKeystoreLocation("");
        cluster.setSslKeystorePassword("encryptStr");
        cluster.setSslKeyPassword("");
        cluster.setSaslJaasConfig("");
        cluster.setSaslMechanism(SaslMechanismEnum.GSSAPI.name());
        cluster.setCreateTime(new Date());
        cluster.setModifyTime(new Date());
        return cluster;
    }

}
