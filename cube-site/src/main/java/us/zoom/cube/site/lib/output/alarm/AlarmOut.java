package us.zoom.cube.site.lib.output.alarm;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AlarmOut {

    String id;

    String alarmName;

    String title;

    String alarmContent;

    List<String> alarmCycle;

    String fromTime;

    String toTime;

    String tenantId;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    Date createTime = new Date();

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    Date modifyTime = new Date();

    List<AlarmRuleOut> alarmRules;

    String metricsId;

    Boolean instanceMessagingEnable;

    String instanceMessagingChannelId;
}
