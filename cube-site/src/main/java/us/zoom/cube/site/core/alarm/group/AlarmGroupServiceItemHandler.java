package us.zoom.cube.site.core.alarm.group;

import com.okta.commons.lang.Collections;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import us.zoom.infra.dao.model.AlarmGroupServiceItemDO;
import us.zoom.infra.dao.service.AlarmGroupServiceItemDAO;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: <EMAIL>
 * @ModuleOwner: <EMAIL>
 * @Date: 01/05/2023 19:33
 * @Description:
 */
@Service
public class AlarmGroupServiceItemHandler {
    @Autowired
    private AlarmGroupServiceItemDAO alarmGroupServiceItemDAO;

    public int insert(AlarmGroupServiceItemDO alarmGroupServiceItemDO){
        return alarmGroupServiceItemDAO.insert(alarmGroupServiceItemDO);
    }



    public int batchInsert(List<AlarmGroupServiceItemDO> alarmGroupServiceItemDOList){
        if(Collections.isEmpty(alarmGroupServiceItemDOList)){
            return 0;
        }
        return alarmGroupServiceItemDAO.batchInsert(alarmGroupServiceItemDOList);
    }

    public int batchUpdate(List<AlarmGroupServiceItemDO> alarmGroupServiceItemDOList){
        if(Collections.isEmpty(alarmGroupServiceItemDOList)){
            return 0;
        }
        return alarmGroupServiceItemDAO.batchUpdate(alarmGroupServiceItemDOList);
    }


    public int batchDelete(List<String> idList){
        if(Collections.isEmpty(idList)){
            return 0;
        }
        return alarmGroupServiceItemDAO.batchDelete(idList);
    }



    public List<AlarmGroupServiceItemDO> findByAlarmGroupIdList(List<String> alarmGroupIdList){
        if(Collections.isEmpty(alarmGroupIdList)){
            return new ArrayList<>();
        }
        return alarmGroupServiceItemDAO.findByAlarmGroupIdList(alarmGroupIdList);
    }

    public List<AlarmGroupServiceItemDO> findByServiceId(String serviceId){
        return alarmGroupServiceItemDAO.findByServiceId(serviceId);
    }

    public List<AlarmGroupServiceItemDO> findByServiceIdList(List<String> serviceIdIdList){
        if(Collections.isEmpty(serviceIdIdList)){
            return new ArrayList<>();
        }
        return alarmGroupServiceItemDAO.findByServiceIdList(serviceIdIdList);
    }

    public void deleteByAlarmGroupId(String alarmGroupId){
        if (StringUtils.isBlank(alarmGroupId)) {
            return;
        }
        alarmGroupServiceItemDAO.deleteByAlarmGroupId(alarmGroupId);
    }

    public List<String> findGroupIdByServiceIdList(List<String> serviceIdIdList, Integer limit){
        if(Collections.isEmpty(serviceIdIdList)){
            return new ArrayList<>();
        }
        return alarmGroupServiceItemDAO.findGroupIdByServiceIdList(serviceIdIdList, limit);
    }
}
