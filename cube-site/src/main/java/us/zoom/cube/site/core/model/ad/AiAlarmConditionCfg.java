package us.zoom.cube.site.core.model.ad;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

/**
 * <AUTHOR>
 * @date 2024-01-30 17:48
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AiAlarmConditionCfg {
    private String id;
    private Integer conditionType;
    private String expression;
    private Integer conditionsOrder;
    private String name;
    private String operator;
    private String alarmRuleId;
    private String threshold;
}
