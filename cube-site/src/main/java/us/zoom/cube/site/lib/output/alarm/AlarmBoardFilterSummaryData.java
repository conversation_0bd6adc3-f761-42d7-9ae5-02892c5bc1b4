package us.zoom.cube.site.lib.output.alarm;

import lombok.Data;

/**
 * @author: <PERSON><PERSON>
 * @date: 2022/9/28 13:24
 * @desc: most column same as AlarmBoardSettingItem
 */
@Data
public class AlarmBoardFilterSummaryData {

    public AlarmBoardFilterSummaryData() {
    }

    public AlarmBoardFilterSummaryData(String label) {
        this.label = label;
    }

    public AlarmBoardFilterSummaryData(String label, long count) {
        this.label = label;
        this.count = count;
    }

    private String label;

    private long count;

    private int percent;
}
