package us.zoom.cube.site.core;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import us.zoom.cube.site.infra.utils.IdUtils;
import us.zoom.cube.site.lib.dto.trace.TraceViewDTO;
import us.zoom.infra.dao.model.TraceViewDO;
import us.zoom.infra.dao.service.TraceViewDAO;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @authoer: eason.jia
 * @createDate: 2024/8/31
 * @description:
 */
@Slf4j
@Component
public class TraceViewHandler {

    @Autowired
    private TraceViewDAO traceViewDAO;

    public String addTraceView(TraceViewDTO traceViewDTO) {
        if (StringUtils.isBlank(traceViewDTO.getUuid())) {
            traceViewDTO.setUuid(IdUtils.generateId());
        }
        TraceViewDO traceViewDO = TraceViewDTO.toDO(traceViewDTO);
        traceViewDAO.add(traceViewDO);
        return traceViewDO.getId();
    }

    public void deleteById(@Param("id") String id) {
        traceViewDAO.deleteById(id);
    }

    public void modifyTraceView(TraceViewDTO traceViewDTO) {
        if (StringUtils.isBlank(traceViewDTO.getUuid())) {
            throw new IllegalArgumentException("trace config id can not be empty");
        }
        TraceViewDO traceViewDO = TraceViewDTO.toDO(traceViewDTO);
        traceViewDO.setUpdatedBy(null);
        traceViewDAO.update(traceViewDO);
    }

    public TraceViewDTO getById(@Param("id") String id) {
        TraceViewDO traceViewDO = traceViewDAO.getById(id);
        return TraceViewDTO.fromDO(traceViewDO);
    }

    public TraceViewDTO getById(@Param("id") String id, @Param("createdBy") String createdBy) {
        TraceViewDO traceViewDO = traceViewDAO.getByIdWithCreator(id, createdBy);
        return TraceViewDTO.fromDO(traceViewDO);
    }

    public List<TraceViewDTO> listAll() {
        return Optional.ofNullable(traceViewDAO.listAll())
                .orElse(new ArrayList<>())
                .stream()
                .map(TraceViewDTO::fromDO)
                .collect(Collectors.toList());
    }

    public List<TraceViewDTO> listAll(@Param("createdBy") String createdBy) {
        return Optional.ofNullable(traceViewDAO.listAllWithCreator(createdBy))
                .orElse(new ArrayList<>())
                .stream()
                .map(TraceViewDTO::fromDO)
                .collect(Collectors.toList());
    }
}
