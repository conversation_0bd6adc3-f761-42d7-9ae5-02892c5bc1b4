package us.zoom.cube.site.core.parser.process.core.monitoring;

import com.tdunning.math.stats.MergingDigest;
import com.tdunning.math.stats.TDigest;
import lombok.Getter;
import us.zoom.cube.site.core.parser.process.core.common.constant.MonitoringConst;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

@Getter
public class CommonFields {
    private Object lock = new Object();
    private AtomicInteger transErrorSize = new AtomicInteger(0);
    private AtomicInteger filterSize = new AtomicInteger(0);
    private AtomicInteger blankSize = new AtomicInteger(0);

    private AtomicInteger totalCost = new AtomicInteger(0);
    private AtomicInteger maxCost = new AtomicInteger(0);
    private AtomicInteger size = new AtomicInteger(0);
    private AtomicInteger totalMsgSize = new AtomicInteger(0);

    private AtomicInteger consumerCostTotal = new AtomicInteger(0);
    private AtomicInteger consumerCostMax = new AtomicInteger(0);
    private AtomicInteger consumerSize = new AtomicInteger(0);

    private TDigest delayTime = new MergingDigest(MonitoringConst.PERCENTILE_COMPRESSION);

    public void incrTransErrorSize() {
        this.transErrorSize.incrementAndGet();
    }

    public void incrFilterSize() {
        this.filterSize.incrementAndGet();
    }

    public void incrBlankSize() {
        this.blankSize.incrementAndGet();
    }

    public void delayTime(int delayTime) {
        synchronized (lock) {
            this.delayTime.add(delayTime);
        }
    }

    public void incrCost(int cost, int msgSize) {
        totalCost.getAndAdd(cost);
        size.incrementAndGet();
        if (cost > maxCost.get()) {
            maxCost.set(cost);
        }
        totalMsgSize.getAndAdd(msgSize);
    }

    public void incrConsumerCost(int consumerCost) {
        consumerSize.incrementAndGet();
        consumerCostTotal.getAndAdd(consumerCost);
        if (consumerCost > consumerCostMax.get()) {
            consumerCostMax.set(consumerCost);
        }
    }


    public Map<String, Object> toMap() {
        Map<String, Object> metricsMap = new HashMap<>();
        metricsMap.put("transError", transErrorSize.get());
        metricsMap.put("filter", filterSize.get());
        metricsMap.put("blank", blankSize.get());

        metricsMap.put("size", size.get());
        metricsMap.put("maxCost", maxCost.get());
        metricsMap.put("avgCost", div(totalCost.get(), size.get()));
        metricsMap.put("totalMsgSize", totalMsgSize.get());

        metricsMap.put("csSize", consumerSize.get());
        metricsMap.put("csMax", consumerCostMax.get());
        metricsMap.put("csAvg", div(consumerCostTotal.get(), (consumerSize.get())));
        metricsMap.put("csTotal", consumerCostTotal.get());

        metricsMap.put("delayP99", parseDouble(delayTime.quantile(0.99)));
        metricsMap.put("delayP95", parseDouble(delayTime.quantile(0.95)));
        metricsMap.put("delayP90", parseDouble(delayTime.quantile(0.90)));
        metricsMap.put("delayP75", parseDouble(delayTime.quantile(0.75)));
        metricsMap.put("delayP50", parseDouble(delayTime.quantile(0.50)));

        return metricsMap;
    }

    public double parseDouble(Double d) {
        if (d.isNaN() || d.isInfinite()) {
            return 0d;
        } else {
            return new BigDecimal(d).setScale(2, RoundingMode.HALF_UP).doubleValue();
        }
    }

    public Double div(Integer x, Integer y) {
        try {
            return parseDouble(x / (y * 1.0));
        } catch (Exception e) {
            return null;
        }
    }
}
