package us.zoom.cube.site.infra.enums;

/**
 * <AUTHOR>
 */
public enum WebCodeEnum {

    /**
     *
     */
    PermitError("1101", "Permit error!"),


    /**
     *
     */
    ParaError("1102", "Para error!"),

    PIIPermitError("1103", "This is a PII metrics alarm. Your role is not permit to access it!"),

    InvalidSQL("1104", "Invalid SQL!"),

    DbNoPermit("1105", "Do not have permit to this db!"),

    DbError("1106", "db operation error!"),

    QueryPIINoPermit("1107", "Do not have permit to query pii!"),

    IllegalArgument("1108", "Illegal argument exception"),

    /**
     * dashboard was private!
     */
    DashboardWasPrivate("80002","dashboard is private!"),
    /**
     * dashboard was deleted!
     */
    DashboardWasDeleted("80001","dashboard was deleted!"),

    /**
     * dashboard was deleted!
     */
    DashboardPointWasDeleted("80008","dashboard point was deleted!"),


    /**
     * dashboard was deleted!
     */
    DashboardIsPoint("80009","dashboard is point template can not delete"),

    /**
     * require to login
     */
    RequireLogin("70001", "Please Login!"),
    /**
     * has no such tenant
     */
    HasNoSuchTenant("70002","you don't have such tenant !"),

    TenantAlreadyExisted("70004","the tenant is already existed!"),
    /**
     *has no such agg
     */
    HasNoSuchAgg("70003","you don't have such agg !"),
    /**
     *has no such metrics
     */
    HasNoSuchMetrics("70011","you don't have such metrics !"),
    /**
     *
     */
    InnerError("500", "inner error"),
    /**
     * has no such alarm
     */
    HasNoSuchAlarm("70004","You do not have such alarm!"),
    /**
     * HasNoSuchCollector
     */
    HasNoSuchCollector("70005","you do not have such collector"),
    /**
     * hasNoSuchTopic
     */
    HasNoSuchTopic("70006","you can't edit this topic!"),
    /**
     *HasNoSuchPattern
     */
    HasNoSuchPattern("70007","you don't have such pattern !"),
    /**
     *HasNoSuchPattern
     */
    HasNoSuchDataParser("70008","you can't edit this dataparser!"),

    /**
     *
     */
    CantDelPipelineHaveCollector("70009","you can't del such pipeline ,because it has collector!"),
    /**
     *
     */
    CannotDoSuchOperate("70010", "can't do such operate!"),
    /**
     *
     */
    BadRequestParam("70011","The request params are illegal!"),
    /**
     * cantDefineMetrics
     */
    CantDefineMetrics("70012","You can't define metrics on this data type!"),
    /**
     * cantCollectSample
     */
    CantCollectSample("70013","You can't collect sample log!"),
    /**
     * ConfirmRequired
     */
    ConfirmRequired("70014","confirmation required!"),
    /**
     * serviceLevelIllegal
     */
    ServiceLevelIllegal("70015","service level value is illegal!"),
    /**
     * ServiceKeyAlreadyExisted
     */
    ServiceKeyAlreadyExisted("70016","service key is already existed!"),
    /**
     * NoRelatedUniqService
     */
    NoRelatedUniqService("70017","No related service in Release portal!"),
    /**
     * MentionGroupsPermissionDenied
     */
    MentionGroupsPermissionDenied("60001", "Mention groups access permission denied!"),
    /**
     * MentionGroupsEmpty
     */
    MentionGroupsEmpty("60002", "No mention groups available in this channel!"),
    /**
     * MentionGroupsConnectionIdNotFound
     */
    MentionGroupsConnectionIdNotFound("60004", "Unable to get connectionId from channel parameters!"),
    /**
     * MentionGroupsEnvMismatch
     */
    MentionGroupsEnvMismatch("60003", "Mismatch between Cube environment and the channel environment!"),
    /**
     * MentionGroupsGroupIdNotFound
     */
    MentionGroupsGroupIdNotFound("60005", "Unable to get groupId through connectionId!"),
    /**
     * MentionGroupsUserInfoNotFound
     */
    MentionGroupsUserInfoNotFound("60006", "Unable to get user information!"),
    /**
     * MentionGroupsXMSCallFailed
     */
    MentionGroupsXMSCallFailed("60007", "Unable to get mentionGroups through groupId!"),
    /**
     * CollectorFieldsEmpty
     */
    CollectorFieldsEmpty("20001","collector fields is empty!"),
    /**
     * CollectorFieldRepeat
     */
    CollectorFieldRepeat("20002","collector fields is repeated!"),
    /**
     * EntityExisted
     */
    EntityExisted("20003","the entity has already existed!"),
    /**
     * EntityNotExisted
     */
    EntityNotExisted("20004","the entity is not existed!"),
    /**
     * EntityIsNull
     */
    EntityIsNull("20005","the entity should not be null!"),

    RsaEncryptionError("20006", "Rsa encryption error!"),

    InfluxDBInnerError("20007", "InfluxDB inner error!"),

    DuplicatedPipelineName("20008", "Pipeline name exists!"),

    UserCannotOperateThisService("20009", "User cannot operate this service!"),

    DataFlowPrecessDebugError("80001", "Dataflow preprocess debug error!"),

    DataParserPreprocessDebugError("80002", "DataParser Preprocess debug error!"),

    DataParserPipelineFilterError("80003", "DataParser Pipeline Filter debug  error!"),

    DataParserPipelineProcessorError("80003", "DataParser Pipeline Processor debug  error!"),

    DataParserPipelineProcessorFilterError("800031", "DataParser Pipeline Processor Filter not match!"),

    ServiceMonitorServiceNameEmpty("900010","ServiceName is Empty, please check!"),

    ServiceMonitorDataTypeNotExist("900011","Query  datatype according to the topic is Null, please check!"),

    ServiceMonitorMetaDataNotExist("900012","Query  metadata according to the datatype is Null, please check!"),

    DeleteNewDataParserRelateError("900013","related dataFlows are quoted by other newDataParsers,please unbind template first!"),

    ServiceMonitorMetricDefinitionNotExist("900014","Query  metric definition according to the datatype is Null, please check!"),

    AUTO_DISCOVER_FORBID_EDIT("50002", """
            Since telemetry data may contain PII, please contact Ask Cube Support for editing. Thank you for your cooperation."""),




    BlacklistTopicError("50001", "Blacklist topic error!");

    private String code;

    private String errMsg;

    WebCodeEnum(String code){
        this.code=code;
    }

    WebCodeEnum(String code, String errMsg){
        this.code=code;
        this.errMsg = errMsg;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getErrMsg() {
        return errMsg;
    }

    public void setErrMsg(String errMsg) {
        this.errMsg = errMsg;
    }
}
