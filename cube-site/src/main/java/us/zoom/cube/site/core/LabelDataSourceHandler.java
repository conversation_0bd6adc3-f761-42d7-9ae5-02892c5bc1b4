package us.zoom.cube.site.core;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import us.zoom.infra.dao.model.LabelDataSourceDO;
import us.zoom.infra.dao.service.LabelDataSourceDAO;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component
public class LabelDataSourceHandler {
    @Autowired
    private LabelDataSourceDAO labelDataSourceDAO;


    public List<LabelDataSourceDO> findByNameLike(String name, int pageIndex, int pageSize) {
        return  labelDataSourceDAO.findByNameLike(name,pageSize*(pageIndex-1), pageSize);
    }

    public int getCountByNameLike(String name) {
        return  labelDataSourceDAO.getCountByNameLike(name);
    }

    public void addLabelDataSource(LabelDataSourceDO labelDataSourceDO) {
        labelDataSourceDAO.addLabelDataSource(labelDataSourceDO);
    }

    public void editLabelDataSource(LabelDataSourceDO labelDataSourceDO) {
        labelDataSourceDAO.editLabelDataSource(labelDataSourceDO);
    }

    public LabelDataSourceDO getLabelDataSourceById(String id) {
      return   labelDataSourceDAO.getLabelDataSourceById(id);
    }

    public List<LabelDataSourceDO> listByType(String labelSourceType) {
        return   labelDataSourceDAO.listByType(labelSourceType);
    }

    public List<LabelDataSourceDO> listAll( ) {
        return   labelDataSourceDAO.listAll();
    }
}
