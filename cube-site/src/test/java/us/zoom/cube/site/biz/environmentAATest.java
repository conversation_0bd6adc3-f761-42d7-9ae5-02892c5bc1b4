package us.zoom.cube.site.biz;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import us.zoom.cube.site.lib.IdPara;
import us.zoom.cube.site.lib.input.EnvironmentInput;
import us.zoom.cube.site.lib.input.EnvironmentModel;
import us.zoom.cube.site.lib.input.EnvironmentRunTimeInput;
import us.zoom.infra.dao.model.EnvironmentChangeLogDO;
import us.zoom.infra.enums.EnvironmentChangeTypeEnum;
import us.zoom.infra.enums.EnvironmentIsMineEnum;
import us.zoom.infra.enums.EnvironmentModEnum;
import us.zoom.cube.lib.utils.JsonUtils;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2022-08-10 17:51
 */
public class environmentAATest extends  BaseTest{

    @Autowired
    private EnvironmentService environmentService;

    @Autowired
    private EnvironmentRunTimeService environmentRunTimeService;

    @Autowired
    private EnvironmentChangeLogService environmentChangeLogService;

    String user1="82b4e894-edc7-47b8-ae99-58b6cbf58326";


    @Test
    public void environmentServiceAdd(){
        EnvironmentInput environmentInput =new EnvironmentInput();
        environmentInput.setCity("OHO");
        environmentInput.setCountry("US");
        environmentInput.setIdc("ZOON1");
        environmentInput.setName("MAIN");
        environmentInput.setIsMain(1);
        String test = JsonUtils.toJsonString(environmentInput);
        environmentService.addEnvironment(environmentInput);

        EnvironmentInput environmentInput2 =new EnvironmentInput();
        environmentInput2.setCity("OHO");
        environmentInput2.setCountry("US");
        environmentInput2.setIdc("ZOON2");
        environmentInput2.setName("STAND");
        environmentInput2.setIsMain(0);
        environmentService.addEnvironment(environmentInput2);
    }
    @Test
    public void environmentServiceEdit(){
        EnvironmentInput environmentInput =new EnvironmentInput();
        environmentInput.setId("7d5e2b79-cd86-44dd-8462-45974a07a707");
        environmentInput.setCity("OHO1");
        environmentInput.setCountry("US1");
        environmentInput.setIdc("ZOON2");
        environmentInput.setName("MAIN1");
        environmentInput.setIsMain(1);
        environmentService.editEnvironment(environmentInput);
    }

    @Test
    public void environmentServiceDel(){
        IdPara idPara=new IdPara();
        idPara.setId("123");
        environmentService.delEnvironment(idPara);
    }

    @Test
    public void environmentServiceSearch(){
        EnvironmentModel environmentModel = environmentService.searchEnvironmentAll();
        String test = JsonUtils.toJsonString(environmentModel);
    }

    @Test
    @Disabled
    public void environmentRunTimeServiceEdit(){
        EnvironmentRunTimeInput environmentRunTimeInput = new EnvironmentRunTimeInput();
        environmentRunTimeInput.setId("123");
        environmentRunTimeInput.setMode(EnvironmentModEnum.STANDBY.getCode());
        environmentRunTimeInput.setIsForce(EnvironmentIsMineEnum.TRUE.getCode());
        environmentRunTimeInput.setWorkingEnvironment("STAND");
        environmentRunTimeInput.setForceEnvironment("STAND");
        environmentRunTimeInput.setChangeType(EnvironmentChangeTypeEnum.SITE.getCode());
        environmentRunTimeInput.setChangeOwner("82b4e894-edc7-47b8-ae99-58b6cbf58326");
        environmentRunTimeInput.setChangeNowEnvironment("STAND");
        environmentRunTimeInput.setChangePastEnvironment("MAIN");
        environmentRunTimeInput.setIsForce(1);
        String test = JsonUtils.toJsonString(environmentRunTimeInput);
        environmentRunTimeService.editEnvironmentRunTime(environmentRunTimeInput);
    }

    @Test
    public void getEnvironmentChangeLogService(){
        List<EnvironmentChangeLogDO> environmentChangeLogDOList = environmentChangeLogService.searchEnvironmentChangeLog();
        String test=JsonUtils.toJsonString(environmentChangeLogDOList);
    }
}
