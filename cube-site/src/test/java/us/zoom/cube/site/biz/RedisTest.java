package us.zoom.cube.site.biz;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import us.zoom.cube.lib.agent.AgentHeartCacheInfo;
import us.zoom.infra.enums.AgentStatusEnum;
import us.zoom.infra.redis.RedisService;
import us.zoom.cube.lib.utils.JsonUtils;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @author: canyon.li
 * @date: 2024/06/19
 **/
public class RedisTest extends BaseTest {

    @Autowired
    RedisService redisService;

    @Test
    public void testRedis() {
        try {
            batchInsert();
            set();
            get();
            getValuesByPrefix();
            getCastValue();
            delete();
            get();
            batchDelete();
            getValuesByPrefix();

        }catch (Exception e) {
            System.out.println(e.getMessage());
        }
    }


    @Test
    public void batchInsert() {
        try {
            Map<String, Object> cacheInfoMap = new HashMap<>();
            cacheInfoMap.put("testKey_0829_1", JsonUtils.toJsonString(createAgentHeartCacheInfo("us_agent_heart_beat")));
            cacheInfoMap.put("testKey_0829_2", JsonUtils.toJsonString(createAgentHeartCacheInfo("us_agent_heart_beat")));
            redisService.batchInsert(cacheInfoMap);
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
    }

    @Test
    public void set() {
        try {
            redisService.set("agentHeart__ip-10-21-7-123_i-0a166215275a41387_AWS", JsonUtils.toJsonString(createAgentHeartCacheInfo("us_agent_heart_beat")));
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
    }

    @Test
    public void get() {
        try {
            System.out.println(redisService.get("mykey"));
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
    }

    @Test
    public void delete() {
        try {
            System.out.println(redisService.delete("testKey_0829_3"));
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
    }

    @Test
    public void batchDelete() {
        try {
            List<String> keys = Arrays.asList("testKey_0829_1", "testKey_0829_2");
            redisService.batchDelete(keys);
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
    }

    @Test
    public void getValuesByPrefix() {
        try {
            List<AgentHeartCacheInfo> results = redisService.getValuesByPrefix("testKey_0829", AgentHeartCacheInfo.class);
            System.out.println(JsonUtils.toJsonStringIgnoreExp(results));
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
    }

    @Test
    public void getCastValue() {
        try {
            AgentHeartCacheInfo result = redisService.getCastValue("testKey_0829_3", AgentHeartCacheInfo.class);
            System.out.println(JsonUtils.toJsonStringIgnoreExp(result));
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
    }


    private AgentHeartCacheInfo createAgentHeartCacheInfo(String sourceUnit) {
        AgentHeartCacheInfo cacheInfo = new AgentHeartCacheInfo();
        cacheInfo.setSourceUnit(sourceUnit);
        cacheInfo.setStatus(AgentStatusEnum.offline.getCode());
        cacheInfo.setUpStatusTime(1722856382477L);
        cacheInfo.setId("69f59392-1d52-4388-8b12-ef10818c95ee");
        return cacheInfo;
    }
}
