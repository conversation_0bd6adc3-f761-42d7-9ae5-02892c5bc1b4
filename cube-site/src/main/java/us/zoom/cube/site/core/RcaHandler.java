package us.zoom.cube.site.core;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.site.infra.enums.WebCodeEnum;
import us.zoom.cube.site.infra.utils.IdUtils;
import us.zoom.cube.site.lib.SiteException;
import us.zoom.cube.site.lib.input.rca.RcaAddInput;
import us.zoom.cube.site.lib.input.rca.RcaCommentInput;
import us.zoom.cube.site.lib.input.rca.RcaEditInput;
import us.zoom.cube.site.lib.input.rca.RcaLinkInput;
import us.zoom.cube.site.lib.input.rca.RcaStatusInput;
import us.zoom.cube.site.lib.query.PageQuery;
import us.zoom.cube.site.lib.query.outage.RcaQuery;
import us.zoom.cube.site.lib.query.outage.RcaCommentQuery;
import us.zoom.infra.dao.model.RcaCommentDO;
import us.zoom.infra.dao.model.RcaDO;
import us.zoom.infra.dao.model.RcaLinkDO;
import us.zoom.infra.dao.service.RcaDAO;
import us.zoom.infra.notification.channel.email.EmailChannelEngine;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class RcaHandler {

    @Autowired
    private RcaDAO rcaDAO;

    @Autowired
    private UserHandler userHandler;

    @Autowired
    private EmailChannelEngine emailChannelEngine;


    @Value("${cube.outage.url.alert:}")
    private String cubeOutageUrlAlert;

    private static final Logger monitorLogger = LoggerFactory.getLogger("Monitor");



    public List<RcaDO> searchRcaList(PageQuery<RcaQuery> query) {
        Assert.notNull(query, "input is null");
        Assert.notNull(query.getQueryPara(), "queryPara is null");
        return rcaDAO.searchRcaList(query.getQueryPara().getTitle(),query.getQueryPara().getSevLevel(),query.getQueryPara().getStatus(),query.getQueryPara().getStartTime(),query.getQueryPara().getEndTime(),query.getPageSize() * (query.getPageIndex() - 1), query.getPageSize());
    }

    public Integer getRcaCount(PageQuery<RcaQuery> query) {
        Assert.notNull(query, "input is null");
        Assert.notNull(query.getQueryPara(), "queryPara is null");
        return rcaDAO.getRcaListCount(query.getQueryPara().getTitle(), query.getQueryPara().getSevLevel(), query.getQueryPara().getStatus(),query.getQueryPara().getStartTime(), query.getQueryPara().getEndTime(), query.getPageSize() * (query.getPageIndex() - 1), query.getPageSize());
    }

    /**
     * add
     * @param rcaAddInput
     * @return outageId
     */
    public String add(RcaAddInput rcaAddInput) {
        check(rcaAddInput);
        RcaDO rcaDO = new RcaDO();
        BeanUtils.copyProperties(rcaAddInput, rcaDO);
        rcaDO.setId(IdUtils.generateId());
        String userName = userHandler.getNameById(rcaAddInput.getUserId());
        rcaDO.setCreator(userName);
        rcaDO.setLastModifiedBy(userName);
        if (StringUtils.isBlank(rcaDO.getDescription())) {
            rcaDO.setDescription(StringUtils.EMPTY);
        }
        if(StringUtils.isBlank(rcaDO.getRootCause())){
            rcaDO.setRootCause(StringUtils.EMPTY);
        }
        rcaDAO.add(rcaDO);
        return rcaDO.getId();
    }

    private void check(RcaAddInput rcaAddInput) {
        Assert.notNull(rcaAddInput, "rca input is null");
        Assert.isTrue(!StringUtils.isBlank(rcaAddInput.getTitle()), "rca title is blank");
        Assert.isTrue(!StringUtils.isBlank(rcaAddInput.getSevLevel()), "rca level is blank");
        Assert.isTrue(!StringUtils.isBlank(rcaAddInput.getProductType()), "rca productType is blank");
        RcaDO rcaDO = this.searchRcaByTitle(rcaAddInput.getTitle());
        if(rcaDO != null){
            throw new SiteException(WebCodeEnum.EntityExisted);
        }
    }

    @Transactional
    public void edit(RcaEditInput rcaEditInput) {
        Assert.isTrue(!StringUtils.isBlank(rcaEditInput.getId()), "id is blank");
        RcaDO rcaDO = new RcaDO();
        BeanUtils.copyProperties(rcaEditInput, rcaDO);
        String userName = userHandler.getNameById(rcaEditInput.getUserId());
        rcaDO.setLastModifiedBy(userName);
        if(StringUtils.isNotBlank(rcaEditInput.getTitle())){
            RcaDO rca = this.searchRcaByTitle(rcaEditInput.getTitle());
            if(rca != null && !rca.getId().equals(rcaEditInput.getId())){
                throw new SiteException(WebCodeEnum.EntityExisted);
            }
        }
        rcaDAO.updateRca(rcaDO);

        if (!CollectionUtils.isEmpty(rcaEditInput.getRcaLinkInputs())) {
            rcaDAO.deleteLinks(rcaEditInput.getId());
            List<RcaLinkInput> rcaLinkInputs = rcaEditInput.getRcaLinkInputs();
            List<RcaLinkDO> rcaLinkDOS = rcaLinkInputs.stream().map(outageLinkInput -> RcaLinkDO.builder().id(IdUtils.generateId()).rcaId(rcaEditInput.getId()).linkUrl(outageLinkInput.getLinkUrl()).linkDesc(outageLinkInput.getLinkDesc()).build()).collect(Collectors.toList());
            rcaDAO.addLinks(rcaLinkDOS);
        }
    }

    public RcaDO getById(String id) {
        Assert.isTrue(!StringUtils.isBlank(id), "id is blank");
        RcaDO rcaDO = rcaDAO.getById(id);
        List<RcaLinkDO> rcaLinkDOS = rcaDAO.selectLinks(id);
        if(!CollectionUtils.isEmpty(rcaLinkDOS)){
            rcaDO.setRcaLinkDOS(rcaLinkDOS);
        }
        return rcaDO;
    }

    public void delete(String id) {
        Assert.isTrue(!StringUtils.isBlank(id), "id is blank");
        rcaDAO.delete(id);
    }

    public void updateStatus(RcaStatusInput rcaStatusInput) {
        Assert.isTrue(!StringUtils.isBlank(rcaStatusInput.getId()), "id is blank");
        Assert.isTrue(!StringUtils.isBlank(rcaStatusInput.getStatus()), "status is blank");
        RcaDO rcaDO=new RcaDO();
        BeanUtils.copyProperties(rcaStatusInput,rcaDO);
        String userName = userHandler.getNameById(rcaStatusInput.getUserId());
        rcaDO.setLastModifiedBy(userName);
        String  fromStatus = rcaDAO.getById(rcaDO.getId()).getStatus();
        rcaDAO.updateStatus(rcaDO.getStatus(),userName,rcaDO.getId(),fromStatus);
    }

    public void comment(RcaCommentInput commentInput) {
        Assert.isTrue(StringUtils.isNotBlank(commentInput.getCommentId()), "commentId is blank");
        Assert.isTrue(StringUtils.isNotBlank(commentInput.getRcaId()), "rcaId is blank");
        Assert.isTrue(StringUtils.isNotBlank(commentInput.getContent()), "content is blank");
        Assert.isTrue(StringUtils.isNotBlank(commentInput.getMailContent()), "mailContent is blank");
        RcaCommentDO rcaCommentDO = new RcaCommentDO();
        BeanUtils.copyProperties(commentInput, rcaCommentDO);
        String userName = userHandler.getNameById(commentInput.getUserId());
        rcaCommentDO.setCreator(userName);
        rcaCommentDO.setLastModifiedBy(userName);
        rcaCommentDO.setId(IdUtils.generateId());
        //comment notice
        if (StringUtils.isNotBlank(commentInput.getMentionedUserIds())) {
            String mentionedUserIds = commentInput.getMentionedUserIds();
            List<String> mentionedUserIdList = Arrays.asList(mentionedUserIds.split(","));
            mentionedUserIds = mentionedUserIdList.stream()
                    .distinct()
                    .collect(Collectors.joining(","));
            rcaCommentDO.setMentionedUserIds(mentionedUserIds);
            notice(commentInput.getRcaId(), mentionedUserIds, userName, commentInput.getMailContent(),commentInput.getContent(), commentInput.getCommentId(),"comment");
        }
        if(StringUtils.isBlank(rcaCommentDO.getParentCommentId())){
            rcaCommentDO.setParentCommentId("0");
        }
        if(StringUtils.isBlank(rcaCommentDO.getMentionedUserIds())){
            rcaCommentDO.setMentionedUserIds(StringUtils.EMPTY);
        }
        if(StringUtils.isBlank(rcaCommentDO.getType())){
            rcaCommentDO.setType(StringUtils.EMPTY);
        }
        rcaDAO.comment(rcaCommentDO);
    }



    private void notice(String rcaId, String mentionedUserIds, String userName,String mailContent, String commentContent,String relatedId,String moduleName) {
        List<String> mentionedUserIdList = Arrays.asList(mentionedUserIds.split(","));
        mentionedUserIdList.forEach(mentionId -> {
            Map<String, String> parameters = new HashMap<>();
            parameters.put("Recipients", mentionId);

            // Generate dynamic content
            String detailsLink = getNoticeDetailLink(rcaId, relatedId, moduleName);
            LocalDateTime now = LocalDateTime.now();
            String time =  DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(now);
            String emailUserName = getEmailPrefix(userName);
            // HTML Content with Styles
            String htmlMessage;
            if(moduleName.equals("comment")){
                htmlMessage = String.format(
                        "<!DOCTYPE html>" +
                                "<html lang=\"en-US\">" +
                                "<head>" +
                                "  <meta charset=\"UTF-8\">" +
                                "  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">" +
                                "  <meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\">" +
                                "  <title>New notification in \"cube-outage-center\"</title>" +
                                "</head>" +
                                "<body style=\"height: 100%%; margin: 0; padding: 0; width: 100%%;\">" +
                                "<div class=\"container\" style=\"background-color: #fff; margin: 0 auto; max-width: 1024px; padding: 0; width: 100%%;\">" +
                                "  <div id=\"header\" style=\"background-color: #0b5cff; box-sizing: border-box; margin: 0; padding: 28px 32px; width: 100%%;\">" +
                                "    <img id=\"logo\" alt=\"LOGO\" src=\"https://st1.zoom.us/zoom-docs/email/assets/logo_white.png\" style=\"height: 24px; margin: 0; padding: 0; width: auto;\">" +
                                "  </div>" +
                                "" +
                                "  <div id=\"article\" style=\"color: #131619; font-family: emoji, Inter, ui-sans-serif, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Helvetica Neue', Arial, sans-serif, 'Segoe UI Symbol'; font-size: 16px; line-height: 1.5em; margin: 0 auto; max-width: 580px; padding: 32px; text-align: left; width: fit-content;\">" +
                                "    <img id=\"docsLogo\" alt=\"ZOOM DOCS\" src=\"https://file-paa.zoom.us/xqmuP22rQPuGwV7ocsfMeg/MS4yLiECN-QGFhJmvbltua3t5_oVk2M4tAfBku0wyhlCiM3q/cube-icon.png\" style=\"height: 32px; margin: 0; margin-bottom: 20px; padding: 0; width: auto;\">" +
                                "    <h1 style=\"color: #131619; font-family: inherit; font-size: 30px; font-weight: 700; hyphens: auto; line-height: 40px; margin: 0 0 32px 0; padding: 0;\">New notification in \"cube-outage-center\"</h1>" +
                                "    <h2 style=\"color: #131619; font-family: inherit; font-size: 20px; font-weight: 500; line-height: 28px; margin: 0 0 10px 0; padding: 0;\">%s commented on page <a class=\"file\" href=\"%s\" target=\"_blank\" style=\"color: #0b5cff; display: inline; font-size: 20px; text-decoration: none;\"><img alt src=\"https://st1.zoom.us/zoom-docs/email/assets/icon_page.png\" style=\"height: 20px; margin: 0 2px 4px 0; padding: 0; vertical-align: middle;\"><span style=\"font-size: inherit; vertical-align: baseline;\">cube-故障知识库</span></a></h2>" +
                                "    <time style=\"color: #6e7680; display: inline-block; font-family: inherit; font-size: 16px; margin: 0 0 12px 0; padding: 0;\">%s (UTC)</time>" +
                                "    <br>" +
                                "" +
                                "    <blockquote style=\"border-left: 4px solid #ffd100; color: #131619; font-family: inherit; font-size: 16px; margin: 20px 0 0 0; padding: 0 0 0 5px;\">%s</blockquote>" +
                                "" +
                                "    <h5 style=\"color: #131619; font-family: inherit; font-size: 14px; line-height: 20px; margin: 32px 0 0 0; padding: 0;\">%s</h5>" +
                                "    <pre style=\"color: #131619; font-family: inherit; font-size: 16px; line-height: 1.5em; margin: 0; padding: 0; white-space: pre-wrap;\">%s</pre>" +
                                "" +
                                "    <span class=\"actions\" style=\"display: inline-block; font-size: 16px; margin: 32px 0; text-align: center; width: 100%%;\">" +
                                "      <a class=\"button\" href=\"%s\" target=\"_blank\" style=\"background-color: #0b5cff; border: 0px solid #333333; border-color: #333333; border-radius: 20px; border-style: solid; border-width: 0px; color: #ffffff; display: inline-block; font-size: 16px; font-weight: bold; letter-spacing: 0px; line-height: normal; min-width: 20%%; padding: 12px 40px 12px 40px; text-align: center; text-decoration: none;\">View comment</a>" +
                                "    </span>" +
                                "  </div>" +
                                "" +
                                "  <div id=\"footer\" style=\"box-sizing: border-box; color: rgba(4, 4, 19, 0.56); font-size: 12px; margin: 0; padding: 24px 32px; text-align: center; width: 100%%;\">" +
                                "    <ul style=\"color: #131619; display: inline-flex; flex-direction: row; font-family: inherit; font-size: 12px; list-style: none; margin: 0 0 16px 0; padding: 0;\">" +
                                "      <li style=\"color: #131619; display: inline-block; font-family: inherit; font-size: 12px; list-style: none; margin: 0; padding: 0;\">" +
                                "        <a href=\"https://twitter.com/zoom\" target=\"_blank\" rel=\"noopener noreferrer\" style=\"color: #0b5cff; font-size: 12px;\">" +
                                "          <img alt=\"Twitter\" class=\"icon\" src=\"https://st1.zoom.us/zoom-docs/email/assets/icon_twitter.png\" style=\"height: 32px; margin: 0; padding: 0; width: 32px;\">" +
                                "        </a>" +
                                "      </li>" +
                                "      <li style=\"color: #131619; display: inline-block; font-family: inherit; font-size: 12px; list-style: none; margin: 0 0 0 16px; padding: 0;\">" +
                                "        <a href=\"https://www.linkedin.com/company/2532259\" target=\"_blank\" rel=\"noopener noreferrer\" style=\"color: #0b5cff; font-size: 12px;\">" +
                                "          <img alt=\"LinkedIn\" class=\"icon\" src=\"https://st1.zoom.us/zoom-docs/email/assets/icon_in.png\" style=\"height: 32px; margin: 0; padding: 0; width: 32px;\">" +
                                "        </a>" +
                                "      </li>" +
                                "      <li style=\"color: #131619; display: inline-block; font-family: inherit; font-size: 12px; list-style: none; margin: 0 0 0 16px; padding: 0;\">" +
                                "        <a href=\"https://blog.zoom.us\" target=\"_blank\" rel=\"noopener noreferrer\" style=\"color: #0b5cff; font-size: 12px;\">" +
                                "          <img alt=\"Blog\" class=\"icon\" src=\"https://st1.zoom.us/zoom-docs/email/assets/icon_blog.png\" style=\"height: 32px; margin: 0; padding: 0; width: 32px;\">" +
                                "        </a>" +
                                "      </li>" +
                                "    </ul>" +
                                "    <p style=\"color: rgba(4, 4, 19, 0.56); font-family: inherit; font-size: 12px; line-height: 20px; margin: 0; padding: 0;\">Copyright &copy;2024 Zoom Video Communications, Inc. All rights reserved.</p>" +
                                "    <p style=\"color: rgba(4, 4, 19, 0.56); font-family: inherit; font-size: 12px; line-height: 20px; margin: 16px 0 0 0; padding: 0;\">" +
                                "      55 Almaden Boulevard, 6th Floor, San Jose, CA 95113 <br>" +
                                "      +1.888.799.9666" +
                                "    </p>" +
                                "  </div>" +
                                "</div>" +
                                "</body>" +
                                "</html>",
                        emailUserName, detailsLink, time, mailContent, emailUserName, commentContent, detailsLink
                );
            }else {
                htmlMessage = String.format(
                        "<!DOCTYPE html>" +
                                "<html lang=\"en-US\">" +
                                "<head>" +
                                "  <meta charset=\"UTF-8\">" +
                                "  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">" +
                                "  <meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\">" +
                                "  <title>New notification in \"cube-outage-center\"</title>" +
                                "<style>" +
                                ".svg-logo {" +
                                "    height: 32px;" +
                                "    margin: 0;" +
                                "    margin-bottom: 20px;" +
                                "    padding: 0;" +
                                "    width: auto;" +
                                "}" +
                                "</style>" +
                                "</head>" +
                                "<body style=\"height: 100%%; margin: 0; padding: 0; width: 100%%;\">" +
                                "<div class=\"container\" style=\"background-color: #fff; margin: 0 auto; max-width: 1024px; padding: 0; width: 100%%;\">" +
                                "  <div id=\"header\" style=\"background-color: #0b5cff; box-sizing: border-box; margin: 0; padding: 28px 32px; width: 100%%;\">" +
                                "    <img id=\"logo\" alt=\"LOGO\" src=\"https://st1.zoom.us/zoom-docs/email/assets/logo_white.png\" style=\"height: 24px; margin: 0; padding: 0; width: auto;\">" +
                                "  </div>" +
                                "" +
                                "  <div id=\"article\" style=\"color: #131619; font-family: emoji, Inter, ui-sans-serif, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Helvetica Neue', Arial, sans-serif, 'Segoe UI Symbol'; font-size: 16px; line-height: 1.5em; margin: 0 auto; max-width: 580px; padding: 32px; text-align: left; width: fit-content;\">" +
                                "    <img id=\"docsLogo\" alt=\"ZOOM DOCS\" src=\"https://file-paa.zoom.us/xqmuP22rQPuGwV7ocsfMeg/MS4yLiECN-QGFhJmvbltua3t5_oVk2M4tAfBku0wyhlCiM3q/cube-icon.png\" style=\"height: 32px; margin: 0; margin-bottom: 20px; padding: 0; width: auto;\">" +
                                "    <h1 style=\"color: #131619; font-family: inherit; font-size: 30px; font-weight: 700; hyphens: auto; line-height: 40px; margin: 0 0 32px 0; padding: 0;\">New notification in \"cube-outage-center\"</h1>" +
                                "    <h2 style=\"color: #131619; font-family: inherit; font-size: 20px; font-weight: 500; line-height: 28px; margin: 0 0 10px 0; padding: 0;\">%s mentioned you in <a class=\"file\" href=\"%s\" target=\"_blank\" style=\"color: #0b5cff; display: inline; font-size: 20px; text-decoration: none;\"><img alt src=\"https://st1.zoom.us/zoom-docs/email/assets/icon_page.png\" style=\"height: 20px; margin: 0 2px 4px 0; padding: 0; vertical-align: middle;\"><span style=\"font-size: inherit; vertical-align: baseline;\">cube-outage-center</span></a></h2>" +
                                "    <time style=\"color: #6e7680; display: inline-block; font-family: inherit; font-size: 16px; margin: 0 0 12px 0; padding: 0;\">%s (UTC)</time>" +
                                "    <br>" +
                                "" +
                                "" +
                                "    <span class=\"actions\" style=\"display: inline-block; font-size: 16px; margin: 32px 0; text-align: center; width: 100%%;\">" +
                                "      <a class=\"button\" href=\"%s\" target=\"_blank\" style=\"background-color: #0b5cff; border: 0px solid #333333; border-color: #333333; border-radius: 20px; border-style: solid; border-width: 0px; color: #ffffff; display: inline-block; font-size: 16px; font-weight: bold; letter-spacing: 0px; line-height: normal; min-width: 20%%; padding: 12px 40px 12px 40px; text-align: center; text-decoration: none;\">View mention</a>" +
                                "    </span>" +
                                "  </div>" +
                                "" +
                                "  <div id=\"footer\" style=\"box-sizing: border-box; color: rgba(4, 4, 19, 0.56); font-size: 12px; margin: 0; padding: 24px 32px; text-align: center; width: 100%%;\">" +
                                "    <ul style=\"color: #131619; display: inline-flex; flex-direction: row; font-family: inherit; font-size: 12px; list-style: none; margin: 0 0 16px 0; padding: 0;\">" +
                                "      <li style=\"color: #131619; display: inline-block; font-family: inherit; font-size: 12px; list-style: none; margin: 0; padding: 0;\">" +
                                "        <a href=\"https://twitter.com/zoom\" target=\"_blank\" rel=\"noopener noreferrer\" style=\"color: #0b5cff; font-size: 12px;\">" +
                                "          <img alt=\"Twitter\" class=\"icon\" src=\"https://st1.zoom.us/zoom-docs/email/assets/icon_twitter.png\" style=\"height: 32px; margin: 0; padding: 0; width: 32px;\">" +
                                "        </a>" +
                                "      </li>" +
                                "      <li style=\"color: #131619; display: inline-block; font-family: inherit; font-size: 12px; list-style: none; margin: 0 0 0 16px; padding: 0;\">" +
                                "        <a href=\"https://www.linkedin.com/company/2532259\" target=\"_blank\" rel=\"noopener noreferrer\" style=\"color: #0b5cff; font-size: 12px;\">" +
                                "          <img alt=\"LinkedIn\" class=\"icon\" src=\"https://st1.zoom.us/zoom-docs/email/assets/icon_in.png\" style=\"height: 32px; margin: 0; padding: 0; width: 32px;\">" +
                                "        </a>" +
                                "      </li>" +
                                "      <li style=\"color: #131619; display: inline-block; font-family: inherit; font-size: 12px; list-style: none; margin: 0 0 0 16px; padding: 0;\">" +
                                "        <a href=\"https://blog.zoom.us\" target=\"_blank\" rel=\"noopener noreferrer\" style=\"color: #0b5cff; font-size: 12px;\">" +
                                "          <img alt=\"Blog\" class=\"icon\" src=\"https://st1.zoom.us/zoom-docs/email/assets/icon_blog.png\" style=\"height: 32px; margin: 0; padding: 0; width: 32px;\">" +
                                "        </a>" +
                                "      </li>" +
                                "    </ul>" +
                                "    <p style=\"color: rgba(4, 4, 19, 0.56); font-family: inherit; font-size: 12px; line-height: 20px; margin: 0; padding: 0;\">Copyright &copy;2024 Zoom Video Communications, Inc. All rights reserved.</p>" +
                                "    <p style=\"color: rgba(4, 4, 19, 0.56); font-family: inherit; font-size: 12px; line-height: 20px; margin: 16px 0 0 0; padding: 0;\">" +
                                "      55 Almaden Boulevard, 6th Floor, San Jose, CA 95113 <br>" +
                                "      +1.888.799.9666" +
                                "    </p>" +
                                "  </div>" +
                                "</div>" +
                                "</body>" +
                                "</html>",
                        emailUserName, detailsLink, time, detailsLink);
            }
            String title = "New Notification from \"Cube Outage Center\"";

            try {
                emailChannelEngine.sendHtmlEmail(parameters, title, htmlMessage);
            } catch (Exception e) {
                monitorLogger.error("Mentioned notice error: {}", e.getMessage());
            }
        });
    }

    public static String getEmailPrefix(String email) {
        if (email == null || !email.contains("@")) {
            throw new IllegalArgumentException("Invalid email address");
        }

        int atIndex = email.indexOf('@');

        return email.substring(0, atIndex);
    }

    private String getNoticeDetailLink(String outageId, String relatedId, String moduleName) {
        String detailsLink;
        if (moduleName.equals("comment")) {
            detailsLink = String.format("%soutage-center?outageId=%s&commentId=%s", cubeOutageUrlAlert, outageId, relatedId);
        }else if(moduleName.equals("edit")){
            detailsLink = String.format("%soutage-center?outageId=%s&mentionModuleType=%s", cubeOutageUrlAlert, outageId, relatedId);
        } else {
            detailsLink = String.format("%soutage-center?outageId=%s&eventId=%s", cubeOutageUrlAlert, outageId, relatedId);
        }
        return detailsLink;
    }


    public List<RcaCommentDO> searchRcaCommentList(RcaCommentQuery query) {
        Assert.notNull(query, "input is null");
        Assert.isTrue(!StringUtils.isBlank(query.getRcaId()), "rcaId is blank");
        return rcaDAO.searchRcaCommentList(query.getRcaId());

    }

    public void deleteComment(String rcaId, String commentId) {
        Assert.isTrue(!StringUtils.isBlank(rcaId), "rcaId is blank");
        Assert.isTrue(!StringUtils.isBlank(commentId), "commentId is blank");
        rcaDAO.deleteParentComment(rcaId,commentId);
        rcaDAO.deleteChildComment(rcaId,commentId);
    }


    public RcaDO searchRcaByTitle(String title) {

        return rcaDAO.searchByTitle(title);
    }
}
