package us.zoom.cube.site.core;

import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import us.zoom.infra.dao.model.DataParserTimestampProcessorDO;
import us.zoom.infra.dao.service.DataParserTimestampProcessorDAO;

import java.util.List;

@Component
public class DataParserTimestampProcessorHandler {
    @Autowired
    private DataParserTimestampProcessorDAO dataParserTimestampProcessorDAO;

    public void addTimestampProcessor(DataParserTimestampProcessorDO dataParserTimestampProcessorDO) {
        Assert.notNull(dataParserTimestampProcessorDO,"timestamp processor is null !");
        dataParserTimestampProcessorDAO.add(dataParserTimestampProcessorDO);
    }

    public DataParserTimestampProcessorDO getTimestampProcessorById(String id) {
        Assert.notNull(id, "id is null !");
        return dataParserTimestampProcessorDAO.getTimestampProcessorById(id);
    }

    public void editTimestampProcessor(DataParserTimestampProcessorDO dataParserTimestampProcessorDO) {
        Assert.notNull(dataParserTimestampProcessorDO,"timestamp processor is null !");
        dataParserTimestampProcessorDAO.editTimestampProcessor(dataParserTimestampProcessorDO);
    }

    public void delTimestampProcessor(String id) {
        Assert.notNull(id, "id is null !");
       dataParserTimestampProcessorDAO.delTimestampProcessor(id);
    }

    public List<DataParserTimestampProcessorDO> listByPipelineIds(List<String> pipeLineIds) {
        List<DataParserTimestampProcessorDO> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(pipeLineIds)) {
            return result;
        }
        return dataParserTimestampProcessorDAO.listByPipelineIds(pipeLineIds);
    }

    public void delTimestampByPipelineIds(List<String> pipeLineIds) {
        if(CollectionUtils.isEmpty(pipeLineIds)){
            return;
        }
        dataParserTimestampProcessorDAO.delByPipeIds(pipeLineIds);
    }
}
