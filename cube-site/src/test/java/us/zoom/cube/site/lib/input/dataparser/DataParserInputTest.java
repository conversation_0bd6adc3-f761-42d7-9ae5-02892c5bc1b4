package us.zoom.cube.site.lib.input.dataparser;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import us.zoom.cube.lib.hub.ForwardProcessorTypeEnum;

/**
 * @Author: luis.zheng
 * @Date: 2021/2/19 11:27
 */
public class DataParserInputTest {
    @Test
    public void test() {
        Assertions.assertTrue(ForwardProcessorTypeEnum.getType("default1") != null, "forwardProcessorType is error!");
    }
}
