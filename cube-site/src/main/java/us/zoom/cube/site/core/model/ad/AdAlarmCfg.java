package us.zoom.cube.site.core.model.ad;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-07-06 13:20
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AdAlarmCfg {
    private String id;
    private String name;
    private String tenantId;
    private String userId;
    private Integer enabled;
    private String metricId;
    private Integer periodInMinutes;
    private Integer timesInPeriod;
    private String description;
    private String subAlarmId;
    private String subAlarmName;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime = new Date();
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date modifyTime = new Date();
    private List<AdAlarmRuleCfg> spcAlarmRuleCfgs;
}
