package us.zoom.cube.site.lib.monitor;

import lombok.Builder;
import lombok.Data;
import us.zoom.cube.sdk.annotation.CubeMonitorLog;
import us.zoom.cube.sdk.annotation.Field;
import us.zoom.cube.sdk.annotation.Tag;


@Data
@Builder
@CubeMonitorLog(measure = "load_dash_tree")
public class LoadDashTreeMonitor {
    @Tag
    private String status;
    @Tag
    private String phase;
    @Field
    private long cost;

    @Field
    private long costLoadItem;

    @Field
    private long costLoadUser;

    @Field
    private long costBuildTree;

    @Field
    private long costLoadUserGroup;

    @Field
    private long costBuildRela;

    @Field
    private String msg;
    @Field
    private String exp;


}
