package us.zoom.cube.site.biz.servicemonitor;

import com.zoom.op.monitor.domain.MetricDefinition;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import us.zoom.cube.site.biz.ServiceMonitorMetricDefExport;
import us.zoom.cube.site.core.MetricsHandler;
import us.zoom.cube.site.core.TenantHandler;
import us.zoom.cube.site.lib.input.migration.MetricsMigrationCheckInput;
import us.zoom.cube.site.lib.input.migration.ServiceMigrationCheckInput;
import us.zoom.cube.site.lib.output.migration.MetricsFieldValueResult;
import us.zoom.cube.site.lib.output.migration.MetricsItemEntry;
import us.zoom.cube.site.lib.output.migration.MetricsTagCheckResult;
import us.zoom.cube.site.lib.output.migration.ServiceMetricCheckResult;
import us.zoom.infra.dao.model.MetricsDO;
import us.zoom.infra.dao.model.TenantDO;

import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;
import java.util.stream.Collectors;

/**
 * @author: Starls Ding
 * @date: 03/08/2025 23:39
 * @desc:
 */
@Slf4j
@Service
public class ServiceMetricsCheckService {

    public static final long ONE_MINUTE_IN_MS = 60 * 1000;

    @Autowired
    private ServiceMonitorMetricDefExport metricDefExport;
    @Autowired
    private TenantHandler tenantHandler;
    @Autowired
    private MetricsHandler metricsHandler;

    @Autowired
    private MetricsTagCompareService metricsTagCompareService;
    @Autowired
    private MetricsFieldValueCheckService metricsFieldValueCheckService;


    public ServiceMetricCheckResult validateServiceMetricData(ServiceMigrationCheckInput input) {
        ServiceMetricCheckResult result = new ServiceMetricCheckResult();

        TimeRange timeRange = formatTimeRangeResult(input, result);
        Sort sort = orderBy(input.getOrderBy());

        List<MetricDefinition> metricDefinitions = metricDefExport.queryOldMetricDef(input.getServiceMonitorService(), sort);
        List<String> serviceMonitorMetricNames = metricDefinitions.stream().map(MetricDefinition::getName).collect(Collectors.toList());

        final String targetService = input.getTargetService();
        TenantDO tenant = tenantHandler.getTenantByName(targetService);
        Assert.notNull(tenant, "Target Service not found: " + targetService);

        List<MetricsDO> targetMetricList = metricsHandler.findByMetricsNameLikeWithTypeAndStatus(StringUtils.EMPTY, 3, 2, StringUtils.EMPTY, input.getTargetDataParser(), List.of(tenant.getId()), null, null, 0, null, 1, 1000);
        List<String> targetMetricNames = targetMetricList.stream().map(MetricsDO::getMetricsName).collect(Collectors.toList());


        //missing target metrics
        result.setTargetMissMetrics(checkTargetMissMetrics(serviceMonitorMetricNames, targetMetricNames));
        //missing source metrics
        result.setSourceMissMetrics(checkSourceMissMetrics(targetMetricNames, serviceMonitorMetricNames));

        Map<String, String> sourceTargetMetricMap = mappingMetrics(serviceMonitorMetricNames, targetMetricNames);


        serviceMonitorMetricNames = page(input.getPageIndex(), input.getPageSize(), serviceMonitorMetricNames, result);

        //customized skipping source metrics
        if (CollectionUtils.isNotEmpty(input.getSkipSourceMetrics())) {
            for (String skipMetric : input.getSkipSourceMetrics()) {
                if (sourceTargetMetricMap.containsKey(skipMetric)) {
                    result.increaseSkipMetricTotal();
                    sourceTargetMetricMap.remove(skipMetric);
                    serviceMonitorMetricNames.remove(skipMetric);
                }
            }
        }
        //customized remapping metrics
        if (MapUtils.isNotEmpty(sourceTargetMetricMap)) {
            input.getManualMatchMetrics().forEach((sourceMetric, targetMetric) -> {
                if (sourceTargetMetricMap.containsKey(sourceMetric)) {
                    result.increaseRemappingMetricTotal();
                    sourceTargetMetricMap.put(sourceMetric, targetMetric);
                }
            });
        }

        //iterate every source metrics, check tag and field values
        int checkTotal = 0;
        int passTotal = 0;
        for (String sourceMetricName : serviceMonitorMetricNames) {
            String targetMetricName = sourceTargetMetricMap.get(sourceMetricName);
            if(null == targetMetricName) {
                continue;
            }

            checkTotal++;
            MetricsMigrationCheckInput checkInput = new MetricsMigrationCheckInput();
            checkInput.setSourceMetrics(sourceMetricName);
            checkInput.setTargetService(targetService);

            checkInput.setTargetMetrics(targetMetricName);
            checkInput.setStartTime(timeRange.getStartTime());
            checkInput.setEndTime(timeRange.getEndTime());

            try {
                checkInput.check();

                boolean singleCheckResult = true;

                if ("tag".equals(input.getCheckTagOrField())) {
                    //check tag
                    MetricsTagCheckResult tagCheckResult = metricsTagCompareService.validateTagsValuesCorrection(checkInput);
                    singleCheckResult = tagCheckResult.isCheckPass();
                    MetricsItemEntry metricsItemEntry = new MetricsItemEntry(sourceMetricName, targetMetricName);
                    metricsItemEntry.setTagCheckResult(tagCheckResult);
                    result.getMetricCheckResult().add(metricsItemEntry);

                } else if ("field".equals(input.getCheckTagOrField())) {
                    //check field
                    MetricsFieldValueResult fieldValueResult = metricsFieldValueCheckService.validateFieldValuesCorrection(checkInput);
                    singleCheckResult = fieldValueResult.isCheckPass();
                    MetricsItemEntry metricsItemEntry = new MetricsItemEntry(sourceMetricName, targetMetricName);
                    metricsItemEntry.setFieldValueResult(fieldValueResult);
                    result.getMetricCheckResult().add(metricsItemEntry);

                } else {
                    //check tag
                    MetricsTagCheckResult tagCheckResult = metricsTagCompareService.validateTagsValuesCorrection(checkInput);
                    result.setCheckPass(result.isCheckPass() & tagCheckResult.isCheckPass());
                    Thread.sleep(3);
                    //check field
                    MetricsFieldValueResult fieldValueResult = metricsFieldValueCheckService.validateFieldValuesCorrection(checkInput);
                    singleCheckResult = result.isCheckPass() & fieldValueResult.isCheckPass();
                    result.getMetricCheckResult().add(new MetricsItemEntry(sourceMetricName, targetMetricName, tagCheckResult, fieldValueResult));
                }

                result.setCheckPass(singleCheckResult);
                if (singleCheckResult) {
                    passTotal++;
                }

            } catch (Exception e) {
                String errMsg = String.format("validateServiceMetricData error for source metric:%s, target metric:%s, error: %s", sourceMetricName, targetMetricName, e.getMessage());
                throw new RuntimeException(errMsg, e);
            }
            try {
                Thread.sleep(5);
            } catch (InterruptedException e) {
            }
        }
        result.setCheckTotal(checkTotal);
        result.setPassTotal(passTotal);
        return result;
    }

    private Sort orderBy(String orderBy) {
        if (StringUtils.equals("lastModifiedTime", orderBy)) {
            return Sort.by(Sort.Direction.DESC, "lastModifiedTime");
        } else {
            return Sort.by(Sort.Direction.ASC, "name");
        }
    }

    //pageStart start from 1
    public List<String> page(int pageStart, int pageSize, List<String> serviceMonitorMetricNames, ServiceMetricCheckResult result) {
        result.setPageIndex(pageStart);
        result.setPageSize(pageSize);

        int size = serviceMonitorMetricNames.size();
        result.setTotal(size);

        if (size == 0 || pageSize <= 0) {
            return serviceMonitorMetricNames;
        }
        result.setPageTotal(size % pageSize == 0 ? size / pageSize : size / pageSize + 1);

        //get the subList from serviceMonitorMetricNames according to pageStart and pageSize, and min pageStart is 1
        int fromIndex = Math.min((pageStart - 1) * pageSize, size);
        int toIndex = Math.min(fromIndex + pageSize, size);
        return serviceMonitorMetricNames.subList(fromIndex, toIndex);
    }


    private Map<String, String> mappingMetrics(List<String> sourceMetricNames, List<String> targetMetricNames) {
        Map<String, String> mapping = new HashMap<>();
        for (String sourceMetricName : sourceMetricNames) {
            String ideaTargetNameFromSource = convertInvalidCharToUnderlineName(sourceMetricName);
            if (!targetMetricNames.contains(ideaTargetNameFromSource)) {
                continue;
            }
            mapping.put(sourceMetricName, ideaTargetNameFromSource);
        }
        return mapping;
    }


    public TimeRange formatTimeRangeResult(ServiceMigrationCheckInput input, ServiceMetricCheckResult result) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
        //comfirm the query time range
        TimeRange timeRange = getValidTimeRange(input.getLastMinute(), input.getStartTime(), input.getEndTime());
        String startTimeStr = sdf.format(timeRange.getStartTime());
        String endTimeStr = sdf.format(timeRange.getEndTime());
        log.info("MetricsTagCompareService compareTagsValues time range is:{}~{}", startTimeStr, endTimeStr);
        result.setStartTime(startTimeStr);
        result.setEndTime(endTimeStr);
        return timeRange;
    }


    private TimeRange getValidTimeRange(Integer lastMinute, Long startTime, Long endTime) {
        if (null == lastMinute) {
            return new TimeRange(startTime, endTime);
        } else {
            long now = System.currentTimeMillis() / ONE_MINUTE_IN_MS * ONE_MINUTE_IN_MS;
            //query from lastMinute to latest 3 minutes
            return new TimeRange(now - lastMinute * ONE_MINUTE_IN_MS, now - ONE_MINUTE_IN_MS * 3);
        }
    }

    private String convertToSmallCase(String value) {
        return value.replaceAll("[^a-zA-Z0-9]", "").toLowerCase();
    }

    // Get the elements from aList that not exist in the bList
    private List<String> diffListContentWithoutCondition(List<String> aList, List<String> bList) {
        return aList.stream().filter(e -> !bList.contains(e)).toList();
    }


    private List<String> checkTargetMissMetrics(List<String> serviceMonitorMetricNames, List<String> targetMetricNames) {
        return serviceMonitorMetricNames.stream().filter(smMetricName -> !targetMetricNames.contains(convertInvalidCharToUnderlineName(smMetricName))).collect(Collectors.toList());
    }

    private List<String> checkSourceMissMetrics(List<String> targetMetricNames, List<String> serviceMonitorMetricNames) {
        List<String> ideaTargetMetricFromSourceList = serviceMonitorMetricNames.stream().map(smMetric -> convertInvalidCharToUnderlineName(smMetric)).collect(Collectors.toList());
        return targetMetricNames.stream().filter(tMetric -> !ideaTargetMetricFromSourceList.contains(tMetric)).toList();
    }


    /**
     * Convert service montior metricDef name to Cube metrics name.
     *
     */
    public String convertInvalidCharToUnderlineName(String name) {
        String newName = name.replaceAll("[^0-9a-zA-Z_]", "_");
        if (newName.startsWith("_")) {
            newName = "trans" + newName;
        }
        return newName;
    }


    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    static class TimeRange {
        long startTime;
        long endTime;
    }
}
