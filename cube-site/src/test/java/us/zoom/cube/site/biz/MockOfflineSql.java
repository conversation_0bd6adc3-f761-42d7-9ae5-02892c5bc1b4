package us.zoom.cube.site.biz;

import java.util.Arrays;
import java.util.List;
import java.util.Random;

public class MockOfflineSql {

    public static void main(String []gs){
        List<String> tenantIds= Arrays.asList("03aef764-bbf9-4191-9a6a-68e3910e0b28","08acd253-2851-4aef-8f14-fb336a014b0a","0c63fb33-eaf9-4100-8ba9-7915c1d2c437","0d626bf4-de01-499e-9b9c-3f0b882d4e24","1","158f422a-3252-4b5a-9794-9468412a551c","1a4cd33a-3046-4f27-b5f5-29b1e66c945e","1f421962-583f-4a84-bdee-cb88382fd974","1f8cacb9-8769-481f-8ad8-443f18799fa2","21b57e7d-4e13-40ba-b666-f88719878a18","23187afa-f342-48f8-bde5-c5d665b287a4","241ac54f-ae22-4148-9fb5-7c8234fed5e8","241b3c55-d6a5-4687-a8c2-9d3d6e098bea","29cf6972-1f7a-4e4f-ba26-4719208a82f0","315da149-87aa-46db-8864-95fe617e3349","390b877b-b882-4874-9e73-081fcbce219d","3aac31bc-52a2-4863-b2a4-98371b0c72cb","3f55881a-2cd8-419c-a008-fa1e346a5fdd","47c326b9-8c36-4bcf-919b-c79dcc81a6c7","4c44814c-1acc-4753-8553-3e2885ca7e0e","4d6ecb09-1e4a-4533-a499-7f5477f723ee","50318523-3f2a-449a-8a03-af477d7acb8e","51a743b0-5858-4e84-aec0-1fbbfd52fa22","548a343c-910c-4024-bc9a-39c10a815249","56f30186-aa5a-47e2-be74-2b16b2724413","5e846775-98e3-4616-ab2a-a0bbae8cb7ea","60965f11-2907-4643-aae0-1f09c41697c2","6c7f15b1-ccd0-4ab0-80c3-ab49511f3c16","70059caa-644b-4a72-ae02-4679b5223a30","71988810-9b65-42b5-acc8-985d94e217fc","7780ef1f-0bf3-454f-94d7-0ba7304f11de","7ac6fcc3-8e4a-4858-96fa-e79fe7e54745","7ea62141-ab79-4c79-b1d4-b2175b842111","82081acb-a720-486c-9028-2e802a84416f","ad33d0ab-338c-4ed0-b539-fcb2867b8955","b509c36e-b8f4-48c1-8da2-eac99f7f39a8","b70fc440-7a3a-427b-9894-b21e789d3563","b7763eb1-fc98-43c8-b9d4-ab068f4aaddf","b8a83c60-c8a2-4d0b-a198-ccf03e6b12db","ba134369-af83-45cf-a6e7-99e34a2ed110","bde6e725-a93c-4ae1-9547-538a7cd352fa","c2231476-492a-4730-a940-568d54c03d4f","c32198f9-7041-4763-a929-42a448fa7992","c42b8cb4-39c6-4b60-ba13-08e40ef05d11","ce1c1f88-8709-4a44-9fd6-221cc741d4a4","cef31c02-2a49-44e1-99f4-1416d68a4584","d7563b25-63d5-4187-9abe-a071fb0557e2","d8021646-7227-4934-896c-d8a603dcc7f0","d88957b0-9120-449a-9c09-3563d382a3ae","ee973c87-c278-4706-a7ef-0af082f73ccc","f0be817d-abf2-4910-86b4-45f96a406b97","f16d3443-ab59-4ece-968a-976754c64053","f4cde32f-1579-4fe0-999b-69fc937e4f45","f5020d1a-799e-4f7d-afa8-ff46322c647e","f53a9ce6-511e-47ef-ab84-ed123d866989","f5bf3430-401e-4daf-9826-cf1e9110f403","fcf4eff6-e51f-4fcd-af3b-b62c720d1d09","fee37827-610d-4713-88cf-6fd16e7f5f4d");

        int i=300;
        for(String tenant:tenantIds){
            for(int j=0;j<4;j++){
                i++;

                String server="insert into server(id, ip, host, cluster_id, gmt_create, gmt_modify, tenant_id, agent_key, service_id, jmx_port, cloud_type, up_status_time, type, channel_type, use_zip, region_id, unit_tag, version, status)" +
                        "values('17c02418-3ca4-4958-8319-6a688f62326f"+i+"', '"+Math.abs(new Random().nextInt()%100)+"."+Math.abs(new Random().nextInt()%100)+".62."+Math.abs(new Random().nextInt()%100) +"', 'nws2-joey"+i+"', 'dev', '2021-11-17 05:34:14', '2021-12-17 07:40:54', '"+tenant+"', '', '', '', '2', '2021-12-17 07:40:49', 'agent', 'normal', '2', 'va', '', '', '1');";
                System.out.println(server);
            }

        }

    }
}
