package us.zoom.cube.site.lib.output.codeperformance;

import lombok.Data;

/**
 * @author: tobey.zhu
 * @date: /2023/07/20
 * @description:
 */
@Data
public class FlameGraphListOut {
    private Long ts;
    private String host;
    private String regionId;
    private String clusterId;
    private String zoneName;
    private String sampleId;
    private Double hostCpu;
    private Double processCpu;
    private Long samplePeriod;

    public FlameGraphListOut(Long ts, String host, String regionId, String clusterId, String zoneName, String sampleId, Double hostCpu, Double processCpu,
                             Long samplePeriod) {
        this.ts = ts;
        this.host = host;
        this.regionId = regionId;
        this.clusterId = clusterId;
        this.zoneName = zoneName;
        this.sampleId = sampleId;
        this.hostCpu = hostCpu;
        this.processCpu = processCpu;
        this.samplePeriod = samplePeriod;
    }
}
