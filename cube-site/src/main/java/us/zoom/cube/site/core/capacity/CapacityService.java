package us.zoom.cube.site.core.capacity;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.influxdb.dto.QueryResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.lib.integrations.Metrics;
import us.zoom.cube.lib.integrations.MetricsField;
import us.zoom.cube.lib.integrations.MetricsFieldTypeEnum;
import us.zoom.cube.site.core.TenantHandler;
import us.zoom.cube.site.core.config.ClickhouseHandlerFactory;
import us.zoom.cube.site.core.model.common.CapacityModel;
import us.zoom.cube.site.lib.CapacityClacCfg;
import us.zoom.cube.site.lib.CapacityServiceCfg;
import us.zoom.infra.clickhouse.ClickhouseSqlUtil;
import us.zoom.infra.clickhouse.ClickhouseWriter;
import us.zoom.infra.dao.service.DistributedLockDAO;
import us.zoom.infra.influx.service.InfluxService;
import us.zoom.infra.utils.DateUtils;
import us.zoom.cube.lib.utils.JsonUtils;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022-05-17 4:12
 */

@Component
@Slf4j
public class CapacityService {

    @Autowired
    DistributedLockDAO distributedLockDAO;
    @Autowired
    TenantHandler tenantHandler;
    @Autowired
    private ClickhouseHandlerFactory clickhouseHandlerFactory;
    @Autowired
    private InfluxService influxService;

    public static String KAFKA_CREATE_TIME ="KAFKA_CREATE_TIME";

    /**
     * preProcess
     * @param resMaxMetrics
     * @param resNumMetrics
     * @return
     */
    public Map<String, List<Metrics>> preProcess(List<Metrics> resMaxMetrics, List<Metrics> resNumMetrics,CapacityModel capacityModel) {
        if (CollectionUtils.isEmpty(resMaxMetrics) || CollectionUtils.isEmpty(resNumMetrics)) {
            return new HashMap<>();
        }
        Map<String, List<Metrics>> metricsMListMap = resMaxMetrics.stream().collect(Collectors.groupingBy(metrics -> String.valueOf(metrics.getTags().values())));
        Map<String, List<Metrics>> metricsNListMap = resNumMetrics.stream().collect(Collectors.groupingBy(metrics -> String.valueOf(metrics.getTags().values())));
        for (String tag : metricsMListMap.keySet()) {
            for (String tag2 : metricsNListMap.keySet()) {
                if (StringUtils.equals(tag, tag2)) {
                    List<MetricsField> metricsFieldList = metricsNListMap.get(tag2).get(0).getFields();
                    if (!CollectionUtils.isEmpty(metricsNListMap.get(tag2))) {
                        if (null != metricsNListMap.get(tag2).get(0)) {
                            if (!CollectionUtils.isEmpty(metricsFieldList)) {
                                for (MetricsField metricsFieldN : metricsFieldList) {
                                    if (StringUtils.equals(metricsFieldN.getFieldName(), capacityModel.getServerCount())) {
                                        metricsMListMap.get(tag).get(0).getFields().add(metricsFieldN);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        return metricsMListMap;
    }

    /**
     * process
     * @param resMetrics
     * @param time
     * @param tenantName
     * @return
     */
    public List<Metrics> process(Map<String, List<Metrics>> resMetrics, long time, String tenantName, CapacityServiceCfg capacityServiceCfg,CapacityClacCfg capacityClacCfg,CapacityModel capacityModel) {
        List<Metrics> resList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(resMetrics)) {
            for (String tag : resMetrics.keySet()) {
                try {
                    Metrics metrics = resMetrics.get(tag).get(0);
                    //eg:27th 00:00 calc 27 before data,it is belong 26th
                    metrics.setTs(DateUtils.addMinute(time,-10));
                    Map tagMap = metrics.getTags();
                    tagMap.put(capacityModel.getServiceName(), tenantName);
                    metrics.setTags(tagMap);
                    Map map = new HashMap();
                    for (MetricsField metricsField : metrics.getFields()) {
                        if(StringUtils.equals(metricsField.getFieldName(),capacityModel.getServerCount())){
                            metricsField.setFieldValue(Double.valueOf(metricsField.getFieldValue().toString()));
                        }
                        if (StringUtils.equals(metricsField.getFieldName(),capacityModel.getMax_us())){
                            double max_us = (double)metricsField.getFieldValue();
                            metricsField.setFieldValue(doublePrase(max_us));
                        }
                        map.put(metricsField.getFieldName(), metricsField.getFieldValue());
                    }
                    double real = (double) map.get(capacityModel.getMax_us());
                    double totalNum = (double)map.get(capacityModel.getServerCount());

                    if(null != capacityServiceCfg){
                        MetricsField metricsField1A = new MetricsField();
                        metricsField1A.setFieldValue(getNeedReduce(real, totalNum,capacityServiceCfg.getLower(),capacityServiceCfg.getUpper(),capacityServiceCfg,capacityClacCfg.getOneA() ));
                        metricsField1A.setFieldName(capacityModel.getOneAReduceCount());
                        metricsField1A.setFieldType(MetricsFieldTypeEnum.number);

                        MetricsField metricsField2A = new MetricsField();
                        metricsField2A.setFieldValue(getNeedReduce(real, totalNum, capacityServiceCfg.getLower(),capacityServiceCfg.getUpper(),capacityServiceCfg,capacityClacCfg.getTwoA()));
                        metricsField2A.setFieldName(capacityModel.getTwoAReduceCount());
                        metricsField2A.setFieldType(MetricsFieldTypeEnum.number);

                        MetricsField metricsField3A = new MetricsField();
                        metricsField3A.setFieldValue(getNeedReduce(real, totalNum, capacityServiceCfg.getLower(),capacityServiceCfg.getUpper(),capacityServiceCfg,capacityClacCfg.getThreeA()));
                        metricsField3A.setFieldName(capacityModel.getThreeAReduceCount());
                        metricsField3A.setFieldType(MetricsFieldTypeEnum.number);

                        metrics.getFields().add(metricsField1A);
                        metrics.getFields().add(metricsField2A);
                        metrics.getFields().add(metricsField3A);
                        resList.add(metrics);
                    }
                }catch (Exception e){
                    log.error("CapacityService calc process!", e);
                }
            }
        }
        return resList;
    }


    /**
     * addDataToClickhouse
     * @param metrics
     */
    public void addDataToClickhouse(Metrics metrics,CapacityClacCfg capacityClacCfg) {
        Map<String, Object> columns = new HashMap<>();

        for (Map.Entry<String, String> stringStringEntry : metrics.getTags().entrySet()) {
            if (stringStringEntry.getValue() != null) {
                columns.put(stringStringEntry.getKey(), stringStringEntry.getValue());
            }
        }
        for (MetricsField e : metrics.getFields()) {
            if (e.getFieldValue() != null) {
                columns.put(e.getFieldName(), e.getFieldValue());
            }
        }
        if(!columns.containsKey(KAFKA_CREATE_TIME)) {
            columns.put(KAFKA_CREATE_TIME, metrics.getTs());
        }
        try {
            ClickhouseWriter writer=clickhouseHandlerFactory.getClickhouseWriter();
            long recordMemorySize = 1024 * 2L;
            writer.write(ClickhouseSqlUtil.toClickhouseName(capacityClacCfg.getDataBaseName()),
                    ClickhouseSqlUtil.toClickhouseName(capacityClacCfg.getTableName()),
                    columns, new Timestamp(metrics.getTs()), recordMemorySize, false);
            log.info("[CapacityService#addDataToClickhouse databaseName:{},tableName:{}, data:{},time:{}",capacityClacCfg.getDataBaseName(),capacityClacCfg.getTableName(), JsonUtils.toJsonStringIgnoreExp(metrics),new Timestamp(metrics.getTs()));
        } catch (Exception e) {
            log.error("addMetricsToClickhouse error! AggregationResult: {}, error is: ", JsonUtils.toJsonStringIgnoreExp(metrics), e);
        }

    }

    /**
     * addMetricsToInfluxDB
     * @param metrics
     */
    public void addMetricsToInfluxDB(Metrics metrics,CapacityClacCfg capacityClacCfg) {
        try {
            influxService.addInfluxData(capacityClacCfg.getDataBaseName(), capacityClacCfg.getTableName(), metrics);

        } catch (Exception e) {
            log.error("addMetricsToInfluxDB error! AggregationResult: {}, error is: ", JsonUtils.toJsonStringIgnoreExp(metrics), e);
        }
    }

    /**
     * checkQueryResult
     * @param queryResult
     * @return
     */
    public boolean checkQueryResult(QueryResult queryResult){
        for (QueryResult.Result result : queryResult.getResults()) {
            if (CollectionUtils.isEmpty(result.getSeries().get(0).getTags())) {
                return false;
            }}
        return true;
    }

    /**
     * getNeedReduce
     * @param real
     * @param totalNum
     * @param lowwer
     * @return
     */
    private double getNeedReduce(double real, double totalNum, double lowwer, double upper, CapacityServiceCfg capacityServiceCfg, double activity) {
        double res = 0.0;
        if (lowwer == 0.0) {
            return res;
        }
        if (upper == 0.0) {
            return res;
        }
        double normal = capacityServiceCfg.getNormal()/activity;
        if (real < capacityServiceCfg.getLower() || real > capacityServiceCfg.getUpper()) {
            //need add + , need redduce -
            res = -(totalNum - ((real * totalNum) / normal));
        }
        if(res<0 && totalNum < 2){
            res = 0.0;
            return res;
        }
        return doublePrase(res);
    }

    /**
     * doublePrase
     * @param num
     * @return
     */
    private double doublePrase(Double num) {
        BigDecimal b = new BigDecimal(num);
        return b.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
    }



}
