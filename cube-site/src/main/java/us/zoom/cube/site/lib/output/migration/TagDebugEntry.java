package us.zoom.cube.site.lib.output.migration;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: <PERSON><PERSON> Ding
 * @date: 03/08/2025 12:59
 * @desc:
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TagDebugEntry {

    private String sourceTag;

    private List<String> sourceValues;

    private String targetTag;

    private List<String> targetValue;

}
