package us.zoom.cube.site.lib.scheduler.input;

import lombok.Data;
import us.zoom.cube.scheduler.lib.enums.ExecutorType;
import us.zoom.cube.site.lib.BasePara;

/**
 * @author: <PERSON><PERSON> Ding
 * @date: 2025/4/16 09:03
 * @desc: DO for
 */
@Data
public class ExecutorInfoInput extends BasePara {

    private String id;
    private String name;
    private boolean enabled;
    private String unit;
    private ExecutorType executorType;
    private String cubeService;
    private boolean domainModeEnabled;
    private String domainModeUrl;

}
