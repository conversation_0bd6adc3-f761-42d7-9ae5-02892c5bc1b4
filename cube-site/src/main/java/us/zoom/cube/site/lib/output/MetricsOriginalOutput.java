package us.zoom.cube.site.lib.output;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.util.ArrayList;
import java.util.List;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class MetricsOriginalOutput {
    String id;

    String metricsId;

    String filterCondition;

    String originalResultFilter;

    List<OriginalCustomFieldRuleOutput> originalCustomFieldRuleOutputs = new ArrayList<>();
}
