package us.zoom.cube.site.core.clickhouse;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.internal.util.MockUtil;
import org.mockito.junit.jupiter.MockitoExtension;
import us.zoom.cube.site.biz.clickhouse.TableCreator;
import us.zoom.infra.clickhouse.ClickhouseHandler;
import us.zoom.infra.clickhouse.ClickhouseSchema;
import us.zoom.infra.enums.FieldTypeEnum;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;

/**
 * <AUTHOR>
 * @date 2024/12/03 9:32 AM
 */
@ExtendWith(MockitoExtension.class)
public class AddColumTest {


    @Mock
    public ClickhouseHandler handler;

    @BeforeEach
    public void initMocks() {
        MockitoAnnotations.initMocks(this);


    }

    @Test
    //has prefix, will not alter
    public void testNormalDistributedTable() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        Map<String, ClickhouseSchema> localTableSchemas =new HashMap<>();
        ClickhouseSchema c=new ClickhouseSchema();
        LinkedHashMap<String, FieldTypeEnum> localColDef=new LinkedHashMap<>();
        localColDef.put("aaa.bbb",FieldTypeEnum.bool);
        c.setColDef(localColDef);
        localTableSchemas.put("ip",c);
        Map<String, FieldTypeEnum> colDef=new HashMap<>();
        colDef.put("aaa",FieldTypeEnum.bool);
        TableCreator tableCreator = new TableCreator("db", "table", colDef, null, false);
        Method privateMethod = TableCreator.class.getDeclaredMethod("addColumn", Map.class);
        privateMethod.setAccessible(true);
        privateMethod.invoke(tableCreator,localTableSchemas);

    }

    @Test
    //has no prefix, will alter
    public void testNormalDistributedTableCase2() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        Mockito.doThrow(new RuntimeException()).when(handler).addColumnOnInstance(any(),any(),any(),any(),any());
        Map<String, ClickhouseSchema> localTableSchemas =new HashMap<>();
        ClickhouseSchema c=new ClickhouseSchema();
        LinkedHashMap<String, FieldTypeEnum> localColDef=new LinkedHashMap<>();
        localColDef.put("aaa.bbb",FieldTypeEnum.bool);
        c.setColDef(localColDef);
        localTableSchemas.put("ip",c);
        Map<String, FieldTypeEnum> colDef=new HashMap<>();
        colDef.put("aaaa",FieldTypeEnum.bool);
        TableCreator tableCreator = new TableCreator("db", "table", colDef, handler, false);
        Method privateMethod = TableCreator.class.getDeclaredMethod("addColumn", Map.class);
        privateMethod.setAccessible(true);
        try {
            privateMethod.invoke(tableCreator, localTableSchemas);
            Assertions.fail("Expected exception was not thrown");
        }catch (Exception e){
            //expected
        }
    }

}
