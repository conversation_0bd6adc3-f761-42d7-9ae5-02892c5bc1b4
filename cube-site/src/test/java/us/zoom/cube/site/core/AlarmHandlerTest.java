package us.zoom.cube.site.core;

import com.google.common.collect.Lists;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.site.CubeSiteApplicationTests;
import us.zoom.cube.site.infra.utils.IdUtils;
import us.zoom.infra.dao.model.*;
import us.zoom.infra.dao.service.AlarmImRelationDAO;
import us.zoom.infra.model.alarm.AlarmLevel;
import us.zoom.cube.lib.utils.JsonUtils;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2020/7/30 3:44 PM
 */
public class AlarmHandlerTest extends CubeSiteApplicationTests {

    private static final String alarmName = "testAlarmName";
    private static final String tenantId = "web1";

    @Autowired
    private AlarmHandler alarmHandler;

    @Autowired
    private InstanceMessagingHandler instanceMessagingHandler;

    @Autowired
    private AlarmImRelationDAO alarmImRelationDAO;

    @Test
    public void listAlarmsByMetricsIdNotEmpty() {
        String metricsId = "ecb9f353-31f0-45e0-9909-1ca7c130b3bf";
        List<AlarmDO> alarmDOList = alarmHandler.listAlarmsByMetricsId(metricsId);
        System.out.println("alarmDOList = " + JsonUtils.toJsonString(alarmDOList));
        Assertions.assertEquals(3, alarmDOList.size());
    }

    @Test
    public void listAlarmsByMetricsIdEmpty() {
        String metricsId = "testEmptyId";
        List<AlarmDO> alarmDOList = alarmHandler.listAlarmsByMetricsId(metricsId);
        System.out.println("alarmDOList = " + JsonUtils.toJsonString(alarmDOList));
        Assertions.assertEquals(0, alarmDOList.size());
    }

    @Test
    public void correlateImChannel() throws Exception {
//        String tenantId = "WEB1";
//        List<AlarmDO> alarms = alarmHandler.findByAlarmNameLike("", tenantId, 1, 20);
//        List<InstanceMessagingChannelDO> channelDOList = instanceMessagingHandler.listAllForTenant(tenantId, "");
//        String channelId = channelDOList.get(0).getId();
//        for (AlarmDO alarmDO : alarms) {
//            AlarmImRelationDO alarmImRelationDO = new AlarmImRelationDO();
//            alarmImRelationDO.setId(IdUtils.generateId());
//            alarmImRelationDO.setAlarmId(alarmDO.getId());
//            alarmImRelationDO.setInstanceMessagingChannelId(channelId);
//            alarmImRelationDO.setCreateTime(new Date());
//            alarmImRelationDO.setModifyTime(new Date());
//            alarmImRelationDAO.add(alarmImRelationDO);
//        }

    }

    private AlarmDO buildAlarmDO() {
        AlarmDO alarmDO = new AlarmDO();
        alarmDO.setId(IdUtils.generateId());
        alarmDO.setName(alarmName);
        alarmDO.setTitle("testAlarmTitle");
        alarmDO.setAlarmContent("testAlarmContent");
        alarmDO.setAlarmCycle("[\"Monday\",\"Tuesday\",\"Wednesday\",\"Thursday\",\"Friday\"]");
        alarmDO.setFromTime("00:00:00");
        alarmDO.setToTime("23:59:59");
        alarmDO.setTenantId(tenantId);
        alarmDO.setCreateTime(new Date());
        alarmDO.setModifyTime(new Date());
        return alarmDO;
    }

    private void buildUpdateAlarmDO(AlarmDO alarmDO) {
        alarmDO.setName("testAlarmName");
        alarmDO.setTitle("testAlarmTitle-update");
        alarmDO.setAlarmContent("testAlarmContent-update");
        alarmDO.setAlarmCycle("[\"Monday\",\"Tuesday\",\"Wednesday\",\"Thursday\",\"Friday\",\"Saturday\"]");
        alarmDO.setFromTime("00:22:00");
        alarmDO.setToTime("23:22:59");
        alarmDO.setTenantId("web1-update");
        alarmDO.setCreateTime(new Date());
        alarmDO.setModifyTime(new Date());
    }

    private MetricsAlarmDO buildMetricsAlarmDO(AlarmDO alarmDO) {
        MetricsAlarmDO metricsAlarmDO = new MetricsAlarmDO();
        metricsAlarmDO.setId(IdUtils.generateId());
        metricsAlarmDO.setMetricsId(IdUtils.generateId());
        metricsAlarmDO.setAlarmId(alarmDO.getId());
        metricsAlarmDO.setCreateTime(new Date());
        metricsAlarmDO.setModifyTime(new Date());

        return metricsAlarmDO;
    }

    private void buildUpdateMetricsAlarmDO(MetricsAlarmDO metricsAlarmDO) {
        metricsAlarmDO.setMetricsId(IdUtils.generateId());
    }

    private List<AlarmRuleDO> buildAlarmRuleDOList() {
        List<AlarmRuleDO> alarmRuleDOList = Lists.newArrayList();
        AlarmRuleDO alarmRuleDO = new AlarmRuleDO();
        alarmRuleDO.setId(IdUtils.generateId());
        alarmRuleDO.setLastCount(3);
        alarmRuleDO.setAlarmType(0);
        alarmRuleDO.setAlarmLevel(AlarmLevel.FATAL.getLevel());
        alarmRuleDO.setAlarmRuleContent("[{\"type\":3,\"tag\":\"host_name\",\"field\":null,\"fieldType\":null,\"operator\":\"==\",\"cpmValue\":\"dev_web001\",\"expression\":null},{\"type\":0,\"tag\":null,\"field\":\"cost.avg\",\"fieldType\":\"number\",\"operator\":\">=\",\"cpmValue\":\"200\",\"expression\":null},{\"type\":1,\"tag\":null,\"field\":\"cost.avg\",\"fieldType\":\"number\",\"operator\":\"crIncrease\",\"cpmValue\":\"0.2\",\"expression\":null},{\"type\":1,\"tag\":null,\"field\":\"cost.avg\",\"fieldType\":\"number\",\"operator\":\"crDecrease\",\"cpmValue\":\"0.2\",\"expression\":null},{\"type\":4,\"tag\":null,\"field\":null,\"fieldType\":null,\"operator\":null,\"cpmValue\":null,\"expression\":\"dataExist('web_agg_joinmeeting','ip',122)\"}]\n");
        alarmRuleDO.setCreateTime(new Date());
        alarmRuleDO.setModifyTime(new Date());
        alarmRuleDOList.add(alarmRuleDO);
        return alarmRuleDOList;
    }

    private List<AlarmRuleDO> buildUpdateAlarmRuleDOList() {
        List<AlarmRuleDO> alarmRuleDOList = Lists.newArrayList();
        AlarmRuleDO alarmRuleDO = new AlarmRuleDO();
        alarmRuleDO.setId(IdUtils.generateId());
        alarmRuleDO.setAlarmType(1);
        alarmRuleDO.setAlarmLevel(AlarmLevel.ERROR.getLevel());
        alarmRuleDO.setAlarmRuleContent("[{\"type\":3,\"tag\":\"host_name\",\"field\":null,\"fieldType\":null,\"operator\":\"==\",\"cpmValue\":\"dev_web002\",\"expression\":null},{\"type\":0,\"tag\":null,\"field\":\"cost.avg\",\"fieldType\":\"number\",\"operator\":\">=\",\"cpmValue\":\"200\",\"expression\":null},{\"type\":1,\"tag\":null,\"field\":\"cost.avg\",\"fieldType\":\"number\",\"operator\":\"crIncrease\",\"cpmValue\":\"0.2\",\"expression\":null},{\"type\":1,\"tag\":null,\"field\":\"cost.avg\",\"fieldType\":\"number\",\"operator\":\"crDecrease\",\"cpmValue\":\"0.2\",\"expression\":null},{\"type\":4,\"tag\":null,\"field\":null,\"fieldType\":null,\"operator\":null,\"cpmValue\":null,\"expression\":\"dataExist('web_agg_joinmeeting','ip',122)\"}]\n");
        alarmRuleDO.setCreateTime(new Date());
        alarmRuleDO.setModifyTime(new Date());
        alarmRuleDOList.add(alarmRuleDO);
        return alarmRuleDOList;
    }

    private List<AlarmRuleRelationDO> buildAlarmRuleRelationDOList(AlarmDO alarmDO, List<AlarmRuleDO> alarmRuleDOList) {
        List<AlarmRuleRelationDO> alarmRuleRelationDOList = Lists.newArrayList();
        alarmRuleDOList.forEach(alarmRuleDO -> {
            AlarmRuleRelationDO alarmRuleRelationDO = new AlarmRuleRelationDO();
            alarmRuleRelationDO.setId(IdUtils.generateId());
            alarmRuleRelationDO.setAlarmId(alarmDO.getId());
            alarmRuleRelationDO.setAlarmRuleId(alarmRuleDO.getId());
            alarmRuleRelationDO.setCreateTime(new Date());
            alarmRuleRelationDO.setModifyTime(new Date());
            alarmRuleRelationDOList.add(alarmRuleRelationDO);
        });

        return alarmRuleRelationDOList;
    }

    private AlarmImRelationDO buildAlarmImRelationDO(AlarmDO alarmDO) {
        AlarmImRelationDO relationDO = new AlarmImRelationDO();
        relationDO.setId(IdUtils.generateId());
        relationDO.setAlarmId(alarmDO.getId());
        relationDO.setInstanceMessagingChannelId(IdUtils.generateId());
        relationDO.setCreateTime(new Date());
        relationDO.setModifyTime(new Date());
        return relationDO;
    }
}
