package us.zoom.cube.site.biz;


import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.mock.web.MockHttpSession;
import us.zoom.cube.lib.common.RoleTypeEnum;
import us.zoom.cube.site.CubeSummary;
import us.zoom.cube.site.api.web.StatusEnum;
import us.zoom.cube.site.lib.ResponseObject;
import us.zoom.cube.site.lib.input.UserInput;
import us.zoom.cube.site.lib.query.NameQuery;
import us.zoom.cube.site.lib.query.PageQuery;
import us.zoom.infra.dao.model.RoleTenantDO;
import us.zoom.infra.dao.model.TenantUserRelaDO;
import us.zoom.infra.dao.model.UserDO;
import us.zoom.infra.dao.service.TenantUserRelaDAO;
import us.zoom.infra.dao.service.UserDAO;
import us.zoom.cube.lib.utils.JsonUtils;

import java.util.List;

import static org.mockito.Mockito.when;

public class UserServiceTest extends BaseTest {
    @Autowired
    UserService userService;

//    @Autowired
//    TenantUserRelationDAO tenantUserRelaDAO;

    @Autowired
    private TenantUserRelaDAO tenantUserRelaDAO;


    @Autowired
    UserDAO userDAO;

    @Test
    public void searchAlarmMentionUser() {
        try {
            NameQuery nameQuery = new NameQuery();
            nameQuery.setName("li");
            PageQuery<NameQuery> pageQuery = new PageQuery();
            pageQuery.setQueryPara(nameQuery);
            pageQuery.setPageIndex(1);
            pageQuery.setPageSize(10);
            ResponseObject result = userService.searchAlarmMentionUser(pageQuery);
            System.out.println(JsonUtils.toJsonString(result));

            nameQuery.setName("<EMAIL>");
            result = userService.searchAlarmMentionUser(pageQuery);
            System.out.println(JsonUtils.toJsonString(result));
        } catch (Exception e) {
            System.out.println("error:" + e.getMessage());
        }
    }


    //@Test
    public void testAll() throws Exception {
        CubeSummary summary = super.buildSummary(USER);
        MockHttpSession httpSession = new MockHttpSession();
        MockHttpServletResponse response = new MockHttpServletResponse();
        ResponseObject responseObject = null;
//        ResponseObject responseObject= userService.login(DataMock.mockUserQuery(),httpSession, response);
        Assertions.assertTrue(StatusEnum.SUCCESS.getStatus().equals(responseObject.getStatus()));
//        responseObject=  userService.refreshToken(summary.getUserId(), request.getSession());
        Assertions.assertTrue(StatusEnum.SUCCESS.getStatus().equals(responseObject.getStatus()));
//        boolean success=  userService.checkTokenWithReturnUser(summary.getUserId(),((LoginUser)responseObject.getData()).getAccessToken());
//        Assertions.assertTrue(success);
        //responseObject= userService.logout(DataMock.mockUserQuery(),httpSession);
        Assertions.assertTrue(StatusEnum.SUCCESS.getStatus().equals(responseObject.getStatus()));
//        userService.login(DataMock.mockUserQuery(),httpSession, response);
//        UserDO userId= userService.getUserIdBySession(httpSession.getId());
//        Assertions.assertNotNull(userId);
    }

    //@Test
    public void editUserTest() {
        UserInput userInput = new UserInput();
        userInput.setId("fca4cd6a-a5b3-4c78-ae2f-591ea3516d4a");
        userInput.setName("dsfdsf");

        RoleTenantDO tli = createRoleTenant();
        userInput.setRoleTenantList(Lists.newArrayList(tli));
        userInput.setResetPass("false");
        userService.editUser(userInput);
    }


    @Test
    public void editUserWithTenantAdminWithEmptyTenant(){
        // 8c752f39-b011-42d0-ad19-3124178dd6bf;<EMAIL>;
        String userId = "8c752f39-b011-42d0-ad19-3124178dd6bf";
        resetTenantAdminToInitStatus(userId);

        UserInput userInput = Mockito.mock(UserInput.class);
        when(userInput.getId()).thenReturn("821ea870-c04a-4097-93f5-ecd2e17137e7");
        RoleTenantDO tli = createRoleTenant();
        when(userInput.getRoleTenantList()).thenReturn(Lists.newArrayList(tli));
        userService.editUser(userInput);
        UserDO userDO = userDAO.getUserById(userId);
        Assertions.assertEquals( StringUtils.EMPTY, userDO.getCurrentTenant());
        List<TenantUserRelaDO> tenantUserRelationDOList = tenantUserRelaDAO.findTenantUserRelationByUseId(userId);
        Assertions.assertEquals(Lists.emptyList(), tenantUserRelationDOList);

        resetTenantAdminToInitStatus(userId);
    }

    private static RoleTenantDO createRoleTenant() {
        RoleTenantDO tli = new RoleTenantDO();
        tli.setRole("service_owner");
        tli.setTenantList(Lists.list("53ba58c1-46aa-485b-8bea-3df45619c196"));
        return tli;
    }

    @Test
    public void editUserWithTenantAdminWithOneTenant(){
        // 8c752f39-b011-42d0-ad19-3124178dd6bf;<EMAIL>;
        String userId = "8c752f39-b011-42d0-ad19-3124178dd6bf";
        resetTenantAdminToInitStatus(userId);

        UserInput userInput = Mockito.mock(UserInput.class);
        when(userInput.getId()).thenReturn("821ea870-c04a-4097-93f5-ecd2e17137e7");
        RoleTenantDO tli = createRoleTenant();
        when(userInput.getRoleTenantList()).thenReturn(Lists.newArrayList(tli));
        userService.editUser(userInput);
        UserDO userDO = userDAO.getUserById(userId);
        Assertions.assertEquals("a53c3073-eb84-458b-952d-a18a02e75d19",userDO.getCurrentTenant());
        List<TenantUserRelaDO> tenantUserRelationDOList = tenantUserRelaDAO.findTenantUserRelationByUseId(userId);
        Assertions.assertEquals( 1,tenantUserRelationDOList.size());
        Assertions.assertEquals("a53c3073-eb84-458b-952d-a18a02e75d19",tenantUserRelationDOList.get(0).getTenantId());

        resetTenantAdminToInitStatus(userId);
    }

    @Test
    public void editUserWithTenantAdminWithTwoTenant(){
        // 8c752f39-b011-42d0-ad19-3124178dd6bf;<EMAIL>;
        String userId = "8c752f39-b011-42d0-ad19-3124178dd6bf";
        resetTenantAdminToInitStatus(userId);

        UserInput userInput = Mockito.mock(UserInput.class);
        when(userInput.getId()).thenReturn("821ea870-c04a-4097-93f5-ecd2e17137e7");
        RoleTenantDO tli = createRoleTenant();
        when(userInput.getRoleTenantList()).thenReturn(Lists.newArrayList(tli));
        userService.editUser(userInput);
        UserDO userDO = userDAO.getUserById(userId);
        Assertions.assertEquals("a53c3073-eb84-458b-952d-a18a02e75d19",userDO.getCurrentTenant());
        List<TenantUserRelaDO> tenantUserRelationDOList = tenantUserRelaDAO.findTenantUserRelationByUseId(userId);
        Assertions.assertEquals( 2,tenantUserRelationDOList.size());
        Assertions.assertEquals(true, tenantUserRelationDOList.stream().anyMatch(e -> e.getTenantId().equals("74c6bbb7-72bc-4b8b-b24e-67873d8be2b4")));

        resetTenantAdminToInitStatus(userId);
    }

    // reset to init status for this user
    private void resetTenantAdminToInitStatus(String userId){
        tenantUserRelaDAO.delTenantUserRelationByUserIdAndRole(userId, RoleTypeEnum.tenantAdmin.name());
        tenantUserRelaDAO.delTenantUserRelationByUserIdAndRole(userId, RoleTypeEnum.admin.name());
        userDAO.setTenant(userId,"");
    }




}
