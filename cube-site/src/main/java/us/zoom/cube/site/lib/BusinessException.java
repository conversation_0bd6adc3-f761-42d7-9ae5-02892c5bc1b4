package us.zoom.cube.site.lib;

public class BusinessException extends RuntimeException {
    private static final long serialVersionUID = 1L;

    private String errorcode;

    private String message;

    public BusinessException() {
        super();
    }

    public BusinessException(String message) {
        super();
        this.message = message;
    }

    public BusinessException(String errorcode, String message) {
        super(message);
        this.errorcode = errorcode;
        this.message = message;
    }

    @Override
    public String getMessage() {
        return message;
    }
}
