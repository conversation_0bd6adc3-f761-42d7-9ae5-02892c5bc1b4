package us.zoom.cube.site.biz.alarm.silence;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.lib.utils.JsonUtils;
import us.zoom.cube.site.api.intercept.AuthInterceptor;
import us.zoom.cube.site.biz.AuthService;
import us.zoom.cube.site.biz.alarm.group.AlarmGroupService;
import us.zoom.cube.site.biz.alarm.group.AlarmGroupUtilService;
import us.zoom.cube.site.biz.syspara.AlarmGroupParaService;
import us.zoom.cube.site.biz.syspara.AlarmSilenceParaService;
import us.zoom.cube.site.core.TenantHandler;
import us.zoom.cube.site.core.UserHandler;
import us.zoom.cube.site.core.alarm.group.AlarmGroupAlarmItemHandler;
import us.zoom.cube.site.core.alarm.group.AlarmGroupHandler;
import us.zoom.cube.site.core.alarm.group.AlarmGroupServiceItemHandler;
import us.zoom.cube.site.core.alarm.group.AlarmGroupTagItemHandler;
import us.zoom.cube.site.core.alarm.silence.AlarmSilenceHandler;
import us.zoom.cube.site.core.alarm.silence.AlarmSilenceRecurringScheduleHandler;
import us.zoom.cube.site.infra.utils.IdUtils;
import us.zoom.cube.site.lib.ResponseObject;
import us.zoom.cube.site.lib.common.MonitorTypeEnum;
import us.zoom.cube.site.lib.input.alarm.group.AlarmGroupAlarmItemInput;
import us.zoom.cube.site.lib.input.alarm.group.AlarmGroupServiceItemInput;
import us.zoom.cube.site.lib.input.alarm.group.AlarmGroupTagItemInput;
import us.zoom.cube.site.lib.input.alarm.silence.*;
import us.zoom.cube.site.lib.output.PageResult;
import us.zoom.cube.site.lib.output.alarm.group.AlarmGroupAlarmItemOutput;
import us.zoom.cube.site.lib.output.alarm.group.AlarmGroupOutput;
import us.zoom.cube.site.lib.output.alarm.group.AlarmGroupServiceItemOutput;
import us.zoom.cube.site.lib.output.alarm.group.AlarmGroupTagItemOutput;
import us.zoom.cube.site.lib.output.alarm.silence.AlarmSilenceOutput;
import us.zoom.cube.site.lib.output.alarm.silence.AlarmSilenceRecurringScheduleOutput;
import us.zoom.cube.site.lib.output.alarm.silence.AlarmSilenceSimpleOutput;
import us.zoom.cube.site.lib.output.alarm.silence.AlarmSilenceTimeOutput;
import us.zoom.cube.site.lib.query.*;
import us.zoom.cube.site.lib.query.alarmSilence.OutAlarmSilenceQuery;
import us.zoom.infra.dao.model.*;
import us.zoom.infra.dao.result.alarmSilence.UpdateAlarmSilenceScheduleResult;
import us.zoom.infra.dao.result.alarmgroup.AddAlarmGroupResult;
import us.zoom.infra.dao.result.alarmgroup.UpdateAlarmGroupResult;
import us.zoom.infra.enums.AlarmSilenceStatusEnum;
import us.zoom.infra.enums.AlarmSilenceTypeEnum;
import us.zoom.infra.enums.alarmSilence.AlarmSilenceCreateTypeEnum;
import us.zoom.infra.utils.DateUtils;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

import static us.zoom.cube.site.biz.alarm.group.AlarmGroupService.V1;
import static us.zoom.cube.site.biz.alarm.group.AlarmGroupService.V2;
import static us.zoom.infra.utils.AlarmGroupConstants.*;
import static us.zoom.infra.utils.CommonSplitConstants.COMMA_SPLIT;
import static us.zoom.infra.utils.CommonSplitConstants.SPACE;

@Component
@Slf4j
public class AlarmSilenceService {
     @Autowired
     private AlarmGroupHandler alarmGroupHandler;

     @Autowired
     private AlarmGroupUtilService alarmGroupUtilService;

     @Autowired
     private AlarmSilenceHandler alarmSilenceHandler;

     @Autowired
     private AlarmSilenceParaService alarmSilenceParaService;

     @Autowired
     private AlarmSilenceUtilService alarmSilenceUtilService;

     @Autowired
     private AuthService authService;

     @Autowired
     private UserHandler userHandler;

     @Autowired
     private TenantHandler tenantHandler;

    @Autowired
    private AlarmGroupServiceItemHandler alarmGroupServiceItemHandler;

    @Autowired
    private AlarmSilenceRecurringScheduleHandler alarmSilenceRecurringScheduleHandler;

    @Autowired
    private AlarmGroupParaService alarmGroupParaService;

    @Autowired
    private AlarmGroupTagItemHandler alarmGroupTagItemHandler;

    @Autowired
    private AlarmGroupAlarmItemHandler alarmGroupAlarmItemHandler;

    @Autowired
    private AlarmGroupService alarmGroupService;

    @Transactional
    public ResponseObject showAlarmSilenceList(PageQuery<AlarmSilenceQuery> pageQuery) {
        authService.checkAuthFromAuthInterceptorIgnoreTenant();
        String userId = pageQuery.getUserId();
        int pageIndex = pageQuery.getPageIndex();
        int pageSize = pageQuery.getPageSize();
        AlarmSilenceQuery alarmSilenceQuery = Optional.ofNullable(pageQuery.getQueryPara()).orElse(new AlarmSilenceQuery());
        alarmSilenceQuery.check();
        pageQuery.setQueryPara(alarmSilenceQuery);
        Boolean isQueryServiceName = false;
        String queryServiceId = null;
        if (!StringUtils.isEmpty(alarmSilenceQuery.getServiceName())) {
            isQueryServiceName = true;
            if (ALL_SERVICE_NAME.equals(alarmSilenceQuery.getServiceName())) {
                queryServiceId = ALL_SERVICE_ID;
            } else {
                TenantDO queryService = tenantHandler.getTenantByName(alarmSilenceQuery.getServiceName());
                Assert.isTrue(queryService != null, "The service in query condition is not existed");
                queryServiceId = queryService.getId();
            }
        }
        final Boolean isQueryServiceNameFinal = isQueryServiceName;
        final String queryServiceIdFinal = queryServiceId;
        Integer visibility = alarmSilenceQuery.getVisibility();
        List<AlarmSilenceDO> alarmSilenceDOList = alarmSilenceHandler.findByParam(alarmSilenceQuery.getName(),
                alarmSilenceQuery.getStatus(), alarmSilenceQuery.getType(), visibility);

        List<String> alarmGroupIdList = alarmSilenceDOList.stream()
                .map(AlarmSilenceDO::getAlarmGroupId)
                .filter(id -> id != null && !id.isEmpty())
                .flatMap(id -> Arrays.stream(id.split(COMMA_SPLIT)))
                .map(String::trim)
                .filter(id -> !id.isEmpty())
                .distinct()
                .collect(Collectors.toList());

        List<AlarmGroupDO> alarmGroupDOList = alarmGroupHandler.findByIdList(alarmGroupIdList);
        Map<String, AlarmGroupDO> alarmGroupDOMap = alarmGroupDOList.stream().collect(Collectors.toMap(AlarmGroupDO::getId, e -> e));

        // Batch query all alarm group service items to avoid N+1 query problem
        List<AlarmGroupServiceItemDO> allAlarmGroupServiceItems = alarmGroupServiceItemHandler.findByAlarmGroupIdList(alarmGroupIdList);
        Map<String, List<AlarmGroupServiceItemDO>> alarmGroupServiceItemMap = allAlarmGroupServiceItems.stream()
                .collect(Collectors.groupingBy(AlarmGroupServiceItemDO::getAlarmGroupId));

        List<AlarmSilenceDO> filterAlarmSilenceList = alarmSilenceDOList.stream().filter(e -> {

            if (e.getAlarmGroupId() == null || e.getAlarmGroupId().isEmpty()) {
                return false;
            }

            return Arrays.stream(e.getAlarmGroupId().split(COMMA_SPLIT))
                    .map(String::trim)
                    .filter(splitId -> !splitId.isEmpty())
                    .anyMatch(alarmGroupId -> {
                        AlarmGroupDO alarmGroupDO = alarmGroupDOMap.get(alarmGroupId);
                        if (alarmGroupDO == null) {
                            return false;
                        }
                        // Use the pre-fetched service items instead of querying in the loop
                        List<AlarmGroupServiceItemDO> serviceItemList = alarmGroupServiceItemMap.get(alarmGroupId);
                        if (serviceItemList == null) {
                            return false;
                        }
                        List<String> checkServiceIdList = serviceItemList.stream()
                                .map(AlarmGroupServiceItemDO::getServiceId)
                                .collect(Collectors.toList());
                        if (!alarmGroupUtilService.canOperateAlarmGroup(userId, checkServiceIdList)) {
                            return false;
                        }
                        if (!isQueryServiceNameFinal) {
                            return true;
                        }
                        if (ALL_SERVICE_ID.equals(queryServiceIdFinal)) {
                            return true;
                        }
                        return checkServiceIdList.contains(queryServiceIdFinal);
                    });
        }).collect(Collectors.toList());


        int total = filterAlarmSilenceList.size();
        int pageStart = (pageIndex - 1) * pageSize;
        int pageEnd = pageStart + pageSize;
        if (pageStart > filterAlarmSilenceList.size()) {
            Assert.isTrue(false, "the page index is too large");
        }
        if (pageEnd > filterAlarmSilenceList.size()) {
            pageEnd = filterAlarmSilenceList.size();
        }
        List<AlarmSilenceDO> pageAlarmSilenceList = filterAlarmSilenceList.subList(pageStart, pageEnd);

        List<AlarmSilenceSimpleOutput> alarmSilenceSimpleOutputList = new ArrayList<>();
        for (AlarmSilenceDO alarmSilenceDO : pageAlarmSilenceList) {
            AlarmSilenceSimpleOutput alarmSilenceSimpleOutput = new AlarmSilenceSimpleOutput();
            BeanUtils.copyProperties(alarmSilenceDO, alarmSilenceSimpleOutput);

            String start = "";
            String end = "";
            if (AlarmSilenceTypeEnum.ONE_TIME.getValue() == alarmSilenceDO.getType().intValue()) {
                start = alarmSilenceDO.getStartDateTimeStr();
                end = alarmSilenceDO.getEndDateTimeStr();
            } else if (AlarmSilenceTypeEnum.RECURRING.getValue() == alarmSilenceDO.getType().intValue()) {
                AlarmSilenceRecurringScheduleDO scheduleDO = alarmSilenceRecurringScheduleHandler.findByAlarmSilenceId(alarmSilenceDO.getId());
                if (scheduleDO != null) {
                    start = scheduleDO.getStartDate() + " 00:00:00";
                    if (!StringUtils.isEmpty(scheduleDO.getEndDate())) {
                        end = scheduleDO.getEndDate() + " 00:00:00";
                    }
                }
            }

            String silenceTime = start + " ~ " + end;
            alarmSilenceSimpleOutput.setSilenceTime(silenceTime);
            alarmSilenceSimpleOutput.setSilenceStartTime(start);
            alarmSilenceSimpleOutput.setSilenceEndTime(end);
            alarmSilenceSimpleOutput.setVisibility(alarmSilenceDO.getVisibility());

            List<String> alarmGroupNames = new ArrayList<>();
            List<String> serviceNameList = new ArrayList<>();
            for (String id : alarmSilenceSimpleOutput.getAlarmGroupId().split(COMMA_SPLIT)) {
                AlarmGroupDO alarmGroupDO = alarmGroupDOMap.get(id);
                alarmGroupNames.add(alarmGroupDO.getName());
                // Use the pre-fetched service items instead of querying in the loop
                List<AlarmGroupServiceItemDO> alarmGroupServiceItemDOList = alarmGroupServiceItemMap.get(id);
                if (alarmGroupServiceItemDOList != null) {
                    for (AlarmGroupServiceItemDO alarmGroupServiceItemDO : alarmGroupServiceItemDOList) {
                        if (ALL_SERVICE_ID.equals(alarmGroupServiceItemDO.getServiceId())) {
                            serviceNameList.add(ALL_SERVICE_NAME);
                        } else {
                            TenantDO tenantDO = tenantHandler.getTenantByIdFromCache(alarmGroupServiceItemDO.getServiceId());
                            if (tenantDO != null) {
                                serviceNameList.add(tenantDO.getName());
                            }
                        }
                    }
                }
            }
            serviceNameList = serviceNameList.stream().distinct().collect(Collectors.toList());
            Collections.sort(serviceNameList);
            alarmSilenceSimpleOutput.setServiceNameList(serviceNameList);
            alarmSilenceSimpleOutput.setAlarmGroupName(String.join(COMMA_SPLIT, alarmGroupNames));
            alarmSilenceSimpleOutputList.add(alarmSilenceSimpleOutput);
        }

        ResponseObject responseObject = ResponseObject.success(pageQuery.getOperId(), new PageResult<>(total, alarmSilenceSimpleOutputList));
        return responseObject;
    }

    public ResponseObject getAlarmSilenceById(AlarmSilenceIdQuery alarmSilenceIdQuery){
        authService.checkAuthFromAuthInterceptorIgnoreTenant();
        String userId = alarmSilenceIdQuery.getUserId();

        AlarmSilenceDO alarmSilenceDO = alarmSilenceHandler.findById(alarmSilenceIdQuery.getId());
        if(alarmSilenceDO == null){
            Assert.isTrue(false, "The alarmSilence is not existed");
        }
        AlarmSilenceOutput alarmSilenceOutput = new AlarmSilenceOutput();
        String alarmGroupId = alarmSilenceDO.getAlarmGroupId();
        List<String> groupIds = List.of(alarmGroupId.split(COMMA_SPLIT));
        List<AlarmGroupDO> alarmGroupDO = alarmGroupHandler.findByIdList(groupIds);
        alarmSilenceOutput.setAlarmGroupName(alarmGroupDO.stream().map(AlarmGroupDO::getName).collect(Collectors.joining(COMMA_SPLIT)));
        if (null != alarmSilenceDO.getCreateType() && AlarmSilenceCreateTypeEnum.NORMAL.getValue() != alarmSilenceDO.getCreateType()) {
            AlarmGroupOutput alarmGroupOutput = alarmGroupService.getAlarmGroupById(groupIds.stream().findFirst().orElse(null), userId);
            alarmSilenceOutput.setServiceItemList(alarmGroupOutput.getServiceItemList());
        }

        if(!alarmGroupUtilService.canOperateAlarmGroup(userId, alarmGroupId)){
            Assert.isTrue(false, "User do not have the permission to the target alarmSilence");
        }
        BeanUtils.copyProperties(alarmSilenceDO, alarmSilenceOutput);
        if(AlarmSilenceTypeEnum.RECURRING.getValue() == alarmSilenceOutput.getType().intValue()){
            AlarmSilenceRecurringScheduleDO alarmSilenceRecurringScheduleDO = alarmSilenceRecurringScheduleHandler.findByAlarmSilenceId(alarmSilenceOutput.getId());
            Assert.isTrue(alarmSilenceRecurringScheduleDO != null, "The recurring of alarm silence is missing");
            AlarmSilenceRecurringScheduleOutput alarmSilenceRecurringScheduleOutput = new AlarmSilenceRecurringScheduleOutput();
            BeanUtils.copyProperties(alarmSilenceRecurringScheduleDO, alarmSilenceRecurringScheduleOutput);
            alarmSilenceOutput.setAlarmSilenceRecurringSchedule(alarmSilenceRecurringScheduleOutput);
        }

        ResponseObject responseObject = ResponseObject.success(alarmSilenceOutput);
        return responseObject;
    }

    public ResponseObject getAlarmSilenceByName(AlarmSilenceNameQuery alarmSilenceNameQuery){
        alarmSilenceNameQuery.check();
        String name = alarmSilenceNameQuery.getName();
        AlarmSilenceDO alarmSilenceDO = alarmSilenceHandler.findByName(name);
        Assert.isTrue(alarmSilenceDO != null, "The alarmSilence is not existed");
        AlarmSilenceIdQuery alarmSilenceIdQuery = new AlarmSilenceIdQuery();
        alarmSilenceIdQuery.setId(alarmSilenceDO.getId());
        return getAlarmSilenceById(alarmSilenceIdQuery);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public ResponseObject getAlarmSilenceList(PageQuery<OutAlarmSilenceQuery> alarmSilenceQueryPageQuery) {
        authService.checkAuthFromAuthInterceptorIgnoreTenant();

        if (alarmSilenceQueryPageQuery == null || alarmSilenceQueryPageQuery.getQueryPara() == null) {
            throw new IllegalArgumentException("The request params cannot be null");
        }
        alarmSilenceQueryPageQuery.getQueryPara().check();
        UserDO user = userHandler.getUserById(AuthInterceptor.getUserId());
        Assert.isTrue(null != user, "This user does not exist in the cube");

        PageQuery<AlarmSilenceQuery> pageQuery = new PageQuery<>();
        AlarmSilenceQuery alarmSilenceQuery = new AlarmSilenceQuery();
        alarmSilenceQuery.setServiceName(alarmSilenceQueryPageQuery.getQueryPara().getServiceName());
        alarmSilenceQuery.setStatus(alarmSilenceQueryPageQuery.getQueryPara().getSilenceStatus());
        alarmSilenceQuery.setName(alarmSilenceQueryPageQuery.getQueryPara().getName());
        pageQuery.setQueryPara(alarmSilenceQuery);
        pageQuery.setUserId(user.getId());
        if (alarmSilenceQueryPageQuery.getPageIndex() != 0) {
            pageQuery.setPageIndex(alarmSilenceQueryPageQuery.getPageIndex());
        }
        if (alarmSilenceQueryPageQuery.getPageSize() != 0) {
            pageQuery.setPageSize(alarmSilenceQueryPageQuery.getPageSize());
        }
        return showAlarmSilenceList(pageQuery);
    }

    public ResponseObject checkExist(AlarmSilenceNameQuery alarmSilenceNameQuery){
        alarmSilenceNameQuery.check();
        String name = alarmSilenceNameQuery.getName();
        AlarmSilenceDO alarmSilenceDO = alarmSilenceHandler.findByName(name);
        if(alarmSilenceDO == null){
            return ResponseObject.success(false);
        }else {
            return ResponseObject.success(true);
        }
    }

    @Transactional
    public ResponseObject addOutAlarmSilence(OutAlarmSilenceInput outAlarmSilenceInput){
        alarmSilenceUtilService.preAddOutAlarmSilenceParameter(outAlarmSilenceInput);
        alarmSilenceUtilService.checkAddOutAlarmSilenceParameter(outAlarmSilenceInput);
        AlarmGroupDO alarmGroupDO = alarmGroupHandler.findByName(outAlarmSilenceInput.getAlarmGroupName());
        AlarmSilenceInput alarmSilenceInput = new AlarmSilenceInput();
        BeanUtils.copyProperties(outAlarmSilenceInput, alarmSilenceInput);
        alarmSilenceInput.setAlarmGroupId(alarmGroupDO.getId());
        return addAlarmSilence(alarmSilenceInput);
    }

    @Transactional
    public ResponseObject addOutAlarmSilenceV2(AlarmSilenceInput alarmSilenceInput) {
        alarmSilenceUtilService.checkAndPreHandleSilenceParameter(alarmSilenceInput);
        return quickAddAlarmSilence(alarmSilenceInput);
    }

    @Transactional
    public ResponseObject addAlarmSilenceV2(AlarmSilenceInput alarmSilenceInput) {
        Assert.isTrue(alarmSilenceInput.getCreateType() != null, "AlarmSilence create type is null");
        Assert.isTrue(AlarmSilenceCreateTypeEnum.isValid(alarmSilenceInput.getCreateType()), "AlarmSilence create type is not correct");
        if ((alarmSilenceInput.getCreateType() == AlarmSilenceCreateTypeEnum.QUICK.getValue())) {
            return quickAddAlarmSilence(alarmSilenceInput);
        } else {
            return addAlarmSilence(alarmSilenceInput);
        }
    }

    @Transactional
    public ResponseObject quickAddAlarmSilence(AlarmSilenceInput alarmSilenceInput) {
        checkAuth(alarmSilenceInput, V2);
        String userName = userHandler.getNameById(alarmSilenceInput.getUserId());
        AlarmSilenceDO alarmSilenceDO = buildAddAlarmSilenceDO(alarmSilenceInput, userName);
        AlarmSilenceRecurringScheduleDO scheduleDO = buildAddAlarmSilenceRecurringScheduleDO(alarmSilenceInput, alarmSilenceDO);
        AlarmSilenceStatusEnum statusEnum = null;
        try {
            statusEnum = alarmSilenceUtilService.calcAlarmSilenceStatus(alarmSilenceDO, scheduleDO, new Date());
        }catch (Exception e){
            log.error("calcAlarmSilenceStatus {} failed",alarmSilenceDO.getName(),e);
            Assert.isTrue(false,"calcAlarmSilenceStatus failed");
        }


        AddAlarmGroupResult addAlarmGroupResult = buildAddAlarmGroupRelateDTO(alarmSilenceDO, alarmSilenceInput, userName);
        Assert.isTrue(addAlarmGroupResult != null, "AlarmSilence creation failed");
        alarmGroupHandler.insert(addAlarmGroupResult.getAlarmGroupDO());
        alarmGroupServiceItemHandler.batchInsert(addAlarmGroupResult.getAlarmGroupServiceItemDOList());
        alarmGroupAlarmItemHandler.batchInsert(addAlarmGroupResult.getAlarmGroupAlarmItemDOList());
        alarmGroupTagItemHandler.batchInsert(addAlarmGroupResult.getAlarmGroupTagItemDOList());

        alarmSilenceDO.setStatus(statusEnum.getValue());
        alarmSilenceHandler.insert(alarmSilenceDO);
        if(scheduleDO != null && StringUtils.isNotEmpty(scheduleDO.getId())){
            alarmSilenceRecurringScheduleHandler.insert(scheduleDO);
        }

        return ResponseObject.success(alarmSilenceDO.getId());
    }

    public AddAlarmGroupResult buildAddAlarmGroupRelateDTO(AlarmSilenceDO alarmSilenceDO, AlarmSilenceInput alarmSilenceInput, String userName) {
        TenantDO tenantDO = tenantHandler.getTenantFromCacheFirst(alarmSilenceInput.getServiceItemList().stream().findFirst()
                .map(AlarmGroupServiceItemInput::getServiceId).orElse(null));
        Assert.isTrue(tenantDO != null, "The service is not existed");
        String serviceId = tenantDO.getName();

        List<AlarmGroupServiceItemInput> serviceItemList = alarmSilenceInput.getServiceItemList();
        Assert.isTrue(!CollectionUtils.isEmpty(serviceItemList), "The service and alarm is null, Please select the service and alarm");

        String alarmGroupName = serviceId + "_" + alarmSilenceInput.getName() + "_" + QUICK_ALARM_SILENCE;
        Assert.isNull(alarmGroupHandler.findByName(alarmGroupName), "Alarm group name is duplicated");

        AlarmGroupDO alarmGroupDO = new AlarmGroupDO();
        String alarmGroupId = IdUtils.generateId();
        alarmSilenceDO.setAlarmGroupId(alarmGroupId);
        alarmGroupDO.setId(alarmGroupId);
        alarmGroupDO.setName(alarmGroupName);
        alarmGroupDO.setCreator(userName);
        alarmGroupDO.setEditor(userName);
        alarmGroupDO.setVisibility(0);

        List<AlarmGroupAlarmItemDO> alarmGroupAlarmItemDOList = new ArrayList<>();
        List<AlarmGroupTagItemDO> alarmGroupTagItemDOS = new ArrayList<>();
        List<AlarmGroupServiceItemDO> alarmGroupServiceItemDOList = new ArrayList<>();
        for (AlarmGroupServiceItemInput serviceItem : serviceItemList) {
            List<AlarmGroupAlarmItemInput> alarmItemList = serviceItem.getAlarmItemList();
            if (CollectionUtils.isEmpty(alarmItemList)) {
                continue;
            }

            AlarmGroupServiceItemDO serviceItemDO = new AlarmGroupServiceItemDO();
            String serviceItemId = IdUtils.generateId();
            serviceItemDO.setId(serviceItemId);
            serviceItemDO.setServiceId(serviceItem.getServiceId());
            serviceItemDO.setAlarmGroupId(alarmGroupId);
            alarmGroupServiceItemDOList.add(serviceItemDO);

            alarmItemList.forEach(alarmItem -> {
                AlarmGroupAlarmItemDO alarmItemDO = new AlarmGroupAlarmItemDO();
                String alarmItemId = IdUtils.generateId();
                alarmItemDO.setId(alarmItemId);
                alarmItemDO.setAlarmGroupServiceItemId(serviceItemId);
                alarmItemDO.setAlarmId(alarmItem.getAlarmId());
                alarmGroupAlarmItemDOList.add(alarmItemDO);
            });

            List<AlarmGroupTagItemInput> groupTagItemList = serviceItem.getGroupTagItemList();
            if (!CollectionUtils.isEmpty(groupTagItemList)) {
                groupTagItemList.forEach(tag -> {
                    AlarmGroupTagItemDO alarmGroupTagItemDO = new AlarmGroupTagItemDO();
                    BeanUtils.copyProperties(tag, alarmGroupTagItemDO);
                    alarmGroupTagItemDO.setId(IdUtils.generateId());
                    alarmGroupTagItemDO.setAlarmGroupServiceItemId(serviceItemId);
                    alarmGroupTagItemDOS.add(alarmGroupTagItemDO);
                });
            }
        }

        if(CollectionUtils.isEmpty(alarmGroupServiceItemDOList)) {
            return null;
        }
        return AddAlarmGroupResult.builder()
                .alarmGroupDO(alarmGroupDO)
                .alarmGroupServiceItemDOList(alarmGroupServiceItemDOList)
                .alarmGroupAlarmItemDOList(alarmGroupAlarmItemDOList)
                .alarmGroupTagItemDOList(alarmGroupTagItemDOS)
                .build();
    }

    public void checkAuth(AlarmSilenceInput alarmSilenceInput, String version) {
        authService.checkAuthFromAuthInterceptorIgnoreTenant();
        alarmSilenceUtilService.checkAddAlarmSilenceParameter(alarmSilenceInput, version);
        alarmGroupUtilService.adjustOrderIndex(alarmSilenceInput.getServiceItemList());
    }

    public AlarmSilenceDO buildAddAlarmSilenceDO(AlarmSilenceInput alarmSilenceInput, String userName) {
        AlarmSilenceDO alarmSilenceDO = new AlarmSilenceDO();
        BeanUtils.copyProperties(alarmSilenceInput, alarmSilenceDO);
        alarmSilenceDO.setId(IdUtils.generateId());
        alarmSilenceDO.setCreateTime(new Date());
        alarmSilenceDO.setModifyTime(new Date());
        alarmSilenceDO.setCreator(userName);
        alarmSilenceDO.setEditor(userName);
        alarmSilenceDO.setVisibility(1);
        return alarmSilenceDO;
    }

    public AlarmSilenceRecurringScheduleDO buildAddAlarmSilenceRecurringScheduleDO(AlarmSilenceInput alarmSilenceInput, AlarmSilenceDO alarmSilenceDO) {
        AlarmSilenceRecurringScheduleDO scheduleDO = null;
        if(AlarmSilenceTypeEnum.RECURRING.getValue() == alarmSilenceInput.getType().intValue()){
            scheduleDO = new AlarmSilenceRecurringScheduleDO();
            BeanUtils.copyProperties(alarmSilenceInput.getAlarmSilenceRecurringSchedule(), scheduleDO);
            scheduleDO.setId(IdUtils.generateId());
            scheduleDO.setAlarmSilenceId(alarmSilenceDO.getId());
        }
        return scheduleDO;
    }

    @Transactional
    public ResponseObject addAlarmSilence(AlarmSilenceInput alarmSilenceInput){
        checkAuth(alarmSilenceInput, V1);
        String userName = userHandler.getNameById(alarmSilenceInput.getUserId());
        AlarmSilenceDO alarmSilenceDO = buildAddAlarmSilenceDO(alarmSilenceInput, userName);
        AlarmSilenceRecurringScheduleDO scheduleDO = buildAddAlarmSilenceRecurringScheduleDO(alarmSilenceInput, alarmSilenceDO);
        AlarmSilenceStatusEnum statusEnum = null;
        try {
            statusEnum = alarmSilenceUtilService.calcAlarmSilenceStatus(alarmSilenceDO, scheduleDO, new Date());
        }catch (Exception e){
            log.error("calcAlarmSilenceStatus {} failed",alarmSilenceDO.getName(),e);
            Assert.isTrue(false,"calcAlarmSilenceStatus failed");
        }

        alarmSilenceDO.setStatus(statusEnum.getValue());
        alarmSilenceHandler.insert(alarmSilenceDO);
        if(scheduleDO != null && StringUtils.isNotEmpty(scheduleDO.getId())){
            alarmSilenceRecurringScheduleHandler.insert(scheduleDO);
        }

        // todo notify
        ResponseObject responseObject = ResponseObject.success(alarmSilenceDO.getId());
        return responseObject;
    }


    @Transactional
    public ResponseObject updateOutAlarmSilence(OutAlarmSilenceInput outAlarmSilenceInput) {
        alarmSilenceUtilService.checkUpdateOutAlarmSilenceParameter(outAlarmSilenceInput.getName(), outAlarmSilenceInput.getAlarmGroupName());
        AlarmSilenceDO alarmSilenceDO = alarmSilenceHandler.findByName(outAlarmSilenceInput.getName());
        AlarmGroupDO alarmGroupDO = alarmGroupHandler.findByName(outAlarmSilenceInput.getAlarmGroupName());

        AlarmSilenceInput alarmSilenceInput = new AlarmSilenceInput();
        BeanUtils.copyProperties(outAlarmSilenceInput, alarmSilenceInput);
        alarmSilenceInput.setId(alarmSilenceDO.getId());
        alarmSilenceInput.setAlarmGroupId(alarmGroupDO.getId());
        return updateAlarmSilence(alarmSilenceInput);
    }

    @Transactional
    public ResponseObject updateAlarmSilenceV2(AlarmSilenceInput alarmSilenceInput){
        Assert.isTrue(alarmSilenceInput.getCreateType() != null, "AlarmSilence create type is null");
        Assert.isTrue(AlarmSilenceCreateTypeEnum.isValid(alarmSilenceInput.getCreateType()), "AlarmSilence create type is not correct");
        if ((alarmSilenceInput.getCreateType() == AlarmSilenceCreateTypeEnum.QUICK.getValue())) {
            return quickUpdataAlarmSilence(alarmSilenceInput);
        } else {
            return updateAlarmSilence(alarmSilenceInput);
        }
    }

    private void determineCreateType(AlarmSilenceInput input) {
        if (input.getCreateType() != null) {
            return;
        }
        input.setCreateType(StringUtils.isNotBlank(input.getAlarmGroupName()) && CollectionUtils.isEmpty(input.getServiceItemList())
                ? AlarmSilenceCreateTypeEnum.NORMAL.getValue() : AlarmSilenceCreateTypeEnum.QUICK.getValue());
    }

    private void setDefaultType(AlarmSilenceInput input) {
        if (input.getType() == null) {
            input.setType(AlarmSilenceTypeEnum.ONE_TIME.getValue());
        }
    }


    @Transactional
    public ResponseObject outUpdateAlarmSilenceV2(AlarmSilenceInput alarmSilenceInput) {
        determineCreateType(alarmSilenceInput);
        setDefaultType(alarmSilenceInput);
        Assert.isTrue(alarmSilenceInput.getCreateType() != null, "AlarmSilence create type is null");
        Assert.isTrue(AlarmSilenceCreateTypeEnum.isValid(alarmSilenceInput.getCreateType()), "AlarmSilence create type is not correct");
        if ((alarmSilenceInput.getCreateType() == AlarmSilenceCreateTypeEnum.QUICK.getValue())) {
            return quickUpdataAlarmSilence(alarmSilenceInput);
        } else {
            alarmSilenceUtilService.checkUpdateOutAlarmSilenceParameter(alarmSilenceInput.getName(), alarmSilenceInput.getAlarmGroupName());
            AlarmSilenceDO alarmSilenceDO = alarmSilenceHandler.findByName(alarmSilenceInput.getName());
            AlarmGroupDO alarmGroupDO = alarmGroupHandler.findByName(alarmSilenceInput.getAlarmGroupName());
            alarmSilenceInput.setId(alarmSilenceDO.getId());
            alarmSilenceInput.setAlarmGroupId(alarmGroupDO.getId());
            return updateAlarmSilence(alarmSilenceInput);
        }
    }

    public UpdateAlarmGroupResult buildUpdateAlarmGroupRelateDTO(AlarmSilenceDO alarmSilenceDO, AlarmSilenceInput alarmSilenceInput, String userName) {
        String alarmGroupId = alarmSilenceDO.getAlarmGroupId();
        AlarmGroupDO alarmGroupDO = alarmGroupHandler.findById(alarmGroupId);
        alarmGroupDO.setEditor(userName);
        AlarmGroupOutput alarmGroupOutput = alarmGroupService.getAlarmGroupById(alarmGroupId, alarmSilenceInput.getUserId());

        List<AlarmGroupServiceItemDO> addAlarmGroupServiceItemDOList = new ArrayList<>();
        List<AlarmGroupServiceItemDO> updateAlarmGroupServiceItemDOList = new ArrayList<>();
        List<String> deleteAlarmGroupServiceItemIdList = new ArrayList<>();
        List<AlarmGroupTagItemDO> addAlarmGroupTagItemDOList = new ArrayList<>();
        List<AlarmGroupTagItemDO> updateAlarmGroupTagItemDOList = new ArrayList<>();
        List<String> deleteAlarmGroupTagItemIdList = new ArrayList<>();
        List<AlarmGroupAlarmItemDO> addAlarmGroupAlarmItemDOList = new ArrayList<>();
        List<AlarmGroupAlarmItemDO> updateAlarmGroupAlarmItemDOList = new ArrayList<>();
        List<String> deleteAlarmGroupAlarmItemIdList = new ArrayList<>();

        alarmSilenceInput.getServiceItemList().forEach(e -> {
            if(StringUtils.isEmpty(e.getId())) {
                // add service
                AlarmGroupServiceItemDO alarmGroupServiceItemDO = new AlarmGroupServiceItemDO();
                BeanUtils.copyProperties(e, alarmGroupServiceItemDO);
                String serviceItemId = IdUtils.generateId();
                alarmGroupServiceItemDO.setId(serviceItemId);
                alarmGroupServiceItemDO.setAlarmGroupId(alarmGroupId);
                addAlarmGroupServiceItemDOList.add(alarmGroupServiceItemDO);

                e.getAlarmItemList().forEach(t -> {
                    AlarmGroupAlarmItemDO alarmGroupAlarmItemDO = new AlarmGroupAlarmItemDO();
                    BeanUtils.copyProperties(t, alarmGroupAlarmItemDO);
                    alarmGroupAlarmItemDO.setId(IdUtils.generateId());
                    alarmGroupAlarmItemDO.setAlarmGroupServiceItemId(serviceItemId);
                    addAlarmGroupAlarmItemDOList.add(alarmGroupAlarmItemDO);

                });

                e.getGroupTagItemList().forEach(t -> {
                    AlarmGroupTagItemDO alarmGroupTagItemDO = new AlarmGroupTagItemDO();
                    BeanUtils.copyProperties(t, alarmGroupTagItemDO);
                    alarmGroupTagItemDO.setId(IdUtils.generateId());
                    alarmGroupTagItemDO.setAlarmGroupServiceItemId(serviceItemId);
                    addAlarmGroupTagItemDOList.add(alarmGroupTagItemDO);
                });
            } else {
                // update service
                AlarmGroupServiceItemDO alarmGroupServiceItemDO = new AlarmGroupServiceItemDO();
                BeanUtils.copyProperties(e, alarmGroupServiceItemDO);
                alarmGroupServiceItemDO.setAlarmGroupId(alarmGroupId);
                updateAlarmGroupServiceItemDOList.add(alarmGroupServiceItemDO);

                // process alarmItem
                List<AlarmGroupAlarmItemInput> newAlarmGroupAlarmItemInputList = e.getAlarmItemList();
                Optional<AlarmGroupServiceItemOutput> alarmGroupServiceItemOutputOptional = alarmGroupOutput.getServiceItemList().stream().filter(t -> t.getId().equals(e.getId())).findFirst();
                if(alarmGroupServiceItemOutputOptional.isEmpty()){
                    Assert.isTrue(false, "service [" + e.getServiceName() + "] is not exist in AlarmGroup");
                }
                AlarmGroupServiceItemOutput alarmGroupServiceItemOutput = alarmGroupServiceItemOutputOptional.get();
                List<AlarmGroupAlarmItemOutput> oldAlarmGroupAlarmItemOutputList = alarmGroupServiceItemOutput.getAlarmItemList();

                newAlarmGroupAlarmItemInputList.forEach(t -> {
                    if(StringUtils.isEmpty(t.getId())){
                        AlarmGroupAlarmItemDO alarmGroupAlarmItemDO = new AlarmGroupAlarmItemDO();
                        BeanUtils.copyProperties(t, alarmGroupAlarmItemDO);
                        alarmGroupAlarmItemDO.setId(IdUtils.generateId());
                        alarmGroupAlarmItemDO.setAlarmGroupServiceItemId(alarmGroupServiceItemDO.getId());
                        addAlarmGroupAlarmItemDOList.add(alarmGroupAlarmItemDO);
                    } else {
                        AlarmGroupAlarmItemDO alarmGroupAlarmItemDO = new AlarmGroupAlarmItemDO();
                        BeanUtils.copyProperties(t, alarmGroupAlarmItemDO);
                        alarmGroupAlarmItemDO.setAlarmGroupServiceItemId(alarmGroupServiceItemDO.getId());
                        updateAlarmGroupAlarmItemDOList.add(alarmGroupAlarmItemDO);
                    }
                });
                Set<String> newAlarmItemIdList = newAlarmGroupAlarmItemInputList.stream().map(AlarmGroupAlarmItemInput::getId).filter(StringUtils::isNotEmpty).collect(Collectors.toSet());
                List<String> needDeleteAlarmItemIdList = oldAlarmGroupAlarmItemOutputList.stream().map(AlarmGroupAlarmItemOutput::getId).filter(id -> !newAlarmItemIdList.contains(id)).toList();
                deleteAlarmGroupAlarmItemIdList.addAll(needDeleteAlarmItemIdList);

                // process tagItem
                List<AlarmGroupTagItemInput> newAlarmGroupTagItemInputList = e.getGroupTagItemList();
                List<AlarmGroupTagItemOutput> oldAlarmGroupTagItemOutputList = alarmGroupServiceItemOutput.getGroupTagItemList();

                newAlarmGroupTagItemInputList.forEach(t -> {
                    if(StringUtils.isEmpty(t.getId())){
                        AlarmGroupTagItemDO alarmGroupTagItemDO = new AlarmGroupTagItemDO();
                        BeanUtils.copyProperties(t, alarmGroupTagItemDO);
                        alarmGroupTagItemDO.setId(IdUtils.generateId());
                        alarmGroupTagItemDO.setAlarmGroupServiceItemId(alarmGroupServiceItemDO.getId());
                        addAlarmGroupTagItemDOList.add(alarmGroupTagItemDO);
                    } else {
                        AlarmGroupTagItemDO alarmGroupTagItemDO = new AlarmGroupTagItemDO();
                        BeanUtils.copyProperties(t, alarmGroupTagItemDO);
                        alarmGroupTagItemDO.setAlarmGroupServiceItemId(alarmGroupServiceItemDO.getId());
                        updateAlarmGroupTagItemDOList.add(alarmGroupTagItemDO);
                    }
                });
                Set<String> newTagItemIdList = newAlarmGroupTagItemInputList.stream().map(AlarmGroupTagItemInput::getId).filter(StringUtils::isNotEmpty).collect(Collectors.toSet());
                List<String> needDeleteTagItemIdList = oldAlarmGroupTagItemOutputList.stream().map(AlarmGroupTagItemOutput::getId).filter(id -> !newTagItemIdList.contains(id)).toList();
                deleteAlarmGroupTagItemIdList.addAll(needDeleteTagItemIdList);
            }
        });

        // delete service
        Set<String> newServiceItemIdSet = alarmSilenceInput.getServiceItemList().stream().map(AlarmGroupServiceItemInput::getId).filter(StringUtils::isNotEmpty).collect(Collectors.toSet());
        List<AlarmGroupServiceItemOutput> oldServiceItemList = alarmGroupOutput.getServiceItemList();
        List<AlarmGroupServiceItemOutput> needDeleteServiceItemList = oldServiceItemList.stream().filter(t -> !newServiceItemIdSet.contains(t.getId())).toList();
        List<String> needDeleteServiceItemIdList = needDeleteServiceItemList.stream().map(AlarmGroupServiceItemOutput::getId).toList();
        deleteAlarmGroupServiceItemIdList.addAll(needDeleteServiceItemIdList);

        needDeleteServiceItemList.forEach(t -> {
            List<String> needDeleteAlarmItemIdList0 = t.getAlarmItemList().stream().map(AlarmGroupAlarmItemOutput::getId).toList();
            deleteAlarmGroupAlarmItemIdList.addAll(needDeleteAlarmItemIdList0);
            List<String> needDeleteTagItemIdList0 = t.getGroupTagItemList().stream().map(AlarmGroupTagItemOutput::getId).toList();
            deleteAlarmGroupTagItemIdList.addAll(needDeleteTagItemIdList0);
        });

        return UpdateAlarmGroupResult.builder()
                .alarmGroupDO(alarmGroupDO)
                .addAlarmGroupServiceItemDOList(addAlarmGroupServiceItemDOList)
                .updateAlarmGroupServiceItemDOList(updateAlarmGroupServiceItemDOList)
                .deleteAlarmGroupServiceItemIdList(deleteAlarmGroupServiceItemIdList)
                .addAlarmGroupAlarmItemDOList(addAlarmGroupAlarmItemDOList)
                .updateAlarmGroupAlarmItemDOList(updateAlarmGroupAlarmItemDOList)
                .deleteAlarmGroupAlarmItemIdList(deleteAlarmGroupAlarmItemIdList)
                .addAlarmGroupTagItemDOList(addAlarmGroupTagItemDOList)
                .updateAlarmGroupTagItemDOList(updateAlarmGroupTagItemDOList)
                .deleteAlarmGroupTagItemIdList(deleteAlarmGroupTagItemIdList)
                .build();
    }

    public ResponseObject quickUpdataAlarmSilence(AlarmSilenceInput alarmSilenceInput) {
        authService.checkAuthFromAuthInterceptorIgnoreTenant();
        alarmSilenceUtilService.checkUpdateAlarmSilenceParameter(alarmSilenceInput, V2);
        alarmGroupUtilService.adjustOrderIndex(alarmSilenceInput.getServiceItemList());

        String userName = userHandler.getNameById(alarmSilenceInput.getUserId());
        AlarmSilenceDO alarmSilenceDO = alarmSilenceHandler.findById(alarmSilenceInput.getId());
        alarmSilenceDO.setAutoDelete(alarmSilenceInput.getAutoDelete());
        alarmSilenceDO.setEditor(userName);

        UpdateAlarmSilenceScheduleResult updateAlarmSilenceScheduleResult = buildScheduleList(alarmSilenceDO, alarmSilenceInput);
        List<AlarmSilenceRecurringScheduleDO> addScheduleDOList = updateAlarmSilenceScheduleResult.getAddScheduleDOList();
        List<AlarmSilenceRecurringScheduleDO> updateScheduleDOList = updateAlarmSilenceScheduleResult.getUpdateScheduleDOList();
        List<String> deleteScheduleDOIdList = updateAlarmSilenceScheduleResult.getDeleteScheduleDOIdList();

        UpdateAlarmGroupResult updateAlarmGroupResult = buildUpdateAlarmGroupRelateDTO(alarmSilenceDO, alarmSilenceInput, userName);
        Assert.isTrue(updateAlarmGroupResult != null, "AlarmSilence update failed");

        alarmGroupHandler.update(updateAlarmGroupResult.getAlarmGroupDO());
        alarmGroupServiceItemHandler.batchInsert(updateAlarmGroupResult.getAddAlarmGroupServiceItemDOList());
        alarmGroupServiceItemHandler.batchUpdate(updateAlarmGroupResult.getUpdateAlarmGroupServiceItemDOList());
        alarmGroupServiceItemHandler.batchDelete(updateAlarmGroupResult.getDeleteAlarmGroupServiceItemIdList());
        alarmGroupAlarmItemHandler.batchInsert(updateAlarmGroupResult.getAddAlarmGroupAlarmItemDOList());
        alarmGroupAlarmItemHandler.batchUpdate(updateAlarmGroupResult.getUpdateAlarmGroupAlarmItemDOList());
        alarmGroupAlarmItemHandler.batchDelete(updateAlarmGroupResult.getDeleteAlarmGroupAlarmItemIdList());
        alarmGroupTagItemHandler.batchInsert(updateAlarmGroupResult.getAddAlarmGroupTagItemDOList());
        alarmGroupTagItemHandler.batchUpdate(updateAlarmGroupResult.getUpdateAlarmGroupTagItemDOList());
        alarmGroupTagItemHandler.batchDelete(updateAlarmGroupResult.getDeleteAlarmGroupTagItemIdList());

        alarmSilenceHandler.update(alarmSilenceDO);
        addScheduleDOList.forEach(e -> alarmSilenceRecurringScheduleHandler.insert(e));
        updateScheduleDOList.forEach(e -> alarmSilenceRecurringScheduleHandler.update(e));
        deleteScheduleDOIdList.forEach(e -> alarmSilenceRecurringScheduleHandler.deleteById(e));
        return ResponseObject.success(alarmSilenceDO.getId());
    }

    public UpdateAlarmSilenceScheduleResult buildScheduleList(AlarmSilenceDO alarmSilenceDO, AlarmSilenceInput alarmSilenceInput) {
        List<AlarmSilenceRecurringScheduleDO> addScheduleDOList = new ArrayList<>();
        List<AlarmSilenceRecurringScheduleDO> updateScheduleDOList = new ArrayList<>();
        List<String> deleteScheduleDOIdList = new ArrayList<>();
        if(AlarmSilenceTypeEnum.ONE_TIME.getValue() == alarmSilenceDO.getType().intValue()){
            if(AlarmSilenceTypeEnum.ONE_TIME.getValue() == alarmSilenceInput.getType().intValue()){
                // do nothing
            }else {
                AlarmSilenceRecurringScheduleDO scheduleDO = new AlarmSilenceRecurringScheduleDO();
                BeanUtils.copyProperties(alarmSilenceInput.getAlarmSilenceRecurringSchedule(),scheduleDO);
                scheduleDO.setId(IdUtils.generateId());
                scheduleDO.setAlarmSilenceId(alarmSilenceDO.getId());
                addScheduleDOList.add(scheduleDO);
            }
        }else {
            AlarmSilenceRecurringScheduleDO scheduleDO = alarmSilenceRecurringScheduleHandler.findByAlarmSilenceId(alarmSilenceDO.getId());
            Assert.isTrue(scheduleDO != null, "the origin recurring alarm silence's recurring schedule is missing");
            if(AlarmSilenceTypeEnum.ONE_TIME.getValue() == alarmSilenceInput.getType().intValue()){
                deleteScheduleDOIdList.add(scheduleDO.getId());
            }else {
                String scheduleId = scheduleDO.getId();
                AlarmSilenceRecurringScheduleInput scheduleInput = alarmSilenceInput.getAlarmSilenceRecurringSchedule();
                BeanUtils.copyProperties(scheduleInput,scheduleDO);
                scheduleDO.setId(scheduleId);
                scheduleDO.setAlarmSilenceId(alarmSilenceDO.getId());
                scheduleDO.setEndDate(scheduleInput.getEndDate());
                scheduleDO.setStartDate(scheduleInput.getStartDate());
                scheduleDO.setStartDayTime(scheduleInput.getStartDayTime());
                scheduleDO.setEndDayTime(scheduleInput.getEndDayTime());
                scheduleDO.setRepeatStep(scheduleInput.getRepeatStep());
                scheduleDO.setRepeatType(scheduleInput.getRepeatType());
                scheduleDO.setSilenceDayOfWeek(scheduleInput.getSilenceDayOfWeek());
                updateScheduleDOList.add(scheduleDO);
            }
        }

        AlarmSilenceRecurringScheduleDO scheduleDO = null;
        if(!CollectionUtils.isEmpty(addScheduleDOList)){
            scheduleDO = addScheduleDOList.get(0);
        }
        if(!CollectionUtils.isEmpty(updateScheduleDOList)){
            scheduleDO = updateScheduleDOList.get(0);
        }
        BeanUtils.copyProperties(alarmSilenceInput, alarmSilenceDO);
        alarmSilenceDO.setStartDateTimeStr(alarmSilenceInput.getStartDateTimeStr());
        alarmSilenceDO.setEndDateTimeStr(alarmSilenceInput.getEndDateTimeStr());
        AlarmSilenceStatusEnum statusEnum = null;
        try {
            statusEnum = alarmSilenceUtilService.calcAlarmSilenceStatus(alarmSilenceDO, scheduleDO, new Date());
        }catch (Exception e){
            log.error("calcAlarmSilenceStatus {} failed",alarmSilenceDO.getName(),e);
            Assert.isTrue(false,"calcAlarmSilenceStatus failed");
        }

        alarmSilenceDO.setStatus(statusEnum.getValue());

        return UpdateAlarmSilenceScheduleResult.builder()
                .addScheduleDOList(addScheduleDOList)
                .updateScheduleDOList(updateScheduleDOList)
                .deleteScheduleDOIdList(deleteScheduleDOIdList)
                .build();
    }

    @Transactional
    public ResponseObject updateAlarmSilence(AlarmSilenceInput alarmSilenceInput){
        authService.checkAuthFromAuthInterceptorIgnoreTenant();
        alarmSilenceUtilService.checkUpdateAlarmSilenceParameter(alarmSilenceInput, V1);

        AlarmSilenceDO alarmSilenceDO = alarmSilenceHandler.findById(alarmSilenceInput.getId());
        String userName = userHandler.getNameById(alarmSilenceInput.getUserId());
        alarmSilenceDO.setEditor(userName);
        alarmSilenceDO.setAutoDelete(alarmSilenceInput.getAutoDelete());

        UpdateAlarmSilenceScheduleResult updateAlarmSilenceScheduleResult = buildScheduleList(alarmSilenceDO, alarmSilenceInput);
        List<AlarmSilenceRecurringScheduleDO> addScheduleDOList = updateAlarmSilenceScheduleResult.getAddScheduleDOList();
        List<AlarmSilenceRecurringScheduleDO> updateScheduleDOList = updateAlarmSilenceScheduleResult.getUpdateScheduleDOList();
        List<String> deleteScheduleDOIdList = updateAlarmSilenceScheduleResult.getDeleteScheduleDOIdList();

        alarmSilenceHandler.update(alarmSilenceDO);
        addScheduleDOList.forEach(e -> alarmSilenceRecurringScheduleHandler.insert(e));
        updateScheduleDOList.forEach(e -> alarmSilenceRecurringScheduleHandler.update(e));
        deleteScheduleDOIdList.forEach(e -> alarmSilenceRecurringScheduleHandler.deleteById(e));
        // todo notify
        ResponseObject responseObject = ResponseObject.success(alarmSilenceDO.getId());
        return responseObject;
    }

    @Deprecated
    public ResponseObject getDatetimeWithTimezone(AlarmSilenceTimeQuery alarmSilenceTimeQuery) throws ParseException {
        //authService.checkAuth(alarmSilenceTimeQuery);
        AlarmSilenceTimeOutput alarmSilenceTimeOutput = new AlarmSilenceTimeOutput();

        Date date = DateUtils.parseDate(alarmSilenceTimeQuery.getTs(), DateUtils.FORMART1);
        DateFormat df = new SimpleDateFormat(DateUtils.FORMART1);


        List<String> frequentlyUsedTimezoneList = alarmSilenceParaService.getFrequentlyUsedTimezoneList();
        List<String> frequently = new ArrayList<>();
        frequentlyUsedTimezoneList.stream().forEach(e -> {
            df.setTimeZone(TimeZone.getTimeZone(e));
            frequently.add(e + SPACE + df.format(date));
        });

        alarmSilenceTimeOutput.setFrequently(frequently);

        List<String> zoneIdList = ZoneId.getAvailableZoneIds().stream().collect(Collectors.toList());
        Collections.sort(zoneIdList);

        List<String> alphabetical = new ArrayList<>();
        zoneIdList.stream().forEach(e -> {
            df.setTimeZone(TimeZone.getTimeZone(e));
            alphabetical.add(e + SPACE + df.format(date));
        });

        alarmSilenceTimeOutput.setAlphabetical(alphabetical);

        ResponseObject responseObject = ResponseObject.success(alarmSilenceTimeOutput);
        return responseObject;
    }

    @Transactional
    public ResponseObject delAlarmSilenceById(AlarmSilenceIdQuery alarmSilenceIdQuery){
        authService.checkAuthFromAuthInterceptorIgnoreTenant();
        String userId = alarmSilenceIdQuery.getUserId();
        AlarmSilenceDO alarmSilenceDO = alarmSilenceHandler.findById(alarmSilenceIdQuery.getId());
        if(alarmSilenceDO == null){
            Assert.isTrue(false, "The AlarmSilence is not exist");
        }

        if (StringUtils.isNotBlank(alarmSilenceDO.getAlarmGroupId())) {
            List<String> alarmGroupIds = List.of(alarmSilenceDO.getAlarmGroupId().split(COMMA_SPLIT));
            if (!alarmGroupUtilService.canOperateAlarmGroupList(userId, alarmGroupIds)) {
                Assert.isTrue(false, "User do not have the permission to this AlarmSilence");
            }
        }
        basicDelAlarmSilenceById(alarmSilenceDO);

        ResponseObject responseObject = ResponseObject.success(alarmSilenceDO.getId());
        return responseObject;
    }

    public void basicDelAlarmSilenceById(AlarmSilenceDO alarmSilenceDO) {
        if (null != alarmSilenceDO.getCreateType() && AlarmSilenceCreateTypeEnum.NORMAL.getValue() != alarmSilenceDO.getCreateType()) {
            AlarmGroupDO alarmGroupDO = alarmGroupHandler.findById(alarmSilenceDO.getAlarmGroupId());
            Assert.isTrue(alarmGroupDO != null, "alarmGroupDO is not existed");
            List<AlarmGroupServiceItemDO> serviceItemDOList = alarmGroupServiceItemHandler.findByAlarmGroupIdList(Lists.newArrayList(alarmGroupDO.getId()));
            for (AlarmGroupServiceItemDO serviceItemDO : serviceItemDOList) {
                alarmGroupAlarmItemHandler.deleteByAlarmGroupServiceItemId(serviceItemDO.getId());
                alarmGroupTagItemHandler.deleteByAlarmGroupServiceItemId(serviceItemDO.getId());
            }
            alarmGroupServiceItemHandler.deleteByAlarmGroupId(alarmGroupDO.getId());
            alarmGroupHandler.deleteById(alarmGroupDO.getId());
        }

        // delete
        alarmSilenceHandler.deleteById(alarmSilenceDO.getId());
        if(AlarmSilenceTypeEnum.RECURRING.getValue() == alarmSilenceDO.getType().intValue()){
            alarmSilenceRecurringScheduleHandler.deleteByAlarmSilenceId(alarmSilenceDO.getId());
        }
    }

    @Transactional
    public ResponseObject delAlarmSilenceByName(AlarmSilenceNameQuery alarmSilenceNameQuery){
        alarmSilenceNameQuery.check();
        String name = alarmSilenceNameQuery.getName();
        AlarmSilenceDO alarmSilenceDO = alarmSilenceHandler.findByName(name);
        Assert.isTrue(alarmSilenceDO != null, "The alarmSilence is not existed");
        AlarmSilenceIdQuery alarmSilenceIdQuery = new AlarmSilenceIdQuery();
        alarmSilenceIdQuery.setId(alarmSilenceDO.getId());
        return delAlarmSilenceById(alarmSilenceIdQuery);
    }

    @Transactional
    public ResponseObject updateAlarmSilenceStatus(AlarmSilenceStatusByNameInput alarmSilenceStatusByNameInput){
        alarmSilenceStatusByNameInput.check();
        String name = alarmSilenceStatusByNameInput.getName();
        AlarmSilenceDO alarmSilenceDO = alarmSilenceHandler.findByName(name);
        Assert.isTrue(alarmSilenceDO != null, "The alarmSilence is not existed");
        AlarmSilenceStatusInput alarmSilenceStatusInput = new AlarmSilenceStatusInput();
        BeanUtils.copyProperties(alarmSilenceStatusByNameInput, alarmSilenceStatusInput);
        alarmSilenceStatusInput.setId(alarmSilenceDO.getId());
        return updateAlarmSilenceStatus(alarmSilenceStatusInput);
    }

    public ResponseObject expireAlarmSilence(AlarmSilenceNameQuery alarmSilenceNameQuery){
        AlarmSilenceStatusByNameInput alarmSilenceStatusByNameInput = new AlarmSilenceStatusByNameInput();
        alarmSilenceStatusByNameInput.setName(alarmSilenceNameQuery.getName());
        alarmSilenceStatusByNameInput.setStatus(AlarmSilenceStatusEnum.expired.getValue());
        return updateAlarmSilenceStatus(alarmSilenceStatusByNameInput);
    }


    @Transactional
    public ResponseObject updateAlarmSilenceStatus(AlarmSilenceStatusInput alarmSilenceStatusInput){
        authService.checkAuthFromAuthInterceptorIgnoreTenant();
        alarmSilenceUtilService.checkUpdateAlarmSilenceStatusParameter(alarmSilenceStatusInput);

        AlarmSilenceDO alarmSilenceDO = alarmSilenceHandler.findById(alarmSilenceStatusInput.getId());

        alarmSilenceDO.setStatus(alarmSilenceStatusInput.getStatus());
        alarmSilenceDO.setEditor(AuthInterceptor.getUserName());
        alarmSilenceHandler.update(alarmSilenceDO);

        // todo notify
        ResponseObject responseObject = ResponseObject.success(alarmSilenceDO.getId());
        return responseObject;
    }



    public  ResponseObject getTimezoneList(TimeZoneQueryInput timeZoneQueryInput){
        authService.checkAuthFromAuthInterceptorIgnoreTenant();
        List<String> zoneIdList = ZoneId.getAvailableZoneIds().stream().collect(Collectors.toList());
        if(!StringUtils.isEmpty(timeZoneQueryInput.getName())){
            zoneIdList = zoneIdList.stream().filter(e -> e.contains(timeZoneQueryInput.getName())).collect(Collectors.toList());
        }
        ResponseObject responseObject = ResponseObject.success(zoneIdList);
        return responseObject;
    }

}
