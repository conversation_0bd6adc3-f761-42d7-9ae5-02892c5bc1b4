package us.zoom.cube.site.external.xms.output;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import us.zoom.cube.site.lib.output.alarm.channel.MentionGroupOutput;

import java.util.List;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class XmsMentionGroupsResponse {

    Integer result;

    XmsData data;

    @Data
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class XmsData {
        List<MentionGroupOutput> mentionGroups;
        String groupId;
    }
} 