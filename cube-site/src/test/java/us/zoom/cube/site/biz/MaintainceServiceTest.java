package us.zoom.cube.site.biz;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import us.zoom.cube.site.CubeSiteApplication;
import us.zoom.cube.site.biz.maintaince.MaintainceService;
import us.zoom.cube.site.lib.BasePara;
import us.zoom.cube.site.lib.ResponseObject;
import us.zoom.infra.thread.ThreadLocalStore;
import us.zoom.cube.lib.utils.JsonUtils;

@SpringBootTest(classes = CubeSiteApplication.class,webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)//springboot
public class MaintainceServiceTest extends  BaseTest {

    @Autowired
    MaintainceService  maintainceService;


    @Test
    public void test(){
        ThreadLocalStore.setUserInfoLocal("2269c62a-67c3-42c4-89cf-f64cf61d442e");
        ThreadLocalStore.setTenantInfoLocal("1f8cacb9-8769-481f-8ad8-443f18799fa2");
        ThreadLocalStore.setApiPath("/api/maintaince/addFoleForUserToSomeTenant");
        ResponseObject responseObject =   maintainceService.addFoleForUserToSomeTenant("applicationGuestors",ThreadLocalStore.getUserInfoLocal(),ThreadLocalStore.getTenantInfoLocal());
        System.out.println(responseObject);
        ThreadLocalStore.setApiPath("/api/maintaince/addAllUserToSomeUserGroup");
        BasePara basePara = new BasePara();
         responseObject =  maintainceService.addAllUserToSomeUserGroup("fa605039-7059-4c62-8546-c772b72ad715");
        System.out.println(responseObject);
        ThreadLocalStore.setApiPath("/api/maintaince/assignNoAccesUserToDefaultService");
        responseObject =  maintainceService.assignNoAccesUserToDefaultService();
        System.out.println(responseObject);
        ThreadLocalStore.setApiPath("/api/maintaince/backUpAuth");
        responseObject =  maintainceService.backUpAuth("[\"systemMaintainer\",\"applicationGuestors\",\"piiAdmin\",\"applicationOwners\",\"admin_role\",\"opOwner\"]");
        System.out.println(JsonUtils.toJsonStringIgnoreExp(responseObject));


    }

}
