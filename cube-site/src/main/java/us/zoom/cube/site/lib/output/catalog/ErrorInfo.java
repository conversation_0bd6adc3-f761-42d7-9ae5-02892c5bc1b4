package us.zoom.cube.site.lib.output.catalog;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ErrorInfo {
    private String exceptionType;
    private Integer count;
    private Date lastSeenUtc;
    private Date firstSeenUtc;
    private String errorMessage;
    private String sampleStackTrace;
    private Map<String, Object> extraTags;
} 