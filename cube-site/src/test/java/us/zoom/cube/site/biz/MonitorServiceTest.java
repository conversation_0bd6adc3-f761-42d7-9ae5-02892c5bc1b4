package us.zoom.cube.site.biz;


import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import us.zoom.cube.site.CubeSummary;
import us.zoom.cube.site.api.web.StatusEnum;
import us.zoom.cube.site.lib.ResponseObject;
import us.zoom.cube.site.mock.DataMock;

public class MonitorServiceTest extends  BaseTest{

    @Autowired
    MonitorService monitorService;


    @Test
    public void testAll() throws Exception {

        CubeSummary summary=super.buildSummary(ALARM);
        ResponseObject responseObject=monitorService.findByAlarmNameLike(DataMock.mockMonitorQuery(summary.getTenantId(),summary.getUserId()));
        Assertions.assertTrue(StatusEnum.SUCCESS.getStatus().equals(responseObject.getStatus()));
    }



}
