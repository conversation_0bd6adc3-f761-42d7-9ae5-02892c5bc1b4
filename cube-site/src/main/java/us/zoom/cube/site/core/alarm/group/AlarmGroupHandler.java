package us.zoom.cube.site.core.alarm.group;

import com.okta.commons.lang.Collections;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import us.zoom.infra.dao.model.AlarmGroupDO;
import us.zoom.infra.dao.service.AlarmGroupAlarmItemDAO;
import us.zoom.infra.dao.service.AlarmGroupDAO;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * @Author: <EMAIL>
 * @ModuleOwner: <EMAIL>
 * @Date: 01/05/2023 19:33
 * @Description:
 */
@Service
public class AlarmGroupHandler {
    @Autowired
    private AlarmGroupDAO alarmGroupDAO;
    @Autowired
    private AlarmGroupAlarmItemDAO alarmGroupAlarmItemDAO;

    public List<AlarmGroupDO> findByLikeParamInPage(String name, int pageIndex, int pageSize) {
        return alarmGroupDAO.findByLikeParamInPage(name, pageIndex, pageSize);
    }


    public List<AlarmGroupDO> findByLikeParam(String name) {
        return alarmGroupDAO.findByLikeParam(name);
    }

    public int getCountByLikeParam(String name) {
        return alarmGroupDAO.getCountByLikeParam(name);
    }

    public AlarmGroupDO findById(String id) {
        return alarmGroupDAO.findById(id);
    }

    public AlarmGroupDO findByName(String name) {
        return alarmGroupDAO.findByName(name);
    }

    public void deleteById(String id) {
        alarmGroupDAO.deleteById(id);
    }

    public int insert(AlarmGroupDO alarmGroupDO) {
        return alarmGroupDAO.insert(alarmGroupDO);
    }

    public int batchInsert(List<AlarmGroupDO> alarmGroupDOList) {
        if (Collections.isEmpty(alarmGroupDOList)) {
            return 0;
        }
        return alarmGroupDAO.batchInsert(alarmGroupDOList);
    }

    public List<AlarmGroupDO> findByIdList(List<String> idList) {
        if (Collections.isEmpty(idList)) {
            return new ArrayList<>();
        }
        return alarmGroupDAO.findByIdList(idList);
    }

    public int update(AlarmGroupDO alarmGroupDO) {
        return alarmGroupDAO.update(alarmGroupDO);
    }

    public void deleteInvalidAlarmItemList() {
        Optional.ofNullable(alarmGroupDAO.findNotExistAlarmId())
                .filter(CollectionUtils::isNotEmpty)
                .ifPresent(alarmGroupAlarmItemDAO::batchDeleteByAlarmId);
    }
}
