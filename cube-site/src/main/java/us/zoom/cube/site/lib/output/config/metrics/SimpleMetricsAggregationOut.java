package us.zoom.cube.site.lib.output.config.metrics;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import org.springframework.beans.BeanUtils;
import us.zoom.cube.site.lib.output.agg.MetricsAggregationRuleComposeOutput;
import us.zoom.infra.dao.model.MetricsAggregationDO;
import us.zoom.infra.dao.model.MetricsDO;

/**
 * @<PERSON> <PERSON>
 * @create 2020/7/19 10:11 AM
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SimpleMetricsAggregationOut {

    String metricsName;

    Integer aggPeriod;

    Integer subPeriod;

    String filterCondition;

    public static SimpleMetricsAggregationOut of(MetricsDO metricsDO, MetricsAggregationDO metricsAggregationDO){
        SimpleMetricsAggregationOut simpleMetricsAggregationOut = new SimpleMetricsAggregationOut();
        simpleMetricsAggregationOut.setMetricsName(metricsDO.getMetricsName());
        simpleMetricsAggregationOut.setAggPeriod(metricsAggregationDO.getAggPeriod());
        simpleMetricsAggregationOut.setFilterCondition(metricsAggregationDO.getFilterCondition());
        simpleMetricsAggregationOut.setSubPeriod(metricsAggregationDO.getSubPeriod());
        return simpleMetricsAggregationOut;
    }


}
