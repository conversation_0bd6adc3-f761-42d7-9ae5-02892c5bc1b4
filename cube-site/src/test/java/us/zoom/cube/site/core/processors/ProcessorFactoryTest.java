package us.zoom.cube.site.core.processors;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import us.zoom.cube.lib.hub.ProcessorTypeEnum;
import us.zoom.cube.site.core.parser.process.core.processor.CustomerLabelProcessor;
import us.zoom.cube.site.core.parser.process.core.processor.FilterProcessor;
import us.zoom.cube.site.core.parser.process.core.processor.GroovyProcessor;
import us.zoom.cube.site.core.parser.process.core.processor.IpProcessor;
import us.zoom.cube.site.core.parser.process.core.processor.ProcessorFactory;
import us.zoom.cube.site.core.parser.process.core.processor.ReMapperProcessor;
import us.zoom.cube.site.core.parser.process.core.processor.TimestampProcessor;

/**
 * @Author: <EMAIL>
 * @ModuleOwner: <EMAIL>
 * @Date: 01/11/2023 17:14
 * @Description:
 */
public class ProcessorFactoryTest {
    @Test
    public void test() {
        Assertions.assertEquals(ProcessorFactory.getInstance(ProcessorTypeEnum.FilterProcessorCfg.name()) instanceof FilterProcessor, true);
        Assertions.assertEquals(ProcessorFactory.getInstance(ProcessorTypeEnum.RemapperProcessorCfg.name()) instanceof ReMapperProcessor, true);
        Assertions.assertEquals(ProcessorFactory.getInstance(ProcessorTypeEnum.GroovyProcessorCfg.name()) instanceof GroovyProcessor, true);
        Assertions.assertEquals(ProcessorFactory.getInstance(ProcessorTypeEnum.IpProcessorCfg.name()) instanceof IpProcessor, true);
        Assertions.assertEquals(ProcessorFactory.getInstance(ProcessorTypeEnum.TimestampProcessorCfg.name()) instanceof TimestampProcessor, true);
        Assertions.assertEquals(ProcessorFactory.getInstance(ProcessorTypeEnum.CustomerLabelProcessorCfg.name()) instanceof CustomerLabelProcessor, true);

        int h1 = ProcessorFactory.getInstance(ProcessorTypeEnum.FilterProcessorCfg.name()).hashCode();
        int h2 = ProcessorFactory.getInstance(ProcessorTypeEnum.FilterProcessorCfg.name()).hashCode();
        int h3 = ProcessorFactory.getInstance(ProcessorTypeEnum.FilterProcessorCfg.name()).hashCode();
        int h4 = ProcessorFactory.getInstance(ProcessorTypeEnum.FilterProcessorCfg.name()).hashCode();
        Assertions.assertEquals(h1, h2);
        Assertions.assertEquals(h1, h3);
        Assertions.assertEquals(h1, h4);
        Assertions.assertEquals(h2, h3);
        Assertions.assertEquals(h2, h4);
        Assertions.assertEquals(h3, h4);
    }

    @Test
    public void testNull() {
        Assertions.assertEquals(ProcessorFactory.getInstance("FilterProcessorCfg1"), null);
    }
}
