 package us.zoom.cube.site.core.parser.process.core.dataobject;

import org.apache.commons.lang3.StringUtils;
import us.zoom.cube.lib.common.QueueTypeEnum;

public class OutputQueueDO {

    private QueueTypeEnum alarmQueueType;

    private String alarmTopic;

    private String alarmCluster;

    private QueueTypeEnum calcQueueType;

    private String calcTopic;

    private String calcCluster;

    private boolean isAlarmQueueExist = true;

    private boolean isCalcQueueExist = true;

    public OutputQueueDO(QueueTypeEnum alarmQueueType, String alarmTopic, String alarmCluster, QueueTypeEnum calcQueueType, String calcTopic, String calcCluster) {
        update(alarmQueueType, alarmTopic, alarmCluster,calcQueueType, calcTopic, calcCluster);
    }

    public void update(QueueTypeEnum alarmQueueType, String alarmTopic, String alarmCluster, QueueTypeEnum calcQueueType, String calcTopic, String calcCluster) {
        this.alarmQueueType = alarmQueueType;
        this.alarmTopic = alarmTopic;
        this.alarmCluster = alarmCluster;
        if (StringUtils.isBlank(alarmTopic) || StringUtils.isBlank(alarmCluster)) {
            isAlarmQueueExist = false;
        }
        this.calcQueueType = calcQueueType;
        this.calcTopic = calcTopic;
        this.calcCluster = calcCluster;
        if (StringUtils.isBlank(calcTopic) || StringUtils.isBlank(calcCluster)) {
            isCalcQueueExist = false;
        }
    }

    public boolean isAlarmQueueExist() {
        return isAlarmQueueExist;
    }

    public boolean isCalcQueueExist() {
        return isCalcQueueExist;
    }

    public QueueTypeEnum getAlarmQueueType() {
        return alarmQueueType;
    }

    public String getAlarmTopic() {
        return alarmTopic;
    }

    public String getAlarmCluster() {
        return alarmCluster;
    }

    public QueueTypeEnum getCalcQueueType() {
        return calcQueueType;
    }

    public String getCalcTopic() {
        return calcTopic;
    }

    public String getCalcCluster() {
        return calcCluster;
    }
}

