package us.zoom.cube.site.core;

import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import us.zoom.infra.dao.model.DataParserLabelRedisProcessorDO;
import us.zoom.infra.dao.service.DataParserLabelRedisProcessorDAO;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2020/5/29 5:54 AM
 */
@Component
public class DataParserLabelRedisProcessorHandler {

    @Autowired
    private DataParserLabelRedisProcessorDAO dataParserLabelRedisProcessorDAO;

    public void addLabelRedisProcessor(DataParserLabelRedisProcessorDO labelRedisProcessorDO) {
        dataParserLabelRedisProcessorDAO.add(labelRedisProcessorDO);
    }

    public DataParserLabelRedisProcessorDO getLabelRedisProcessorById(String id) {
        Assert.notNull(id, "id is null !");
        return dataParserLabelRedisProcessorDAO.getLabelProcessorById(id);
    }

    public void editLabelProcessor(DataParserLabelRedisProcessorDO labelRedisProcessorDO) {
        Assert.notNull(labelRedisProcessorDO, "label redis processor   is null !");
        dataParserLabelRedisProcessorDAO.edit(labelRedisProcessorDO);
    }

    public void delLabelRedisProcessor(String id) {
        Assert.notNull(id,"id is null !");
        dataParserLabelRedisProcessorDAO.delete(id);
    }

    public void delLabelRedisProcessorByDataParserPipelineId(String dataParserPipelineId) {
        Assert.notNull(dataParserPipelineId, "dataParserPipelineId is null !");
        dataParserLabelRedisProcessorDAO.deleteByDataParserPipelineId(dataParserPipelineId);
    }

    public List<DataParserLabelRedisProcessorDO> listByPipelineIds(List<String> pipeLineIds) {
        List<DataParserLabelRedisProcessorDO> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(pipeLineIds)) {
            return result;
        }
        return dataParserLabelRedisProcessorDAO.listByPipelineIds(pipeLineIds);
    }

    public void delLabelRedisProcessorByPipelineIds(List<String> pipeLineIds) {
        if(CollectionUtils.isEmpty(pipeLineIds)){
            return;
        }
        dataParserLabelRedisProcessorDAO.delByPipelindIds(pipeLineIds);
    }
}
