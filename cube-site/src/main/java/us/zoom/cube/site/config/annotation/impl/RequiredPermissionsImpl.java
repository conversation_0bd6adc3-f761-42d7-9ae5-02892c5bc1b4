package us.zoom.cube.site.config.annotation.impl;

import lombok.RequiredArgsConstructor;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import us.zoom.cube.site.biz.AuthService;
import us.zoom.cube.site.lib.BasePara;

/**
 * @description:
 * @author: <EMAIL>
 * @date: 2024-07-17 15:49
 **/
@Aspect
@Component
@RequiredArgsConstructor
public class RequiredPermissionsImpl {

    private final AuthService authService;

    @Pointcut("@annotation(us.zoom.cube.site.config.annotation.RequiredPermissions)")
    public void pointCut() {

    }

    @Around(value = "pointCut()")
    public Object Around(ProceedingJoinPoint point) throws Throwable {
        BasePara basePara = new BasePara();
        authService.checkAuth(basePara);
        Assert.isTrue(authService.canOperate(basePara.getUserId(), basePara.getTenantId()),
                String.format("You don't have permission to operate this service:[%s]", basePara.getTenantId()));
        return point.proceed();
    }
}
