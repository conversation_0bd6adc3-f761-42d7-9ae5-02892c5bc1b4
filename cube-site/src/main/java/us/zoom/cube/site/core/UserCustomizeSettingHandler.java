package us.zoom.cube.site.core;

import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import us.zoom.infra.dao.model.UserCustomizeSettingDO;
import us.zoom.infra.dao.param.UserCustomizeSettingSearchParam;
import us.zoom.infra.dao.service.UserCustomizeSettingDAO;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class UserCustomizeSettingHandler {

    private Logger monitorLog = LoggerFactory.getLogger("Monitor");

    @Autowired
    private UserCustomizeSettingDAO userCustomizeSettingDAO;

    public void addUserCustomizeSetting(UserCustomizeSettingDO userCustomizeSettingDO) {
        userCustomizeSettingDAO.add(userCustomizeSettingDO);
    }

    public void deleteUserCustomizeSetting(String id) {
        userCustomizeSettingDAO.deleteById(id);
    }

    public List<UserCustomizeSettingDO> findByParam(UserCustomizeSettingSearchParam param) {
        return userCustomizeSettingDAO.findByParam(param);
    }

    public int getCountByParam(UserCustomizeSettingSearchParam param) {
        return userCustomizeSettingDAO.getCountByParam(param);
    }

    public void editUserCustomizeSetting(UserCustomizeSettingDO userCustomizeSettingDO) {
        userCustomizeSettingDAO.edit(userCustomizeSettingDO);

    }

    public UserCustomizeSettingDO getUserCustomizeSetting(String id) {
        return userCustomizeSettingDAO.getById(id);
    }

    public void unsetDefault(String userId, int type) {
        userCustomizeSettingDAO.unsetDefault(userId, type);
    }

    public UserCustomizeSettingDO getDefault(String userId, int type, String relatedId) {
        return userCustomizeSettingDAO.getDefault(userId, type, relatedId);
    }

}
