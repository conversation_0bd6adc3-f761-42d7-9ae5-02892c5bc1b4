package us.zoom.cube.site.core;

import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import us.zoom.infra.dao.model.AggregationCustomFieldRuleDO;
import us.zoom.infra.dao.model.OriginalCustomFieldRuleDO;
import us.zoom.infra.dao.service.AggregationCustomFieldRuleDAO;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2020/7/19 10:20 AM
 */
@Service
public class AggregationCustomFieldRuleHandler {

    @Autowired
    private AggregationCustomFieldRuleDAO aggregationCustomFieldRuleDAO;

    public int insertCustomFieldRule(AggregationCustomFieldRuleDO aggregationCustomFieldRuleDO){
        return aggregationCustomFieldRuleDAO.insertCustomFieldRule(aggregationCustomFieldRuleDO);
    }

    public int batchInsertCustomFieldRule(List<AggregationCustomFieldRuleDO> aggregationCustomFieldRuleDOList){
        if(CollectionUtils.isEmpty(aggregationCustomFieldRuleDOList)){
            return 0;
        }
        return aggregationCustomFieldRuleDAO.batchInsertCustomFieldRule(aggregationCustomFieldRuleDOList);
    }

    public List<AggregationCustomFieldRuleDO> listRulesByAggIds(List<String> aggIds){
        if(CollectionUtils.isEmpty(aggIds)){
            return Lists.newArrayList();
        }
        return aggregationCustomFieldRuleDAO.listRulesByAggIds(aggIds);
    }

    public void deleteByAggId(String aggId){
        aggregationCustomFieldRuleDAO.deleteByAggId(aggId);
    }

    public void deleteByAggIds(List<String> aggIds){
        aggregationCustomFieldRuleDAO.deleteByAggIds(aggIds);
    }

    public List<AggregationCustomFieldRuleDO> listAll(){
        return aggregationCustomFieldRuleDAO.listAll();
    }


    public void deleteByMetricsFieldIds(List<String> metricsFieldIds){
        if(CollectionUtils.isEmpty(metricsFieldIds)){
            return;
        }
        aggregationCustomFieldRuleDAO.deleteByMetricsFieldIds(metricsFieldIds);
    }

    public int updateCustomFieldRule(AggregationCustomFieldRuleDO aggregationCustomFieldRuleDO){
        return aggregationCustomFieldRuleDAO.updateCustomFieldRule(aggregationCustomFieldRuleDO);
    }


    public int batchUpdateCustomFieldRule(List<AggregationCustomFieldRuleDO> aggregationCustomFieldRuleDOList){
        if(CollectionUtils.isEmpty(aggregationCustomFieldRuleDOList)){
            return 0;
        }
        return aggregationCustomFieldRuleDAO.batchUpdateCustomFieldRule(aggregationCustomFieldRuleDOList);
    }

    public void batchDeleteByIdList(List<String> idList){
        if(!CollectionUtils.isEmpty(idList)) {
            aggregationCustomFieldRuleDAO.batchDeleteByIdList(idList);
        }
    }

}
