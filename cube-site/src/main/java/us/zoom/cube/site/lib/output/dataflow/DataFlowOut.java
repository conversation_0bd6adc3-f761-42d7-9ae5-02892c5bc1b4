package us.zoom.cube.site.lib.output.dataflow;

import org.springframework.format.annotation.DateTimeFormat;
import us.zoom.cube.site.lib.input.AreaCfgInput;
import us.zoom.cube.site.lib.input.InputTopicProfileAreaCfgs;
import us.zoom.infra.enums.CommonStatusEnum;

import java.util.Date;
import java.util.List;

/**
 * @Author: <EMAIL>
 * @ModuleOwner: <EMAIL>
 * @Date: 03/10/2022 14:45
 * @Description:
 */
public class DataFlowOut {
    private String id;
    private String name;
    private String group;
    /**
     * @see CommonStatusEnum
     */
    private String status;
    private String scriptMethod;
    private String scriptContent;
    private String topicTemplate;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date modifyTime;

    private String createUserName;

    private String editUserName;

    public String getCreateUserName() {
        return createUserName;
    }

    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }

    public String getEditUserName() {
        return editUserName;
    }

    public void setEditUserName(String editUserName) {
        this.editUserName = editUserName;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getGroup() {
        return group;
    }

    public void setGroup(String group) {
        this.group = group;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getScriptMethod() {
        return scriptMethod;
    }

    public void setScriptMethod(String scriptMethod) {
        this.scriptMethod = scriptMethod;
    }

    public String getScriptContent() {
        return scriptContent;
    }

    public void setScriptContent(String scriptContent) {
        this.scriptContent = scriptContent;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getTopicTemplate() {
        return topicTemplate;
    }

    public void setTopicTemplate(String topicTemplate) {
        this.topicTemplate = topicTemplate;
    }
}
