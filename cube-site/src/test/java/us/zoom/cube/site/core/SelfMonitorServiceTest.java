package us.zoom.cube.site.core;


import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import us.zoom.cube.lib.common.StaticsModel;
import us.zoom.cube.site.CubeSiteApplicationTests;
import us.zoom.cube.site.core.monitor.SelfMonitorService;
import us.zoom.infra.utils.DateUtils;
import us.zoom.infra.utils.IpUtils;

import java.util.ArrayList;
import java.util.List;

public class SelfMonitorServiceTest extends CubeSiteApplicationTests {
    @Autowired
    private SelfMonitorService selfMonitorService;

    @Test
    public void test(){
        List<StaticsModel> staticsModels=new ArrayList<>();
        StaticsModel staticsModel=new StaticsModel(DateUtils.getPeriodEndTime(System.currentTimeMillis(),60),"test", IpUtils.getLocalIP());
        staticsModel.setAvgTime(3.3);
        staticsModel.setCount(34L);
        staticsModel.setIp("*******");
        staticsModel.setMaxTime(34345L);
        staticsModel.setMinTime(1L);
        staticsModel.setOtherLevel(234L);
        staticsModel.setP5Level(34L);
        staticsModel.setP10Level(12312L);
        staticsModel.setP20Level(3434L);
        staticsModel.setPeriod(System.currentTimeMillis());
        staticsModel.setTid(1L);
        staticsModel.setOtherTag("roy2");
        //staticsModel.setOther2Tag("roy");
        staticsModels.add(staticsModel);
        selfMonitorService.statics(staticsModels);
    }

}
