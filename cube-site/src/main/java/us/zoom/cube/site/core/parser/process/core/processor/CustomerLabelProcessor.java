package us.zoom.cube.site.core.parser.process.core.processor;

import org.apache.commons.lang3.StringUtils;
import us.zoom.cube.lib.hub.BaseProcessorCfg;
import us.zoom.cube.lib.hub.CustomerLabelInfoSource;
import us.zoom.cube.lib.hub.CustomerLabelInfoSourceType;
import us.zoom.cube.lib.hub.CustomerLabelProcessorCfg;
import us.zoom.cube.site.core.parser.process.core.common.enums.MonitoringLogType;

import java.util.List;
import java.util.Map;


/**
 * @Author: <EMAIL>
 * @ModuleOwner: <EMAIL>
 * @Date: 05/06/2024 15:30
 * @Description:
 */
public class CustomerLabelProcessor extends Processor {

    public CustomerLabelProcessor() {
        super.type = MonitoringLogType.customerLabelProcessorFail;
    }

    @Override
    public ProcessorResp process(Map<String, Object> messageMap, BaseProcessorCfg processorCfg) {
        ProcessorResp resp = new ProcessorResp(true,messageMap);
        try {
            CustomerLabelProcessorCfg customerLabelProcessorCfg = (CustomerLabelProcessorCfg) processorCfg;

            String dataSource = customerLabelProcessorCfg.getDataSource();
            Map<String, Map<String, Object>> customerAttributes;
            if (StringUtils.equals(dataSource, CustomerLabelInfoSourceType.Custom.name())) {
                customerAttributes = customerLabelProcessorCfg.getCustomData();
            } else {
                CustomerLabelInfoSource customerLabelInfoSource = CustomerProcessorCache.getInstance().getProcessorCacheDataMap().get(dataSource);
                if (customerLabelInfoSource == null || customerLabelInfoSource.getCustomerAttributes() == null) {
                    resp.setOutputMap(messageMap);
                    return resp;
                }
                customerAttributes = customerLabelInfoSource.getCustomerAttributes();
            }

            Object sourceFiledValue = messageMap.get(customerLabelProcessorCfg.getSourceField());
            if (sourceFiledValue == null) {
                resp.setOutputMap(messageMap);
                return resp;
            }
            Map<String, Object> customerLabels = customerAttributes.get(String.valueOf(sourceFiledValue));
            if (customerLabels == null) {
                resp.setOutputMap(messageMap);
                return resp;
            }
            List<String> selectedLabels = customerLabelProcessorCfg.getSelectedLabels();
            selectedLabels.forEach(selectedLabel -> {
                messageMap.put(customerLabelProcessorCfg.getFiledPrefix() + selectedLabel, customerLabels.get(selectedLabel));
            });

            logSuccess(messageMap,resp);
        } catch (Exception e) {
            logException(e, processorCfg,resp);
        }finally {
            logProcessor(processorCfg,resp);
        }
        return resp;
    }

}
