package us.zoom.cube.site.core;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import us.zoom.infra.dao.model.DataParserExpressionProcessorDO;
import us.zoom.infra.dao.service.DataParserExpressionProcessorDAO;

import java.util.Arrays;
import java.util.List;

@Component
public class DataParserExpressionProcessorHandler {
    @Autowired
    private DataParserExpressionProcessorDAO dataParserExpressionProcessorDAO;

    public void addProcessor(DataParserExpressionProcessorDO expressionProcessorDO) {
        Assert.notNull(expressionProcessorDO, "expression processor is null !");
        dataParserExpressionProcessorDAO.add(expressionProcessorDO);
    }

    public void editProcessor(DataParserExpressionProcessorDO expressionProcessorDO) {
        Assert.notNull(expressionProcessorDO, "expression processor is null !");
        dataParserExpressionProcessorDAO.edit(expressionProcessorDO);
    }

    public void delProcessor(String id) {
        Assert.notNull(id, "id is null !");
        dataParserExpressionProcessorDAO.del(id);
    }


    public DataParserExpressionProcessorDO getById(String id) {
        Assert.notNull(id, "id is null !");
        return dataParserExpressionProcessorDAO.getById(id);
    }

    public List<DataParserExpressionProcessorDO> listByPipelineIds(List<String> pipeLineIds) {
        return dataParserExpressionProcessorDAO.listByPipelineIds(pipeLineIds);
    }

    public void delByPipelineId(String pipelineId) {
        Assert.notNull(pipelineId, "pipelineId is null !");
        dataParserExpressionProcessorDAO.delByPipelineIds(Arrays.asList(pipelineId));
    }

    public void delByPipelineIds(List<String> pipelineIds) {
        if (CollectionUtils.isEmpty(pipelineIds)) {
            return;
        }
        dataParserExpressionProcessorDAO.delByPipelineIds(pipelineIds);
    }
}
