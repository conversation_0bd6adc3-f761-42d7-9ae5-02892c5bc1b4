package us.zoom.cube.site.core.model.resource;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;
import java.io.Serializable;
import java.util.List;


@XmlAccessorType(XmlAccessType.FIELD)
// XML
@XmlRootElement(name = "apps")
// JAXB
@XmlType(propOrder = {
        "app",
})
public class FlinkYarnApps implements Serializable {

    public FlinkYarnApps(){

    }
    public FlinkYarnApps(List<FlinkYarnApp> app) {
        this.app = app;
    }

    private List<FlinkYarnApp> app;

    public List<FlinkYarnApp> getApp() {
        return app;
    }

    public void setApp(List<FlinkYarnApp> app) {
        this.app = app;
    }
}
