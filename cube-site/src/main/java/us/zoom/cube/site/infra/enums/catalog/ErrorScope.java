package us.zoom.cube.site.infra.enums.catalog;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 */
public enum ErrorScope {
    LOG("log"),
    TRACE("trace");

    private final String value;

    ErrorScope(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public static ErrorScope fromString(String value) {
        for (ErrorScope scope : ErrorScope.values()) {
            if (StringUtils.equals(scope.getValue(), value)) {
                return scope;
            }
        }
        return null;
    }

    public static boolean isValid(String value) {
        return fromString(value) != null;
    }
} 