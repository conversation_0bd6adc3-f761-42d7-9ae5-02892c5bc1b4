package us.zoom.cube.site.biz;


import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import us.zoom.cube.site.CubeSiteApplicationTests;
import us.zoom.cube.site.lib.ResponseObject;
import us.zoom.cube.site.lib.query.NameQuery;
import us.zoom.cube.site.lib.query.PageQuery;

/**
 * <AUTHOR>
 * @create 2020/8/14 4:58 PM
 */
public class AsyncQueueGroupServiceTest extends CubeSiteApplicationTests {

    @Autowired
    private AsyncQueueGroupService asyncQueueGroupService;

    @Test
    public void searchAqGroup() {
        PageQuery<NameQuery> pageQuery = new PageQuery<>();
        NameQuery nameQuery = new NameQuery();
        nameQuery.setName("dev");
        pageQuery.setQueryPara(nameQuery);
        pageQuery.setTenantId("WEB1");
        pageQuery.setUserId("2565ab89-d345-49e4-9548-9066da3001db");
        ResponseObject result = asyncQueueGroupService.searchAqGroup(pageQuery);
        System.out.println(result);
    }
}
