package us.zoom.cube.site.lib.output.alarm;

import com.zoom.op.monitor.domain.alarm.RuleCondition;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @author: <PERSON><PERSON> Ding
 * @date: 2022/10/17 17:07
 * @desc:
 */
@Data
public class AlarmRecordPageQueryOutput {

    private String id;

    private String alarmLevel;

    private String alarmId;

    private String alarmName;

    private String alarmRuleId;

    //pending/alarming/resolved
    private String status;

    private boolean canMarkResolved;

    private boolean hasSubAlarm;

    private String subAlarmId;

    private String subAlarmName;

    private Map metricsTags;

    private String notificationTitle;

    private String notificationContent;

    private String serviceName;

    private String metricsName;

    //even if true doesn't means user had received the alarm
    private String sendingStatus;

    private long ts;

    private long createTime;

    //for validation
    private boolean isLastCountMatched;
    //for validation
    private boolean isNoticed;

    //for alarm record detail
    private List<RuleCondition> hitRule;
    //for alarm record detail
    private Object noticedChannelList;
    //for alarm record detail
    private List<Map<Object, Object>> dashboardList = new ArrayList<>();
    //for alarm record detail
    private Object hostDetailTagValue;

    // Access & PII
    private boolean isPiiMetric;
    private boolean hasAccess;

}
