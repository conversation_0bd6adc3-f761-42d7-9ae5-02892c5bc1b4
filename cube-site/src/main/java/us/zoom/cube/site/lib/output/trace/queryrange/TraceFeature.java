package us.zoom.cube.site.lib.output.trace.queryrange;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

import static org.apache.commons.lang3.StringUtils.EMPTY;

/**
 * @author: eason.jia
 * @date: 2024/8/28
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TraceFeature {
    private String name;
    private boolean active;
    private long usage;
    @JsonProperty("usage_limit")
    private long usageLimit;
    private String route;

    @JsonIgnore
    public static final List<TraceFeature> DEFAULT_FEATURE_SET = new ArrayList<>();

    static {
        DEFAULT_FEATURE_SET.add(TraceFeature.builder().name("SSO").active(false).usage(0).usageLimit(-1).route(EMPTY).build());
        DEFAULT_FEATURE_SET.add(TraceFeature.builder().name("OSS").active(false).usage(0).usageLimit(-1).route(EMPTY).build());
        DEFAULT_FEATURE_SET.add(TraceFeature.builder().name("DISABLE_UPSELL").active(false).usage(0).usageLimit(-1).route(EMPTY).build());
        DEFAULT_FEATURE_SET.add(TraceFeature.builder().name("SMART_TRACE_DETAIL").active(false).usage(0).usageLimit(-1).route(EMPTY).build());
        DEFAULT_FEATURE_SET.add(TraceFeature.builder().name("CUSTOM_METRICS_FUNCTION").active(false).usage(0).usageLimit(-1).route(EMPTY).build());
        DEFAULT_FEATURE_SET.add(TraceFeature.builder().name("QUERY_BUILDER_PANELS").active(false).usage(0).usageLimit(-1).route(EMPTY).build());
        DEFAULT_FEATURE_SET.add(TraceFeature.builder().name("QUERY_BUILDER_ALERTS").active(false).usage(0).usageLimit(-1).route(EMPTY).build());
        DEFAULT_FEATURE_SET.add(TraceFeature.builder().name("ALERT_CHANNEL_SLACK").active(false).usage(0).usageLimit(-1).route(EMPTY).build());
        DEFAULT_FEATURE_SET.add(TraceFeature.builder().name("ALERT_CHANNEL_WEBHOOK").active(false).usage(0).usageLimit(-1).route(EMPTY).build());
        DEFAULT_FEATURE_SET.add(TraceFeature.builder().name("ALERT_CHANNEL_PAGERDUTY").active(false).usage(0).usageLimit(-1).route(EMPTY).build());
        DEFAULT_FEATURE_SET.add(TraceFeature.builder().name("ALERT_CHANNEL_OPSGENIE").active(false).usage(0).usageLimit(-1).route(EMPTY).build());
        DEFAULT_FEATURE_SET.add(TraceFeature.builder().name("ALERT_CHANNEL_EMAIL").active(false).usage(0).usageLimit(-1).route(EMPTY).build());
        DEFAULT_FEATURE_SET.add(TraceFeature.builder().name("ALERT_CHANNEL_MSTEAMS").active(false).usage(0).usageLimit(-1).route(EMPTY).build());
        DEFAULT_FEATURE_SET.add(TraceFeature.builder().name("USE_SPAN_METRICS").active(false).usage(0).usageLimit(-1).route(EMPTY).build());
        DEFAULT_FEATURE_SET.add(TraceFeature.builder().name("DurationSort").active(false).usage(0).usageLimit(-1).route(EMPTY).build());
        DEFAULT_FEATURE_SET.add(TraceFeature.builder().name("TimestampSort").active(false).usage(0).usageLimit(-1).route(EMPTY).build());
        DEFAULT_FEATURE_SET.add(TraceFeature.builder().name("PreferRPM").active(false).usage(0).usageLimit(-1).route(EMPTY).build());
    }
}
