package us.zoom.cube.site.external.metadata.enums;

public enum ServiceLevelEnum {
    PRODUCT(1, "Product"),
    SERVICE_GROUP(2, "ServiceGroup"),
    APPLICATION(3, "Application"),
    COMPONENT(4, "Component");

    private final Integer level;
    private final String description;

    ServiceLevelEnum(Integer level, String description) {
        this.level = level;
        this.description = description;
    }

    public Integer getLevel() {
        return level;
    }

    public String getDescription() {
        return description;
    }

    public static ServiceLevelEnum fromLevel(Integer level) {
        if (level == null) {
            return null;
        }
        for (ServiceLevelEnum serviceLevel : ServiceLevelEnum.values()) {
            if (serviceLevel.getLevel().equals(level)) {
                return serviceLevel;
            }
        }
        return null;
    }
} 