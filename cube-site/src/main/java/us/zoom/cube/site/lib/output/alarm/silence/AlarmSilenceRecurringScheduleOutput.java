package us.zoom.cube.site.lib.output.alarm.silence;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AlarmSilenceRecurringScheduleOutput {
    String startDate;
    String endDate;
    String startDayTime;
    String endDayTime;
    Integer repeatStep;
    Integer repeatType;
    String silenceDayOfWeek;
}
