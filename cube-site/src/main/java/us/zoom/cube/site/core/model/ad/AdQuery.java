package us.zoom.cube.site.core.model.ad;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import org.apache.commons.lang3.StringUtils;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2023-11-14 13:26
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AdQuery {
    private String adName;
    private String metricsName;
    private String fieldName;
    private Boolean adStatus;
    private List<String> labelIds;
    private String labelMatchType;

    public void adjust() {
        if (StringUtils.isEmpty(metricsName)) {
            metricsName = "";
        }
        metricsName = metricsName.trim();
        if (StringUtils.isEmpty(adName)) {
            adName = "";
        }
        adName = adName.trim();
        if (StringUtils.isEmpty(fieldName)) {
            fieldName = "";
        }
        fieldName = fieldName.trim();
    }
}
