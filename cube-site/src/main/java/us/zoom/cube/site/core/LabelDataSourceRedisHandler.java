package us.zoom.cube.site.core;

import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import us.zoom.infra.dao.model.LabelDataSourceRedisDO;
import us.zoom.infra.dao.service.LabelDataSourceRedisDAO;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2020/5/27 6:46 AM
 */
@Component
public class LabelDataSourceRedisHandler {

    @Autowired
    private LabelDataSourceRedisDAO labelDataSourceRedisDAO;

    public void addLabelDataSourceRedis(LabelDataSourceRedisDO labelDataSourceRedisDO) {
        labelDataSourceRedisDAO.addLabelDataSourceRedis(labelDataSourceRedisDO);
    }

    public LabelDataSourceRedisDO findByLabelDataSourceId(String labelDataSourceId) {
        return labelDataSourceRedisDAO.findByLabelDataSourceId(labelDataSourceId);
    }

    public void editLabelDataSourceRedis(LabelDataSourceRedisDO labelDataSourceRedisDO) {
        labelDataSourceRedisDAO.editLabelDataSourceRedis(labelDataSourceRedisDO);
    }

    public Map<String, LabelDataSourceRedisDO> listAll() {
        Map<String, LabelDataSourceRedisDO> resultMap = Maps.newHashMap();
        List<LabelDataSourceRedisDO> list = labelDataSourceRedisDAO.listAll();
        if (CollectionUtils.isEmpty(list)) {
            return resultMap;
        }
        resultMap = list.stream().collect(Collectors.toMap(LabelDataSourceRedisDO::getLabelDataSourceId, e -> e));
        return resultMap;
    }
}
