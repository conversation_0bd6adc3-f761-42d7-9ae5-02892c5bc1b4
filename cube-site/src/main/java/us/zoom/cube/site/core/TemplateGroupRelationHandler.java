package us.zoom.cube.site.core;

import cn.hutool.core.lang.Assert;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.site.infra.utils.IdUtils;
import us.zoom.infra.dao.model.TemplateGroupRelationDO;
import us.zoom.infra.dao.service.TemplateGroupRelationDAO;

import java.util.Collections;
import java.util.List;

/**
 * @Author: <PERSON>
 * @Date:12/14/2022 17:00
 * @Description:
 */
@Service
@RequiredArgsConstructor
public class TemplateGroupRelationHandler {

    private final TemplateGroupRelationDAO templateGroupRelationDAO;

    public String addTemplateGroupRelation(TemplateGroupRelationDO templateGroupRelation) {
        Assert.notNull(templateGroupRelation, "template group relation can't be null");
        Assert.isTrue(StringUtils.isNotEmpty(templateGroupRelation.getTemplateGroupId()), "template group id can't be null");
        Assert.isTrue(StringUtils.isNotEmpty(templateGroupRelation.getTemplateId()), "template id can't be null");

        templateGroupRelation.setId(IdUtils.generateId());
        templateGroupRelationDAO.addTemplate(templateGroupRelation);
        return templateGroupRelation.getId();
    }

    public void updateTemplateGroup(String templateId, String newTemplateGroupId) {
        Assert.isTrue(StringUtils.isNotEmpty(newTemplateGroupId), "template group id can't be null");
        Assert.isTrue(StringUtils.isNotEmpty(templateId), "template id can't be null");
        templateGroupRelationDAO.updateTemplateGroup(templateId, newTemplateGroupId);
    }

    public List<TemplateGroupRelationDO> listBy(String templateGroupId) {
        Assert.isTrue(StringUtils.isNotEmpty(templateGroupId), "template group id can't be null");
        List<TemplateGroupRelationDO> relations = templateGroupRelationDAO.listBy(templateGroupId);
        return relations;
    }

    public List<TemplateGroupRelationDO> listBy(List<String> groupIds) {
        if (CollectionUtils.isEmpty(groupIds)) {
            return Collections.emptyList();
        }
        List<TemplateGroupRelationDO> relations = templateGroupRelationDAO.listByGroupIds(groupIds);
        return relations;
    }

    public void deleteByTemplateId(String templateId) {
        templateGroupRelationDAO.deleteByTemplateId(templateId);
    }

    public List<TemplateGroupRelationDO> listByTemplateId(List<String> templateIds) {
        if (CollectionUtils.isEmpty(templateIds)) {
            return Collections.emptyList();
        }
        return templateGroupRelationDAO.listByTemplateIds(templateIds);
    }
}
