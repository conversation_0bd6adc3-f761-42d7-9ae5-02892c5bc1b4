package us.zoom.cube.site.core.parser.process.core.processor;

import us.zoom.cube.lib.hub.BaseProcessorCfg;
import us.zoom.cube.lib.hub.RemapperProcessorCfg;
import us.zoom.cube.site.core.parser.process.core.common.enums.MonitoringLogType;

import java.util.Map;

/**
 * @Author: <EMAIL>
 * @ModuleOwner: <EMAIL>
 * @Date: 01/11/2023 16:47
 * @Description:
 */
public class ReMapperProcessor extends Processor {

    public ReMapperProcessor() {
        super.type = MonitoringLogType.reMapperProcessorFail;
    }

    @Override
    public ProcessorResp process(Map<String, Object> messageMap, BaseProcessorCfg processorCfg) {
        ProcessorResp resp = new ProcessorResp(true,messageMap);
        try {
            RemapperProcessorCfg remapperProcessorCfg = (RemapperProcessorCfg) processorCfg;
            Map<String, String> fieldMap = remapperProcessorCfg.getFieldsMap();
            for (Map.Entry<String, String> entry : fieldMap.entrySet()) {
                String sourceField = entry.getKey();
                String targetField = entry.getValue();
                messageMap.put(targetField, messageMap.get(sourceField));
            }
            logSuccess(messageMap,resp);
        } catch (Exception e) {
            logException(e, processorCfg,resp);
        }finally {
            logProcessor(processorCfg,resp);
        }
        return resp;
    }
}
