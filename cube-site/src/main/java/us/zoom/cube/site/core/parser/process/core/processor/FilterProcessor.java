package us.zoom.cube.site.core.parser.process.core.processor;

import com.googlecode.aviator.AviatorEvaluator;
import org.apache.commons.lang3.StringUtils;
import us.zoom.cube.lib.hub.BaseProcessorCfg;
import us.zoom.cube.lib.hub.FilterProcessorCfg;
import us.zoom.cube.site.core.parser.process.core.common.enums.MonitoringLogType;
import us.zoom.cube.site.infra.enums.WebCodeEnum;
import us.zoom.cube.site.lib.SiteException;

import java.util.Map;

/**
 * @Author: <EMAIL>
 * @ModuleOwner: <EMAIL>
 * @Date: 01/11/2023 16:47
 * @Description:
 */
public class FilterProcessor extends Processor {

    public FilterProcessor() {
        super.type = MonitoringLogType.filterProcessorFail;
    }

    @Override
    public ProcessorResp process(Map<String, Object> messageMap, BaseProcessorCfg processorCfg) {
        ProcessorResp resp = new ProcessorResp(true,messageMap);
        try {
            FilterProcessorCfg filterProcessorCfg = (FilterProcessorCfg) processorCfg;
            String filterRule = filterProcessorCfg.getFilterRule();
            if (StringUtils.isBlank(filterRule)) {
                return resp;
            }
            resp.setContinue((boolean) AviatorEvaluator.execute(filterRule, messageMap, true));
            if(!resp.isContinue()){
                throw new SiteException(WebCodeEnum.DataParserPipelineProcessorFilterError.getCode(),WebCodeEnum.DataParserPipelineProcessorFilterError.getErrMsg());
            }
            logSuccess(messageMap,resp);
        } catch (Exception e) {
            logException(e, processorCfg,resp);
        }finally {
            logProcessor(processorCfg,resp);
        }
        return resp;
    }
}
