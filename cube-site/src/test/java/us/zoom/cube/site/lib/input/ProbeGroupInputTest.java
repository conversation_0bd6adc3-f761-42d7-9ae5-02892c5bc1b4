package us.zoom.cube.site.lib.input;

import com.google.gson.Gson;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import us.zoom.cube.site.infra.utils.IdUtils;
import us.zoom.infra.enums.CommonStatusEnum;

import java.util.Arrays;

/**
 * @Author: <EMAIL>
 * @ModuleOwner: <EMAIL>
 * @Date: 07/05/2022 17:14
 * @Description:
 */
public class ProbeGroupInputTest {
    private ProbeGroupInput input;

    @BeforeEach
    public void before() {
        input = new ProbeGroupInput();
        input.setId(IdUtils.generateId());
        input.setName("name");
        input.setCloud("cloud");
        input.setCountry("country");
        input.setCity("city");
        input.setOperator("operator");
        input.setThreadCount(1);
    }

    @Test
    public void printJson() {
        System.out.println(new Gson().toJson(input));
    }

    @Test
    public void testName1() {
        try {
            input.setName("");
            input.check();
            Assertions.fail("check fail");
        } catch (Exception e) {
            Assertions.assertEquals(e.getMessage(), "name is null or blank");
        }
    }

    @Test
    public void testName2() {
        try {
            input.setName(null);
            input.check();
            Assertions.fail("check fail");
        } catch (Exception e) {
            Assertions.assertEquals(e.getMessage(), "name is null or blank");
        }
    }

    @Test
    public void testName3() {
        try {
            StringBuilder name = new StringBuilder();
            for (int i = 0; i < 513; i++) {
                name.append("1");
            }
            input.setName(name.toString());
            input.check();
            Assertions.fail("check fail");
        } catch (Exception e) {
            Assertions.assertEquals(e.getMessage(), "Name length must be less than 512");
        }
    }

    @Test
    public void testCloud1() {
        try {
            input.setCloud("");
            input.check();
            Assertions.fail("check fail");
        } catch (Exception e) {
            Assertions.assertEquals(e.getMessage(), "cloud is null or blank");
        }
    }

    @Test
    public void testCloud2() {
        try {
            input.setCloud(null);
            input.check();
            Assertions.fail("check fail");
        } catch (Exception e) {
            Assertions.assertEquals(e.getMessage(), "cloud is null or blank");
        }
    }

    @Test
    public void testCloud3() {
        try {
            StringBuilder cloud = new StringBuilder();
            for (int i = 0; i < 65; i++) {
                cloud.append("1");
            }
            input.setCloud(cloud.toString());
            input.check();
            Assertions.fail("check fail");
        } catch (Exception e) {
            Assertions.assertEquals(e.getMessage(), "cloud length must be less than 64");
        }
    }

    @Test
    public void testCountry1() {
        try {
            input.setCountry("");
            input.check();
            Assertions.fail("check fail");
        } catch (Exception e) {
            Assertions.assertEquals(e.getMessage(), "country is null or blank");
        }
    }

    @Test
    public void testCountry2() {
        try {
            input.setCountry(null);
            input.check();
            Assertions.fail("check fail");
        } catch (Exception e) {
            Assertions.assertEquals(e.getMessage(), "country is null or blank");
        }
    }

    @Test
    public void testCountry3() {
        try {
            StringBuilder cloud = new StringBuilder();
            for (int i = 0; i < 257; i++) {
                cloud.append("1");
            }
            input.setCountry(cloud.toString());
            input.check();
            Assertions.fail("check fail");
        } catch (Exception e) {
            Assertions.assertEquals(e.getMessage(), "country length must be less than 256");
        }
    }

    @Test
    public void testCity1() {
        try {
            input.setCity("");
            input.check();
            Assertions.fail("check fail");
        } catch (Exception e) {
            Assertions.assertEquals(e.getMessage(), "city is null or blank");
        }
    }

    @Test
    public void testCity2() {
        try {
            input.setCity(null);
            input.check();
            Assertions.fail("check fail");
        } catch (Exception e) {
            Assertions.assertEquals(e.getMessage(), "city is null or blank");
        }
    }

    @Test
    public void testCity3() {
        try {
            StringBuilder cloud = new StringBuilder();
            for (int i = 0; i < 257; i++) {
                cloud.append("1");
            }
            input.setCity(cloud.toString());
            input.check();
            Assertions.fail("check fail");
        } catch (Exception e) {
            Assertions.assertEquals(e.getMessage(), "city length must be less than 256");
        }
    }

    @Test
    public void testOperator1() {
        input.setOperator(null);
        input.check();
        Assertions.assertEquals("", input.getOperator());
    }

    @Test
    public void testOperator2() {
        try {
            StringBuilder cloud = new StringBuilder();
            for (int i = 0; i < 257; i++) {
                cloud.append("1");
            }
            input.setOperator(cloud.toString());
            input.check();
            Assertions.fail("check fail");
        } catch (Exception e) {
            Assertions.assertEquals(e.getMessage(), "operator length must be less than 256");
        }
    }

    @Test
    public void testStatus() {
        input.setStatus("web");
        input.check();
        Assertions.assertEquals(CommonStatusEnum.enable.name(), input.getStatus());
    }

    @Test
    public void testThreadCount() {
        input.setThreadCount(null);
        input.check();
        Assertions.assertEquals(new Integer(1), input.getThreadCount());
    }
}
