package us.zoom.cube.site.lib.output.alarm;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.ArrayList;
import java.util.List;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@AllArgsConstructor
@NoArgsConstructor
public class MetricsAlarmsRelationOut {
    String metricsId;
    List<SimpleAlarm> alarms = new ArrayList<>();

}



