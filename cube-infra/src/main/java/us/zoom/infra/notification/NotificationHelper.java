package us.zoom.infra.notification;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import us.zoom.infra.influx.model.alarm.AlarmMatchRecord;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;

import static java.nio.charset.StandardCharsets.UTF_8;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class NotificationHelper {

    private String alarmDetailEndpoint;

    private String cubeIncidentUrlAlert;


    @Value("${alarm.details.endpoint:}")
    public void setAlarmDetailEndpoint(String alarmDetailEndpoint) {
        this.alarmDetailEndpoint = alarmDetailEndpoint;
    }

    @Value("${cube.incident.url.alert:}")
    public void setCubeIncidentUrlAlert(String cubeIncidentUrlAlert) {
        this.cubeIncidentUrlAlert = cubeIncidentUrlAlert;
    }

    public String buildAlarmDetailUrl(String newEndpoint, AlarmMatchRecord record) {
        String url = StringUtils.isNotEmpty(newEndpoint) ? newEndpoint : alarmDetailEndpoint;
        return url +
                "?tenantName=" + encode(record.getTenantName()) +
                "&name=" + encode(record.getAlarmName()) +
                "&begin=" + (record.getTime() - 1000L) +
                "&end=" + (record.getTime() + 1000L) +
                "&alarmLevel=" + record.getAlarmLevel().name().toLowerCase() +
                "&tenantId=" + record.getTenantId() +
                "&alarmId=" + record.getAlarmId();
    }

    public String buildAgentDetailUrl(String tenantId){
        return cubeIncidentUrlAlert+"agents"+
                "?serviceId=" + encode(tenantId)+
                "&status=2";
    }
    public String buildHubDetailUrl(){
        return cubeIncidentUrlAlert+"hubs";
    }

    public static String encode(String str) {
        try {
            return URLEncoder.encode(str, UTF_8);
        } catch (Exception e) {
            log.warn("Failed to encode parameters.", e);
        }
        return str;
    }

    public static String encodeUrlQueryString(String url) {
        int queryIndex = url.indexOf('?');
        if (queryIndex == -1) {
            return url; // No query string to encode
        }
        String path = url.substring(0, queryIndex + 1);
        String queryString = url.substring(queryIndex + 1);

        // Split the query string into parameters
        String[] params = queryString.split("&");
        StringBuilder encodedQueryString = new StringBuilder();

        for (String param : params) {
            int eqIndex = param.indexOf('=');
            if (eqIndex == -1) {
                // No '=' in parameter, encode the whole thing
                String encodedParam = encode(param);
                if (!encodedQueryString.isEmpty()) {
                    encodedQueryString.append("&");
                }
                encodedQueryString.append(encodedParam);
            } else {
                // Encode key and value separately
                String key = param.substring(0, eqIndex);
                String value = param.substring(eqIndex + 1);
                String encodedKey = encode(key);
                String encodedValue = encode(value);
                if (!encodedQueryString.isEmpty()) {
                    encodedQueryString.append("&");
                }
                encodedQueryString.append(encodedKey).append("=").append(encodedValue);
            }
        }

        return path + encodedQueryString;
    }

    public static String decode(String str) {
        try {
            return URLDecoder.decode(str, UTF_8);
        } catch (Exception e) {
            log.warn("Failed to decode parameters.", e);
        }
        return str;
    }

    public static void main(String[] args) {
        String originUrl = "https://linktrace.zoomdev.us/tracing/#/configuration/discover?params={\"r\":\"sss\",\"g\":\"\",\"cmdb\":[\"sssss\"],\"sts\":1.23231202121E11,\"ets\":1.23231262121E11,\"kql\":\"\\\"ssss\\\" AND \\\"ssss\\\"\",\"lucene\":null,\"step\":\"Hour\",\"f\":[],\"o\":[{\"name\":\"logdate\",\"direction\":\"desc\"}],\"c\":[],\"cur\":{\"label\":\"End date\",\"showLabel\":true,\"start\":{\"category\":\"absolute\",\"config\":{\"type\":\"last\",\"unit\":\"hours\",\"interval\":10,\"timestamp\":1.23231202121E11}},\"end\":{\"category\":\"absolute\",\"config\":{\"timestamp\":1.23231262121E11}},\"hasError\":false},\"tc\":\"cur\"}";
        System.out.println(encodeUrlQueryString(originUrl));
    }
}
