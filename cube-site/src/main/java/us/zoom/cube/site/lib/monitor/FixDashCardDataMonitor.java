package us.zoom.cube.site.lib.monitor;

import lombok.Builder;
import lombok.Data;
import us.zoom.cube.sdk.annotation.CubeMonitorLog;
import us.zoom.cube.sdk.annotation.Field;
import us.zoom.cube.sdk.annotation.Tag;


@Data
@Builder
@CubeMonitorLog(measure = "fix_dash_card_data")
public class FixDashCardDataMonitor {
    @Tag
    private String ip;
    @Field
    private String dashId;

    @Field
    private int fixDash;

    @Field
    private int fixCard;

}
