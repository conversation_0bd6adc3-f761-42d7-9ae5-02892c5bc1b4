package us.zoom.cube.site.core.parser.process.core.dataparser;

import com.google.common.collect.Maps;
import com.googlecode.aviator.AviatorEvaluator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.lib.common.CubeConstants;
import us.zoom.cube.lib.hub.BaseProcessorCfg;
import us.zoom.cube.lib.hub.DataParserPipelineCfg;
import us.zoom.cube.site.core.parser.process.core.common.enums.TimeTypeEnum;
import us.zoom.cube.site.core.parser.process.core.monitoring.MonitoringLogUtils;
import us.zoom.cube.site.core.parser.process.core.processor.Processor;
import us.zoom.cube.site.core.parser.process.core.processor.ProcessorFactory;
import us.zoom.cube.site.core.parser.process.core.processor.ProcessorResp;
import us.zoom.cube.site.core.parser.process.core.response.PipelineEnum;
import us.zoom.cube.site.core.parser.process.core.response.PipelineResp;
import us.zoom.cube.site.infra.enums.WebCodeEnum;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@Slf4j
public class PipelineHandler {

    private MonitoringLogUtils monitoringLogUtils = new MonitoringLogUtils();

    public PipelineHandler() {

    }

    public PipelineResp processForPipeline(Map<String, Object> messageMap, DataParserPipelineCfg pipelineCfg) {
        PipelineResp pipelineResponse = new PipelineResp();
        pipelineResponse.setInputMap(messageMap);
        Map<String, ProcessorResp> processorRespMap = Maps.newHashMap();
        try {
            // 1. pipeline processors
            List<BaseProcessorCfg> processorCfgs = pipelineCfg.getProcessores();
            // pipeline filter rule
            PipelineResp pipelineFilterResponse = pipelineFilter(messageMap, pipelineCfg, processorRespMap, pipelineResponse);
            if (pipelineFilterResponse != null){
                return pipelineFilterResponse;
            }
            if (!CollectionUtils.isEmpty(processorCfgs)) {
                for (BaseProcessorCfg processorCfg : processorCfgs) {
                    Processor processor = ProcessorFactory.getInstance(processorCfg.getType());
                    if (processor == null) {
                        continue;
                    }
                    String processorId = processorCfg.getId();
                    ProcessorResp resp = processor.process(messageMap, processorCfg);
                    processorRespMap.put(processorId,resp);
                    if (!resp.isContinue()) {
                        //just return error status, errorMessage in processorRespMap
                        pipelineResponse.setStatus(PipelineEnum.FAIL);
                        pipelineResponse.setInputMap(Collections.emptyMap());
                        pipelineResponse.setProcessorRespMap(processorRespMap);
                        return pipelineResponse;
                    }
                }
            }
            //check ts field miss
            PipelineResp pipelineTsResp = tsCheck(messageMap, pipelineResponse, processorRespMap);
            if (pipelineTsResp != null){
                return pipelineTsResp;
            }
            pipelineResponse.setProcessorRespMap(processorRespMap);
            pipelineResponse.setStatus(PipelineEnum.SUCCESS);
            if(MapUtils.isNotEmpty(processorRespMap)){
                pipelineResponse.setMessageMap(Collections.emptyMap());
                pipelineResponse.setInputMap(Collections.emptyMap());
            }else {
                if(PipelineEnum.SUCCESS.equals(pipelineResponse.getStatus())){
                    pipelineResponse.setMessageMap(messageMap);
                }
            }
            return pipelineResponse;
        } catch (Exception e) {
            pipelineResponse.setProcessorRespMap(processorRespMap);
            pipelineResponse.setStatus(PipelineEnum.FAIL);
            return pipelineResponse;
        }
    }

    @Nullable
    private PipelineResp tsCheck(Map<String, Object> messageMap, PipelineResp pipelineResponse, Map<String, ProcessorResp> processorRespMap) {
        TimeTypeEnum timeTypeEnum = getTimeType(messageMap.get(CubeConstants.TAG_TIMESTAMP));
        String tsStr = String.valueOf(messageMap.get(CubeConstants.TAG_TIMESTAMP));
        ProcessorResp resp = new ProcessorResp();
        resp.setId(UUID.randomUUID().toString());
        resp.setName("result check");
        resp.setInputMap(messageMap);

        resp.setOrder(Integer.MAX_VALUE);
        if (timeTypeEnum == TimeTypeEnum.inCorrect|| !isValidMilliTimestamp(tsStr)) {
            resp.setFailMessage("'ts' field missing—required for cube  build metrics.");
            processorRespMap.put(resp.getId(),resp);
            //just return error status, errorMessage in processorRespMap
            pipelineResponse.setStatus(PipelineEnum.FAIL);
            pipelineResponse.setInputMap(Collections.emptyMap());
            pipelineResponse.setProcessorRespMap(processorRespMap);
            return pipelineResponse;
        }else {
            resp.setOutputMap(messageMap);
            processorRespMap.put(resp.getId(),resp);
        }
        return null;
    }

    @Nullable
    private PipelineResp pipelineFilter(Map<String, Object> messageMap, DataParserPipelineCfg pipelineCfg, Map<String, ProcessorResp> processorRespMap, PipelineResp pipelineResponse) {
        ProcessorResp filterResp = new ProcessorResp();
        filterResp.setId(UUID.randomUUID().toString());
        filterResp.setName("filter rule");
        filterResp.setInputMap(messageMap);
        filterResp.setOrder(Integer.MIN_VALUE);
        if (isContinue(pipelineCfg.getFilterRule(), messageMap)) {
            filterResp.setOutputMap(messageMap);
            processorRespMap.put(filterResp.getId(),filterResp);
        }else {
            filterResp.setFailMessage(WebCodeEnum.DataParserPipelineFilterError.getErrMsg());
            processorRespMap.put(filterResp.getId(),filterResp);
            //just return error status, errorMessage in processorRespMap
            pipelineResponse.setStatus(PipelineEnum.FAIL);
            pipelineResponse.setInputMap(Collections.emptyMap());
            pipelineResponse.setProcessorRespMap(processorRespMap);
            return pipelineResponse;
        }
        return null;
    }

    protected TimeTypeEnum getTimeType(Object time) {
        try {
            long tmpTime = (long) time;
            long now = System.currentTimeMillis();
            int delayTime = (int) (now - tmpTime);
            if (delayTime < 0) {
                return TimeTypeEnum.future;
            }
        } catch (Exception e) {
            return TimeTypeEnum.inCorrect;
        }
        return TimeTypeEnum.correct;
    }

    private boolean isValidMilliTimestamp(String tsStr) {
        if (tsStr == null) {
            return false;
        }
        return tsStr.matches("^\\d{13}$");
    }

    protected boolean isContinue(String filterRule, Map<String, Object> messageMap) {
        try {
            if (StringUtils.isBlank(filterRule)) {
                return true;
            }
            return (Boolean) (boolean) AviatorEvaluator.execute(filterRule, messageMap, true);
        } catch (Exception e) {
            return false;
        }
    }
}