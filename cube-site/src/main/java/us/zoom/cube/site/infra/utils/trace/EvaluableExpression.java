package us.zoom.cube.site.infra.utils.trace;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

/**
 * @author: eason.jia
 * @date: 2024/8/19
 */
public class EvaluableExpression {

//    // Represents the query format used to output dates. Typically only used when creating SQL or Mongo queries from an expression.
//    // Defaults to the complete ISO8601 format, including nanoseconds.
//    public static final String isoDateFormat = "2006-01-02T15:04:05.999999999Z0700";
//
//    // Short-circuit holder value
//    public static final int shortCircuitHolder = -1;
//
//    public static Map<String, Object> DUMMY_PARAMETERS = new HashMap<>();
//
//    // Whether or not to safely check types when evaluating.
//    // If true, this class will return error messages when invalid types are used.
//    // If false, the class will throw an exception when operators encounter types they can't use.
//    private boolean checksTypes;
//
//    // The query date format
//    private String queryDateFormat;
//
//    // The expression tokens
//    private List<ExpressionToken> tokens;
//
//    // The evaluation stages
//    private EvaluationStage evaluationStages;
//
//    // The input expression
//    private String inputExpression;
//
//    // Parses a new EvaluableExpression from the given expression string.
//    // Returns an error if the given expression has invalid syntax.
//    public static EvaluableExpression newEvaluableExpression(String expression) throws Exception {
//        return newEvaluableExpressionWithFunctions(expression, ExpressionFunction.functionMap());
//    }
//
//    // Similar to [NewEvaluableExpression], except that instead of a string, an already-tokenized expression is given.
//    // This is useful in cases where you may be generating an expression automatically, or using some other parser (e.g., to parse from a query language)
//    public static EvaluableExpression newEvaluableExpressionFromTokens(List<ExpressionToken> tokens) throws Exception {
//        EvaluableExpression ret = new EvaluableExpression();
//        ret.queryDateFormat = isoDateFormat;
//
//        if (checkBalance(tokens)!= null) {
//            throw new Exception(checkBalance(tokens));
//        }
//
//        if (checkExpressionSyntax(tokens)!= null) {
//            throw new Exception(checkExpressionSyntax(tokens));
//        }
//
//        ret.tokens = optimizeTokens(tokens);
//        if (ret.tokens == null) {
//            throw new Exception("Token optimization failed");
//        }
//
//        ret.evaluationStages = planStages(ret.tokens);
//        if (ret.evaluationStages == null) {
//            throw new Exception("Stage planning failed");
//        }
//
//        ret.checksTypes = true;
//        return ret;
//    }
//
//    // Similar to [NewEvaluableExpression], except enables the use of user-defined functions.
//    // Functions passed into this will be available to the expression.
//    public static EvaluableExpression newEvaluableExpressionWithFunctions(String expression, Map<String, Function> functions) throws Exception {
//        EvaluableExpression ret = new EvaluableExpression();
//        ret.queryDateFormat = isoDateFormat;
//        ret.inputExpression = expression;
//
//        ret.tokens = parseTokens(expression, functions);
//        if (ret.tokens == null) {
//            throw new Exception("Token parsing failed");
//        }
//
//        if (checkBalance(ret.tokens)!= null) {
//            throw new Exception(checkBalance(ret.tokens));
//        }
//
//        if (checkExpressionSyntax(ret.tokens)!= null) {
//            throw new Exception(checkExpressionSyntax(ret.tokens));
//        }
//
//        ret.tokens = optimizeTokens(ret.tokens);
//        if (ret.tokens == null) {
//            throw new Exception("Token optimization failed");
//        }
//
//        ret.evaluationStages = planStages(ret.tokens);
//        if (ret.evaluationStages == null) {
//            throw new Exception("Stage planning failed");
//        }
//
//        ret.checksTypes = true;
//        return ret;
//    }
//
//    // Constructor for the EvaluableExpression class
//    public EvaluableExpression() {
//        this.queryDateFormat = isoDateFormat;
//    }
//
//    // Same as `Eval`, but automatically wraps a map of parameters into a `govalute.Parameters` structure.
//    public Object evaluate(Map<String, Object> parameters) throws Exception {
//        if (parameters == null) {
//            return eval(null);
//        }
//        return eval(new MapParameters(parameters));
//    }
//
//    // Runs the entire expression using the given [parameters].
//    // e.g., If the expression contains a reference to the variable "foo", it will be taken from `parameters.Get("foo")`.
//    // This function returns errors if the combination of expression and parameters cannot be run,
//    // such as if a variable in the expression is not present in [parameters].
//    // In all non-error circumstances, this returns the single value result of the expression and parameters given.
//    // e.g., if the expression is "1 + 1", this will return 2.0.
//    // e.g., if the expression is "foo + 1" and parameters contains "foo" = 2, this will return 3.0
//    public Object eval(Parameters parameters) throws Exception {
//        if (evaluationStages == null) {
//            return null;
//        }
//
//        if (parameters!= null) {
//            parameters = new SanitizedParameters(parameters);
//        } else {
//            parameters = DUMMY_PARAMETERS;
//        }
//
//        return evaluateStage(evaluationStages, parameters);
//    }
//
//    public Object evaluateStage(EvaluationStage stage, Parameters parameters) throws Exception {
//        Object left = null;
//        Object right = null;
//        Exception err = null;
//
//        if (stage.leftStage!= null) {
//            left = evaluateStage(stage.leftStage, parameters);
//            if (left instanceof Exception) {
//                err = (Exception) left;
//                return null;
//            }
//        }
//
//        if (stage.isShortCircuitable()) {
//            switch (stage.symbol) {
//                case AND:
//                    if (left == false) {
//                        return false;
//                    }
//                    break;
//                case OR:
//                    if (left == true) {
//                        return true;
//                    }
//                    break;
//                case COALESCE:
//                    if (left!= null) {
//                        return left;
//                    }
//                    break;
//                case TERNARY_TRUE:
//                    if (left == false) {
//                        right = shortCircuitHolder;
//                    }
//                    break;
//                case TERNARY_FALSE:
//                    if (left!= null) {
//                        right = shortCircuitHolder;
//                    }
//                    break;
//            }
//        }
//
//        if (right!= shortCircuitHolder && stage.rightStage!= null) {
//            right = evaluateStage(stage.rightStage, parameters);
//            if (right instanceof Exception) {
//                err = (Exception) right;
//                return null;
//            }
//        }
//
//        if (checksTypes) {
//            if (stage.typeCheck == null) {
//                if (typeCheck(stage.leftTypeCheck, left, stage.symbol, stage.typeErrorFormat)!= null) {
//                    err = new Exception(typeCheck(stage.leftTypeCheck, left, stage.symbol, stage.typeErrorFormat));
//                    return null;
//                }
//
//                if (typeCheck(stage.rightTypeCheck, right, stage.symbol, stage.typeErrorFormat)!= null) {
//                    err = new Exception(typeCheck(stage.rightTypeCheck, right, stage.symbol, stage.typeErrorFormat));
//                    return null;
//                }
//            } else {
//                // special case where the type check needs to know both sides to determine if the operator can handle it
//                if (!stage.typeCheck(left, right)) {
//                    String errorMsg = String.format(stage.typeErrorFormat, left, stage.symbol.toString());
//                    err = new Exception(errorMsg);
//                    return null;
//                }
//            }
//        }
//
//        return stage.operator(left, right, parameters);
//    }
//
//    public static boolean isSubset(List<String> subset, List<String> superset) {
//        for (String sub : subset) {
//            boolean found = false;
//            for (String super : superset) {
//                if (sub.equals(super)) {
//                    found = true;
//                    break;
//                }
//            }
//            if (!found) {
//                return false;
//            }
//        }
//
//        return true;
//    }
//
//    public boolean canJoin(Map<String, Object> parameters) throws Exception {
//        if (parameters == null) {
//            return canJoinInternal(null);
//        }
//
//        return canJoinInternal(new MapParameters(parameters));
//    }
//
//    public boolean canJoinInternal(Parameters parameters) throws Exception {
//        if (evaluationStages == null) {
//            return true;
//        }
//
//        if (parameters!= null) {
//            parameters = new SanitizedParameters(parameters);
//        } else {
//            parameters = DUMMY_PARAMETERS;
//        }
//
//        return canJoin(evaluationStages, parameters);
//    }
//
//    public boolean canJoin(EvaluationStage stage, Parameters parameters) throws Exception {
//        boolean left = false;
//        boolean right = false;
//        List<String> leftKeys = new ArrayList<>();
//        List<String> rightKeys = new ArrayList<>();
//        Exception err = null;
//
//        if (stage.symbol == OperatorSymbol.VALUE) {
//            Object v = stage.token.getValue();
//            if (!(v instanceof String)) {
//                throw new Exception("value is not a string");
//            }
//            Object joinKeys = parameters.get((String) v);
//            if (joinKeys == null) {
//                throw new Exception("no group keys for " + v);
//            }
//
//            if (joinKeys instanceof List<?>) {
//                List<String> keys = (List<String>) joinKeys;
//                return true;
//            }
//
//            throw new Exception("Group keys for " + v + " are not a string array");
//        }
//
//        if (stage.leftStage!= null) {
//            left = canJoin(stage.leftStage, parameters);
//            if (left instanceof Exception) {
//                err = (Exception) left;
//                return false;
//            }
//        }
//
//        if (stage.rightStage!= null) {
//            right = canJoin(stage.rightStage, parameters);
//            if (right instanceof Exception) {
//                err = (Exception) right;
//                return false;
//            }
//        }
//
//        if (left && right) {
//            if (isSubset(leftKeys, rightKeys)) {
//                return true;
//            }
//
//            if (isSubset(rightKeys, leftKeys)) {
//                return true;
//            }
//        }
//
//        if (stage.leftStage == null && right) {
//            return true;
//        }
//
//        if (stage.rightStage == null && left) {
//            return true;
//        }
//
//        if (stage.leftStage == null && stage.rightStage == null) {
//            return true;
//        }
//
//        throw new Exception("Group keys must match or be a subset of the other but found left: " + leftKeys + ", right: " + rightKeys);
//    }
//
//    public static Exception typeCheck(StageTypeCheck check, Object value, OperatorSymbol symbol, String format) {
//        if (check == null) {
//            return null;
//        }
//
//        if (check.check(value)) {
//            return null;
//        }
//
//        String errorMsg = String.format(format, value, symbol.toString());
//        return new Exception(errorMsg);
//    }
//
//    // Returns an array representing the ExpressionTokens that make up this expression.
//    public List<ExpressionToken> tokens() {
//        return tokens;
//    }
//
//    // Returns a string representation of this expression.
//    public String toString() {
//        if (inputExpression!= null &&!inputExpression.isEmpty()) {
//            return inputExpression;
//        }
//
//        StringBuilder expressionText = new StringBuilder();
//        for (ExpressionToken val : tokens) {
//            switch (val.getKind()) {
//                case VARIABLE:
//                    expressionText.append(String.format("[%+v]", val.getMeta()));
//                    break;
//                case STRING:
//                case TIME:
//                    expressionText.append(String.format("'%+v'", val.getMeta()));
//                    break;
//                case COMPARATOR:
//                case LOGICALOP:
//                case MODIFIER:
//                case TERNARY:
//                    expressionText.append(String.format(" %+v ", val.getMeta()));
//                    break;
//                case SEPARATOR:
//                    expressionText.append(String.format("%+v ", val.getMeta()));
//                    break;
//                default:
//                    expressionText.append(String.format("%+v", val.getMeta()));
//                    break;
//            }
//        }
//
//        return expressionText.toString();
//    }
//
//    // Returns a string representation of this expression without brackets for vars.
//    public String expressionString() {
//        if (inputExpression!= null &&!inputExpression.isEmpty()) {
//            return inputExpression;
//        }
//
//        StringBuilder expressionText = new StringBuilder();
//        for (ExpressionToken val : tokens) {
//            switch (val.getKind()) {
//                case VARIABLE:
//                    expressionText.append(String.format("%+v", val.getMeta()));
//                    break;
//                case STRING:
//                case TIME:
//                    expressionText.append(String.format("'%+v'", val.getMeta()));
//                    break;
//                case COMPARATOR:
//                case LOGICALOP:
//                case MODIFIER:
//                case TERNARY:
//                    expressionText.append(String.format(" %+v ", val.getMeta()));
//                    break;
//                case SEPARATOR:
//                    expressionText.append(String.format("%+v ", val.getMeta()));
//                    break;
//                default:
//                    expressionText.append(String.format("%+v", val.getMeta()));
//                    break;
//            }
//        }
//
//        return expressionText.toString();
//    }
//
//    // Returns an array representing the variables contained in this EvaluableExpression.
//    public List<String> vars() {
//        List<String> varlist = new ArrayList<>();
//        for (ExpressionToken val : tokens) {
//            if (val.getKind() == TokenKind.VARIABLE) {
//                varlist.add((String) val.getValue());
//            }
//        }
//        return varlist;
//    }
}
