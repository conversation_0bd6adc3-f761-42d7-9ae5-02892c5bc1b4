package us.zoom.infra.dao.service;


import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import us.zoom.cube.site.BaseSpringTest;
import us.zoom.infra.dao.model.InfluxdbTenantRelationDO;

import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @create 8/18/21 9:21 AM
 */
public class InfluxdbTenantRelationDAOTest extends BaseSpringTest {

    private static final String TENANT_ID1 = "tenantId1";
    private static final String TENANT_ID2 = "tenantId2";

    private static final String INFLUXDB_CLUSTER_ID1 = "influxdbClusterId1";
    private static final String INFLUXDB_CLUSTER_ID2 = "influxdbClusterId2";

    @Autowired
    private InfluxdbTenantRelationDAO influxdbTenantRelationDAO;

    @Test
    public void goThrough() {

        InfluxdbTenantRelationDO relationDO1 = buildRelation1();
        influxdbTenantRelationDAO.add(relationDO1);
        InfluxdbTenantRelationDO relationDO2 = buildRelation2();
        influxdbTenantRelationDAO.add(relationDO2);

        int relationNumForTenant = influxdbTenantRelationDAO.countRelationForTenant(TENANT_ID1);
        Assertions.assertEquals(1, relationNumForTenant);

        int relationNumForCluster = influxdbTenantRelationDAO.countRelationForCluster(INFLUXDB_CLUSTER_ID1);
        Assertions.assertEquals(1, relationNumForCluster);


        List<InfluxdbTenantRelationDO> result = influxdbTenantRelationDAO.listAll();
        Assertions.assertTrue(result.size() > 0);

        List<InfluxdbTenantRelationDO> tenantRelations = influxdbTenantRelationDAO.listTenantRelationByInfluxdbClusterId(INFLUXDB_CLUSTER_ID1);
        Assertions.assertTrue(tenantRelations.size() > 0);

        influxdbTenantRelationDAO.removeRelationInCurrentCluster(INFLUXDB_CLUSTER_ID1);

        influxdbTenantRelationDAO.delete(relationDO1.getRelatedTenantId(), relationDO1.getInfluxdbClusterId());
        //influxdbTenantRelationDAO.delete(relationDO2.getRelatedTenantId(), relationDO2.getInfluxdbClusterId());
        influxdbTenantRelationDAO.deleteRelationWithinTenant(TENANT_ID2);

    }

    private InfluxdbTenantRelationDO buildRelation1() {
        InfluxdbTenantRelationDO relationDO = new InfluxdbTenantRelationDO();
        relationDO.setId(UUID.randomUUID().toString());
        relationDO.setInfluxdbClusterId(INFLUXDB_CLUSTER_ID1);
        relationDO.setRelatedTenantId(TENANT_ID1);
        relationDO.setCreateTime(new Date());
        relationDO.setModifyTime(new Date());
        return relationDO;
    }

    private InfluxdbTenantRelationDO buildRelation2() {
        InfluxdbTenantRelationDO relationDO = new InfluxdbTenantRelationDO();
        relationDO.setId(UUID.randomUUID().toString());
        relationDO.setInfluxdbClusterId(INFLUXDB_CLUSTER_ID2);
        relationDO.setRelatedTenantId(TENANT_ID2);
        relationDO.setCreateTime(new Date());
        relationDO.setModifyTime(new Date());
        return relationDO;
    }

}
