package us.zoom.cube.site.core;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import us.zoom.infra.dao.model.KafkaClusterDO;
import us.zoom.infra.dao.service.KafkaClusterDAO;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;

/**
 * @Author: luis.zheng
 */
@Component
public class KafkaClusterHandler {
    private KafkaClusterDAO kafkaClusterDAO;

    private RsaService rsaService;

    @Autowired
    public KafkaClusterHandler(KafkaClusterDAO kafkaClusterDAO, RsaService rsaService) {
        this.kafkaClusterDAO = kafkaClusterDAO;
        this.rsaService = rsaService;
    }

    public List<KafkaClusterDO> listAll(){
        List<KafkaClusterDO> kafkaClusterDOS = kafkaClusterDAO.listAll();
        return kafkaClusterDOS;
    }

    public List<KafkaClusterDO> findByParam(String name, String clusterId, String regionId, int pageIndex, int pageSize) throws Exception {
        List<KafkaClusterDO> kafkaClusterDOS = kafkaClusterDAO.findByParam(name, clusterId, regionId, pageSize * (pageIndex - 1), pageSize);
        return securityFieldSetNullForArray(kafkaClusterDOS);
    }

    public Integer getCountByParam(String name, String clusterId, String regionId) {
        return kafkaClusterDAO.getCountByParam(name, clusterId, regionId);
    }

    public List<KafkaClusterDO> getByIds(Set<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }
        return securityFieldSetNullForArray(kafkaClusterDAO.getByIds(ids));
    }

    public void add(KafkaClusterDO KafkaClusterDO) throws Exception {
        encrypt(KafkaClusterDO);
        kafkaClusterDAO.add(KafkaClusterDO);
    }

    public void edit(KafkaClusterDO KafkaClusterDO) throws Exception {
        encrypt(KafkaClusterDO);
        kafkaClusterDAO.edit(KafkaClusterDO);
    }

    public KafkaClusterDO getSecurityById(String id) {
        return singleSecurityFieldSetNull(kafkaClusterDAO.getById(id));
    }

    public KafkaClusterDO getById(String id) throws Exception {
        return decryptSingle(kafkaClusterDAO.getById(id));
    }

    public int countById(String id) {
        return kafkaClusterDAO.countById(id);
    }

    public boolean hasSameName(String name) {
        return kafkaClusterDAO.countByName(name) > 0;
    }

    public void deleteById(String id) {
        kafkaClusterDAO.deleteById(id);
    }

    private List<KafkaClusterDO> securityFieldSetNullForArray(List<KafkaClusterDO> kafkaClusterDOS) {
        if (CollectionUtils.isEmpty(kafkaClusterDOS)) {
            return Collections.emptyList();
        }

        for (KafkaClusterDO kafkaClusterDO : kafkaClusterDOS) {
            singleSecurityFieldSetNull(kafkaClusterDO);
        }
        return kafkaClusterDOS;
    }

    private KafkaClusterDO singleSecurityFieldSetNull(KafkaClusterDO kafkaClusterDO) {
        kafkaClusterDO.setSslTruststorePassword(null);
        kafkaClusterDO.setSslKeystorePassword(null);
        kafkaClusterDO.setSslKeyPassword(null);
        kafkaClusterDO.setSaslJaasConfig(null);
        return kafkaClusterDO;
    }

    private KafkaClusterDO decryptSingle(KafkaClusterDO kafkaClusterDO) throws Exception {
        if (StringUtils.isNotBlank(kafkaClusterDO.getSslTruststorePassword())) {
            kafkaClusterDO.setSslTruststorePassword(rsaService.decrypt(kafkaClusterDO.getSslTruststorePassword()));
        }
        if (StringUtils.isNotBlank(kafkaClusterDO.getSslKeystorePassword())) {
            kafkaClusterDO.setSslKeystorePassword(rsaService.decrypt(kafkaClusterDO.getSslKeystorePassword()));
        }
        if (StringUtils.isNotBlank(kafkaClusterDO.getSslKeyPassword())) {
            kafkaClusterDO.setSslKeyPassword(rsaService.decrypt(kafkaClusterDO.getSslKeyPassword()));
        }
        if (StringUtils.isNotBlank(kafkaClusterDO.getSaslJaasConfig())) {
            kafkaClusterDO.setSaslJaasConfig(rsaService.decrypt(kafkaClusterDO.getSaslJaasConfig()));
        }
        return kafkaClusterDO;
    }

    private KafkaClusterDO encrypt(KafkaClusterDO kafkaClusterDO) throws Exception {
        if (StringUtils.isNotBlank(kafkaClusterDO.getSslTruststorePassword())) {
            kafkaClusterDO.setSslTruststorePassword(rsaService.encrypt(kafkaClusterDO.getSslTruststorePassword()));
        }
        if (StringUtils.isNotBlank(kafkaClusterDO.getSslKeystorePassword())) {
            kafkaClusterDO.setSslKeystorePassword(rsaService.encrypt(kafkaClusterDO.getSslKeystorePassword()));
        }
        if (StringUtils.isNotBlank(kafkaClusterDO.getSslKeyPassword())) {
            kafkaClusterDO.setSslKeyPassword(rsaService.encrypt(kafkaClusterDO.getSslKeyPassword()));
        }
        if (StringUtils.isNotBlank(kafkaClusterDO.getSaslJaasConfig())) {
            kafkaClusterDO.setSaslJaasConfig(rsaService.encrypt(kafkaClusterDO.getSaslJaasConfig()));
        }
        return kafkaClusterDO;
    }
}
