package us.zoom.cube.site.core;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.lib.common.CubeConstants;
import us.zoom.infra.dao.model.UnitTagDO;
import us.zoom.infra.dao.service.UnitTagDAO;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * @Author: luis.zheng
 */
@Component
public class UnitTagHandler {
    @Autowired
    private UnitTagDAO unitTagDAO;

    public List<UnitTagDO> listAll() {
        List<UnitTagDO> unitTagDOList = new ArrayList<>();
        long counts = unitTagDAO.queryCounts();
        int pageSize = CubeConstants.CUBE_SQL_BATCH_QUERY_SIZE;
        long pageNum = counts / pageSize + (counts % pageSize == 0 ? 0 : 1);
        for (int i = 1 ; i <= pageNum ; i++){
            unitTagDOList.addAll(unitTagDAO.listBatch(pageSize * (i - 1),pageSize));
        }
        return  unitTagDOList;
    }

    public List<UnitTagDO> findByParam(String name, String taskType, int pageIndex, int pageSize) {
        List<UnitTagDO> unitTagDOS = unitTagDAO.findByParam(name, taskType, pageSize * (pageIndex - 1), pageSize);
        return unitTagDOS;
    }

    public Integer getCountByParam(String name, String taskType) {
        return unitTagDAO.getCountByParam(name, taskType);
    }

    public UnitTagDO getByNameAndType(String name, String taskType) {
        return unitTagDAO.getByNameAndType(name, taskType);
    }

    public void add(UnitTagDO unitTagDO) {
        unitTagDAO.add(unitTagDO);
    }

    public void edit(UnitTagDO unitTagDO) {
        unitTagDAO.edit(unitTagDO);
    }

    public UnitTagDO getById(String id) {
        return unitTagDAO.getById(id);
    }

    public Integer getCountByName(String name) {
        return unitTagDAO.getCountByName(name);
    }

    public Integer getCountByNameNotId(String name, String id) {
        return unitTagDAO.getCountByNameNotId(name, id);
    }

    public List<UnitTagDO> getByIds(Set<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }
        return unitTagDAO.getByIds(ids);
    }

    public void deleteById(String id) {
        unitTagDAO.deleteById(id);
    }

    public List<UnitTagDO> selectByUnitTag(String unitTag, String taskType) {
        return unitTagDAO.selectByUnitTag(unitTag,taskType);
    }
}
