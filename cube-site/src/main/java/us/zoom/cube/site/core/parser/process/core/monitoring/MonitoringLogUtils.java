package us.zoom.cube.site.core.parser.process.core.monitoring;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import us.zoom.cube.lib.utils.ExceptionStackUtils;
import us.zoom.cube.lib.utils.JsonUtils;
import us.zoom.cube.site.core.parser.process.core.common.ErrorTags;
import us.zoom.cube.site.core.parser.process.core.common.constant.MonitoringConst;
import us.zoom.cube.site.core.parser.process.core.common.enums.MonitoringLogType;
import us.zoom.cube.site.core.parser.process.core.limit.HubLimiter;

import java.util.concurrent.atomic.AtomicLong;


public class MonitoringLogUtils {
    private static final Logger monitorLog = LoggerFactory.getLogger(MonitoringConst.MONITOR_LOG_NAME);
    private static final String FIELD_MSG = "msg";
    private static final String FIELD_ERROR_TYPE = "errorType";
    private static final String FIELD_STACK = "stack";
    private static final AtomicLong logIndex = new AtomicLong();
    private HubLimiter limiter;
    private long logSampling = 10000;

    public MonitoringLogUtils() {
        limiter = new HubLimiter();
    }

    public MonitoringLogUtils(long logSampling) {
        this.logSampling = logSampling;
    }

    public void printErrorLog(ErrorTags tags, MonitoringLogType type, Exception e) {
        try {
            if (limiter.isLimit()) {
                Measure measure = new Measure(MonitoringConst.ERROR_METRICS);
                measure.getField().put(FIELD_MSG, ExceptionStackUtils.parseExceptionStackToString(e));

                measure.getTag().putAll(tags.toMap());
                measure.getTag().put(FIELD_ERROR_TYPE, type.name());

                monitorLog.info(JsonUtils.toJsonStringIgnoreExp(measure));
            }
        } catch (Exception e1) {
        }
    }


}