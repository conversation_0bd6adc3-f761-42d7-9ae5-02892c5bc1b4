package us.zoom.cube.site.core;

import org.influxdb.dto.QueryResult;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import us.zoom.cube.lib.integrations.Metrics;
import us.zoom.cube.site.CubeSiteApplicationTests;
import us.zoom.cube.site.core.capacity.CapacityDataConvertService;
import us.zoom.cube.site.core.capacity.CapacitySchedule;
import us.zoom.cube.site.core.capacity.CapacityService;
import us.zoom.cube.site.core.capacity.CapacitySqlParseService;
import us.zoom.cube.site.core.model.common.CapacityModel;
import us.zoom.infra.utils.DateUtils;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022-05-24 11:04
 */
public class CapacityCalcTest extends CubeSiteApplicationTests {
    @Autowired
    CapacitySchedule capacitySchedule;

    @Autowired
    CapacityService capacityService;

    @Autowired
    CapacitySqlParseService capacitySqlParseService;

    @Autowired
    CapacityDataConvertService capacityDataConvertService;


    @Test
    public void capacityScheduleTest(){
        capacitySchedule.asignTasks();
    }

    @Test
    public void capacityServiceTest(){
        CapacityModel capacityModel =new CapacityModel();
        long startTime = DateUtils.getTodayStartTime().getTime();
        String sqlMax = capacitySqlParseService.sqlMaxTasks("ds_web", startTime, 14,5,"clusterId,regionId");
        QueryResult resMax = capacityDataConvertService.getData(sqlMax,"ds_web");
        String sqlNum = capacitySqlParseService.sqlNumTasks("ds_web","cluster,region");
        QueryResult resNum = capacityDataConvertService.getData(sqlNum,"ds_web");
        List<Metrics> resMaxMetrics = capacityDataConvertService.transferQueryResult2Metrics(resMax);
        List<Metrics> resNumMetrics = capacityDataConvertService.transferQueryResult2Metrics(resNum);
        Map<String, List<Metrics>> resMetrics = capacityService.preProcess(resMaxMetrics, resNumMetrics,capacityModel);
        //List<Metrics> resList = capacityService.process(resMetrics, startTime, "ds_web",);
        //Assert.assertNotNull(resList);
    }
}
