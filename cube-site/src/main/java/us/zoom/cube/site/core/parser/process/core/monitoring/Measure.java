package us.zoom.cube.site.core.parser.process.core.monitoring;

import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.Data;
import lombok.NoArgsConstructor;
import us.zoom.cube.site.core.parser.process.core.common.constant.Constants;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

@Data
@NoArgsConstructor
public class Measure {
    private String measure;
    private Map<String, Object> tag;
    private long ts;
    private String cubeVer;
    private Map<String, Object> field;
    private Set<String> pii;
    private Map<String, Object> mapField;
    private Map<String, Object> hisField;
    private Map<String, Object> hisSchema;
    private Map<String, Object> sumField;
    private Map<String, Object> sumSchema;

    public Measure(String measure, Map<String, Object> tag, Map<String, Object> field, Set<String> pii, long ts) {
        this(measure, "1.0.0", tag, field, pii, ts);
    }

    public Measure(String measure, Map<String, Object> tag, Map<String, Object> field, Set<String> pii,
                   Map<String, Object> hisField, Map<String, Object> hisSchema,
                   Map<String, Object> sumField, Map<String, Object> sumSchema, Long ts) {
        this.measure = measure;
        this.cubeVer = "1.0.0";
        this.ts = ts;
        this.tag = tag;
        this.field = field;
        this.pii = pii;
        this.hisField = hisField;
        this.hisSchema = hisSchema;
        this.sumField = sumField;
        this.sumSchema = sumSchema;
    }

    public Measure(String measure, String cubeVer, Map<String, Object> tag, Map<String, Object> field, Set<String> pii, long ts) {
        this.measure = measure;
        this.cubeVer = cubeVer;
        this.ts = ts;
        this.tag = tag;
        this.field = field;
        this.pii = pii;
    }

    public Measure(String measure) {
        this.measure = measure;
        this.cubeVer = "1.0.0";
        this.ts = System.currentTimeMillis();
        this.tag = new HashMap<>();
        this.field = new HashMap<>();
        this.pii = Sets.newHashSetWithExpectedSize(1);
    }



    public Set<String> getPii() {
        if (pii == null) {
            return Sets.newLinkedHashSetWithExpectedSize(1);
        }
        return pii;
    }

    public Map<String, Object> toMap() {
        Map<String, Object> map = new HashMap<>();
        if (mapField != null) {
            map.putAll(mapField);
        }
        map.put(Constants.MEASURE_CUBE_VER, cubeVer);
        map.put(Constants.MEASURE, measure);
        map.putAll(tag);
        map.putAll(field);
        if (hisField != null) {
            map.putAll(hisField);
        }
        if (sumField != null) {
            map.putAll(sumField);
        }
        map.put(Constants.MEASURE_TS, ts);
        return map;
    }



    public void checkTagFiled() {
        if (tag == null) {
            tag = Maps.newHashMapWithExpectedSize(1);
        }
        if (field == null) {
            field = Maps.newHashMapWithExpectedSize(1);
        }
    }
}
