package us.zoom.cube.site.core.parser.process.core.limit;

import com.google.common.util.concurrent.RateLimiter;

public class HubLimiter {
    private RateLimiter limiter;

    public HubLimiter() {
        limiter = RateLimiter.create(0.01);
    }

    public HubLimiter(double permitsPerSecond) {
        limiter = RateLimiter.create(permitsPerSecond);
    }

    public boolean isLimit() {
        return limiter.tryAcquire();
    }
}
