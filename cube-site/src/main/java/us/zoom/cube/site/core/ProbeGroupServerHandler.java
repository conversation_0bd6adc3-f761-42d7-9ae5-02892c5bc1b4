package us.zoom.cube.site.core;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import us.zoom.infra.dao.model.ProbeGroupServerDO;
import us.zoom.infra.dao.service.ProbeGroupServerDAO;

/**
 * @Author: <EMAIL>
 * @ModuleOwner: <EMAIL>
 * @Date: 06/29/2022 16:43
 * @Description:
 */
@Component
@Slf4j
public class ProbeGroupServerHandler {
    @Autowired
    private ProbeGroupServerDAO probeGroupServerDAO;

    public ProbeGroupServerDO getByServerId(String serverId) {
        return probeGroupServerDAO.getByServerId(serverId);
    }

    public int getCountByProbeGroupId(String probeGroupId) {
        return probeGroupServerDAO.getCountByProbeGroupId(probeGroupId);
    }

    public void delByServerId(String serverId) {
        probeGroupServerDAO.delByServerId(serverId);
    }

    public void bindProbeGroup(String id, String probeGroupId, String serverId) {
        probeGroupServerDAO.add(id, probeGroupId, serverId);
    }
}
