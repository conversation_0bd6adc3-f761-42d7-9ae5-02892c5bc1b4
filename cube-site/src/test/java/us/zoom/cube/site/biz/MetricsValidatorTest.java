package us.zoom.cube.site.biz;

import com.google.common.collect.Lists;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;
import us.zoom.cube.site.lib.input.*;
import us.zoom.cube.site.lib.input.metrics.MetricsDocumentLinkInput;
import us.zoom.infra.enums.MetricsTypeEnum;
import us.zoom.cube.lib.utils.JsonUtils;
import us.zoom.infra.utils.MathUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;

import static us.zoom.infra.utils.RegexConstants.CONFIG_NAME_PATTERN;

@ExtendWith(MockitoExtension.class)
public class MetricsValidatorTest {

    @InjectMocks
    private MetricsValidator metricsValidator = new MetricsValidator();


    @Test
    public void checkAddMetricsParameter(){
        MetricsInput metricsInput = new MetricsInput();
        metricsInput.setTagNames(Lists.newArrayList("host","cluster"));
        metricsInput.setType(MetricsTypeEnum.AGGREGATION.getValue());
        metricsInput.setMetricsName("sjj-ut-metricsName1");
        metricsInput.setCollectorId("sjj-ut-collectorId1");
        MetricsAggregationInput metricsAggregationInput = buildMetricsAggregationInput();
        metricsInput.setMetricsAggregation(metricsAggregationInput);

        metricsValidator.checkAddMetricsParameter(metricsInput);

    }

    @Test
    public void doubleEqualTest(){
        Double a = 20.1;
        Double b = 20.1;
        System.out.println(MathUtils.equals(a,b));
    }
    private MetricsAggregationInput buildMetricsAggregationInput(){
        MetricsAggregationInput metricsAggregationInput = new MetricsAggregationInput();
        metricsAggregationInput.setAggPeriod(60);
        metricsAggregationInput.setWaitTime(5000);
        metricsAggregationInput.setFilterCondition("clusterId == 'dev'");
        MetricsAggregationRuleComposeInput metricsAggregationRuleComposeInput = new MetricsAggregationRuleComposeInput();
        List<AggregationFunctionRuleInput> aggregationFunctionRuleInputList = new ArrayList<>();
        AggregationFunctionRuleInput aggregationFunctionRuleInput1 = new AggregationFunctionRuleInput();
        aggregationFunctionRuleInput1.setAggField("load1");
        aggregationFunctionRuleInput1.setFilterCondition("a=b");
        aggregationFunctionRuleInput1.setIsConditioned(0);
        List<AggregationFunctionItemInput> aggregationFunctionItemInputList1 = new ArrayList<>();
        AggregationFunctionItemInput aggregationFunctionItemInput1 = new AggregationFunctionItemInput();
        aggregationFunctionItemInput1.setValue("count");
        aggregationFunctionItemInputList1.add(aggregationFunctionItemInput1);
        AggregationFunctionItemInput aggregationFunctionItemInput2 = new AggregationFunctionItemInput();
        aggregationFunctionItemInput2.setValue("sum");
        aggregationFunctionItemInputList1.add(aggregationFunctionItemInput2);
        aggregationFunctionRuleInput1.setAggTypes(aggregationFunctionItemInputList1);
        aggregationFunctionRuleInputList.add(aggregationFunctionRuleInput1);

        AggregationFunctionRuleInput aggregationFunctionRuleInput2 = new AggregationFunctionRuleInput();
        aggregationFunctionRuleInput2.setAggField("load1");
        aggregationFunctionRuleInput2.setIsConditioned(1);
        aggregationFunctionRuleInput2.setConditionalFieldPrefix("load1_1");
        List<AggregationFunctionItemInput> aggregationFunctionItemInputList2 = new ArrayList<>();
        AggregationFunctionItemInput aggregationFunctionItemInput2_1 = new AggregationFunctionItemInput();
        aggregationFunctionItemInput2_1.setValue("count");
        aggregationFunctionItemInputList2.add(aggregationFunctionItemInput2_1);

        aggregationFunctionRuleInput2.setAggTypes(aggregationFunctionItemInputList2);
        aggregationFunctionRuleInputList.add(aggregationFunctionRuleInput2);
        metricsAggregationRuleComposeInput.setCommon(aggregationFunctionRuleInputList);

        List<AggregationHistogramRuleInput> aggregationHistogramRuleInputList = new ArrayList<>();
        AggregationHistogramRuleInput aggregationHistogramRuleInput1 = new AggregationHistogramRuleInput();
        aggregationHistogramRuleInput1.setAggField("load1");
        List<AggregationHistogramRangeItemInput> aggregationHistogramRangeItemInputList = new ArrayList<>();
        AggregationHistogramRangeItemInput aggregationHistogramRangeItemInput1 = new AggregationHistogramRangeItemInput();
        aggregationHistogramRangeItemInput1.setLowerLimit(10.1);
        aggregationHistogramRangeItemInput1.setUpperLimit(20.1);
        aggregationHistogramRangeItemInput1.setLowerIntervalNotation(1);
        aggregationHistogramRangeItemInput1.setUpperIntervalNotation(1);
        aggregationHistogramRangeItemInput1.setResultLabel("load_10_20");
        aggregationHistogramRangeItemInputList.add(aggregationHistogramRangeItemInput1);

        AggregationHistogramRangeItemInput aggregationHistogramRangeItemInput2 = new AggregationHistogramRangeItemInput();
        aggregationHistogramRangeItemInput2.setLowerLimit(20.1);
        aggregationHistogramRangeItemInput2.setUpperLimit(70.0);
        aggregationHistogramRangeItemInput2.setLowerIntervalNotation(0);
        aggregationHistogramRangeItemInput2.setUpperIntervalNotation(0);
        aggregationHistogramRangeItemInput2.setResultLabel("load_20_70");
        aggregationHistogramRangeItemInputList.add(aggregationHistogramRangeItemInput2);
        aggregationHistogramRuleInput1.setRanges(aggregationHistogramRangeItemInputList);
        aggregationHistogramRuleInputList.add(aggregationHistogramRuleInput1);

        AggregationHistogramRuleInput aggregationHistogramRuleInput2 = new AggregationHistogramRuleInput();
        aggregationHistogramRuleInput2.setAggField("usedTime");
        List<AggregationHistogramRangeItemInput> aggregationHistogramRangeItemInputList2 = new ArrayList<>();
        AggregationHistogramRangeItemInput aggregationHistogramRangeItemInput3 = new AggregationHistogramRangeItemInput();
        aggregationHistogramRangeItemInput3.setLowerLimit(0.1);
        aggregationHistogramRangeItemInput3.setUpperLimit(20.1);
        aggregationHistogramRangeItemInput3.setLowerIntervalNotation(1);
        aggregationHistogramRangeItemInput3.setUpperIntervalNotation(0);
        aggregationHistogramRangeItemInput3.setResultLabel("load_0_20");
        aggregationHistogramRangeItemInputList2.add(aggregationHistogramRangeItemInput3);
        AggregationHistogramRangeItemInput aggregationHistogramRangeItemInput4 = new AggregationHistogramRangeItemInput();
        aggregationHistogramRangeItemInput4.setLowerLimit(20.1);
        aggregationHistogramRangeItemInput4.setUpperLimit(21.2);
        aggregationHistogramRangeItemInput4.setLowerIntervalNotation(1);
        aggregationHistogramRangeItemInput4.setUpperIntervalNotation(0);
        aggregationHistogramRangeItemInput4.setResultLabel("load_20_21");
        aggregationHistogramRangeItemInputList2.add(aggregationHistogramRangeItemInput4);
        aggregationHistogramRuleInput2.setRanges(aggregationHistogramRangeItemInputList2);
        aggregationHistogramRuleInputList.add(aggregationHistogramRuleInput2);
        metricsAggregationRuleComposeInput.setHistogram(aggregationHistogramRuleInputList);

        List<AggregationPercentileRuleInput> aggregationPercentileRuleInputList = new ArrayList<>();
        AggregationPercentileRuleInput aggregationPercentileRuleInput = new AggregationPercentileRuleInput();
        aggregationPercentileRuleInput.setAggField("load1");
        List<AggregationPercentileItemInput> aggregationPercentileItemInputList = new ArrayList<>();
        AggregationPercentileItemInput aggregationPercentileItemInput1 = new AggregationPercentileItemInput();
        aggregationPercentileItemInput1.setValue(95);
        aggregationPercentileItemInputList.add(aggregationPercentileItemInput1);
        AggregationPercentileItemInput aggregationPercentileItemInput2 = new AggregationPercentileItemInput();
        aggregationPercentileItemInput2.setValue(15);
        aggregationPercentileItemInputList.add(aggregationPercentileItemInput2);
        aggregationPercentileRuleInput.setPercentileValues(aggregationPercentileItemInputList);
        aggregationPercentileRuleInputList.add(aggregationPercentileRuleInput);
        metricsAggregationRuleComposeInput.setPercentile(aggregationPercentileRuleInputList);
        metricsAggregationInput.setMetricsAggregationRuleCompose(metricsAggregationRuleComposeInput);
        return metricsAggregationInput;
    }

    @Test
    public void matcherTest(){
        String prefix = "";
        Matcher fieldPrefixMatcher = CONFIG_NAME_PATTERN.matcher(prefix);
        Assertions.assertTrue(fieldPrefixMatcher.find(), "[function]conditionalFieldPrefix can only contain [0-9a-zA-Z_], the invalid prefix = " + prefix);

    }


    @Test()
    public void checkAggTest() throws IllegalArgumentException{
        String json = "{\n" +
                "    \"type\": 1,\n" +
                "    \"metricsName\": \"cputa\",\n" +
                "    \"collectorId\": \"04adedab-c669-4210-9c5b-4f584aaffb5b\",\n" +
                "    \"tagNames\": [\n" +
                "        \"appName\"\n" +
                "    ],\n" +
                "    \"enabled\": true,\n" +
                "    \"metricsAggregation\": {\n" +
                "        \"aggPeriod\": 60,\n" +
                "        \"spcStatisticalPeriod\": 1,\n" +
                "        \"spcSamplingWeight\": 1,\n" +
                "        \"metricsAggregationRuleCompose\": {\n" +
                "            \"common\": [\n" +
                "                {\n" +
                "                    \"aggField\": \"appName\",\n" +
                "                    \"fieldType\": \"number\",\n" +
                "                    \"aggTypes\": [\n" +
                "                        {\n" +
                "                            \"value\": \"count\",\n" +
                "                            \"condition\": \"\"\n" +
                "                        }\n" +
                "                    ],\n" +
                "                    \"spcCompute\": 0,\n" +
                "                    \"isConditioned\": 0,\n" +
                "                    \"conditionalFieldPrefix\": \"\",\n" +
                "                    \"filterCondition\": \"\"\n" +
                "                },\n" +
                "                {\n" +
                "                    \"aggField\": \"wS\",\n" +
                "                    \"fieldType\": \"number\",\n" +
                "                    \"aggTypes\": [\n" +
                "                        {\n" +
                "                            \"value\": \"count\",\n" +
                "                            \"condition\": \"\"\n" +
                "                        }\n" +
                "                    ],\n" +
                "                    \"spcCompute\": 0,\n" +
                "                    \"isConditioned\": 0,\n" +
                "                    \"conditionalFieldPrefix\": \"\",\n" +
                "                    \"filterCondition\": \"\"\n" +
                "                }\n" +
                "            ]\n" +
                "        }\n" +
                "    }\n" +
                "}";
        MetricsInput metricsInput = JsonUtils.toObject(json, MetricsInput.class);
        List<AggregationFunctionRuleInput> common = metricsInput.getMetricsAggregation().getMetricsAggregationRuleCompose().getCommon();
        Assertions.assertThrows(IllegalArgumentException.class, () -> metricsValidator.checkAggMetrics(metricsInput, common, false));

        List<String> list = new ArrayList<>();
        list.add("us");
        list.add("eu");
        Assertions.assertEquals("us,eu", String.join(",", list));
    }

    @Test
    public void testUrl() {
        String url1 = "https://zoomvideo.atlassian.net/wiki/spaces/ZW/pages/2699138262/APM+metrics+data+dictionary#thread_dump_metrics(Jian)";
        Assertions.assertTrue(MetricsDocumentLinkInput.isValidURL(url1));

        String url2 = "https://eng.corp.zoom.com/2/doc/eyJ0eXBlSWQiOjUsImRvY0lkIjo1MTI1fQ==#Jvm%2520(%2520)";
        Assertions.assertTrue(MetricsDocumentLinkInput.isValidURL(url2));
    }

}
