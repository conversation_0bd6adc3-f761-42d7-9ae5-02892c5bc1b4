package us.zoom.cube.site.biz.template;

import com.zoom.op.monitor.domain.alarm.AlarmDefinition;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import us.zoom.cube.lib.utils.JsonUtils;
import com.fasterxml.jackson.core.type.TypeReference;


import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;


class TemplateInnerHandlerServiceTest {

    @Test
    public void testFilterSameNameAlarm() {
        String str = "[\n" +
                "    {\n" +
                "        \"userId\": \"c793c210-9be2-48f0-8cf5-dd857eafdc40\",\n" +
                "        \"tenantId\": \"ab75c9a3-ee2f-4b3a-8438-69275340121e\",\n" +
                "        \"id\": \"3f4a7cf4-4e23-4371-a515-2650c203e2d8\",\n" +
                "        \"name\": \"cpu\"\n" +
                "    },\n" +
                "    {\n" +
                "        \"userId\": \"c793c210-9be2-48f0-8cf5-dd857eafdc40\",\n" +
                "        \"tenantId\": \"ab75c9a3-ee2f-4b3a-8438-69275340121e\",\n" +
                "        \"id\": \"3f4a7cf4-4e23-4371-a515-2650c203e2d8\",\n" +
                "        \"name\": \"cpu\",\n" +
                "        \"metricId\": \"ea4e40b6-91c8-427b-9d3e-bad9efad02cc\"\n" +
                "    }\n" +
                "]";
        List<AlarmDefinition> addAlarmDefinitionList = JsonUtils.toObjectByTypeRef(str, new TypeReference<List<AlarmDefinition>>() {
        });

        Assertions.assertEquals(2, addAlarmDefinitionList.size());
        List<AlarmDefinition> newAlarmDefinitionList = new ArrayList<>(addAlarmDefinitionList.stream()
                .collect(Collectors.toMap(AlarmDefinition::getName, dto -> dto, (existing, replacement) -> existing))
                .values());
        Assertions.assertEquals(1, newAlarmDefinitionList.size());
    }

}