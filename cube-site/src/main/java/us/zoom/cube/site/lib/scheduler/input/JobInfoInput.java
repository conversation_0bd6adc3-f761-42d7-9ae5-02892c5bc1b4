package us.zoom.cube.site.lib.scheduler.input;

import lombok.Data;
import us.zoom.cube.scheduler.lib.enums.*;
import us.zoom.cube.site.lib.BasePara;

import java.util.Date;

/**
 * @author: <PERSON><PERSON> Ding
 * @date: 2025/4/16 16:30
 * @desc:
 */
@Data
public class JobInfoInput extends BasePara {

    private String id;
    private String name;
    private String jobDesc;
    private String refId;
    private String busiType;
    private ScheduleType scheduleType;
    private String scheduleConf;
    private boolean enabled;
    private String jobParam;
    private TriggerStatus triggerStatus;
    private ExecutorType executorType;
    private ChannelMedia channelMedia;
    private String executorInfoId;
    private MisfireStrategy misfireStrategy;
    private boolean timeRangeEnabled;
    private boolean timeRangeAlign;
    private int triggerTimeoutRetryCount;
    private String triggerAsyncmqTopic;
    private String triggerAsyncmqUser;
    private String triggerExecutorUrl;
    private String csmsApp;
    private ExecutorBlockStrategy executorBlockStrategy;
    private int executorTimeout;
    private int executorFailRetryCount;
    private TriggerRouteStrategy triggerRouteStrategy;
    private long triggerLastTime;
    private long triggerNextTime;
    private Date createTime;
    private Date updateTime;
    private String creator;
    private String editor;

}
