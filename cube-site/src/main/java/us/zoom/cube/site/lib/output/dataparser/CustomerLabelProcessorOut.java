package us.zoom.cube.site.lib.output.dataparser;

import java.util.List;

public class CustomerLabelProcessorOut extends BaseProcessorOut{
    private String sourceField;
    private List<String> selectedLabels;
    private String prefix;
    private String dataSource;

    private String customData;

    public String getSourceField() {
        return sourceField;
    }

    public void setSourceField(String sourceField) {
        this.sourceField = sourceField;
    }

    public List<String> getSelectedLabels() {
        return selectedLabels;
    }

    public void setSelectedLabels(List<String> selectedLabels) {
        this.selectedLabels = selectedLabels;
    }

    public String getPrefix() {
        return prefix;
    }

    public void setPrefix(String prefix) {
        this.prefix = prefix;
    }

    public String getDataSource() {
        return dataSource;
    }

    public void setDataSource(String dataSource) {
        this.dataSource = dataSource;
    }

    public String getCustomData() {
        return customData;
    }

    public void setCustomData(String customData) {
        this.customData = customData;
    }

    public CustomerLabelProcessorOut(String id, String name, Integer order, String dataParserPipelineId, String dataParserId, String type, String sourceField, List<String> selectedLabels, String prefix, String dataSource, String customData) {
        super(id, name, order, dataParserPipelineId, dataParserId, type);
        this.sourceField = sourceField;
        this.selectedLabels = selectedLabels;
        this.prefix = prefix;
        this.dataSource = dataSource;
        this.customData = customData;
    }
}
