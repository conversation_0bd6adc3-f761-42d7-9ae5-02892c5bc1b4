package us.zoom.cube.site.core.parser.process.core.preprocess;

import com.fasterxml.jackson.core.type.TypeReference;
import us.zoom.mq.common.entity.TaskEntity;

import java.util.List;
import java.util.Map;

/**
 * @Author: <EMAIL>
 * @ModuleOwner: <EMAIL>
 * @Date: 12/22/2022 17:06
 * @Description:
 */
public interface DataFlowPreProcessService {
    TypeReference<List<Map<String, Object>>> listMapTypeReference = new TypeReference<>() {
    };
    TypeReference<Map<String, Object>> mapTypeReference = new TypeReference<>() {
    };

    /**
     * TaskEntity is not actually used
     * @param message
     * @param taskEntity
     * @return
     */
    List<Map<String, Object>> handle(String message, TaskEntity<String> taskEntity);
}
