package us.zoom.cube.site.infra.utils.trace;

import com.google.common.base.Joiner;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import us.zoom.cube.sdk.model.MonitorLog;
import us.zoom.cube.site.infra.enums.trace.AttributeKeyDataType;
import us.zoom.cube.site.infra.enums.trace.FilterOperator;
import us.zoom.cube.site.infra.enums.trace.FunctionName;
import us.zoom.cube.site.infra.enums.trace.QueryType;
import us.zoom.cube.site.lib.input.trace.BuilderQuery;
import us.zoom.cube.site.lib.input.trace.BuilderQuery.FilterItem;
import us.zoom.cube.site.lib.input.trace.BuilderQuery.FilterSet;
import us.zoom.cube.site.lib.input.trace.BuilderQuery.Function;
import us.zoom.cube.site.lib.input.trace.CompositeQuery;
import us.zoom.cube.site.lib.input.trace.QueryValidateResult;
import us.zoom.cube.site.lib.input.trace.TraceQueryRangeParam;
import us.zoom.cube.lib.utils.JsonUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

import static us.zoom.cube.site.infra.enums.trace.PanelType.LIST;

/**
 * @author: eason.jia
 * @date: 2024/8/19
 */
public class QueryRangeParamUtils {

    private static final Logger logger = LoggerFactory.getLogger(QueryRangeParamUtils.class);
    private static final Logger monitorLogger = LoggerFactory.getLogger("Monitor");

    public static final int MAX_ALLOWED_POINTS_IN_TIME_SERIES = 300;

    public static void sanitizeQueryRangeParam(CompositeQuery compositeQuery) {
        for (Map.Entry<String, BuilderQuery> entry : compositeQuery.getBuilderQueries().entrySet()) {
            BuilderQuery builderQuery = entry.getValue();
            if (CollectionUtils.size(builderQuery.getGroupBy()) > 0
                    && StringUtils.equals(compositeQuery.getPanelType(), LIST.getType())) {
                builderQuery.setGroupBy(new ArrayList<>());
            }
        }
    }

    public static QueryValidateResult validateAndFormatQueryRangeParams(TraceQueryRangeParam traceQueryRangeParam) {
        CompositeQuery compositeQuery = traceQueryRangeParam.getCompositeQuery();
        if (compositeQuery == null) {
            throw new IllegalArgumentException("composite query is required");
        }
        // validate compositeQuery
        QueryValidateResult result = compositeQuery.validate();
        // for test
        monitorLogger.info(
                JsonUtils.toJsonStringIgnoreExp(MonitorLog.builder()
                        .withMeasure("trace_query_validate_result")
                        .withCubeVer("1.0.0")
                        .withTs(System.currentTimeMillis())
                        .addTag("success", String.valueOf(result.isSuccess()))
                        .addField("result", JsonUtils.toJsonStringIgnoreExp(result.getValidation()))
                        .addField("errorMessage", result.getErrorMessage())
                        .build())
        );
        if (!result.isSuccess()) {
            throw new IllegalArgumentException("composite query validation failed: " + result);
        }
        if (StringUtils.equals(compositeQuery.getQueryType(), QueryType.BUILDER.getType())) {
            // format query builder
            formatQueryBuilder(traceQueryRangeParam);
        }
        return result;
    }

    /**
     * formats the value to be used in clickhouse query
     *
     * @return
     */
    public static String clickHouseFormattedValue(Object value) {
        Object pointerValue = getPointerValue(value);
        if (pointerValue instanceof Byte || pointerValue instanceof Short || pointerValue instanceof Integer || pointerValue instanceof Long
                || pointerValue instanceof Integer || pointerValue instanceof Byte || pointerValue instanceof Short || pointerValue instanceof Integer || pointerValue instanceof Long) {
            return String.format("%d", pointerValue);
        } else if (pointerValue instanceof Float || pointerValue instanceof Double) {
            return String.format("%f", pointerValue);
        } else if (pointerValue instanceof String) {
            return String.format("'%s'", quoteEscapedString((String) pointerValue));
        } else if (pointerValue instanceof Boolean) {
            return String.format("%b", pointerValue);
        } else if (pointerValue.getClass().isArray()) {
            Object[] array = (Object[]) pointerValue;
            if (array.length == 0) {
                return "[]";
            }
            if (array[0] instanceof String) {
                StringBuilder str = new StringBuilder("[");
                for (int idx = 0; idx < array.length; idx++) {
                    str.append(String.format("'%s'", quoteEscapedString((String) array[idx])));
                    if (idx != array.length - 1) {
                        str.append(",");
                    }
                }
                str.append("]");
                return str.toString();
            } else if (array[0] instanceof Byte || array[0] instanceof Short || array[0] instanceof Integer || array[0] instanceof Long
                    || array[0] instanceof Float || array[0] instanceof Double || array[0] instanceof Boolean) {
                StringBuilder builder = new StringBuilder("[");
                builder.append(Joiner.on(",").join(array));
                builder.append("]");
                return builder.toString();
            } else {
                logger.error("invalid type for formatted value: " + array[0].getClass().getName());
                return "[]";
            }
        } else if (pointerValue instanceof List<?>) {
            List<?> list = (List<?>) pointerValue;
            if (list.size() == 0) {
                return "[]";
            }
            Object obj = list.get(0);
            if (obj instanceof String) {
                StringBuilder str = new StringBuilder("[");
                for (int idx = 0; idx < list.size(); idx++) {
                    str.append(String.format("'%s'", quoteEscapedString((String) list.get(idx))));
                    if (idx != list.size() - 1) {
                        str.append(",");
                    }
                }
                str.append("]");
                return str.toString();
            } else if (obj instanceof Byte || obj instanceof Short || obj instanceof Integer || obj instanceof Long
                    || obj instanceof Float || obj instanceof Double || obj instanceof Boolean) {
                StringBuilder builder = new StringBuilder("[");
                builder.append(Joiner.on(",").join(list));
                builder.append("]");
                return builder.toString();
            } else {
                logger.error("invalid type for formatted value: " + obj.getClass().getName());
                return "[]";
            }
        } else {
            logger.error("invalid type for formatted value: " + pointerValue.getClass().getName());
            return "";
        }
    }

    public static Object getPointerValue(Object value) {
        if (value instanceof Byte) {
            return ((Byte) value).byteValue();
        } else if (value instanceof Short) {
            return ((Short) value).shortValue();
        } else if (value instanceof Integer) {
            return ((Integer) value).intValue();
        } else if (value instanceof Long) {
            return ((Long) value).longValue();
        } else if (value instanceof Float) {
            return ((Float) value).floatValue();
        } else if (value instanceof Double) {
            return ((Double) value).doubleValue();
        } else if (value instanceof String) {
            return ((String) value);
        } else if (value instanceof Boolean) {
            return ((Boolean) value);
        } else if (value.getClass().isArray()) {
            Object[] array = (Object[]) value;
            Object[] values = new Object[array.length];
            for (int i = 0; i < array.length; i++) {
                values[i] = getPointerValue(array[i]);
            }
            return values;
        } else {
            return value;
        }
    }

    public static String quoteEscapedString(String str) {
        return str.replace("'", "''");
    }

    private static void formatQueryBuilder(TraceQueryRangeParam traceQueryRangeParam) {
        Map<String, BuilderQuery> builderQueries = traceQueryRangeParam.getCompositeQuery().getBuilderQueries();
        for (BuilderQuery query : builderQueries.values()) {
            // If the step interval is less than the minimum allowed step interval, set it to the minimum allowed step interval
            long minStep = minAllowedStepInterval(traceQueryRangeParam.getStart(), traceQueryRangeParam.getEnd());
            if (query.getStepInterval() < minStep) {
                query.setStepInterval(minStep);
            }

            // Remove the time shift function from the list of functions and set the shift by value
            query.setShiftBy(getTimeShiftBy(query));

            // fill in filter item value
            FilterSet filterSet = query.getFilters();
            if (filterSet == null || CollectionUtils.isEmpty(filterSet.getItems())) {
                continue;
            }
            populateFilterItemValue(traceQueryRangeParam, filterSet.getItems());
        }
    }

    private static void populateFilterItemValue(TraceQueryRangeParam traceQueryRangeParam, List<FilterItem> filterItems) {
        for (FilterItem item : filterItems) {
            Object value = item.getValue();
            if (value != null) {
                if (value instanceof String) {
                    String variableName = ((String) value).trim().replaceAll("\\{\\[\\.$\\]}", "");
                    if (traceQueryRangeParam.getVariables().containsKey(variableName)) {
                        item.setValue(traceQueryRangeParam.getVariables().get(variableName));
                    }
                } else if (value instanceof List) {
                    List<?> list = (List<?>) value;
                    if (!list.isEmpty()) {
                        if (list.get(0) instanceof String) {
                            String variableName = ((String) list.get(0)).trim().replaceAll("\\{\\[\\.$\\]}", "");
                            if (traceQueryRangeParam.getVariables().containsKey(variableName)) {
                                item.setValue(traceQueryRangeParam.getVariables().get(variableName));
                            }
                        }
                    }
                }
            }
            FilterOperator filterOperator = FilterOperator.from(item.getOp().toLowerCase());
            if (filterOperator != FilterOperator.IN && filterOperator != FilterOperator.NOT_IN) {
                // the value type should not be multiple values
                if (value instanceof List) {
                    throw new IllegalArgumentException("multiple values " + value + " are not allowed for operator `" + item.getOp() + "` for key `" + item.getKey().getKey() + "`");
                }
            }
        }
    }

    private static long getTimeShiftBy(BuilderQuery query) {
        long timeShiftBy = 0;
        if (CollectionUtils.isNotEmpty(query.getFunctions())) {
            for (int idx = 0; idx < query.getFunctions().size(); idx++) {
                Function function = query.getFunctions().get(idx);
                if (function.getName().equals(FunctionName.TIME_SHIFT.getName())) {
                    // move the function to the beginning of the list
                    // so any other function can use the shifted time
                    List<Function> fns = new ArrayList<>();
                    fns.add(function);
                    fns.addAll(query.getFunctions().subList(0, idx));
                    fns.addAll(query.getFunctions().subList(idx + 1, query.getFunctions().size()));
                    query.setFunctions(fns);
                    timeShiftBy = (long) function.getArgs().get(0);
                    break;
                }
            }
        }
        return timeShiftBy;
    }

    private static long minAllowedStepInterval(long start, long end) {
        long step = (end - start) / MAX_ALLOWED_POINTS_IN_TIME_SERIES / 1000;
        if (step < 60) {
            return step;
        }
        // return the nearest lower multiple of 60
        return step - step % 60;
    }

    public static Object validateAndCastValue(Object value, AttributeKeyDataType dataType) {
        switch (dataType) {
            case STRING:
                return castToString(value);
            case BOOL:
                return castToBoolean(value);
            case INT64:
                return castToLong(value);
            case FLOAT64:
                return castToDouble(value);
            default:
                return null;
        }
    }

    private static Object castToString(Object obj) {
        if (obj instanceof String) {
            return obj;
        } else if (obj instanceof Integer || obj instanceof Long || obj instanceof Float || obj instanceof Double || obj instanceof Boolean) {
            return String.valueOf(obj);
        } else if (obj instanceof Collection<?>) {
            List<String> result = new ArrayList<>();
            for (Object item : (Collection<?>) obj) {
                Object value = castToString(item);
                if (value == null) {
                    return null;
                }
                if (value instanceof Collection<?>) {
                    result.addAll((Collection<? extends String>) value);
                } else {
                    result.add(String.valueOf(value));
                }
            }
            return result;
        } else {
            return null;
        }
    }

    private static Object castToBoolean(Object v) {
        if (v instanceof Boolean) {
            return v;
        } else if (v instanceof String) {
            return Boolean.parseBoolean((String) v);
        } else if (v instanceof List) {
            List<Boolean> result = new ArrayList<>();
            for (Object item : (List<Object>) v) {
                Object value = castToBoolean(item);
                if (value == null) {
                    return null;
                }
                if (value instanceof List<?>) {
                    result.addAll((List<? extends Boolean>) value);
                } else {
                    result.add((Boolean) value);
                }
            }
            return result;
        } else {
            return null;
        }
    }

    private static Object castToLong(Object v) {
        if (v instanceof Integer || v instanceof Long) {
            return ((Number) v).longValue();
        } else if (v instanceof Float || v instanceof Double) {
            return ((Number) v).longValue();
        } else if (v instanceof String) {
            try {
                return Long.parseLong((String) v);
            } catch (NumberFormatException e) {
                return null;
            }
        } else if (v instanceof List) {
            List<Long> result = new ArrayList<>();
            for (Object item : (List<Object>) v) {
                Object value = castToLong(item);
                if (value == null) {
                    return null;
                }
                if (value instanceof List<?>) {
                    result.addAll((List<? extends Long>) value);
                } else {
                    result.add((Long) value);
                }
            }
            return result;
        } else {
            return null;
        }
    }

    private static Object castToDouble(Object v) {
        if (v instanceof Integer || v instanceof Long || v instanceof Float || v instanceof Double) {
            return ((Number) v).doubleValue();
        } else if (v instanceof String) {
            try {
                return Double.parseDouble((String) v);
            } catch (NumberFormatException e) {
                return null;
            }
        } else if (v instanceof List) {
            List<Double> result = new ArrayList<>();
            for (Object item : (List<Object>) v) {
                Object value = castToDouble(item);
                if (value == null) {
                    return null;
                }
                if (value instanceof List<?>) {
                    result.addAll((List<? extends Double>) value);
                } else {
                    result.add((Double) value);
                }
            }
            return result;
        } else {
            return null;
        }
    }
}