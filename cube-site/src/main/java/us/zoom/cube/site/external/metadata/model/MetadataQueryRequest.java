package us.zoom.cube.site.external.metadata.model;

import lombok.Data;
import java.util.List;

@Data
public class MetadataQueryRequest {
    private String typeName;
    private List<String> fields;
    private List<Condition> conditions;
    private int pageNumber;
    private int pageSize;

    @Data
    public static class Condition {
        private String field;
        private String operateType;
        private String value;
    }
} 