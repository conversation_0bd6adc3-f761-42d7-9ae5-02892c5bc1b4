package us.zoom.cube.site.config;

import org.springframework.stereotype.Service;
import us.zoom.cloud.secrets.spring.annotation.SecretValue;

@Service
public class HotReloadHolder {

    @SecretValue("cube.dataSource.clickhouse.password")
    private String clickhousePassword;

    @SecretValue("okta.oauth2.client-id")
    private String clientId;

    @SecretValue("okta.oauth2.client-secret")
    private String clientSecret;

    @SecretValue("zdca.asyncmq.pass")
    private String asyncmqPass;

    @SecretValue("jfrog_api_token")
    private String jfrogApiToken;

}
