package us.zoom.cube.site.biz;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import us.zoom.cube.lib.common.ServerTypeEnum;
import us.zoom.cube.lib.probe.ProbePointInfo;
import us.zoom.cube.site.core.ProbeGroupHandler;
import us.zoom.cube.site.core.ProbeGroupServerHandler;
import us.zoom.cube.site.core.ServerHandler;
import us.zoom.cube.site.infra.utils.IdUtils;
import us.zoom.cube.site.lib.ResponseObject;
import us.zoom.infra.dao.model.ProbeGroupDO;
import us.zoom.infra.dao.model.ProbeGroupServerDO;
import us.zoom.infra.dao.model.ServerDO;
import us.zoom.infra.enums.CommonStatusEnum;

/**
 * @Author: <EMAIL>
 * @ModuleOwner: <EMAIL>
 * @Date: 06/30/2022 15:28
 * @Description:
 */
public class ProbeCfgServiceTest {
    private ProbeCfgService probeCfgService;

    private ServerHandler serverHandler;
    private ProbeGroupHandler probeGroupHandler;
    private ProbeGroupServerHandler probeGroupServerHandler;

    @BeforeEach
    public void before() {
        serverHandler = Mockito.mock(ServerHandler.class);
        probeGroupHandler = Mockito.mock(ProbeGroupHandler.class);
        probeGroupServerHandler = Mockito.mock(ProbeGroupServerHandler.class);
        probeCfgService = new ProbeCfgService(serverHandler, probeGroupHandler, probeGroupServerHandler);
    }

    private String ip = "*********";
    private String version = "1.0.0";

    @Test
    public void testServerNotExist() {
        Mockito.when(serverHandler.getByIpAndType(ip, ServerTypeEnum.probe.name())).thenReturn(null);
        ProbePointInfo info = new ProbePointInfo();
        info.setIp(ip);
        info.setVersion("1.0.0");

        ResponseObject object = probeCfgService.getProbeGroupCfg(info);
        Assertions.assertEquals(object.getData(), null);
        Assertions.assertEquals(object.getMessage(), "probe point not exist");
    }

    @Test
    public void testServerNotExistProbeGroupServer() {
        String serverId = IdUtils.generateId();
        ServerDO serverDO = new ServerDO();
        serverDO.setId(serverId);
        serverDO.setIp(ip);
        Mockito.when(serverHandler.getByIpAndType(ip, ServerTypeEnum.probe.name())).thenReturn(serverDO);
        ProbePointInfo info = new ProbePointInfo();
        info.setIp(ip);
        info.setVersion(version);

        Mockito.when(probeGroupServerHandler.getByServerId(serverDO.getId())).thenReturn(null);

        ResponseObject object = probeCfgService.getProbeGroupCfg(info);
        Assertions.assertEquals(object.getData(), null);
        Assertions.assertEquals(object.getMessage(), "probe point has no group assigned, id:" + serverDO.getId());
    }

    @Test
    public void testServerNotExistProbeGroup() {
        String serverId = IdUtils.generateId();
        ServerDO serverDO = new ServerDO();
        serverDO.setId(serverId);
        serverDO.setIp(ip);
        Mockito.when(serverHandler.getByIpAndType(ip, ServerTypeEnum.probe.name())).thenReturn(serverDO);
        ProbePointInfo info = new ProbePointInfo();
        info.setIp(ip);
        info.setVersion("1.0.0");

        String groupId = IdUtils.generateId();
        ProbeGroupServerDO probeGroupServerDO = new ProbeGroupServerDO();
        probeGroupServerDO.setServerId(serverId);
        probeGroupServerDO.setId(groupId);
        Mockito.when(probeGroupServerHandler.getByServerId(serverDO.getId())).thenReturn(probeGroupServerDO);

        ProbeGroupDO probeGroupDO = new ProbeGroupDO();
        Mockito.when(probeGroupHandler.getById(groupId)).thenReturn(null);

        ResponseObject object = probeCfgService.getProbeGroupCfg(info);
        Assertions.assertEquals(object.getData(), null);
        Assertions.assertEquals(object.getMessage(), "probe group not exist, id:" + groupId);
    }

    @Test
    public void test() {
        String serverId = IdUtils.generateId();
        ServerDO serverDO = new ServerDO();
        serverDO.setId(serverId);
        serverDO.setIp(ip);
        Mockito.when(serverHandler.getByIpAndType(ip, ServerTypeEnum.probe.name())).thenReturn(serverDO);
        ProbePointInfo info = new ProbePointInfo();
        info.setIp(ip);
        info.setVersion("1.0.0");

        String groupId = IdUtils.generateId();
        ProbeGroupServerDO probeGroupServerDO = new ProbeGroupServerDO();
        probeGroupServerDO.setServerId(serverId);
        probeGroupServerDO.setProbeGroupId(groupId);
        Mockito.when(probeGroupServerHandler.getByServerId(serverDO.getId())).thenReturn(probeGroupServerDO);

        ProbeGroupDO probeGroupDO = new ProbeGroupDO();
        probeGroupDO.setName("name");
        probeGroupDO.setCity("city");
        probeGroupDO.setCountry("country");
        probeGroupDO.setCloud("cloud");
        probeGroupDO.setOperator("operator");
        probeGroupDO.setStatus(CommonStatusEnum.enable.name());
        Mockito.when(probeGroupHandler.getById(groupId)).thenReturn(probeGroupDO);

        ResponseObject object = probeCfgService.getProbeGroupCfg(info);
        Mockito.doNothing().when(serverHandler).updateUpStatusTimeAndVersionByIpType(ip, version, ServerTypeEnum.probe.name());
        Assertions.assertEquals(object.getData() != null, true);
    }
}
