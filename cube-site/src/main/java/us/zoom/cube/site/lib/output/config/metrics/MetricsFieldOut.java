package us.zoom.cube.site.lib.output.config.metrics;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @create 2020/7/19 10:03 AM
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class MetricsFieldOut {

    String id;

    String metricsId;

    String fieldName;

    int fieldType;

    String description;
//
//    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    Date createTime = new Date();
//
//    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    Date modifyTime = new Date();
}
