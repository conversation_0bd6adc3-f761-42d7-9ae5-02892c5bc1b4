package us.zoom.cube.site.core.clickhouse;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import us.zoom.infra.clickhouse.ClickhouseHandler;
import us.zoom.infra.clickhouse.ClickhouseSchema;
import us.zoom.infra.enums.FieldTypeEnum;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/01/03 9:32 AM
 */
@ExtendWith(MockitoExtension.class)
public class TableParseTest {


    @BeforeEach
    public void initMocks() {
        MockitoAnnotations.initMocks(this);
    }


    @Test
    public void testNullableDateTime64(){
        String db="Meeting_Web_asyncmq_application_cube_ch_env1_132d";
        String table="asyncmq_client_consumer_stats";
        String sql="CREATE TABLE zcc_report_dev.zcc_report_dataset_queue_all_history_v3\n" +
                "(\n" +
                "    `id` String,\n" +
                "    `taskId` String,\n" +
                "    `accountId` String,\n" +
                "    `parentAccountId` String,\n" +
                "    `engagementId` String,\n" +
                "    `queueId` String,\n" +
                "    `queueName` String,\n" +
                "    `enterChannel` Nullable(String) DEFAULT NULL,\n" +
                "    `enterSource` Nullable(String) DEFAULT NULL,\n" +
                "    `exitChannel` Nullable(String) DEFAULT NULL,\n" +
                "    `exitSource` Nullable(String) DEFAULT NULL,\n" +
                "    `datasetType` Nullable(String) DEFAULT NULL,\n" +
                "    `engagementDirection` Nullable(String) DEFAULT NULL,\n" +
                "    `originMethod` Nullable(String) DEFAULT NULL,\n" +
                "    `handled` Int32 DEFAULT 0,\n" +
                "    `hold` Int32 DEFAULT 0,\n" +
                "    `warmTransferInitiated` Int32 DEFAULT 0,\n" +
                "    `warmTransferCompleted` Int32 DEFAULT 0,\n" +
                "    `directTransfer` Int32 DEFAULT 0,\n" +
                "    `transferInitiated` Int32 DEFAULT 0,\n" +
                "    `transferCompleted` Int32 DEFAULT 0,\n" +
                "    `warmConference` Int32 DEFAULT 0,\n" +
                "    `conference` Int32 DEFAULT 0,\n" +
                "    `autoCloseDisconnect` Nullable(Int32) DEFAULT 0,\n" +
                "    `inboundHandled` Int32 DEFAULT 0,\n" +
                "    `queueOffered` Int32 DEFAULT 0,\n" +
                "    `queueOutbound` Int32 DEFAULT 0,\n" +
                "    `outboundHandled` Int32 DEFAULT 0,\n" +
                "    `callbackRegistered` Int32 DEFAULT 0,\n" +
                "    `callbackRetryRegistered` Nullable(Int32) DEFAULT 0,\n" +
                "    `callbackRetryTriggered` Nullable(Int32) DEFAULT 0,\n" +
                "    `callbackHandled` Int32 DEFAULT 0,\n" +
                "    `callbackAbandoned` Int32 DEFAULT 0,\n" +
                "    `callbackCanceled` Int32 DEFAULT 0,\n" +
                "    `callbackTimeout` Int32 DEFAULT 0,\n" +
                "    `callbackConnected` Nullable(Int32) DEFAULT 0,\n" +
                "    `callbackOutcome` Nullable(String) DEFAULT NULL,\n" +
                "    `callbackStatus` Nullable(String) DEFAULT NULL,\n" +
                "    `outboundConnected` Int32 DEFAULT 0,\n" +
                "    `abandoned` Int32 DEFAULT 0,\n" +
                "    `shortAbandoned` Int32 DEFAULT 0,\n" +
                "    `longAbandoned` Int32 DEFAULT 0,\n" +
                "    `overflow` Int32 DEFAULT 0,\n" +
                "    `slaAchieved` Int32 DEFAULT 0,\n" +
                "    `slaRequired` Int32 DEFAULT 0,\n" +
                "    `talkDuration` Nullable(Int64) DEFAULT NULL,\n" +
                "    `conversationDuration` Nullable(Int64) DEFAULT NULL,\n" +
                "    `wrapUpDuration` Nullable(Int64) DEFAULT NULL,\n" +
                "    `handleDuration` Nullable(Int64) DEFAULT NULL,\n" +
                "    `holdDuration` Nullable(Int64) DEFAULT NULL,\n" +
                "    `warmTransferDuration` Nullable(Int64) DEFAULT NULL,\n" +
                "    `warmConferenceDuration` Nullable(Int64) DEFAULT NULL,\n" +
                "    `conferenceDuration` Nullable(Int64) DEFAULT NULL,\n" +
                "    `inboundTalkDuration` Nullable(Int64) DEFAULT NULL,\n" +
                "    `inboundConversationDuration` Nullable(Int64) DEFAULT NULL,\n" +
                "    `inboundWrapUpDuration` Nullable(Int64) DEFAULT NULL,\n" +
                "    `inboundHandleDuration` Nullable(Int64) DEFAULT NULL,\n" +
                "    `inQueueWaitDuration` Nullable(Int64) DEFAULT NULL,\n" +
                "    `abandonDuration` Nullable(Int64) DEFAULT NULL,\n" +
                "    `outboundTalkDuration` Nullable(Int64) DEFAULT NULL,\n" +
                "    `outboundConversationDuration` Nullable(Int64) DEFAULT NULL,\n" +
                "    `outboundWrapUpDuration` Nullable(Int64) DEFAULT NULL,\n" +
                "    `outboundHandleDuration` Nullable(Int64) DEFAULT NULL,\n" +
                "    `dialDuration` Nullable(Int64) DEFAULT NULL,\n" +
                "    `callbackTalkDuration` Nullable(Int64) DEFAULT NULL,\n" +
                "    `callbackConversationDuration` Nullable(Int64) DEFAULT NULL,\n" +
                "    `callbackWrapUpDuration` Nullable(Int64) DEFAULT NULL,\n" +
                "    `callbackHandleDuration` Nullable(Int64) DEFAULT NULL,\n" +
                "    `callbackWaitDuration` Nullable(Int64) DEFAULT NULL,\n" +
                "    `callbackDisconnectedDuration` Nullable(Int64) DEFAULT NULL,\n" +
                "    `callbackPendingDuration` Nullable(Int64) DEFAULT NULL,\n" +
                "    `speedOfAnswerDuration` Nullable(Int64) DEFAULT NULL,\n" +
                "    `callbackAtTime` DateTime64(3) DEFAULT 0,\n" +
                "    `callbackAtTimeV2` Nullable(DateTime64(3)) DEFAULT NULL,\n" +
                "    `callbackAtTimeV3` Nullable(DateTime64) DEFAULT NULL,\n" +
                "    `enterTime` DateTime64(3) DEFAULT 0,\n" +
                "    `exitTime` DateTime64(3) DEFAULT 0,\n" +
                "    `createTime` DateTime64(3) DEFAULT 0,\n" +
                "    `modifyTime` DateTime64(3) DEFAULT 0,\n" +
                "    `time` DateTime64(3)\n" +
                ")\n" +
                "ENGINE = Distributed('{cluster}', 'zcc_report_dev', 'zcc_report_dataset_queue_all_history_v3_click_local', sipHash64(accountId))";

        ClickhouseSchema clickhouseSchema = ClickhouseHandler.parseCreateSql(db, table, sql);
        for(Map.Entry<String, FieldTypeEnum>entry: clickhouseSchema.getColDef().entrySet()){
            if(entry.getKey().contains("callbackAtTimeV")){
                Assertions.assertEquals(FieldTypeEnum.nullable_datetime,entry.getValue());
            }
        }
    }

    @Test
    public void testNormalDistributedTable(){
        String db="Meeting_Web_asyncmq_application_cube_ch_env1_132d";
        String table="asyncmq_client_consumer_stats";
        String sql="CREATE TABLE Meeting_Web_asyncmq_application_cube_ch_env1_132d.asyncmq_client_consumer_stats\n" +
                "(\n" +
                "    `app` String DEFAULT 'no_available',\n" +
                "    `hostName` String DEFAULT 'no_available',\n" +
                "    `failover_count` Float64\n" +
                ")\n" +
                "ENGINE = Distributed('{cluster}', 'Meeting_Web_asyncmq_application_cube_ch_env1_132d', 'asyncmq_client_consumer_stats_click_local', rand())";

        ClickhouseSchema clickhouseSchema = ClickhouseHandler.parseCreateSql(db, table, sql);
        Assertions.assertEquals(clickhouseSchema.getLocalTable(), "asyncmq_client_consumer_stats_click_local");
        Assertions.assertEquals(4,clickhouseSchema.getColDef().size());
    }

    @Test
    public void testNormalLocalTable(){
        String db="Meeting_Web_asyncmq_application_cube_ch_env1_132d";
        String table="asyncmq_client_consumer_stats";
        String sql="CREATE TABLE Meeting_Web_asyncmq_application_cube_ch_env1_132d.asyncmq_client_consumer_stats_click_local\n" +
                "(\n" +
                "    `time` DateTime,\n" +
                "    `app` String,\n" +
                "    `hostName` String,\n" +
                "    `failover_count` Float64\n" +
                ")\n" +
                "ENGINE = ReplicatedMergeTree('/clickhouse/tables/{shard}/Meeting_Web_asyncmq_application_cube_ch_env1_132d.asyncmq_client_consumer_stats_click_local.3d7db90b3d9345f7ae2c01c6205222eb', '{replica}')\n" +
                "PARTITION BY toMonday(time + toIntervalDay(3))\n" +
                "ORDER BY time\n" +
                "TTL time + toIntervalDay(5)\n" +
                "SETTINGS index_granularity = 8192";

        ClickhouseSchema clickhouseSchema = ClickhouseHandler.parseCreateSql(db, table, sql);
        Assertions.assertEquals(clickhouseSchema.getZkPath(), "/clickhouse/tables/{shard}/Meeting_Web_asyncmq_application_cube_ch_env1_132d.asyncmq_client_consumer_stats_click_local.3d7db90b3d9345f7ae2c01c6205222eb");
        Assertions.assertEquals(4,clickhouseSchema.getColDef().size());
    }

    @Test
    public void testWithCodec(){
        String db="Meeting_Web_asyncmq_application_cube_ch_env1_132d";
        String table="asyncmq_client_consumer_stats";
        String sql="CREATE TABLE Meeting_Web_asyncmq_application_cube_ch_env1_132d.nginx_access_ct_click_local\n" +
                "(\n" +
                "    `time` DateTime CODEC(Delta(4), LZ4),\n" +
                "    `requestTime` Float64 CODEC(Gorilla),\n" +
                "    `hostName` LowCardinality(String),\n" +
                "    `request` LowCardinality(String),\n" +
                "    `count_4xx` Float64 CODEC(ZSTD(1)),\n" +
                "    `instanceId` String,\n" +
                "    `count_5xx` Float64 CODEC(Delta(8), ZSTD(1)),\n" +
                "    `count_2xx` Float64 CODEC(DoubleDelta, ZSTD(1)),\n" +
                "    `count_3xx` Float64 CODEC(Gorilla, LZ4),\n" +
                ")\n" +
                "ENGINE = ReplicatedMergeTree('/clickhouse/tables/{shard}/Meeting_Web_asyncmq_application_cube_ch_env1_132d.nginx_access_ct_click_local.2250d301df4943f4b7e1104102b41680', '{replica}')\n" +
                "PARTITION BY toMonday(time + toIntervalDay(3))\n" +
                "ORDER BY time\n" +
                "TTL time + toIntervalDay(40)\n" +
                "SETTINGS index_granularity = 8192";

        ClickhouseSchema clickhouseSchema = ClickhouseHandler.parseCreateSql(db, table, sql);
        Assertions.assertEquals(clickhouseSchema.getZkPath(), "/clickhouse/tables/{shard}/Meeting_Web_asyncmq_application_cube_ch_env1_132d.nginx_access_ct_click_local.2250d301df4943f4b7e1104102b41680");
        Assertions.assertEquals(9,clickhouseSchema.getColDef().size());
    }
}
