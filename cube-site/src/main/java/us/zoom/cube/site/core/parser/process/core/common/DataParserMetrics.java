package us.zoom.cube.site.core.parser.process.core.common;

import lombok.Data;
import us.zoom.cube.site.core.parser.process.core.monitoring.DataParserFields;
import us.zoom.cube.site.core.parser.process.core.monitoring.DataParserTags;
import us.zoom.infra.utils.DateUtils;

@Data
public class DataParserMetrics extends AbstractMetrics {
    private DataParserFields fields;
    private DataParserTags tags;
    private Long ts;

    public DataParserMetrics(DataParserTags tags) {
        this.fields = new DataParserFields();
        this.tags = tags;
        this.ts = DateUtils.getPeriodBeginTime(System.currentTimeMillis(), 60);
    }

    public void updateTags(DataParserTags tags) {
        this.tags = tags;
    }

    public Long getTs() {
        return ts;
    }

    public DataParserFields getFields() {
        return fields;
    }

    public DataParserTags getTags() {
        return tags;
    }

    @Override
    public void incrFilterSize() {
        fields.incrFilterSize();
    }

    @Override
    public void incrBlankSize() {
        fields.incrBlankSize();
    }

    @Override
    public void delayTime(int delayTime) {
        fields.delayTime(delayTime);
    }
}
