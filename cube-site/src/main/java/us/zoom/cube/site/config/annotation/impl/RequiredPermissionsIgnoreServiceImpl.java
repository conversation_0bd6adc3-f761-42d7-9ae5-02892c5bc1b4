package us.zoom.cube.site.config.annotation.impl;

import lombok.RequiredArgsConstructor;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;
import us.zoom.cube.site.biz.AuthService;
import us.zoom.cube.site.lib.BasePara;

@Aspect
@Component
@RequiredArgsConstructor
public class RequiredPermissionsIgnoreServiceImpl {
    private final AuthService authService;

    @Pointcut("@annotation(us.zoom.cube.site.config.annotation.RequiredPermissionsIgnoreService)")
    public void pointCut() {

    }

    @Around(value = "pointCut()")
    public Object Around(ProceedingJoinPoint point) throws Throwable {
        BasePara basePara = new BasePara();
        authService.checkAuth(basePara);
        return point.proceed();
    }
}
