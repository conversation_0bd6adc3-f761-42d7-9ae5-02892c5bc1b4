package us.zoom.cube.site.lib.monitor;


import lombok.Data;
import us.zoom.cube.sdk.annotation.CubeMonitorLog;
import us.zoom.cube.sdk.annotation.Field;
import us.zoom.cube.sdk.annotation.Tag;

@Data
@CubeMonitorLog(measure = "AgentHeartBeatDealMetric")
public class AgentHeartBeatDealMetric {
    @Tag
    private String host;
    @Tag
    private String ip;
    @Tag
    private String phase;
    @Tag
    private String status;
    @Field
    private long cost;
    @Field
    private String error;

    public AgentHeartBeatDealMetric(String host, String ip, String phase, String status, long cost, String error) {
        this.host = host;
        this.ip = ip;
        this.phase = phase;
        this.status = status;
        this.cost = cost;
        this.error = error;
    }
}
