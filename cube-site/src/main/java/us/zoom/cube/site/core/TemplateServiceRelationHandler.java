package us.zoom.cube.site.core;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.site.infra.utils.IdUtils;
import us.zoom.infra.dao.model.TemplateServiceRelationDO;
import us.zoom.infra.dao.service.TemplateServiceRelationDAO;

import java.util.Collections;
import java.util.List;

/**
 * @Author: Jesse <PERSON>
 * @Date:12/19/2022 17:22
 * @Description:
 */
@Component
@RequiredArgsConstructor
public class TemplateServiceRelationHandler {

    private final TemplateServiceRelationDAO relationDAO;

    public int batchAdd(List<TemplateServiceRelationDO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return 0;
        }
        for (TemplateServiceRelationDO relation : list) {
            relation.setId(IdUtils.generateId());
        }
        return relationDAO.batchAdd(list);
    }

    public List<TemplateServiceRelationDO> listByTemplateId(String templateId) {
        List<TemplateServiceRelationDO> relations = relationDAO.listByTemplateId(templateId);
        return relations;
    }

    public List<TemplateServiceRelationDO> listByServiceId(String serviceId) {
        List<TemplateServiceRelationDO> relations = relationDAO.listByServiceId(serviceId);
        return relations;
    }

    public List<TemplateServiceRelationDO> listByTemplateIds(List<String> templateIds) {
        if (CollectionUtils.isEmpty(templateIds)) {
            return Collections.emptyList();
        }
        return relationDAO.listByTemplateIds(templateIds);
    }

}
