package us.zoom.cube.site.biz.alarm.group;

import com.google.common.collect.Lists;
import com.okta.commons.lang.Collections;
import com.zoom.op.monitor.dao.alarm.AlarmDefinitionDao;
import com.zoom.op.monitor.domain.alarm.AlarmDefinition;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.lib.utils.expression.CustomAviatorUtils;
import us.zoom.cube.site.api.intercept.AuthInterceptor;
import us.zoom.cube.site.biz.syspara.AlarmGroupParaService;
import us.zoom.cube.site.core.TenantHandler;
import us.zoom.cube.site.core.alarm.group.AlarmGroupAlarmItemHandler;
import us.zoom.cube.site.core.alarm.group.AlarmGroupHandler;
import us.zoom.cube.site.core.alarm.group.AlarmGroupServiceItemHandler;
import us.zoom.cube.site.core.auth.AuthHandler;
import us.zoom.cube.site.infra.utils.IdUtils;
import us.zoom.cube.site.lib.input.alarm.group.*;
import us.zoom.cube.site.lib.output.config.metrics.FieldOrTag;
import us.zoom.cube.site.lib.query.AlarmGroupTagQuery;
import us.zoom.infra.dao.model.AlarmGroupAlarmItemDO;
import us.zoom.infra.dao.model.AlarmGroupDO;
import us.zoom.infra.dao.model.AlarmGroupServiceItemDO;
import us.zoom.infra.dao.model.TenantDO;
import us.zoom.infra.enums.AlarmGroupTagOperatorEnum;
import us.zoom.infra.enums.alarm.AlarmGroupConditionTypeEnum;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Matcher;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static us.zoom.cube.site.biz.alarm.group.AlarmGroupService.V2;
import static us.zoom.infra.utils.AlarmGroupConstants.*;
import static us.zoom.infra.utils.AlarmSilenceConstants.MESSAGE;
import static us.zoom.infra.utils.AlarmSilenceConstants.SUCCESS;
import static us.zoom.infra.utils.RegexConstants.CONFIG_NAME_PATTERN;
import static us.zoom.infra.utils.RegexConstants.CONFIG_SPACE_NAME_PATTERN;

/**
 * @Author: <EMAIL>
 * @ModuleOwner: <EMAIL>
 * @Date: 12/29/2022 16:44
 * @Description:
 */
@Component
@Slf4j
public class AlarmGroupUtilService {

    @Autowired
    private TenantHandler tenantHandler;

    @Autowired
    private AuthHandler authHandler;

    @Autowired
    private AlarmGroupHandler alarmGroupHandler;

    @Autowired
    private AlarmGroupServiceItemHandler alarmGroupServiceItemHandler;

    @Autowired
    private AlarmGroupAlarmItemHandler alarmGroupAlarmItemHandler;

    @Autowired
    private AlarmDefinitionDao alarmDefinitionDao;

    @Autowired
    private AlarmGroupParaService alarmGroupParaService;

    @Autowired
    private AlarmGroupService alarmGroupService;

    public void check(AlarmGroupInput alarmGroupInput){
        // 1. check basic input, such as format
        // 2. check ownership of service and user
        // 3. check ownership of service and alarm
        return;
    }

    public void checkAddAlarmGroupParameter(AlarmGroupInput alarmGroupInput, String version){
        checkAlarmGroupInputBasically(alarmGroupInput, version);
        String alarmGroupName = alarmGroupInput.getName();
        AlarmGroupDO alarmGroupDO = alarmGroupHandler.findByName(alarmGroupName);
        Assert.isTrue(alarmGroupDO == null, "The AlarmGroup name is duplicated");

    }

    private void checkAlarmGroupInputBasically(AlarmGroupInput alarmGroupInput, String version){
        Assert.isTrue(!StringUtils.isEmpty(alarmGroupInput.getName()),"AlarmGroup name can not be empty");
        String alarmGroupName = alarmGroupInput.getName();
        Matcher nameMatcher = CONFIG_SPACE_NAME_PATTERN.matcher(alarmGroupName);
        Assert.isTrue(nameMatcher.find(), "AlarmGroup names can only contain [0-9a-zA-Z_ ]");

        Assert.isTrue(!Collections.isEmpty(alarmGroupInput.getServiceItemList()), "AlarmGroup must contain at least one service");

        // check ALL_SERVICE
        for(AlarmGroupServiceItemInput alarmGroupServiceItemInput: alarmGroupInput.getServiceItemList()){
            if(ALL_SERVICE_ID.equals(alarmGroupServiceItemInput.getServiceId()) && ALL_SERVICE_NAME.equals(alarmGroupServiceItemInput.getServiceName())){
                Assert.isTrue(alarmGroupInput.getServiceItemList().size() == 1, "ALL_SERVICE related alarmGroup is exclusive");
                Assert.isTrue(!Collections.isEmpty(alarmGroupServiceItemInput.getAlarmItemList()) && alarmGroupServiceItemInput.getAlarmItemList().size() == 1, "ALL_SERVICE can only contain one Alarm");
                AlarmGroupAlarmItemInput alarmGroupAlarmItemInput = alarmGroupServiceItemInput.getAlarmItemList().get(0);
                Assert.isTrue(ALL_ALARM_ID.equals(alarmGroupAlarmItemInput.getAlarmId()) && ALL_ALARM_NAME.equals(alarmGroupAlarmItemInput.getAlarmName()), "ALL_SERVICE can only relate to ALL_ALARM");
                // Assert.isTrue(CollectionUtils.isEmpty(alarmGroupServiceItemInput.getGroupTagItemList()), "ALL_SERVICE does not support group tag");
                Assert.isTrue(authHandler.canCrossAndOperate(alarmGroupInput.getUserId()), "user does not have the access to operate ALL_SERVICE related AlarmGroup");
                return;
            }
        }
        checkAlarmGroupInputBasically(version, alarmGroupInput.getServiceItemList(), alarmGroupInput.getUserId());

    }

    public void checkAlarmGroupInputBasically(String version, List<AlarmGroupServiceItemInput> serviceItemList, String userId) {
        List<TenantDO> allTenantList = tenantHandler.getAllTenantFromCache();
        Set<String> allTenantIdSet = allTenantList.stream().map(e -> e.getId()).collect(Collectors.toSet());

        List<TenantDO> hasPermissionServiceList = authHandler.getServiceUserCanOperate(userId);
        Set<String> hasPermissionServiceIdSet = hasPermissionServiceList.stream().map(e -> e.getId()).collect(Collectors.toSet());
        
        for (AlarmGroupServiceItemInput alarmGroupServiceItemInput : serviceItemList) {
            if (!allTenantIdSet.contains(alarmGroupServiceItemInput.getServiceId())) {
                Assert.isTrue(false, "the service " + alarmGroupServiceItemInput.getServiceName() + " is not existed");
            }
            if (!hasPermissionServiceIdSet.contains(alarmGroupServiceItemInput.getServiceId())) {
                Assert.isTrue(false, "user do not has the permission to operate this service " + alarmGroupServiceItemInput.getServiceName());
            }

            List<AlarmGroupAlarmItemInput> alarmGroupAlarmItemInputList = alarmGroupServiceItemInput.getAlarmItemList();
            Assert.isTrue(!CollectionUtils.isEmpty(alarmGroupAlarmItemInputList), "alarmList can not be empty");

            AlarmGroupTagQuery alarmGroupTagQuery = new AlarmGroupTagQuery();
            alarmGroupTagQuery.setAlarmNameList(alarmGroupAlarmItemInputList.stream().map(AlarmGroupAlarmItemInput::getAlarmName).collect(Collectors.toList()));
            alarmGroupTagQuery.setServiceName(alarmGroupServiceItemInput.getServiceName());
            List<FieldOrTag> fieldAndTagList = (List<FieldOrTag>) alarmGroupService.listTagAndFiled(alarmGroupTagQuery).getData();
            for (AlarmGroupAlarmItemInput alarmGroupAlarmItemInput : alarmGroupAlarmItemInputList) {
                if (ALL_ALARM_ID.equals(alarmGroupAlarmItemInput.getAlarmId()) && ALL_ALARM_NAME.equals(alarmGroupAlarmItemInput.getAlarmName())) {
                    Assert.isTrue(alarmGroupAlarmItemInputList.size() == 1, "ALL_ALARM is exclusive");
                }
            }
            List<AlarmGroupTagItemInput> alarmGroupTagItemInputList = alarmGroupServiceItemInput.getGroupTagItemList();
            for (AlarmGroupTagItemInput alarmGroupTagItemInput : alarmGroupTagItemInputList) {
                if (V2.equals(version)) {
                    if (null == alarmGroupTagItemInput.getConditionType() || !AlarmGroupConditionTypeEnum.isValid(alarmGroupTagItemInput.getConditionType())) {
                        Assert.isTrue(false, "AlarmGroup config is not valid");
                    }
                    if (StringUtils.isNotBlank(alarmGroupTagItemInput.getOperator()) && !AlarmGroupTagOperatorEnum.isValid(alarmGroupTagItemInput.getOperator())) {
                        Assert.isTrue(false, "AlarmGroup Tag is not valid");
                    }
                    if (null != alarmGroupTagItemInput.getConditionType() && AlarmGroupConditionTypeEnum.EXPRESSION.getValue().equals(alarmGroupTagItemInput.getConditionType())) {
                        Map<String, Object> result = checkAviatorExpression(alarmGroupTagItemInput.getExpression(), fieldAndTagList);
                        boolean judge = (Boolean) result.get(SUCCESS);
                        String message = String.valueOf(result.get(MESSAGE));
                        Assert.isTrue(judge, "The expression judgement error; expression[" + alarmGroupTagItemInput.getExpression() + "] is invalid, the error msg is " + message);
                    }
                } else {
                    if (StringUtils.isEmpty(alarmGroupTagItemInput.getTagName()) || StringUtils.isEmpty(alarmGroupTagItemInput.getThreshold()) || !AlarmGroupTagOperatorEnum.isValid(alarmGroupTagItemInput.getOperator())) {
                        Assert.isTrue(false, "AlarmGroup Tag is not valid");
                    }
                }
                alarmGroupTagItemInput.trimThreshold();
            }
        }
    }

    public Map<String, Object> checkAviatorExpression(String expression, List<FieldOrTag> fieldOrTags) {
        Map<String, Object> map = fieldOrTags.stream().collect(Collectors.toMap(FieldOrTag::getFieldOrTagName, FieldOrTag::getSampleValue, (a, b) -> a));
        return checkAviatorExpression(expression, map);
    }

    public Map<String, Object> checkAviatorExpression(String expression, Map<String, Object> map) {
        Map<String, Object> result = new HashMap<>();
        if (StringUtils.isEmpty(expression)) {
            return createResult(false, "the expression is null");
        }

        try {
            List<String> variableNames = CustomAviatorUtils.compile(expression, true).getVariableFullNames();
            for (String v : variableNames) {
                if (!map.containsKey(v)) {
                    log.error("The silence expression[" + expression + "] is invalid, the error msg is " + "Could not find variable " + v);
                    return createResult(false, "the " + v + " cannot be used as a variable");

                }
            }
            CustomAviatorUtils.execute(expression, map, true);
        } catch (Exception e) {
            log.error("The silence expression[" + expression + "] is invalid, the error msg is " + e.getMessage());
            return createResult(false, e.getMessage());
        }
        return createResult(true, null);
    }

    private Map<String, Object> createResult(boolean success, String message) {
        Map<String, Object> result = new HashMap<>();
        result.put(SUCCESS, success);
        result.put(MESSAGE, message);
        return result;
    }

    public void checkOutAddAlarmGroupParameter(AlarmGroupInput alarmGroupInput){
        checkOutAlarmGroupInputBasically(alarmGroupInput);
        String alarmGroupName = alarmGroupInput.getName();
        AlarmGroupDO alarmGroupDO = alarmGroupHandler.findByName(alarmGroupName);
        Assert.isTrue(alarmGroupDO == null, "The AlarmGroup name is duplicated");
    }

    public void checkOutUpdateAlarmGroupParameter(AlarmGroupInput alarmGroupInput){
        checkOutAlarmGroupInputBasically(alarmGroupInput);
        AlarmGroupDO alarmGroupDO = alarmGroupHandler.findByName(alarmGroupInput.getName());
        Assert.isTrue(alarmGroupDO != null, "alarmGroup is not existed");
        Boolean hasPermissionForOld = canOperateAlarmGroup(AuthInterceptor.getUserId(), alarmGroupDO.getId());
        Assert.isTrue(hasPermissionForOld, "User do not have the permission to this AlarmGroup");
    }

    private void checkOutAlarmGroupInputBasically(AlarmGroupInput alarmGroupInput) {
        Assert.isTrue(!StringUtils.isEmpty(alarmGroupInput.getName()), "AlarmGroup name can not be empty");
        String alarmGroupName = alarmGroupInput.getName();
        Matcher nameMatcher = CONFIG_NAME_PATTERN.matcher(alarmGroupName);
        Assert.isTrue(nameMatcher.find(), "AlarmGroup names can only contain [0-9a-zA-Z_]");

        Assert.isTrue(!Collections.isEmpty(alarmGroupInput.getServiceItemList()), "AlarmGroup must contain at least one service");

        List<TenantDO> allTenantList = tenantHandler.getAllTenantFromCache();
        Set<String> allTenantNameSet = allTenantList.stream().map(TenantDO::getName).collect(Collectors.toSet());

        List<TenantDO> hasPermissionServiceList = authHandler.getServiceUserCanOperate(alarmGroupInput.getUserId());
        Set<String> hasPermissionServiceNameSet = hasPermissionServiceList.stream().map(TenantDO::getName).collect(Collectors.toSet());

        for (AlarmGroupServiceItemInput alarmGroupServiceItemInput : alarmGroupInput.getServiceItemList()) {
            Assert.isTrue(allTenantNameSet.contains(alarmGroupServiceItemInput.getServiceName()), "the service " +
                    alarmGroupServiceItemInput.getServiceName() + " is not existed");
            Assert.isTrue(hasPermissionServiceNameSet.contains(alarmGroupServiceItemInput.getServiceName()), "user " +
                    "do not has the permission to operate this service " + alarmGroupServiceItemInput.getServiceName());

            List<AlarmGroupAlarmItemInput> alarmGroupAlarmItemInputList = alarmGroupServiceItemInput.getAlarmItemList();
            Assert.isTrue(!CollectionUtils.isEmpty(alarmGroupAlarmItemInputList), "alarmList can not be empty");
            for (AlarmGroupAlarmItemInput alarmGroupAlarmItemInput : alarmGroupAlarmItemInputList) {
                if (ALL_ALARM_NAME.equals(alarmGroupAlarmItemInput.getAlarmName())) {
                    Assert.isTrue(alarmGroupAlarmItemInputList.size() == 1, "ALL_ALARM is exclusive");
                }
            }
            List<AlarmGroupTagItemInput> alarmGroupTagItemInputList = alarmGroupServiceItemInput.getGroupTagItemList();
            for (AlarmGroupTagItemInput alarmGroupTagItemInput : alarmGroupTagItemInputList) {
                if (StringUtils.isEmpty(alarmGroupTagItemInput.getTagName()) || StringUtils.isEmpty(alarmGroupTagItemInput.getThreshold()) || !AlarmGroupTagOperatorEnum.isValid(alarmGroupTagItemInput.getOperator())) {
                    Assert.isTrue(false, "AlarmGroup Tag is not valid");
                }
            }
        }
    }

    public void checkUpdateAlarmGroupParameter(AlarmGroupInput alarmGroupInput, String version){
        checkAlarmGroupInputBasically(alarmGroupInput, version);
        String alarmGroupId = alarmGroupInput.getId();
        Assert.isTrue(!StringUtils.isEmpty(alarmGroupId), "alarmGroup Id can not be empty");
        AlarmGroupDO alarmGroupDO = alarmGroupHandler.findById(alarmGroupId);
        Assert.isTrue(alarmGroupDO != null, "alarmGroup is not existed");
        Boolean hasPermissionForOld = canOperateAlarmGroup(alarmGroupInput.getUserId(), alarmGroupInput.getId());
        Assert.isTrue(hasPermissionForOld, "User do not have the permission to this AlarmGroup");

        String alarmGroupName = alarmGroupInput.getName();
        AlarmGroupDO alarmGroupDO1 = alarmGroupHandler.findByName(alarmGroupName);
        Assert.isTrue(alarmGroupDO1 == null || alarmGroupDO1.getId().equals(alarmGroupId), "The AlarmGroup name is duplicated");
    }

    public boolean canOperateAlarmGroup(String userId, List<String> serviceIdList){
        if(authHandler.canCrossAndOperate(userId)){
            return true;
        }

        if(CollectionUtils.isEmpty(serviceIdList)){
            return true;
        }
        Set<String> authedServiceIdSet = authHandler.getServiceUserCanOperate(userId).stream().map(TenantDO::getId).collect(Collectors.toSet());
        for(String serviceId: serviceIdList){
            if(!authedServiceIdSet.contains(serviceId)){
                TenantDO tenant = tenantHandler.getTenantFromCacheFirst(serviceId);
                if(tenant != null && tenant.getStatus() == 0){
                    return false;
                }
            }
        }
        return true;
    }

    public boolean canOperateAlarmGroup(String userId, String alarmGroupId){
        AlarmGroupDO alarmGroupDO = alarmGroupHandler.findById(alarmGroupId);
        if(alarmGroupDO == null){
            return true;
        }
        List<AlarmGroupServiceItemDO> alarmGroupServiceItemDOList = alarmGroupServiceItemHandler.findByAlarmGroupIdList(Lists.newArrayList(alarmGroupId));
        List<String> checkServiceIdList = alarmGroupServiceItemDOList.stream().map(AlarmGroupServiceItemDO::getServiceId).collect(Collectors.toList());
        return canOperateAlarmGroup(userId, checkServiceIdList);
    }

    public boolean canOperateAlarmGroupList(String userId, List<String> alarmGroupIds) {
        List<AlarmGroupDO> alarmGroupDOList = alarmGroupHandler.findByIdList(alarmGroupIds);
        if (CollectionUtils.isEmpty(alarmGroupDOList)) {
            return true;
        }
        List<AlarmGroupServiceItemDO> alarmGroupServiceItemDOList = alarmGroupServiceItemHandler.findByAlarmGroupIdList(alarmGroupIds);
        List<String> checkServiceIdList = alarmGroupServiceItemDOList.stream().map(AlarmGroupServiceItemDO::getServiceId).collect(Collectors.toList());
        return canOperateAlarmGroup(userId, checkServiceIdList);
    }

    public void checkAddAlarmListParameter(AddAlarmList2Group addAlarmList2Group){
        addAlarmList2Group.isValid();
        String userId = addAlarmList2Group.getUserId();
        String alarmGroupId = addAlarmList2Group.getAlarmGroupId();
        String serviceId = addAlarmList2Group.getServiceId();
        List<String> alarmIdList = addAlarmList2Group.getAlarmIdList();
        AlarmGroupDO alarmGroupDO = alarmGroupHandler.findById(alarmGroupId);
        Assert.isTrue(alarmGroupDO != null, "alarmGroup is not existed");
        TenantDO tenantDO = tenantHandler.getTenantById(serviceId);
        Assert.isTrue(tenantDO != null, "service is not existed");
        for(String alarmId: alarmIdList){
            Optional<AlarmDefinition> alarmDefinitionOptional = alarmDefinitionDao.findById(alarmId);
            Assert.isTrue(alarmDefinitionOptional.isPresent(), "some alarm is not existed");
            Assert.isTrue(alarmDefinitionOptional.get().getTenantId().equals(serviceId), "alarm is not belong to the service");
        }

        if(!canOperateAlarmGroup(userId, alarmGroupId)){
            Assert.isTrue(false, "user can not operate the alarmGroup");
        }
    }

    public List<AlarmGroupDO> findAlarmGroupList(String serviceId, Boolean includeAllService) {
        List<String> serviceIdList;
        if(Boolean.TRUE.equals(includeAllService)){
            serviceIdList = Stream.of(serviceId, ALL_SERVICE_ID).distinct().collect(Collectors.toList());
        } else {
            serviceIdList = Stream.of(serviceId).collect(Collectors.toList());
        }
        Integer limit = alarmGroupParaService.getGroupQueryLimit();
        List<String> alarmGroupIdList = alarmGroupServiceItemHandler.findGroupIdByServiceIdList(serviceIdList, limit);
        return alarmGroupHandler.findByIdList(alarmGroupIdList);
    }


    public Boolean isAlarmInAlarmGroup(String alarmId, String alarmGroupId){
        Optional<AlarmDefinition> alarmDefinitionOptional = alarmDefinitionDao.findById(alarmId);
        Assert.isTrue(alarmDefinitionOptional.isPresent(), "alarm is not existed");
        String serviceId = alarmDefinitionOptional.get().getTenantId();


        AlarmGroupDO alarmGroupDO = alarmGroupHandler.findById(alarmGroupId);
        if(alarmGroupDO == null){
            return false;
        }
        List<AlarmGroupServiceItemDO> alarmGroupServiceItemDOList = alarmGroupServiceItemHandler.findByAlarmGroupIdList(Lists.newArrayList(alarmGroupId));
        for(AlarmGroupServiceItemDO serviceItemDO: alarmGroupServiceItemDOList){
            if(serviceItemDO.getServiceId().equals(ALL_SERVICE_ID) || serviceItemDO.getServiceId().equals(serviceId)){
                List<AlarmGroupAlarmItemDO> alarmGroupAlarmItemDOList = alarmGroupAlarmItemHandler.findByAlarmGroupServiceItemIdList(Lists.newArrayList(serviceItemDO.getId()));
                for(AlarmGroupAlarmItemDO alarmGroupAlarmItemDO: alarmGroupAlarmItemDOList){
                    if(alarmGroupAlarmItemDO.getAlarmId().equals(ALL_ALARM_ID) || alarmGroupAlarmItemDO.getAlarmId().equals(alarmId)){
                        return true;
                    }
                }

            }
        }
        return false;
    }

    public List<AlarmGroupDO> belongsToAlarmGroupList(AlarmDefinition alarmDefinition, List<AlarmGroupDO> alarmGroupDOList){
        List<AlarmGroupDO> result = new ArrayList<>();
        Assert.isTrue(alarmDefinition != null, "alarm is not existed");
        String serviceId = alarmDefinition.getTenantId();
        String alarmId = alarmDefinition.getId();
        if(CollectionUtils.isEmpty(alarmGroupDOList)){
            return result;
        }
        List<String> alarmGroupIdList = alarmGroupDOList.stream().map(e -> e.getId()).collect(Collectors.toList());
        List<AlarmGroupServiceItemDO> alarmGroupServiceItemDOList = alarmGroupServiceItemHandler.findByAlarmGroupIdList(alarmGroupIdList);
        Map<String, String> serviceItemId2GroupId = new HashMap<>();
        alarmGroupServiceItemDOList.forEach(e -> {
            serviceItemId2GroupId.put(e.getId(), e.getAlarmGroupId());
        });

        List<String> serviceItemIdList = alarmGroupServiceItemDOList.stream().filter(serviceItemDO -> {
            return serviceItemDO.getServiceId().equals(ALL_SERVICE_ID) || serviceItemDO.getServiceId().equals(serviceId);
        }).map(e -> e.getId()).collect(Collectors.toList());
        Set<String> targetAlarmGroupIdSet = new HashSet<>();
        List<AlarmGroupAlarmItemDO> alarmGroupAlarmItemDOList = alarmGroupAlarmItemHandler.findByAlarmGroupServiceItemIdList(serviceItemIdList);
        for(AlarmGroupAlarmItemDO alarmGroupAlarmItemDO: alarmGroupAlarmItemDOList){
            if(alarmGroupAlarmItemDO.getAlarmId().equals(ALL_ALARM_ID) || alarmGroupAlarmItemDO.getAlarmId().equals(alarmId)){
                String serviceItemId = alarmGroupAlarmItemDO.getAlarmGroupServiceItemId();
                if(serviceItemId2GroupId.containsKey(serviceItemId)){
                    targetAlarmGroupIdSet.add(serviceItemId2GroupId.get(serviceItemId));
                }
            }
        }
        alarmGroupDOList.forEach(e -> {
            if(targetAlarmGroupIdSet.contains(e.getId())){
                result.add(e);
            }
        });
        return result;
    }

    public void addAlarm2AlarmGroup(String alarmId, String alarmGroupId){
        Optional<AlarmDefinition> alarmDefinitionOptional = alarmDefinitionDao.findById(alarmId);
        Assert.isTrue(alarmDefinitionOptional.isPresent(), "alarm is not existed");
        AlarmDefinition alarmDefinition = alarmDefinitionOptional.get();
        String serviceId = alarmDefinition.getTenantId();

        AlarmGroupDO alarmGroupDO = alarmGroupHandler.findById(alarmGroupId);
        Assert.isTrue(alarmGroupDO != null, "alarmGroup is not exist");

        AlarmGroupServiceItemDO addAlarmGroupServiceItemDO = new AlarmGroupServiceItemDO();
        String serviceItemId = IdUtils.generateId();
        addAlarmGroupServiceItemDO.setId(serviceItemId);
        addAlarmGroupServiceItemDO.setAlarmGroupId(alarmGroupId);
        addAlarmGroupServiceItemDO.setServiceId(serviceId);

        AlarmGroupAlarmItemDO addAlarmGroupAlarmItemDO = new AlarmGroupAlarmItemDO();
        String alarmItemId = IdUtils.generateId();
        addAlarmGroupAlarmItemDO.setId(alarmItemId);
        addAlarmGroupAlarmItemDO.setAlarmGroupServiceItemId(serviceItemId);
        addAlarmGroupAlarmItemDO.setAlarmId(alarmDefinition.getId());

        alarmGroupServiceItemHandler.insert(addAlarmGroupServiceItemDO);
        alarmGroupAlarmItemHandler.batchInsert(Lists.newArrayList(addAlarmGroupAlarmItemDO));

    }

    public void adjustOrderIndex(List<AlarmGroupServiceItemInput> serviceItems) {
        // adjust service and tag order
        Optional<Integer> maxServiceOpt = serviceItems.stream()
                .map(AlarmGroupServiceItemInput::getOrderIndex)
                .filter(Objects::nonNull)
                .max(Integer::compareTo);

        int baseServiceOrder = maxServiceOpt.map(integer -> integer + 1).orElse(0);
        AtomicInteger serviceCounter = new AtomicInteger(baseServiceOrder);

        serviceItems.forEach(serviceItem -> {
            if (serviceItem.getOrderIndex() == null) {
                serviceItem.setOrderIndex(serviceCounter.getAndIncrement());
            }

            List<AlarmGroupTagItemInput> tags = serviceItem.getGroupTagItemList();
            Optional<Integer> maxTagOpt = tags.stream()
                    .map(AlarmGroupTagItemInput::getOrderIndex)
                    .filter(Objects::nonNull)
                    .max(Integer::compareTo);

            int baseTagOrder = maxTagOpt.map(integer -> integer + 1).orElse(0);
            AtomicInteger tagCounter = new AtomicInteger(baseTagOrder);

            tags.forEach(tag -> {
                if (tag.getOrderIndex() == null) {
                    tag.setOrderIndex(tagCounter.getAndIncrement());
                }
            });
        });
    }
}
