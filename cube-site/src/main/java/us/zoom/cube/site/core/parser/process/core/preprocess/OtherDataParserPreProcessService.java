package us.zoom.cube.site.core.parser.process.core.preprocess;

import us.zoom.cube.site.core.parser.process.core.common.DataParserLogTypeEnum;
import us.zoom.cube.site.core.parser.process.core.common.GroovyHandler;
import us.zoom.cube.site.core.parser.process.core.response.PreProcessDO;
import us.zoom.cube.site.core.parser.process.core.response.PreResp;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class OtherDataParserPreProcessService implements DataParserPreProcessService {
    private GroovyHandler groovyHandler;
    private String scriptText;

    public OtherDataParserPreProcessService(String function, String scriptText) {
        this.groovyHandler = new GroovyHandler(function, scriptText);
        this.scriptText = scriptText;
    }

    @Override
    public PreResp handle(String message) {
        List<Map<String, Object>> monitorLogList = groovyHandler.invoke(message);
        if (monitorLogList == null) {
            return new PreResp(new ArrayList<>(1));
        }
        List<PreProcessDO> results = new ArrayList<>(monitorLogList.size());
        monitorLogList.forEach(monitorMap -> {
            results.add(new PreProcessDO(DataParserLogTypeEnum.other, monitorMap));
        });
        return new PreResp(results);
    }
}
