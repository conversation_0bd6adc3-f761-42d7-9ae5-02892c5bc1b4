package us.zoom.cube.site.lib.output.panoramic;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
public class BusinessTypeResult implements Serializable {
    @Serial
    private static final long serialVersionUID = 3901610342499885394L;
    private String title;
    private Double current;
    private Double lowest;
    private String link;
    private BusinessDetailResult<List<Map<String, Object>>> successRate;
    private BusinessDetailResult<List<Map<String, Object>>> volume;
    @JsonIgnore
    private BusinessDetailResult<List<Map<String, Object>>> cluster;

    private List<BusinessClusterResult<List<Map<String, Object>>>> clusterData;
}
