package us.zoom.cube.site.core;

import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import us.zoom.infra.dao.model.InspectionLibRelaDO;
import us.zoom.infra.dao.service.InspectionLibRelaDAO;

import java.util.List;

/**
 * @Description:
 * @author: <PERSON>
 * @date: 2023/04/11 10:25
 */
@Service
public class InspectionLibRelaHandler {

    @Autowired
    private InspectionLibRelaDAO inspectionLibRelaDAO;

    public List<InspectionLibRelaDO> findByLibId(String libId) {
        return inspectionLibRelaDAO.findByLibId(libId);
    }

    public int deleteByInspectionId(String inspectionId) {
        return inspectionLibRelaDAO.deleteByInspectionId(inspectionId);
    }

    public int add(InspectionLibRelaDO inspectionLibRelaDO) {
        return inspectionLibRelaDAO.add(inspectionLibRelaDO);
    }
}
