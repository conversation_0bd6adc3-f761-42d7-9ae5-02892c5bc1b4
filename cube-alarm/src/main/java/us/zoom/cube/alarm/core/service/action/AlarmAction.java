package us.zoom.cube.alarm.core.service.action;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.alarm.core.config.ConfigCache;
import us.zoom.cube.alarm.core.config.EnvironmentCacheLoader;
import us.zoom.cube.alarm.core.model.alarm.config.AlarmSilenceCfg;
import us.zoom.cube.alarm.core.model.alarm.model.AlarmEndpointParaService;
import us.zoom.cube.alarm.core.model.metrics.MetricsCfg;
import us.zoom.cube.alarm.core.service.StorageService;
import us.zoom.cube.alarm.core.service.aa.RdsCheckService;
import us.zoom.cube.alarm.core.service.alarm.AlarmSilenceHelperService;
import us.zoom.cube.alarm.core.service.syspara.SysParaService;
import us.zoom.cube.alarm.util.AlarmTraceUtil;
import us.zoom.cube.lib.common.AlarmMonitorTypeEnum;
import us.zoom.cube.lib.common.CubeConstants;
import us.zoom.infra.influx.model.alarm.AlarmMatchRecord;
import us.zoom.infra.model.LogResult;
import us.zoom.infra.model.alarm.AlarmLevel;
import us.zoom.infra.notification.NotificationHelper;
import us.zoom.infra.utils.AlarmGroupConstants;
import us.zoom.infra.utils.DateUtils;
import us.zoom.infra.utils.IpUtils;
import us.zoom.cube.lib.utils.JsonUtils;
import us.zoom.cube.lib.utils.expression.CustomAviatorUtils;

import java.util.*;

import static us.zoom.infra.utils.DateUtils.FORMART1;

/**
 * <AUTHOR> Junjian
 * @create 2020/4/24 9:18 AM
 */
@Service
@Slf4j
public abstract class AlarmAction {
    @Autowired
    private ConfigCache configCache;

    @Autowired
    protected StorageService storageService;

    @Autowired
    protected SysParaService sysParaService;

    @Autowired
    protected AlarmSilenceHelperService alarmSilenceHelperService;

    @Autowired
    EnvironmentCacheLoader environmentCacheLoader;

    @Autowired
    RdsCheckService rdsCheckService;

    @Autowired
    private AlarmEndpointParaService alarmEndpointParaService;

    @Autowired
    private NotificationHelper notificationHelper;


    protected Logger monitorLog = LoggerFactory.getLogger("Monitor");

    private static final String SPLIT = ",";

    protected static final int QUERY_MAX_SIZE = 20;

    protected static final List<AlarmLevel> ORDERED_ALARM_LEVEL_LIST = Lists.newArrayList(AlarmLevel.FATAL, AlarmLevel.ERROR, AlarmLevel.WARN, AlarmLevel.INFO);

    /**
     * alarm action
     */
    public abstract void doAction(List<AlarmMatchRecord> alarmMatchRecord, Map<AlarmLevel, List<AlarmMatchRecord>> alarmMatchRecordByLevel, boolean isSend);

    /**
     * matching check,it will decide if need execute current action
     */
    public abstract boolean matchingCheck(List<AlarmMatchRecord> alarmMatchRecord);

    AlarmSilenceRes computeSilenced(AlarmMatchRecord alarmMatchRecord) {
        AlarmSilenceRes alarmSilenceRes = new AlarmSilenceRes();
        alarmSilenceRes.setIsSilenced(false);
        if (configCache == null) {
            return alarmSilenceRes;
        }
        Map<String, Set<AlarmSilenceCfg>> alarmSilenceCfgMap = configCache.getAlarmSilenceCfgMap();
        if (CollectionUtils.isEmpty(alarmSilenceCfgMap)) {
            return alarmSilenceRes;
        }
        String serviceId = alarmMatchRecord.getTenantId();

        Set<AlarmSilenceCfg> alarmSilenceCfgSet = Optional.ofNullable(alarmSilenceCfgMap.get(serviceId)).orElse(new HashSet<>());

        if (alarmSilenceCfgMap.containsKey(AlarmGroupConstants.ALL_SERVICE_ID)) {
            alarmSilenceCfgSet.addAll(alarmSilenceCfgMap.get(AlarmGroupConstants.ALL_SERVICE_ID));
        }

        for (AlarmSilenceCfg alarmSilenceCfg : alarmSilenceCfgSet) {
            if (alarmSilenceHelperService.isSilenced(alarmSilenceCfg, alarmMatchRecord)) {
                alarmSilenceRes.setIsSilenced(true);
                alarmSilenceRes.setAlarmSilenceId(alarmSilenceCfg.getId());
                alarmSilenceRes.setAlarmSilenceName(alarmSilenceCfg.getAlarmSilenceDO().getName());
                break;
            }
        }
        return alarmSilenceRes;
    }

    protected void trace(AlarmMatchRecord alarmMatchRecordSample, String action) {
        trace(alarmMatchRecordSample, action, null);
    }

    protected void trace(AlarmMatchRecord alarmMatchRecordSample, String action, String addition) {
        try {
            if (AlarmTraceUtil.shouldTrace(sysParaService.getAlarmTracePara(), alarmMatchRecordSample.getAlarmId(), alarmMatchRecordSample.fetchTags())) {
                Map<String, Object> metricsMap = Maps.newHashMap();
                metricsMap.put("action", action);
                if (StringUtils.isNotBlank(addition)) {
                    metricsMap.put("addition", addition);
                }
                monitorLog.info(LogResult.getSuccessLogResult(AlarmMonitorTypeEnum.alarmTrace.name(), JsonUtils.toJsonStringIgnoreExp(alarmMatchRecordSample), metricsMap));
            }
        } catch (Exception ignored) {
        }
    }

    protected boolean notContains(AlarmMatchRecord currentAlarmMatchRecord, String whichLevels, String routingRule, String channelName, AlarmLevel alarmLevel) {
        boolean levelMatchResult = Arrays.stream(whichLevels.split(",")).anyMatch(level -> level.equals(alarmLevel.name()));
        boolean routingRuleResult = true;
        if (StringUtils.isNotBlank(routingRule)) {
            Map<String, Object> varMap = getVarMapFromRecord(currentAlarmMatchRecord);
            routingRuleResult = (boolean) CustomAviatorUtils.execute(routingRule, varMap, true);
        }
        if (!routingRuleResult) {
            log.info("routingRule not match, service:{}, alarm:{}, notifyChannel:{}, routingRule:{}",
                    currentAlarmMatchRecord.getTenantName(),
                    currentAlarmMatchRecord.getAlarmName(),
                    channelName,
                    routingRule);
        }
        return !(levelMatchResult && routingRuleResult);
    }

    /**
     * get all var in alarm record (contains system param)
     *
     * @param alarmMatchRecord record
     * @return paramMap
     */
    protected Map<String, Object> getVarMapFromRecord(AlarmMatchRecord alarmMatchRecord) {

        if (!CollectionUtils.isEmpty(alarmMatchRecord.getVarMap())) {
            return new HashMap<>(alarmMatchRecord.getVarMap());
        }
        Map<String, Object> tagAndFieldKeys = getAllMetricsFieldsAndTagsKey(alarmMatchRecord.getMetricsId());
        Map<String, Object> varMap = new HashMap<>(tagAndFieldKeys);
        varMap.putAll(alarmMatchRecord.getMetricsTags());
        varMap.putAll(alarmMatchRecord.getMetricFields());
        //Overwrite the original histogram field 0.32(P30),0.35(P50),0.82(P70)
        if(!CollectionUtils.isEmpty(alarmMatchRecord.getHistogramResult())) {
            varMap.putAll(alarmMatchRecord.getHistogramResult());
        }

        //ai var
        if(!CollectionUtils.isEmpty(alarmMatchRecord.getAiVarMap())) {
            varMap.putAll(alarmMatchRecord.getAiVarMap());
        }
        
        //system param
        varMap.put(CubeConstants.CUBE_ALARM_IP, IpUtils.getLocalIP());
        varMap.put(CubeConstants.CUBE_ALARM_ENV, rdsCheckService.getEnvInfor());
        varMap.put(CubeConstants.CUBE_ALARM_UNIT_TAG, getUnitTagName());
        varMap.put(CubeConstants.CUBE_ALARM_SERVICE_NAME, alarmMatchRecord.getTenantName());
        varMap.put(CubeConstants.SERVICE_NAME, alarmMatchRecord.getTenantName());

        String timeString = DateUtils.format(new Date(alarmMatchRecord.getTime()), FORMART1);
        varMap.put(CubeConstants.ALARM_TIME, timeString);
        varMap.put(CubeConstants.ALARM_NAME, alarmMatchRecord.getAlarmName());
        varMap.put(CubeConstants.ALARM_METRICS_NAME, alarmMatchRecord.getMetricsName());
        varMap.put(CubeConstants.ALARM_METRIC_NAME, alarmMatchRecord.getMetricsName());
        varMap.put(CubeConstants.ALARM_LEVEL, alarmMatchRecord.getAlarmLevel().name());
        varMap.put(CubeConstants.ALARM_DETAIL_URL, notificationHelper.buildAlarmDetailUrl(getEndpoint(), alarmMatchRecord));

        alarmMatchRecord.setVarMap(varMap);
        return new HashMap<>(alarmMatchRecord.getVarMap());
    }

    protected Map<String, Object> getAllMetricsFieldsAndTagsKey(String metricsId) {

        Map<String, Object> result = new HashMap<>(16);
        if (StringUtils.isBlank(metricsId)) {
            return result;
        }
        try {
            MetricsCfg metricsCfg = configCache.getMetricsCfgCache().get(metricsId);
            Arrays.stream(metricsCfg.getTagNames().split(SPLIT)).forEach(e -> result.put(e, null));
            metricsCfg.getFields().forEach(e -> result.put(e.getFieldName(), null));
        } catch (Exception e) {
            log.error("getAllMetricsFieldsAndTagsKey error, metricsId:{}", metricsId);
        }
        return result;
    }

    protected String getUnitTagName() {
        for (String key : configCache.getUnitTagMap().keySet()) {
            if (StringUtils.equals(IpUtils.getLocalIP(), key)) {
                return configCache.getUnitTagMap().get(key);
            }
        }
        return "";
    }

    public String getEndpoint() {
        return alarmEndpointParaService.getNewEndpoint();
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class AlarmSilenceRes {
        Boolean isSilenced = false;
        String alarmSilenceId;
        String alarmSilenceName;
    }

}
