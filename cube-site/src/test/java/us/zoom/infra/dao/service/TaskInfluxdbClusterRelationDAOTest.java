package us.zoom.infra.dao.service;


import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import us.zoom.cube.site.BaseSpringTest;
import us.zoom.infra.dao.model.TaskInfluxdbClusterRelationDO;

import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR> <PERSON>
 * @create 12/28/21 10:10 AM
 */
public class TaskInfluxdbClusterRelationDAOTest extends BaseSpringTest {

    @Autowired
    private TaskInfluxdbClusterRelationDAO taskInfluxdbClusterRelationDAO;

    @Test
    public void add() {
        TaskInfluxdbClusterRelationDO ido=new TaskInfluxdbClusterRelationDO();
        String rid = UUID.randomUUID().toString();
        ido.setId(rid);
        ido.setInfluxClusterId("c1");
        ido.setTaskId("t1");
        taskInfluxdbClusterRelationDAO.add(ido);

        List<TaskInfluxdbClusterRelationDO> t1 = taskInfluxdbClusterRelationDAO.getByTaskId("t1");
        Assertions.assertTrue(t1.size() > 0);
        List<TaskInfluxdbClusterRelationDO> c1 = taskInfluxdbClusterRelationDAO.getByInfluxClusterId("c1");
        Assertions.assertTrue(c1.size() > 0);
    }
}
