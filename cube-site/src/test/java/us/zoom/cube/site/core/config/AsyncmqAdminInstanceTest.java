package us.zoom.cube.site.core.config;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ActiveProfiles;
import us.zoom.cube.site.CubeSiteApplicationTests;
import us.zoom.cube.lib.utils.JsonUtils;
import us.zoom.mq.common.param.TopicPrivilegesParam;
import us.zoom.mq.common.response.UserResult;

import java.util.HashSet;
import java.util.Set;

@ActiveProfiles("perf_for_local")
@Slf4j
public class AsyncmqAdminInstanceTest extends CubeSiteApplicationTests {

    @Autowired
    AsyncmqAdminInstance asyncmqAdminInstance;

    @Test
    @Disabled
    public void resetUserSecret() {
        UserResult userResult = asyncmqAdminInstance.resetUserSecret("486d39d8-d8ef-46aa-922f-c7b3115fa9e6");
        log.info(JsonUtils.toJsonString(userResult));
        Assertions.assertNotNull(userResult.getPassword());
    }

    @Test
    public void setIncreaseTopicPrivileges() {
        TopicPrivilegesParam topicPrivilegesParam = new TopicPrivilegesParam();
        Set<String> subscriberUserIds = new HashSet<>();
        Set<String> publisherUserIds = new HashSet<>();
        subscriberUserIds.add("45032546-22d1-4ef3-a3e9-a47bd1b83791");
        publisherUserIds.add("45032546-22d1-4ef3-a3e9-a47bd1b83791");
        topicPrivilegesParam.setPublisherUserIds(publisherUserIds);
        topicPrivilegesParam.setSubscriberUserIds(subscriberUserIds);
        boolean result = asyncmqAdminInstance.setIncreaseTopicPrivileges("3cbd698e-0c14-43a4-a17f-9e913a714c25", topicPrivilegesParam);
        Assertions.assertTrue(result);
    }
}