package us.zoom.cube.site.core.tag;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import us.zoom.cube.site.core.auth.TenantUserRelaHandler;
import us.zoom.cube.site.lib.BasePara;
import us.zoom.cube.site.lib.BusinessException;

/**
 * <AUTHOR>
 * @date 2025/1/15 14:49
 */
@Component
public class TagPermissionUtil {

    @Autowired
    private TenantUserRelaHandler tenantUserRelaHandler;

    private final int systemTagType = 1;
    private final String adminRole = "admin_role";

    public int getSystemTagType() {
        return systemTagType;
    }

    public String getAdminRole() {
        return adminRole;
    }


    public boolean isAdminRole(String userId) {
        if(tenantUserRelaHandler.checkContainRoleByUserId(userId, adminRole)){
            return true;
        }
        return false;
    }

    public void checkTagOperationPermission(BasePara basePara, Integer type){
        if(tenantUserRelaHandler.checkContainRoleByUserId(basePara.getUserId(), adminRole)){
            return;
        }
        if(type == systemTagType){
            if(!tenantUserRelaHandler.checkContainRoleByUserId(basePara.getUserId(), adminRole)){
                throw new BusinessException("No permission");
            }
        }else{

            if(!tenantUserRelaHandler.checkContainServiceByUserId(basePara.getUserId(), basePara.getTenantId())){
                throw new BusinessException("No permission");
            }
        }

    }
}
