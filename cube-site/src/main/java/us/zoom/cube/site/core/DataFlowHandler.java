package us.zoom.cube.site.core;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.lib.common.CubeConstants;
import us.zoom.infra.dao.model.DataFlowDO;
import us.zoom.infra.dao.service.DataFlowDAO;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Set;

@Component
public class DataFlowHandler {

    @Autowired
    private DataFlowDAO dataFlowDAO;

    public List<DataFlowDO> listAll() {
        List<DataFlowDO> dataFlowDOList = new ArrayList<>();
        long counts = dataFlowDAO.queryCounts();
        int pageSize = CubeConstants.CUBE_SQL_BATCH_QUERY_SIZE;
        long pageNum = counts / pageSize + (counts % pageSize == 0 ? 0 : 1);
        for (int i = 1 ; i <= pageNum ; i++){
            dataFlowDOList.addAll(dataFlowDAO.listBatch(pageSize * (i - 1),pageSize));
        }
        return dataFlowDOList;
    }

    public List<DataFlowDO> findByParam(String name, String group, String status, int pageIndex, int pageSize) {
        return dataFlowDAO.findByParam(name, group, status, (pageIndex - 1) * pageSize, pageSize);
    }

    public Integer getCountByParam(String name, String group, String status) {
        return dataFlowDAO.getCountByParam(name, group, status);
    }

    public Integer getCountByName(String name) {
        return dataFlowDAO.getCountByName(name);
    }

    public void addDataFlow(DataFlowDO dataFlowDO) {
        Assert.notNull(dataFlowDO, "dataFlow is null ");
        dataFlowDAO.addDataFlow(dataFlowDO);
    }

    public DataFlowDO getDataFlowByName(String name) {
        Assert.notNull(name, "name is null ");
        return dataFlowDAO.getDataFlowByName(name);
    }

    public DataFlowDO getDataFlowById(String id) {
        Assert.notNull(id, "id is null ");
        return dataFlowDAO.getDataFlowById(id);
    }

    public List<DataFlowDO> getDataFlowByIds(Set<String> ids) {
        Assert.notNull(ids, "ids is null ");
        return dataFlowDAO.getDataFlowByIds(ids);
    }

    public List<DataFlowDO> getDataFlowByGroup(String group) {
        Assert.notNull(group, "group is null ");
        return dataFlowDAO.getDataFlowByGroup(group);
    }

    public void delDataFlowById(String id) {
        Assert.notNull(id, "id is null ");
        dataFlowDAO.delDataFlowById(id);
    }

    public void batchUpdateDataFlow(List<DataFlowDO> dataFlowDOS) {
        Assert.notEmpty(dataFlowDOS, "user is empty!");
        dataFlowDAO.batchUpdateDataFlow(dataFlowDOS);
    }

    public void batchUpdateDataFlowGroup(List<String> ids, String group) {
        dataFlowDAO.batchUpdateDataFlowGroup(ids, group);
    }

    public void editDataFlow(DataFlowDO dataFlowDO) {
        Assert.notNull(dataFlowDO, "dataFlowDO is null!");
        batchUpdateDataFlow(Arrays.asList(dataFlowDO));
    }

    public Integer getCountByNameNotId(String name, String id) {
        return dataFlowDAO.getCountByNameNotId(name, id);
    }

    public String getDataFlowNameById(String dataFlowId) {
        return dataFlowDAO.getDataFlowNameById(dataFlowId);
    }


    public String getDataFlowByGroupAndStatus(String group) {
        return dataFlowDAO.getDataFlowByGroupAndStatus(group);
    }
    public int countByTaskQueueId(String taskQueueId) {
        return dataFlowDAO.getCountByTaskQueueId(taskQueueId);
    }

    public List<String> findTopicTemplateList(Set<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }
        return dataFlowDAO.findTopicTemplateList(ids);

    }
}
