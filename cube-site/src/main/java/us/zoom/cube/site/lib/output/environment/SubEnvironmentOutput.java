package us.zoom.cube.site.lib.output.environment;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.util.Date;

/**
 * @author: canyon.li
 * @date: 2023/03/13
 **/
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SubEnvironmentOutput {
    String id;
    String subId;
    String subName;
    String type;
    String alarmActionEnv;
    String alarmInsightEnv;
    String dataQueryEnv;
    Date createTime;
    Date modifyTime;
    String creator;
    String editor;
}
