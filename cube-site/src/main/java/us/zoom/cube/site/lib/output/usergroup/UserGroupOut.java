package us.zoom.cube.site.lib.output.usergroup;

import lombok.Data;
import us.zoom.infra.dao.model.UserGroupDO;

import java.util.List;

/**
 * @Author: <PERSON>
 * @ModuleOwner: <PERSON>
 * @Date:12/19/2022 16:37
 * @Description:
 */
@Data
public class UserGroupOut extends UserGroupDO {
    private List<UserGroupUserOut> users;

    private List<UserGroupTenantOut> tenants;

    private List<UserGroupUserOut> sharedOwnersList;
}
