package us.zoom.cube.site;

import org.junit.jupiter.api.Test;
import us.zoom.cube.lib.utils.JsonUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class PureTest {
    @Test
    public void mapJsonTest(){
        Map<String,List<String>> map = new HashMap<>();
        List<String> list1 = new ArrayList<>();
        list1.add("list01");
        list1.add("list02");
        map.put("123",list1);
        map.put("456",list1);
        System.out.println(JsonUtils.toJsonString(map));
    }
}
