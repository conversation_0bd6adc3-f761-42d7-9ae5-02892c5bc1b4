package us.zoom.cube.site.lib.output.alarm.insight;

import lombok.Builder;
import lombok.Data;
import us.zoom.infra.model.alarm.AlarmLevel;

import jakarta.persistence.Transient;
import java.util.HashMap;
import java.util.Map;

/**
 * @author: <PERSON><PERSON> Ding
 * @date: 2023/6/28 21:50
 * @desc:
 */
@Data
public class AlarmOverviewOutputEntry {

    private String id;
    private String name;

    private Map<String,Long> levelCount = new HashMap<String,Long>(){{
        put(AlarmLevel.FATAL.getPeril(), 0L);
        put(AlarmLevel.ERROR.getPeril(), 0L);
        put(AlarmLevel.WARN.getPeril(), 0L);
        put(AlarmLevel.INFO.getPeril(), 0L);
    }};

    public AlarmOverviewOutputEntry() {
    }

    public AlarmOverviewOutputEntry(String name) {
        this.name = name;
    }

    public AlarmOverviewOutputEntry(String id, String name) {
        this.id = id;
        this.name = name;
    }


}
