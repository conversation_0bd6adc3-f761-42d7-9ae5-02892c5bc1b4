package us.zoom.cube.site.lib.output.dataparser;

import us.zoom.cube.lib.config.hub.processor.ExpressionSchema;

public class ExpressionProcessorOut extends BaseProcessorOut {
    private ExpressionSchema schema;

    public ExpressionProcessorOut(String id, String name, Integer order, String dataParserPipelineId, String dataParserId, String type, ExpressionSchema schema) {
        super(id, name, order, dataParserPipelineId, dataParserId, type);
        this.schema = schema;
    }

    public ExpressionProcessorOut() {

    }


    public ExpressionSchema getSchema() {
        return schema;
    }

    public void setSchema(ExpressionSchema schema) {
        this.schema = schema;
    }
}
