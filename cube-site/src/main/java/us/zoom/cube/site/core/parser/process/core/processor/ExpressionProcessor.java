package us.zoom.cube.site.core.parser.process.core.processor;

import com.googlecode.aviator.AviatorEvaluator;
import us.zoom.cube.lib.hub.BaseProcessorCfg;
import us.zoom.cube.lib.hub.ExpressionEntryCfg;
import us.zoom.cube.lib.hub.ExpressionProcessorCfg;

import java.util.Map;

/**
 * @Author: <EMAIL>
 * @ModuleOwner: <EMAIL>
 * @Date: 04/03/2024 16:47
 * @Description:
 */
public class ExpressionProcessor extends Processor {

    public ExpressionProcessor() {
    }

    @Override
    public ProcessorResp process(Map<String, Object> messageMap, BaseProcessorCfg processorCfg) {
        ProcessorResp resp = new ProcessorResp(true,messageMap);
        try {
            ExpressionProcessorCfg expressionProcessorCfg = (ExpressionProcessorCfg) processorCfg;
            expressionProcessorCfg.getSchema().getEntries().forEach(entry -> {
                messageMap.put(entry.getName(), getValue(entry, messageMap));
            });
            logSuccess(messageMap,resp);
        } catch (Exception e) {
            logException(e, processorCfg,resp);
        }finally {
            logProcessor(processorCfg,resp);
        }
        return resp;
    }

    private Object getValue(ExpressionEntryCfg expressionCfg, Map<String, Object> messageMap) {
        try {
            // Alarm and Sink will perform type coercion based on the type defined in the pipeline, so no additional type handling is needed here.
            return AviatorEvaluator.execute(expressionCfg.getExpression(), messageMap, true);
        } catch (Exception e) {
            return null;
        }
    }
}
