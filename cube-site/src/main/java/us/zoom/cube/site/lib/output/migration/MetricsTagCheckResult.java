package us.zoom.cube.site.lib.output.migration;

import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @author: <PERSON><PERSON> Ding
 * @date: 30/07/2025 19:20
 * @desc:
 */
@Data
public class MetricsTagCheckResult {

    private boolean checkPass;
    private String startTime;
    private String endTime;
    private List<TagDebugEntry> debugStatistic = new ArrayList<>();

    private List<String> targetMissTags;
    private List<String> sourceMissTags;

    // [{sourceTag:souceCount, targetTag:targetCount}] under condition of not equals between sourceCount and targetCount
    private List<Map<String, Long>> NotEqualCountTags = new ArrayList<>();

    //important warning !!!!
    private List<String> zeroCountTagWarning = new ArrayList<>();

    //important warning !!!!
    private List<String> emptyTagValueWarning = new ArrayList<>();

    //<tag, how many difference>
    private Map<String, Integer> NotEqualValuesCount = new HashMap<>();

    //<tag, difference sample>
    private Map<String, String> NotEqualValuesExample = new HashMap<>();

    private String errorException;

    public void finalCheck() {
        checkPass = CollectionUtils.isEmpty(targetMissTags) && CollectionUtils.isEmpty(sourceMissTags) && NotEqualCountTags.isEmpty() && zeroCountTagWarning.isEmpty() && emptyTagValueWarning.isEmpty() && NotEqualValuesCount.isEmpty() && NotEqualValuesExample.isEmpty();
    }

}
