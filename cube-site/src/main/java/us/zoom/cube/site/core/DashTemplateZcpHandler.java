package us.zoom.cube.site.core;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.site.infra.utils.IdUtils;
import us.zoom.cube.site.lib.input.CardInput;
import us.zoom.cube.site.lib.input.DashInput;
import us.zoom.cube.site.lib.input.DashTemplateInput;
import us.zoom.infra.dao.model.*;
import us.zoom.infra.dao.service.*;
import us.zoom.infra.enums.DashtemplateTypeEnum;
import us.zoom.infra.utils.Instance;
import us.zoom.cube.lib.utils.JsonUtils;

import jakarta.validation.Valid;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022-12-20 11:08
 */
@Component
public class DashTemplateZcpHandler {

    private static final String ZCPNAME = "ZCP_Template";
    @Autowired
    DashTemplateDAO dashTemplateDAO;
    @Autowired
    DashTemplateGroupHandler dashTemplateGroupHandler;
    @Autowired
    DashTemplateHandler dashTemplateHandler;
    @Autowired
    DashTemplateUserRelaDAO dashTemplateUserRelaDAO;
    @Autowired
    DashTemplateRelaDAO dashTemplateRelaDAO;
    @Autowired
    DashTemplateGroupUserRelaDAO dashTemplateGroupUserRelaDAO;
    @Autowired
    DashTemplateGroupRelaDAO dashTemplateGroupRelaDAO;
    @Autowired
    private DashDAO dashDAO;
    @Autowired
    private DashHandler dashHandler;
    @Autowired
    private CardHandler cardHandler;
    @Autowired
    private TenantDAO tenantDAO;
    @Autowired
    private DashCardRelaDAO dashCardRelaDAO;
    @Autowired
    private DashTreeItemDAO dashTreeItemDAO;
    @Autowired
    private CardDAO cardDAO;

    public static final String RULE = "\\s*Template";

    public static final String SEPARATOR = "#_";

    public static final String UNDERLINE = "_";

    /**
     * addTemplateByZCP
     *
     * @param dashTemplateInput
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public String addTemplateByZCP(@Valid DashTemplateInput dashTemplateInput) {
        DashTemplateDO dashTemplateDO = dashTemplateDAO.getDashTemplateByZcpId(dashTemplateInput.getTemplateId(),dashTemplateInput.getDashId());
        if (null == dashTemplateDO) {
            addNewZCPTemplate(dashTemplateInput);
        }
        DashTemplateInput dashTemplateInputCopy = new DashTemplateInput();
        dashTemplateInputCopy.setTemplateId(dashTemplateInput.getTemplateId());
        dashTemplateInputCopy.setTemplateType(DashtemplateTypeEnum.ZCP.getCode());
        dashTemplateInputCopy.setDashId(dashTemplateInput.getDashId());
        dashTemplateInputCopy.setZcpOwnerList(dashTemplateInput.getZcpOwnerList());
        //String id = addDashByTemplateZCP(dashTemplateInputCopy,dashTemplateInput.getServiceId());
        return addDashByTemplateZCP(dashTemplateInputCopy, dashTemplateInput.getServiceId());

    }

    /**
     * addNewZCPTemplate
     *
     * @param dashTemplateInput
     */
    @Transactional(rollbackFor = Exception.class)
    public void addNewZCPTemplate(@Valid DashTemplateInput dashTemplateInput) {
        StringBuffer stringBuffer = new StringBuffer();
        DashTemplateDO dashTemplateDO = new DashTemplateDO();
        stringBuffer.append(ZCPNAME).append(UNDERLINE).append(dashTemplateInput.getServiceId());
        dashTemplateInput.setTemplateName(stringBuffer.toString());
        dashTemplateInput.setId(IdUtils.generateId());
        dashTemplateInput.setDashId(dashTemplateInput.getDashId());
        dashTemplateInput.setDescription(ZCPNAME);
        //dashTemplateHandler.addTemplate(dashTemplateInput);
        BeanUtils.copyProperties(dashTemplateInput, dashTemplateDO);
        dashTemplateDO.setZcpTemplateId(dashTemplateInput.getTemplateId());
        dashTemplateDAO.add(dashTemplateDO);
    }

    /**
     * updateDashByTemplateZCP
     *
     * @param templateRelaId
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public String updateDashByTemplateZCP(String templateRelaId) {
        DashTemplateRelaDO dashTemplateRelaDO = dashTemplateRelaDAO.getTemplateById(templateRelaId);
        if(null == dashTemplateRelaDO){
            return null;
        }
        DashDO dashDO = dashDAO.getDashById(dashTemplateRelaDO.getPointDashId());
        String serviceNameOld= dashDO.getService();
        DashDO dashById = dashDAO.getDashById(dashTemplateRelaDO.getDashId());
        if (null == dashById) {
            return null;
        }
        String serviceNameNew = dashById.getService();
        List<CardDO> pointList = cardHandler.getCardByDashId(dashTemplateRelaDO.getPointDashId());
        List<CardDO> cardlist = cardHandler.getCardByDashId(dashTemplateRelaDO.getDashId());
        List<CardDO> diffList = new ArrayList<>();
        Map<String, CardDO> pointListMap = pointList.stream().collect(Collectors.toMap(CardDO::getName, cardDO -> cardDO, (k1, k2) -> k1));
        Map<String, CardDO> cardlistMap = cardlist.stream().collect(Collectors.toMap(CardDO::getName, cardDO -> cardDO, (k1, k2) -> k1));
        for (String pointCardName : pointListMap.keySet()) {
            if (null == cardlistMap.get(pointCardName)) {
                diffList.add(pointListMap.get(pointCardName));
            }
        }
        if (!CollectionUtils.isEmpty(diffList)) {
            HashMap<String, String> idMap = new HashMap<>();
            for (CardDO cardDO : diffList) {
                CardInput cardInput = new CardInput();
                BeanUtils.copyProperties(cardDO, cardInput);
                cardInput.setDashId(dashTemplateRelaDO.getDashId());
                cardInput.setTenantId(cardlist.get(0).getTenantId());
                if (!StringUtils.isEmpty(cardDO.getConfigs())) {
                    cardInput.setConfigs(JsonUtils.toObject(cardDO.getConfigs(), Map.class));
                }
                cardHandler.changeCardSqlCfg(cardInput, serviceNameNew, serviceNameOld);
                String id = cardHandler.add(cardInput);
                idMap.put(cardDO.getId(), id);
                /*List list = new ArrayList();
                if(!org.springframework.util.StringUtils.isEmpty(dashDO.getConfigs())){
                    if (!org.springframework.util.StringUtils.isEmpty(JsonUtils.toObject(dashDO.getConfigs(), Map.class).get("layout"))){
                        List layoutList =JsonUtils.toObject(JsonUtils.toJsonString(JsonUtils.toObject(dashDO.getConfigs(), Map.class).get("layout")),List.class);
                        for (Object layout : layoutList ){
                            Map layoutMap = JsonUtils.toObject(JsonUtils.toJsonString(layout),Map.class);
                            String layoutId = idMap.get(layoutMap.get("i"));
                            layoutMap.put("i",layoutId);
                            list.add(layoutMap);
                        }
                        Map map = JsonUtils.toObject(dashDO.getConfigs(), Map.class);
                        DashInput dashInputCfg = new DashInput();
                        dashInputCfg.setConfigs(map);
                        dashInputCfg.getConfigs().put("layout",list);
                        dashInputCfg.setId(dashDO.getId());
                        dashHandler.changeConfigs(dashInputCfg);
                    }
                }*/
            }

        }
        return templateRelaId;

    }

    /**
     * deleteDashByTemplateZCP
     *
     * @param templateRelaIds
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteDashByTemplateZCP(List<String> templateRelaIds) {
        List<DashTemplateRelaDO> dashTemplateRelaDO = dashTemplateRelaDAO.getTemplateByIds(templateRelaIds);
        List<String> dashIds = Instance.ofNullable(dashTemplateRelaDO).stream().map(x -> x.getDashId()).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(dashIds)) {
            dashDAO.batchDelete(dashIds);
            dashTreeItemDAO.batchDeleteByDashId(dashIds);
            List<DashCardRelaDO> dashCardRelaDOList = dashCardRelaDAO.getDashCardRelaByDashIds(dashIds);
            List<String> cardIds = Instance.ofNullable(dashCardRelaDOList).stream().map(x -> x.getCardId()).collect(Collectors.toList());

            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(cardIds)) {
                dashCardRelaDAO.batchDelete(cardIds);
                cardDAO.batchDelete(cardIds);
            }
        }
        dashTemplateRelaDAO.batchDelete(templateRelaIds);
    }

    /**
     * addDashByTemplateZCP
     *
     * @param dashTemplateInput
     * @param serviceId
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public String addDashByTemplateZCP(@Valid DashTemplateInput dashTemplateInput, String serviceId) {

        String result = "";
        TenantDO service = tenantDAO.getTenantById(serviceId);
        String serviceName = service.getName();
        dashTemplateInput.setService(serviceName);
        DashTemplateDO dashTemplateDO = dashTemplateDAO.getDashTemplateByZcpId(dashTemplateInput.getTemplateId(),dashTemplateInput.getDashId());
        DashDO dashDORoot = dashDAO.getDashById(dashTemplateDO.getDashId());
        String serviceNameOld = dashDORoot.getService();
        String serviceNameNew = dashTemplateInput.getService();
        DashTemplateRelaDO dashTemplateRelaDO = new DashTemplateRelaDO();
        dashTemplateRelaDO.setId(IdUtils.generateId());
        StringBuffer dashName = new StringBuffer();
        dashName.append(serviceName).append(UNDERLINE).append(dashDORoot.getName());
        String newDashName = dashName.toString();
        newDashName = newDashName.replaceAll(RULE, "");

        String resId = null;
        if (null != dashTemplateDO) {
            if (null != dashDORoot) {
                DashInput dashInput = new DashInput();
                BeanUtils.copyProperties(dashDORoot, dashInput);
                Map mapCfg = JsonUtils.toObject(dashDORoot.getConfigs(), Map.class);
                dashInput.setConfigs(mapCfg);
                dashInput.setDashTemplateType(DashtemplateTypeEnum.ZCP.getCode());
                dashInput.setSharedOwners(String.join(",",dashTemplateInput.getZcpOwnerList()));
                dashInput.setName(newDashName);
                dashInput.setDescription(dashTemplateInput.getDescription());
                dashInput.setService(dashTemplateInput.getService());
                dashInput.setDashTemplateRelaId(dashTemplateRelaDO.getId());
                if (dashTemplateInput.getTemplateType().equals(DashtemplateTypeEnum.ZCP.getCode())) {
                    DashDO dashDO = dashHandler.copyDash(dashInput);
                    HashMap<String, String> idMap = cardHandler.copyCard(dashInput.getId(), dashDO.getId(), true, serviceNameNew, serviceNameOld);

                    if (!org.springframework.util.StringUtils.isEmpty(dashDO.getConfigs())) {
                        Map map = JsonUtils.toObject(dashDO.getConfigs(), Map.class);
                        DashInput dashInputCfg = new DashInput();
                        dashInputCfg.setConfigs(map);
                        dashInputCfg.setId(dashDO.getId());

                        if (!org.springframework.util.StringUtils.isEmpty(JsonUtils.toObject(dashDO.getConfigs(), Map.class).get("layout"))) {
                            List layoutList = JsonUtils.toObject(JsonUtils.toJsonString(JsonUtils.toObject(dashDO.getConfigs(), Map.class).get("layout")), List.class);
                            List list = new ArrayList();
                            for (Object layout : layoutList) {
                                Map layoutMap = JsonUtils.toObject(JsonUtils.toJsonString(layout), Map.class);
                                String id = idMap.get(layoutMap.get("i"));
                                layoutMap.put("i", id);
                                list.add(layoutMap);
                            }

                            dashInputCfg.getConfigs().put("layout", list);
                        }

                        if (!org.springframework.util.StringUtils.isEmpty(JsonUtils.toObject(dashDO.getConfigs(), Map.class).get("variables"))) {
                            List layoutList = JsonUtils.toObject(JsonUtils.toJsonString(JsonUtils.toObject(dashDO.getConfigs(), Map.class).get("variables")), List.class);
                            List list = new ArrayList();
                            for (Object layout : layoutList) {
                                Map layoutMap = JsonUtils.toObject(JsonUtils.toJsonString(layout), Map.class);
                                if (layoutMap.containsKey("tenant")) {
                                    layoutMap.put("tenant", dashInput.getService());
                                }
                                list.add(layoutMap);
                            }
                            dashInputCfg.getConfigs().put("variables", list);
                        }
                        dashHandler.changeConfigs(dashInputCfg);
                    }

                    dashTemplateRelaDO.setPointDashId(dashTemplateInput.getDashId());
                    dashTemplateRelaDO.setDashId(dashDO.getId());
                    dashTemplateRelaDO.setDashType(DashtemplateTypeEnum.ZCP.getCode());
                    dashTemplateRelaDO.setTemplateId(dashTemplateDO.getId());
                    dashTemplateRelaDAO.add(dashTemplateRelaDO);
                    resId = dashTemplateRelaDO.getId();
                }
            }
        }
        result = resId + SEPARATOR + newDashName;
        return result;
    }
}
