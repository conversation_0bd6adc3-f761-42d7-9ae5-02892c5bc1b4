package us.zoom.cube.site.core;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.site.infra.utils.IdUtils;
import us.zoom.cube.site.lib.output.cmdb.CMDBServerInfo;
import us.zoom.cube.site.lib.output.cmdb.HostInfo;
import us.zoom.cube.site.lib.output.cmdb.NetDeviceTorTopo;
import us.zoom.infra.dao.model.CmdbServerNetdeviceTopoDO;
import us.zoom.infra.dao.service.CmdbServerNetdeviceTopoDAO;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class CmdbServerNetdeviceTopoHandler {
    @Autowired
    private CmdbServerNetdeviceTopoDAO cmdbServerNetdeviceTopoDAO;

    public void batchAddCmdbServerNetdeviceTopo(List<NetDeviceTorTopo> netDeviceTorTopoBatch) {
        if (CollectionUtils.isEmpty(netDeviceTorTopoBatch)) {
            return;
        }

        List<CmdbServerNetdeviceTopoDO> cmdbServerNetdeviceTopoDOList = new ArrayList<>();
        for (NetDeviceTorTopo netDeviceTorTopo : netDeviceTorTopoBatch) {
            if (netDeviceTorTopo.getHostsCount() <= 0) {
                continue;
            }
            List<HostInfo> hostInfoList = netDeviceTorTopo.getHosts();
            for (HostInfo hostInfo : hostInfoList) {
                CmdbServerNetdeviceTopoDO cmdbServerNetdeviceTopoDO = new CmdbServerNetdeviceTopoDO();
                cmdbServerNetdeviceTopoDO.setId(IdUtils.generateId());
                cmdbServerNetdeviceTopoDO.setDeviceId(netDeviceTorTopo.getId());
                cmdbServerNetdeviceTopoDO.setDeviceName(netDeviceTorTopo.getDeviceName());
                cmdbServerNetdeviceTopoDO.setHostName(hostInfo.getHostName());
                cmdbServerNetdeviceTopoDOList.add(cmdbServerNetdeviceTopoDO);
            }
        }
        if (CollectionUtils.isEmpty(cmdbServerNetdeviceTopoDOList)) {
            return;
        }
        cmdbServerNetdeviceTopoDAO.batchAdd(cmdbServerNetdeviceTopoDOList);
    }

    public List<CmdbServerNetdeviceTopoDO> listAll() {
        return cmdbServerNetdeviceTopoDAO.listAll();
    }

    public void batchUpdateCmdbServerNetdeviceTopo(List<NetDeviceTorTopo> netDeviceTorTopoBatchUpdate) {
        if (CollectionUtils.isEmpty(netDeviceTorTopoBatchUpdate)) {
            return;
        }
        List<String> deviceIds = netDeviceTorTopoBatchUpdate.stream().map(item->item.getId()).collect(Collectors.toList());
        cmdbServerNetdeviceTopoDAO.deleteByDeviceId(deviceIds);
        List<CmdbServerNetdeviceTopoDO> NetdeviceTopoList = new ArrayList<>();
        for (NetDeviceTorTopo netDeviceTorTopo : netDeviceTorTopoBatchUpdate) {
            if (netDeviceTorTopo.getHostsCount() <= 0) {
                continue;
            }
            List<HostInfo> hostInfoList = netDeviceTorTopo.getHosts();
            for (HostInfo hostInfo : hostInfoList) {
                CmdbServerNetdeviceTopoDO cmdbServerNetdeviceTopoDO = new CmdbServerNetdeviceTopoDO();
                cmdbServerNetdeviceTopoDO.setId(IdUtils.generateId());
                cmdbServerNetdeviceTopoDO.setDeviceId(netDeviceTorTopo.getId());
                cmdbServerNetdeviceTopoDO.setDeviceName(netDeviceTorTopo.getDeviceName());
                cmdbServerNetdeviceTopoDO.setHostName(hostInfo.getHostName());
                NetdeviceTopoList.add(cmdbServerNetdeviceTopoDO);
            }
        }
        if (CollectionUtils.isEmpty(NetdeviceTopoList)) {
            return;
        }
        cmdbServerNetdeviceTopoDAO.batchAdd(NetdeviceTopoList);
    }

    public void deleteByDeviceId(String deviceId) {
        List<String> deviceIds = new ArrayList<>();
        deviceIds.add(deviceId);
        cmdbServerNetdeviceTopoDAO.deleteByDeviceId(deviceIds);
    }
}
