package us.zoom.cube.site.lib.output.clickhouse;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import us.zoom.infra.dao.model.ClickhouseClusterDO;
import us.zoom.infra.dao.model.MetricsAggregationDO;
import us.zoom.infra.dao.model.MetricsDO;

/**
 * <AUTHOR>
 * @create 2020/7/19 10:11 AM
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SimpleClickhouseClusterOut {

    String id;

    String name;

    public static SimpleClickhouseClusterOut of(ClickhouseClusterDO clickhouseClusterDO){
        SimpleClickhouseClusterOut simpleClickhouseClusterOut = new SimpleClickhouseClusterOut();
        simpleClickhouseClusterOut.setId(clickhouseClusterDO.getId());
        simpleClickhouseClusterOut.setName(clickhouseClusterDO.getName());
        return simpleClickhouseClusterOut;
    }


}
