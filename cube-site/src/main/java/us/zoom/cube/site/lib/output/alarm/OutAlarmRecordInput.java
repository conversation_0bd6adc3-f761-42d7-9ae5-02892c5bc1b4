package us.zoom.cube.site.lib.output.alarm;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import us.zoom.cube.site.lib.BasePara;

import java.io.Serializable;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class OutAlarmRecordInput extends BasePara implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotNull
    String serviceName;
    @NotBlank
    String alarmRecordId;
    @NotNull
    Long begin;
    @NotNull
    Long end;
}
