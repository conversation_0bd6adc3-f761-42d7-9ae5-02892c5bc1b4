package us.zoom.cube.site.infra.utils;

import us.zoom.infra.clickhouse.ClickhouseSqlUtil;
import us.zoom.infra.thread.ThreadLocalStore;

import static us.zoom.cube.site.infra.constants.trace.TraceConstant.*;

/**
 * @author: eason.jia
 * @date: 2024/10/11
 */
public class TraceUtils {

    public static String getTraceIndexTable(boolean queryColdData) {
        return queryColdData ? TRACE_INDEX_TABLE + LONG_TIME_STORAGE_TABLE_SUFFIX : TRACE_INDEX_TABLE;
    }

    public static String getTraceSpanTable(boolean queryColdData) {
        return queryColdData ? TRACE_SPANS_TABLE + LONG_TIME_STORAGE_TABLE_SUFFIX : TRACE_SPANS_TABLE;
    }

    public static String getTraceErrorIndexTable() {
        return getActualTableNameWithSuffix(TRACE_ERROR_TABLE);
    }


    public static String getTraceAttributeKeysTable() {
        return getActualTableNameWithSuffix(TRACE_SPAN_ATTRIBUTES_KEYS_TABLE);
    }

    public static String getTraceAttributeValuesTable() {
        return getActualTableNameWithSuffix(TRACE_SPAN_ATTRIBUTE_TABLE);
    }

    public static String getActualTableNameWithSuffix(String prefix) {
        String tenantName = ThreadLocalStore.getTenantNameLocal();
        return prefix + "_" + ClickhouseSqlUtil.encodeClickhouseName(tenantName);
    }
}
