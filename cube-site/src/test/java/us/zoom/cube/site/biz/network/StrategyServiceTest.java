package us.zoom.cube.site.biz.network;


import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import us.zoom.cube.site.api.web.StatusEnum;
import us.zoom.cube.site.biz.BaseTest;
import us.zoom.cube.site.biz.StrategyService;
import us.zoom.cube.site.lib.ResponseObject;
import us.zoom.cube.site.lib.input.network.*;
import us.zoom.cube.site.lib.query.PageQuery;
import us.zoom.infra.enums.StrategyStatusEnum;

import java.util.Arrays;

//@FixMethodOrder(MethodSorters.JVM)
public class StrategyServiceTest extends BaseTest {
    @Autowired
    StrategyService strategyService;

    private String strategyId = "f34532bc-b030-43b7-83b7-0363fe80d3a5";

    @Test
    @Disabled
    public void add() {
        StrategyInput strategyInput = makeStrategyInput();
        try {
            ResponseObject result = strategyService.add(strategyInput);
            if (result.getStatus() == StatusEnum.SUCCESS.getStatus()){
                strategyId = (String) result.getData();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void update(){
        StrategyInput strategyInput = makeStrategyInput();
        strategyInput.setInterval(120);
        try {
            strategyService.update(strategyInput);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    @Disabled
    public void delete(){
        try {
            StrategyInput strategyInput = new StrategyInput();
            strategyInput.setId(strategyId);
            strategyService.delete(strategyInput);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }



    @Test
    public void calMaxHostCount(){
        Detection sourceDetection = new Detection();
        sourceDetection.setDc("dc1");
        sourceDetection.setService("mmr");
        int calMaxHostCount = strategyService.calMaxHostCount(sourceDetection);
        Assertions.assertEquals(calMaxHostCount,1);
    }

    @Test
    public void searchStrategy(){
        PageQuery<StrategyQuery> strategyPageQuery = new PageQuery<>();
        StrategyQuery strategyQuery = makeStrategyQuery();
        strategyPageQuery.setQueryPara(strategyQuery);
        strategyService.searchStrategy(strategyPageQuery);
    }

    private StrategyQuery makeStrategyQuery() {
        StrategyQuery strategyQuery = new StrategyQuery();
        strategyQuery.setStrategyName("test");
        return strategyQuery;
    }

    @Test
    public void searchStrategyDetail(){
        StrategyDetailQuery strategyDetailQuery = new StrategyDetailQuery();
        strategyDetailQuery.setStrategyId(strategyId);
        strategyService.searchStrategyDetail(strategyDetailQuery);
    }

    @Test
    public void getDcList(){
        strategyService.getDcList();
    }

    @Test
    public void getServiceList(){
        String dc1 = "dc1";
        strategyService.getServiceList(dc1);
    }

    @Test
    public void getIpList(){
        IpListQuery ipListQuery = new IpListQuery();
        ipListQuery.setDc("dc1");
        ipListQuery.setService("mmr");
        strategyService.getIpList(ipListQuery);
    }

    @Test
    public void getStrategyList(){
        strategyService.getStrategyList();
    }

    @Test
    public void getFilterList(){
        FilterListQuery filterListQuery = new FilterListQuery();
        filterListQuery.setStrategyIds(Arrays.asList(strategyId));
        strategyService.getFilterList(filterListQuery);
    }



    private StrategyInput makeStrategyInput() {
        String strategyName = "test";
        Detection sourceDetection = new Detection();
        sourceDetection.setDc("dc1");
        sourceDetection.setService("mmr");
        Detection destDetection = new Detection();
        destDetection.setDc("dc2");
        destDetection.setService("mmr");
        Integer sourceHostCount = 1;
        Integer destHostCount = 1;
        Integer interval = 60;
        Integer useStatus = StrategyStatusEnum.enable.getValue();
        StrategyInput strategyInput = new StrategyInput();
        strategyInput.setId(strategyId);
        strategyInput.setStrategyName(strategyName);
        strategyInput.setSourceDetection(sourceDetection);
        strategyInput.setDestDetection(destDetection);
        strategyInput.setSourceHostCount(sourceHostCount);
        strategyInput.setDestHostCount(destHostCount);
        strategyInput.setInterval(interval);
        strategyInput.setUseStatus(useStatus);
        return strategyInput;
    }


}
