package us.zoom.cube.site.infra.e2e;

import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.site.biz.syspara.E2EParaService;
import us.zoom.cube.site.core.config.CubeServerCacheLoader;
import us.zoom.infra.thread.NamedThreadFactory;
import us.zoom.infra.utils.IpUtils;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * @description:
 * @author: <EMAIL>
 * @date: 2024-11-19 18:03
 **/
@Service
@Slf4j
@RequiredArgsConstructor
public class ProbeSyncTaskStarter implements CommandLineRunner {
    private final ProbeSyncTaskService probeSyncTaskService;
    private final ProbeMqConsumer probeMqConsumer;
    private final CubeServerCacheLoader cubeServerCacheLoader;
    private final E2EParaService e2EParaService;

    // timed task
    private static final int LOAD_INTERVAL_SENCONDS = 5;
    private static final int LOAD_INTERVAL_HOURS = 1;
    private static final int LOAD_INITIAL_DELAY = 10;
    private static final String AGGREGATION_STATUS = "enable";
    private static final String SITE_ENV = "standby";
    @Value("${cube.site.e2e.aggregation.consumer.topic:cube_probe_scheduler_job}")
    public String e2eConsumerTopic;
    @Value("${cube.site.e2e.aggregation.consumer.groupId:e2e-aggregation-groupId-001}")
    public String e2eGroupId;
    @Value("${async.mq.username}")
    public String username;
    @Value("${async.mq.endpoint}")
    public String endpoint;
    @Value("${async.mq.password}")
    public String password;
    @Value("${cube.e2e.aggregation.status:disable}")
    public String instanceEnv;


    @Override
    public void run(String... args) {
        try {
            List<String> probeAggregationEnv = e2EParaService.getProbeAggregationEnv();
            if (CollectionUtils.isEmpty(probeAggregationEnv)) {
                probeAggregationEnv = List.of(SITE_ENV);
            }
            String siteEnv = cubeServerCacheLoader.getEnv();
            log.info("[ProbeSyncTaskStarter] ip:{}", IpUtils.getLocalIP());
            log.info("[ProbeSyncTaskStarter] siteEnv:{}", siteEnv);
            log.info("[ProbeSyncTaskStarter] probeAggregationEnv:{}", JSON.toJSONString(probeAggregationEnv));
            if (!(AGGREGATION_STATUS.equals(instanceEnv) || (!Objects.isNull(siteEnv) && probeAggregationEnv.contains(siteEnv)))) {
                return;
            }
            log.info("[ProbeSyncTaskStarter]========================================== Step1: init async mq ===================================================");
            AsyncMqE2ESingleton.init(endpoint, username, password);
            log.info("[ProbeSyncTaskStarter]async.mq.topic : {}", e2eConsumerTopic);
            AsyncMqE2ESingleton.getInstance().registerConsumer(e2eConsumerTopic, e2eGroupId, 1, probeMqConsumer);
            log.info("[ProbeSyncTaskStarter]========================================== Step2: register async mq password rotate handler =======================");
            AsyncMqE2ESingleton.getInstance().registerRotateHandler(username, "async.mq.password");
            log.info("[ProbeSyncTaskStarter]========================================== Step3: aggregate probe data  ===============================");
            Executors.newSingleThreadScheduledExecutor(new NamedThreadFactory("Aggregate probe data scheduler"))
                    .scheduleAtFixedRate(() -> {
                        try {
                            probeSyncTaskService.syncTask();
                        } catch (Exception e) {
                            log.error("[ProbeSyncTaskService]syncTask exception{}", e.getMessage(), e);
                        }
                    }, LOAD_INITIAL_DELAY, LOAD_INTERVAL_SENCONDS, TimeUnit.SECONDS);
            log.info("[ProbeSyncTaskStarter]========================================== Step4: Deletion of data from 7 days ago（probe_task_sync）  ===============================");

            Executors.newSingleThreadScheduledExecutor(new NamedThreadFactory("Aggregate probe data scheduler"))
                    .scheduleAtFixedRate(() -> {
                        try {
                            //too much mysql data causing query performance degradation, regularly clean up the data 7 days ago
                            probeSyncTaskService.deleteByTime(7);
                        } catch (Exception e) {
                            log.error("[ProbeSyncTaskService]deleteByTime exception{}", e.getMessage(), e);
                        }
                    }, LOAD_INITIAL_DELAY, LOAD_INTERVAL_HOURS, TimeUnit.HOURS);
        } catch (Exception e) {
            log.error("[ProbeSyncTaskStarter] start error:{}", e.getMessage(), e);
        }

    }

}
