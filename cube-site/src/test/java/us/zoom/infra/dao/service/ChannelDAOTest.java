package us.zoom.infra.dao.service;

import com.zoom.op.monitor.dao.alarm.ChannelDao;
import com.zoom.op.monitor.domain.alarm.Channel;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import us.zoom.cube.site.BaseSpringTest;

import java.util.Optional;

public class ChannelDAOTest extends BaseSpringTest {
    private static final Logger logger= LoggerFactory.getLogger(ChannelDAOTest.class.getName());

    @Autowired
    ChannelDao channelDao;

    @Test
    public void disableAllDefaultChannel(){
        String id = "47c326b9-8c36-4bcf-919b-c79dcc81a6c7";
        channelDao.disableAllDefaultChannel(id);
    }

    @Test
    public void findByNameAndTenantIdLimit1() {
        Optional<Channel> channel = channelDao.findByNameAndTenantIdLimit1("cube_template", "d63b3ab7-4b55-4435-acbc-71ef4bcebd1b");
        Assertions.assertEquals("cube_template", channel.get().getName());
        Assertions.assertEquals("AsyncMQ", channel.get().getEngineName());
    }


}
