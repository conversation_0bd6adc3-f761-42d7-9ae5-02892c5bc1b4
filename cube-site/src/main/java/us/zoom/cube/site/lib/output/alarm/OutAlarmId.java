package us.zoom.cube.site.lib.output.alarm;

import com.okta.commons.lang.Assert;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import org.springframework.util.StringUtils;
import us.zoom.cube.site.lib.BasePara;

import jakarta.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class OutAlarmId extends BasePara implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotBlank
    String alarmId;

    public void check() {
        Assert.isTrue(!StringUtils.isEmpty(alarmId), "alarmId can not be empty");
    }
}
