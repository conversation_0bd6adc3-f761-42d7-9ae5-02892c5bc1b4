package us.zoom.cube.site.core;


import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import us.zoom.cube.site.CubeSiteApplicationTests;
import us.zoom.cube.site.infra.utils.IdUtils;
import us.zoom.infra.dao.model.CollectorDO;
import us.zoom.infra.dao.service.CollectorDAO;

import java.util.Date;

/**
 * <AUTHOR>
 * @create 2020/9/11 9:10 AM
 */
public class CollectorHandlerTest extends CubeSiteApplicationTests {

    @Autowired
    private CollectorDAO collectorDAO;

    @Test
    public void insertCollector() {
        CollectorDO collectorDO = new CollectorDO();
        collectorDO.setId(IdUtils.generateId());
        collectorDO.setCollectorName("testName");
        collectorDO.setTenantId("tenantId");
        collectorDO.setCollectorType(3);
        collectorDO.setCollectorKey("testKey");
        collectorDO.setParseType("json");
        collectorDO.setCreateTime(new Date());
        collectorDO.setModifyTime(new Date());
        collectorDO.setTopic("testTopic");
        collectorDAO.insertCollector(collectorDO);
    }
}
