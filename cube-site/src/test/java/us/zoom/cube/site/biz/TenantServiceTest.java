package us.zoom.cube.site.biz;


import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import us.zoom.cube.site.CubeSummary;
import us.zoom.cube.site.biz.alarm.ChannelService;
import us.zoom.cube.site.infra.utils.IdUtils;
import us.zoom.cube.site.lib.ResponseObject;
import us.zoom.cube.site.lib.input.AssignTenantDataFlowInput;
import us.zoom.cube.site.lib.input.DelTenantInput;
import us.zoom.cube.site.lib.input.TenantInput;
import us.zoom.cube.site.lib.query.UserTenantQuery;
import us.zoom.infra.dao.model.TenantDO;
import us.zoom.infra.thread.ThreadLocalStore;
import us.zoom.infra.utils.Instance;

import java.util.Arrays;
import java.util.List;

public class TenantServiceTest extends BaseTest {
    @Autowired
    TenantService tenantService;

    @Autowired
    UserService userService;


    @Autowired
    private ChannelService channelService;

    String user1 = "9e729792-1258-41fe-8e58-1b2c028b77a4";


    @Test
    public void testAll() throws Exception {
        CubeSummary summary = super.buildSummary(USER);
        List<TenantDO> tenantDOList = tenantService.myTenantNames(summary.getUserId());
        Assertions.assertTrue(tenantDOList.size() >= 1, "myTenantNames error!");
    }

    @Test
    public void addTenantTest() {
        TenantInput tenantInput = new TenantInput();
        tenantInput.setName("test1");
        tenantService.addTenant(tenantInput);
    }

    @Test
    public void getTenantUserById() {
        UserTenantQuery userTenantQuery = new UserTenantQuery();
        userTenantQuery.setUserId("c8983dd1-8a85-4512-a535-42e45181756f");
        ResponseObject responseObject = tenantService.getTenantByUserId(userTenantQuery);
    }

    @Test
    public void addDelNwsTenantTest() {
        ThreadLocalStore.setUserInfoLocal(user1);
        TenantInput tenantInput = new TenantInput();
        tenantInput.setName("test123");
        tenantInput.setType(1);
        tenantInput.setSource("nws");
        tenantInput.setCreateDefaultUser(true);
        tenantInput.setCreateDefaultChannel(true);
        tenantInput.setDefaultChannelName("alarmChannel1");
        tenantInput.setDefaultChannelTopic("alarm");
        tenantService.addTenant(tenantInput);

        TenantDO tenantDO = tenantService.getTenantByName("test123");
        Assertions.assertNotNull(tenantDO);
        Assertions.assertNotNull(userService.getUserByName("test123_normal"));
        Assertions.assertNotNull(userService.getUserByName("test123_admin"));

        Assertions.assertTrue(channelService.listBy(tenantDO.getId()).getData().stream().anyMatch(channel -> channel.getName().equals("alarmChannel1")));


        DelTenantInput delTenantInput = new DelTenantInput();
        delTenantInput.setName("test123");
        delTenantInput.setSource("nws");
        delTenantInput.setDelDefaultUser(true);
        delTenantInput.setDelDefaultChannel(true);
        delTenantInput.setDefaultChannelName("alarmChannel1");
        tenantService.delTenant(delTenantInput);


        Assertions.assertNull(tenantService.getTenantByName("test123"));
        Assertions.assertNull(userService.getUserByName("test123_normal"));
        Assertions.assertNull(userService.getUserByName("test123_admin"));

        Assertions.assertFalse(Instance.ofNullable(channelService.listBy(tenantDO.getId()).getData()).stream().anyMatch(channel -> channel.getName().equals("alarmChannel1")));


    }

    @Test
    public void assignDataFlowIdForIdTest() {
        ThreadLocalStore.setUserInfoLocal(user1);
        AssignTenantDataFlowInput tenantInput = new AssignTenantDataFlowInput();
        tenantInput.setIds(Arrays.asList(IdUtils.generateId(), IdUtils.generateId(), IdUtils.generateId()));
        tenantInput.setDataFlowId(IdUtils.generateId());
        tenantService.assignDataFlowIdForIds(tenantInput);
    }

}
