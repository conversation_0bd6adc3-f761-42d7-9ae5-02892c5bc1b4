package us.zoom.cube.site.biz;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import us.zoom.cube.lib.utils.JsonUtils;
import us.zoom.cube.site.lib.ResponseObject;
import us.zoom.cube.site.lib.query.ZcpSync2cubeQuery;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;

@Slf4j
public class ZcpServiceTest extends BaseTest {

    @Resource
    ZcpService zcpService;

    @Test
    public void testGetTypeName() {
        ResponseObject responseObject = zcpService.getTypeName();
        Assertions.assertNotNull(responseObject.getData());
    }

    @Test
    public void testgetTopics() {
        String topic1 = "{dc}_{appName}_monitor_log";
        Assertions.assertEquals(ZcpService.convert(topic1), "~{global.asyncmq.dc}_~{app}_monitor_log");
        String topic2 = "{dc}_infra";
        Assertions.assertEquals(ZcpService.convert(topic2), "~{global.asyncmq.dc}_infra");
    }


    private final AtomicInteger callCount = new AtomicInteger(0);

    @Test
    public void testZcpSync2cube() throws Exception {
        int numberOfThreads = 10;
        ExecutorService executorService = Executors.newFixedThreadPool(numberOfThreads);
        CountDownLatch latch = new CountDownLatch(numberOfThreads);
        String str = "{\n" +
                "    \"template\": {\n" +
                "        \"serviceName\": \"Infra_Monitor_Mlarx\",\n" +
                "        \"withTopic\": true,\n" +
                "        \"operator\": \"<EMAIL>\",\n" +
                "        \"topicMappingList\": [\n" +
                "            {\n" +
                "                \"templateTopic\": \"~{global.asyncmq.dc}_k8s_resource_metrics\",\n" +
                "                \"prefixTopic\": \"k8s_resource_metrics\",\n" +
                "                \"topicList\": [\n" +
                "                    \"us_k8s_resource_metrics\"\n" +
                "                ]\n" +
                "            },\n" +
                "            {\n" +
                "                \"templateTopic\": \"~{global.asyncmq.dc}_~{tenant}_infra\",\n" +
                "                \"prefixTopic\": \"cube_infra\",\n" +
                "                \"topicList\": [\n" +
                "                    \"us_cube_infra\"\n" +
                "                ]\n" +
                "            }\n" +
                "        ],\n" +
                "        \"typeList\": [\n" +
                "            \"zcp_k8s\",\n" +
                "            \"infra\"\n" +
                "        ]\n" +
                "    },\n" +
                "    \"inspection\": {\n" +
                "        \"serviceName\": \"Infra_Monitor_Mlarx\",\n" +
                "        \"enable\": true\n" +
                "    },\n" +
                "    \"alarm\": {\n" +
                "        \"serviceName\": \"Infra_Monitor_Mlarx\",\n" +
                "        \"alarmName\": [\n" +
                "            \"container_cpu_alarm\",\n" +
                "            \"ContainerCPUThrottlingHigh\"\n" +
                "        ]\n" +
                "    },\n" +
                "    \"channel\": {\n" +
                "        \"serviceName\": \"Infra_Monitor_Mlarx\",\n" +
                "        \"name\": \"cube_template\",\n" +
                "        \"engineName\": \"Email\",\n" +
                "        \"parameters\": [\n" +
                "            {\n" +
                "                \"name\": \"Recipients\",\n" +
                "                \"value\": \"<EMAIL>;<EMAIL>\"\n" +
                "            }\n" +
                "        ]\n" +
                "    }\n" +
                "}";
        ZcpSync2cubeQuery zcpSync2cubeQuery = JsonUtils.toObject(str, ZcpSync2cubeQuery.class);

        for (int i = 0; i < numberOfThreads; i++) {
            callCount.incrementAndGet();
            executorService.submit(() -> {
                try {
                    if (callCount.get() == 1) {
                        ResponseObject responseObject = zcpService.zcpSync2cube(zcpSync2cubeQuery);
                        log.info("result {}", JsonUtils.toJsonStringIgnoreExp(responseObject));
                        Assertions.assertNotNull(responseObject);
                    } else {
                        Assertions.assertThrows(IllegalArgumentException.class, () -> {
                            ResponseObject responseObject =zcpService.zcpSync2cube(zcpSync2cubeQuery);
                            log.info("elseResult {}", JsonUtils.toJsonStringIgnoreExp(responseObject));
                        }, "Expected IllegalArgumentException");
                    }
                } catch (Exception e) {
                    log.error("Request error", e);
                } finally {
                    latch.countDown();
                }
            });
        }
        latch.await();
        executorService.shutdown();
    }
}