package us.zoom.cube.site.core;

import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import us.zoom.infra.dao.model.ViewComponentDO;
import us.zoom.infra.dao.model.ViewComponentWithContainerDO;
import us.zoom.infra.dao.service.ViewComponentDAO;

import java.util.Collections;
import java.util.List;

@Component
public class ViewComponentHandler {

    @Autowired
    private ViewComponentDAO viewComponentDAO;

   public List<ViewComponentDO> listByTargetType( String tenantId,  String targetType){
        Assert.notNull(tenantId,"tenantId is null!");
        Assert.notNull(targetType,"targetType is null!");
        return viewComponentDAO.listByTargetType(tenantId,targetType);
    }

    public List<ViewComponentWithContainerDO> listByContainerAndDash(List<String> containerIds,String dashboardId) {
       if(CollectionUtils.isEmpty(containerIds)){
           return Collections.emptyList();
       }
        return viewComponentDAO.listByContainerAndDash(containerIds,dashboardId);
    }

    public ViewComponentDO getById(String id) {
        Assert.notNull(id,"id is null!");
        return viewComponentDAO.getById(id);
    }
}
