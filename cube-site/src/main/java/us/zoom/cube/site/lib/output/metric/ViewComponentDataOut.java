package us.zoom.cube.site.lib.output.metric;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class ViewComponentDataOut {
    private String type;
    private Object data;
    private String x;
    private List<String> y;
    private List<String> groupBy;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }

    public String getX() {
        return x;
    }

    public void setX(String x) {
        this.x = x;
    }

    public List<String> getY() {
        return y;
    }

    public void setY(List<String> y) {
        this.y = y;
    }


    public List<String> getGroupBy() {
        return groupBy;
    }

    public void setGroupBy(List<String> groupBy) {
        this.groupBy = groupBy;
    }
}
