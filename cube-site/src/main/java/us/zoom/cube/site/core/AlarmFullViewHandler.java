package us.zoom.cube.site.core;

import com.zoom.op.monitor.dao.alarm.AlarmDefinitionDao;
import com.zoom.op.monitor.domain.IdAndName;
import com.zoom.op.monitor.domain.NameAndCount;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.site.api.intercept.AuthInterceptor;
import us.zoom.cube.site.core.config.ClickhouseHandlerFactory;
import us.zoom.cube.site.core.config.PiiTableCacheLoader;
import us.zoom.cube.site.lib.input.AlarmFullViewQueryInput;
import us.zoom.cube.site.lib.output.alarm.AlarmFullViewOutput;
import us.zoom.infra.clickhouse.ChTable;
import us.zoom.infra.clickhouse.ClickhouseConst;
import us.zoom.infra.clickhouse.ClickhouseSqlUtil;
import us.zoom.infra.dao.model.MetricsDO;
import us.zoom.infra.dao.model.TenantDO;
import us.zoom.infra.enums.AlarmRecordStatusEnum;

import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: Starls Ding
 * @date: 2022/11/21 18:02
 * @desc:
 */
@Slf4j
@Component
public class AlarmFullViewHandler {

    @Autowired
    private TenantHandler tenantHandler;
    @Autowired
    private AlarmDefinitionDao alarmDefinitionDao;
    @Autowired
    private ClickhouseHandlerFactory clickhouseHandlerFactory;
    @Autowired
    private AlarmIndexHelperHandler alarmIndexHelperHandler;
    @Autowired
    private PiiTableCacheLoader piiTableLoader;

    //use the right join to make sure the record match the right status
    private static final String FETCH_STATUS_SQL = "select atb.time as time,atb.`__alarmName` as alarmName from %s.%s as atb " +
            "global right join (select * from %s.%s as atb where 1=1 %s %s order by status desc limit 1 by alarmMatchRecordId) as btb " +
            "on atb.`__id`=btb.alarmMatchRecordId " +
            "where 1=1 %s %s order by atb.time desc limit 1 by atb.`__alarmName`";

    //use the right join to make sure we can fetch the least noticed alarm content
    private static final String FETCH_CONTENT_SQL = "select atb.time as time,atb.`__alarmName` as alarmName,btb.title as title, btb.content as content," +
            "atb.`__gmtCreate` as gmtCreate from %s.%s as atb " +
            "global right join (select * from %s.%s as atb where 1=1 %s order by time desc limit 1 by alarmMatchRecordId) as btb " +
            "on atb.`__id`=btb.alarmMatchRecordId " +
            "where 1=1 %s %s order by atb.time desc limit 1 by atb.`__alarmName`";

    private static final String COUNT_SQL = "select count(*) as count,atb.`__alarmName` as alarmName from %s.%s as atb " +
            "global left join %s.%s as btb on atb.`__id`=btb.alarmMatchRecordId where btb.status=%d %s %s group by alarmName order by count desc";

    public List<AlarmFullViewOutput> getFullView(AlarmFullViewQueryInput fullViewQueryInput) {
        List<AlarmFullViewOutput> fullviews = new ArrayList<>();
        final String serviceId = fullViewQueryInput.getServiceId();
        TenantDO tenant = tenantHandler.getTenantById(serviceId);
        Assert.notNull(tenant, "Can't find the service by id:" + serviceId);
        final String serviceName = tenant.getName();
        final String dbName = ClickhouseSqlUtil.encodeClickhouseName(tenant.getName());
        String filterCondition = StringUtils.EMPTY;

        List<String> allAlarmNames = alarmDefinitionDao.findByTenantId(serviceId).stream().map(IdAndName::getName).sorted().collect(Collectors.toList());

        //filter
        String filterAlarmName = null != fullViewQueryInput.getPara() ? fullViewQueryInput.getPara().getName() : null;
        if (StringUtils.isNotBlank(filterAlarmName)) {
            filterCondition = String.format(" and atb.`__alarmName`='%s' ", filterAlarmName);
        }
        String filterAlarmStatus = null != fullViewQueryInput.getPara() ? fullViewQueryInput.getPara().getStatus() : null;

        //time
        long now = System.currentTimeMillis();
        String timeCondition = String.format(" and atb.time>=%d and atb.time<=%d", now / 1000 - 3600 * 24, now / 1000);

        Map<String, AlarmFullViewOutput> fullViewOutputMap = new LinkedHashMap<>();
        fetchNotNomalStatusAlarms(serviceName,timeCondition,filterCondition,AlarmRecordStatusEnum.alarming,fullViewOutputMap);
        fetchLatestAlarmContentForAlarmingStatus(serviceName,timeCondition,filterCondition,fullViewOutputMap);
        fetchNotNomalStatusAlarms(serviceName,timeCondition,filterCondition,AlarmRecordStatusEnum.pending,fullViewOutputMap);

        Set<String> alreadyOuput = new HashSet<>();
        if (!CollectionUtils.isEmpty(fullViewOutputMap)) {
            //count for alarming status and pending status,default order by count desc
            List<NameAndCount> alarmingStatusCounts = getAlarmCountByStatus(serviceName, AlarmRecordStatusEnum.alarming, filterCondition, timeCondition);
            List<NameAndCount> pendingStatusCounts = getAlarmCountByStatus(serviceName, AlarmRecordStatusEnum.pending, filterCondition, timeCondition);
            //order should be alarming/pending/normal
            fullviews.addAll(addAlarmAndCountToList(alreadyOuput, alarmingStatusCounts, fullViewOutputMap));
            fullviews.addAll(addAlarmAndCountToList(alreadyOuput, pendingStatusCounts, fullViewOutputMap));
        }
        fullviews.addAll(addNormalStatusToList(alreadyOuput, allAlarmNames, filterAlarmName));

        //customized filter and order
        if (StringUtils.isNotBlank(filterAlarmName)) {
            fullviews = fullviews.stream().filter(fullview -> filterAlarmName.equals(fullview.getName())).collect(Collectors.toList());
        }
        if (StringUtils.isNotBlank(filterAlarmStatus)) {
            fullviews = fullviews.stream().filter(fullview -> filterAlarmStatus.equals(fullview.getStatus())).collect(Collectors.toList());
        }
        AlarmFullViewQueryInput.SortByEnum sortBy = fullViewQueryInput.getSortBy();
        AlarmFullViewQueryInput.SortDirectionEnum sortDirection = fullViewQueryInput.getSortDirection();
        //default order by count desc, so just sort by other type
        if (null != sortBy && null != sortDirection) {
            if (AlarmFullViewQueryInput.SortByEnum.name == sortBy && AlarmFullViewQueryInput.SortDirectionEnum.asc == sortDirection) {
                fullviews.sort(Comparator.comparing(AlarmFullViewOutput::getName));
            }
        }
        //temporary pii filter implement
        if(!CollectionUtils.isEmpty(fullviews)) {
            Map<String, MetricsDO> alarmNameWithMetric = alarmIndexHelperHandler.mappingAlarmWithMetric(serviceId);
            fullviews = fullviews.stream().filter(fullview-> piiTableLoader.auth(new ChTable(serviceName,alarmNameWithMetric.get(fullview.getName()).getMetricsName()), AuthInterceptor.getUserId(),AuthInterceptor.getRealIp())).collect(Collectors.toList());
        }

        return fullviews;
    }



    private void fetchNotNomalStatusAlarms(String serviceName, String timeCondition, String filterCondition, AlarmRecordStatusEnum alarmRecordStatus, Map<String, AlarmFullViewOutput> fullViewOutputMap) {
        final String dbName = ClickhouseSqlUtil.encodeClickhouseName(serviceName);
        final String alarmingStatusSql = String.format(FETCH_STATUS_SQL, dbName, ClickhouseConst.ALARM_EACH_SERCICE_RECORD_TABLE_NAME,
                dbName, ClickhouseConst.ALARM_MATCH_RECORD_STATUS_TABLE_NAME, timeCondition, " and status=" + alarmRecordStatus.getStatus(),
                timeCondition, filterCondition);
        //log.info("alarmingStatusSql sql is:{}", alarmingStatusSql);
        List<Map<String, Object>> alarmingStatusQuery = clickhouseHandlerFactory.get().query(serviceName, alarmingStatusSql);
        alarmingStatusQuery.forEach(query -> {
            final String alarmName = query.get("alarmName").toString();
            fullViewOutputMap.putIfAbsent(alarmName, AlarmFullViewOutput.builder()
                    .name(alarmName)
                    .status(alarmRecordStatus.name())
                    .ts(((Timestamp) query.get("time")).getTime())
                    .build());
        });
    }

    private void fetchLatestAlarmContentForAlarmingStatus(String serviceName,String timeCondition, String filterCondition, Map<String, AlarmFullViewOutput> fullViewOutputMap) {
        final String dbName = ClickhouseSqlUtil.encodeClickhouseName(serviceName);
        final String alarmContentSql = String.format(FETCH_CONTENT_SQL, dbName, ClickhouseConst.ALARM_EACH_SERCICE_RECORD_TABLE_NAME,
                dbName, ClickhouseConst.ALARM_MATCH_RECORD_NOTIFICATION_TABLE_NAME, timeCondition,
                timeCondition, filterCondition);
        List<Map<String, Object>> alarmContentQuery = clickhouseHandlerFactory.get().query(serviceName, alarmContentSql);
        //atb.time as time,atb.`__alarmName` as alarmName,btb.title as title, btb.content as content,atb.`__gmtCreate` as gmtCreate
        for (Map<String, Object> contentEntity : alarmContentQuery) {
            String alarmName = contentEntity.get("alarmName").toString();
            if (fullViewOutputMap.containsKey(alarmName)) {
                AlarmFullViewOutput fullViewOutput = fullViewOutputMap.get(alarmName);
                fullViewOutput.setContent(contentEntity.get("content").toString());
                fullViewOutput.setTs(((Timestamp) contentEntity.get("time")).getTime());
                fullViewOutput.setGmtCreate(((Timestamp) contentEntity.get("gmtCreate")).getTime());
            }
        }
    }

    private List<AlarmFullViewOutput> addNormalStatusToList(Set<String> alreadyOuput, List<String> allAlarmNames, String filterAlarmName) {
        List<AlarmFullViewOutput> fullviews = new ArrayList<>();
        if (StringUtils.isBlank(filterAlarmName)) {
            fullviews.addAll(allAlarmNames.stream().filter(alarm -> !alreadyOuput.contains(alarm))
                    .map(alarm -> AlarmFullViewOutput.builder().name(alarm).status(AlarmRecordStatusEnum.normal.name()).build()).collect(Collectors.toList()));
        } else {
            if (!alreadyOuput.contains(filterAlarmName)) {
                fullviews.add(AlarmFullViewOutput.builder().name(filterAlarmName).status(AlarmRecordStatusEnum.normal.name()).build());
            }
        }
        return fullviews;
    }

    private List<AlarmFullViewOutput> addAlarmAndCountToList(Set<String> alreadyOuput, List<NameAndCount> anyStatusCounts, Map<String, AlarmFullViewOutput> fullViewOutputMap) {
        List<AlarmFullViewOutput> fullviews = new ArrayList<>();
        for (NameAndCount anyStatusCount : anyStatusCounts) {
            final String alarmName = anyStatusCount.getName();
            if (alreadyOuput.contains(alarmName) || !fullViewOutputMap.containsKey(alarmName)) {
                continue;
            }
            AlarmFullViewOutput fullViewOutput = fullViewOutputMap.get(alarmName);
            fullViewOutput.setCount(anyStatusCount.getCount());
            fullviews.add(fullViewOutput);
            alreadyOuput.add(alarmName);
        }
        return fullviews;
    }

    private List<NameAndCount> getAlarmCountByStatus(String serviceName, AlarmRecordStatusEnum alarmRecordStatus, String filterCondition, String timeCondition) {
        List<NameAndCount> counts = new ArrayList<>();
        final String dbName = ClickhouseSqlUtil.encodeClickhouseName(serviceName);
        final String countSql = String.format(COUNT_SQL,
                dbName, ClickhouseConst.ALARM_EACH_SERCICE_RECORD_TABLE_NAME, dbName, ClickhouseConst.ALARM_MATCH_RECORD_STATUS_TABLE_NAME,
                alarmRecordStatus.getStatus(), filterCondition, timeCondition);

        //log.info("getAlarmCount sql is:{}", countSql);
        List<Map<String, Object>> query = clickhouseHandlerFactory.get().query(serviceName, countSql);
        for (Map<String, Object> entry : query) {
            counts.add(new NameAndCount(entry.get("alarmName").toString(), Long.valueOf(entry.get("count").toString())));
        }
        return counts;
    }

}
