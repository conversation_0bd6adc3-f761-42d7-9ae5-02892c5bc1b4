package us.zoom.cube.site.infra.utils.influx;

import com.sun.jna.Native;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import us.zoom.cube.site.infra.enums.WebCodeEnum;
import us.zoom.cube.site.lib.SiteException;
import us.zoom.cube.site.lib.TopStruct;
import us.zoom.infra.utils.CommonSplitConstants;
import us.zoom.cube.lib.utils.JsonUtils;

import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 *  parser influx sql
 */
@Slf4j
public class InfluxSqlParserUtils  {

    private static JNADllCall.CLibrary instance;


    private static final String limitKey=" limit ";
    private static final Integer defaultSqlLimit=100000;
    public static String checkAndAdjustQueryLimit(String sql){
        int limitIndex= StringUtils.lastIndexOfIgnoreCase(sql,limitKey);
        if(limitIndex <0 ){
            sql=sql+limitKey+defaultSqlLimit;
        }else{
            String limitSql=sql.substring(limitIndex+limitKey.length()).trim();
            int limitEndIndex=limitSql.indexOf(" ");
            int limitSize=0;
            if(limitEndIndex <0){
                limitSize=Integer.valueOf(limitSql.trim());
            }else {
                limitSize=Integer.valueOf(limitSql.substring(0,limitEndIndex).trim());
            }
            if(limitSize > defaultSqlLimit){
                throw new SiteException(WebCodeEnum.ParaError.getCode(),"query result is over "+defaultSqlLimit);
            }

        }
        return sql;
    }

    public static Map parserSql(String querySql){
        boolean readDllSucceed = true;
        try{
            readDll();
        } catch (Error e){
            log.error("readDll error",e);
            readDllSucceed = false;
        }
        Map map;
        if(readDllSucceed){
            String query = instance.Convert(new GoString.ByValue(querySql));
            map = JsonUtils.toObject(query,Map.class);
            log.info("parsed map = {}", JsonUtils.toJsonString(map));
        }else {
            map = JsonUtils.toObject("{}",Map.class);
            map.put("rawText",querySql);
            map.put("database","");
            map.put("measurement","");
            log.info("unparsed map = {}", JsonUtils.toJsonString(map));
        }
        return map;
    }




    private static final String topNKey="top(";

    public static TopStruct parseTop(String sql){
        TopStruct topStruct=new TopStruct();

        int topIndex=sql.toLowerCase().indexOf(topNKey);
        if(topIndex == -1){
           return topStruct;
        }

        String topnStr=sql.substring(topIndex+topNKey.length());
        topnStr=topnStr.substring(0,topnStr.indexOf(")"));
        if(topnStr.indexOf(CommonSplitConstants.COMMA_SPLIT) == -1){
            return topStruct;
        }

        topStruct.setTopn(true);
        String [] fields=topnStr.split(CommonSplitConstants.COMMA_SPLIT);
        if(fields.length <=2){
            return topStruct;
        }

        List<String> tags=  Arrays.asList(fields).subList(1,fields.length-1).stream().map(tag->{
            if(StringUtils.isBlank(tag)){
                return tag;
            }
            tag=tag.trim();
            if(tag.startsWith("\"")||tag.startsWith("'")){
                tag=tag.substring(1);
            }

            if(tag.endsWith("\"")||tag.endsWith("'")){
                tag=tag.substring(0,tag.length()-1);
            }
            return tag;
        }).collect(Collectors.toList());
        topStruct.setTags(tags);
        return topStruct;
    }

    public static void main(String []gs){
      Map map=parserSql("SELECT mean(\"us\") AS \"mean_us\" FROM \"web\".\"autogen\".\"cpu\" WHERE time > :dashboardTime: AND time < :upperDashboardTime: AND ( \"clusterId\"=':cluster:'  ) GROUP BY time(:interval:), \"regionId\", \"clusterId\"");
      System.out.println(JsonUtils.toJsonString(map));

    map = JsonUtils.toObject("{}",Map.class);
    map.put("rawText","select");
    map.put("database","");
    map.put("measurement","");
    log.info("unparsed map = {}", JsonUtils.toJsonString(map));

    }


    public static void readDll(){
        try {
                if(null != instance){
                    return;
                }
                String lib =null ;
                String os = System.getProperty("os.name").toLowerCase();
                if (os.contains("windows")) {
                    lib ="libparser.dll";
                } else {
                    lib = "libparser.so";
                }

                InputStream in = InfluxSqlParserUtils.class.getResourceAsStream("/lib/"+lib);
                File ffile = new File("");
                String filePath = null;
                filePath = ffile.getAbsolutePath() + File.separator
                        + lib;
                log.info("filePath libparser  is " + filePath);
                File dll = new File(filePath);
                FileOutputStream out = new FileOutputStream(dll);
                int i;
                byte[] buf = new byte[1024];
                try {
                    while ((i = in.read(buf)) != -1) {
                        out.write(buf, 0, i);
                    }
                } finally {
                    in.close();
                    out.close();
                }
                log.info("libparser generated succeed");

            if(null == instance){
                log.info("start load libparser");
                instance = (JNADllCall.CLibrary) Native.loadLibrary(dll.getAbsolutePath() , JNADllCall.CLibrary.class);
                log.info("end load libparser");
                }
                dll.deleteOnExit();
            } catch (Exception e) {
                log.error("load jni error!",e);
            }

    }
}
