package us.zoom.cube.site.biz;


import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import us.zoom.cube.site.CubeSiteApplicationTests;
import us.zoom.cube.site.lib.ResponseObject;
import us.zoom.cube.site.lib.query.NameQuery;
import us.zoom.cube.site.lib.query.PageQuery;

/**
 * <AUTHOR>
 * @create 2020/11/16 3:12 PM
 */
public class InstanceMessaagingServiceTest extends CubeSiteApplicationTests {

    @Autowired
    private InstanceMessaagingService instanceMessaagingService;

    @Test
    public void findByChannelNameLike() {
        PageQuery<NameQuery> pageQuery = new PageQuery<>();
        pageQuery.setTenantId("bb6a0a17-0703-4e06-affc-92834a5d9079");
        pageQuery.setPageIndex(1);
        pageQuery.setPageSize(5);
        pageQuery.setQueryPara(new NameQuery());
        ResponseObject result = instanceMessaagingService.findByChannelNameLike(pageQuery);
        Assertions.assertNotNull(result);
    }
}
