package us.zoom.cube.site.infra.enums;

/**
 * <AUTHOR>
 */
public enum ClickhouseBackendCodeEnum {


    SUCCESS("0000", "success"),


    INVALID_PARAMS("5000", "INVALID_PARAMS"),

    JWT_TOKEN_EXPIRED("5020", "JWT_TOKEN_EXPIRED"),

    JWT_TOKEN_INVALID("5021", "JWT_TOKEN_INVALID"),
    JWT_TOKEN_NONE("5022", "JWT_TOKEN_NONE"),
    JWT_TOKEN_IP_MISMATCH("5023", "JWT_TOKEN_IP_MISMATCH"),
    USER_VERIFY_FAIL("5030", "USER_VERIFY_FAIL"),
    GET_USER_PASSWORD_FAIL("5031", "GET_USER_PASSWORD_FAIL"),
    PASSWORD_VERIFY_FAIL("5032", "PASSWORD_VERIFY_FAIL"),
    CREAT_TOKEN_FAIL("5033", "CREAT_TOKEN_FAIL"),
    CREAT_TASK_FAIL("5034", "CREAT_TASK_FAIL"),
    START_TASK_FAIL("5035", "START_TASK_FAIL"),
    GET_TASK_STATUS_FAIL("5036", "GET_TASK_STATUS_FAIL"),
    GET_ZK_DATA_FAIL("5037", "GET_ZK_DATA_FAIL"),
    DELETE_COPIER_PROCESS_FAIL("5038", "DELETE_COPIER_PROCESS_FAIL"),
    DELETE_ZK_DATA_FAIL("5039", "DELETE_ZK_DATA_FAIL"),
    UNKNOWN("9999", "UNKNOWN");


    private String code;

    private String errMsg;

    ClickhouseBackendCodeEnum(String code){
        this.code=code;
    }

    ClickhouseBackendCodeEnum(String code, String errMsg){
        this.code=code;
        this.errMsg = errMsg;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getErrMsg() {
        return errMsg;
    }

    public void setErrMsg(String errMsg) {
        this.errMsg = errMsg;
    }
}
