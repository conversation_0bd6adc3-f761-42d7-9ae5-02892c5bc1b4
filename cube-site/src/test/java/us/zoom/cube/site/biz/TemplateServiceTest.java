package us.zoom.cube.site.biz;

import com.google.common.collect.Lists;
import com.google.common.util.concurrent.Futures;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.ListeningExecutorService;
import com.google.common.util.concurrent.MoreExecutors;
import com.zoom.op.monitor.dao.alarm.AlarmDefinitionDao;
import com.zoom.op.monitor.domain.alarm.*;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.site.biz.alarm.AlarmDefinitionService;
import us.zoom.cube.site.core.MetricsFieldHandler;
import us.zoom.cube.site.core.TplTenantRelaHandler;
import us.zoom.cube.site.lib.ResponseObject;
import us.zoom.cube.site.lib.input.*;
import us.zoom.cube.site.lib.input.tpl.ApplyTplAlarmInput;
import us.zoom.cube.site.lib.input.tpl.ApplyTplInput;
import us.zoom.cube.site.lib.input.tpl.ApplyTplMetricsInput;
import us.zoom.cube.site.lib.input.tpl.RevokeTplInput;
import us.zoom.cube.site.lib.output.tpl.ApplyTplAlarmResult;
import us.zoom.cube.site.lib.output.tpl.ApplyTplMetricsResult;
import us.zoom.infra.dao.model.CollectorIdInfoDO;
import us.zoom.infra.dao.model.TenantDO;
import us.zoom.infra.dao.model.TplTenantRelaDO;
import us.zoom.infra.dao.service.CollectorMetricsDAO;
import us.zoom.infra.dao.service.DataParserPipelineDAO;
import us.zoom.infra.dao.service.MetricsDAO;
import us.zoom.infra.dao.service.TplTenantRelaDao;
import us.zoom.infra.enums.MetricsTypeEnum;
import us.zoom.infra.thread.ThreadLocalStore;
import us.zoom.cube.lib.utils.JsonUtils;

import jakarta.annotation.Resource;
import java.util.*;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * @Author: caesar.jiang
 * @Date: 2021/6/24 6:08 PM
 * @Desc: TemplateServiceTest
 */
public class TemplateServiceTest extends BaseTest {

    @Autowired
    private TemplateService templateService;


    @Autowired
    private MetricsDAO metricsDAO;

    @Autowired
    private CollectorMetricsDAO collectorMetricsDAO;

    @Autowired
    private MetricsFieldHandler metricsFieldHandler;

    @Autowired
    private AlarmDefinitionService alarmDefinitionService;

    @Autowired
    private AlarmDefinitionDao alarmDefinitionDao;

    @Autowired
    private DataParserService dataParserService;

    @InjectMocks
    @Resource
    private MetricsService metricsService;

    @InjectMocks
    @Resource
    private TplTenantRelaHandler tplTenantRelaHandler;

    @Mock
    private TplTenantRelaDao tplTenantRelaDao;

    @Mock
    private DataParserPipelineDAO dataParserPipelineDAO;

    @BeforeEach
    public void setUp() throws Exception {
        //MockitoAnnotations.initMocks(this); //Deprecated
        MockitoAnnotations.openMocks(this);
    }


    private ListeningExecutorService executor = MoreExecutors.listeningDecorator(new ThreadPoolExecutor(10, 100, 1, TimeUnit.SECONDS, new LinkedBlockingQueue<>(10000)));


    private void mockAllTplRelationList(List<String> tenantIds) {
        List<TplTenantRelaDO> mockList = new ArrayList<>();
        List<CollectorIdInfoDO> mockList2 = new ArrayList<>();
        for (String tenantId : tenantIds) {
            mockList.add(new TplTenantRelaDO(UUID.randomUUID().toString(), "461da051-9f18-4b02-a0da-6864a41bd7f1", tenantId, 1, new Date(), new Date()));
            mockList2.add(new CollectorIdInfoDO(UUID.randomUUID().toString(), tenantId));
        }
        when(tplTenantRelaDao.queryByTplId(any())).thenReturn(mockList);
        when(dataParserPipelineDAO.getCollectsByNameAndTenantIds(any(), any())).thenReturn(mockList2);
    }


    private String getMetricsName(Map<String, String> map) {
        return map.values().stream().findFirst().get();
    }

    private String getMetricsId(Map<String, String> map) {
        return map.keySet().stream().findFirst().get();
    }

    private String getAlarmName(Map<String, String> map) {
        return map.values().stream().findFirst().get();
    }

    private String getAlarmId(Map<String, String> map) {
        return map.keySet().stream().findFirst().get();
    }

    private void cleanData(List<String>... list) throws Exception {
        List<String> metricsIds = new ArrayList<>();
        for (List<String> l : list) {
            metricsIds.addAll(l);
        }
        if (!CollectionUtils.isEmpty(metricsIds)) {
            List<ListenableFuture<String>> futures = Lists.newArrayList();
            for (String metricsId : metricsIds) {
                ListenableFuture future = executor.submit(() -> cleanMetricsData(metricsId));
                futures.add(future);
            }
            Futures.allAsList(futures).get();
        }
    }

    private boolean cleanMetricsData(String metricsId) throws Exception {
        try {
            metricsDAO.deleteMetricsById(metricsId);
            collectorMetricsDAO.deleteByMetricsId(metricsId);
            metricsFieldHandler.deleteByMetricsId(metricsId);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return true;
    }


    private void cleanAlarm(List<String>... list) throws Exception {
        List<String> alarmIds = new ArrayList<>();
        for (List<String> l : list) {
            alarmIds.addAll(l);
        }
        if (!CollectionUtils.isEmpty(alarmIds)) {
            List<ListenableFuture<String>> futures = Lists.newArrayList();
            for (String alarmId : alarmIds) {
                ListenableFuture future = executor.submit(() -> cleanAlarmData(alarmId));
                futures.add(future);
            }
            Futures.allAsList(futures).get();
        }
    }

    private boolean cleanAlarmData(String alarmId) {
        try {
            alarmDefinitionDao.deleteById(alarmId);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return true;
    }


    private Map initOriginalMetrics() {
        String name = "test_c_test_original_" + System.nanoTime();
        String userId = "06e6bd8d-8591-4866-968b-ff197957793a";
        ThreadLocalStore.setUserInfoLocal(userId);
        ThreadLocalStore.setTenantInfoLocal("47046ca9-3df0-4b05-97f0-ccb99303ee9b");
        MetricsInput metricsInput = new MetricsInput();
        metricsInput.setMetricsName(name);
        metricsInput.setCollectorId("15c2b875-843e-48bd-8aac-d59b30df65f3");
        metricsInput.setTagNames(Lists.newArrayList("packetOut"));
        metricsInput.setType(MetricsTypeEnum.ORIGINAL.getValue());
        metricsInput.setMetricsFieldList(Arrays.asList(new MetricsFieldInput(null, null, "clusterId", 2, ""), new MetricsFieldInput(null, null, "regionId", 2, "")));
        ResponseObject result = metricsService.addMetrics(metricsInput);
        Assertions.assertTrue("success".equals(result.getStatus()));
        Map map = new HashMap();
        map.put(result.getData().toString(), name);
        return map;
    }


    private Map initAggregationMetrics() {
        String name = "test_c_test_aggregation_" + System.nanoTime();
        String userId = "06e6bd8d-8591-4866-968b-ff197957793a";
        ThreadLocalStore.setUserInfoLocal(userId);
        ThreadLocalStore.setTenantInfoLocal("47046ca9-3df0-4b05-97f0-ccb99303ee9b");
        MetricsInput metricsInput = new MetricsInput();
        metricsInput.setMetricsName(name);
        metricsInput.setCollectorId("15c2b875-843e-48bd-8aac-d59b30df65f3");
        metricsInput.setTagNames(Lists.newArrayList("byteIn"));
        metricsInput.setType(MetricsTypeEnum.AGGREGATION.getValue());
        MetricsAggregationInput metricsAggregationInput = new MetricsAggregationInput();
        metricsAggregationInput.setAggPeriod(60);
        metricsAggregationInput.setWaitTime(5000);
        MetricsAggregationRuleComposeInput metricsAggregationRuleCompose = new MetricsAggregationRuleComposeInput();
        List<AggregationFunctionRuleInput> common = new ArrayList<>();
        AggregationFunctionRuleInput c1 = new AggregationFunctionRuleInput();
        c1.setAggField("byteOut");
        c1.setAggTypes(Arrays.asList(new AggregationFunctionItemInput(null, "count",""), new AggregationFunctionItemInput(null, "max","")));
        c1.setIsConditioned(0);
        c1.setConditionalFieldPrefix("");
        c1.setFilterCondition("");
        AggregationFunctionRuleInput c2 = new AggregationFunctionRuleInput();
        c2.setAggField("packetOut");
        c2.setAggTypes(Arrays.asList(new AggregationFunctionItemInput(null, "count","")));
        c2.setIsConditioned(0);
        c2.setConditionalFieldPrefix("");
        c2.setFilterCondition("");
        AggregationFunctionRuleInput c3 = new AggregationFunctionRuleInput();
        c3.setAggField("byteIn");
        c3.setAggTypes(Arrays.asList(new AggregationFunctionItemInput(null, "count","")));
        c3.setIsConditioned(0);
        c3.setConditionalFieldPrefix("");
        c3.setFilterCondition("");
        AggregationFunctionRuleInput c4 = new AggregationFunctionRuleInput();
        c4.setAggField("packetIn");
        c4.setAggTypes(Arrays.asList(new AggregationFunctionItemInput(null, "count","")));
        c4.setIsConditioned(0);
        c4.setConditionalFieldPrefix("");
        c4.setFilterCondition("");
        common.add(c1);
        common.add(c2);
        common.add(c3);
        common.add(c4);
        metricsAggregationRuleCompose.setCommon(common);
        List<AggregationPercentileRuleInput> percentile = new ArrayList<>();
        AggregationPercentileRuleInput p1 = new AggregationPercentileRuleInput();
        p1.setAggField("packetOut");
        p1.setFilterCondition("packetOut>1");
        p1.setPercentileValues(Arrays.asList(new AggregationPercentileItemInput(null, 50), new AggregationPercentileItemInput(null, 90), new AggregationPercentileItemInput(null, 95)));
        percentile.add(p1);
        metricsAggregationRuleCompose.setPercentile(percentile);
        List<AggregationHistogramRuleInput> histogram = new ArrayList<>();
        AggregationHistogramRuleInput h1 = new AggregationHistogramRuleInput();
        h1.setAggField("byteIn");
        h1.setRanges(Arrays.asList(new AggregationHistogramRangeItemInput(null, 20d, 0d, 1, 0, "b1"), new AggregationHistogramRangeItemInput(null, 40d, 20d, 1, 0, "b2"), new AggregationHistogramRangeItemInput(null, 60d, 40d, 1, 0, "b3"), new AggregationHistogramRangeItemInput(null, 80d, 60d, 1, 0, "b4"), new AggregationHistogramRangeItemInput(null, 100d, 80d, 1, 0, "b5")));
        histogram.add(h1);
        metricsAggregationRuleCompose.setHistogram(histogram);
        List<AggregationCustomFieldRuleInput> customize = new ArrayList<>();
        AggregationCustomFieldRuleInput custom = new AggregationCustomFieldRuleInput();
        custom.setFieldName("myTest");
        custom.setExpression("byteIn.count / packetIn.count");
        customize.add(custom);
        metricsAggregationRuleCompose.setCustomize(customize);
        metricsAggregationInput.setMetricsAggregationRuleCompose(metricsAggregationRuleCompose);
        metricsInput.setMetricsAggregation(metricsAggregationInput);
        ResponseObject result = metricsService.addMetrics(metricsInput);
        Assertions.assertTrue("success".equals(result.getStatus()));
        Map map = new HashMap();
        map.put(result.getData().toString(), name);
        return map;
    }


    private ResponseObject<ApplyTplMetricsResult> createBasicData(String metricsName, String scope) throws Exception {
        String userId = "06e6bd8d-8591-4866-968b-ff197957793a";
        ThreadLocalStore.setUserInfoLocal(userId);
        ThreadLocalStore.setTenantInfoLocal("47046ca9-3df0-4b05-97f0-ccb99303ee9b");
        ApplyTplMetricsInput applyTplMetricsInput = new ApplyTplMetricsInput();
        applyTplMetricsInput.setIncludeTenants(null);
        applyTplMetricsInput.setExcludeTenants(null);
        applyTplMetricsInput.setDestTenantScope(scope);
        applyTplMetricsInput.setTplTenantName("Cube_Template_Manager");
        applyTplMetricsInput.setTplName("infra");
        applyTplMetricsInput.setMetrics(metricsName);
        applyTplMetricsInput.setOverride(false);
        applyTplMetricsInput.setApplyInDB(true);
        ResponseObject<ApplyTplMetricsResult> responseObject = templateService.applyMetrics(applyTplMetricsInput);
        Assertions.assertTrue("success".equals(responseObject.getStatus()));
        return responseObject;
    }

    private ResponseObject<ApplyTplAlarmResult> createAlarmBasicData(String alarmName, String scope) throws Exception {
        String userId = "06e6bd8d-8591-4866-968b-ff197957793a";
        ThreadLocalStore.setUserInfoLocal(userId);
        ThreadLocalStore.setTenantInfoLocal("47046ca9-3df0-4b05-97f0-ccb99303ee9b");
        ApplyTplAlarmInput applyTplAlarmInput = new ApplyTplAlarmInput();
        applyTplAlarmInput.setIncludeTenants(null);
        applyTplAlarmInput.setExcludeTenants(null);
        applyTplAlarmInput.setDestTenantScope(scope);
        applyTplAlarmInput.setTplTenantName("Cube_Template_Manager");
        applyTplAlarmInput.setTplName("infra");
        applyTplAlarmInput.setAlarm(alarmName);
        applyTplAlarmInput.setOverride(false);
        applyTplAlarmInput.setApplyInDB(true);
        ResponseObject<ApplyTplAlarmResult> responseObject = templateService.applyAlarm(applyTplAlarmInput);
        Assertions.assertTrue("success".equals(responseObject.getStatus()));
        return responseObject;
    }


    private ResponseObject<ApplyTplMetricsResult> createAggregationData(String metricsName, String scope) throws Exception {
        String userId = "06e6bd8d-8591-4866-968b-ff197957793a";
        ThreadLocalStore.setUserInfoLocal(userId);
        ThreadLocalStore.setTenantInfoLocal("47046ca9-3df0-4b05-97f0-ccb99303ee9b");
        ApplyTplMetricsInput applyTplMetricsInput = new ApplyTplMetricsInput();
        applyTplMetricsInput.setIncludeTenants(null);
        applyTplMetricsInput.setExcludeTenants(null);
        applyTplMetricsInput.setDestTenantScope(scope);
        applyTplMetricsInput.setTplTenantName("Cube_Template_Manager");
        applyTplMetricsInput.setTplName("infra");
        applyTplMetricsInput.setMetrics(metricsName);
        applyTplMetricsInput.setOverride(false);
        applyTplMetricsInput.setApplyInDB(true);
        ResponseObject<ApplyTplMetricsResult> responseObject = templateService.applyMetrics(applyTplMetricsInput);
        Assertions.assertTrue("success".equals(responseObject.getStatus()));
        return responseObject;
    }


    @Test
    public void testApplyOriginalMetrics01() throws Exception {
        Long start = System.currentTimeMillis();
        mockAllTplRelationList(Arrays.asList("c80852bd-76db-47f3-a750-5c88c2164b65", "a83a4151-3d0b-44a5-9a5f-b76f13a6eaed", "a68a39ea-81a6-402f-9732-1ef7f4480095", "38b90a20-660c-498d-9a16-90f8f6a2170f"));
        Map<String, String> map = initOriginalMetrics();
        Long t1 = System.currentTimeMillis();
        ResponseObject<ApplyTplMetricsResult> responseObject = createBasicData(getMetricsName(map), "all");
        System.out.println("cost-time={" + (System.currentTimeMillis() - t1) + "}");
        System.out.println(JsonUtils.toJsonString(responseObject));
        Assertions.assertTrue(responseObject.getData().getSuccessList().size() == 4);
        Assertions.assertTrue(responseObject.getData().getFailList().size() == 0);
        Assertions.assertTrue(responseObject.getData().getOverrideList().size() == 0);
        Assertions.assertTrue(responseObject.getData().getSkipList().size() == 0);
        cleanData(responseObject.getData().getSuccessList().stream().map(e -> e.getMetricsId()).collect(Collectors.toList()), Arrays.asList(getMetricsId(map)));
        System.out.println("all-cost-time={" + (System.currentTimeMillis() - start) + "}");
    }


    @Test
    public void testApplyOriginalMetrics01_1() throws Exception {
        mockAllTplRelationList(Arrays.asList("c80852bd-76db-47f3-a750-5c88c2164b65", "a83a4151-3d0b-44a5-9a5f-b76f13a6eaed", "a68a39ea-81a6-402f-9732-1ef7f4480095", "38b90a20-660c-498d-9a16-90f8f6a2170f"));
        Map<String, String> map = initOriginalMetrics();
        ResponseObject<ApplyTplMetricsResult> responseObject = createBasicData(getMetricsName(map), "all");
        Assertions.assertTrue(responseObject.getData().getSuccessList().size() == 4);
        Assertions.assertTrue(responseObject.getData().getFailList().size() == 0);
        Assertions.assertTrue(responseObject.getData().getOverrideList().size() == 0);
        Assertions.assertTrue(responseObject.getData().getSkipList().size() == 0);
        List<String> list = responseObject.getData().getSuccessList().stream().map(e -> e.getMetricsId()).collect(Collectors.toList());
        List<String> cleanList = list.subList(0, 2);
        cleanData(cleanList);
        mockAllTplRelationList(Arrays.asList("c80852bd-76db-47f3-a750-5c88c2164b65", "a83a4151-3d0b-44a5-9a5f-b76f13a6eaed", "a68a39ea-81a6-402f-9732-1ef7f4480095", "38b90a20-660c-498d-9a16-90f8f6a2170f"));
        Long t1 = System.currentTimeMillis();
        responseObject = createBasicData(getMetricsName(map), "all");
        System.out.println("cost-time={" + (System.currentTimeMillis() - t1) + "}");
        System.out.println(JsonUtils.toJsonString(responseObject));
        Assertions.assertTrue(responseObject.getData().getFailList().size() == 0);
        Assertions.assertTrue(responseObject.getData().getOverrideList().size() == 0);
        Assertions.assertTrue(responseObject.getData().getSkipList().size() == 2);
        Assertions.assertTrue(responseObject.getData().getSuccessList().size() == 2);
        list.removeAll(cleanList);
        cleanData(list, responseObject.getData().getSuccessList().stream().map(e -> e.getMetricsId()).collect(Collectors.toList()), Arrays.asList(getMetricsId(map)));
    }

    @Test
    public void testApplyOriginalMetrics02() throws Exception {
        mockAllTplRelationList(Arrays.asList("c80852bd-76db-47f3-a750-5c88c2164b65", "a83a4151-3d0b-44a5-9a5f-b76f13a6eaed", "a68a39ea-81a6-402f-9732-1ef7f4480095"));
        Map<String, String> map = initOriginalMetrics();
        String userId = "06e6bd8d-8591-4866-968b-ff197957793a";
        ThreadLocalStore.setUserInfoLocal(userId);
        ThreadLocalStore.setTenantInfoLocal("47046ca9-3df0-4b05-97f0-ccb99303ee9b");
        ApplyTplMetricsInput applyTplMetricsInput = new ApplyTplMetricsInput();
        applyTplMetricsInput.setIncludeTenants("asyncmq,asyncfile,11111");
        applyTplMetricsInput.setExcludeTenants(null);
        applyTplMetricsInput.setDestTenantScope("all");
        applyTplMetricsInput.setTplTenantName("Cube_Template_Manager");
        applyTplMetricsInput.setTplName("infra");
        applyTplMetricsInput.setMetrics(getMetricsName(map));
        applyTplMetricsInput.setOverride(false);
        applyTplMetricsInput.setApplyInDB(true);
        Long t1 = System.currentTimeMillis();
        ResponseObject<ApplyTplMetricsResult> responseObject = templateService.applyMetrics(applyTplMetricsInput);
        System.out.println("cost-time={" + (System.currentTimeMillis() - t1) + "}");
        System.out.println(JsonUtils.toJsonString(responseObject));
        Assertions.assertTrue("success".equals(responseObject.getStatus()));
        Assertions.assertTrue(responseObject.getData().getSuccessList().stream().map(e -> e.getTenantName()).collect(Collectors.toList()).containsAll(Arrays.asList("asyncmq", "asyncfile", "11111")));
        Assertions.assertTrue(responseObject.getData().getFailList().size() == 0);
        Assertions.assertTrue(responseObject.getData().getOverrideList().size() == 0);
        Assertions.assertTrue(responseObject.getData().getSkipList().size() == 0);
        Assertions.assertTrue(responseObject.getData().getSuccessList().size() == 3);
        cleanData(responseObject.getData().getSuccessList().stream().map(e -> e.getMetricsId()).collect(Collectors.toList()), Arrays.asList(getMetricsId(map)));
    }


    @Test
    public void testApplyOriginalMetrics02_1() throws Exception {
        mockAllTplRelationList(Arrays.asList("c80852bd-76db-47f3-a750-5c88c2164b65", "a83a4151-3d0b-44a5-9a5f-b76f13a6eaed", "a68a39ea-81a6-402f-9732-1ef7f4480095", "38b90a20-660c-498d-9a16-90f8f6a2170f"));
        Map<String, String> map = initOriginalMetrics();
        ResponseObject<ApplyTplMetricsResult> responseObject = createBasicData(getMetricsName(map), "all");
        List<String> list = responseObject.getData().getSuccessList().stream().map(e -> e.getMetricsId()).collect(Collectors.toList());
        List<String> cleanList = responseObject.getData().getSuccessList().stream().filter(e -> Arrays.asList("asyncmq").contains(e.getTenantName())).map(ee -> ee.getMetricsId()).collect(Collectors.toList());
        cleanData(cleanList);
        String userId = "06e6bd8d-8591-4866-968b-ff197957793a";
        ThreadLocalStore.setUserInfoLocal(userId);
        ThreadLocalStore.setTenantInfoLocal("47046ca9-3df0-4b05-97f0-ccb99303ee9b");
        ApplyTplMetricsInput applyTplMetricsInput = new ApplyTplMetricsInput();
        mockAllTplRelationList(Arrays.asList("c80852bd-76db-47f3-a750-5c88c2164b65", "a83a4151-3d0b-44a5-9a5f-b76f13a6eaed", "a68a39ea-81a6-402f-9732-1ef7f4480095"));
        applyTplMetricsInput.setIncludeTenants("asyncmq,asyncfile,11111");
        applyTplMetricsInput.setDestTenantScope("all");
        applyTplMetricsInput.setTplTenantName("Cube_Template_Manager");
        applyTplMetricsInput.setTplName("infra");
        applyTplMetricsInput.setMetrics(getMetricsName(map));
        applyTplMetricsInput.setOverride(false);
        applyTplMetricsInput.setApplyInDB(true);
        Long t1 = System.currentTimeMillis();
        responseObject = templateService.applyMetrics(applyTplMetricsInput);
        System.out.println("cost-time={" + (System.currentTimeMillis() - t1) + "}");
        System.out.println(JsonUtils.toJsonString(responseObject));
        Assertions.assertTrue("success".equals(responseObject.getStatus()));
        Assertions.assertTrue(responseObject.getData().getFailList().size() == 0);
        Assertions.assertTrue(responseObject.getData().getOverrideList().size() == 0);
        Assertions.assertTrue(responseObject.getData().getSkipList().size() == 2);
        Assertions.assertTrue(responseObject.getData().getSuccessList().size() == 1);
        list.removeAll(cleanList);
        cleanData(list, responseObject.getData().getSuccessList().stream().map(e -> e.getMetricsId()).collect(Collectors.toList()), Arrays.asList(getMetricsId(map)));
    }


    @Test
    public void testApplyOriginalMetrics02_2() throws Exception {
        mockAllTplRelationList(Arrays.asList("38b90a20-660c-498d-9a16-90f8f6a2170f", "a83a4151-3d0b-44a5-9a5f-b76f13a6eaed", "a68a39ea-81a6-402f-9732-1ef7f4480095", "c80852bd-76db-47f3-a750-5c88c2164b65"));
        Map<String, String> map = initOriginalMetrics();
        ResponseObject<ApplyTplMetricsResult> responseObject = createBasicData(getMetricsName(map), "all");
        List<String> list = responseObject.getData().getSuccessList().stream().map(e -> e.getMetricsId()).collect(Collectors.toList());
        List<String> cleanList = responseObject.getData().getSuccessList().stream().filter(e -> Arrays.asList("test_syn_exist").contains(e.getTenantName())).map(ee -> ee.getMetricsId()).collect(Collectors.toList());
        cleanData(cleanList);
        String userId = "06e6bd8d-8591-4866-968b-ff197957793a";
        ThreadLocalStore.setUserInfoLocal(userId);
        ThreadLocalStore.setTenantInfoLocal("47046ca9-3df0-4b05-97f0-ccb99303ee9b");
        ApplyTplMetricsInput applyTplMetricsInput = new ApplyTplMetricsInput();
        mockAllTplRelationList(Arrays.asList("38b90a20-660c-498d-9a16-90f8f6a2170f", "a83a4151-3d0b-44a5-9a5f-b76f13a6eaed"));
        applyTplMetricsInput.setIncludeTenants("test_syn_exist,asyncfile,error-11111");
        applyTplMetricsInput.setExcludeTenants(null);
        applyTplMetricsInput.setDestTenantScope("all");
        applyTplMetricsInput.setTplTenantName("Cube_Template_Manager");
        applyTplMetricsInput.setTplName("infra");
        applyTplMetricsInput.setMetrics(getMetricsName(map));
        applyTplMetricsInput.setOverride(false);
        applyTplMetricsInput.setApplyInDB(true);
        Long t1 = System.currentTimeMillis();
        responseObject = templateService.applyMetrics(applyTplMetricsInput);
        System.out.println("cost-time={" + (System.currentTimeMillis() - t1) + "}");
        System.out.println(JsonUtils.toJsonString(responseObject));
        Assertions.assertTrue("success".equals(responseObject.getStatus()));
        Assertions.assertTrue(responseObject.getData().getFailList().size() == 1);
        Assertions.assertTrue(responseObject.getData().getOverrideList().size() == 0);
        Assertions.assertTrue(responseObject.getData().getSkipList().size() == 1);
        Assertions.assertTrue(responseObject.getData().getSuccessList().size() == 1);
        list.removeAll(cleanList);
        cleanData(list, responseObject.getData().getSuccessList().stream().map(e -> e.getMetricsId()).collect(Collectors.toList()), Arrays.asList(getMetricsId(map)));
    }


    @Test
    public void testApplyOriginalMetrics03() throws Exception {
        mockAllTplRelationList(Arrays.asList("a83a4151-3d0b-44a5-9a5f-b76f13a6eaed", "a68a39ea-81a6-402f-9732-1ef7f4480095", "38b90a20-660c-498d-9a16-90f8f6a2170f", "c80852bd-76db-47f3-a750-5c88c2164b65"));
        Map<String, String> map = initOriginalMetrics();
        String userId = "06e6bd8d-8591-4866-968b-ff197957793a";
        ThreadLocalStore.setUserInfoLocal(userId);
        ThreadLocalStore.setTenantInfoLocal("47046ca9-3df0-4b05-97f0-ccb99303ee9b");
        ApplyTplMetricsInput applyTplMetricsInput = new ApplyTplMetricsInput();
        applyTplMetricsInput.setExcludeTenants("asyncmq");
        applyTplMetricsInput.setDestTenantScope("all");
        applyTplMetricsInput.setTplTenantName("Cube_Template_Manager");
        applyTplMetricsInput.setTplName("infra");
        applyTplMetricsInput.setMetrics(getMetricsName(map));
        applyTplMetricsInput.setOverride(false);
        applyTplMetricsInput.setApplyInDB(true);
        Long t1 = System.currentTimeMillis();
        ResponseObject<ApplyTplMetricsResult> responseObject = templateService.applyMetrics(applyTplMetricsInput);
        System.out.println("cost-time={" + (System.currentTimeMillis() - t1) + "}");
        System.out.println(JsonUtils.toJsonString(responseObject));
        Assertions.assertTrue("success".equals(responseObject.getStatus()));
        Assertions.assertTrue(responseObject.getData().getFailList().size() == 0);
        Assertions.assertTrue(responseObject.getData().getOverrideList().size() == 0);
        Assertions.assertTrue(responseObject.getData().getSkipList().size() == 0);
        Assertions.assertTrue(responseObject.getData().getSuccessList().size() == 3);
        cleanData(responseObject.getData().getSuccessList().stream().map(e -> e.getMetricsId()).collect(Collectors.toList()), Arrays.asList(getMetricsId(map)));
    }

    @Test
    public void testApplyOriginalMetrics03_1() throws Exception {
        mockAllTplRelationList(Arrays.asList("a83a4151-3d0b-44a5-9a5f-b76f13a6eaed", "a68a39ea-81a6-402f-9732-1ef7f4480095", "c80852bd-76db-47f3-a750-5c88c2164b65"));
        Map<String, String> map = initOriginalMetrics();
        String userId = "06e6bd8d-8591-4866-968b-ff197957793a";
        ThreadLocalStore.setUserInfoLocal(userId);
        ThreadLocalStore.setTenantInfoLocal("47046ca9-3df0-4b05-97f0-ccb99303ee9b");
        ApplyTplMetricsInput applyTplMetricsInput = new ApplyTplMetricsInput();
        applyTplMetricsInput.setIncludeTenants("asyncfile,11111,asyncmq");
        applyTplMetricsInput.setExcludeTenants("asyncfile");
        applyTplMetricsInput.setDestTenantScope("all");
        applyTplMetricsInput.setTplTenantName("Cube_Template_Manager");
        applyTplMetricsInput.setTplName("infra");
        applyTplMetricsInput.setMetrics(getMetricsName(map));
        applyTplMetricsInput.setOverride(false);
        applyTplMetricsInput.setApplyInDB(true);
        Long t1 = System.currentTimeMillis();
        ResponseObject<ApplyTplMetricsResult> responseObject = templateService.applyMetrics(applyTplMetricsInput);
        System.out.println("cost-time={" + (System.currentTimeMillis() - t1) + "}");
        System.out.println(JsonUtils.toJsonString(responseObject));
        Assertions.assertTrue("success".equals(responseObject.getStatus()));
        Assertions.assertTrue(responseObject.getData().getFailList().size() == 0);
        Assertions.assertTrue(responseObject.getData().getOverrideList().size() == 0);
        Assertions.assertTrue(responseObject.getData().getSkipList().size() == 0);
        Assertions.assertTrue(responseObject.getData().getSuccessList().size() == 2);
        cleanData(responseObject.getData().getSuccessList().stream().map(e -> e.getMetricsId()).collect(Collectors.toList()), Arrays.asList(getMetricsId(map)));
    }

    @Test
    public void testApplyOriginalMetrics03_2() throws Exception {
        mockAllTplRelationList(Arrays.asList("c80852bd-76db-47f3-a750-5c88c2164b65", "a83a4151-3d0b-44a5-9a5f-b76f13a6eaed"));
        Map<String, String> map = initOriginalMetrics();
        String userId = "06e6bd8d-8591-4866-968b-ff197957793a";
        ThreadLocalStore.setUserInfoLocal(userId);
        ThreadLocalStore.setTenantInfoLocal("47046ca9-3df0-4b05-97f0-ccb99303ee9b");
        ApplyTplMetricsInput applyTplMetricsInput = new ApplyTplMetricsInput();
        applyTplMetricsInput.setIncludeTenants("asyncmq,asyncfile,error-11111");
        applyTplMetricsInput.setExcludeTenants("asyncmq,error-asyncfile");
        applyTplMetricsInput.setDestTenantScope("all");
        applyTplMetricsInput.setTplTenantName("Cube_Template_Manager");
        applyTplMetricsInput.setTplName("infra");
        applyTplMetricsInput.setMetrics(getMetricsName(map));
        applyTplMetricsInput.setOverride(false);
        applyTplMetricsInput.setApplyInDB(true);
        Long t1 = System.currentTimeMillis();
        ResponseObject<ApplyTplMetricsResult> responseObject = templateService.applyMetrics(applyTplMetricsInput);
        System.out.println("cost-time={" + (System.currentTimeMillis() - t1) + "}");
        System.out.println(JsonUtils.toJsonString(responseObject));
        Assertions.assertTrue("success".equals(responseObject.getStatus()));
        Assertions.assertTrue(responseObject.getData().getFailList().size() == 2);
        Assertions.assertTrue(responseObject.getData().getOverrideList().size() == 0);
        Assertions.assertTrue(responseObject.getData().getSkipList().size() == 0);
        Assertions.assertTrue(responseObject.getData().getSuccessList().size() == 1);
        cleanData(responseObject.getData().getSuccessList().stream().map(e -> e.getMetricsId()).collect(Collectors.toList()), Arrays.asList(getMetricsId(map)));
    }

    @Test
    public void testApplyOriginalMetrics03_3() throws Exception {
        mockAllTplRelationList(Arrays.asList("a83a4151-3d0b-44a5-9a5f-b76f13a6eaed", "a68a39ea-81a6-402f-9732-1ef7f4480095", "38b90a20-660c-498d-9a16-90f8f6a2170f", "c80852bd-76db-47f3-a750-5c88c2164b65"));
        Map<String, String> map = initOriginalMetrics();
        ResponseObject<ApplyTplMetricsResult> responseObject = createBasicData(getMetricsName(map), "all");
        List<String> list = responseObject.getData().getSuccessList().stream().map(e -> e.getMetricsId()).collect(Collectors.toList());
        List<String> cleanList = responseObject.getData().getSuccessList().stream().filter(e -> Arrays.asList("asyncfile").contains(e.getTenantName())).map(ee -> ee.getMetricsId()).collect(Collectors.toList());
        cleanData(cleanList);
        String userId = "06e6bd8d-8591-4866-968b-ff197957793a";
        ThreadLocalStore.setUserInfoLocal(userId);
        ThreadLocalStore.setTenantInfoLocal("47046ca9-3df0-4b05-97f0-ccb99303ee9b");
        ApplyTplMetricsInput applyTplMetricsInput = new ApplyTplMetricsInput();

        mockAllTplRelationList(Arrays.asList("c80852bd-76db-47f3-a750-5c88c2164b65", "a83a4151-3d0b-44a5-9a5f-b76f13a6eaed", "a68a39ea-81a6-402f-9732-1ef7f4480095"));
        applyTplMetricsInput.setIncludeTenants("asyncmq,asyncfile,11111");
        applyTplMetricsInput.setExcludeTenants("asyncmq,error-asyncfile");
        applyTplMetricsInput.setDestTenantScope("all");
        applyTplMetricsInput.setTplTenantName("Cube_Template_Manager");
        applyTplMetricsInput.setTplName("infra");
        applyTplMetricsInput.setMetrics(getMetricsName(map));
        applyTplMetricsInput.setOverride(false);
        applyTplMetricsInput.setApplyInDB(true);
        Long t1 = System.currentTimeMillis();
        responseObject = templateService.applyMetrics(applyTplMetricsInput);
        System.out.println("cost-time={" + (System.currentTimeMillis() - t1) + "}");
        System.out.println(JsonUtils.toJsonString(responseObject));
        Assertions.assertTrue("success".equals(responseObject.getStatus()));
        Assertions.assertTrue(responseObject.getData().getFailList().size() == 1);
        Assertions.assertTrue(responseObject.getData().getOverrideList().size() == 0);
        Assertions.assertTrue(responseObject.getData().getSkipList().size() == 1);
        Assertions.assertTrue(responseObject.getData().getSuccessList().size() == 1);
        list.removeAll(cleanList);
        cleanData(list, responseObject.getData().getSuccessList().stream().map(e -> e.getMetricsId()).collect(Collectors.toList()), Arrays.asList(getMetricsId(map)));
    }


    @Test
    public void testApplyOriginalMetrics04() throws Exception {
        mockAllTplRelationList(Arrays.asList("a83a4151-3d0b-44a5-9a5f-b76f13a6eaed", "38b90a20-660c-498d-9a16-90f8f6a2170f", "a68a39ea-81a6-402f-9732-1ef7f4480095"));
        Map<String, String> map = initOriginalMetrics();
        String userId = "06e6bd8d-8591-4866-968b-ff197957793a";
        ThreadLocalStore.setUserInfoLocal(userId);
        ThreadLocalStore.setTenantInfoLocal("47046ca9-3df0-4b05-97f0-ccb99303ee9b");
        ApplyTplMetricsInput applyTplMetricsInput = new ApplyTplMetricsInput();
        //38b90a20-660c-498d-9a16-90f8f6a2170f-》outer
        applyTplMetricsInput.setIncludeTenants("asyncfile,test_syn_exist,11111");
        applyTplMetricsInput.setExcludeTenants("asyncfile");
        applyTplMetricsInput.setDestTenantScope("outer");
        applyTplMetricsInput.setTplTenantName("Cube_Template_Manager");
        applyTplMetricsInput.setTplName("infra");
        applyTplMetricsInput.setMetrics(getMetricsName(map));
        applyTplMetricsInput.setOverride(false);
        applyTplMetricsInput.setApplyInDB(true);
        Long t1 = System.currentTimeMillis();
        ResponseObject<ApplyTplMetricsResult> responseObject = templateService.applyMetrics(applyTplMetricsInput);
        System.out.println("cost-time={" + (System.currentTimeMillis() - t1) + "}");
        System.out.println(JsonUtils.toJsonString(responseObject));
        Assertions.assertTrue("success".equals(responseObject.getStatus()));
        Assertions.assertTrue(responseObject.getData().getFailList().size() == 0);
        Assertions.assertTrue(responseObject.getData().getOverrideList().size() == 0);
        Assertions.assertTrue(responseObject.getData().getSkipList().size() == 0);
        Assertions.assertTrue(responseObject.getData().getSuccessList().size() == 1);
        cleanData(responseObject.getData().getSuccessList().stream().map(e -> e.getMetricsId()).collect(Collectors.toList()), Arrays.asList(getMetricsId(map)));
    }


    @Test
    public void testApplyOriginalMetrics04_1() throws Exception {
        mockAllTplRelationList(Arrays.asList("a83a4151-3d0b-44a5-9a5f-b76f13a6eaed", "a68a39ea-81a6-402f-9732-1ef7f4480095", "38b90a20-660c-498d-9a16-90f8f6a2170f", "c80852bd-76db-47f3-a750-5c88c2164b65"));
        Map<String, String> map = initOriginalMetrics();
        ResponseObject<ApplyTplMetricsResult> responseObject = createBasicData(getMetricsName(map), "outer");
        List<String> list = responseObject.getData().getSuccessList().stream().map(e -> e.getMetricsId()).collect(Collectors.toList());
        List<String> cleanList = responseObject.getData().getSuccessList().stream().filter(e -> Arrays.asList("test_syn_exist").contains(e.getTenantName())).map(ee -> ee.getMetricsId()).collect(Collectors.toList());
        cleanData(cleanList);
        String userId = "06e6bd8d-8591-4866-968b-ff197957793a";
        ThreadLocalStore.setUserInfoLocal(userId);
        ThreadLocalStore.setTenantInfoLocal("47046ca9-3df0-4b05-97f0-ccb99303ee9b");
        ApplyTplMetricsInput applyTplMetricsInput = new ApplyTplMetricsInput();
        mockAllTplRelationList(Arrays.asList("a83a4151-3d0b-44a5-9a5f-b76f13a6eaed", "38b90a20-660c-498d-9a16-90f8f6a2170f", "a68a39ea-81a6-402f-9732-1ef7f4480095"));
        applyTplMetricsInput.setIncludeTenants("asyncfile,test_syn_exist,11111");
        applyTplMetricsInput.setExcludeTenants("asyncfile");
        applyTplMetricsInput.setDestTenantScope("outer");
        applyTplMetricsInput.setTplTenantName("Cube_Template_Manager");
        applyTplMetricsInput.setTplName("infra");
        applyTplMetricsInput.setMetrics(getMetricsName(map));
        applyTplMetricsInput.setOverride(false);
        applyTplMetricsInput.setApplyInDB(true);
        Long t1 = System.currentTimeMillis();
        responseObject = templateService.applyMetrics(applyTplMetricsInput);
        System.out.println("cost-time={" + (System.currentTimeMillis() - t1) + "}");
        System.out.println(JsonUtils.toJsonString(responseObject));
        Assertions.assertTrue("success".equals(responseObject.getStatus()));
        Assertions.assertTrue(responseObject.getData().getFailList().size() == 0);
        Assertions.assertTrue(responseObject.getData().getOverrideList().size() == 0);
        Assertions.assertTrue(responseObject.getData().getSkipList().size() == 0);
        Assertions.assertTrue(responseObject.getData().getSuccessList().size() == 1);
        list.removeAll(cleanList);
        cleanData(list, responseObject.getData().getSuccessList().stream().map(e -> e.getMetricsId()).collect(Collectors.toList()), Arrays.asList(getMetricsId(map)));
    }

    @Test
    public void testApplyOriginalMetrics04_2() throws Exception {
        mockAllTplRelationList(Arrays.asList("a83a4151-3d0b-44a5-9a5f-b76f13a6eaed", "a68a39ea-81a6-402f-9732-1ef7f4480095", "38b90a20-660c-498d-9a16-90f8f6a2170f", "c80852bd-76db-47f3-a750-5c88c2164b65"));
        Map<String, String> map = initOriginalMetrics();
        ResponseObject<ApplyTplMetricsResult> responseObject = createBasicData(getMetricsName(map), "outer");
        List<String> list = responseObject.getData().getSuccessList().stream().map(e -> e.getMetricsId()).collect(Collectors.toList());
        List<String> cleanList = responseObject.getData().getSuccessList().stream().filter(e -> !Arrays.asList("test_syn_exist").contains(e.getTenantName())).map(ee -> ee.getMetricsId()).collect(Collectors.toList());
        cleanData(cleanList);
        String userId = "06e6bd8d-8591-4866-968b-ff197957793a";
        ThreadLocalStore.setUserInfoLocal(userId);
        ThreadLocalStore.setTenantInfoLocal("47046ca9-3df0-4b05-97f0-ccb99303ee9b");
        ApplyTplMetricsInput applyTplMetricsInput = new ApplyTplMetricsInput();
        mockAllTplRelationList(Arrays.asList("a83a4151-3d0b-44a5-9a5f-b76f13a6eaed", "38b90a20-660c-498d-9a16-90f8f6a2170f", "a68a39ea-81a6-402f-9732-1ef7f4480095"));
        applyTplMetricsInput.setIncludeTenants("asyncfile,test_syn_exist,11111");
        applyTplMetricsInput.setExcludeTenants("asyncfile");
        applyTplMetricsInput.setDestTenantScope("outer");
        applyTplMetricsInput.setTplTenantName("Cube_Template_Manager");
        applyTplMetricsInput.setTplName("infra");
        applyTplMetricsInput.setMetrics(getMetricsName(map));
        applyTplMetricsInput.setOverride(false);
        applyTplMetricsInput.setApplyInDB(true);
        Long t1 = System.currentTimeMillis();
        responseObject = templateService.applyMetrics(applyTplMetricsInput);
        System.out.println("cost-time={" + (System.currentTimeMillis() - t1) + "}");
        System.out.println(JsonUtils.toJsonString(responseObject));
        Assertions.assertTrue("success".equals(responseObject.getStatus()));
        Assertions.assertTrue(responseObject.getData().getFailList().size() == 0);
        Assertions.assertTrue(responseObject.getData().getOverrideList().size() == 0);
        Assertions.assertTrue(responseObject.getData().getSkipList().size() == 1);
        Assertions.assertTrue(responseObject.getData().getSuccessList().size() == 0);
        list.removeAll(cleanList);
        cleanData(list, responseObject.getData().getSuccessList().stream().map(e -> e.getMetricsId()).collect(Collectors.toList()), Arrays.asList(getMetricsId(map)));
    }


    @Test
    public void testApplyOriginalMetrics05() throws Exception {
        mockAllTplRelationList(Arrays.asList("a83a4151-3d0b-44a5-9a5f-b76f13a6eaed", "38b90a20-660c-498d-9a16-90f8f6a2170f", "a68a39ea-81a6-402f-9732-1ef7f4480095"));
        Map<String, String> map = initOriginalMetrics();
        String userId = "06e6bd8d-8591-4866-968b-ff197957793a";
        ThreadLocalStore.setUserInfoLocal(userId);
        ThreadLocalStore.setTenantInfoLocal("47046ca9-3df0-4b05-97f0-ccb99303ee9b");
        ApplyTplMetricsInput applyTplMetricsInput = new ApplyTplMetricsInput();
        applyTplMetricsInput.setIncludeTenants("asyncfile,test_syn_exist,11111");
        applyTplMetricsInput.setExcludeTenants("asyncfile");
        applyTplMetricsInput.setDestTenantScope("inner");
        applyTplMetricsInput.setTplTenantName("Cube_Template_Manager");
        applyTplMetricsInput.setTplName("infra");
        applyTplMetricsInput.setMetrics(getMetricsName(map));
        applyTplMetricsInput.setOverride(false);
        applyTplMetricsInput.setApplyInDB(true);
        Long t1 = System.currentTimeMillis();
        ResponseObject<ApplyTplMetricsResult> responseObject = templateService.applyMetrics(applyTplMetricsInput);
        System.out.println("cost-time={" + (System.currentTimeMillis() - t1) + "}");
        System.out.println(JsonUtils.toJsonString(responseObject));
        Assertions.assertTrue("success".equals(responseObject.getStatus()));
        Assertions.assertTrue(responseObject.getData().getFailList().size() == 0);
        Assertions.assertTrue(responseObject.getData().getOverrideList().size() == 0);
        Assertions.assertTrue(responseObject.getData().getSkipList().size() == 0);
        Assertions.assertTrue(responseObject.getData().getSuccessList().size() == 1);
        cleanData(responseObject.getData().getSuccessList().stream().map(e -> e.getMetricsId()).collect(Collectors.toList()), Arrays.asList(getMetricsId(map)));
    }


    @Test
    public void testApplyOriginalMetrics05_1() throws Exception {
        mockAllTplRelationList(Arrays.asList("a83a4151-3d0b-44a5-9a5f-b76f13a6eaed", "a68a39ea-81a6-402f-9732-1ef7f4480095", "38b90a20-660c-498d-9a16-90f8f6a2170f", "c80852bd-76db-47f3-a750-5c88c2164b65"));
        Map<String, String> map = initOriginalMetrics();
        ResponseObject<ApplyTplMetricsResult> responseObject = createBasicData(getMetricsName(map), "inner");
        List<String> list = responseObject.getData().getSuccessList().stream().map(e -> e.getMetricsId()).collect(Collectors.toList());
        List<String> cleanList = responseObject.getData().getSuccessList().stream().filter(e -> Arrays.asList("11111").contains(e.getTenantName())).map(ee -> ee.getMetricsId()).collect(Collectors.toList());
        cleanData(cleanList);
        String userId = "06e6bd8d-8591-4866-968b-ff197957793a";
        ThreadLocalStore.setUserInfoLocal(userId);
        ThreadLocalStore.setTenantInfoLocal("47046ca9-3df0-4b05-97f0-ccb99303ee9b");
        ApplyTplMetricsInput applyTplMetricsInput = new ApplyTplMetricsInput();
        mockAllTplRelationList(Arrays.asList("a83a4151-3d0b-44a5-9a5f-b76f13a6eaed", "38b90a20-660c-498d-9a16-90f8f6a2170f", "a68a39ea-81a6-402f-9732-1ef7f4480095"));
        applyTplMetricsInput.setIncludeTenants("asyncfile,test_syn_exist,11111");
        applyTplMetricsInput.setExcludeTenants("asyncfile");
        applyTplMetricsInput.setDestTenantScope("inner");
        applyTplMetricsInput.setTplTenantName("Cube_Template_Manager");
        applyTplMetricsInput.setTplName("infra");
        applyTplMetricsInput.setMetrics(getMetricsName(map));
        applyTplMetricsInput.setOverride(false);
        applyTplMetricsInput.setApplyInDB(true);
        Long t1 = System.currentTimeMillis();
        responseObject = templateService.applyMetrics(applyTplMetricsInput);
        System.out.println("cost-time={" + (System.currentTimeMillis() - t1) + "}");
        System.out.println(JsonUtils.toJsonString(responseObject));
        Assertions.assertTrue("success".equals(responseObject.getStatus()));
        Assertions.assertTrue(responseObject.getData().getFailList().size() == 0);
        Assertions.assertTrue(responseObject.getData().getOverrideList().size() == 0);
        Assertions.assertTrue(responseObject.getData().getSkipList().size() == 0);
        Assertions.assertTrue(responseObject.getData().getSuccessList().size() >= 1);
        list.removeAll(cleanList);
        cleanData(list, responseObject.getData().getSuccessList().stream().map(e -> e.getMetricsId()).collect(Collectors.toList()), Arrays.asList(getMetricsId(map)));
    }


    @Test
    public void testApplyAggregationMetrics01() throws Exception {
        mockAllTplRelationList(Arrays.asList("c80852bd-76db-47f3-a750-5c88c2164b65", "a83a4151-3d0b-44a5-9a5f-b76f13a6eaed", "a68a39ea-81a6-402f-9732-1ef7f4480095", "38b90a20-660c-498d-9a16-90f8f6a2170f"));
        Map<String, String> map = initAggregationMetrics();
        Long t1 = System.currentTimeMillis();
        ResponseObject<ApplyTplMetricsResult> responseObject = createAggregationData(getMetricsName(map), "all");
        System.out.println("cost-time={" + (System.currentTimeMillis() - t1) + "}");
        System.out.println(JsonUtils.toJsonString(responseObject));
        Assertions.assertTrue(responseObject.getData().getSuccessList().size() == 4);
        Assertions.assertTrue(responseObject.getData().getFailList().size() == 0);
        Assertions.assertTrue(responseObject.getData().getOverrideList().size() == 0);
        Assertions.assertTrue(responseObject.getData().getSkipList().size() == 0);
        cleanData(responseObject.getData().getSuccessList().stream().map(e -> e.getMetricsId()).collect(Collectors.toList()), Arrays.asList(getMetricsId(map)));
    }


    @Test
    public void testApplyAggregationMetrics01_1() throws Exception {
        mockAllTplRelationList(Arrays.asList("c80852bd-76db-47f3-a750-5c88c2164b65", "a83a4151-3d0b-44a5-9a5f-b76f13a6eaed", "a68a39ea-81a6-402f-9732-1ef7f4480095", "38b90a20-660c-498d-9a16-90f8f6a2170f"));
        Map<String, String> map = initAggregationMetrics();
        ResponseObject<ApplyTplMetricsResult> responseObject = createBasicData(getMetricsName(map), "all");
        Assertions.assertTrue(responseObject.getData().getSuccessList().size() == 4);
        Assertions.assertTrue(responseObject.getData().getFailList().size() == 0);
        Assertions.assertTrue(responseObject.getData().getOverrideList().size() == 0);
        Assertions.assertTrue(responseObject.getData().getSkipList().size() == 0);
        List<String> list = responseObject.getData().getSuccessList().stream().map(e -> e.getMetricsId()).collect(Collectors.toList());
        List<String> cleanList = list.subList(0, 2);
        cleanData(cleanList);
        mockAllTplRelationList(Arrays.asList("c80852bd-76db-47f3-a750-5c88c2164b65", "a83a4151-3d0b-44a5-9a5f-b76f13a6eaed", "a68a39ea-81a6-402f-9732-1ef7f4480095", "38b90a20-660c-498d-9a16-90f8f6a2170f"));
        Long t1 = System.currentTimeMillis();
        responseObject = createBasicData(getMetricsName(map), "all");
        System.out.println("cost-time={" + (System.currentTimeMillis() - t1) + "}");
        System.out.println(JsonUtils.toJsonString(responseObject));
        Assertions.assertTrue(responseObject.getData().getFailList().size() == 0);
        Assertions.assertTrue(responseObject.getData().getOverrideList().size() == 0);
        Assertions.assertTrue(responseObject.getData().getSkipList().size() == 2);
        Assertions.assertTrue(responseObject.getData().getSuccessList().size() == 2);
        list.removeAll(cleanList);
        cleanData(list, responseObject.getData().getSuccessList().stream().map(e -> e.getMetricsId()).collect(Collectors.toList()), Arrays.asList(getMetricsId(map)));
    }


    @Test
    public void testApplyAggregationMetrics02() throws Exception {
        mockAllTplRelationList(Arrays.asList("c80852bd-76db-47f3-a750-5c88c2164b65", "a83a4151-3d0b-44a5-9a5f-b76f13a6eaed", "a68a39ea-81a6-402f-9732-1ef7f4480095"));
        Map<String, String> map = initAggregationMetrics();
        String userId = "06e6bd8d-8591-4866-968b-ff197957793a";
        ThreadLocalStore.setUserInfoLocal(userId);
        ThreadLocalStore.setTenantInfoLocal("47046ca9-3df0-4b05-97f0-ccb99303ee9b");
        ApplyTplMetricsInput applyTplMetricsInput = new ApplyTplMetricsInput();
        applyTplMetricsInput.setIncludeTenants("asyncmq,asyncfile,11111");
        applyTplMetricsInput.setExcludeTenants(null);
        applyTplMetricsInput.setDestTenantScope("all");
        applyTplMetricsInput.setTplTenantName("Cube_Template_Manager");
        applyTplMetricsInput.setTplName("infra");
        applyTplMetricsInput.setMetrics(getMetricsName(map));
        applyTplMetricsInput.setOverride(false);
        applyTplMetricsInput.setApplyInDB(true);
        Long t1 = System.currentTimeMillis();
        ResponseObject<ApplyTplMetricsResult> responseObject = templateService.applyMetrics(applyTplMetricsInput);
        System.out.println("cost-time={" + (System.currentTimeMillis() - t1) + "}");
        System.out.println(JsonUtils.toJsonString(responseObject));
        Assertions.assertTrue("success".equals(responseObject.getStatus()));
        Assertions.assertTrue(responseObject.getData().getSuccessList().stream().map(e -> e.getTenantName()).collect(Collectors.toList()).containsAll(Arrays.asList("asyncmq", "asyncfile", "11111")));
        Assertions.assertTrue(responseObject.getData().getFailList().size() == 0);
        Assertions.assertTrue(responseObject.getData().getOverrideList().size() == 0);
        Assertions.assertTrue(responseObject.getData().getSkipList().size() == 0);
        Assertions.assertTrue(responseObject.getData().getSuccessList().size() == 3);
        cleanData(responseObject.getData().getSuccessList().stream().map(e -> e.getMetricsId()).collect(Collectors.toList()), Arrays.asList(getMetricsId(map)));
    }


    @Test
    public void testApplyAggregationMetrics02_1() throws Exception {
        mockAllTplRelationList(Arrays.asList("c80852bd-76db-47f3-a750-5c88c2164b65", "a83a4151-3d0b-44a5-9a5f-b76f13a6eaed", "a68a39ea-81a6-402f-9732-1ef7f4480095", "38b90a20-660c-498d-9a16-90f8f6a2170f"));
        Map<String, String> map = initAggregationMetrics();
        ResponseObject<ApplyTplMetricsResult> responseObject = createBasicData(getMetricsName(map), "all");
        List<String> list = responseObject.getData().getSuccessList().stream().map(e -> e.getMetricsId()).collect(Collectors.toList());
        List<String> cleanList = responseObject.getData().getSuccessList().stream().filter(e -> Arrays.asList("asyncmq").contains(e.getTenantName())).map(ee -> ee.getMetricsId()).collect(Collectors.toList());
        cleanData(cleanList);
        String userId = "06e6bd8d-8591-4866-968b-ff197957793a";
        ThreadLocalStore.setUserInfoLocal(userId);
        ThreadLocalStore.setTenantInfoLocal("47046ca9-3df0-4b05-97f0-ccb99303ee9b");
        ApplyTplMetricsInput applyTplMetricsInput = new ApplyTplMetricsInput();
        mockAllTplRelationList(Arrays.asList("c80852bd-76db-47f3-a750-5c88c2164b65", "a83a4151-3d0b-44a5-9a5f-b76f13a6eaed", "a68a39ea-81a6-402f-9732-1ef7f4480095"));
        applyTplMetricsInput.setIncludeTenants("asyncmq,asyncfile,11111");
        applyTplMetricsInput.setDestTenantScope("all");
        applyTplMetricsInput.setTplTenantName("Cube_Template_Manager");
        applyTplMetricsInput.setTplName("infra");
        applyTplMetricsInput.setMetrics(getMetricsName(map));
        applyTplMetricsInput.setOverride(false);
        applyTplMetricsInput.setApplyInDB(true);
        Long t1 = System.currentTimeMillis();
        responseObject = templateService.applyMetrics(applyTplMetricsInput);
        System.out.println("cost-time={" + (System.currentTimeMillis() - t1) + "}");
        System.out.println(JsonUtils.toJsonString(responseObject));
        Assertions.assertTrue("success".equals(responseObject.getStatus()));
        Assertions.assertTrue(responseObject.getData().getFailList().size() == 0);
        Assertions.assertTrue(responseObject.getData().getOverrideList().size() == 0);
        Assertions.assertTrue(responseObject.getData().getSkipList().size() == 2);
        Assertions.assertTrue(responseObject.getData().getSuccessList().size() == 1);
        list.removeAll(cleanList);
        cleanData(list, responseObject.getData().getSuccessList().stream().map(e -> e.getMetricsId()).collect(Collectors.toList()), Arrays.asList(getMetricsId(map)));
    }


    @Test
    public void testApplyAggregationMetrics02_2() throws Exception {
        mockAllTplRelationList(Arrays.asList("38b90a20-660c-498d-9a16-90f8f6a2170f", "a83a4151-3d0b-44a5-9a5f-b76f13a6eaed", "a68a39ea-81a6-402f-9732-1ef7f4480095", "c80852bd-76db-47f3-a750-5c88c2164b65"));
        Map<String, String> map = initAggregationMetrics();
        ResponseObject<ApplyTplMetricsResult> responseObject = createBasicData(getMetricsName(map), "all");
        List<String> list = responseObject.getData().getSuccessList().stream().map(e -> e.getMetricsId()).collect(Collectors.toList());
        List<String> cleanList = responseObject.getData().getSuccessList().stream().filter(e -> Arrays.asList("test_syn_exist").contains(e.getTenantName())).map(ee -> ee.getMetricsId()).collect(Collectors.toList());
        cleanData(cleanList);
        String userId = "06e6bd8d-8591-4866-968b-ff197957793a";
        ThreadLocalStore.setUserInfoLocal(userId);
        ThreadLocalStore.setTenantInfoLocal("47046ca9-3df0-4b05-97f0-ccb99303ee9b");
        ApplyTplMetricsInput applyTplMetricsInput = new ApplyTplMetricsInput();
        mockAllTplRelationList(Arrays.asList("38b90a20-660c-498d-9a16-90f8f6a2170f", "a83a4151-3d0b-44a5-9a5f-b76f13a6eaed"));
        applyTplMetricsInput.setIncludeTenants("test_syn_exist,asyncfile,error-11111");
        applyTplMetricsInput.setExcludeTenants(null);
        applyTplMetricsInput.setDestTenantScope("all");
        applyTplMetricsInput.setTplTenantName("Cube_Template_Manager");
        applyTplMetricsInput.setTplName("infra");
        applyTplMetricsInput.setMetrics(getMetricsName(map));
        applyTplMetricsInput.setOverride(false);
        applyTplMetricsInput.setApplyInDB(true);
        Long t1 = System.currentTimeMillis();
        responseObject = templateService.applyMetrics(applyTplMetricsInput);
        System.out.println("cost-time={" + (System.currentTimeMillis() - t1) + "}");
        System.out.println(JsonUtils.toJsonString(responseObject));
        Assertions.assertTrue("success".equals(responseObject.getStatus()));
        Assertions.assertTrue(responseObject.getData().getFailList().size() == 1);
        Assertions.assertTrue(responseObject.getData().getOverrideList().size() == 0);
        Assertions.assertTrue(responseObject.getData().getSkipList().size() == 1);
        Assertions.assertTrue(responseObject.getData().getSuccessList().size() == 1);
        list.removeAll(cleanList);
        cleanData(list, responseObject.getData().getSuccessList().stream().map(e -> e.getMetricsId()).collect(Collectors.toList()), Arrays.asList(getMetricsId(map)));
    }


    @Test
    public void testApplyAggregationMetrics03() throws Exception {
        mockAllTplRelationList(Arrays.asList("a83a4151-3d0b-44a5-9a5f-b76f13a6eaed", "a68a39ea-81a6-402f-9732-1ef7f4480095", "38b90a20-660c-498d-9a16-90f8f6a2170f", "c80852bd-76db-47f3-a750-5c88c2164b65"));
        Map<String, String> map = initAggregationMetrics();
        String userId = "06e6bd8d-8591-4866-968b-ff197957793a";
        ThreadLocalStore.setUserInfoLocal(userId);
        ThreadLocalStore.setTenantInfoLocal("47046ca9-3df0-4b05-97f0-ccb99303ee9b");
        ApplyTplMetricsInput applyTplMetricsInput = new ApplyTplMetricsInput();
        applyTplMetricsInput.setExcludeTenants("asyncmq");
        applyTplMetricsInput.setDestTenantScope("all");
        applyTplMetricsInput.setTplTenantName("Cube_Template_Manager");
        applyTplMetricsInput.setTplName("infra");
        applyTplMetricsInput.setMetrics(getMetricsName(map));
        applyTplMetricsInput.setOverride(false);
        applyTplMetricsInput.setApplyInDB(true);
        Long t1 = System.currentTimeMillis();
        ResponseObject<ApplyTplMetricsResult> responseObject = templateService.applyMetrics(applyTplMetricsInput);
        System.out.println("cost-time={" + (System.currentTimeMillis() - t1) + "}");
        System.out.println(JsonUtils.toJsonString(responseObject));
        Assertions.assertTrue("success".equals(responseObject.getStatus()));
        Assertions.assertTrue(responseObject.getData().getFailList().size() == 0);
        Assertions.assertTrue(responseObject.getData().getOverrideList().size() == 0);
        Assertions.assertTrue(responseObject.getData().getSkipList().size() == 0);
        Assertions.assertTrue(responseObject.getData().getSuccessList().size() == 3);
        cleanData(responseObject.getData().getSuccessList().stream().map(e -> e.getMetricsId()).collect(Collectors.toList()), Arrays.asList(getMetricsId(map)));
    }

    @Test
    public void testApplyAggregationMetrics03_1() throws Exception {
        mockAllTplRelationList(Arrays.asList("a83a4151-3d0b-44a5-9a5f-b76f13a6eaed", "a68a39ea-81a6-402f-9732-1ef7f4480095", "c80852bd-76db-47f3-a750-5c88c2164b65"));
        Map<String, String> map = initAggregationMetrics();
        String userId = "06e6bd8d-8591-4866-968b-ff197957793a";
        ThreadLocalStore.setUserInfoLocal(userId);
        ThreadLocalStore.setTenantInfoLocal("47046ca9-3df0-4b05-97f0-ccb99303ee9b");
        ApplyTplMetricsInput applyTplMetricsInput = new ApplyTplMetricsInput();
        applyTplMetricsInput.setIncludeTenants("asyncfile,11111,asyncmq");
        applyTplMetricsInput.setExcludeTenants("asyncfile");
        applyTplMetricsInput.setDestTenantScope("all");
        applyTplMetricsInput.setTplTenantName("Cube_Template_Manager");
        applyTplMetricsInput.setTplName("infra");
        applyTplMetricsInput.setMetrics(getMetricsName(map));
        applyTplMetricsInput.setOverride(false);
        applyTplMetricsInput.setApplyInDB(true);
        Long t1 = System.currentTimeMillis();
        ResponseObject<ApplyTplMetricsResult> responseObject = templateService.applyMetrics(applyTplMetricsInput);
        System.out.println("cost-time={" + (System.currentTimeMillis() - t1) + "}");
        System.out.println(JsonUtils.toJsonString(responseObject));
        Assertions.assertTrue("success".equals(responseObject.getStatus()));
        Assertions.assertTrue(responseObject.getData().getFailList().size() == 0);
        Assertions.assertTrue(responseObject.getData().getOverrideList().size() == 0);
        Assertions.assertTrue(responseObject.getData().getSkipList().size() == 0);
        Assertions.assertTrue(responseObject.getData().getSuccessList().size() == 2);
        cleanData(responseObject.getData().getSuccessList().stream().map(e -> e.getMetricsId()).collect(Collectors.toList()), Arrays.asList(getMetricsId(map)));
    }

    @Test
    public void testApplyAggregationMetrics03_2() throws Exception {
        mockAllTplRelationList(Arrays.asList("c80852bd-76db-47f3-a750-5c88c2164b65", "a83a4151-3d0b-44a5-9a5f-b76f13a6eaed"));
        Map<String, String> map = initAggregationMetrics();
        String userId = "06e6bd8d-8591-4866-968b-ff197957793a";
        ThreadLocalStore.setUserInfoLocal(userId);
        ThreadLocalStore.setTenantInfoLocal("47046ca9-3df0-4b05-97f0-ccb99303ee9b");
        ApplyTplMetricsInput applyTplMetricsInput = new ApplyTplMetricsInput();
        applyTplMetricsInput.setIncludeTenants("asyncmq,asyncfile,error-11111");
        applyTplMetricsInput.setExcludeTenants("asyncmq,error-asyncfile");
        applyTplMetricsInput.setDestTenantScope("all");
        applyTplMetricsInput.setTplTenantName("Cube_Template_Manager");
        applyTplMetricsInput.setTplName("infra");
        applyTplMetricsInput.setMetrics(getMetricsName(map));
        applyTplMetricsInput.setOverride(false);
        applyTplMetricsInput.setApplyInDB(true);
        Long t1 = System.currentTimeMillis();
        ResponseObject<ApplyTplMetricsResult> responseObject = templateService.applyMetrics(applyTplMetricsInput);
        System.out.println("cost-time={" + (System.currentTimeMillis() - t1) + "}");
        System.out.println(JsonUtils.toJsonString(responseObject));
        Assertions.assertTrue("success".equals(responseObject.getStatus()));
        Assertions.assertTrue(responseObject.getData().getFailList().size() == 2);
        Assertions.assertTrue(responseObject.getData().getOverrideList().size() == 0);
        Assertions.assertTrue(responseObject.getData().getSkipList().size() == 0);
        Assertions.assertTrue(responseObject.getData().getSuccessList().size() == 1);
        cleanData(responseObject.getData().getSuccessList().stream().map(e -> e.getMetricsId()).collect(Collectors.toList()), Arrays.asList(getMetricsId(map)));
    }

    @Test
    public void testApplyAggregationMetrics03_3() throws Exception {
        mockAllTplRelationList(Arrays.asList("a83a4151-3d0b-44a5-9a5f-b76f13a6eaed", "a68a39ea-81a6-402f-9732-1ef7f4480095", "38b90a20-660c-498d-9a16-90f8f6a2170f", "c80852bd-76db-47f3-a750-5c88c2164b65"));
        Map<String, String> map = initAggregationMetrics();
        ResponseObject<ApplyTplMetricsResult> responseObject = createBasicData(getMetricsName(map), "all");
        List<String> list = responseObject.getData().getSuccessList().stream().map(e -> e.getMetricsId()).collect(Collectors.toList());
        List<String> cleanList = responseObject.getData().getSuccessList().stream().filter(e -> Arrays.asList("asyncfile").contains(e.getTenantName())).map(ee -> ee.getMetricsId()).collect(Collectors.toList());
        cleanData(cleanList);
        String userId = "06e6bd8d-8591-4866-968b-ff197957793a";
        ThreadLocalStore.setUserInfoLocal(userId);
        ThreadLocalStore.setTenantInfoLocal("47046ca9-3df0-4b05-97f0-ccb99303ee9b");
        ApplyTplMetricsInput applyTplMetricsInput = new ApplyTplMetricsInput();
        mockAllTplRelationList(Arrays.asList("c80852bd-76db-47f3-a750-5c88c2164b65", "a83a4151-3d0b-44a5-9a5f-b76f13a6eaed", "a68a39ea-81a6-402f-9732-1ef7f4480095"));
        applyTplMetricsInput.setIncludeTenants("asyncmq,asyncfile,11111");
        applyTplMetricsInput.setExcludeTenants("asyncmq,error-asyncfile");
        applyTplMetricsInput.setDestTenantScope("all");
        applyTplMetricsInput.setTplTenantName("Cube_Template_Manager");
        applyTplMetricsInput.setTplName("infra");
        applyTplMetricsInput.setMetrics(getMetricsName(map));
        applyTplMetricsInput.setOverride(false);
        applyTplMetricsInput.setApplyInDB(true);
        Long t1 = System.currentTimeMillis();
        responseObject = templateService.applyMetrics(applyTplMetricsInput);
        System.out.println("cost-time={" + (System.currentTimeMillis() - t1) + "}");
        System.out.println(JsonUtils.toJsonString(responseObject));
        Assertions.assertTrue("success".equals(responseObject.getStatus()));
        Assertions.assertTrue(responseObject.getData().getFailList().size() == 1);
        Assertions.assertTrue(responseObject.getData().getOverrideList().size() == 0);
        Assertions.assertTrue(responseObject.getData().getSkipList().size() == 1);
        Assertions.assertTrue(responseObject.getData().getSuccessList().size() == 1);
        list.removeAll(cleanList);
        cleanData(list, responseObject.getData().getSuccessList().stream().map(e -> e.getMetricsId()).collect(Collectors.toList()), Arrays.asList(getMetricsId(map)));
    }


    @Test
    public void testApplyAggregationMetrics04() throws Exception {
        mockAllTplRelationList(Arrays.asList("a83a4151-3d0b-44a5-9a5f-b76f13a6eaed", "38b90a20-660c-498d-9a16-90f8f6a2170f", "a68a39ea-81a6-402f-9732-1ef7f4480095"));
        Map<String, String> map = initAggregationMetrics();
        String userId = "06e6bd8d-8591-4866-968b-ff197957793a";
        ThreadLocalStore.setUserInfoLocal(userId);
        ThreadLocalStore.setTenantInfoLocal("47046ca9-3df0-4b05-97f0-ccb99303ee9b");
        ApplyTplMetricsInput applyTplMetricsInput = new ApplyTplMetricsInput();
        //38b90a20-660c-498d-9a16-90f8f6a2170f-》outer
        applyTplMetricsInput.setIncludeTenants("asyncfile,test_syn_exist,11111");
        applyTplMetricsInput.setExcludeTenants("asyncfile");
        applyTplMetricsInput.setDestTenantScope("outer");
        applyTplMetricsInput.setTplTenantName("Cube_Template_Manager");
        applyTplMetricsInput.setTplName("infra");
        applyTplMetricsInput.setMetrics(getMetricsName(map));
        applyTplMetricsInput.setOverride(false);
        applyTplMetricsInput.setApplyInDB(true);
        Long t1 = System.currentTimeMillis();
        ResponseObject<ApplyTplMetricsResult> responseObject = templateService.applyMetrics(applyTplMetricsInput);
        System.out.println("cost-time={" + (System.currentTimeMillis() - t1) + "}");
        System.out.println(JsonUtils.toJsonString(responseObject));
        Assertions.assertTrue("success".equals(responseObject.getStatus()));
        Assertions.assertTrue(responseObject.getData().getFailList().size() == 0);
        Assertions.assertTrue(responseObject.getData().getOverrideList().size() == 0);
        Assertions.assertTrue(responseObject.getData().getSkipList().size() == 0);
        Assertions.assertTrue(responseObject.getData().getSuccessList().size() == 1);
        cleanData(responseObject.getData().getSuccessList().stream().map(e -> e.getMetricsId()).collect(Collectors.toList()), Arrays.asList(getMetricsId(map)));
    }


    @Test
    public void testApplyAggregationMetrics04_1() throws Exception {
        mockAllTplRelationList(Arrays.asList("a83a4151-3d0b-44a5-9a5f-b76f13a6eaed", "a68a39ea-81a6-402f-9732-1ef7f4480095", "38b90a20-660c-498d-9a16-90f8f6a2170f", "c80852bd-76db-47f3-a750-5c88c2164b65"));
        Map<String, String> map = initAggregationMetrics();
        ResponseObject<ApplyTplMetricsResult> responseObject = createBasicData(getMetricsName(map), "outer");
        List<String> list = responseObject.getData().getSuccessList().stream().map(e -> e.getMetricsId()).collect(Collectors.toList());
        List<String> cleanList = responseObject.getData().getSuccessList().stream().filter(e -> Arrays.asList("test_syn_exist").contains(e.getTenantName())).map(ee -> ee.getMetricsId()).collect(Collectors.toList());
        cleanData(cleanList);
        String userId = "06e6bd8d-8591-4866-968b-ff197957793a";
        ThreadLocalStore.setUserInfoLocal(userId);
        ThreadLocalStore.setTenantInfoLocal("47046ca9-3df0-4b05-97f0-ccb99303ee9b");
        ApplyTplMetricsInput applyTplMetricsInput = new ApplyTplMetricsInput();
        mockAllTplRelationList(Arrays.asList("a83a4151-3d0b-44a5-9a5f-b76f13a6eaed", "38b90a20-660c-498d-9a16-90f8f6a2170f", "a68a39ea-81a6-402f-9732-1ef7f4480095"));
        applyTplMetricsInput.setIncludeTenants("asyncfile,test_syn_exist,11111");
        applyTplMetricsInput.setExcludeTenants("asyncfile");
        applyTplMetricsInput.setDestTenantScope("outer");
        applyTplMetricsInput.setTplTenantName("Cube_Template_Manager");
        applyTplMetricsInput.setTplName("infra");
        applyTplMetricsInput.setMetrics(getMetricsName(map));
        applyTplMetricsInput.setOverride(false);
        applyTplMetricsInput.setApplyInDB(true);
        Long t1 = System.currentTimeMillis();
        responseObject = templateService.applyMetrics(applyTplMetricsInput);
        System.out.println("cost-time={" + (System.currentTimeMillis() - t1) + "}");
        System.out.println(JsonUtils.toJsonString(responseObject));
        Assertions.assertTrue("success".equals(responseObject.getStatus()));
        Assertions.assertTrue(responseObject.getData().getFailList().size() == 0);
        Assertions.assertTrue(responseObject.getData().getOverrideList().size() == 0);
        Assertions.assertTrue(responseObject.getData().getSkipList().size() == 0);
        Assertions.assertTrue(responseObject.getData().getSuccessList().size() == 1);
        list.removeAll(cleanList);
        cleanData(list, responseObject.getData().getSuccessList().stream().map(e -> e.getMetricsId()).collect(Collectors.toList()), Arrays.asList(getMetricsId(map)));
    }

    @Test
    public void testApplyAggregationMetrics04_2() throws Exception {
        mockAllTplRelationList(Arrays.asList("a83a4151-3d0b-44a5-9a5f-b76f13a6eaed", "a68a39ea-81a6-402f-9732-1ef7f4480095", "38b90a20-660c-498d-9a16-90f8f6a2170f", "c80852bd-76db-47f3-a750-5c88c2164b65"));
        Map<String, String> map = initAggregationMetrics();
        ResponseObject<ApplyTplMetricsResult> responseObject = createBasicData(getMetricsName(map), "outer");
        List<String> list = responseObject.getData().getSuccessList().stream().map(e -> e.getMetricsId()).collect(Collectors.toList());
        List<String> cleanList = responseObject.getData().getSuccessList().stream().filter(e -> !Arrays.asList("test_syn_exist").contains(e.getTenantName())).map(ee -> ee.getMetricsId()).collect(Collectors.toList());
        cleanData(cleanList);
        String userId = "06e6bd8d-8591-4866-968b-ff197957793a";
        ThreadLocalStore.setUserInfoLocal(userId);
        ThreadLocalStore.setTenantInfoLocal("47046ca9-3df0-4b05-97f0-ccb99303ee9b");
        ApplyTplMetricsInput applyTplMetricsInput = new ApplyTplMetricsInput();
        mockAllTplRelationList(Arrays.asList("a83a4151-3d0b-44a5-9a5f-b76f13a6eaed", "38b90a20-660c-498d-9a16-90f8f6a2170f", "a68a39ea-81a6-402f-9732-1ef7f4480095"));
        applyTplMetricsInput.setIncludeTenants("asyncfile,test_syn_exist,11111");
        applyTplMetricsInput.setExcludeTenants("asyncfile");
        applyTplMetricsInput.setDestTenantScope("outer");
        applyTplMetricsInput.setTplTenantName("Cube_Template_Manager");
        applyTplMetricsInput.setTplName("infra");
        applyTplMetricsInput.setMetrics(getMetricsName(map));
        applyTplMetricsInput.setOverride(false);
        applyTplMetricsInput.setApplyInDB(true);
        Long t1 = System.currentTimeMillis();
        responseObject = templateService.applyMetrics(applyTplMetricsInput);
        System.out.println("cost-time={" + (System.currentTimeMillis() - t1) + "}");
        System.out.println(JsonUtils.toJsonString(responseObject));
        Assertions.assertTrue("success".equals(responseObject.getStatus()));
        Assertions.assertTrue(responseObject.getData().getFailList().size() == 0);
        Assertions.assertTrue(responseObject.getData().getOverrideList().size() == 0);
        Assertions.assertTrue(responseObject.getData().getSkipList().size() == 1);
        Assertions.assertTrue(responseObject.getData().getSuccessList().size() == 0);
        list.removeAll(cleanList);
        cleanData(list, responseObject.getData().getSuccessList().stream().map(e -> e.getMetricsId()).collect(Collectors.toList()), Arrays.asList(getMetricsId(map)));
    }


    @Test
    public void testApplyAggregationMetrics05() throws Exception {
        mockAllTplRelationList(Arrays.asList("a83a4151-3d0b-44a5-9a5f-b76f13a6eaed", "38b90a20-660c-498d-9a16-90f8f6a2170f", "a68a39ea-81a6-402f-9732-1ef7f4480095"));
        Map<String, String> map = initAggregationMetrics();
        String userId = "06e6bd8d-8591-4866-968b-ff197957793a";
        ThreadLocalStore.setUserInfoLocal(userId);
        ThreadLocalStore.setTenantInfoLocal("47046ca9-3df0-4b05-97f0-ccb99303ee9b");
        ApplyTplMetricsInput applyTplMetricsInput = new ApplyTplMetricsInput();
        applyTplMetricsInput.setIncludeTenants("asyncfile,test_syn_exist,11111");
        applyTplMetricsInput.setExcludeTenants("asyncfile");
        applyTplMetricsInput.setDestTenantScope("inner");
        applyTplMetricsInput.setTplTenantName("Cube_Template_Manager");
        applyTplMetricsInput.setTplName("infra");
        applyTplMetricsInput.setMetrics(getMetricsName(map));
        applyTplMetricsInput.setOverride(false);
        applyTplMetricsInput.setApplyInDB(true);
        Long t1 = System.currentTimeMillis();
        ResponseObject<ApplyTplMetricsResult> responseObject = templateService.applyMetrics(applyTplMetricsInput);
        System.out.println("cost-time={" + (System.currentTimeMillis() - t1) + "}");
        System.out.println(JsonUtils.toJsonString(responseObject));
        Assertions.assertTrue("success".equals(responseObject.getStatus()));
        Assertions.assertTrue(responseObject.getData().getFailList().size() == 0);
        Assertions.assertTrue(responseObject.getData().getOverrideList().size() == 0);
        Assertions.assertTrue(responseObject.getData().getSkipList().size() == 0);
        Assertions.assertTrue(responseObject.getData().getSuccessList().size() == 1);
        cleanData(responseObject.getData().getSuccessList().stream().map(e -> e.getMetricsId()).collect(Collectors.toList()), Arrays.asList(getMetricsId(map)));
    }


    @Test
    public void testApplyAggregationMetrics05_1() throws Exception {
        mockAllTplRelationList(Arrays.asList("a83a4151-3d0b-44a5-9a5f-b76f13a6eaed", "a68a39ea-81a6-402f-9732-1ef7f4480095", "38b90a20-660c-498d-9a16-90f8f6a2170f", "c80852bd-76db-47f3-a750-5c88c2164b65"));
        Map<String, String> map = initAggregationMetrics();
        ResponseObject<ApplyTplMetricsResult> responseObject = createBasicData(getMetricsName(map), "inner");
        List<String> list = responseObject.getData().getSuccessList().stream().map(e -> e.getMetricsId()).collect(Collectors.toList());
        List<String> cleanList = responseObject.getData().getSuccessList().stream().filter(e -> Arrays.asList("11111").contains(e.getTenantName())).map(ee -> ee.getMetricsId()).collect(Collectors.toList());
        cleanData(cleanList);
        String userId = "06e6bd8d-8591-4866-968b-ff197957793a";
        ThreadLocalStore.setUserInfoLocal(userId);
        ThreadLocalStore.setTenantInfoLocal("47046ca9-3df0-4b05-97f0-ccb99303ee9b");
        ApplyTplMetricsInput applyTplMetricsInput = new ApplyTplMetricsInput();
        mockAllTplRelationList(Arrays.asList("a83a4151-3d0b-44a5-9a5f-b76f13a6eaed", "38b90a20-660c-498d-9a16-90f8f6a2170f", "a68a39ea-81a6-402f-9732-1ef7f4480095"));
        applyTplMetricsInput.setIncludeTenants("asyncfile,test_syn_exist,11111");
        applyTplMetricsInput.setExcludeTenants("asyncfile");
        applyTplMetricsInput.setDestTenantScope("inner");
        applyTplMetricsInput.setTplTenantName("Cube_Template_Manager");
        applyTplMetricsInput.setTplName("infra");
        applyTplMetricsInput.setMetrics(getMetricsName(map));
        applyTplMetricsInput.setOverride(false);
        applyTplMetricsInput.setApplyInDB(true);
        Long t1 = System.currentTimeMillis();
        responseObject = templateService.applyMetrics(applyTplMetricsInput);
        System.out.println("cost-time={" + (System.currentTimeMillis() - t1) + "}");
        System.out.println(JsonUtils.toJsonString(responseObject));
        Assertions.assertTrue("success".equals(responseObject.getStatus()));
        Assertions.assertTrue(responseObject.getData().getFailList().size() == 0);
        Assertions.assertTrue(responseObject.getData().getOverrideList().size() == 0);
        Assertions.assertTrue(responseObject.getData().getSkipList().size() == 0);
        Assertions.assertTrue(responseObject.getData().getSuccessList().size() >= 1);
        list.removeAll(cleanList);
        cleanData(list, responseObject.getData().getSuccessList().stream().map(e -> e.getMetricsId()).collect(Collectors.toList()), Arrays.asList(getMetricsId(map)));
    }


    //>>>>>>>>>>>>>>>>alarm>>>>>>>>>>>>>>>>>>>>
    private Map initAlarm(String metricId) {
        Map map = new HashMap();
        String alarmName = "test_c_test_alarm_" + System.nanoTime();
        String userId = "06e6bd8d-8591-4866-968b-ff197957793a";
        ThreadLocalStore.setUserInfoLocal(userId);
        String tenantId = "47046ca9-3df0-4b05-97f0-ccb99303ee9b";
        ThreadLocalStore.setTenantInfoLocal(tenantId);
        AlarmDefinition alarmDefinition = new AlarmDefinition();
        alarmDefinition.setEnabled(true);
        alarmDefinition.setTenantId(tenantId);
        alarmDefinition.setUserId(userId);

        List<AlarmRule> rules = new ArrayList<>();

        AlarmRule alarmRule = new AlarmRule();
        alarmRule.setNeedHits(1);
        alarmRule.setLevel(AlarmLevel.WARN);

        List<RuleCondition> conditions = new ArrayList<>();

        RuleCondition ruleCondition = new RuleCondition();
        ruleCondition.setConditionType(ConditionType.TAG);
        ruleCondition.setName("clusterId");
        ruleCondition.setOperator("in");
        ruleCondition.setThreshold("a,b");

        RuleCondition ruleCondition2 = new RuleCondition();
        ruleCondition2.setConditionType(ConditionType.FIELD);
        ruleCondition2.setName("byteIn");
        ruleCondition2.setOperator("<");
        ruleCondition2.setThreshold("-1");

        conditions.add(ruleCondition);
        conditions.add(ruleCondition2);
        alarmRule.setConditions(conditions);
        rules.add(alarmRule);
        alarmDefinition.setRules(rules);


        List<Notification> notifications = new ArrayList<>();

        Notification notification = new Notification();
        Channel channel = new Channel();
        channel.setId("b82551ec-2201-4518-beb5-9b4abeed3edd");

        notification.setChannel(channel);
        notification.setTitle("this is alarm syn");
        notification.setContent("byteIn is ${byteIn}");
        notification.setWhichLevels("WARN");
        notifications.add(notification);

        alarmDefinition.setNotifications(notifications);
        alarmDefinition.setTimesInPeriod(3);
        alarmDefinition.setPeriodInMinutes(30);
        alarmDefinition.setMetricId(metricId);
        alarmDefinition.setName(alarmName);

        System.out.println(JsonUtils.toJsonString(alarmDefinition));

        ResponseObject<AlarmDefinition> responseObject = alarmDefinitionService.save(alarmDefinition);
        Assertions.assertTrue("success".equals(responseObject.getStatus()));
        map.put(responseObject.getData().getId(), alarmName);
        return map;
    }


    @Test
    public void testApplyAlarm01() throws Exception {
        mockAllTplRelationList(Arrays.asList("c80852bd-76db-47f3-a750-5c88c2164b65", "a83a4151-3d0b-44a5-9a5f-b76f13a6eaed", "a68a39ea-81a6-402f-9732-1ef7f4480095", "38b90a20-660c-498d-9a16-90f8f6a2170f"));
        Map<String, String> metricsMap = initOriginalMetrics();
        ResponseObject<ApplyTplMetricsResult> metricsResultResponseObject = createBasicData(getMetricsName(metricsMap), "all");

        Assertions.assertTrue(metricsResultResponseObject.getData().getSuccessList().size() == 4);
        Assertions.assertTrue(metricsResultResponseObject.getData().getFailList().size() == 0);
        Assertions.assertTrue(metricsResultResponseObject.getData().getOverrideList().size() == 0);
        Assertions.assertTrue(metricsResultResponseObject.getData().getSkipList().size() == 0);
        Map<String, String> alarmMap = initAlarm(getMetricsId(metricsMap));

        Long t1 = System.currentTimeMillis();
        ResponseObject<ApplyTplAlarmResult> alarmResultResponseObject = createAlarmBasicData(getAlarmName(alarmMap), "all");
        System.out.println("cost-time={" + (System.currentTimeMillis() - t1) + "}");
        System.out.println(JsonUtils.toJsonString(alarmResultResponseObject));
        Assertions.assertTrue(alarmResultResponseObject.getData().getSuccessList().size() == 4);
        Assertions.assertTrue(alarmResultResponseObject.getData().getFailList().size() == 0);
        Assertions.assertTrue(alarmResultResponseObject.getData().getOverrideList().size() == 0);
        Assertions.assertTrue(alarmResultResponseObject.getData().getSkipList().size() == 0);

        cleanAlarm(Arrays.asList(getAlarmId(alarmMap)), alarmResultResponseObject.getData().getSuccessList().stream().map(e -> e.getAlarmId()).collect(Collectors.toList()));
        cleanData(Arrays.asList(getMetricsId(metricsMap)), metricsResultResponseObject.getData().getSuccessList().stream().map(e -> e.getMetricsId()).collect(Collectors.toList()));
    }


    @Test
    public void testApplyAlarm01_1() throws Exception {
        mockAllTplRelationList(Arrays.asList("c80852bd-76db-47f3-a750-5c88c2164b65", "a83a4151-3d0b-44a5-9a5f-b76f13a6eaed", "a68a39ea-81a6-402f-9732-1ef7f4480095", "38b90a20-660c-498d-9a16-90f8f6a2170f"));
        Map<String, String> metricsMap = initOriginalMetrics();
        ResponseObject<ApplyTplMetricsResult> metricsResultResponseObject = createBasicData(getMetricsName(metricsMap), "all");
        Assertions.assertTrue(metricsResultResponseObject.getData().getSuccessList().size() == 4);
        Assertions.assertTrue(metricsResultResponseObject.getData().getFailList().size() == 0);
        Assertions.assertTrue(metricsResultResponseObject.getData().getOverrideList().size() == 0);
        Assertions.assertTrue(metricsResultResponseObject.getData().getSkipList().size() == 0);
        Map<String, String> alarmMap = initAlarm(getMetricsId(metricsMap));
        ResponseObject<ApplyTplAlarmResult> alarmResultResponseObject = createAlarmBasicData(getAlarmName(alarmMap), "all");
        Assertions.assertTrue(alarmResultResponseObject.getData().getSuccessList().size() == 4);
        Assertions.assertTrue(alarmResultResponseObject.getData().getFailList().size() == 0);
        Assertions.assertTrue(alarmResultResponseObject.getData().getOverrideList().size() == 0);
        Assertions.assertTrue(alarmResultResponseObject.getData().getSkipList().size() == 0);
        List<String> list = alarmResultResponseObject.getData().getSuccessList().stream().map(e -> e.getAlarmId()).collect(Collectors.toList());
        List<String> cleanList = list.subList(0, 2);
        cleanAlarm(cleanList);
        mockAllTplRelationList(Arrays.asList("c80852bd-76db-47f3-a750-5c88c2164b65", "a83a4151-3d0b-44a5-9a5f-b76f13a6eaed", "a68a39ea-81a6-402f-9732-1ef7f4480095", "38b90a20-660c-498d-9a16-90f8f6a2170f"));
        Long t1 = System.currentTimeMillis();
        alarmResultResponseObject = createAlarmBasicData(getAlarmName(alarmMap), "all");
        System.out.println("cost-time={" + (System.currentTimeMillis() - t1) + "}");
        System.out.println(JsonUtils.toJsonString(alarmResultResponseObject));
        Assertions.assertTrue(alarmResultResponseObject.getData().getFailList().size() == 0);
        Assertions.assertTrue(alarmResultResponseObject.getData().getOverrideList().size() == 0);
        Assertions.assertTrue(alarmResultResponseObject.getData().getSkipList().size() == 2);
        Assertions.assertTrue(alarmResultResponseObject.getData().getSuccessList().size() == 2);
        list.removeAll(cleanList);
        cleanAlarm(Arrays.asList(getAlarmId(alarmMap)), list, alarmResultResponseObject.getData().getSuccessList().stream().map(e -> e.getAlarmId()).collect(Collectors.toList()));
        cleanData(Arrays.asList(getMetricsId(metricsMap)), metricsResultResponseObject.getData().getSuccessList().stream().map(e -> e.getMetricsId()).collect(Collectors.toList()));
    }


    @Test
    public void testApplyAlarm02() throws Exception {
        mockAllTplRelationList(Arrays.asList("c80852bd-76db-47f3-a750-5c88c2164b65", "a83a4151-3d0b-44a5-9a5f-b76f13a6eaed", "a68a39ea-81a6-402f-9732-1ef7f4480095"));
        Map<String, String> metricsMap = initOriginalMetrics();
        ResponseObject<ApplyTplMetricsResult> metricsResultResponseObject = createBasicData(getMetricsName(metricsMap), "all");
        Assertions.assertTrue("success".equals(metricsResultResponseObject.getStatus()));
        Map<String, String> alarmMap = initAlarm(getMetricsId(metricsMap));

        String userId = "06e6bd8d-8591-4866-968b-ff197957793a";
        ThreadLocalStore.setUserInfoLocal(userId);
        ThreadLocalStore.setTenantInfoLocal("47046ca9-3df0-4b05-97f0-ccb99303ee9b");
        ApplyTplAlarmInput applyTplAlarmInput = new ApplyTplAlarmInput();
        applyTplAlarmInput.setIncludeTenants("asyncmq,asyncfile,11111");
        applyTplAlarmInput.setExcludeTenants(null);
        applyTplAlarmInput.setDestTenantScope("all");
        applyTplAlarmInput.setTplTenantName("Cube_Template_Manager");
        applyTplAlarmInput.setTplName("infra");
        applyTplAlarmInput.setAlarm(getAlarmName(alarmMap));
        applyTplAlarmInput.setOverride(false);
        applyTplAlarmInput.setApplyInDB(true);
        Long t1 = System.currentTimeMillis();
        ResponseObject<ApplyTplAlarmResult> alarmResultResponseObject = templateService.applyAlarm(applyTplAlarmInput);
        System.out.println("cost-time={" + (System.currentTimeMillis() - t1) + "}");
        System.out.println(JsonUtils.toJsonString(alarmResultResponseObject));
        Assertions.assertTrue(alarmResultResponseObject.getData().getFailList().size() == 0);
        Assertions.assertTrue(alarmResultResponseObject.getData().getOverrideList().size() == 0);
        Assertions.assertTrue(alarmResultResponseObject.getData().getSkipList().size() == 0);
        Assertions.assertTrue(alarmResultResponseObject.getData().getSuccessList().size() == 3);

        cleanAlarm(Arrays.asList(getAlarmId(alarmMap)), alarmResultResponseObject.getData().getSuccessList().stream().map(e -> e.getAlarmId()).collect(Collectors.toList()));
        cleanData(Arrays.asList(getMetricsId(metricsMap)), metricsResultResponseObject.getData().getSuccessList().stream().map(e -> e.getMetricsId()).collect(Collectors.toList()));
    }


    @Test
    public void testApplyAlarm02_1() throws Exception {
        mockAllTplRelationList(Arrays.asList("c80852bd-76db-47f3-a750-5c88c2164b65", "a83a4151-3d0b-44a5-9a5f-b76f13a6eaed", "a68a39ea-81a6-402f-9732-1ef7f4480095", "38b90a20-660c-498d-9a16-90f8f6a2170f"));
        Map<String, String> metricsMap = initOriginalMetrics();
        ResponseObject<ApplyTplMetricsResult> metricsResultResponseObject = createBasicData(getMetricsName(metricsMap), "all");
        Assertions.assertTrue("success".equals(metricsResultResponseObject.getStatus()));
        Map<String, String> alarmMap = initAlarm(getMetricsId(metricsMap));
        ResponseObject<ApplyTplAlarmResult> alarmResultResponseObject = createAlarmBasicData(getAlarmName(alarmMap), "all");
        List<String> list = alarmResultResponseObject.getData().getSuccessList().stream().map(e -> e.getAlarmId()).collect(Collectors.toList());
        List<String> cleanList = alarmResultResponseObject.getData().getSuccessList().stream().filter(e -> Arrays.asList("asyncmq").contains(e.getTenantName())).map(ee -> ee.getAlarmId()).collect(Collectors.toList());
        cleanAlarm(cleanList);
        String userId = "06e6bd8d-8591-4866-968b-ff197957793a";
        ThreadLocalStore.setUserInfoLocal(userId);
        ThreadLocalStore.setTenantInfoLocal("47046ca9-3df0-4b05-97f0-ccb99303ee9b");
        ApplyTplAlarmInput applyTplAlarmInput = new ApplyTplAlarmInput();
        mockAllTplRelationList(Arrays.asList("c80852bd-76db-47f3-a750-5c88c2164b65", "a83a4151-3d0b-44a5-9a5f-b76f13a6eaed", "a68a39ea-81a6-402f-9732-1ef7f4480095"));
        applyTplAlarmInput.setIncludeTenants("asyncmq,asyncfile,11111");
        applyTplAlarmInput.setExcludeTenants(null);
        applyTplAlarmInput.setDestTenantScope("all");
        applyTplAlarmInput.setTplTenantName("Cube_Template_Manager");
        applyTplAlarmInput.setTplName("infra");
        applyTplAlarmInput.setAlarm(getAlarmName(alarmMap));
        applyTplAlarmInput.setOverride(false);
        applyTplAlarmInput.setApplyInDB(true);
        Long t1 = System.currentTimeMillis();
        alarmResultResponseObject = templateService.applyAlarm(applyTplAlarmInput);
        System.out.println("cost-time={" + (System.currentTimeMillis() - t1) + "}");
        System.out.println(JsonUtils.toJsonString(alarmResultResponseObject));
        Assertions.assertTrue(alarmResultResponseObject.getData().getFailList().size() == 0);
        Assertions.assertTrue(alarmResultResponseObject.getData().getOverrideList().size() == 0);
        Assertions.assertTrue(alarmResultResponseObject.getData().getSkipList().size() == 2);
        Assertions.assertTrue(alarmResultResponseObject.getData().getSuccessList().size() == 1);
        list.removeAll(cleanList);
        cleanAlarm(Arrays.asList(getAlarmId(alarmMap)), list, alarmResultResponseObject.getData().getSuccessList().stream().map(e -> e.getAlarmId()).collect(Collectors.toList()));
        cleanData(Arrays.asList(getMetricsId(metricsMap)), metricsResultResponseObject.getData().getSuccessList().stream().map(e -> e.getMetricsId()).collect(Collectors.toList()));
    }


    @Test
    public void testApplyAlarm02_2() throws Exception {
        mockAllTplRelationList(Arrays.asList("38b90a20-660c-498d-9a16-90f8f6a2170f", "a83a4151-3d0b-44a5-9a5f-b76f13a6eaed", "a68a39ea-81a6-402f-9732-1ef7f4480095", "c80852bd-76db-47f3-a750-5c88c2164b65"));
        Map<String, String> metricsMap = initOriginalMetrics();
        ResponseObject<ApplyTplMetricsResult> metricsResultResponseObject = createBasicData(getMetricsName(metricsMap), "all");
        Assertions.assertTrue("success".equals(metricsResultResponseObject.getStatus()));
        Map<String, String> alarmMap = initAlarm(getMetricsId(metricsMap));
        ResponseObject<ApplyTplAlarmResult> alarmResultResponseObject = createAlarmBasicData(getAlarmName(alarmMap), "all");
        List<String> list = alarmResultResponseObject.getData().getSuccessList().stream().map(e -> e.getAlarmId()).collect(Collectors.toList());
        List<String> cleanList = alarmResultResponseObject.getData().getSuccessList().stream().filter(e -> Arrays.asList("test_syn_exist").contains(e.getTenantName())).map(ee -> ee.getAlarmId()).collect(Collectors.toList());
        cleanAlarm(cleanList);
        String userId = "06e6bd8d-8591-4866-968b-ff197957793a";
        ThreadLocalStore.setUserInfoLocal(userId);
        ThreadLocalStore.setTenantInfoLocal("47046ca9-3df0-4b05-97f0-ccb99303ee9b");
        ApplyTplAlarmInput applyTplAlarmInput = new ApplyTplAlarmInput();
        mockAllTplRelationList(Arrays.asList("38b90a20-660c-498d-9a16-90f8f6a2170f", "a83a4151-3d0b-44a5-9a5f-b76f13a6eaed"));
        applyTplAlarmInput.setIncludeTenants("test_syn_exist,asyncfile,error-11111");
        applyTplAlarmInput.setExcludeTenants(null);
        applyTplAlarmInput.setDestTenantScope("all");
        applyTplAlarmInput.setTplTenantName("Cube_Template_Manager");
        applyTplAlarmInput.setTplName("infra");
        applyTplAlarmInput.setAlarm(getAlarmName(alarmMap));
        applyTplAlarmInput.setOverride(false);
        applyTplAlarmInput.setApplyInDB(true);
        Long t1 = System.currentTimeMillis();
        alarmResultResponseObject = templateService.applyAlarm(applyTplAlarmInput);
        System.out.println("cost-time={" + (System.currentTimeMillis() - t1) + "}");
        System.out.println(JsonUtils.toJsonString(alarmResultResponseObject));
        Assertions.assertTrue(alarmResultResponseObject.getData().getFailList().size() == 1);
        Assertions.assertTrue(alarmResultResponseObject.getData().getOverrideList().size() == 0);
        Assertions.assertTrue(alarmResultResponseObject.getData().getSkipList().size() == 1);
        Assertions.assertTrue(alarmResultResponseObject.getData().getSuccessList().size() == 1);
        list.removeAll(cleanList);
        cleanAlarm(Arrays.asList(getAlarmId(alarmMap)), list, alarmResultResponseObject.getData().getSuccessList().stream().map(e -> e.getAlarmId()).collect(Collectors.toList()));
        cleanData(Arrays.asList(getMetricsId(metricsMap)), metricsResultResponseObject.getData().getSuccessList().stream().map(e -> e.getMetricsId()).collect(Collectors.toList()));
    }


    @Test
    public void testApplyAlarm03() throws Exception {
        mockAllTplRelationList(Arrays.asList("a83a4151-3d0b-44a5-9a5f-b76f13a6eaed", "a68a39ea-81a6-402f-9732-1ef7f4480095", "38b90a20-660c-498d-9a16-90f8f6a2170f", "c80852bd-76db-47f3-a750-5c88c2164b65"));
        Map<String, String> metricsMap = initOriginalMetrics();
        ResponseObject<ApplyTplMetricsResult> metricsResultResponseObject = createBasicData(getMetricsName(metricsMap), "all");
        Assertions.assertTrue("success".equals(metricsResultResponseObject.getStatus()));
        Map<String, String> alarmMap = initAlarm(getMetricsId(metricsMap));

        String userId = "06e6bd8d-8591-4866-968b-ff197957793a";
        ThreadLocalStore.setUserInfoLocal(userId);
        ThreadLocalStore.setTenantInfoLocal("47046ca9-3df0-4b05-97f0-ccb99303ee9b");
        ApplyTplAlarmInput applyTplAlarmInput = new ApplyTplAlarmInput();
        applyTplAlarmInput.setIncludeTenants(null);
        applyTplAlarmInput.setExcludeTenants("asyncmq");
        applyTplAlarmInput.setDestTenantScope("all");
        applyTplAlarmInput.setTplTenantName("Cube_Template_Manager");
        applyTplAlarmInput.setTplName("infra");
        applyTplAlarmInput.setAlarm(getAlarmName(alarmMap));
        applyTplAlarmInput.setOverride(false);
        applyTplAlarmInput.setApplyInDB(true);
        Long t1 = System.currentTimeMillis();
        ResponseObject<ApplyTplAlarmResult> alarmResultResponseObject = templateService.applyAlarm(applyTplAlarmInput);
        System.out.println("cost-time={" + (System.currentTimeMillis() - t1) + "}");
        System.out.println(JsonUtils.toJsonString(alarmResultResponseObject));
        Assertions.assertTrue(alarmResultResponseObject.getData().getFailList().size() == 0);
        Assertions.assertTrue(alarmResultResponseObject.getData().getOverrideList().size() == 0);
        Assertions.assertTrue(alarmResultResponseObject.getData().getSkipList().size() == 0);
        Assertions.assertTrue(alarmResultResponseObject.getData().getSuccessList().size() == 3);

        cleanAlarm(Arrays.asList(getAlarmId(alarmMap)), alarmResultResponseObject.getData().getSuccessList().stream().map(e -> e.getAlarmId()).collect(Collectors.toList()));
        cleanData(Arrays.asList(getMetricsId(metricsMap)), metricsResultResponseObject.getData().getSuccessList().stream().map(e -> e.getMetricsId()).collect(Collectors.toList()));
    }


    @Test
    public void testApplyAlarm03_1() throws Exception {
        mockAllTplRelationList(Arrays.asList("a83a4151-3d0b-44a5-9a5f-b76f13a6eaed", "a68a39ea-81a6-402f-9732-1ef7f4480095", "c80852bd-76db-47f3-a750-5c88c2164b65"));
        Map<String, String> metricsMap = initOriginalMetrics();
        ResponseObject<ApplyTplMetricsResult> metricsResultResponseObject = createBasicData(getMetricsName(metricsMap), "all");
        Assertions.assertTrue("success".equals(metricsResultResponseObject.getStatus()));
        Map<String, String> alarmMap = initAlarm(getMetricsId(metricsMap));

        String userId = "06e6bd8d-8591-4866-968b-ff197957793a";
        ThreadLocalStore.setUserInfoLocal(userId);
        ThreadLocalStore.setTenantInfoLocal("47046ca9-3df0-4b05-97f0-ccb99303ee9b");
        ApplyTplAlarmInput applyTplAlarmInput = new ApplyTplAlarmInput();
        applyTplAlarmInput.setIncludeTenants("asyncfile,11111,asyncmq");
        applyTplAlarmInput.setExcludeTenants("asyncfile");
        applyTplAlarmInput.setDestTenantScope("all");
        applyTplAlarmInput.setTplTenantName("Cube_Template_Manager");
        applyTplAlarmInput.setTplName("infra");
        applyTplAlarmInput.setAlarm(getAlarmName(alarmMap));
        applyTplAlarmInput.setOverride(false);
        applyTplAlarmInput.setApplyInDB(true);
        Long t1 = System.currentTimeMillis();
        ResponseObject<ApplyTplAlarmResult> alarmResultResponseObject = templateService.applyAlarm(applyTplAlarmInput);
        System.out.println("cost-time={" + (System.currentTimeMillis() - t1) + "}");
        System.out.println(JsonUtils.toJsonString(alarmResultResponseObject));
        Assertions.assertTrue(alarmResultResponseObject.getData().getFailList().size() == 0);
        Assertions.assertTrue(alarmResultResponseObject.getData().getOverrideList().size() == 0);
        Assertions.assertTrue(alarmResultResponseObject.getData().getSkipList().size() == 0);
        Assertions.assertTrue(alarmResultResponseObject.getData().getSuccessList().size() == 2);

        cleanAlarm(Arrays.asList(getAlarmId(alarmMap)), alarmResultResponseObject.getData().getSuccessList().stream().map(e -> e.getAlarmId()).collect(Collectors.toList()));
        cleanData(Arrays.asList(getMetricsId(metricsMap)), metricsResultResponseObject.getData().getSuccessList().stream().map(e -> e.getMetricsId()).collect(Collectors.toList()));
    }


    @Test
    public void testApplyAlarm03_2() throws Exception {
        mockAllTplRelationList(Arrays.asList("c80852bd-76db-47f3-a750-5c88c2164b65", "a83a4151-3d0b-44a5-9a5f-b76f13a6eaed"));
        Map<String, String> metricsMap = initOriginalMetrics();
        ResponseObject<ApplyTplMetricsResult> metricsResultResponseObject = createBasicData(getMetricsName(metricsMap), "all");
        Assertions.assertTrue("success".equals(metricsResultResponseObject.getStatus()));
        Map<String, String> alarmMap = initAlarm(getMetricsId(metricsMap));

        String userId = "06e6bd8d-8591-4866-968b-ff197957793a";
        ThreadLocalStore.setUserInfoLocal(userId);
        ThreadLocalStore.setTenantInfoLocal("47046ca9-3df0-4b05-97f0-ccb99303ee9b");
        ApplyTplAlarmInput applyTplAlarmInput = new ApplyTplAlarmInput();
        applyTplAlarmInput.setIncludeTenants("asyncmq,asyncfile,error-11111");
        applyTplAlarmInput.setExcludeTenants("asyncmq,error-asyncfile");
        applyTplAlarmInput.setDestTenantScope("all");
        applyTplAlarmInput.setTplTenantName("Cube_Template_Manager");
        applyTplAlarmInput.setTplName("infra");
        applyTplAlarmInput.setAlarm(getAlarmName(alarmMap));
        applyTplAlarmInput.setOverride(false);
        applyTplAlarmInput.setApplyInDB(true);
        Long t1 = System.currentTimeMillis();
        ResponseObject<ApplyTplAlarmResult> alarmResultResponseObject = templateService.applyAlarm(applyTplAlarmInput);
        System.out.println("cost-time={" + (System.currentTimeMillis() - t1) + "}");
        System.out.println(JsonUtils.toJsonString(alarmResultResponseObject));
        Assertions.assertTrue(alarmResultResponseObject.getData().getFailList().size() == 2);
        Assertions.assertTrue(alarmResultResponseObject.getData().getOverrideList().size() == 0);
        Assertions.assertTrue(alarmResultResponseObject.getData().getSkipList().size() == 0);
        Assertions.assertTrue(alarmResultResponseObject.getData().getSuccessList().size() == 1);

        cleanAlarm(Arrays.asList(getAlarmId(alarmMap)), alarmResultResponseObject.getData().getSuccessList().stream().map(e -> e.getAlarmId()).collect(Collectors.toList()));
        cleanData(Arrays.asList(getMetricsId(metricsMap)), metricsResultResponseObject.getData().getSuccessList().stream().map(e -> e.getMetricsId()).collect(Collectors.toList()));
    }


    @Test
    public void testApplyAlarm03_3() throws Exception {
        mockAllTplRelationList(Arrays.asList("a83a4151-3d0b-44a5-9a5f-b76f13a6eaed", "a68a39ea-81a6-402f-9732-1ef7f4480095", "38b90a20-660c-498d-9a16-90f8f6a2170f", "c80852bd-76db-47f3-a750-5c88c2164b65"));
        Map<String, String> metricsMap = initOriginalMetrics();
        ResponseObject<ApplyTplMetricsResult> metricsResultResponseObject = createBasicData(getMetricsName(metricsMap), "all");
        Assertions.assertTrue("success".equals(metricsResultResponseObject.getStatus()));
        Map<String, String> alarmMap = initAlarm(getMetricsId(metricsMap));
        ResponseObject<ApplyTplAlarmResult> alarmResultResponseObject = createAlarmBasicData(getAlarmName(alarmMap), "all");
        List<String> list = alarmResultResponseObject.getData().getSuccessList().stream().map(e -> e.getAlarmId()).collect(Collectors.toList());
        List<String> cleanList = alarmResultResponseObject.getData().getSuccessList().stream().filter(e -> Arrays.asList("asyncfile").contains(e.getTenantName())).map(ee -> ee.getAlarmId()).collect(Collectors.toList());
        cleanAlarm(cleanList);
        String userId = "06e6bd8d-8591-4866-968b-ff197957793a";
        ThreadLocalStore.setUserInfoLocal(userId);
        ThreadLocalStore.setTenantInfoLocal("47046ca9-3df0-4b05-97f0-ccb99303ee9b");
        ApplyTplAlarmInput applyTplAlarmInput = new ApplyTplAlarmInput();
        mockAllTplRelationList(Arrays.asList("c80852bd-76db-47f3-a750-5c88c2164b65", "a83a4151-3d0b-44a5-9a5f-b76f13a6eaed", "a68a39ea-81a6-402f-9732-1ef7f4480095"));
        applyTplAlarmInput.setIncludeTenants("asyncmq,asyncfile,11111");
        applyTplAlarmInput.setExcludeTenants("asyncmq,error-asyncfile");
        applyTplAlarmInput.setDestTenantScope("all");
        applyTplAlarmInput.setTplTenantName("Cube_Template_Manager");
        applyTplAlarmInput.setTplName("infra");
        applyTplAlarmInput.setAlarm(getAlarmName(alarmMap));
        applyTplAlarmInput.setOverride(false);
        applyTplAlarmInput.setApplyInDB(true);
        Long t1 = System.currentTimeMillis();
        alarmResultResponseObject = templateService.applyAlarm(applyTplAlarmInput);
        System.out.println("cost-time={" + (System.currentTimeMillis() - t1) + "}");
        System.out.println(JsonUtils.toJsonString(alarmResultResponseObject));
        Assertions.assertTrue(alarmResultResponseObject.getData().getFailList().size() == 1);
        Assertions.assertTrue(alarmResultResponseObject.getData().getOverrideList().size() == 0);
        Assertions.assertTrue(alarmResultResponseObject.getData().getSkipList().size() == 1);
        Assertions.assertTrue(alarmResultResponseObject.getData().getSuccessList().size() == 1);
        list.removeAll(cleanList);
        cleanAlarm(Arrays.asList(getAlarmId(alarmMap)), list, alarmResultResponseObject.getData().getSuccessList().stream().map(e -> e.getAlarmId()).collect(Collectors.toList()));
        cleanData(Arrays.asList(getMetricsId(metricsMap)), metricsResultResponseObject.getData().getSuccessList().stream().map(e -> e.getMetricsId()).collect(Collectors.toList()));
    }


    @Test
    public void testApplyAlarm04() throws Exception {
        mockAllTplRelationList(Arrays.asList("a83a4151-3d0b-44a5-9a5f-b76f13a6eaed", "38b90a20-660c-498d-9a16-90f8f6a2170f", "a68a39ea-81a6-402f-9732-1ef7f4480095"));
        Map<String, String> metricsMap = initOriginalMetrics();
        ResponseObject<ApplyTplMetricsResult> metricsResultResponseObject = createBasicData(getMetricsName(metricsMap), "all");
        Assertions.assertTrue("success".equals(metricsResultResponseObject.getStatus()));
        Map<String, String> alarmMap = initAlarm(getMetricsId(metricsMap));

        String userId = "06e6bd8d-8591-4866-968b-ff197957793a";
        ThreadLocalStore.setUserInfoLocal(userId);
        ThreadLocalStore.setTenantInfoLocal("47046ca9-3df0-4b05-97f0-ccb99303ee9b");
        ApplyTplAlarmInput applyTplAlarmInput = new ApplyTplAlarmInput();
        applyTplAlarmInput.setIncludeTenants("asyncfile,test_syn_exist,11111");
        applyTplAlarmInput.setExcludeTenants("asyncfile");
        applyTplAlarmInput.setDestTenantScope("outer");
        applyTplAlarmInput.setTplTenantName("Cube_Template_Manager");
        applyTplAlarmInput.setTplName("infra");
        applyTplAlarmInput.setAlarm(getAlarmName(alarmMap));
        applyTplAlarmInput.setOverride(false);
        applyTplAlarmInput.setApplyInDB(true);
        Long t1 = System.currentTimeMillis();
        ResponseObject<ApplyTplAlarmResult> alarmResultResponseObject = templateService.applyAlarm(applyTplAlarmInput);
        System.out.println("cost-time={" + (System.currentTimeMillis() - t1) + "}");
        System.out.println(JsonUtils.toJsonString(alarmResultResponseObject));
        Assertions.assertTrue(alarmResultResponseObject.getData().getFailList().size() == 0);
        Assertions.assertTrue(alarmResultResponseObject.getData().getOverrideList().size() == 0);
        Assertions.assertTrue(alarmResultResponseObject.getData().getSkipList().size() == 0);
        Assertions.assertTrue(alarmResultResponseObject.getData().getSuccessList().size() == 1);

        cleanAlarm(Arrays.asList(getAlarmId(alarmMap)), alarmResultResponseObject.getData().getSuccessList().stream().map(e -> e.getAlarmId()).collect(Collectors.toList()));
        cleanData(Arrays.asList(getMetricsId(metricsMap)), metricsResultResponseObject.getData().getSuccessList().stream().map(e -> e.getMetricsId()).collect(Collectors.toList()));
    }


    @Test
    public void testApplyAlarm04_1() throws Exception {
        mockAllTplRelationList(Arrays.asList("a83a4151-3d0b-44a5-9a5f-b76f13a6eaed", "a68a39ea-81a6-402f-9732-1ef7f4480095", "38b90a20-660c-498d-9a16-90f8f6a2170f", "c80852bd-76db-47f3-a750-5c88c2164b65"));
        Map<String, String> metricsMap = initOriginalMetrics();
        ResponseObject<ApplyTplMetricsResult> metricsResultResponseObject = createBasicData(getMetricsName(metricsMap), "all");
        Assertions.assertTrue("success".equals(metricsResultResponseObject.getStatus()));
        Map<String, String> alarmMap = initAlarm(getMetricsId(metricsMap));
        ResponseObject<ApplyTplAlarmResult> alarmResultResponseObject = createAlarmBasicData(getAlarmName(alarmMap), "outer");
        List<String> list = alarmResultResponseObject.getData().getSuccessList().stream().map(e -> e.getAlarmId()).collect(Collectors.toList());
        List<String> cleanList = alarmResultResponseObject.getData().getSuccessList().stream().filter(e -> Arrays.asList("test_syn_exist").contains(e.getTenantName())).map(ee -> ee.getAlarmId()).collect(Collectors.toList());
        cleanAlarm(cleanList);
        String userId = "06e6bd8d-8591-4866-968b-ff197957793a";
        ThreadLocalStore.setUserInfoLocal(userId);
        ThreadLocalStore.setTenantInfoLocal("47046ca9-3df0-4b05-97f0-ccb99303ee9b");
        ApplyTplAlarmInput applyTplAlarmInput = new ApplyTplAlarmInput();
        mockAllTplRelationList(Arrays.asList("a83a4151-3d0b-44a5-9a5f-b76f13a6eaed", "38b90a20-660c-498d-9a16-90f8f6a2170f", "a68a39ea-81a6-402f-9732-1ef7f4480095"));
        applyTplAlarmInput.setIncludeTenants("asyncfile,test_syn_exist,11111");
        applyTplAlarmInput.setExcludeTenants("asyncfile");
        applyTplAlarmInput.setDestTenantScope("outer");
        applyTplAlarmInput.setTplTenantName("Cube_Template_Manager");
        applyTplAlarmInput.setTplName("infra");
        applyTplAlarmInput.setAlarm(getAlarmName(alarmMap));
        applyTplAlarmInput.setOverride(false);
        applyTplAlarmInput.setApplyInDB(true);
        Long t1 = System.currentTimeMillis();
        alarmResultResponseObject = templateService.applyAlarm(applyTplAlarmInput);
        System.out.println("cost-time={" + (System.currentTimeMillis() - t1) + "}");
        System.out.println(JsonUtils.toJsonString(alarmResultResponseObject));
        Assertions.assertTrue(alarmResultResponseObject.getData().getFailList().size() == 0);
        Assertions.assertTrue(alarmResultResponseObject.getData().getOverrideList().size() == 0);
        Assertions.assertTrue(alarmResultResponseObject.getData().getSkipList().size() == 0);
        Assertions.assertTrue(alarmResultResponseObject.getData().getSuccessList().size() == 1);

        list.removeAll(cleanList);
        cleanAlarm(list, Arrays.asList(getAlarmId(alarmMap)), alarmResultResponseObject.getData().getSuccessList().stream().map(e -> e.getAlarmId()).collect(Collectors.toList()));
        cleanData(Arrays.asList(getMetricsId(metricsMap)), metricsResultResponseObject.getData().getSuccessList().stream().map(e -> e.getMetricsId()).collect(Collectors.toList()));
    }


    @Test
    public void testApplyAlarm04_2() throws Exception {
        mockAllTplRelationList(Arrays.asList("a83a4151-3d0b-44a5-9a5f-b76f13a6eaed", "a68a39ea-81a6-402f-9732-1ef7f4480095", "38b90a20-660c-498d-9a16-90f8f6a2170f", "c80852bd-76db-47f3-a750-5c88c2164b65"));
        Map<String, String> metricsMap = initOriginalMetrics();
        ResponseObject<ApplyTplMetricsResult> metricsResultResponseObject = createBasicData(getMetricsName(metricsMap), "all");
        Assertions.assertTrue("success".equals(metricsResultResponseObject.getStatus()));
        Map<String, String> alarmMap = initAlarm(getMetricsId(metricsMap));
        ResponseObject<ApplyTplAlarmResult> alarmResultResponseObject = createAlarmBasicData(getAlarmName(alarmMap), "outer");
        List<String> list = alarmResultResponseObject.getData().getSuccessList().stream().map(e -> e.getAlarmId()).collect(Collectors.toList());
        List<String> cleanList = alarmResultResponseObject.getData().getSuccessList().stream().filter(e -> !Arrays.asList("test_syn_exist").contains(e.getTenantName())).map(ee -> ee.getAlarmId()).collect(Collectors.toList());
        cleanAlarm(cleanList);
        String userId = "06e6bd8d-8591-4866-968b-ff197957793a";
        ThreadLocalStore.setUserInfoLocal(userId);
        ThreadLocalStore.setTenantInfoLocal("47046ca9-3df0-4b05-97f0-ccb99303ee9b");
        ApplyTplAlarmInput applyTplAlarmInput = new ApplyTplAlarmInput();
        mockAllTplRelationList(Arrays.asList("a83a4151-3d0b-44a5-9a5f-b76f13a6eaed", "38b90a20-660c-498d-9a16-90f8f6a2170f", "a68a39ea-81a6-402f-9732-1ef7f4480095"));
        applyTplAlarmInput.setIncludeTenants("asyncfile,test_syn_exist,11111");
        applyTplAlarmInput.setExcludeTenants("asyncfile");
        applyTplAlarmInput.setDestTenantScope("outer");
        applyTplAlarmInput.setTplTenantName("Cube_Template_Manager");
        applyTplAlarmInput.setTplName("infra");
        applyTplAlarmInput.setAlarm(getAlarmName(alarmMap));
        applyTplAlarmInput.setOverride(false);
        applyTplAlarmInput.setApplyInDB(true);
        Long t1 = System.currentTimeMillis();
        alarmResultResponseObject = templateService.applyAlarm(applyTplAlarmInput);
        System.out.println("cost-time={" + (System.currentTimeMillis() - t1) + "}");
        System.out.println(JsonUtils.toJsonString(alarmResultResponseObject));
        Assertions.assertTrue(alarmResultResponseObject.getData().getFailList().size() == 0);
        Assertions.assertTrue(alarmResultResponseObject.getData().getOverrideList().size() == 0);
        Assertions.assertTrue(alarmResultResponseObject.getData().getSkipList().size() == 1);
        Assertions.assertTrue(alarmResultResponseObject.getData().getSuccessList().size() == 0);

        list.removeAll(cleanList);
        cleanAlarm(list, Arrays.asList(getAlarmId(alarmMap)), alarmResultResponseObject.getData().getSuccessList().stream().map(e -> e.getAlarmId()).collect(Collectors.toList()));
        cleanData(Arrays.asList(getMetricsId(metricsMap)), metricsResultResponseObject.getData().getSuccessList().stream().map(e -> e.getMetricsId()).collect(Collectors.toList()));
    }


    @Test
    public void testApplyAlarm05() throws Exception {
        mockAllTplRelationList(Arrays.asList("a83a4151-3d0b-44a5-9a5f-b76f13a6eaed", "38b90a20-660c-498d-9a16-90f8f6a2170f", "a68a39ea-81a6-402f-9732-1ef7f4480095"));
        Map<String, String> metricsMap = initOriginalMetrics();
        ResponseObject<ApplyTplMetricsResult> metricsResultResponseObject = createBasicData(getMetricsName(metricsMap), "all");
        Assertions.assertTrue("success".equals(metricsResultResponseObject.getStatus()));
        Map<String, String> alarmMap = initAlarm(getMetricsId(metricsMap));

        String userId = "06e6bd8d-8591-4866-968b-ff197957793a";
        ThreadLocalStore.setUserInfoLocal(userId);
        ThreadLocalStore.setTenantInfoLocal("47046ca9-3df0-4b05-97f0-ccb99303ee9b");
        ApplyTplAlarmInput applyTplAlarmInput = new ApplyTplAlarmInput();
        applyTplAlarmInput.setIncludeTenants("asyncfile,test_syn_exist,11111");
        applyTplAlarmInput.setExcludeTenants("asyncfile");
        applyTplAlarmInput.setDestTenantScope("inner");
        applyTplAlarmInput.setTplTenantName("Cube_Template_Manager");
        applyTplAlarmInput.setTplName("infra");
        applyTplAlarmInput.setAlarm(getAlarmName(alarmMap));
        applyTplAlarmInput.setOverride(false);
        applyTplAlarmInput.setApplyInDB(true);
        Long t1 = System.currentTimeMillis();
        ResponseObject<ApplyTplAlarmResult> alarmResultResponseObject = templateService.applyAlarm(applyTplAlarmInput);
        System.out.println("cost-time={" + (System.currentTimeMillis() - t1) + "}");
        System.out.println(JsonUtils.toJsonString(alarmResultResponseObject));
        Assertions.assertTrue(alarmResultResponseObject.getData().getFailList().size() == 0);
        Assertions.assertTrue(alarmResultResponseObject.getData().getOverrideList().size() == 0);
        Assertions.assertTrue(alarmResultResponseObject.getData().getSkipList().size() == 0);
        Assertions.assertTrue(alarmResultResponseObject.getData().getSuccessList().size() == 1);

        cleanAlarm(Arrays.asList(getAlarmId(alarmMap)), alarmResultResponseObject.getData().getSuccessList().stream().map(e -> e.getAlarmId()).collect(Collectors.toList()));
        cleanData(Arrays.asList(getMetricsId(metricsMap)), metricsResultResponseObject.getData().getSuccessList().stream().map(e -> e.getMetricsId()).collect(Collectors.toList()));

    }


    @Test
    public void testApplyAlarm05_1() throws Exception {
        mockAllTplRelationList(Arrays.asList("a83a4151-3d0b-44a5-9a5f-b76f13a6eaed", "a68a39ea-81a6-402f-9732-1ef7f4480095", "38b90a20-660c-498d-9a16-90f8f6a2170f", "c80852bd-76db-47f3-a750-5c88c2164b65"));
        Map<String, String> metricsMap = initOriginalMetrics();
        ResponseObject<ApplyTplMetricsResult> metricsResultResponseObject = createBasicData(getMetricsName(metricsMap), "all");
        Assertions.assertTrue("success".equals(metricsResultResponseObject.getStatus()));
        Map<String, String> alarmMap = initAlarm(getMetricsId(metricsMap));
        ResponseObject<ApplyTplAlarmResult> alarmResultResponseObject = createAlarmBasicData(getAlarmName(alarmMap), "inner");
        List<String> list = alarmResultResponseObject.getData().getSuccessList().stream().map(e -> e.getAlarmId()).collect(Collectors.toList());
        List<String> cleanList = alarmResultResponseObject.getData().getSuccessList().stream().filter(e -> Arrays.asList("11111").contains(e.getTenantName())).map(ee -> ee.getAlarmId()).collect(Collectors.toList());
        cleanAlarm(cleanList);
        String userId = "06e6bd8d-8591-4866-968b-ff197957793a";
        ThreadLocalStore.setUserInfoLocal(userId);
        ThreadLocalStore.setTenantInfoLocal("47046ca9-3df0-4b05-97f0-ccb99303ee9b");
        ApplyTplAlarmInput applyTplAlarmInput = new ApplyTplAlarmInput();
        mockAllTplRelationList(Arrays.asList("a83a4151-3d0b-44a5-9a5f-b76f13a6eaed", "38b90a20-660c-498d-9a16-90f8f6a2170f", "a68a39ea-81a6-402f-9732-1ef7f4480095"));
        applyTplAlarmInput.setIncludeTenants("asyncfile,test_syn_exist,11111");
        applyTplAlarmInput.setExcludeTenants("asyncfile");
        applyTplAlarmInput.setDestTenantScope("inner");
        applyTplAlarmInput.setTplTenantName("Cube_Template_Manager");
        applyTplAlarmInput.setTplName("infra");
        applyTplAlarmInput.setAlarm(getAlarmName(alarmMap));
        applyTplAlarmInput.setOverride(false);
        applyTplAlarmInput.setApplyInDB(true);
        Long t1 = System.currentTimeMillis();
        alarmResultResponseObject = templateService.applyAlarm(applyTplAlarmInput);
        System.out.println("cost-time={" + (System.currentTimeMillis() - t1) + "}");
        System.out.println(JsonUtils.toJsonString(alarmResultResponseObject));
        Assertions.assertTrue("success".equals(alarmResultResponseObject.getStatus()));
        Assertions.assertTrue(alarmResultResponseObject.getData().getFailList().size() == 0);
        Assertions.assertTrue(alarmResultResponseObject.getData().getOverrideList().size() == 0);
        Assertions.assertTrue(alarmResultResponseObject.getData().getSkipList().size() == 0);
        Assertions.assertTrue(alarmResultResponseObject.getData().getSuccessList().size() == 1);

        list.removeAll(cleanList);
        cleanAlarm(list, Arrays.asList(getAlarmId(alarmMap)), alarmResultResponseObject.getData().getSuccessList().stream().map(e -> e.getAlarmId()).collect(Collectors.toList()));
        cleanData(Arrays.asList(getMetricsId(metricsMap)), metricsResultResponseObject.getData().getSuccessList().stream().map(e -> e.getMetricsId()).collect(Collectors.toList()));
    }


    @Test
    public void testApplyAndRevokeTpl() throws Exception{
        String tenantName = "nwsAccount123_roy";
        String user="9e729792-1258-41fe-8e58-1b2c028b77a4";

        // 1 create tenant
        ThreadLocalStore.setUserInfoLocal(user);
        TenantInput tenantInput = new TenantInput();
        tenantInput.setName(tenantName);
        tenantInput.setType(1);
        tenantInput.setSource("nws");
        tenantInput.setCreateDefaultUser(true);
        tenantInput.setCreateDefaultChannel(true);
        tenantInput.setDefaultChannelName("alarmChannel1");
        tenantInput.setDefaultChannelTopic("alarm");
        tenantService.addTenant(tenantInput);

        TenantDO tenantDO = tenantService.getTenantByName(tenantName);
        Assertions.assertNotNull(tenantDO);


        // 2 apply tpl
        ApplyTplInput applyTplInput = new ApplyTplInput();
        applyTplInput.setDestTenantName("nwsAccount123_roy");
        applyTplInput.setTplTenantName("Cube_Template_Manager");
        applyTplInput.setTplName("nws_infra_tpl");
        applyTplInput.setAlarmChannelName("alarmChannel1");
        applyTplInput.setUnitTag("ds01");
        ResponseObject<String> responseObject = templateService.applyTpl(applyTplInput);
        Assertions.assertEquals("success", responseObject.getStatus());

        Assertions.assertNotNull(dataParserService.getDataParserByTenantIdAndName(tenantDO.getId(),"nws_infra_tpl"));

        // 3 revoke tpl
        RevokeTplInput revokeTplInput = new RevokeTplInput();
        revokeTplInput.setDestTenantName(tenantName);
        revokeTplInput.setTplTenantName("Cube_Template_Manager");
        revokeTplInput.setTplName("nws_infra_tpl");
        Assertions.assertEquals("success", templateService.revokeTpl(revokeTplInput).getStatus());
        Assertions.assertNull(dataParserService.getDataParserByTenantIdAndName(tenantDO.getId(),"nws_infra_tpl"));



        // 4 del tenant
        DelTenantInput delTenantInput = new DelTenantInput();
        delTenantInput.setName(tenantName);
        delTenantInput.setSource("nws");
        delTenantInput.setDelDefaultUser(true);
        delTenantInput.setDelDefaultChannel(true);
        delTenantInput.setDefaultChannelName("alarmChannel1");
        tenantService.delTenant(delTenantInput);

        Assertions.assertNull(tenantService.getTenantByName(tenantName));

    }














}

