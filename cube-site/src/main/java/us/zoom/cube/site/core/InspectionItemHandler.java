package us.zoom.cube.site.core;

import org.apache.commons.compress.utils.Lists;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import us.zoom.infra.dao.model.InspectionItemDO;
import us.zoom.infra.dao.service.InspectionItemDAO;

import java.util.List;
import java.util.Objects;

/**
 * @Description:
 * @author: <PERSON>
 * @date: 2023/04/11 10:07
 */
@Service
public class InspectionItemHandler {

    @Autowired
    private InspectionItemDAO inspectionItemDAO;

    public List<InspectionItemDO> selectItemByIds(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        return inspectionItemDAO.selectItemByIds(ids);
    }

    public int addInspectionItem(InspectionItemDO inspection) {
        return inspectionItemDAO.addInspectionItem(inspection);
    }

    public InspectionItemDO findByName(String name) {
        return inspectionItemDAO.findByName(name);
    }

    public int deleteById(String id) {
        return inspectionItemDAO.deleteById(id);
    }

}
