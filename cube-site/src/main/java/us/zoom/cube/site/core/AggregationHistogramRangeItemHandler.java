package us.zoom.cube.site.core;

import org.apache.ibatis.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import us.zoom.infra.dao.model.AggregationFunctionItemDO;
import us.zoom.infra.dao.model.AggregationHistogramRangeItemDO;
import us.zoom.infra.dao.model.MetricsAggregationRuleDO;
import us.zoom.infra.dao.service.AggregationFunctionItemDAO;
import us.zoom.infra.dao.service.AggregationHistogramRangeItemDAO;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2020/7/19 10:20 AM
 */
@Service
public class AggregationHistogramRangeItemHandler {

    @Autowired
    private AggregationHistogramRangeItemDAO aggregationHistogramRangeItemDAO;

    public int insertAggregationHistogramRange(AggregationHistogramRangeItemDO aggregationHistogramRangeItemDO){
        return aggregationHistogramRangeItemDAO.insertAggregationHistogramRange(aggregationHistogramRangeItemDO);
    }

    public int batchInsertAggregationHistogramRange(List<AggregationHistogramRangeItemDO> aggregationHistogramRangeItemDOList){
        if(CollectionUtils.isEmpty(aggregationHistogramRangeItemDOList)){
            return 0;
        }
        return aggregationHistogramRangeItemDAO.batchInsertAggregationHistogramRange(aggregationHistogramRangeItemDOList);
    }

    public AggregationHistogramRangeItemDO getById(String id){
        return aggregationHistogramRangeItemDAO.getById(id);
    }

    public void deleteById(String id){
        aggregationHistogramRangeItemDAO.deleteById(id);
    }

    public List<AggregationHistogramRangeItemDO> listAll(){
        return aggregationHistogramRangeItemDAO.listAll();
    }

    public void deleteByRuleId(String ruleId){
        aggregationHistogramRangeItemDAO.deleteByRuleId(ruleId);
    }

    public int updateAggregationHistogramRange(AggregationHistogramRangeItemDO aggregationHistogramRangeItemDO){
        return aggregationHistogramRangeItemDAO.updateAggregationHistogramRange(aggregationHistogramRangeItemDO);
    }

    public int batchUpdateAggregationHistogramRange(List<AggregationHistogramRangeItemDO> aggregationHistogramRangeItemDOList){
        if(CollectionUtils.isEmpty(aggregationHistogramRangeItemDOList)){
            return 0;
        }
        return aggregationHistogramRangeItemDAO.batchUpdateAggregationHistogramRange(aggregationHistogramRangeItemDOList);
    }


    public void deleteByRuleIdList(List<String> ruleIdList){
        if(!CollectionUtils.isEmpty(ruleIdList)) {
            aggregationHistogramRangeItemDAO.deleteByRuleIdList(ruleIdList);
        }
    }

    public void batchDeleteByIdList(List<String> idList) {
        if(!CollectionUtils.isEmpty(idList)) {
            aggregationHistogramRangeItemDAO.batchDeleteByIdList(idList);
        }
    }

}
