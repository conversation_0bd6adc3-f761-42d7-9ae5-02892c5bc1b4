package us.zoom.cube.site.lib;

import us.zoom.cube.site.api.web.StatusEnum;
import us.zoom.cube.site.infra.enums.WebCodeEnum;

public class ResponseObject<T> {
    String status;
    String operId;
    String message;
    String operCode;
    T data;

    public static <T> ResponseObject<T> fail(String msg) {
        return fail(null,msg,null);
    }


    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getOperId() {
        return operId;
    }

    public void setOperId(String operId) {
        this.operId = operId;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getOperCode() {
        return operCode;
    }

    public void setOperCode(String operCode) {
        this.operCode = operCode;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public ResponseObject() {
    }

    public ResponseObject(String status, String operId, String message, T data) {
        this.status = status;
        this.operId = operId;
        this.message = message;
        this.data = data;
    }

    public ResponseObject(String status, String operCode, T data) {
        this.status = status;
        this.operCode = operCode;
        this.data = data;
    }

    public ResponseObject(String status, String operId, String message, String operCode) {
        this.status = status;
        this.operId = operId;
        this.message = message;
        this.operCode = operCode;
    }

    public ResponseObject(String status, T data, String message, String operCode) {
        this.status = status;
        this.data = data;
        this.message = message;
        this.operCode = operCode;
    }

    public static <T> ResponseObject<T> success(String operId,T data){
        return new ResponseObject<>(StatusEnum.SUCCESS.getStatus(),operId,null,data);
    }

    public static <T> ResponseObject<T> success(T data){
        return new ResponseObject<>(StatusEnum.SUCCESS.getStatus(),null,null,data);
    }

    public static <T> ResponseObject<T> success(T data, String code, String operId) {
        return new ResponseObject<>(StatusEnum.SUCCESS.getStatus(), code, data);
    }

    public static <T> ResponseObject<T> fail(String operId,String message,T data){
        return new ResponseObject<>(StatusEnum.FAIL.getStatus(),operId,message,data);
    }

    public static <T> ResponseObject<T> failWithCode(String code,String message,T data){
        return new ResponseObject<>(StatusEnum.FAIL.getStatus(),data,message,code);
    }

    public static <T> ResponseObject<T> failWithCode(String operId,String message,String code){
        return new ResponseObject<>(StatusEnum.FAIL.getStatus(),operId,message,code);
    }

    public static <T> ResponseObject<T> failWithWebCode(WebCodeEnum webCode){
        return ResponseObject.failWithCode(null, webCode.getErrMsg(), webCode.getCode());
    }

    public static <T> ResponseObject<T> requireToLogin(){
        return ResponseObject.failWithCode(null,"Please Login!", WebCodeEnum.RequireLogin.getCode());
    }

    public static <T> ResponseObject<T> hasNoSuchTenant() {
       return ResponseObject.failWithCode(null,"you don't have such tenant !",WebCodeEnum.HasNoSuchTenant.getCode());
    }

    public static <T> ResponseObject<T> hasNoSuchAgg() {
        return  ResponseObject.failWithCode(null,"you don't have such agg !",WebCodeEnum.HasNoSuchAgg.getCode());
    }

    public static <T> ResponseObject<T> hasNoSuchMetrics() {
        return  ResponseObject.failWithCode(null,"you don't have such metrics !",WebCodeEnum.HasNoSuchMetrics.getCode());
    }

    public static <T> ResponseObject<T> hasNoSuchPattern() {
        return  ResponseObject.failWithCode(null,"you don't have such pattern !",WebCodeEnum.HasNoSuchPattern.getCode());
    }



    public static <T> ResponseObject<T> hasNoSuchAlarm() {
        return ResponseObject.failWithCode(null,"You do not have such alarm!",WebCodeEnum.HasNoSuchAlarm.getCode());
    }

    public static  <T> ResponseObject<T> hasNoSuchCollector() {
        return ResponseObject.failWithCode(null,"you do not have such collector",WebCodeEnum.HasNoSuchCollector.getCode());
    }

    public static  <T> ResponseObject<T> hasNoSuchTopic() {
        return      ResponseObject.failWithCode(null,"you can't edit this topic!",WebCodeEnum.HasNoSuchTopic.getCode());
    }


    public static  <T> ResponseObject<T> hasNoSuchDataParser() {
        return      ResponseObject.failWithCode(null,"you can't edit this dataparser!",WebCodeEnum.HasNoSuchDataParser.getCode());
    }

    public static  <T> ResponseObject<T> entityIsNull() {
        return      ResponseObject.failWithCode(null,"the entity should not be null!",WebCodeEnum.EntityIsNull.getCode());
    }

    public static  <T> ResponseObject<T> entityExited() {
        return      ResponseObject.failWithCode(null,"the entity has already existed!",WebCodeEnum.EntityExisted.getCode());
    }

    public static  <T> ResponseObject<T> entityNotExited() {
        return      ResponseObject.failWithCode(null,"the entity is not existed!",WebCodeEnum.EntityNotExisted.getCode());
    }

    public static  <T> ResponseObject<T> badRequestParam() {
        return      ResponseObject.failWithCode(null,"The request params are illegal!",WebCodeEnum.BadRequestParam.getCode());
    }

    public static  <T> ResponseObject<T> cantDefineMetrics() {
        return      ResponseObject.failWithCode(null,"You can't define metrics on this data type!",WebCodeEnum.CantDefineMetrics.getCode());
    }

    public static  <T> ResponseObject<T> cantCollectSample() {
        return      ResponseObject.failWithCode(null,"You can't collect sample log!",WebCodeEnum.CantCollectSample.getCode());
    }





    public static  <T> ResponseObject<T> cantDelPipelineHaveCollector() {
        return      ResponseObject.failWithCode(null,"you can't del such pipeline ,because it has collector!",WebCodeEnum.CantDelPipelineHaveCollector.getCode());

    }


}
