package us.zoom.cube.site.infra.utils;

import org.apache.commons.lang3.StringUtils;
import org.springframework.util.DigestUtils;

import java.io.UnsupportedEncodingException;

/**
 * <AUTHOR>
 */
public class Md5Utils {
    private static final String slat = "&%5123***&&%%$$#@";

    public static String getMD5(String input) {
        try {
            input = input +"/"+slat;
            return DigestUtils.md5DigestAsHex(input.getBytes("UTF-8"));
        } catch (UnsupportedEncodingException e) {
            return StringUtils.EMPTY;
        }

    }
}
