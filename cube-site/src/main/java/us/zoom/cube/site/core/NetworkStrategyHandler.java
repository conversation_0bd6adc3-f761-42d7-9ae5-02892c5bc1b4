package us.zoom.cube.site.core;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.site.lib.input.network.StrategyQuery;
import us.zoom.cube.site.lib.query.PageQuery;
import us.zoom.infra.dao.model.NetworkStrategyDO;
import us.zoom.infra.dao.service.NetworkStrategyDAO;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

@Component
public class NetworkStrategyHandler {
    @Autowired
    private NetworkStrategyDAO networkStrategyDAO;

    public int insert(NetworkStrategyDO networkStrategyDO) throws Exception{
        return networkStrategyDAO.insert(networkStrategyDO);
    }

    public NetworkStrategyDO findByKey(String sourceKey, String destKey) {
        return networkStrategyDAO.findByKey(sourceKey,destKey);
    }

    public NetworkStrategyDO selectById(String id) {
        return networkStrategyDAO.selectById(id);
    }

    public List<NetworkStrategyDO> findByParam(PageQuery<StrategyQuery> strategyPageQuery) {
        StrategyQuery query = strategyPageQuery.getQueryPara();
        return networkStrategyDAO.findByParam(query.getStrategyName(), query.getSourceDc(), query.getSourceService(), query.getSourceCluster(),query.getSourceIp(),query.getDestDc(),query.getDestService(),query.getDestCluster(),query.getDestIp(),(strategyPageQuery.getPageIndex()-1)*strategyPageQuery.getPageSize(),strategyPageQuery.getPageSize());
    }

    public int getCountByParam(StrategyQuery query) {
        return networkStrategyDAO.getCountByParam(query.getStrategyName(),query.getSourceDc(),query.getSourceService(), query.getSourceCluster(), query.getSourceIp(),query.getDestDc(),query.getDestService(),query.getDestCluster(),query.getDestIp());
    }

    public void updateStrategy(NetworkStrategyDO strategyDO){
       networkStrategyDAO.updateStrategy(strategyDO);
    }

    public List<NetworkStrategyDO> selectByIds(List<String> strategyList) {
        if(CollectionUtils.isEmpty(strategyList)){
            return new ArrayList<>();
        }
        return networkStrategyDAO.selectByIds(strategyList);
    }

    public List<NetworkStrategyDO> selectAll() {
        return networkStrategyDAO.selectAll();
    }
}
