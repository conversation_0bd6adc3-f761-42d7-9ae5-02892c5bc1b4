package us.zoom.cube.site.lib.input.dataparser;


import org.junit.jupiter.api.Test;
import us.zoom.cube.lib.common.AuthEncrTypeEnum;
import us.zoom.cube.lib.common.DataParserQueueType;
import us.zoom.cube.lib.common.KafkaZipTypeEnum;
import us.zoom.cube.site.infra.utils.IdUtils;
import us.zoom.cube.site.lib.output.dataparser.DataParserV2Out;
import us.zoom.infra.dao.model.KafkaQueueDO;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @Author: luis.zheng
 * @Date: 2021/8/25 2:37
 */
public class DataParserV2OutTest {
    @Test
    public void test() {
        DataParserV2Out out = new DataParserV2Out();
        List<KafkaQueueDO> kafkaQueueList = new ArrayList<>();
        kafkaQueueList.add(createKafkaQueueDO(5));
        kafkaQueueList.add(createKafkaQueueDO(3));
        kafkaQueueList.add(createKafkaQueueDO(2));
        kafkaQueueList.add(createKafkaQueueDO(4));
        kafkaQueueList.add(createKafkaQueueDO(1));
    }

    public KafkaQueueDO createKafkaQueueDO(int order) {
        KafkaQueueDO kafkaQueueDO = new KafkaQueueDO();
        kafkaQueueDO.setId(IdUtils.generateId());
        kafkaQueueDO.setTopics("topic1,topic2");
        kafkaQueueDO.setType(DataParserQueueType.input.name());
        kafkaQueueDO.setDataParserId(IdUtils.generateId());
        kafkaQueueDO.setUnitTagId(IdUtils.generateId());
        kafkaQueueDO.setGroupId("group.a.1");
        kafkaQueueDO.setKafkaClusterId(IdUtils.generateId());
        kafkaQueueDO.setProtocol(AuthEncrTypeEnum.saslSsl.name());
        kafkaQueueDO.setPollSize(200);
        kafkaQueueDO.setThreadCount(3);
        kafkaQueueDO.setSessionTimeout(200000);
        kafkaQueueDO.setCompressionType(KafkaZipTypeEnum.zstd.name());
        kafkaQueueDO.setOrder(order);
        kafkaQueueDO.setCreateTime(new Date());
        return kafkaQueueDO;
    }
}
