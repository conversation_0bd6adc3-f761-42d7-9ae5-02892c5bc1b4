package us.zoom.cube.site.biz;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import us.zoom.cube.site.CubeSiteApplication;
import us.zoom.cube.site.core.missDataMonitor.MissDataMonitorHandler;

@SpringBootTest(classes = CubeSiteApplication.class,webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)//springboot
public class MissDataMonitorHandlerTest extends  BaseTest {

    @Autowired
    MissDataMonitorHandler missDataMonitorHandler;

    @Test
    public void test(){
        missDataMonitorHandler.compareData(30,"MAIN");
    }
}
