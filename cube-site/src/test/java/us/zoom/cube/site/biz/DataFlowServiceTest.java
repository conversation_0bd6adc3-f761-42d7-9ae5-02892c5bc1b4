package us.zoom.cube.site.biz;

import com.google.common.collect.Sets;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import us.zoom.cube.site.core.*;
import us.zoom.cube.site.infra.utils.IdUtils;
import us.zoom.cube.site.lib.IdPara;
import us.zoom.cube.site.lib.ResponseObject;
import us.zoom.cube.site.lib.input.AreaCfgInput;
import us.zoom.cube.site.lib.input.DataFlowInput;
import us.zoom.cube.site.lib.input.InputTopicProfileAreaCfgs;
import us.zoom.cube.site.lib.input.dataflow.BatchUpdateDataFlowInput;
import us.zoom.cube.site.lib.output.PageResult;
import us.zoom.cube.site.lib.output.dataflow.DataFlowOut;
import us.zoom.cube.site.lib.query.DataFlowQuery;
import us.zoom.cube.site.lib.query.NameQuery;
import us.zoom.cube.site.lib.query.PageQuery;
import us.zoom.infra.dao.model.DataFlowDO;
import us.zoom.infra.dao.model.TaskQueueDO;
import us.zoom.infra.dao.model.UnitTagDO;
import us.zoom.infra.enums.CommonStatusEnum;
import us.zoom.cube.lib.utils.JsonUtils;

import java.util.*;

/**
 * @Author: <EMAIL>
 * @ModuleOwner: <EMAIL>
 * @Date: 03/01/2022 11:19
 * @Description:
 */
public class DataFlowServiceTest {

    DataFlowService dataFlowService;

    private DataFlowHandler dataFlowHandler;
    private AuthService authService;
    private UnitTagHandler unitTagHandler;
    private TaskQueueHandler taskQueueHandler;
    private TenantHandler tenantHandler;
    private UserHandler userHandler;

    @BeforeEach
    public void before() {
        unitTagHandler = Mockito.mock(UnitTagHandler.class);
        dataFlowHandler = Mockito.mock(DataFlowHandler.class);
        taskQueueHandler = Mockito.mock(TaskQueueHandler.class);
        authService = Mockito.mock(AuthService.class);
        tenantHandler = Mockito.mock(TenantHandler.class);
        userHandler = Mockito.mock(UserHandler.class);
        dataFlowService = new DataFlowService(authService, dataFlowHandler, unitTagHandler, taskQueueHandler, tenantHandler,userHandler);
    }

    String id = IdUtils.generateId();
    String hubUnitTagId = IdUtils.generateId();
    String alarmQueueId = IdUtils.generateId();
    String calculationQueueId = IdUtils.generateId();

    @Test
    public void testSearchDataFlow1() {
        PageQuery<DataFlowQuery> query = new PageQuery<>();
        query.setPageIndex(2);
        query.setPageSize(10);
        DataFlowQuery dataFlowQuery = new DataFlowQuery();
        dataFlowQuery.setName("data flow");
        dataFlowQuery.setStatus(CommonStatusEnum.enable.name());
        dataFlowQuery.setGroup("group");
        query.setQueryPara(dataFlowQuery);

        String hubUnitTagId1 = IdUtils.generateId();
        String hubUnitTagId2 = IdUtils.generateId();
        String alarmId1 = IdUtils.generateId();
        String alarmId2 = IdUtils.generateId();
        String flinkId1 = IdUtils.generateId();
        String flinkId2 = IdUtils.generateId();

        Mockito.doNothing().when(dataFlowHandler).addDataFlow(createDataFlowDO());
        DataFlowDO dataFlowDO1 = new DataFlowDO();
        dataFlowDO1.setHubUnitTagId(hubUnitTagId1);
        dataFlowDO1.setAlarmQueueId(alarmId1);
        dataFlowDO1.setInputTopicProfileArea(JsonUtils.toJsonString(new ArrayList<>()));
        dataFlowDO1.setCalculationQueueId(flinkId1);
        DataFlowDO dataFlowDO2 = new DataFlowDO();
        dataFlowDO2.setHubUnitTagId(hubUnitTagId2);
        dataFlowDO2.setAlarmQueueId(alarmId2);
        dataFlowDO2.setInputTopicProfileArea(JsonUtils.toJsonString(new ArrayList<>()));
        dataFlowDO2.setCalculationQueueId(flinkId2);
        DataFlowDO dataFlowDO3 = new DataFlowDO();
        dataFlowDO3.setHubUnitTagId(hubUnitTagId2);
        dataFlowDO3.setAlarmQueueId(alarmId2);
        dataFlowDO3.setInputTopicProfileArea(JsonUtils.toJsonString(new ArrayList<>()));
        dataFlowDO3.setCalculationQueueId(flinkId2);
        Mockito.when(dataFlowHandler.findByParam(dataFlowQuery.getName(), dataFlowQuery.getGroup(), dataFlowQuery.getStatus(), 2, 10)).thenReturn(Arrays.asList(dataFlowDO1, dataFlowDO2, dataFlowDO3));
        Mockito.when(dataFlowHandler.getCountByParam(dataFlowQuery.getName(), dataFlowQuery.getGroup(), dataFlowQuery.getStatus())).thenReturn(13);

        TaskQueueDO taskQueueDO = new TaskQueueDO();
        taskQueueDO.setId(alarmId1);
        taskQueueDO.setName("alarmId1");
        TaskQueueDO taskQueueDO1 = new TaskQueueDO();
        taskQueueDO1.setId(alarmId2);
        taskQueueDO1.setName("alarmId2");
        TaskQueueDO taskQueueDO2 = new TaskQueueDO();
        taskQueueDO2.setId(flinkId1);
        taskQueueDO2.setName("flinkId1");
        TaskQueueDO taskQueueDO3 = new TaskQueueDO();
        taskQueueDO3.setId(flinkId2);
        taskQueueDO3.setName("flinkId2");
        Mockito.when(taskQueueHandler.getByIds(Sets.newHashSet(alarmId1, alarmId2, flinkId1, flinkId2))).thenReturn(Arrays.asList(taskQueueDO, taskQueueDO1, taskQueueDO2, taskQueueDO3));

        UnitTagDO unitTagDO = new UnitTagDO();
        unitTagDO.setId(hubUnitTagId1);
        unitTagDO.setName("hubUnitTagId1");
        UnitTagDO unitTagDO2 = new UnitTagDO();
        unitTagDO2.setId(hubUnitTagId2);
        unitTagDO2.setName("hubUnitTagId2");
        Mockito.when(unitTagHandler.getByIds(Sets.newHashSet(hubUnitTagId1, hubUnitTagId2))).thenReturn(Arrays.asList(unitTagDO, unitTagDO2));


        ResponseObject<PageResult<DataFlowOut>> responseObject = dataFlowService.searchDataFlow(query);
        Assertions.assertEquals(responseObject.getData().getTotal(), 13);
        Assertions.assertEquals(responseObject.getData().getItems().size(), 3);
    }

    @Test
    public void testAddDataFlow1() {
        DataFlowInput input = createDataFlowInput();

        Mockito.doNothing().when(dataFlowHandler).addDataFlow(createDataFlowDO());
        Mockito.when(unitTagHandler.getById(hubUnitTagId)).thenReturn(new UnitTagDO());
        Mockito.when(unitTagHandler.getCountByName(hubUnitTagId)).thenReturn(0);
        Mockito.when(taskQueueHandler.getById(calculationQueueId)).thenReturn(new TaskQueueDO());
        dataFlowService.addDataFlow(input);
    }

    @Test
    public void testAddDataFlow2() {
        DataFlowInput input = createDataFlowInput();

        Mockito.doNothing().when(dataFlowHandler).addDataFlow(createDataFlowDO());
        Mockito.when(dataFlowHandler.getCountByName(input.getName())).thenReturn(1);
        Mockito.when(taskQueueHandler.getById(calculationQueueId)).thenReturn(new TaskQueueDO());

        Mockito.when(unitTagHandler.getById(hubUnitTagId)).thenReturn(null);
        IllegalArgumentException exception = Assertions.assertThrows(IllegalArgumentException.class, () ->
                dataFlowService.addDataFlow(input));
    }

    @Test
    public void testAddDataFlow3() {
        DataFlowInput input = createDataFlowInput();

        Mockito.doNothing().when(dataFlowHandler).addDataFlow(createDataFlowDO());
        Mockito.when(unitTagHandler.getById(hubUnitTagId)).thenReturn(new UnitTagDO());
        Mockito.when(taskQueueHandler.getById(calculationQueueId)).thenReturn(new TaskQueueDO());

        Mockito.when(dataFlowHandler.getCountByName(input.getName())).thenReturn(1);
        IllegalArgumentException exception = Assertions.assertThrows(IllegalArgumentException.class, () ->
                dataFlowService.addDataFlow(input));
        Assertions.assertEquals("name " + input.getName() + " is exist", exception.getMessage());
    }

    @Test
    public void testAddDataFlow4() {
        DataFlowInput input = createDataFlowInput();

        Mockito.doNothing().when(dataFlowHandler).addDataFlow(createDataFlowDO());
        Mockito.when(unitTagHandler.getById(hubUnitTagId)).thenReturn(new UnitTagDO());
        Mockito.when(taskQueueHandler.getById(calculationQueueId)).thenReturn(null);

        Mockito.when(dataFlowHandler.getCountByName(input.getName())).thenReturn(0);
        IllegalArgumentException exception = Assertions.assertThrows(IllegalArgumentException.class, () ->
                dataFlowService.addDataFlow(input));
    }

    @Test
    public void testEditDataFlow1() {
        DataFlowInput input = createDataFlowInput();

        Mockito.when(dataFlowHandler.getDataFlowById(input.getId())).thenReturn(new DataFlowDO());

        Mockito.doNothing().when(dataFlowHandler).addDataFlow(createDataFlowDO());
        Mockito.when(unitTagHandler.getById(hubUnitTagId)).thenReturn(new UnitTagDO());
        Mockito.when(taskQueueHandler.getById(calculationQueueId)).thenReturn(new TaskQueueDO());

        Mockito.when(dataFlowHandler.getCountByName(input.getName())).thenReturn(0);
        dataFlowService.editDataFlow(input);
    }

    @Test
    public void testEditDataFlow2() {
        DataFlowInput input = createDataFlowInput();

        Mockito.when(dataFlowHandler.getDataFlowById(input.getId())).thenReturn(null);

        Mockito.doNothing().when(dataFlowHandler).addDataFlow(createDataFlowDO());
        Mockito.when(unitTagHandler.getById(hubUnitTagId)).thenReturn(new UnitTagDO());
        Mockito.when(taskQueueHandler.getById(calculationQueueId)).thenReturn(new TaskQueueDO());

        Mockito.when(dataFlowHandler.getCountByName(input.getName())).thenReturn(0);
        IllegalArgumentException exception = Assertions.assertThrows(IllegalArgumentException.class, () ->
                dataFlowService.editDataFlow(input));
        Assertions.assertEquals("old dataFlow is null", exception.getMessage());
    }

    @Test
    public void testGetAndDeleteDataFlow() {
        IdPara idPara = new IdPara();
        idPara.setId(null);
        IllegalArgumentException exception = Assertions.assertThrows(IllegalArgumentException.class, () ->
                dataFlowService.getDataFlowById(idPara));
        Assertions.assertEquals("id not null", exception.getMessage());
    }

    @Test
    public void testGetAndDeleteDataFlowIsOK() {
        String id = IdUtils.generateId();
        IdPara idPara = new IdPara();
        idPara.setId(id);
        Mockito.when(dataFlowHandler.getDataFlowById(id)).thenReturn(new DataFlowDO());

        dataFlowService.getDataFlowById(idPara);
    }

    @Test
    public void testCheckDataFlowNameExist1() {
        NameQuery nameQuery = new NameQuery();
        String name = "nws";
        nameQuery.setName(name);
        nameQuery.setId(id);
        Mockito.when(dataFlowHandler.getCountByNameNotId(name, id)).thenReturn(2);
        ResponseObject object = dataFlowService.checkDataFlowNameExist(nameQuery);
        Assertions.assertEquals(true, object.getData());
    }

    @Test
    public void testCheckDataFlowNameExist2() {
        NameQuery nameQuery = new NameQuery();
        String name = "nws";
        nameQuery.setName(name);
        nameQuery.setId(id);
        Mockito.when(dataFlowHandler.getCountByNameNotId(name, id)).thenReturn(0);
        ResponseObject object = dataFlowService.checkDataFlowNameExist(nameQuery);
        Assertions.assertEquals(false, object.getData());
    }

    @Test
    public void testDeleteDataFlowById() {
        IdPara idPara = new IdPara();
        idPara.setId(id);
        Mockito.doNothing().when(dataFlowHandler).delDataFlowById(idPara.getId());
        Mockito.doNothing().when(tenantHandler).deleteDataFlowFormTenant(idPara.getId());

        ResponseObject object = dataFlowService.deleteDataFlowById(idPara);
        Assertions.assertEquals(true, object.getData());
    }

    private DataFlowInput createDataFlowInput() {
        DataFlowInput input = new DataFlowInput();
        input.setName("data flow name");
        input.setId(id);
        input.setStatus(CommonStatusEnum.enable.name());
        input.setGroup("nws");
        input.setScriptMethod("getAppName");
        input.setScriptContent("code script");
        AreaCfgInput areaCfgInput = new AreaCfgInput();
        areaCfgInput.setArea("cn");
        areaCfgInput.setGroupId("cn_group");
        areaCfgInput.setThreadCount(2);
        AreaCfgInput areaCfgInput1 = new AreaCfgInput();
        areaCfgInput1.setArea("us");
        areaCfgInput1.setGroupId("us_group");
        areaCfgInput1.setThreadCount(1);
        InputTopicProfileAreaCfgs inputTopicProfileAreaCfgs = new InputTopicProfileAreaCfgs();
        inputTopicProfileAreaCfgs.setInputTopicProfile("topicProfile");
        inputTopicProfileAreaCfgs.setAreaCfgs(Arrays.asList(areaCfgInput1, areaCfgInput));
        return input;
    }

    @Test
    public void testBatchUpdateDataFlow() {
        BatchUpdateDataFlowInput batchUpdateDataFlowInput = new BatchUpdateDataFlowInput();
        batchUpdateDataFlowInput.setGroup("");
        batchUpdateDataFlowInput.setIds(Arrays.asList("11"));
        Mockito.doNothing().when(dataFlowHandler).batchUpdateDataFlowGroup(batchUpdateDataFlowInput.getIds(), batchUpdateDataFlowInput.getGroup());
        IllegalArgumentException exception = Assertions.assertThrows(IllegalArgumentException.class, () ->
                dataFlowService.batchUpdateDataFlow(batchUpdateDataFlowInput));
        Assertions.assertEquals("group is not blank", exception.getMessage());
    }

    @Test
    public void testBatchUpdateDataFlow1() {
        BatchUpdateDataFlowInput batchUpdateDataFlowInput = new BatchUpdateDataFlowInput();
        batchUpdateDataFlowInput.setGroup("ss");
        batchUpdateDataFlowInput.setIds(Arrays.asList());
        Mockito.doNothing().when(dataFlowHandler).batchUpdateDataFlowGroup(batchUpdateDataFlowInput.getIds(), batchUpdateDataFlowInput.getGroup());
        IllegalArgumentException exception = Assertions.assertThrows(IllegalArgumentException.class, () ->
                dataFlowService.batchUpdateDataFlow(batchUpdateDataFlowInput));
        Assertions.assertEquals("ids is empty", exception.getMessage());
    }

    private DataFlowDO createDataFlowDO() {
        DataFlowDO input = new DataFlowDO();
        input.setName("data flow name");
        input.setId(id);
        input.setStatus(CommonStatusEnum.enable.name());
        // input.setInputTopicProfile("topicProfile");
        input.setHubUnitTagId(hubUnitTagId);
        input.setGroup("nws");
        input.setCalculationQueueId(calculationQueueId);
        return input;
    }
}
