package us.zoom.cube.site.biz;

import com.amazonaws.util.json.Jackson;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.Rollback;
import org.springframework.transaction.annotation.Transactional;
import us.zoom.cube.lib.integrations.Metrics;
import us.zoom.cube.lib.integrations.MetricsField;
import us.zoom.cube.lib.integrations.MetricsFieldTypeEnum;
import us.zoom.cube.site.biz.dashboard.ViewComponentService;
import us.zoom.cube.site.core.MetricsHandler;
import us.zoom.cube.site.infra.utils.IdUtils;
import us.zoom.cube.site.lib.ResponseObject;
import us.zoom.cube.site.lib.common.ValueText;
import us.zoom.cube.site.lib.input.MetricsFieldInput;
import us.zoom.cube.site.lib.input.MetricsInput;
import us.zoom.cube.site.lib.output.metric.ViewComponentDataOut;
import us.zoom.cube.site.lib.output.metric.ViewComponentOut;
import us.zoom.cube.site.lib.output.metric.ViewContainerOut;
import us.zoom.cube.site.lib.query.DashboardViewQuery;
import us.zoom.infra.dao.model.*;
import us.zoom.infra.dao.service.*;
import us.zoom.infra.enums.DashboardTypeEnum;
import us.zoom.infra.enums.MetricsTypeEnum;
import us.zoom.infra.enums.ViewComponentTargetTypeEnum;
import us.zoom.infra.enums.ViewComponentTypeEnum;
import us.zoom.infra.influx.service.InfluxService;
import us.zoom.infra.model.dashboard.BaseViewComponentCfg;
import us.zoom.infra.model.dashboard.HeatMapCfg;
import us.zoom.infra.utils.DateUtils;
import us.zoom.cube.lib.utils.JsonUtils;

import java.io.IOException;
import java.math.BigDecimal;
import java.text.ParseException;
import java.util.*;


public class VcDataServiceTest extends BaseTest  {


    @Autowired
    private DashboardDAO dashboardDAO;

    @Autowired
    private ViewContainerDAO viewContainerDAO;

    @Autowired
    private DashboardViewContainerRelaDAO dashboardViewContainerRelaDAO;

    @Autowired
    private ViewContainerComponentRelaDAO viewContainerComponentRelaDAO;

    @Autowired
    private MetricsService metricsService;

    @Autowired
    private ViewComponentDAO viewComponentDAO;

    @Autowired
    private InfluxService influxService;

    @Autowired
    private ViewComponentService viewComponentDataProvideService;

    @Autowired
    private DashboardService dashboardService;


    private String allHostAllMetric="all host all metric";
    private String hostAllMetric="host all metric";



    @Test
    @Transactional
    @Rollback
    public void     testInitInfo() throws Exception {
        String tenantId="WEB1";
//        String database="web";
//        String metricName="mysql";
        String metricId = "2";
        String mysql1="msyql_1";
        DashboardDO dashboardDO = addGetDashBoard(tenantId, DashboardTypeEnum.APP_ALL_METRIC.getCode(),allHostAllMetric);
        ViewContainerDO viewContainerDO = addGetViewContainer(tenantId, dashboardDO,mysql1,3,2);

        String view1=addViewComponent(tenantId, viewContainerDO, metricId,"whole bar",1, ViewComponentTypeEnum.bar, ViewComponentTargetTypeEnum.app.name(),1,dashboardDO.getId(),
                createBaseCfg("time",Arrays.asList("ARC"),true,15,false,null));
        String view4=addViewComponent(tenantId, viewContainerDO, metricId,"host scatter",2, ViewComponentTypeEnum.scatter,ViewComponentTargetTypeEnum.app.name(),1,dashboardDO.getId(),
                createBaseCfg("time",Arrays.asList("ARC"),false,null,true,"hostName"));

        DashboardViewQuery dashboardViewQuery=new DashboardViewQuery();
        dashboardViewQuery.setId(view1);
        dashboardViewQuery.setTenantId(tenantId);
        dashboardViewQuery.setDashboardType(DashboardTypeEnum.APP_ALL_METRIC.getCode());
        Map<String,String> tags=new HashMap<>();
        tags.put("clusterId","dev");
        tags.put("regionId","VA");
        dashboardViewQuery.setTags(tags);
        System.out.println("para is "+ JsonUtils.toJsonString(dashboardViewQuery));
        ResponseObject result= viewComponentDataProvideService.getComponentData(dashboardViewQuery);
        System.out.println("result is "+JsonUtils.toJsonString(result));


        dashboardViewQuery.setId(view4);
        dashboardViewQuery.setDashboardType(DashboardTypeEnum.APP_ALL_METRIC.getCode());
        System.out.println("para is "+JsonUtils.toJsonString(dashboardViewQuery));
        result= viewComponentDataProvideService.getComponentData(dashboardViewQuery);
        System.out.println("result is "+JsonUtils.toJsonString(result));

        String msyql2="mysql_2";
        String mysql3="msyql_3";
        dashboardDO = addGetDashBoard(tenantId, DashboardTypeEnum.HOST_ALL_METRIC.getCode(),hostAllMetric);
        addGetViewContainer(tenantId, dashboardDO,mysql3,3,2);
        viewContainerDO = addGetViewContainer(tenantId, dashboardDO,msyql2,1,2);
        String mysqlPerformanceTitle="mysql perfermance";
        String view2= addViewComponent(tenantId, viewContainerDO, metricId,mysqlPerformanceTitle,5, ViewComponentTypeEnum.line,ViewComponentTargetTypeEnum.host.name(),2,dashboardDO.getId(),
                createBaseCfg("time",Arrays.asList("ARC"),false,null,true,"hostName") );
        String mysqlRt="mysql rt";
        String view5= addViewComponent(tenantId, viewContainerDO, metricId,mysqlRt,1, ViewComponentTypeEnum.line,ViewComponentTargetTypeEnum.host.name(),2,dashboardDO.getId(),
                createBaseCfg("time",Arrays.asList("ARC"),false,null,true,"hostName") );

        dashboardViewQuery.setId(view2);
        tags.put("hostName","zoomwebsgdevweb002");
        dashboardViewQuery.setDashboardType(DashboardTypeEnum.HOST_ALL_METRIC.getCode());
        System.out.println("para is "+JsonUtils.toJsonString(dashboardViewQuery));

        result= viewComponentDataProvideService.getComponentData(dashboardViewQuery);
        System.out.println("result is "+JsonUtils.toJsonString(result));
        dashboardViewQuery.setDashboardType(DashboardTypeEnum.HOST_ALL_METRIC.getCode());
        result=dashboardService.listContainerComponentByDashType(dashboardViewQuery);
        List<ViewContainerOut> viewContainerOuts= (List<ViewContainerOut>) result.getData();

        boolean hasMysql=false;
        List<ViewComponentOut> mysql2Components=null;
        for(ViewContainerOut viewContainerOut:viewContainerOuts){
            if(viewContainerOut.getTitle().equals(msyql2)){
                hasMysql=true;
                mysql2Components=viewContainerOut.getViewComponentes();
            }
        }
        Assertions.assertTrue(hasMysql, "no msqyl viewContainer");



        int mysql2Index=-1,mysql1Index=-1;
        int index=0;
        for(ViewContainerOut viewContainerOut:viewContainerOuts){
            if(viewContainerOut.getTitle().equals(msyql2)){
                mysql2Index=index;
            }else if(viewContainerOut.getTitle().equals(mysql3)){
                mysql1Index=index;
            }
            index++;
        }
        Assertions.assertTrue(mysql2Index<mysql1Index, "dashboard container order error");



        Assertions.assertTrue(mysql2Components.size()==2, "add dashboard component size error");

        int viewComIndex1=-1,viewColIndex2=-1;
        index=0;
        for(ViewComponentOut components:mysql2Components){
            if(components.getTitle().equals(mysqlPerformanceTitle)){
                viewComIndex1=index;
            }else if(components.getTitle().equals(mysqlRt)){
                viewColIndex2=index;
            }
            index++;
        }

        Assertions.assertTrue(viewColIndex2<viewComIndex1, "dashboard component order error");


    }

    @Test
    public void testQuer(){
        DashboardViewQuery dashboardViewQuery=new DashboardViewQuery();
        try {
            dashboardViewQuery.setDashboardType(DashboardTypeEnum.APP_ALL_METRIC.getCode());
            dashboardViewQuery.setId("3011");
            dashboardViewQuery.setTenantId("bb6a0a17-0703-4e06-affc-92834a5d9079");
            Map<String,String> tags=new HashMap<>();
            tags.put("clusterId","devintegration");
            tags.put("regionId","VA");
            tags.put("hostName","zoomwebsgdevintweb001");
            dashboardViewQuery.setTags(tags);
            ResponseObject responseObject=  viewComponentDataProvideService.getComponentData(dashboardViewQuery);
        } catch (ParseException e) {
            e.printStackTrace();
        }
    }


    private BaseViewComponentCfg createBaseCfg(String x,List<String> y ,Boolean needCompress,Integer compressStep,boolean needGroupBy,String groupByField){
        BaseViewComponentCfg cfg=new BaseViewComponentCfg();
        cfg.setX(x);
        cfg.setY(y);
        cfg.setTimeFieldName("time");
        cfg.setNeedCompress(needCompress);
        cfg.setTimeStep(compressStep);
        cfg.setNeedGroupBy(needGroupBy);
        cfg.setGroupbyField(Arrays.asList(groupByField));
        return cfg;
    }
//
//    private BarCfg createBarCfg(String x,String y ,Boolean needCompress,Integer compressStep,boolean needGroupBy,String groupByField){
//        BarCfg cfg=new BarCfg();
//        cfg.setTimeStep(step);
//        cfg.setX(x);
//        cfg.setY(Arrays.asList(y));
//        cfg.setTimeFieldName("time");
//        return cfg;
//    }
//
//    private ScatterCfg createScatterfg(String x, String y,String lableField ){
//        ScatterCfg cfg=new ScatterCfg();
//        cfg.setX(x);
//        cfg.setY(y);
//        cfg.setLabelField(lableField);
//        cfg.setTimeFieldName("time");
//        return cfg;
//    }
//
//    private LineCfg createLineCfg(String x, List<String> y ){
//        LineCfg cfg=new LineCfg();
//        cfg.setX(x);
//        cfg.setY(y);
//        cfg.setTimeFieldName("time");
//        return cfg;
//    }

    private BaseViewComponentCfg createHeatMapCfg(String x, String y ,String lableField){
        HeatMapCfg cfg=new HeatMapCfg();
        cfg.setX(x);
        cfg.setY(Arrays.asList(y));
        cfg.setLabelField(lableField);
        cfg.setTimeFieldName("time");
        return cfg;
    }

    @Test
    @Transactional
    @Rollback
    public void testAll() throws Exception {
        String tenantId="WEB1";
        String database="web";
        String metricName="cpu";
//        InfluxDBUtil.createDefaultRetentionPolicy(database);

        DashboardDO dashboardDO = addGetDashBoard(tenantId, DashboardTypeEnum.APP_ALL_METRIC.getCode(),this.allHostAllMetric);

        ViewContainerDO viewContainerDO = addGetViewContainer(tenantId, dashboardDO,"cpu",1,3);
        String metricId = addCpuMetric(metricName,tenantId);
        //cpu
        String cpuUsComId=addViewComponent(tenantId, viewContainerDO, metricId,"us",1,  ViewComponentTypeEnum.bar,ViewComponentTargetTypeEnum.app.name(),1,dashboardDO.getId(),
                createBaseCfg("time",Arrays.asList("us"),true,15,false,null));
        //cpu
        String cpuIdelComId= addViewComponent(tenantId, viewContainerDO, metricId,"us",2, ViewComponentTypeEnum.heatmap,ViewComponentTargetTypeEnum.app.name(),2,dashboardDO.getId(),
                createHeatMapCfg("time","us","hostName"));

        Long begin=System.currentTimeMillis();
        mockData(database, metricName, metricId);
        Long cost=System.currentTimeMillis()-begin;

        DashboardViewQuery dashboardViewQuery=new DashboardViewQuery();
        Date end=new Date(DateUtils.addSeconds(DateUtils.getCurrentUTCDate(),1));
        dashboardViewQuery.setEnd(end);
        dashboardViewQuery.setBegin(new Date(DateUtils.addSeconds(dashboardViewQuery.getEnd(), (int) - (cost/1000)-20)));
        dashboardViewQuery.setId(cpuUsComId);
        dashboardViewQuery.setTenantId(tenantId);
        dashboardViewQuery.setDashboardType(DashboardTypeEnum.APP_ALL_METRIC.getCode());
        Map<String,String> tags=new HashMap<>();
        tags.put("clusterId","c0");
        tags.put("regionId","z0");
//        tags.put("hostName","h00");
        dashboardViewQuery.setTags(tags);
        System.out.println("add influx data cost "+cost+",para is "+JsonUtils.toJsonString(dashboardViewQuery));

        ResponseObject result= viewComponentDataProvideService.getComponentData(dashboardViewQuery);
        ViewComponentDataOut viewComponentDataOut= (ViewComponentDataOut) result.getData();
        System.out.println("query result is "+ JsonUtils.toJsonString(viewComponentDataOut));

        Map<String,List<Map<String,Object>>> datas= (Map<String, List<Map<String, Object>>>) viewComponentDataOut.getData();
        Assertions.assertTrue(datas.size()==loopSize);



        /*datas.get(ViewComponentService.DEFAULT_GROUPBY).forEach(item->{
            double avgUs=   avgUsMap.get((Long)item.get("time"));
            Assertions.assertTrue(avgUs==((BigDecimal)item.get("us")).doubleValue());
        });*/

        dashboardViewQuery.setId(cpuIdelComId);
        result= viewComponentDataProvideService.getComponentData(dashboardViewQuery);
        System.out.println("query result is "+ JsonUtils.toJsonString(result));

        viewComponentDataOut = (ViewComponentDataOut) result.getData();
        List<Map<String,Object>> heatmapData= (List<Map<String, Object>>) viewComponentDataOut.getData();

        Assertions.assertTrue(heatmapData.size()==hostSize);

        dashboardDO = addGetDashBoard(tenantId, DashboardTypeEnum.HOST_ALL_METRIC.getCode(),hostAllMetric);
        viewContainerDO = addGetViewContainer(tenantId, dashboardDO,"jvm",2,3);
        metricId = addCpuMetric("jvm",tenantId);
        addViewComponent(tenantId, viewContainerDO, metricId,"heap us",1,  ViewComponentTypeEnum.bar,ViewComponentTargetTypeEnum.host.name(),1,dashboardDO.getId(),
                createBaseCfg("time",Arrays.asList("us"),true,15,false,null) );
        addViewComponent(tenantId, viewContainerDO, metricId,"fgc count",3, ViewComponentTypeEnum.line,ViewComponentTargetTypeEnum.host.name(),1,dashboardDO.getId(),
                createBaseCfg("time",Arrays.asList("us","idel"),false,null,true,"hostName") );
        addViewComponent(tenantId, viewContainerDO, metricId,"ygc time",2,ViewComponentTypeEnum.line,ViewComponentTargetTypeEnum.host.name(),1,dashboardDO.getId(),
                createBaseCfg("time",Arrays.asList("us","idel"),false,null,true,"hostName") );
        result=  dashboardService.listContainerComponentByDashType(dashboardViewQuery);
        System.out.println("query result is "+ JsonUtils.toJsonString(result));

        List<ViewContainerOut> viewContainerOuts = (List<ViewContainerOut>) result.getData();

        Assertions.assertTrue(viewContainerOuts.size()>=2);
        for(ViewContainerOut viewContainerOut:viewContainerOuts){
            if(viewContainerOut.getTitle().equals("jvm")){
                Assertions.assertTrue( viewContainerOut.getColSize()==2);
                Assertions.assertTrue( viewContainerOut.getOrder()==2);
                Assertions.assertTrue( viewContainerOut.getComponentSize()==3);
                for(ViewComponentOut viewComponentOut:viewContainerOut.getViewComponentes()){
                    if(viewComponentOut.getTitle().equals("fgc count")){
                        Assertions.assertTrue(viewComponentOut.getType().equals(ViewComponentTypeEnum.line.name()));
                        Assertions.assertTrue(viewComponentOut.getOrder().equals(3));
                    }
                }
            }
        }

        dashboardViewQuery.setTargetType("host");
       List<ValueText> labels= (List<ValueText>) dashboardService.listComponentTitle(dashboardViewQuery).getData();
       Assertions.assertTrue(labels.size()>0);

    }

    int loopSize=1;
    int hostSize=20;
    Map<Long,Double> avgUsMap=new HashMap<>();
    private void mockData(String database, String metricName,   String metricId ) throws IOException, InterruptedException {
        BigDecimal avgUs=new BigDecimal(0);
        for(int i=0;i<loopSize;i++){
            long count=0;
            Long notTime=0l;
            while((count=(notTime=DateUtils.getCurrentUTCTime())%(15*1000))!=0){

            }

            System.out.println("ts "+notTime+" %="+count);
            Metrics metrics=new Metrics();
            Map<String,String> tags=new HashMap<>();
            tags.put("clusterId","c0");
            tags.put("regionId","z0");
            BigDecimal totalUs=new BigDecimal(0);
            for(int j=0;j<hostSize;j++){
                tags.put("hostName","h0"+j);
                metrics.setTags(tags);
                List<MetricsField> fields=new ArrayList<>();
                Double us=new Random().nextDouble()*100;
                Double idel=100d-us;
                totalUs=totalUs.add(BigDecimal.valueOf(us));
                fields.add(generateField("us", MetricsFieldTypeEnum.number,us));
                fields.add(generateField("idel",MetricsFieldTypeEnum.number,idel));
                metrics.setFields(fields);
                metrics.setMetricsId(metricId);
                metrics.setTs(DateUtils.getCurrentUTCTime());
                influxService.addInfluxData(database,metricName,metrics);
            }
             avgUs= totalUs.divide(new BigDecimal(hostSize), 3, BigDecimal.ROUND_HALF_UP);
             avgUsMap.put(notTime,avgUs.doubleValue());
             System.out.println("totalUs = "+totalUs+",avg value="+avgUs);
//            Thread.sleep(13*1000);

        }
    }

    private MetricsField generateField(String name,MetricsFieldTypeEnum type,Object value) {
        MetricsField field=new MetricsField();
        field.setFieldName(name);
        field.setFieldValue(value);
        field.setFieldType(type);
        return field;
    }

    private String addViewComponent(String tenantId, ViewContainerDO viewContainerDO, String metricId  , String title, Integer order, ViewComponentTypeEnum viewComponentType,
                                    String targetType,Integer colSize, String dashboardId, BaseViewComponentCfg cfg) {
        List<ViewComponentDO> viewComponentDOS=viewComponentDAO.listByContainerAndTitle(viewContainerDO.getId(),title);
        if(CollectionUtils.isNotEmpty(viewComponentDOS)){
            return viewComponentDOS.get(0).getId();
        }

        ViewComponentDO viewComponentDO=new ViewComponentDO();
        viewComponentDO.setId(IdUtils.generateId());
        viewComponentDO.setConfigs(Jackson.toJsonString(cfg));
        viewComponentDO.setMetricId(metricId);
        viewComponentDO.setTargetType(targetType);
        viewComponentDO.setTitle(title);
        viewComponentDO.setTenantId(tenantId);
        viewComponentDO.setType(viewComponentType.name());
        viewComponentDAO.add(viewComponentDO);
        ViewContainerComponentRelaDO viewContainerComponentRelaDO=new ViewContainerComponentRelaDO();
        viewContainerComponentRelaDO.setViewComponentId(viewComponentDO.getId());
        viewContainerComponentRelaDO.setViewContainerId(viewContainerDO.getId());
        viewContainerComponentRelaDO.setId(IdUtils.generateId());
        viewContainerComponentRelaDO.setOrder(order);
        viewContainerComponentRelaDO.setColSize(colSize);
        viewContainerComponentRelaDO.setDashboardId(dashboardId);
        viewContainerComponentRelaDAO.add(viewContainerComponentRelaDO);
        return viewComponentDO.getId();
    }

//    private LineCfg mockLineChart(ViewContainerDO viewContainerDO, String fieldName, ViewComponentDO viewComponentDO, List<String> yFields) {
//        LineCfg lineChartCfg=new LineCfg();
//        lineChartCfg.setXField("time");
//        lineChartCfg.setYFields(yFields);
//        lineChartCfg.setGroupByFieldName("hostName");
//        lineChartCfg.setTimeFieldName("time");
//        return  lineChartCfg;
//    }
//
//    private BarCfg mockCylinderDeployCfg(ViewContainerDO viewContainerDO, String fieldName, ViewComponentDO viewComponentDO, String yfields) {
//        BarCfg barCfg =new BarCfg();
//        barCfg.setTimeStep(15);
//        barCfg.setTimeFieldName("time");
//        barCfg.setDeployLabelName("hostName");
//        barCfg.setDeployValueField(fieldName);
//        barCfg.setCylinderXField("time");
//        barCfg.setCylinderYField(yfields);
//        return barCfg;
//    }


    @Autowired
    private MetricsHandler metricsHandler;
    private String  addCpuMetric(String metricName,String tenantId){
        MetricsDO metricsDO= metricsHandler.getMetricsByNameOfTenant( tenantId,metricName);
        if(metricsDO!=null ){
            return  metricsDO.getId();
        }
        MetricsInput metricsInput = new MetricsInput();
        metricsInput.setCollectorId("1");
        metricsInput.setId(IdUtils.generateId());
        metricsInput.setTenantId(tenantId);
        metricsInput.setUserId("82b4e894-edc7-47b8-ae99-58b6cbf58326");
        metricsInput.setMetricsName(metricName);
        metricsInput.setTagNames(Lists.newArrayList("regionId", "clusterId", "hostName"));
        List<MetricsFieldInput> metricsFieldInputList=new ArrayList<>();
        MetricsFieldInput metricsFieldInput=new MetricsFieldInput();
        metricsFieldInput.setFieldName("us");
        metricsFieldInput.setFieldType(1);
        metricsFieldInput.setId(IdUtils.generateId());
        metricsFieldInput.setMetricsId(metricsInput.getId());
        metricsFieldInputList.add(metricsFieldInput);
        metricsFieldInput=new MetricsFieldInput();
        metricsFieldInput.setFieldName("idel");
        metricsFieldInput.setFieldType(1);
        metricsFieldInput.setId(IdUtils.generateId());
        metricsFieldInput.setMetricsId(metricsInput.getId());
        metricsFieldInputList.add(metricsFieldInput);
        metricsInput.setMetricsFieldList(metricsFieldInputList);
        metricsInput.setType(MetricsTypeEnum.ORIGINAL.getValue());
        return metricsService.addMetrics(metricsInput).getData().toString();
    }

    private ViewContainerDO addGetViewContainer(String tenantId, DashboardDO dashboardDO,String title,Integer order,Integer colSize) {
        List<ViewContainerDO> viewContainerDOS=viewContainerDAO.listByDashboardAndTitle(dashboardDO.getId(),title);
        if(CollectionUtils.isNotEmpty(viewContainerDOS)){
            return viewContainerDOS.get(0);
        }
        ViewContainerDO viewContainerDO=new ViewContainerDO();
        viewContainerDO.setId(IdUtils.generateId());
        viewContainerDO.setColSize(colSize);
        viewContainerDO.setTitle(title);
        viewContainerDO.setTenantId(tenantId);
        viewContainerDAO.add(viewContainerDO);
        DashboardViewContainerRelaDO dashboardViewContainerRelaDO=new DashboardViewContainerRelaDO();
        dashboardViewContainerRelaDO.setDashboardId(dashboardDO.getId());
        dashboardViewContainerRelaDO.setViewContainerId(viewContainerDO.getId());
        dashboardViewContainerRelaDO.setId(IdUtils.generateId());
        dashboardViewContainerRelaDO.setOrder(order);
        dashboardViewContainerRelaDAO.add(dashboardViewContainerRelaDO);
        return viewContainerDO;
    }

    private DashboardDO addGetDashBoard(String tenantId,Integer type,String title) {
        List<DashboardDO> dashboardDOS= dashboardDAO.listByType(tenantId,type);
        DashboardDO dashboardDO=null;
        if(CollectionUtils.isEmpty(dashboardDOS)){
            dashboardDO=new DashboardDO();
            dashboardDO.setId(IdUtils.generateId());
            dashboardDO.setOrder(1);
            dashboardDO.setTenantId(tenantId);
            dashboardDO.setTitle(title);
            dashboardDO.setType(type);
            dashboardDAO.add(dashboardDO);
        }else {
            dashboardDO=dashboardDOS.get(0);
         }
        return dashboardDO;
    }
}
