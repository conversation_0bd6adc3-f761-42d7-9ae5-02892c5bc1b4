package us.zoom.cube.site.api.web.out;

import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import us.zoom.cube.site.biz.ZcpService;
import us.zoom.cube.site.lib.ResponseObject;
import us.zoom.cube.site.lib.dto.ZcpGetTopicDTO;
import us.zoom.cube.site.lib.input.alarm.silence.AlarmSilenceInput;
import us.zoom.cube.site.lib.input.alarm.silence.AlarmSilenceStatusByNameInput;
import us.zoom.cube.site.lib.query.*;
import us.zoom.cube.site.lib.query.zcp.*;

import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/out/zcp")
public class OutZcpController {

    @Autowired
    private ZcpService zcpService;

    @RequestMapping(value = "/getTypeName", method = RequestMethod.GET)
    public ResponseObject getTypeName() {

        return zcpService.getTypeName();
    }

    @RequestMapping(value = "/getTopics", method = RequestMethod.POST)
    public ResponseObject getTopic(@RequestBody ZcpGetTopicDTO zcpGetTopicDTO) {
        Map<String, Object> result = zcpService.getTopic(zcpGetTopicDTO);
        return ResponseObject.success(result);
    }

    @RequestMapping(value = "/getDashboards", method = RequestMethod.POST)
    public ResponseObject getDashboard(@RequestBody ZcpDashboardQuery zcpDashboardQuery) {
        return zcpService.getDashboard(zcpDashboardQuery);
    }

    @RequestMapping(value = "/syncAppOwners", method = RequestMethod.POST)
    public ResponseObject syncAppOwners(@RequestBody ZcpSyncAppOwnerQuery syncAppOwnerQuery) {
        return zcpService.syncAppOwner(syncAppOwnerQuery);
    }

    @RequestMapping(value = "/createCubeService", method = RequestMethod.POST)
    public ResponseObject createService(@RequestParam String appName, @RequestParam(required = false) String serviceKey) {
        return zcpService.createService(appName, serviceKey);
    }

    @RequestMapping(value = "/listChannelByService", method = RequestMethod.POST)
    public ResponseObject listChannelByService(@RequestBody ZcpRequestQuery zcpRequestQuery) {
        return zcpService.listChannelByService(zcpRequestQuery);
    }

    @RequestMapping(value = "/listAlarmByType", method = RequestMethod.POST)
    public ResponseObject listAlarmByType(@RequestBody ZcpRequestQuery zcpRequestQuery) {
        return zcpService.listAlarmByType(zcpRequestQuery);
    }

    @RequestMapping(value = "/createUserAndAssignmentPermission", method = RequestMethod.POST)
    public ResponseObject createUserAndAssignmentPermission(@RequestBody ZcpCreateUserAndSyncPremissionQuery zcpCreateUserAndSyncPremissionQuery) {
        return zcpService.createUserAndAssignmentPermission(zcpCreateUserAndSyncPremissionQuery);
    }

    @RequestMapping(value = "/zcpSync2cube", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject zcpSync2cube(@RequestBody ZcpSync2cubeQuery zcpSync2cubeQuery) throws Exception {
        return zcpService.zcpSync2cube(zcpSync2cubeQuery);
    }

    @RequestMapping(value = "/listServices", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject listServices(@RequestBody ZcpBaseQuery zcpBaseQuery) throws Exception {
        return zcpService.listServices(zcpBaseQuery.getUserName());
    }

    @RequestMapping(value = "/listTables", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject listTables(@RequestBody ZcpBaseQuery zcpBaseQuery) throws Exception {
        return zcpService.listTables(zcpBaseQuery);
    }

    @RequestMapping(value = "/listAlarmSilencePage", method = RequestMethod.POST)
    public ResponseObject listAlarmSilencePage(@RequestBody PageQuery<ZcpBaseQuery> zcpBaseQuery) {
        return zcpService.listAlarmSilencePage(zcpBaseQuery);
    }

    @RequestMapping(value = "/listAlarmSilenceDetail", method = RequestMethod.POST)
    public ResponseObject listAlarmSilenceDetail(@RequestBody AlarmSilence2ZcpQuery alarmSilence2ZcpQuery) {
        return zcpService.listAlarmSilenceDetail(alarmSilence2ZcpQuery);
    }

    @RequestMapping(value = "/listAlarmGroupPage", method = RequestMethod.POST)
    public ResponseObject listAlarmGroupPage(@RequestBody PageQuery<ZcpBaseQuery> zcpBaseQuery) {
        return zcpService.listAlarmGroupPage(zcpBaseQuery);
    }

    @RequestMapping(value = "/createAlarmSilence", method = RequestMethod.POST)
    public ResponseObject createAlarmSilence(@RequestBody ZcpAlarmSilenceInput zcpAlarmSilenceInput) {
        return zcpService.createAlarmSilence(zcpAlarmSilenceInput);
    }

    @RequestMapping(value = "/editAlarmSilence", method = RequestMethod.POST)
    public ResponseObject editAlarmSilence(@RequestBody ZcpAlarmSilenceInput zcpAlarmSilenceInput) {
        return zcpService.editAlarmSilence(zcpAlarmSilenceInput);
    }

    @RequestMapping(value = "/getTimezoneList", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject getTimezoneList(@Valid @RequestBody ZcpBaseQuery zcpBaseQuery) {
        return zcpService.getTimezoneList(zcpBaseQuery);
    }

    @RequestMapping(value = "/listAlarmGroupDetail", method = RequestMethod.POST)
    public ResponseObject listAlarmGroupDetail(@RequestBody AlarmGroup2ZcpQuery alarmGroup2ZcpQuery) {
        return zcpService.listAlarmGroupDetail(alarmGroup2ZcpQuery);
    }

    @RequestMapping(value = "/clusterBindTemplate", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject clusterBindTemplate(@RequestBody List<ClusterBindTemplateQuery> clusterBindTemplateQueryList) throws Exception {
        return zcpService.clusterBindTemplate(clusterBindTemplateQueryList);
    }

    @RequestMapping(value = "/exipreSilence", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject updateStatus(@Valid @RequestBody AlarmSilenceStatusByNameInput alarmSilenceStatusByNameInput) throws Exception {
        return zcpService.updateAlarmSilenceStatus(alarmSilenceStatusByNameInput);
    }

    @RequestMapping(value = "/listAlarmByServiceName", method = RequestMethod.POST)
    public ResponseObject listAlarmByServiceName(@RequestParam("userName") String userName,
                                                 @RequestParam("serviceName") String serviceName) {
        return zcpService.listAlarmByServiceName(userName, serviceName);
    }

    @RequestMapping(value = "/listTags", method = RequestMethod.POST)
    public ResponseObject listTags(@RequestBody AlarmGroupTagQuery alarmGroupTagQuery) {
        return zcpService.listTags(alarmGroupTagQuery);
    }

    @RequestMapping(value = "/callbackSilence", method = RequestMethod.POST)
    public ResponseObject callbackSilence(@RequestParam("userName") String userName,
                                          @RequestParam("serviceName") String serviceName,
                                          @RequestParam("silenceId") String silenceId) {
        try {
            return zcpService.callbackSilence(userName, serviceName, silenceId);
        } catch (Exception e) {
            log.error("callbackSilence error, userName = {}, serviceName = {}, silenceId = {}", userName, serviceName, silenceId, e);
            return ResponseObject.fail("Callback silence error: " + e.getMessage());
        }
    }

    @RequestMapping(value = "/createAlarmSilenceV2", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject addAlarmSilenceV2(@Valid @RequestBody AlarmSilenceInput alarmSilenceInput) {
        return zcpService.addAlarmSilenceV2(alarmSilenceInput);
    }

    @RequestMapping(value = "/updateAlarmSilenceV2", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject updateAlarmSilenceV2(@Valid @RequestBody AlarmSilenceInput alarmSilenceInput) {
        return zcpService.updateAlarmSilenceV2(alarmSilenceInput);
    }

    @RequestMapping(value = "/templateCheck", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject tempalteCheck(@Valid @RequestBody TemplateCheckQueck templateCheckQueck) {
        return zcpService.templateCheck(templateCheckQueck);
    }

    @RequestMapping(value = "/templateCheckV2", method = RequestMethod.POST)
    @ResponseBody
    public ResponseObject tempalteCheckV2(@Valid @RequestBody TemplateCheckQueck templateCheckQueck) {
        return zcpService.templateCheckV2(templateCheckQueck);
    }
}
