package us.zoom.cube.site.core.parser.process.core.processor;

import us.zoom.cube.lib.hub.ProcessorTypeEnum;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author: <EMAIL>
 * @ModuleOwner: <EMAIL>
 * @Date: 01/11/2023 17:04
 * @Description:
 */
public class ProcessorFactory {

    private static final Map<String, Processor> processorMap = new HashMap<>();

    static {
        processorMap.put(ProcessorTypeEnum.FilterProcessorCfg.name(), new FilterProcessor());
        processorMap.put(ProcessorTypeEnum.RemapperProcessorCfg.name(), new ReMapperProcessor());
        processorMap.put(ProcessorTypeEnum.GroovyProcessorCfg.name(), new GroovyProcessor());
        processorMap.put(ProcessorTypeEnum.TimestampProcessorCfg.name(), new TimestampProcessor());
        processorMap.put(ProcessorTypeEnum.IpProcessorCfg.name(), new IpProcessor());
        processorMap.put(ProcessorTypeEnum.SplitProcessorCfg.name(), new SplitProcessor());
        processorMap.put(ProcessorTypeEnum.ExpressionProcessorCfg.name(), new ExpressionProcessor());
        processorMap.put(ProcessorTypeEnum.CustomerLabelProcessorCfg.name(), new CustomerLabelProcessor());
    }

    public static Processor getInstance(String type) {
        return processorMap.get(type);
    }
}