package us.zoom.cube.site.lib;

import com.fasterxml.jackson.databind.node.ArrayNode;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * @author: <PERSON><PERSON> Ding
 * @date: 2025/4/14 13:17
 * @desc:
 */
@Data
public class MetricDefMetricsExportInfo {

    ArrayNode metricsArrayNodes;

    //<metricId, newMetricName>
    private Map<String, String> metricIdAndNewName = new HashMap<>();
    //<metricId, <oldTagName, newTagName>>
    private Map<String, Map<String, String>> metricTagMapping = new HashMap<>();
    //<metricId, <oldFieldName, newFieldName>>
    private Map<String, Map<String, String>> metricFieldMapping = new HashMap<>();

}
