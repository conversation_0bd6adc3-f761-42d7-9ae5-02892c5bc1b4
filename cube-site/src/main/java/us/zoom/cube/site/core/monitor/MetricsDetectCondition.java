package us.zoom.cube.site.core.monitor;

import java.util.Map;
import java.util.List;

/**
 * <AUTHOR>
 *  the detect contion for all metrics
 */
public class MetricsDetectCondition {

    private String service;
    private String metrics;
    private String field;
    private List<List<MetricsDetectConditionDetail>> conditions;
    private Integer detectPeriods;
    private Long lastAlarmTime;
    private Integer sendCount;

    private List<String> channelNameList;

    public String getService() {
        return service;
    }

    public void setService(String service) {
        this.service = service;
    }

    public String getMetrics() {
        return metrics;
    }

    public void setMetrics(String metrics) {
        this.metrics = metrics;
    }

    public List<List<MetricsDetectConditionDetail>> getConditions() {
        return conditions;
    }

    public void setConditions(List<List<MetricsDetectConditionDetail>> conditions) {
        this.conditions = conditions;
    }

    public Integer getDetectPeriods() {
        return detectPeriods;
    }

    public void setDetectPeriods(Integer detectPeriods) {
        this.detectPeriods = detectPeriods;
    }

    public String getField() {
        return field;
    }

    public void setField(String field) {
        this.field = field;
    }

    public Long getLastAlarmTime() {
        return lastAlarmTime;
    }

    public void setLastAlarmTime(Long lastAlarmTime) {
        this.lastAlarmTime = lastAlarmTime;
    }

    public Integer getSendCount() {
        return sendCount;
    }

    public void setSendCount(Integer sendCount) {
        this.sendCount = sendCount;
    }

    public List<String> getChannelNameList() {
        return channelNameList;
    }

    public void setChannelNameList(List<String> channelNameList) {
        this.channelNameList = channelNameList;
    }
}
