package us.zoom.infra.dao.service;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import us.zoom.cube.site.BaseSpringTest;
import us.zoom.cube.site.core.TplTenantRelaHandler;
import us.zoom.infra.dao.model.TplTenantRelaDO;

import java.util.Date;

public class TplTenantRelaDAOTest extends BaseSpringTest {
    @Autowired
    private TplTenantRelaDao tplTenantRelaDao;

    @Autowired
    private TplTenantRelaHandler tplTenantRelaHandler;

    @Test
    public void addAndDel(){
        TplTenantRelaDO tplTenantRelaDO = new TplTenantRelaDO();
        tplTenantRelaDO.setId("4e4c1300-264e-4071-9e0e-cf8c1c1e0cdd");
        tplTenantRelaDO.setStatus(1);
        tplTenantRelaDO.setTenantId("6f725b72-34c8-48dd-ae5c-ddd61588cc98");
        tplTenantRelaDO.setTplId("078a9527-2433-47b3-bbc0-8a92d91ff445");
        tplTenantRelaDO.setCreateTime(new Date());
        tplTenantRelaDO.setModifyTime(new Date());
        tplTenantRelaHandler.insertTplTenantRela(tplTenantRelaDO);

        TplTenantRelaDO do1 = tplTenantRelaHandler.queryByTplIdAndTenantId("078a9527-2433-47b3-bbc0-8a92d91ff445","6f725b72-34c8-48dd-ae5c-ddd61588cc98");
        Assertions.assertNotNull(do1);

        tplTenantRelaHandler.deleteById(do1.getId());

        TplTenantRelaDO do2 = tplTenantRelaHandler.queryByTplIdAndTenantId("078a9527-2433-47b3-bbc0-8a92d91ff445","6f725b72-34c8-48dd-ae5c-ddd61588cc98");
        Assertions.assertNull(do2);

    }

}
