//package us.zoom.cube.site.biz;
//
//import com.google.common.collect.Sets;
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.Test;
//import org.mockito.Mockito;
//import org.springframework.core.env.Environment;
//import us.zoom.cube.lib.common.DataParserPurposeEnum;
//import us.zoom.cube.site.core.*;
//import us.zoom.cube.site.infra.utils.IdUtils;
//import us.zoom.cube.site.lib.BasePara;
//import us.zoom.cube.site.lib.query.DataParserV2Query;
//import us.zoom.cube.site.lib.query.PageQuery;
//import us.zoom.infra.dao.model.DataParserDO;
//import us.zoom.infra.dao.model.KafkaQueueDO;
//import us.zoom.infra.dao.model.UnitTagDO;
//
//import java.util.ArrayList;
//import java.util.List;
//import java.util.Set;
//
///**
// * @Author: luis.zheng
// * @Date: 2021/8/23 1:36
// */
//public class DataParserServiceV2SearchTest {
//    private DataParserServiceV2 dataParserServiceV2;
//
//    private AuthService authService;
//    private UnitTagHandler unitTagHandler;
//    private DataParserHandler dataParserHandler;
//    private KafkaQueueHandler kafkaQueueHandler;
//    private DataParserService dataParserService;
//    private AsyncQueueHandler asyncQueueHandler;
//    private KafkaClusterHandler kafkaClusterHandler;
//    private AsyncMqQueueHandler asyncMqQueueHandler;
//    private AsyncQueueGroupService asyncQueueGroupService;
//    private DataParserPipelineHandler dataParserPipelineHandler;
//    private TaskQueueHandler taskQueueHandler;
//    private DataForwardHandler dataForwardHandler;
//    private Environment environment;
//    private TenantHandler tenantHandler;
//    private UserHandler userHandler;
//    private ServerHandler serverHandler;
//    private SysParaService sysParaService;
//    private CollectorHandler collectorHandler;
//    private AlarmDefinitionHandler alarmDefinitionHandler;
//    private MetricsHandler metricsHandler;
//    private SysParaHandler sysParaHandler;
//    private DataParserDeleteService dataParserDeleteService;
//
//    private String userId = "userId";
//
//    @BeforeEach
//    public void before() {
//        authService = Mockito.mock(AuthService.class);
////        Mockito.doNothing().when(authService).mustAdmin(userId);
//        Mockito.doNothing().when(authService).checkAuth(new BasePara());
//        unitTagHandler = Mockito.mock(UnitTagHandler.class);
//        dataParserHandler = Mockito.mock(DataParserHandler.class);
//        kafkaQueueHandler = Mockito.mock(KafkaQueueHandler.class);
//        dataParserService = Mockito.mock(DataParserService.class);
//        kafkaClusterHandler = Mockito.mock(KafkaClusterHandler.class);
//        asyncMqQueueHandler = Mockito.mock(AsyncMqQueueHandler.class);
//        asyncQueueGroupService = Mockito.mock(AsyncQueueGroupService.class);
//        dataParserPipelineHandler = Mockito.mock(DataParserPipelineHandler.class);
//        taskQueueHandler = Mockito.mock(TaskQueueHandler.class);
//        asyncQueueHandler = Mockito.mock(AsyncQueueHandler.class);
//        dataForwardHandler = Mockito.mock(DataForwardHandler.class);
//        environment = Mockito.mock(Environment.class);
//        tenantHandler = Mockito.mock(TenantHandler.class);
//        userHandler = Mockito.mock(UserHandler.class);
//        serverHandler = Mockito.mock(ServerHandler.class);
//        sysParaService = Mockito.mock(SysParaService.class);
//        dataParserServiceV2 = new DataParserServiceV2(authService, dataParserHandler, kafkaQueueHandler, dataParserService, asyncMqQueueHandler, dataParserPipelineHandler, environment, dataForwardHandler, tenantHandler,userHandler,sysParaService,
//                collectorHandler,alarmDefinitionHandler,metricsHandler, sysParaHandler, dataParserDeleteService);
//    }
//
//    @Test
//    public void testSearch() throws Exception {
//        PageQuery<DataParserV2Query> pageQuery = new PageQuery<>();
//        DataParserV2Query query = new DataParserV2Query();
//        query.setName("name");
//        query.setPurpose(DataParserPurposeEnum.handle.getValue());
//        query.setUnitTagId("unitTagsId");
//        query.setTenantId("tenantId");
//        pageQuery.setQueryPara(query);
////        pageQuery.setUserId(userId);
//
//        mockFindByParam(pageQuery);
//
//        mockGetByDataParserIds();
//
//        mockGetUnitTagIds();
//
//        dataParserServiceV2.searchDataParser(pageQuery);
//    }
//
//    @Test
//    public void testSearchUnitTagIsNull() throws Exception {
//        PageQuery<DataParserV2Query> pageQuery = new PageQuery<>();
//        DataParserV2Query query = new DataParserV2Query();
//        query.setName("name");
//        query.setPurpose(DataParserPurposeEnum.handle.getValue());
//        query.setUnitTagId("unitTagsId");
//        query.setTenantId("tenantId");
//        pageQuery.setQueryPara(query);
////        pageQuery.setUserId(userId);
//
//        mockFindByParam(pageQuery);
//
//        Mockito.when(kafkaQueueHandler.getBySourceIds(Sets.newHashSet(dataParserId1, dataParserId2, dataParserId3, dataParserId4))).thenReturn(null);
//
//        Mockito.when(unitTagHandler.getByIds(Sets.newHashSet(unitTagId1, unitTagId2, unitTagId3, unitTagId4, unitTagId5, unitTagId6, unitTagId7, unitTagId8))).thenReturn(null);
//
//        dataParserServiceV2.searchDataParser(pageQuery);
//    }
//
//    private void mockFindByParam(PageQuery<DataParserV2Query> pageQuery) throws Exception {
//        List<DataParserDO> dataParserDOS = new ArrayList<>();
//        DataParserDO dataParserDO1 = new DataParserDO();
//        dataParserDO1.setId(dataParserId1);
//        DataParserDO dataParserDO2 = new DataParserDO();
//        dataParserDO2.setId(dataParserId2);
//        DataParserDO dataParserDO3 = new DataParserDO();
//        dataParserDO3.setId(dataParserId3);
//        DataParserDO dataParserDO4 = new DataParserDO();
//        dataParserDO4.setId(dataParserId4);
//        dataParserDOS.add(dataParserDO1);
//        dataParserDOS.add(dataParserDO2);
//        dataParserDOS.add(dataParserDO3);
//        dataParserDOS.add(dataParserDO4);
//
//        Mockito.when(dataParserHandler.findByParam(pageQuery.getQueryPara().getTenantId(), pageQuery.getQueryPara().getName(), pageQuery.getQueryPara().getUnitTagId(),null, pageQuery.getPageIndex(), pageQuery.getPageSize())).thenReturn(dataParserDOS);
//        Mockito.when(dataParserHandler.getCountByParam(pageQuery.getQueryPara().getTenantId(), pageQuery.getQueryPara().getName(), pageQuery.getQueryPara().getUnitTagId(),null)).thenReturn(100);
//    }
//
//    private void mockGetByDataParserIds() {
//        List<KafkaQueueDO> kafkaQueueDOS = new ArrayList<>();
//        KafkaQueueDO kafkaQueueDO1 = new KafkaQueueDO();
//        kafkaQueueDO1.setUnitTagId(unitTagId1);
//        kafkaQueueDO1.setTopics("topicA,topicB");
//        KafkaQueueDO kafkaQueueDO2 = new KafkaQueueDO();
//        kafkaQueueDO2.setUnitTagId(unitTagId2);
//        kafkaQueueDO2.setTopics("topicC");
//        KafkaQueueDO kafkaQueueDO3 = new KafkaQueueDO();
//        kafkaQueueDO3.setUnitTagId(unitTagId3);
//        kafkaQueueDO3.setTopics("topicD");
//        KafkaQueueDO kafkaQueueDO4 = new KafkaQueueDO();
//        kafkaQueueDO4.setUnitTagId(unitTagId4);
//        kafkaQueueDO4.setTopics("topicE,topicF,topicG");
//        KafkaQueueDO kafkaQueueDO5 = new KafkaQueueDO();
//        kafkaQueueDO5.setUnitTagId(unitTagId5);
//        kafkaQueueDO5.setTopics("topic1,topic2");
//        KafkaQueueDO kafkaQueueDO6 = new KafkaQueueDO();
//        kafkaQueueDO6.setUnitTagId(unitTagId6);
//        kafkaQueueDO6.setTopics("topic3");
//        KafkaQueueDO kafkaQueueDO7 = new KafkaQueueDO();
//        kafkaQueueDO7.setUnitTagId(unitTagId7);
//        kafkaQueueDO7.setTopics("topic4");
//        KafkaQueueDO kafkaQueueDO8 = new KafkaQueueDO();
//        kafkaQueueDO8.setUnitTagId(unitTagId8);
//        kafkaQueueDO8.setTopics("topic5,topic6,topic7");
//        //dataParserId1
//        kafkaQueueDO1.setDataParserId(dataParserId1);
//        kafkaQueueDO2.setDataParserId(dataParserId1);
//        kafkaQueueDOS.add(kafkaQueueDO1);
//        kafkaQueueDOS.add(kafkaQueueDO2);
//        //dataParserId2
//        kafkaQueueDO3.setDataParserId(dataParserId2);
//        kafkaQueueDOS.add(kafkaQueueDO3);
//        //dataParserId3
//        kafkaQueueDO4.setDataParserId(dataParserId3);
//        kafkaQueueDOS.add(kafkaQueueDO4);
//        //dataParserId4
//        kafkaQueueDO5.setDataParserId(dataParserId4);
//        kafkaQueueDO6.setDataParserId(dataParserId4);
//        kafkaQueueDO7.setDataParserId(dataParserId4);
//        kafkaQueueDO8.setDataParserId(dataParserId4);
//        kafkaQueueDOS.add(kafkaQueueDO5);
//        kafkaQueueDOS.add(kafkaQueueDO6);
//        kafkaQueueDOS.add(kafkaQueueDO7);
//        kafkaQueueDOS.add(kafkaQueueDO8);
//        Mockito.when(kafkaQueueHandler.getBySourceIds(Sets.newHashSet(dataParserId1, dataParserId2, dataParserId3, dataParserId4))).thenReturn(kafkaQueueDOS);
//    }
//
//    private void mockGetUnitTagIds() {
//        List<UnitTagDO> unitTagList = new ArrayList<>();
//        UnitTagDO unitTagDO1 = new UnitTagDO();
//        unitTagDO1.setId(unitTagId1);
//        unitTagDO1.setName("name1");
//        UnitTagDO unitTagDO2 = new UnitTagDO();
//        unitTagDO2.setId(unitTagId2);
//        unitTagDO2.setName("name2");
//        UnitTagDO unitTagDO3 = new UnitTagDO();
//        unitTagDO3.setId(unitTagId3);
//        unitTagDO3.setName("name3");
//        UnitTagDO unitTagDO4 = new UnitTagDO();
//        unitTagDO4.setId(unitTagId4);
//        unitTagDO4.setName("name4");
//        UnitTagDO unitTagDO5 = new UnitTagDO();
//        unitTagDO5.setId(unitTagId5);
//        unitTagDO5.setName("name5");
//        UnitTagDO unitTagDO6 = new UnitTagDO();
//        unitTagDO6.setId(unitTagId6);
//        unitTagDO6.setName("name6");
//        UnitTagDO unitTagDO7 = new UnitTagDO();
//        unitTagDO7.setId(unitTagId7);
//        unitTagDO7.setName("name7");
//        UnitTagDO unitTagDO8 = new UnitTagDO();
//        unitTagDO8.setId(unitTagId8);
//        unitTagDO8.setName("name8");
//        unitTagList.add(unitTagDO1);
//        unitTagList.add(unitTagDO2);
//        unitTagList.add(unitTagDO3);
//        unitTagList.add(unitTagDO4);
//        unitTagList.add(unitTagDO5);
//        unitTagList.add(unitTagDO6);
//        unitTagList.add(unitTagDO7);
//        unitTagList.add(unitTagDO8);
//        Mockito.when(unitTagHandler.getByIds(Sets.newHashSet(unitTagId1, unitTagId2, unitTagId3, unitTagId4, unitTagId5, unitTagId6, unitTagId7, unitTagId8))).thenReturn(unitTagList);
//    }
//
//
//    @Test
//    public void test() throws Exception {
//        PageQuery<DataParserV2Query> pageQuery = new PageQuery<>();
//        DataParserV2Query query = new DataParserV2Query();
//        query.setName("name");
//        query.setTenantId("tenantId");
//        pageQuery.setQueryPara(query);
////        pageQuery.setUserId(userId);
//        Set<String> ids = null;
//        List<DataParserDO> dataParserDOS = new ArrayList<>();
//
//        Mockito.when(dataParserHandler.findByParam(query.getTenantId(), query.getName(), query.getUnitTagId(), ids, pageQuery.getPageIndex(), pageQuery.getPageSize())).thenReturn(dataParserDOS);
//        Mockito.when(dataParserHandler.getCountByParam(query.getTenantId(), query.getName(), query.getUnitTagId(),ids)).thenReturn(100);
//
//        dataParserServiceV2.searchDataParser(pageQuery);
//    }
//
//    String dataParserId1 = IdUtils.generateId();
//    String dataParserId2 = IdUtils.generateId();
//    String dataParserId3 = IdUtils.generateId();
//    String dataParserId4 = IdUtils.generateId();
//
//    String unitTagId1 = IdUtils.generateId();
//    String unitTagId2 = IdUtils.generateId();
//    String unitTagId3 = IdUtils.generateId();
//    String unitTagId4 = IdUtils.generateId();
//    String unitTagId5 = IdUtils.generateId();
//    String unitTagId6 = IdUtils.generateId();
//    String unitTagId7 = IdUtils.generateId();
//    String unitTagId8 = IdUtils.generateId();
//}
