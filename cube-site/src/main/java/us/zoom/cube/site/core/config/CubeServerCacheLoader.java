package us.zoom.cube.site.core.config;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import us.zoom.cube.lib.common.TaskTypeEnum;
import us.zoom.infra.dao.model.CubeServerDO;
import us.zoom.infra.dao.service.CubeServerDAO;
import us.zoom.infra.enums.CommonStatusEnum;
import us.zoom.infra.utils.IpUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-07-02 10:11
 */
@Component
@Slf4j
public class CubeServerCacheLoader implements CacheLoader {
    @Autowired
    private CubeServerDAO cubeServerDAO;

    @Getter
    private volatile CubeServerDO cubeServerDO;

    private static final int DEFAULT_PAGE_INDEX = 0;

    private static final int DEFAULT_PAGE_SIZE = 10000;
    @Override
    public void load() {

        try {
            cubeServerDO = new CubeServerDO();
            cubeServerDO.setIp(IpUtils.getLocalIP());
            cubeServerDO.setTaskType(TaskTypeEnum.site.name());
            cubeServerDO.setStatus(CommonStatusEnum.enable.getValue());
            List<CubeServerDO> cubeServers = cubeServerDAO.findByParam(cubeServerDO, DEFAULT_PAGE_INDEX, DEFAULT_PAGE_SIZE);
            if (CollectionUtils.isEmpty(cubeServers)) {
                return;
            }
            cubeServerDO = cubeServers.get(0);
        } catch (Exception e) {
            log.error("Load cube site server error! ", e);
        }
    }

    public String getEnv() {
        if (cubeServerDO != null) {
            return cubeServerDO.getEnv();
        }
        return "unknown";
    }

}
