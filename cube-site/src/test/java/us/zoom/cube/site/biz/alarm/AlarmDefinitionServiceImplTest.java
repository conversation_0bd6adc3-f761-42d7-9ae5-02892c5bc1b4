package us.zoom.cube.site.biz.alarm;

import com.zoom.op.monitor.dao.alarm.AlarmDefinitionDao;
import com.zoom.op.monitor.domain.alarm.AlarmDefinition;
import com.zoom.op.monitor.domain.alarm.AlarmLevel;
import com.zoom.op.monitor.domain.alarm.Channel;
import com.zoom.op.monitor.domain.alarm.Notification;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import us.zoom.cube.site.CubeSiteApplication;
import us.zoom.cube.site.api.web.StatusEnum;
import us.zoom.cube.site.biz.AuthService;
import us.zoom.cube.site.lib.IdPara;
import us.zoom.cube.site.lib.ResponseObject;
import us.zoom.infra.dao.model.MetricsDO;
import us.zoom.infra.dao.service.MetricsDAO;
import us.zoom.infra.dao.service.MetricsFieldDAO;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Optional;


import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@SpringBootTest(classes = CubeSiteApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class AlarmDefinitionServiceImplTest {

    @Autowired
    private AlarmDefinitionService test;

    @MockBean
    private AlarmDefinitionDao alarmDefinitionDao;

    @MockBean
    private MetricsFieldDAO metricsFieldDAO;

    @MockBean
    private MetricsDAO metricsDAO;

    @SuppressWarnings("unused")
    @MockBean
    private AuthService authService;

    @Test
    public void pageBy() {

        Pageable pageable = PageRequest.of(1, 10, Sort.by(Sort.Direction.ASC, "name"));

        Page<AlarmDefinition> page = Page.empty();

        Specification<AlarmDefinition> anySpecification = ArgumentMatchers.any();

        when(alarmDefinitionDao.findAll(anySpecification, any(Pageable.class))).thenReturn(page);

        ResponseObject<Page<AlarmDefinition>> result =
                test.pageBy("", AlarmLevel.FATAL, "29224fd6-2b8b-41c8-bba5-b58903f56d6f", "e41d4f2d-4a89-4662-9c18-c4d50e665843", "", "", pageable);

        assertEquals(page, result.getData());
    }

    @Test
    public void save() {

        AlarmDefinition mockAlarmDefinition = new AlarmDefinition(){{
            setName("mock-alarm-name");
            setTenantId("mock-tenant-id");
            setNotifications(Collections.singletonList(
                    new Notification(){{
                        setContent("");
                        setChannel(new Channel());
                    }}
            ));
        }};

        when(metricsDAO.getMetricsById(any())).thenReturn(new MetricsDO(){{
            setTagNames("");
        }});
        when(metricsFieldDAO.listFieldByMetricsIds(anyList())).thenReturn(new ArrayList<>());

        test.save(mockAlarmDefinition);

        verify(alarmDefinitionDao).save(mockAlarmDefinition);
    }

    @Test
    public void save_nameUsed_failed() {

        Optional<AlarmDefinition> exists = Optional.of(
                new AlarmDefinition() {{
                    setId("100L");
                    setName("mock-alarm-name");
                    setTenantId("mock-tenant-id");
                }});

        when(alarmDefinitionDao.findByNameAndTenantId("mock-alarm-name", "mock-tenant-id"))
                .thenReturn(exists);

        AlarmDefinition alarmDefinition = new AlarmDefinition() {{
            setName("mock-alarm-name");
            setTenantId("mock-tenant-id");
            setNotifications(Collections.singletonList(
                    new Notification(){{
                        setContent("");
                        setChannel(new Channel());
                    }}
            ));
        }};

        when(metricsDAO.getMetricsById(any())).thenReturn(new MetricsDO(){{
            setTagNames("");
        }});
        when(metricsFieldDAO.listFieldByMetricsIds(anyList())).thenReturn(new ArrayList<>());

        ResponseObject<AlarmDefinition> response = test.update(alarmDefinition);

        assertEquals(StatusEnum.FAIL.getStatus(), response.getStatus());
    }

    @Test
    public void update() {

        AlarmDefinition mockAlarmDefinition = new AlarmDefinition(){{
            setId("100L");
            setName("mock-alarm-name");
            setTenantId("mock-tenant-id");
            setNotifications(Collections.singletonList(
                    new Notification(){{
                        setContent("");
                        setChannel(new Channel());
                    }}
            ));
        }};

        when(metricsDAO.getMetricsById(any())).thenReturn(new MetricsDO(){{
            setTagNames("");
        }});
        when(metricsFieldDAO.listFieldByMetricsIds(anyList())).thenReturn(new ArrayList<>());

        test.update(mockAlarmDefinition);

        verify(alarmDefinitionDao).save(mockAlarmDefinition);
    }

    @Test
    public void update_nameUsed_failed() {

        Optional<AlarmDefinition> exists = Optional.of(
                new AlarmDefinition() {{
                    setId("100L");
                    setName("mock-alarm-name");
                    setTenantId("mock-tenant-id");
                }});

        when(alarmDefinitionDao.findByNameAndTenantId("mock-alarm-name", "mock-tenant-id"))
                .thenReturn(exists);

        AlarmDefinition mockAlarmDefinition = new AlarmDefinition() {{
            setId("101L");
            setName("mock-alarm-name");
            setTenantId("mock-tenant-id");
            setNotifications(Collections.singletonList(
                    new Notification(){{
                        setContent("");
                        setChannel(new Channel());
                    }}
            ));
        }};

        when(metricsDAO.getMetricsById(any())).thenReturn(new MetricsDO(){{
            setTagNames("");
        }});
        when(metricsFieldDAO.listFieldByMetricsIds(anyList())).thenReturn(new ArrayList<>());

        ResponseObject<AlarmDefinition> response = test.update(mockAlarmDefinition);

        assertEquals(StatusEnum.FAIL.getStatus(), response.getStatus());
    }

    @Test
    public void delete() {

        Optional<AlarmDefinition> mock = Optional.of(
                new AlarmDefinition() {{
                    setId("100L");
                    setName("mock-alarm-name");
                    setTenantId("mock-tenant-id");
                }}
        );

        when(alarmDefinitionDao.findById("100L")).thenReturn(mock);

        IdPara idPara = new IdPara();
        idPara.setId("100");
        test.delete(idPara);

        verify(alarmDefinitionDao).delete(mock.get());
    }

    @Test
    public void getById() {

        Optional<AlarmDefinition> mock = Optional.of(
                new AlarmDefinition() {{
                    setId("100L");
                    setName("mock-alarm-name");
                }}
        );

        when(alarmDefinitionDao.findById("100L")).thenReturn(mock);

        ResponseObject<AlarmDefinition> response = test.getById("100L");

        assertEquals(mock.get(), response.getData());
    }

    @Test
    public void getById_notFound_failed() {

        when(alarmDefinitionDao.findById("100L")).thenReturn(Optional.empty());

        ResponseObject<AlarmDefinition> response = test.getById("100L");

        assertEquals(StatusEnum.FAIL.getStatus(), response.getStatus());
    }
}