package us.zoom.cube.site.lib.monitor;


import lombok.Builder;
import lombok.Data;
import us.zoom.cube.sdk.annotation.CubeMonitorLog;
import us.zoom.cube.sdk.annotation.Field;
import us.zoom.cube.sdk.annotation.Tag;

@Data
@Builder
@CubeMonitorLog(measure = "rcaTicketMetric")
public class RcaTicketMetric {
    /**
     * ZFG GO/DogFood Commercial
     */
    @Tag
    private String environment;
    @Tag
    private String productType;
    @Tag
    private String serviceOfProductImpact;
    @Tag
    private String failureType;
    @Tag
    private String rootCause;
    @Tag
    private String rootCauseClass;
    @Tag
    private String ownerTeam;
    @Tag
    private String sevLevel;
    /**
     * Dev、DevOps、IT
     */
    @Tag
    private String rcaOwner;
    @Tag
    private String zoomModule;
    /**
     * True/False
     */
    @Tag
    private String serviceEngineerTicket;
    @Tag
    private String status;
    @Field
    private String summary;
    @Field
    private String assignee;
    @Field
    private String assigneeManager;
    @Field
    private String manager;
    @Field
    private String managerPlus;
    @Field
    private String reporter;
    @Field
    private String rcaKey;
    @Tag
    private String rcaReviewed;
    @Field
    private String nocInternalReviewer;
    @Field
    private String impact;
    @Field
    private String dueDate;
    @Field
    private String startDate;
    @Field
    private String rcaRegionReviewed;
    @Tag
    private String labels;
    @Field
    private String rcaWikiLink;
    @Field
    private String created;

}
