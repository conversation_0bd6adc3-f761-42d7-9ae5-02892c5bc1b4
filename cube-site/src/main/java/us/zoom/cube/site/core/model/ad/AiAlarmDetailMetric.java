package us.zoom.cube.site.core.model.ad;

import lombok.Data;
import us.zoom.cube.sdk.annotation.CubeMonitorLog;
import us.zoom.cube.sdk.annotation.Field;
import us.zoom.cube.sdk.annotation.Tag;

/**
 * <AUTHOR>
 * @date 2024-07-10 13:26
 */
@Data
@CubeMonitorLog(measure = "AiAlarmDetailMetric")
public class AiAlarmDetailMetric {
    @Tag
    private String host;
    @Tag
    private String ip;
    @Tag
    private String serviceName;
    @Field
    private String modelName;
    @Field
    private String metricsName;
    @Field
    private Integer enabled;
    @Field
    private String alarmName;
    @Field
    private String alarmRuleId;
    @Field
    private Integer level;
    @Field
    private String threshold;
    @Field
    private String fieldName;
    @Field
    private Integer periodInMinutes;
    @Field
    private Integer timesInPeriod;

    public AiAlarmDetailMetric(String host, String ip, String serviceName, String modelName, String metricsName,
                               Integer enabled, String alarmName, String alarmRuleId, Integer level, String threshold, String fieldName,
                               Integer periodInMinutes, Integer timesInPeriod) {
        this.host = host;
        this.ip = ip;
        this.serviceName = serviceName;
        this.modelName = modelName;
        this.metricsName = metricsName;
        this.enabled = enabled;
        this.alarmName = alarmName;
        this.alarmRuleId = alarmRuleId;
        this.level = level;
        this.threshold = threshold;
        this.fieldName = fieldName;
        this.periodInMinutes = periodInMinutes;
        this.timesInPeriod = timesInPeriod;
    }
}
