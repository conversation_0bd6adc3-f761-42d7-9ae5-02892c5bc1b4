package us.zoom.cube.site.core;

import com.google.common.collect.Lists;
import com.zoom.op.monitor.domain.alarm.AlarmDefinition;
import com.zoom.op.monitor.domain.alarm.ConditionType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RestController;
import us.zoom.cube.lib.utils.expression.CustomAviatorUtils;
import us.zoom.cube.site.lib.output.alarm.FieldAlarmRelationOut;
import us.zoom.cube.site.lib.output.alarm.SimpleAlarm;
import us.zoom.infra.dao.model.*;
import us.zoom.infra.dao.service.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RestController
public class AlarmHandler {
    @Autowired
    private AlarmDAO alarmDAO;

    @Autowired
    private AlarmRuleDAO alarmRuleDAO;

    @Autowired
    private AlarmRuleRelationDAO alarmRuleRelationDAO;

    @Autowired
    private MetricsAlarmDAO metricsAlarmDAO;

    @Autowired
    private AlarmImRelationDAO alarmImRelationDAO;

    @Autowired
    private AlarmDefinitonRuleConditionDAO ruleConditionDAO;

    @Autowired
    private MetricsDAO metricsDAO;


    public List<AlarmDO> findByAlarmNameLike(String alarmName, String tenantId,int pageIndex, int pageSize)throws Exception{
        return alarmDAO.findByAlarmNameLike(alarmName,tenantId,pageSize*(pageIndex-1),pageSize);
    }

    @Transactional(rollbackFor = Exception.class)
    public void addAlarm(AlarmDO alarmDO, MetricsAlarmDO metricsAlarmDO, List<AlarmRuleDO> alarmRuleDOList, List<AlarmRuleRelationDO> alarmRuleRelationDOList,
                         AlarmImRelationDO alarmImRelationDO)throws Exception{
        alarmDAO.insertAlarm(alarmDO);
        metricsAlarmDAO.insertMetricsAlarm(metricsAlarmDO);
        for(int i=0;i<alarmRuleDOList.size();i++) {
            alarmRuleDAO.insertAlarmRule(alarmRuleDOList.get(i));
            alarmRuleRelationDAO.insertAlarmRuleRelation(alarmRuleRelationDOList.get(i));
        }
        if (alarmImRelationDO != null) {
            alarmImRelationDAO.add(alarmImRelationDO);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void editAlarmById(AlarmDO alarmDO, MetricsAlarmDO metricsAlarmDO, List<AlarmRuleDO> alarmRuleDOList, List<AlarmRuleRelationDO> alarmRuleRelationDOList,
                              AlarmImRelationDO alarmImRelationDO)throws Exception{
        updateAlarm(alarmDO);
        metricsAlarmDAO.updateMetricsIdByAlarmId(alarmDO.getId(), metricsAlarmDO.getMetricsId());
        List<String> alarmRuleIds = alarmRuleRelationDAO.selectAlarmRuleIdsByAlarmId(alarmDO.getId());
        alarmRuleIds.forEach(alarmRuleId -> alarmRuleDAO.delAlarmRuleById(alarmRuleId));
        alarmRuleRelationDAO.delRelaByAlarmId(alarmDO.getId());
        alarmImRelationDAO.delRelationByAlarmId(alarmDO.getId());
        for(int i=0;i<alarmRuleDOList.size();i++) {
            alarmRuleDAO.insertAlarmRule(alarmRuleDOList.get(i));
            alarmRuleRelationDAO.insertAlarmRuleRelation(alarmRuleRelationDOList.get(i));
        }
        if (alarmImRelationDO != null) {
            alarmImRelationDAO.add(alarmImRelationDO);
        }
    }

    private void updateAlarm(AlarmDO alarmDO) throws Exception {
        alarmDAO.updateAlarmById(alarmDO);
    }


    public int getCountByAlarmNameLike(String alarmName,String tenantId) {
        return  alarmDAO.getCountByAlarmNameLike(alarmName,tenantId);
    }

    public List<AlarmDO> listAlarmsByMetricsId(String metricsId) {
        return metricsAlarmDAO.listAlarmsByMetricsId(metricsId);
    }

    public List<AlarmDefinition> listAlarmsByMetricsIdList(List<String> metricsIdList) {
        if (CollectionUtils.isEmpty(metricsIdList)) {
            return Collections.emptyList();
        }
        return metricsAlarmDAO.listAlarmsByMetricsIdList(metricsIdList);
    }

    public List<AlarmDO> listAlarmIdNames(String metricsId) {
        return metricsAlarmDAO.listAlarmIdNames(metricsId);
    }

    public List<AlarmDO> listAlarmIdNames(List<String> metricsId) {
        return metricsAlarmDAO.listAlarmIdNamesBatch(metricsId);
    }

    public List<AlarmRuleDO> listRuleByAlarmIds(List<String> alarmIds) {
        return alarmRuleDAO.listRuleByAlarmIds(alarmIds);
    }

    public AlarmDO getAlarmById(String alarmId) {
        return alarmDAO.getAlarmById(alarmId);
    }

    public String getMetricsIdByAlarmId(String alarmId) {
       return metricsAlarmDAO.getMetricsIdByAlarmId(alarmId);
    }

    public List<String> listAlarmNamesByTenant(String tenantId) {
        return alarmDAO.listAlarmNamesByTenant(tenantId);
    }

    public boolean hasSameAlarmName(String alarmName, List<String> tenantIds) {
        return alarmDAO.getCountByAlarmName(alarmName, tenantIds) > 0;
    }

    public String getImChannelIdByAlarmId(String alarmId) {
        return alarmImRelationDAO.getImChannelIdByAlarmId(alarmId);
    }

    public List<String> listChannelIdsByAlarmIds(List<String> alarmIds){
        if(CollectionUtils.isEmpty(alarmIds)){
            return Collections.emptyList();
        }

        return alarmDAO.listChannelIdsByAlarmIds(alarmIds);
    }

    /**
     * Refactor for alarm field usage checking when metrics editing
     */
    public FieldAlarmRelationOut getFieldAlarmRelationsByMetricsId(String metricsId, int maxAlarmDefLimit) {
        MetricsDO metricsDO = metricsDAO.getMetricsById(metricsId);
        return getFieldAlarmRelationsByMetricsId(metricsDO, maxAlarmDefLimit);
    }

    /**
     * Refactor for alarm field usage checking when metrics editing
     */
    public FieldAlarmRelationOut getFieldAlarmRelationsByMetricsId(MetricsDO metricsDO, int maxAlarmDefLimit) {
        Assert.notNull(metricsDO, "Can't find this metrics, please check it!");
        final String metricsId = metricsDO.getId();
        FieldAlarmRelationOut out = new FieldAlarmRelationOut();
        out.setMetricsId(metricsId);
        out.setTenantId(metricsDO.getTenantId());

        List<AlarmDO> metricsAlarmDOS = this.listAlarmIdNames(metricsId);
        if(CollectionUtils.isEmpty(metricsAlarmDOS)) {
            return out;
        }
        //key is alarmId
        Map<String, SimpleAlarm> alarmIdNames = metricsAlarmDOS.stream().map(alarmDO -> new SimpleAlarm(alarmDO.getId(), alarmDO.getName())).collect(Collectors.toMap(SimpleAlarm::getId, simpleAlarm -> simpleAlarm, (e1, e2) -> e1));

        // get field name and tag name from Metrics
        /*Set<String> metricsFieldNames = metricsFieldDAO.listFieldByMetricsIds(Lists.newArrayList(metricsId)).stream().map(MetricsFieldDO::getFieldName).collect(Collectors.toSet());
        metricsFieldNames.addAll(Arrays.asList(metricsDO.getTagNames().split(CommonSplitConstants.COMMA_SPLIT)));*/

        //<field(tag), <alarmId, alarmName>>
        Map<String, Set<String>> fieldAlarmIdSets = new HashMap<>();

        List<AlarmDefinitionRuleConditionDO> ruleConditions = ruleConditionDAO.getConditionsByAlarmIds(metricsAlarmDOS.stream().map(AlarmDO::getId).collect(Collectors.toList()));
        for(AlarmDefinitionRuleConditionDO ruleCondition : ruleConditions) {
            if (StringUtils.isNotBlank(ruleCondition.getName())) {
                putAlarmIdForSameField(fieldAlarmIdSets, ruleCondition.getAlarmDefinitionId(), maxAlarmDefLimit, ruleCondition.getName());

            } else if (ConditionType.EXPRESSION.getValue().equals(ruleCondition.getConditionType())) {
                List<String> variableFullNames = CustomAviatorUtils.compile(ruleCondition.getExpression(), false).getVariableFullNames();
                putAlarmIdForSameField(fieldAlarmIdSets, ruleCondition.getAlarmDefinitionId(), maxAlarmDefLimit, variableFullNames.toArray(String[]::new));
            }
        }

        fieldAlarmIdSets.forEach((fieldName, alarmIdSet) -> out.getRelations().add(new FieldAlarmRelationOut.Relation(fieldName, alarmIdSet.stream().map(alarmId -> alarmIdNames.get(alarmId)).collect(Collectors.toList()))));
        return out;
    }

    private void putAlarmIdForSameField(Map<String, Set<String>> fieldAlarmIdSets, String alarmId, int limit, String... fieldNames) {
        for(String fieldName : fieldNames) {
            if (!fieldAlarmIdSets.containsKey(fieldName)) {
                fieldAlarmIdSets.put(fieldName, new HashSet<>());
            }
            if (fieldAlarmIdSets.get(fieldName).size() < limit) {
                fieldAlarmIdSets.get(fieldName).add(alarmId);
            }
        }
    }



}
