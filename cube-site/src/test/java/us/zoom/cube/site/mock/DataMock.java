package us.zoom.cube.site.mock;

import us.zoom.cube.lib.common.DataParserPurposeEnum;
import us.zoom.cube.lib.common.DataParserUseStatusEnum;
import us.zoom.cube.lib.common.HubChannelType;
import us.zoom.cube.lib.hub.DataParserRawDataTypeEnum;
import us.zoom.cube.site.CubeSummary;
import us.zoom.cube.site.infra.utils.IdUtils;
import us.zoom.cube.site.lib.BasePara;
import us.zoom.cube.site.lib.input.*;
import us.zoom.cube.site.lib.input.dataparser.*;
import us.zoom.cube.site.lib.query.*;
import us.zoom.cube.site.lib.query.topic.TopicQuery;
import us.zoom.infra.dao.model.TenantDO;
import us.zoom.infra.enums.DataParserCollectTypeEnum;
import us.zoom.infra.enums.DataParserDataTypeEnum;
import us.zoom.infra.enums.FieldTypeEnum;
import us.zoom.infra.enums.LanguageEnum;
import us.zoom.infra.utils.CommonSplitConstants;
import us.zoom.infra.utils.DateUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

public class DataMock {


    public static final   String TENANT_NAME="testTenant";
    public static final String USER_NAME="testMan1";
    public static final String TOPIC_NAME="testTopic";

    public static final String PASS="testPass";

   public static final  PageQuery PAGE_QUERY=new PageQuery<>();
    public static final String PATTERN_NAME ="testPattern" ;
    public static final String INNER_PATTERN_NAME ="testInnerPattern" ;
    public static final String WEB_SOURCE="web_source";
    public static final String WEB_CMD="web_cmd";



    public static final String PATTERN_VALUE="%{TIMESTAMP_ISO8601:logdate}\\[%{DATA:id}\\]\\[%{LOGLEVEL:loglevel}\\]\\[%{DATA:javaClass}\\]%{DATA:message}\\\"";
    public static final String PATTERN_VALUE2="%{TIMESTAMP_ISO8601:innerDate},%{DATA:web_cmd},%{DATA:web_source},%{GREEDYDATA:messageInner}";
    public static final String COLLECTOR_NAME = "collectorName";
    public static final String LOG_CONTENT="[{\"id\":\"35093106881369677870314110651131286131243630868748959744\",\"timestamp\":1573629337000,\"loggroup\":\"webcluster_clusterDev_webinfolog\",\"appcluster\":\"clusterDev\",\"logstream\":\"web-info_i-04545003efc0d1ea3_zoomwebsgdevweb001.dev.zoom.us_VA\",\"message\":\"2019-11-13 07:15:37.592[WEB_4d1a5ac375d4599f78e042274734f831][INFO][WebLogger]2019-11-13 07:15:37,APISuccess,1,,/rc/op,,,,,gw.h323,,1\",\"lambdaTimestamp\":1573629344047},{\"id\":\"35093106881369677870314110651131286131243630868748959745\",\"timestamp\":1573629337000,\"loggroup\":\"webcluster_clusterDev_webinfolog\",\"appcluster\":\"clusterDev\",\"logstream\":\"web-info_i-04545003efc0d1ea3_zoomwebsgdevweb001.dev.zoom.us_VA\",\"message\":\"2019-11-13 07:15:37.748[WEB_70e16f414533dd8e2addddba59e44645][INFO][WebLogger]2019-11-13 07:15:37,APISuccess,1,,/rc/op,,,,,gw.h323,,1\",\"lambdaTimestamp\":1573629344047},{\"id\":\"35093106903670423068844733792667004403891992374729375746\",\"timestamp\":1573629338000,\"loggroup\":\"webcluster_clusterDev_webinfolog\",\"appcluster\":\"clusterDev\",\"logstream\":\"web-info_i-04545003efc0d1ea3_zoomwebsgdevweb001.dev.zoom.us_VA\",\"message\":\"2019-11-13 07:15:38.667[WEB_2a69b1f865e7f5364a00ced6a46194fa][INFO][WebLogger]2019-11-13 07:15:38,Login,1,whHlUrZGZG5ZhNej9Ly6/33Hg0KJe7N2r7n05JkjEPQ=,3.5.64722.0907,,1,5WuS_KCkThikKH-T5toI_A,100,iphone,,1\",\"lambdaTimestamp\":1573629344047},{\"id\":\"35093106903670423068844733792667004403891992374729375747\",\"timestamp\":1573629338000,\"loggroup\":\"webcluster_clusterDev_webinfolog\",\"appcluster\":\"clusterDev\",\"logstream\":\"web-info_i-04545003efc0d1ea3_zoomwebsgdevweb001.dev.zoom.us_VA\",\"message\":\"2019-11-13 07:15:38.752[WEB_2a69b1f865e7f5364a00ced6a46194fa][INFO][WebLogger]2019-11-13 07:15:38,APISuccess,1,,/login,,,,,iphone,,1\",\"lambdaTimestamp\":1573629344047}]";

    public static  final String AGG_NAME="testAgg";
    public static final String ALARM_NAME ="testAlarm" ;

    public static TenantDO mockTenant(){
        TenantDO tenantDO=new TenantDO();
        tenantDO.setId(IdUtils.generateId());
        tenantDO.setType(1);
        tenantDO.setDataFlowId("1");
        tenantDO.setName(TENANT_NAME);
        tenantDO.setCreateTime(new Date());
        tenantDO.setCreateTime(new Date());
        return tenantDO;
    }

    public static UserInput mockUser() {
        UserInput userInput=new UserInput();
        userInput.setName(USER_NAME);
        userInput.setUserInputPD(PASS);
        return userInput;
    }

    public static UserQuery mockUserQuery() {
        UserQuery userQuery=new UserQuery();
        userQuery.setName(USER_NAME);
        userQuery.setUserQueryPD(PASS);
        return userQuery;
    }

    public static TopicInput mockTopic(String tenantId,String userId) {
        TopicInput topicInput=new TopicInput();
        topicInput.setUserId(userId);
        topicInput.setTenantId(tenantId);
        topicInput.setId(IdUtils.generateId());
        topicInput.setLogCollector(0);
        topicInput.setLogHandler(0);
        topicInput.setLogConcurrency(1);
        topicInput.setTopicName(TOPIC_NAME);
        return topicInput;
    }

    public static BasePara mockBasePara(String userId  , String tenantId ) {

        BasePara basePara=new BasePara();
        basePara.setTenantId(tenantId);
        basePara.setUserId(userId);
//        basePara.setAccessToken(token);
        return basePara;
    }

    public static PageQuery<TopicQuery> mockeTopicQuery(String tenantId,String userId) {
        TopicQuery topicQuery=new TopicQuery();
        topicQuery.setTopicName(TOPIC_NAME);
        topicQuery.setTenantId(tenantId);
        topicQuery.setUserId(userId);
        PAGE_QUERY.setQueryPara(topicQuery);
        PAGE_QUERY.setTenantId(tenantId);
        PAGE_QUERY.setUserId(userId);
        return  PAGE_QUERY;
    }

    public static CollectorInput<CollectorFieldInput> mockCollectorInput(String tenantId, String userId,String topicId,String collectorId) {
        CollectorInput<CollectorFieldInput> collectorInput=new CollectorInput<>();
        collectorInput.setPatternName(PATTERN_NAME);
        collectorInput.setId(collectorId);
        collectorInput.setTenantId(tenantId);
        collectorInput.setUserId(userId);
        collectorInput.setInnerPatternName(INNER_PATTERN_NAME);
        collectorInput.setCollectorKey(topicId);
        collectorInput.setCollectorName(COLLECTOR_NAME);
        collectorInput.setCollectorType(0);
        collectorInput.setParseType("pattern");
        collectorInput.setCollectorField(mockField(collectorId));
        return collectorInput;
    }

    public static List<CollectorFieldInput> mockField(String collectorId) {
        List<CollectorFieldInput> fieldInputs=new ArrayList<>();
        CollectorFieldInput field=new CollectorFieldInput();
        field.setCollectorId(collectorId);
        field.setFieldType(FieldTypeEnum.string.name());
        field.setSourceField(WEB_CMD);
        field.setTargetField(WEB_CMD);
        fieldInputs.add(field);

        field=new CollectorFieldInput();
        field.setCollectorId(collectorId);
        field.setFieldType(FieldTypeEnum.number.name());
        field.setSourceField(WEB_SOURCE);
        field.setTargetField(WEB_SOURCE);
        fieldInputs.add(field);
        return fieldInputs;
    }

    public static PageQuery<CollectorQuery> mockCollectorQuery(String tenantId,String userId) {
        CollectorQuery query=new CollectorQuery();
        query.setCollectorName(COLLECTOR_NAME);
        query.setTenantId(tenantId);
        query.setUserId(userId);
        PAGE_QUERY.setQueryPara(query);
        PAGE_QUERY.setTenantId(tenantId);
        PAGE_QUERY.setUserId(userId);
        return  PAGE_QUERY;
    }

    public static FieldParseInput mockFieldParseInput() {
        FieldParseInput fieldParseInput=new FieldParseInput();
        fieldParseInput.setPatternName(PATTERN_NAME);
        fieldParseInput.setInnerPatternName(INNER_PATTERN_NAME);
        fieldParseInput.setLogContent(LOG_CONTENT);
        return fieldParseInput;
    }

    public static AggregationInput<AggregationRuleInput> mockAgg(String tenantId,String userId,String collectorId) {
        AggregationInput<AggregationRuleInput> aggregationInput=new AggregationInput();
        aggregationInput.setAggName(AGG_NAME);
        aggregationInput.setAggPeriod(10);
        aggregationInput.setCollectorId(collectorId);
        aggregationInput.setGroupFields(WEB_CMD);
        aggregationInput.setAggRule(mockAggRule());
        aggregationInput.setUserId(userId);
        aggregationInput.setTenantId(tenantId);
        return aggregationInput;
    }

    public static List<AggregationRuleInput> mockAggRule() {
        List<AggregationRuleInput> rules=new ArrayList<>();
        AggregationRuleInput rule=new AggregationRuleInput();
        rule.setAggField(WEB_SOURCE);
        rule.setAggTypes("sum,max,min,count,avg");
        rules.add(rule);
        return  rules;
    }

    public static List<AggregationRuleInput> mockAggRule2() {
        List<AggregationRuleInput> rules=new ArrayList<>();
        AggregationRuleInput rule=new AggregationRuleInput();
        rule.setAggField(WEB_CMD);
        rule.setAggTypes("sum,max,min,count,avg");
        rules.add(rule);
        return  rules;
    }

    public static PageQuery<AggregationQuery> mockAggQuery(String tenantId, String userId) {
        AggregationQuery query=new AggregationQuery();
        query.setAggName(AGG_NAME);
        PAGE_QUERY.setTenantId(tenantId);
        PAGE_QUERY.setUserId(userId);
        PAGE_QUERY.setQueryPara(query);
        return  PAGE_QUERY;
    }


    public static final String ALARM_CONTENT1="1";
    public static final String ALARM_CONTENT2="2";


    public static final String ALARM_TITLE1="1";
    public static final String ALARM_TITLE2="2";
    public static AlarmInput<AlarmRuleInput> mockAlarm(String tenantId, String userId, String aggId) {
        AlarmInput<AlarmRuleInput> alarmInput=new AlarmInput<>();
        alarmInput.setMetricsId(aggId);
        alarmInput.setAlarmName(ALARM_NAME);
//        alarmInput.setFromTime(new Date());
//        alarmInput.setToTime(new Date());
//        alarmInput.setAlarmCycle("[\"Monday\",\"Tuesday\",\"Wednesday\",\"Thursday\",\"Friday\"]");
        alarmInput.setAlarmContent(ALARM_CONTENT1);
        alarmInput.setTenantId(tenantId);
        alarmInput.setUserId(userId);
        alarmInput.setTitle(ALARM_TITLE1);
        alarmInput.setAlarmRules(mockAlarmRule());
        return alarmInput;
    }

    public static final String P0="p0";
    public static final String P1="p1";
    public static final String ALARM_EXPRESSION1="[{\"expression\":\"web_cmd == '/url1'\",\"field\":\"web_cmd\",\"type\":3 },{\"expression\":\"web_source != '200'\",\"field\":\"web_source\",\"type\":3 }]";
    public static final String ALARM_EXPRESSION2="[{\"expression\":\"web_cmd == '/url2'\",\"field\":\"web_cmd\",\"type\":3 },{\"expression\":\"web_source == '200'\",\"field\":\"web_source\",\"type\":3 }]";

    private static List<AlarmRuleInput> mockAlarmRule() {
        List<AlarmRuleInput> rules=new ArrayList<>();
        AlarmRuleInput rule=new AlarmRuleInput();
        rule.setAlarmLevel(P0);
        //rule.setAlarmRuleContent(ALARM_EXPRESSION1);
        rules.add(rule);
        return rules;
    }

    public static PageQuery<AlarmRecordsQuery> mockMonitorQuery(String tenantId, String userId) {
        AlarmRecordsQuery query=new AlarmRecordsQuery();
        query.setBegin(new Date(DateUtils.addMinute(new Date(),-100)).getTime());
        query.setEnd(new Date().getTime());
        query.setName(ALARM_NAME);
        query.setTenantId(tenantId);
        query.setUserId(userId);
        PAGE_QUERY.setTenantId(tenantId);
        PAGE_QUERY.setUserId(userId);
        PAGE_QUERY.setQueryPara(query);
        return  PAGE_QUERY;
    }


    public static final String DATA_PARSE_NAME="testDataParser";
    public static final String DATA_PARSE_FILE="/home/<USER>/logs/monitor.log";

    public static DataParserInput mockDataParserInput(CubeSummary summary) {
        DataParserInput dataParserInput=new DataParserInput();
        dataParserInput.setUserId(summary.getUserId());
        dataParserInput.setTenantId(summary.getTenantId());
        dataParserInput.setName(TENANT_NAME+ CommonSplitConstants.SPLIT+DATA_PARSE_NAME);
        dataParserInput.setCollectType(DataParserCollectTypeEnum.file.getCode());
        dataParserInput.setExcludeRule("exclude");
        dataParserInput.setChannelType(HubChannelType.normal.name());
        dataParserInput.setLanguageType(LanguageEnum.java.getCode());
        dataParserInput.setDataType(DataParserDataTypeEnum.app.getCode());
        dataParserInput.setRawDataType(DataParserRawDataTypeEnum.json.getCode());
        dataParserInput.setFilePath(DATA_PARSE_FILE);
        dataParserInput.setPipelines(mockPipelines());
        dataParserInput.setPurpose(DataParserPurposeEnum.forward.getValue());
        dataParserInput.setUseStatus(DataParserUseStatusEnum.use.getValue());
        dataParserInput.setTopic("topc1");
        dataParserInput.setOutputAqId("aq1");
        return  dataParserInput;
    }

    public static final String DATA_PARSE_PIPELINE_NAME_PREFIX="testDataParserPipeline";
    public static final String DATA_PARSE_PIPELINE_FILTER="url:login";


    public static List<DataParserPipelineInput> mockPipelines() {
        List<DataParserPipelineInput> pipelineInputs=new ArrayList<>();
        DataParserPipelineInput pipeline = mockDataParsePipeLine("parent1",1);
        pipeline.setSons(Arrays.asList(mockDataParsePipeLine("son1_1", 1),mockDataParsePipeLine("son1_2", 2)));
        pipelineInputs.add(pipeline);

        pipeline = mockDataParsePipeLine("parent2", 2);
        pipeline.setSons(Arrays.asList(mockDataParsePipeLine("son2_1", 1),mockDataParsePipeLine("son2_2", 2)));
        pipelineInputs.add(pipeline);
        return pipelineInputs;
    }

    public static DataParserPipelineInput mockDataParsePipeLine(String pipeLIineName, int order) {
        DataParserPipelineInput pipeline=new DataParserPipelineInput();
        pipeline.setName(DATA_PARSE_PIPELINE_NAME_PREFIX+ CommonSplitConstants.SPLIT+pipeLIineName);
        pipeline.setFilterRule(DATA_PARSE_PIPELINE_FILTER);
        pipeline.setFields(mockDataParserFields());
        pipeline.setFilterProcessores(mockFilterProcessor(pipeLIineName,1));
        pipeline.setGrokProcessores(mockGrokProcessor(pipeLIineName,2));
        pipeline.setRemapperProcessores(mockRemapperProcessor(pipeLIineName,3));
        pipeline.setOrder(order);
        return pipeline;
    }

    public static final String DATA_PARSE_PIPELINE_REMAPPER_PROCESSOR_NAME="testDataParserPipelineRemapperProcessor";
    public static final String DATA_PARSE_PIPELINE_REMAPPER_PROCESSOR_SOURCE_FIELDS="s1,s2,s3.s31.s311";
    public static final String DATA_PARSE_PIPELINE_REMAPPER_PROCESSOR_TARGET_FIELDS="t1,t2,t3";

    public static List<DataParserRemapperProcessorInput> mockRemapperProcessor(String namePrefix, int order) {
        List<DataParserRemapperProcessorInput> result=new ArrayList<>();
        DataParserRemapperProcessorInput remapperProcessorInput=new DataParserRemapperProcessorInput();
        remapperProcessorInput.setName(namePrefix+CommonSplitConstants.SPLIT+DATA_PARSE_PIPELINE_REMAPPER_PROCESSOR_NAME);
        remapperProcessorInput.setOrder(order);
        remapperProcessorInput.setSourceFileds(DATA_PARSE_PIPELINE_REMAPPER_PROCESSOR_SOURCE_FIELDS);
        remapperProcessorInput.setTargetFields(DATA_PARSE_PIPELINE_REMAPPER_PROCESSOR_TARGET_FIELDS);
        result.add(remapperProcessorInput);
        return result;
    }

    public static final String DATA_PARSE_PIPELINE_GROK_PROCESSOR_NAME="testDataParserPipelineGrokProcessor";
    public static final String DATA_PARSE_PIPELINE_GROK_PROCESSOR_TARGET_FIELD="testDataParserPipelineGrokProcessorTargetField";
    public static final String DATA_PARSE_PIPELINE_GROK_PROCESSOR_PARSE_RULE="23123123";

    public static List<DataParserGrokProcessorInput> mockGrokProcessor(String namePrefix, int order) {
        List<DataParserGrokProcessorInput> result=new ArrayList<>();
        DataParserGrokProcessorInput grokProcessorInput=new DataParserGrokProcessorInput();
        grokProcessorInput.setName(namePrefix+CommonSplitConstants.SPLIT+DATA_PARSE_PIPELINE_GROK_PROCESSOR_NAME);
        grokProcessorInput.setOrder(order);
        grokProcessorInput.setTargetField(DATA_PARSE_PIPELINE_GROK_PROCESSOR_TARGET_FIELD);
        grokProcessorInput.setParseRule(DATA_PARSE_PIPELINE_GROK_PROCESSOR_PARSE_RULE);
        result.add(grokProcessorInput);
        return result;
    }


    public static final String DATA_PARSE_PIPELINE_FILTER2="code:200";

    public static final String DATA_PARSE_PIPELINE_FILTER_PROCESSOR_NAME="testDataParserPipelineFilterProcessor";

    public static List<DataParserFilterProcessorInput> mockFilterProcessor(String namePrefix, int order) {
        List<DataParserFilterProcessorInput> processorInputs=new ArrayList<>();
        DataParserFilterProcessorInput processorInput=new DataParserFilterProcessorInput();
        processorInput.setFilterRule(DATA_PARSE_PIPELINE_FILTER2);
        processorInput.setName(namePrefix+CommonSplitConstants.SPLIT+DATA_PARSE_PIPELINE_FILTER_PROCESSOR_NAME);
        processorInput.setOrder(order);
        processorInputs.add(processorInput);
        return  processorInputs;
    }


    public static List<DataField> mockDataParserFields() {
        List<DataField> result=new ArrayList<>();
        DataField field=new DataField();
        field.setFieldName(WEB_CMD);
        field.setFieldType(FieldTypeEnum.string.name());
        result.add(field);

        field=new DataField();
        field.setFieldName(WEB_SOURCE);
        field.setFieldType(FieldTypeEnum.number.name());
        result.add(field);
        return result;
    }
}
