package us.zoom.cube.site.lib.output.dataparser;


/**
 * <AUTHOR> @date 2020/3/10
 */
public class GrokProcessorOut extends  BaseProcessorOut{

    private String targetField;
    private String parseRule;

    public GrokProcessorOut() {
    }

    public GrokProcessorOut(String id, String name, Integer order, String dataParserPipelineId, String dataParserId, String type, String targetField, String parseRule) {
        super(id, name, order, dataParserPipelineId, dataParserId, type);
        this.targetField = targetField;
        this.parseRule = parseRule;
    }

    public String getTargetField() {
        return targetField;
    }

    public void setTargetField(String targetField) {
        this.targetField = targetField;
    }

    public String getParseRule() {
        return parseRule;
    }

    public void setParseRule(String parseRule) {
        this.parseRule = parseRule;
    }
}
