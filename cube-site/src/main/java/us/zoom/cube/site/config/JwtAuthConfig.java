package us.zoom.cube.site.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.AutoConfigurationExcludeFilter;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.ImportAutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration;
import org.springframework.boot.context.TypeExcludeFilter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.FilterType;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import us.zoom.cube.site.api.intercept.SingleJwtAuthInterceptor;
import us.zoom.cube.site.config.properties.JwtAuthProperties;

import jakarta.servlet.Filter;
import jakarta.servlet.http.HttpServletRequest;
import java.util.List;


/**
 * @author: Starls Ding
 * @date: 2022/5/17 21:20
 * @desc:  do not use @EnableAutoConfiguration but @ImportAutoConfiguration
 */

@Configuration
@ConditionalOnBean(JwtAuthProperties.class)
@ImportAutoConfiguration(exclude = SecurityAutoConfiguration.class)
public class JwtAuthConfig implements WebMvcConfigurer {

    @Autowired
    private SingleJwtAuthInterceptor singleJwtAuthInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(singleJwtAuthInterceptor).addPathPatterns("/**").excludePathPatterns("/static/**", "/selfmonitor/**");

    }

    /**
    * mock a useless condition bean but required even if we close spring security since 2.4
     * (https://github.com/spring-projects/spring-boot/commit/0818f27f44317c52f66b9f0e7030fa8fcb7a92f0)
    * why use this condition can see the why author create the new annotation @ConditionalOnDefaultWebSecurity
    * https://github.com/spring-projects/spring-boot/commit/ef89eb6dfb0cf0ee2c63d2565c555458058e73f1#diff-4d8c90eff9e701be24e692bab3ecc625b74c433a94fc98ec9af4eff8fc2e2f4c
    * PS: OKTA follow this code design since 1.4.0
     */
    @Bean
    public SecurityFilterChain mockSecurityFilterChain() {
        return new SecurityFilterChain() {
            @Override
            public boolean matches(HttpServletRequest request) {
                return true;
            }

            @Override
            public List<Filter> getFilters() {
                return null;
            }
        };
    }



}
