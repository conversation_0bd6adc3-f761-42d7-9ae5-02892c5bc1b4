package us.zoom.trace;

import com.fasterxml.jackson.core.type.TypeReference;
import org.junit.jupiter.api.Test;
import us.zoom.cube.lib.utils.JsonUtils;
import us.zoom.cube.site.biz.trace.TraceServiceMapBuilder;
import us.zoom.cube.site.lib.dto.trace.SearchSpanResponseItem;
import us.zoom.cube.site.lib.dto.trace.TraceGraph;

import java.io.File;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.nio.file.Files;
import java.sql.Timestamp;
import java.time.Instant;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class TraceServiceMapBuilderTest {


    @Test
    public void build() {
        try {
            File file = new File("/Users/<USER>/trace_data.json");

            Map<String, List<Map<String, Object>>> data = JsonUtils.toObjectByTypeRef(new String(Files.readAllBytes(file.toPath())), new TypeReference<Map<String, List<Map<String, Object>>>>() {
            });
            List<Map<String, Object>> traceSpans = data.get("trace_spans");
            for (Map<String, Object> traceSpan : traceSpans) {
                Object time = traceSpan.get("timestamp");
                try {
                    if (time instanceof String) {
                        time = new Timestamp(Instant.parse((String) time).toEpochMilli());
                        traceSpan.put("timestamp", time);
                    }
                } catch (Throwable e) {
                    if (time instanceof String) {
                        time = Timestamp.valueOf((String) time);
                        traceSpan.put("timestamp", time);
                    }
                }
            }
            TraceServiceMapBuilder traceCommonServiceMapBuilder = new TraceServiceMapBuilder();
            for (Map<String, Object> rowData : traceSpans) {
                SearchSpanResponseItem searchSpanResponseItem = JsonUtils.toObject(String.valueOf(rowData.get("model")), SearchSpanResponseItem.class);
                searchSpanResponseItem.setTimestamp(((Timestamp) rowData.get("timestamp")).getTime());
                traceCommonServiceMapBuilder.addItem(searchSpanResponseItem);
            }
            TraceGraph build = traceCommonServiceMapBuilder.build();
            System.out.println(JsonUtils.getMapper().writerWithDefaultPrettyPrinter().writeValueAsString(build));
        } catch (Throwable e) {
            StringWriter stringWriter = new StringWriter();
            PrintWriter p = new PrintWriter(stringWriter);
            e.printStackTrace(p);
            System.out.println(stringWriter.getBuffer().toString());
        }
    }
}
