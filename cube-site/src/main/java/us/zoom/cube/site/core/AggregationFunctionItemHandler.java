package us.zoom.cube.site.core;

import com.google.common.collect.Lists;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import us.zoom.infra.dao.model.AggregationFunctionItemDO;
import us.zoom.infra.dao.model.AggregationHistogramRangeItemDO;
import us.zoom.infra.dao.model.AggregationPercentileItemDO;
import us.zoom.infra.dao.model.MetricsAggregationRuleDO;
import us.zoom.infra.dao.service.AggregationFunctionItemDAO;
import us.zoom.infra.dao.service.MetricsAggregationRuleDAO;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2020/7/19 10:20 AM
 */
@Service
public class AggregationFunctionItemHandler {

    @Autowired
    private AggregationFunctionItemDAO aggregationFunctionItemDAO;

    public int insertAggregationFunction(AggregationFunctionItemDO aggregationFunctionItemDO) {
        return aggregationFunctionItemDAO.insertAggregationFunction(aggregationFunctionItemDO);
    }



    public int batchInsertAggregationFunction(List<AggregationFunctionItemDO> aggregationFunctionItemDOList){
        if(CollectionUtils.isEmpty(aggregationFunctionItemDOList)) {
            return 0;
        }
        return aggregationFunctionItemDAO.batchInsertAggregationFunction(aggregationFunctionItemDOList);
    }


    public AggregationFunctionItemDO getById(String id){
        return aggregationFunctionItemDAO.getById(id);
    }

    public void deleteById(String id){
        aggregationFunctionItemDAO.deleteById(id);
    }

    public List<AggregationFunctionItemDO> listAll(){
        return aggregationFunctionItemDAO.listAll();
    }

    public void deleteByRuleId(String ruleId){
        aggregationFunctionItemDAO.deleteByRuleId(ruleId);
    }


   public void deleteByRuleIdList(List<String> ruleIdList){
        if(!CollectionUtils.isEmpty(ruleIdList)) {
            aggregationFunctionItemDAO.deleteByRuleIdList(ruleIdList);
        }
   }

    public int batchUpdateAggregationFunction(List<AggregationFunctionItemDO> aggregationFunctionItemDOList){
        if(CollectionUtils.isEmpty(aggregationFunctionItemDOList)){
            return 0;
        }
        return aggregationFunctionItemDAO.batchUpdateAggregationFunction(aggregationFunctionItemDOList);
    }


    public void batchDeleteByIdList(List<String> idList) {
        if(!CollectionUtils.isEmpty(idList)) {
            aggregationFunctionItemDAO.batchDeleteByIdList(idList);
        }
    }

}
