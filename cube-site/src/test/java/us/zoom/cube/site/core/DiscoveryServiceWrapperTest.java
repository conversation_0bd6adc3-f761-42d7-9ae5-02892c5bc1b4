package us.zoom.cube.site.core;


import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import us.zoom.cube.site.CubeSiteApplicationTests;
import us.zoom.cube.site.infra.utils.IdUtils;
import us.zoom.infra.dao.model.DataParserDO;
import us.zoom.infra.dao.service.DataParserDAO;
import us.zoom.infra.enums.DataParserDataTypeEnum;
import us.zoom.infra.enums.LanguageEnum;

import java.util.Date;

/**
 * <AUTHOR>
 * @create 2020/8/7 11:05 AM
 */
public class DiscoveryServiceWrapperTest extends CubeSiteApplicationTests {

    @Autowired
    private DataParserDAO dataParserDAO;

    @Test
    public void testDao() {
        DataParserDO dataParserDO = buildDataParser();
        dataParserDAO.addDataParser(dataParserDO);

        dataParserDO.setAqGroupId("aqGroupId-666");
        dataParserDAO.editDataParser(dataParserDO);

        String id = dataParserDO.getId();
        dataParserDAO.deleteById(id);
    }

    private DataParserDO buildDataParser() {
        DataParserDO dataParserDO = new DataParserDO();
        dataParserDO.setId(IdUtils.generateId());
        dataParserDO.setName("testDataParserName");
        dataParserDO.setCollectType(5);
        dataParserDO.setRawDataType(1);
        dataParserDO.setRawDataParseRule("testRule");
        dataParserDO.setTenantId("WEB1");
        dataParserDO.setGmtCreate(new Date());
        dataParserDO.setGmtModify(new Date());
        dataParserDO.setAqGroupId("af437e4a-c755-4133-8438-154beed62297");
        dataParserDO.setChannelType("normal");
        dataParserDO.setRawDataParseType("groovy");
        dataParserDO.setInvokeFunction("parseMessage");
        dataParserDO.setCreateUserId("bdf9bf43-2376-4ade-abd1-bd0fab887009");
        dataParserDO.setEditUserId("bdf9bf43-2376-4ade-abd1-bd0fab887009");
        if (null == dataParserDO.getInfraType()) {
            dataParserDO.setInfraType("");
        }
        if (null == dataParserDO.getDataType()) {
            dataParserDO.setDataType(DataParserDataTypeEnum.app.getCode());
        }
        if (null == dataParserDO.getExcludeRule()) {
            dataParserDO.setExcludeRule("");
        }
        if (null == dataParserDO.getFilePath()) {
            dataParserDO.setFilePath("");
        }
        if (null == dataParserDO.getIncludeRule()) {
            dataParserDO.setIncludeRule("");
        }
        if (null == dataParserDO.getLanguageType()) {
            dataParserDO.setLanguageType(LanguageEnum.java.getCode());
        }
        if (null == dataParserDO.getRawDataParseRule()) {
            dataParserDO.setRawDataParseRule("");
        }
        if (null == dataParserDO.getChannelRule()) {
            dataParserDO.setChannelRule("");
        }
        if (null == dataParserDO.getInvokeFunction()) {
            dataParserDO.setInvokeFunction("");
        }
        if (null == dataParserDO.getServiceId()) {
            dataParserDO.setServiceId("");
        }
        if (null == dataParserDO.getAqId()) {
            dataParserDO.setAqId("");
        }
        if (null == dataParserDO.getAqGroupId()) {
            dataParserDO.setAqGroupId("");
        }
        if (null == dataParserDO.getRawDataParseType()) {
            dataParserDO.setRawDataParseType("");
        }

        return dataParserDO;
    }
}
