package us.zoom.cube.site.core;

import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import us.zoom.infra.dao.model.AccessApplyDO;
import us.zoom.infra.dao.service.AccessApplyDAO;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class AccessApplyHandler {

    private Logger monitorLog = LoggerFactory.getLogger("Monitor");

    @Autowired
    private AccessApplyDAO accessApplyDAO;

    public void addAccessApply(AccessApplyDO accessApplyDO) {
        accessApplyDAO.add(accessApplyDO);
    }

    public void addAccessApply(List<AccessApplyDO> accessApplyDOS) {
        accessApplyDAO.addBatch(accessApplyDOS);
    }

    public void deleteAccessApply(String id) {
        accessApplyDAO.deleteById(id);
    }

    public List<AccessApplyDO> findByParam(String userId, List<String> tenantIds, Integer status, String role, int pageIndex, int pageSize) {
        return accessApplyDAO.findByParam(userId, tenantIds, status, role, pageIndex, pageSize);
    }

    public int getCountByParam(String userId, List<String> tenantIds, Integer status, String role) {
        return accessApplyDAO.getCountByParam(userId, tenantIds, status, role);
    }

    public void editAccessApply(AccessApplyDO AccessApplyDO) {
        accessApplyDAO.edit(AccessApplyDO);

    }

    public AccessApplyDO getAccessApply(String id) {
        return accessApplyDAO.getById(id);
    }

}
