package us.zoom.cube.site.core;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import us.zoom.cube.lib.common.CubeConstants;
import us.zoom.infra.dao.model.TaskQueueDO;
import us.zoom.infra.dao.service.TaskQueueDAO;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * @Author: luis.zheng
 */
@Component
@Slf4j
public class TaskQueueHandler {
    @Autowired
    private TaskQueueDAO taskQueueDAO;

    public List<TaskQueueDO> findByParam(String name, String topic, String taskType, int pageIndex, int pageSize) {
        List<TaskQueueDO> kafkaClusterDOS = taskQueueDAO.findByParam(name, topic, taskType, pageSize * (pageIndex - 1), pageSize);
        return kafkaClusterDOS;
    }

    public Integer getCountByParam(String name, String topic, String taskType) {
        return taskQueueDAO.getCountByParam(name, topic, taskType);
    }

    public List<TaskQueueDO> listAll() {
        List<TaskQueueDO> taskQueueDOList= new ArrayList<>();
        long counts = taskQueueDAO.queryCounts();
        int pageSize = CubeConstants.CUBE_SQL_BATCH_QUERY_SIZE;
        long pageNum = counts / pageSize + (counts % pageSize == 0 ? 0 : 1);
        for (int i = 1 ; i <= pageNum ; i++){
            taskQueueDOList.addAll(taskQueueDAO.listBatch(pageSize * (i - 1),pageSize));
        }
        return taskQueueDOList;
    }

    public void add(TaskQueueDO taskQueueDO) {
        taskQueueDAO.add(taskQueueDO);
    }

    public void edit(TaskQueueDO taskQueueDO) {
        taskQueueDAO.edit(taskQueueDO);

    }

    public TaskQueueDO getById(String id) {
        TaskQueueDO task = taskQueueDAO.getById(id);
        return task;
    }

    public List<TaskQueueDO> getByIds(Set<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }
        return taskQueueDAO.getByIds(ids);
    }

    public TaskQueueDO findUnique(String kafkaClusterId, String groupId, String topic) {
        return taskQueueDAO.findUnique(kafkaClusterId, groupId, topic);
    }

    public TaskQueueDO findUniqueNotId(String kafkaClusterId, String groupId, String topic, String id) {
        return taskQueueDAO.findUniqueNotId(kafkaClusterId, groupId, topic, id);
    }

    public int countByKafkaClusterId(String kafkaClusterId) {
        return taskQueueDAO.countByKafkaClusterId(kafkaClusterId);
    }

    public List<TaskQueueDO> getByKafkaClusterId(String kafkaClusterId, Integer limitNum) {
        return taskQueueDAO.getByKafkaClusterId(kafkaClusterId, limitNum);
    }

    public int countByUnitTagId(String kafkaClusterId) {
        return taskQueueDAO.countByUnitTagId(kafkaClusterId);
    }

    public TaskQueueDO findByName(String name) {
        return taskQueueDAO.findByName(name);
    }

    public TaskQueueDO findByNameNotId(String name, String id) {
        return taskQueueDAO.findByNameNotId(name, id);
    }

    public void deleteById(String id) {
        taskQueueDAO.deleteById(id);
    }

    public void setDefault(String id, String isDefault) {
        taskQueueDAO.setDefault(id, isDefault);
    }
}
