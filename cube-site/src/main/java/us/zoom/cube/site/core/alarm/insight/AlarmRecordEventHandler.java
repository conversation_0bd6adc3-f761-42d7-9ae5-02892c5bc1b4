package us.zoom.cube.site.core.alarm.insight;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import us.zoom.cube.site.biz.syspara.AlarmParaService;
import us.zoom.cube.site.lib.output.alarm.insight.AlarmRecordsOutputEntry;
import us.zoom.infra.dao.model.AlarmRecordEventDO;
import us.zoom.infra.dao.service.AlarmRecordEventDAO;
import us.zoom.infra.influx.model.alarm.AlarmMatchRecord;
import us.zoom.infra.utils.Instance;
import us.zoom.cube.lib.utils.JsonUtils;

import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: canyon.li
 * @date: 2023/07/06
 **/
@Slf4j
@Component
public class AlarmRecordEventHandler {

    @Autowired
    private AlarmRecordEventDAO alarmRecordEventDAO;

    @Autowired
    private AlarmParaService alarmParaService;


    public boolean addAlarmRecordEventToDB(AlarmMatchRecord alarmMatchRecord) {

        if (!alarmParaService.getParamBooleanValue(AlarmParaService.ALARM_RECORD_EVENT_FROM_DB, false)) {
            return true;
        }

        try {
            AlarmRecordEventDO alarmRecordEventDO = convertToEventDO(alarmMatchRecord);
            alarmRecordEventDAO.add(alarmRecordEventDO);
            return true;
        } catch (Exception e) {
            log.error("addAlarmRecordEventToDB error, record:{}", JsonUtils.toJsonStringIgnoreExp(alarmMatchRecord), e);
        }
        return false;
    }

    private static final long DEFAULT_CHECK_PERIOD = 2 * 60 * 1000;
    public Map<String, AlarmRecordsOutputEntry> getRecentChangeRecord(String alarmId) {

        if (!alarmParaService.getParamBooleanValue(AlarmParaService.ALARM_RECORD_EVENT_FROM_DB, false)) {
            return null;
        }
        long checkPeriod = alarmParaService.getParamLongValue(AlarmParaService.ALARM_RECORD_EVENT_FROM_DB_PERIOD, DEFAULT_CHECK_PERIOD);
        Date end = new Timestamp(System.currentTimeMillis());
        Date begin = new Timestamp(System.currentTimeMillis() - checkPeriod);

        try {
            List<AlarmRecordEventDO> eventList = alarmRecordEventDAO.selectByTimeRange(alarmId, begin, end);
            //convert eventList to outputEntryMap, Key is recordId
            //When there are multiple records with same recordId in the eventList, take the one with the largest eventGmtCreate, which means the latest operation
            Map<String, AlarmRecordsOutputEntry> changedRecordMap = Instance.ofNullable(eventList).stream().map(this::convertToRecordOutput).filter(Objects::nonNull)
                    .collect(Collectors.toMap(AlarmRecordsOutputEntry::getId, outputEntry -> outputEntry, (a, b) -> a.getEventGmtCreate() > b.getEventGmtCreate() ? a : b));
            return changedRecordMap;
        } catch (Exception e) {
            log.error("getRecentChangeRecord error");
            return null;
        }
    }

    /**
     * convert AlarmRecordEventDO -> AlarmRecordsOutputEntry
     */
    private AlarmRecordsOutputEntry convertToRecordOutput(AlarmRecordEventDO alarmRecordEventDO) {
        try {
            AlarmRecordsOutputEntry alarmRecordsOutputEntry = new AlarmRecordsOutputEntry();
            alarmRecordsOutputEntry.setId(alarmRecordEventDO.getId());
            alarmRecordsOutputEntry.setAlarmLevel(alarmRecordEventDO.getAlarmLevel());
            alarmRecordsOutputEntry.setAlarmId(alarmRecordEventDO.getAlarmId());
            alarmRecordsOutputEntry.setAlarmName(alarmRecordEventDO.getAlarmName());
            alarmRecordsOutputEntry.setAlarmRuleId(alarmRecordEventDO.getAlarmRuleId());
            alarmRecordsOutputEntry.setHittedRule(alarmRecordEventDO.getHittedRule());
            alarmRecordsOutputEntry.setStatus(alarmRecordEventDO.getAlarmStatus());
            alarmRecordsOutputEntry.setMetricsId(alarmRecordEventDO.getMetricsId());
            alarmRecordsOutputEntry.setMetricsName(alarmRecordEventDO.getMetricsName());
            alarmRecordsOutputEntry.setMetricsTagMap(JsonUtils.toObject(alarmRecordEventDO.getTagMap(), HashMap.class));
            alarmRecordsOutputEntry.setAckExpireTime(Optional.of(alarmRecordEventDO.getAckExpireTime()).map(Date::getTime).orElse(null));
            alarmRecordsOutputEntry.setTime(Optional.of(alarmRecordEventDO.getTime()).map(Date::getTime).orElse(null));
            alarmRecordsOutputEntry.setEventGmtCreate(Optional.of(alarmRecordEventDO.getEventGmtCreate()).map(Date::getTime).orElse(null));
            alarmRecordsOutputEntry.setRecordGmtCreate(Optional.of(alarmRecordEventDO.getRecordGmtCreate()).map(Date::getTime).orElse(null));
            return alarmRecordsOutputEntry;
        } catch (Exception e) {
            log.error("convertToRecordOutput error, eventDO:{}", JsonUtils.toJsonStringIgnoreExp(alarmRecordEventDO), e);
        }
        return null;
    }



    private List<AlarmRecordsOutputEntry> mappingResultToOutput(List<Map<String, Object>> queryResult) {
        List<AlarmRecordsOutputEntry> alarmRecordsOutputList = new ArrayList<>();
        for (Map<String, Object> resultEntry : queryResult) {
            AlarmRecordsOutputEntry alarmRecordsOutputEntry = new AlarmRecordsOutputEntry();
            alarmRecordsOutputEntry.setId(Optional.ofNullable(resultEntry.get("id")).map(Object::toString).orElse(null));
            alarmRecordsOutputEntry.setAlarmLevel(Optional.ofNullable(resultEntry.get("alarmLevel")).map(Object::toString).orElse(null));
            alarmRecordsOutputEntry.setAlarmId(Optional.ofNullable(resultEntry.get("alarmId")).map(Object::toString).orElse(null));
            alarmRecordsOutputEntry.setAlarmName(Optional.ofNullable(resultEntry.get("alarmName")).map(Object::toString).orElse(null));
            alarmRecordsOutputEntry.setAlarmRuleId(Optional.ofNullable(resultEntry.get("alarmRuleId")).map(Object::toString).orElse(null));
            alarmRecordsOutputEntry.setHittedRule(Optional.ofNullable(resultEntry.get("hittedRule")).map(Object::toString).orElse(null));
            alarmRecordsOutputEntry.setStatus(Optional.ofNullable(resultEntry.get("alarmStatus")).map(Object::toString).orElse(null));
            alarmRecordsOutputEntry.setMetricsId(Optional.ofNullable(resultEntry.get("metricsId")).map(Object::toString).orElse(null));
            alarmRecordsOutputEntry.setMetricsName(Optional.ofNullable(resultEntry.get("metricsName")).map(Object::toString).orElse(null));
            alarmRecordsOutputEntry.setMetricsTagMap((Map)resultEntry.get("tagMap"));
            alarmRecordsOutputEntry.setAckExpireTime(Optional.of(((Timestamp)(resultEntry.get("ackExpireTime"))).getTime()).orElse(0L));
            alarmRecordsOutputEntry.setTime(((Timestamp) resultEntry.get("time")).getTime());
            alarmRecordsOutputEntry.setEventGmtCreate(((Timestamp) resultEntry.get("gmtCreate")).getTime());
            if (Objects.nonNull(resultEntry.get("tb.gmtCreate"))) {
                alarmRecordsOutputEntry.setRecordGmtCreate(((Timestamp) resultEntry.get("tb.gmtCreate")).getTime());
            }

            alarmRecordsOutputList.add(alarmRecordsOutputEntry);
        }
        return alarmRecordsOutputList;
    }

    /**
     * convert AlarmMatchRecord -> AlarmRecordEventDO
     */
    private AlarmRecordEventDO convertToEventDO(AlarmMatchRecord alarmMatchRecord) {
        AlarmRecordEventDO alarmRecordEventDO = new AlarmRecordEventDO();
        alarmRecordEventDO.setId(alarmMatchRecord.getId());
        alarmRecordEventDO.setAlarmStatus(alarmMatchRecord.getStatus().getWithNoPrefix());
        Timestamp metricTs = new Timestamp(alarmMatchRecord.getTime() == 0 ? System.currentTimeMillis() : alarmMatchRecord.getTime());
        alarmRecordEventDO.setTime(metricTs);
        alarmRecordEventDO.setAckExpireTime(Optional.ofNullable(alarmMatchRecord.getAckExpireTime()).map(Timestamp::new).orElse(null));
        alarmRecordEventDO.setRecordGmtCreate(Optional.of(alarmMatchRecord.getRecordGmtCreate()).orElse(null));
        alarmRecordEventDO.setEventGmtCreate(Optional.of(alarmMatchRecord.getEventGmtCreate()).orElse(null));
        alarmRecordEventDO.setMetricsId(alarmMatchRecord.getMetricsId());
        alarmRecordEventDO.setMetricsName(alarmMatchRecord.getMetricsName());
        alarmRecordEventDO.setAlarmId(alarmMatchRecord.getAlarmId());
        alarmRecordEventDO.setAlarmName(alarmMatchRecord.getAlarmName());
        alarmRecordEventDO.setAlarmLevel(alarmMatchRecord.getAlarmLevel().getLevel());
        alarmRecordEventDO.setEvent(alarmMatchRecord.getAlarmEventType().name());
        alarmRecordEventDO.setTagKey(alarmMatchRecord.getTagKey());
        alarmRecordEventDO.setAlarmRuleId(alarmMatchRecord.getAlarmRuleId());
        alarmRecordEventDO.setHittedRule(JsonUtils.toJsonStringIgnoreExp(alarmMatchRecord.getHittedRule()));
        alarmRecordEventDO.setTagMap(JsonUtils.toJsonStringIgnoreExp(alarmMatchRecord.getMetricsTags()));
        alarmRecordEventDO.setHittedValue(JsonUtils.toJsonStringIgnoreExp(alarmMatchRecord.getHittedValue()));
        return alarmRecordEventDO;
    }
}
