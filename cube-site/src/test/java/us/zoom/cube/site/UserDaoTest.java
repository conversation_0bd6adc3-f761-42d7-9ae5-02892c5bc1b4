package us.zoom.cube.site;


import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import us.zoom.infra.dao.model.UserDO;
import us.zoom.infra.dao.service.UserDAO;

import java.util.UUID;

@SpringBootTest(classes = CubeSiteApplication.class,webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)//springboot
public class UserDaoTest {

    @Autowired
    private UserDAO userDAO;
    @Test
    public void add(){
        UserDO userDO=new UserDO();
       /* userDO.setName("<EMAIL>");
        if(userDAO.getUserByName(userDO.getName())!=null ){
            return;
        }*/
        userDO.setId(UUID.randomUUID().toString());
        userDO.setName("hutest");
        userDO.setRole("normal");
        userDO.setPasswordNew("passwordNew");
        userDAO.add(userDO);

        userDO.setPasswordNew("passwordNew-2");
        userDAO.editUser(userDO, true);

        userDO.setPasswordNew("passwordNew-3");
        userDAO.editUser(userDO, false);

        userDAO.delUser(userDO.getId());
    }
}
