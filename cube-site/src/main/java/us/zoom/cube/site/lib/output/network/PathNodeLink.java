package us.zoom.cube.site.lib.output.network;

import lombok.Data;

@Data
public class PathNodeLink {
    private String source;
    private String target;
    private String color;
    private double avgLoss;
    private double avgLatency;
    public void setColorByLossAndLate() {
        if (avgLoss > 90) {
            // red
            color = "#e8173d";
        } else if (avgLoss >= 70 && avgLoss <= 90) {
            // yellow
            color = "#ffbf39";
        } else {
            // green
            color = "#268543";
        }
    }
}
