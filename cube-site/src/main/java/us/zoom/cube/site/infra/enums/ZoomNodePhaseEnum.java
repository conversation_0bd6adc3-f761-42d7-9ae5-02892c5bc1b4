package us.zoom.cube.site.infra.enums;

/**
 * <AUTHOR>
 */
public enum ZoomNodePhaseEnum {
    requestStart,
    updateInfo,
    requestMid,
    userNotExist,
    beforeCreateUser,
    afterCreateUser,
    beforeResetPassword,
    afterResetPassword,
    beforeLogin,
    afterLogin,
    createUserFail,
    loginFail,
    setTopicPrivilegesOuterForceStart,
    setTopicPrivilegesOuterForceEnd,
    setTopicPrivilegesOuterStart,
    setTopicPrivilegesOuterEnd,
    setTopicPrivilegesOuterError,
    requestErrorEnd,
    requestEnd,
    getTopicsStart,
    getTopicsEnd,
    setTopicPrivileges_userNotExist,
    setTopicPrivilegesInnerError,
    setTopicPrivilegesInnerStart,
    setTopicPrivilegesInnerEnd,
    publiserError;
}
