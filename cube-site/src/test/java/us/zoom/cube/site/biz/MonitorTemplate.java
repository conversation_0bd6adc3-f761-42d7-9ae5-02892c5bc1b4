package us.zoom.cube.site.biz;

import com.amazonaws.util.json.Jackson;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.lib.common.HubChannelType;
import us.zoom.cube.lib.hub.DataParserRawDataTypeEnum;
import us.zoom.cube.lib.hub.RawDataParseTypeEnum;
import us.zoom.cube.lib.integrations.MetricsFieldTypeEnum;
import us.zoom.cube.site.core.*;
import us.zoom.cube.site.infra.utils.IdUtils;
import us.zoom.cube.site.lib.ResponseObject;
import us.zoom.cube.site.lib.input.*;
import us.zoom.cube.site.lib.input.dataparser.DataParserGroovyProcessorInput;
import us.zoom.cube.site.lib.input.dataparser.DataParserInput;
import us.zoom.cube.site.lib.input.dataparser.DataParserPipelineInput;
import us.zoom.cube.site.lib.input.dataparser.DataParserRemapperProcessorInput;
import us.zoom.infra.dao.model.*;
import us.zoom.infra.dao.service.CollectorFieldDAO;
import us.zoom.infra.dao.service.ViewComponentDAO;
import us.zoom.infra.dao.service.ViewContainerComponentRelaDAO;
import us.zoom.infra.enums.*;
import us.zoom.infra.model.dashboard.BaseViewComponentCfg;
import us.zoom.infra.thread.ThreadLocalStore;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;

public class MonitorTemplate extends BaseTest {


    @Autowired
    private TenantService tenantService;

    @Autowired
    private AsyncQueueGroupService asyncQueueGroupService;

    @Autowired
    AsyncQueueService asyncQueueService;

    @Autowired
    private DataParserService dataParserService;

    @Autowired
    MetricsService metricsService;

    private String userId="82b4e894-edc7-47b8-ae99-58b6cbf58326";


    @Test
    public void addCalAq(){
                List<String> tenantNames=Arrays.asList("asyncmq","asyncsearch","searchmanage","websrvlinkpv","aisense","csms","cciapi","cciop","common-consumer","email-service","eventservice","Command-Center","zurm","lookup","marketplace","onzoom","migration","nacos","ocs","web-op","pms");        List<Future> futures=new ArrayList<>();

    }

    @Test
    public void addTenant() throws Exception {
        ExecutorService executorService= Executors.newFixedThreadPool(17);
//        List<String> tenantNames=Arrays.asList("aisense","csms","cciapi","cciop","common-consumer","email-service","eventservice","Command-Center","zurm","lookup","marketplace","onzoom","migration","nacos","ocs","web-op","pms");        List<Future> futures=new ArrayList<>();

        List<String> tenantNames=Arrays.asList("asyncmq-kafka");
        List<Future> futures=new ArrayList<>();
        for(final String tenantName:tenantNames){
           Future future= executorService.submit(new Runnable() {
                @SneakyThrows
                @Override
                public void run() {
                    System.out.println("add data for "+tenantName);
                    ResponseObject responseObject=null;
                    String tenantId=addAndGetTenantId(tenantName);

                    addInfra(tenantId, tenantName);

                    addMonitor(tenantId, tenantName);
                    System.out.println("add data for "+tenantName+" over !");

                }
            });

            futures.add(future);

        }

        for(Future future:futures){
            future.get();
        }

    }

    private void addMonitor(String tenantId, String tenantName) {
        String aqGroupId = addMonitorGroup(tenantName, tenantId);
        String queueId = addMonitorAq(tenantName, tenantId, aqGroupId);
        String dataParseId = addGetMonitorDataParser(tenantName, tenantId, aqGroupId, queueId);
        addDemoMonitorPipeline(tenantName,tenantId,dataParseId);
    }

    private void addInfra(String tenantId, String tenantName) {
        ThreadLocalStore.setUserInfoLocal(userId);
        String dashId=addDashboard(tenantId);
        //add infra aq group
        String aqGroupId = addAqGroup(tenantName, tenantId);

        //add infra queue
        String queueId = addAq(tenantName, tenantId, aqGroupId);

        //add infra data parser
        String dataParseId = addGetDataParser(tenantName, tenantId, aqGroupId, queueId);

        //add network pipeline
        addNetWork(tenantName, tenantId, dataParseId,dashId);
        addIo(tenantName, tenantId, dataParseId,dashId);
//                    //add cpu
        addCpu(tenantName, tenantId, dataParseId,dashId);
////        //add disk
        addDisk(tenantName, tenantId, dataParseId,dashId);
////        //add memory
        addMemory(tenantName, tenantId, dataParseId,dashId);
////      //add load
        addLoad(tenantName, tenantId, dataParseId,dashId);
//                    //add jvm
        addJvm(tenantName, tenantId, dataParseId,dashId);
////        //add process
        addProcess(tenantName, tenantId, dataParseId,dashId);
    }

    @Autowired
    DashboardHandler dashboardHandler;
    private String addDashboard(String tenantId) {
        return  dashboardHandler.addDash(tenantId, DashboardTypeEnum.HOST_ALL_METRIC.getCode(),"host metrics");
    }


    @Autowired
    DataParserHandler dataParserHandler;
    private String addGetDataParser(String tenantName, String tenantId, String aqGroupId, String queueId) {
        String name=tenantName+"_infra_parser";
        DataParserDO dataParserDO= dataParserHandler.getDataParserByTenantIdAndName(tenantId,name);
        if(null != dataParserDO){
            return dataParserDO.getId();
        }
        ResponseObject responseObject=null;
        DataParserInput dataParserInput=new DataParserInput();
        dataParserInput.setUserId(userId);
        dataParserInput.setTenantId(tenantId);
        dataParserInput.setName(name);
        dataParserInput.setChannelType(HubChannelType.normal.name());
        dataParserInput.setAqGroupId(aqGroupId);
        dataParserInput.setAqId(queueId);
        dataParserInput.setCollectType(DataParserCollectTypeEnum.aq.getCode());
        dataParserInput.setRawDataType(DataParserRawDataTypeEnum.json.getCode());
        try {
            responseObject=  dataParserService.addDataParser(dataParserInput);
        } catch (Exception e) {
            e.printStackTrace();
        }

        return responseObject.getData().toString();
    }


    private String addGetMonitorDataParser(String tenantName, String tenantId, String aqGroupId, String queueId) {
        String name=tenantName+"_monitor_parser";
        DataParserDO dataParserDO= dataParserHandler.getDataParserByTenantIdAndName(tenantId,name);
        if(null != dataParserDO){
            return dataParserDO.getId();
        }
        ResponseObject responseObject=null;
        DataParserInput dataParserInput=new DataParserInput();
        dataParserInput.setUserId(userId);
        dataParserInput.setTenantId(tenantId);
        dataParserInput.setName(name);
        dataParserInput.setChannelType(HubChannelType.normal.name());
        dataParserInput.setAqGroupId(aqGroupId);
        dataParserInput.setAqId(queueId);
        dataParserInput.setCollectType(DataParserCollectTypeEnum.aq.getCode());
        dataParserInput.setRawDataType(DataParserRawDataTypeEnum.other.getCode());
        dataParserInput.setRawDataParseType(RawDataParseTypeEnum.groovy.name());
        dataParserInput.setRawDataParseRule("import groovy.json.JsonSlurper\n" +
                "\n" +
                "def parseMessage(String message) {\n" +
                "    def jsonSlurper = new JsonSlurper();\n" +
                "    List<Map<String, Object>> messageJson = jsonSlurper.parseText(message);\n" +
                "    for (Map<String, Object> map : messageJson) {\n" +
                "        innnerMessageText = map.message;\n" +
                "        Map<String, Object> innnerMessageJson = jsonSlurper.parseText(innnerMessageText);\n" +
                "        map.putAll(innnerMessageJson);\n" +
                "    }\n" +
                "    return messageJson;\n" +
                "}\n");
        dataParserInput.setInvokeFunction("parseMessage");
        try {
            responseObject=  dataParserService.addDataParser(dataParserInput);
        } catch (Exception e) {
            e.printStackTrace();
        }

        return responseObject.getData().toString();
    }

    private void addProcess(String tenantName, String tenantId, String dataParseId,String dashId) {
        String pipelineName;
        String pipeLineId;
        pipelineName="process";
        pipeLineId= addPipeline(tenantId,pipelineName,8,dataParseId);
        addRemapper(tenantId, dataParseId, pipeLineId);
        addResetPid(tenantId, dataParseId, pipeLineId);
        addField(tenantName, tenantId, pipelineName, pipeLineId,"clusterId", FieldTypeEnum.string.name());
        addField(tenantName, tenantId, pipelineName, pipeLineId,"regionId",FieldTypeEnum.string.name());
        addField(tenantName, tenantId, pipelineName, pipeLineId,"hostName",FieldTypeEnum.string.name());
        addField(tenantName, tenantId, pipelineName, pipeLineId,"ip",FieldTypeEnum.string.name());
        addField(tenantName, tenantId, pipelineName, pipeLineId,"processName",FieldTypeEnum.string.name());
        addField(tenantName, tenantId, pipelineName, pipeLineId,"threadNumbers",FieldTypeEnum.number.name());
        addField(tenantName, tenantId, pipelineName, pipeLineId,"cpuPercent",FieldTypeEnum.number.name());
        addField(tenantName, tenantId, pipelineName, pipeLineId,"memoryPercent",FieldTypeEnum.number.name());
        addField(tenantName, tenantId, pipelineName, pipeLineId,"ioWrite",FieldTypeEnum.number.name());
        addField(tenantName, tenantId, pipelineName, pipeLineId,"ioRead",FieldTypeEnum.number.name());





        List<MetricsFieldInput> fieldInputList=new ArrayList<>();
        fieldInputList.add(newMetricsFieldInput("threadNumbers",MetricsFieldTypeEnum.number.getValue()));
        fieldInputList.add(newMetricsFieldInput("cpuPercent",MetricsFieldTypeEnum.number.getValue()));
        fieldInputList.add(newMetricsFieldInput("memoryPercent",MetricsFieldTypeEnum.number.getValue()));
        fieldInputList.add(newMetricsFieldInput("ioWrite",MetricsFieldTypeEnum.number.getValue()));
        fieldInputList.add(newMetricsFieldInput("ioRead",MetricsFieldTypeEnum.number.getValue()));

        String metricId=addMetrics(tenantId, pipelineName, pipeLineId, Arrays.asList("hostName","ip","clusterId","regionId","processName"),fieldInputList);
        Integer colSize=2;
        String viewId=addViewContainerToDash(dashId,tenantId,pipelineName,8,colSize);
        BaseViewComponentCfg cfg=createBaseCfg("time",Arrays.asList("cpuPercent"),false,null,true,Arrays.asList("processName"));
        addViewComponent(metricId,viewId,tenantId,"Cpu Info", Jackson.toJsonString(cfg),dashId,colSize,1);

        cfg=createBaseCfg("time",Arrays.asList("memoryPercent"),false,null,true,Arrays.asList("processName"));
        addViewComponent(metricId,viewId,tenantId,"Mem Info", Jackson.toJsonString(cfg),dashId,colSize,2);

        cfg=createBaseCfg("time",Arrays.asList("threadNumbers"),false,null,true,Arrays.asList("processName"));
        addViewComponent(metricId,viewId,tenantId,"Thread Info", Jackson.toJsonString(cfg),dashId,colSize,3);



        cfg=createBaseCfg("time",Arrays.asList("ioWrite","ioRead"),false,null,true,Arrays.asList("processName"));
        addViewComponent(metricId,viewId,tenantId,"IO Info", Jackson.toJsonString(cfg),dashId,colSize,4);

    }

    private void addJvm(String tenantName, String tenantId, String dataParseId,String dashId) {
        String pipelineName;
        String pipeLineId;
        pipelineName="jvm";
        pipeLineId= addPipeline(tenantId,pipelineName,7,dataParseId);
        addRemapper(tenantId, dataParseId, pipeLineId);
        addField(tenantName, tenantId, pipelineName, pipeLineId,"clusterId", FieldTypeEnum.string.name());
        addField(tenantName, tenantId, pipelineName, pipeLineId,"regionId",FieldTypeEnum.string.name());
        addField(tenantName, tenantId, pipelineName, pipeLineId,"hostName",FieldTypeEnum.string.name());
        addField(tenantName, tenantId, pipelineName, pipeLineId,"ip",FieldTypeEnum.string.name());


        addField(tenantName, tenantId, pipelineName, pipeLineId,"heapMemoryUsedPercent",FieldTypeEnum.number.name());
        addField(tenantName, tenantId, pipelineName, pipeLineId,"gcMinorCollectionCount",FieldTypeEnum.number.name());
        addField(tenantName, tenantId, pipelineName, pipeLineId,"gcSurvivorSize",FieldTypeEnum.number.name());
        addField(tenantName, tenantId, pipelineName, pipeLineId,"nonHeapMemoryMax",FieldTypeEnum.number.name());
        addField(tenantName, tenantId, pipelineName, pipeLineId,"cpuLoadSystem",FieldTypeEnum.number.name());
        addField(tenantName, tenantId, pipelineName, pipeLineId,"loadedClasses",FieldTypeEnum.number.name());
        addField(tenantName, tenantId, pipelineName, pipeLineId,"threadCount",FieldTypeEnum.number.name());
        addField(tenantName, tenantId, pipelineName, pipeLineId,"nonHeapMemoryCommitted",FieldTypeEnum.number.name());
        addField(tenantName, tenantId, pipelineName, pipeLineId,"nonHeapMemoryInit",FieldTypeEnum.number.name());
        addField(tenantName, tenantId, pipelineName, pipeLineId,"heapMemoryCommitted",FieldTypeEnum.number.name());
        addField(tenantName, tenantId, pipelineName, pipeLineId,"cpuLoadProcess",FieldTypeEnum.number.name());
        addField(tenantName, tenantId, pipelineName, pipeLineId,"heapMemoryMax",FieldTypeEnum.number.name());
        addField(tenantName, tenantId, pipelineName, pipeLineId,"heapMemory",FieldTypeEnum.number.name());
        addField(tenantName, tenantId, pipelineName, pipeLineId,"gcMinorCollectionTime",FieldTypeEnum.number.name());
        addField(tenantName, tenantId, pipelineName, pipeLineId,"heapMemoryInit",FieldTypeEnum.number.name());
        addField(tenantName, tenantId, pipelineName, pipeLineId,"gcMajorCollectionCount",FieldTypeEnum.number.name());
        addField(tenantName, tenantId, pipelineName, pipeLineId,"gcMajorCollectionTime",FieldTypeEnum.number.name());




        List<MetricsFieldInput> fieldInputList=new ArrayList<>();
        fieldInputList.add(newMetricsFieldInput("heapMemoryUsedPercent",MetricsFieldTypeEnum.number.getValue()));
        fieldInputList.add(newMetricsFieldInput("gcMinorCollectionCount",MetricsFieldTypeEnum.number.getValue()));
        fieldInputList.add(newMetricsFieldInput("gcSurvivorSize",MetricsFieldTypeEnum.number.getValue()));
        fieldInputList.add(newMetricsFieldInput("nonHeapMemoryMax",MetricsFieldTypeEnum.number.getValue()));
        fieldInputList.add(newMetricsFieldInput("cpuLoadSystem",MetricsFieldTypeEnum.number.getValue()));
        fieldInputList.add(newMetricsFieldInput("loadedClasses",MetricsFieldTypeEnum.number.getValue()));
        fieldInputList.add(newMetricsFieldInput("threadCount",MetricsFieldTypeEnum.number.getValue()));
        fieldInputList.add(newMetricsFieldInput("nonHeapMemoryCommitted",MetricsFieldTypeEnum.number.getValue()));
        fieldInputList.add(newMetricsFieldInput("nonHeapMemoryInit",MetricsFieldTypeEnum.number.getValue()));
        fieldInputList.add(newMetricsFieldInput("heapMemoryCommitted",MetricsFieldTypeEnum.number.getValue()));
        fieldInputList.add(newMetricsFieldInput("cpuLoadProcess",MetricsFieldTypeEnum.number.getValue()));
        fieldInputList.add(newMetricsFieldInput("heapMemoryMax",MetricsFieldTypeEnum.number.getValue()));
        fieldInputList.add(newMetricsFieldInput("heapMemory",MetricsFieldTypeEnum.number.getValue()));
        fieldInputList.add(newMetricsFieldInput("gcMinorCollectionTime",MetricsFieldTypeEnum.number.getValue()));
        fieldInputList.add(newMetricsFieldInput("heapMemoryInit",MetricsFieldTypeEnum.number.getValue()));
        fieldInputList.add(newMetricsFieldInput("gcMajorCollectionCount",MetricsFieldTypeEnum.number.getValue()));
        fieldInputList.add(newMetricsFieldInput("gcMajorCollectionTime",MetricsFieldTypeEnum.number.getValue()));

        String metricId=addMetrics(tenantId, pipelineName, pipeLineId, Arrays.asList("hostName","ip","clusterId","regionId"),fieldInputList);
        Integer colSize=2;
        String viewId=addViewContainerToDash(dashId,tenantId,pipelineName,7,colSize);
        BaseViewComponentCfg cfg=createBaseCfg("time",Arrays.asList("heapMemory"),false,null,false,null);
        addViewComponent(metricId,viewId,tenantId,"The total Java heap memory used(KB)", Jackson.toJsonString(cfg),dashId,colSize,1);



        cfg=createBaseCfg("time",Arrays.asList("heapMemoryCommitted"),false,null,false,null);
        addViewComponent(metricId,viewId,tenantId,"The total Java heap memory committed to be used(KB)", Jackson.toJsonString(cfg),dashId,colSize,2);

        cfg=createBaseCfg("time",Arrays.asList("heapMemoryInit"),false,null,false,null);
        addViewComponent(metricId,viewId,tenantId,"The initial Java heap memory allocated(KB)", Jackson.toJsonString(cfg),dashId,colSize,3);

        cfg=createBaseCfg("time",Arrays.asList("heapMemoryMax"),false,null,false,null);
        addViewComponent(metricId,viewId,tenantId,"The maximum Java heap memory available(KB)", Jackson.toJsonString(cfg),dashId,colSize,4);

        cfg=createBaseCfg("time",Arrays.asList("nonHeapMemory"),false,null,false,null);
        addViewComponent(metricId,viewId,tenantId,"The total Java non-heap memory used(KB)", Jackson.toJsonString(cfg),dashId,colSize,5);

        cfg=createBaseCfg("time",Arrays.asList("nonHeapMemoryCommitted"),false,null,false,null);
        addViewComponent(metricId,viewId,tenantId,"The total Java non-heap memory committed to be used(KB)", Jackson.toJsonString(cfg),dashId,colSize,6);


        cfg=createBaseCfg("time",Arrays.asList("nonHeapMemoryInit"),false,null,false,null);
        addViewComponent(metricId,viewId,tenantId,"The initial Java non-heap memory allocated(KB)", Jackson.toJsonString(cfg),dashId,colSize,7);

        cfg=createBaseCfg("time",Arrays.asList("threadCount"),false,null,false,null);
        addViewComponent(metricId,viewId,tenantId,"The number of live threads", Jackson.toJsonString(cfg),dashId,colSize,8);


        cfg=createBaseCfg("time",Arrays.asList("loadedClasses"),false,null,false,null);
        addViewComponent(metricId,viewId,tenantId,"The number of  classes loaded by JVM", Jackson.toJsonString(cfg),dashId,colSize,9);

        cfg=createBaseCfg("time",Arrays.asList("cpuLoadProcess"),false,null,false,null);
        addViewComponent(metricId,viewId,tenantId,"The load of JVM process", Jackson.toJsonString(cfg),dashId,colSize,10);

        cfg=createBaseCfg("time",Arrays.asList("gcMajorCollectionCount","gcMinorCollectionCount"),false,null,false,null);
        addViewComponent(metricId,viewId,tenantId,"Gc Count", Jackson.toJsonString(cfg),dashId,colSize,11);

        cfg=createBaseCfg("time",Arrays.asList("gcMajorCollectionTime","gcMinorCollectionTime"),false,null,false,null);
        addViewComponent(metricId,viewId,tenantId,"Gc Time", Jackson.toJsonString(cfg),dashId,colSize,12);


        cfg=createBaseCfg("time",Arrays.asList("heapMemoryUsedPercent"),false,null,false,null);
        addViewComponent(metricId,viewId,tenantId,"The Java heap memory used percentage", Jackson.toJsonString(cfg),dashId,colSize,13);



    }

    private void addLoad(String tenantName, String tenantId, String dataParseId,String dashId) {
        String pipelineName;
        String pipeLineId;
        pipelineName="load";
        pipeLineId= addPipeline(tenantId,pipelineName,6,dataParseId);
        addRemapper(tenantId, dataParseId, pipeLineId);
        addField(tenantName, tenantId, pipelineName, pipeLineId,"clusterId", FieldTypeEnum.string.name());
        addField(tenantName, tenantId, pipelineName, pipeLineId,"regionId",FieldTypeEnum.string.name());
        addField(tenantName, tenantId, pipelineName, pipeLineId,"hostName",FieldTypeEnum.string.name());
        addField(tenantName, tenantId, pipelineName, pipeLineId,"ip",FieldTypeEnum.string.name());


        addField(tenantName, tenantId, pipelineName, pipeLineId,"loadNorm1",FieldTypeEnum.number.name());
        addField(tenantName, tenantId, pipelineName, pipeLineId,"load1",FieldTypeEnum.number.name());
        addField(tenantName, tenantId, pipelineName, pipeLineId,"load5",FieldTypeEnum.number.name());
        addField(tenantName, tenantId, pipelineName, pipeLineId,"loadNorm5",FieldTypeEnum.number.name());
        addField(tenantName, tenantId, pipelineName, pipeLineId,"loadNorm15",FieldTypeEnum.number.name());
        addField(tenantName, tenantId, pipelineName, pipeLineId,"load15",FieldTypeEnum.number.name());




        List<MetricsFieldInput> fieldInputList=new ArrayList<>();
        fieldInputList.add(newMetricsFieldInput("loadNorm1", MetricsFieldTypeEnum.number.getValue()));
        fieldInputList.add(newMetricsFieldInput("load1", MetricsFieldTypeEnum.number.getValue()));
        fieldInputList.add(newMetricsFieldInput("load5", MetricsFieldTypeEnum.number.getValue()));
        fieldInputList.add(newMetricsFieldInput("loadNorm5", MetricsFieldTypeEnum.number.getValue()));
        fieldInputList.add(newMetricsFieldInput("loadNorm15", MetricsFieldTypeEnum.number.getValue()));
        fieldInputList.add(newMetricsFieldInput("load15", MetricsFieldTypeEnum.number.getValue()));

        String metricId=addMetrics(tenantId, pipelineName, pipeLineId, Arrays.asList("hostName","ip","clusterId","regionId"),fieldInputList);
        Integer colSize=1;
        String viewId=addViewContainerToDash(dashId,tenantId,pipelineName,6,colSize);
        BaseViewComponentCfg cfg=createBaseCfg("time",Arrays.asList("load1","load5","load15"),false,null,false,null);
        addViewComponent(metricId,viewId,tenantId,"Total load", Jackson.toJsonString(cfg),dashId,colSize,1);


        cfg=createBaseCfg("time",Arrays.asList("loadNorm1","loadNorm5","loadNorm15"),false,null,false,null);
        addViewComponent(metricId,viewId,tenantId,"Average load", Jackson.toJsonString(cfg),dashId,colSize,2);
    }

    private void addMemory(String tenantName, String tenantId, String dataParseId,String dashId) {
        String pipelineName;
        String pipeLineId;
        pipelineName="memory";
        pipeLineId= addPipeline(tenantId,pipelineName,5,dataParseId);
        addRemapper(tenantId, dataParseId, pipeLineId);
        addField(tenantName, tenantId, pipelineName, pipeLineId,"clusterId", FieldTypeEnum.string.name());
        addField(tenantName, tenantId, pipelineName, pipeLineId,"regionId",FieldTypeEnum.string.name());
        addField(tenantName, tenantId, pipelineName, pipeLineId,"hostName",FieldTypeEnum.string.name());
        addField(tenantName, tenantId, pipelineName, pipeLineId,"ip",FieldTypeEnum.string.name());


        addField(tenantName, tenantId, pipelineName, pipeLineId,"free",FieldTypeEnum.number.name());
        addField(tenantName, tenantId, pipelineName, pipeLineId,"usable",FieldTypeEnum.number.name());
        addField(tenantName, tenantId, pipelineName, pipeLineId,"used",FieldTypeEnum.number.name());
        addField(tenantName, tenantId, pipelineName, pipeLineId,"usedPercent",FieldTypeEnum.number.name());


        List<MetricsFieldInput> fieldInputList=new ArrayList<>();
        fieldInputList.add(newMetricsFieldInput("free", MetricsFieldTypeEnum.number.getValue()));
        fieldInputList.add(newMetricsFieldInput("usable", MetricsFieldTypeEnum.number.getValue()));
        fieldInputList.add(newMetricsFieldInput("used", MetricsFieldTypeEnum.number.getValue()));
        fieldInputList.add(newMetricsFieldInput("usedPercent", MetricsFieldTypeEnum.number.getValue()));

        String metricId=addMetrics(tenantId, pipelineName, pipeLineId, Arrays.asList("hostName","ip","clusterId","regionId"),fieldInputList);
        Integer colSize=2;
        String viewId=addViewContainerToDash(dashId,tenantId,pipelineName,5,colSize);
        BaseViewComponentCfg cfg=createBaseCfg("time",Arrays.asList("usedPercent"),false,null,false,null);
        addViewComponent(metricId,viewId,tenantId,"Memory Use Info", Jackson.toJsonString(cfg),dashId,colSize,1);


        cfg=createBaseCfg("time",Arrays.asList("free","used","usable"),false,null,false,null);
        addViewComponent(metricId,viewId,tenantId,"Memory  Info(KB)", Jackson.toJsonString(cfg),dashId,colSize,2);
    }

    private void addDisk(String tenantName, String tenantId, String dataParseId,String dashId) {
        String pipelineName;
        String pipeLineId;
        pipelineName="disk";
        pipeLineId= addPipeline(tenantId,pipelineName,4,dataParseId);
        addRemapper(tenantId, dataParseId, pipeLineId);
        addField(tenantName, tenantId, pipelineName, pipeLineId,"clusterId", FieldTypeEnum.string.name());
        addField(tenantName, tenantId, pipelineName, pipeLineId,"regionId",FieldTypeEnum.string.name());
        addField(tenantName, tenantId, pipelineName, pipeLineId,"hostName",FieldTypeEnum.string.name());
        addField(tenantName, tenantId, pipelineName, pipeLineId,"ip",FieldTypeEnum.string.name());
        addField(tenantName, tenantId, pipelineName, pipeLineId,"device",FieldTypeEnum.string.name());
        addField(tenantName, tenantId, pipelineName, pipeLineId,"used",FieldTypeEnum.number.name());
        addField(tenantName, tenantId, pipelineName, pipeLineId,"usedPercent",FieldTypeEnum.number.name());
        addField(tenantName, tenantId, pipelineName, pipeLineId,"total",FieldTypeEnum.number.name());




        List<MetricsFieldInput> fieldInputList=new ArrayList<>();
        fieldInputList.add(newMetricsFieldInput("used", MetricsFieldTypeEnum.number.getValue()));
        fieldInputList.add(newMetricsFieldInput("usedPercent", MetricsFieldTypeEnum.number.getValue()));
        fieldInputList.add(newMetricsFieldInput("total", MetricsFieldTypeEnum.number.getValue()));

        String metricId=addMetrics(tenantId, pipelineName, pipeLineId, Arrays.asList("hostName","ip","clusterId","regionId","device"),fieldInputList);
        Integer colSize=2;
        String viewId=addViewContainerToDash(dashId,tenantId,pipelineName,4,colSize);
        BaseViewComponentCfg cfg=createBaseCfg("time",Arrays.asList("usedPercent"),false,null,true,Arrays.asList("device"));
        addViewComponent(metricId,viewId,tenantId,"Disk Use Percent(%)", Jackson.toJsonString(cfg),dashId,colSize,1);
    }

    private void addCpu(String tenantName, String tenantId, String dataParseId,String dashId) {
        String pipelineName;
        String pipeLineId;
        pipelineName="cpu";
        pipeLineId= addPipeline(tenantId,pipelineName,3,dataParseId);
        addRemapper(tenantId, dataParseId, pipeLineId);
        addField(tenantName, tenantId, pipelineName, pipeLineId,"clusterId", FieldTypeEnum.string.name());
        addField(tenantName, tenantId, pipelineName, pipeLineId,"regionId",FieldTypeEnum.string.name());
        addField(tenantName, tenantId, pipelineName, pipeLineId,"hostName",FieldTypeEnum.string.name());
        addField(tenantName, tenantId, pipelineName, pipeLineId,"ip",FieldTypeEnum.string.name());

        addField(tenantName, tenantId, pipelineName, pipeLineId,"st",FieldTypeEnum.number.name());
        addField(tenantName, tenantId, pipelineName, pipeLineId,"si",FieldTypeEnum.number.name());
        addField(tenantName, tenantId, pipelineName, pipeLineId,"hi",FieldTypeEnum.number.name());
        addField(tenantName, tenantId, pipelineName, pipeLineId,"id",FieldTypeEnum.number.name());
        addField(tenantName, tenantId, pipelineName, pipeLineId,"sy",FieldTypeEnum.number.name());
        addField(tenantName, tenantId, pipelineName, pipeLineId,"ni",FieldTypeEnum.number.name());
        addField(tenantName, tenantId, pipelineName, pipeLineId,"us",FieldTypeEnum.number.name());
        addField(tenantName, tenantId, pipelineName, pipeLineId,"wa",FieldTypeEnum.number.name());




        List<MetricsFieldInput> fieldInputList=new ArrayList<>();
        fieldInputList.add(newMetricsFieldInput("st", MetricsFieldTypeEnum.number.getValue()));
        fieldInputList.add(newMetricsFieldInput("si", MetricsFieldTypeEnum.number.getValue()));
        fieldInputList.add(newMetricsFieldInput("hi", MetricsFieldTypeEnum.number.getValue()));
        fieldInputList.add(newMetricsFieldInput("id", MetricsFieldTypeEnum.number.getValue()));
        fieldInputList.add(newMetricsFieldInput("sy", MetricsFieldTypeEnum.number.getValue()));
        fieldInputList.add(newMetricsFieldInput("ni", MetricsFieldTypeEnum.number.getValue()));
        fieldInputList.add(newMetricsFieldInput("us", MetricsFieldTypeEnum.number.getValue()));
        fieldInputList.add(newMetricsFieldInput("wa", MetricsFieldTypeEnum.number.getValue()));

        String metricId=addMetrics(tenantId, pipelineName, pipeLineId, Arrays.asList("hostName","ip","clusterId","regionId"),fieldInputList);
        Integer colSize=1;
        String viewId=addViewContainerToDash(dashId,tenantId,pipelineName,3,colSize);
        BaseViewComponentCfg cfg=createBaseCfg("time",Arrays.asList("st","si","hi","id","sy","ni","wa","us"),false,null,false,null);
        addViewComponent(metricId,viewId,tenantId,"Cpu Info", Jackson.toJsonString(cfg),dashId,colSize,1);
    }

    private void addIo(String tenantName, String tenantId, String dataParseId,String dashId) {
        String pipelineName;
        String pipeLineId;//add io
        pipelineName="io";
        pipeLineId= addPipeline(tenantId,pipelineName,2,dataParseId);
        addRemapper(tenantId, dataParseId, pipeLineId);
        addField(tenantName, tenantId, pipelineName, pipeLineId,"clusterId", FieldTypeEnum.string.name());
        addField(tenantName, tenantId, pipelineName, pipeLineId,"regionId",FieldTypeEnum.string.name());
        addField(tenantName, tenantId, pipelineName, pipeLineId,"hostName",FieldTypeEnum.string.name());
        addField(tenantName, tenantId, pipelineName, pipeLineId,"ip",FieldTypeEnum.string.name());

        addField(tenantName, tenantId, pipelineName, pipeLineId,"rkbS",FieldTypeEnum.number.name());
        addField(tenantName, tenantId, pipelineName, pipeLineId,"await",FieldTypeEnum.number.name());
        addField(tenantName, tenantId, pipelineName, pipeLineId,"rS",FieldTypeEnum.number.name());
        addField(tenantName, tenantId, pipelineName, pipeLineId,"wrqmS",FieldTypeEnum.number.name());
        addField(tenantName, tenantId, pipelineName, pipeLineId,"rAwait",FieldTypeEnum.number.name());
        addField(tenantName, tenantId, pipelineName, pipeLineId,"wAwait",FieldTypeEnum.number.name());
        addField(tenantName, tenantId, pipelineName, pipeLineId,"avgQSz",FieldTypeEnum.number.name());
        addField(tenantName, tenantId, pipelineName, pipeLineId,"wS",FieldTypeEnum.number.name());
        addField(tenantName, tenantId, pipelineName, pipeLineId,"rrqmS",FieldTypeEnum.number.name());
        addField(tenantName, tenantId, pipelineName, pipeLineId,"avgRqSz",FieldTypeEnum.number.name());
        addField(tenantName, tenantId, pipelineName, pipeLineId,"util",FieldTypeEnum.number.name());
        addField(tenantName, tenantId, pipelineName, pipeLineId,"svctm",FieldTypeEnum.number.name());
        addField(tenantName, tenantId, pipelineName, pipeLineId,"wkbS",FieldTypeEnum.number.name());



        List<MetricsFieldInput> fieldInputList=new ArrayList<>();
        fieldInputList.add(newMetricsFieldInput("rkbS", MetricsFieldTypeEnum.number.getValue()));
        fieldInputList.add(newMetricsFieldInput("await", MetricsFieldTypeEnum.number.getValue()));
        fieldInputList.add(newMetricsFieldInput("rS", MetricsFieldTypeEnum.number.getValue()));
        fieldInputList.add(newMetricsFieldInput("wrqmS", MetricsFieldTypeEnum.number.getValue()));
        fieldInputList.add(newMetricsFieldInput("rAwait", MetricsFieldTypeEnum.number.getValue()));
        fieldInputList.add(newMetricsFieldInput("wAwait", MetricsFieldTypeEnum.number.getValue()));
        fieldInputList.add(newMetricsFieldInput("avgQSz", MetricsFieldTypeEnum.number.getValue()));
        fieldInputList.add(newMetricsFieldInput("wS", MetricsFieldTypeEnum.number.getValue()));
        fieldInputList.add(newMetricsFieldInput("rrqmS", MetricsFieldTypeEnum.number.getValue()));
        fieldInputList.add(newMetricsFieldInput("avgRqSz", MetricsFieldTypeEnum.number.getValue()));
        fieldInputList.add(newMetricsFieldInput("util", MetricsFieldTypeEnum.number.getValue()));
        fieldInputList.add(newMetricsFieldInput("svctm", MetricsFieldTypeEnum.number.getValue()));
        fieldInputList.add(newMetricsFieldInput("wkbS", MetricsFieldTypeEnum.number.getValue()));

        String metricId=addMetrics(tenantId, pipelineName, pipeLineId, Arrays.asList("hostName","ip","clusterId","regionId"),fieldInputList);
        Integer colSize=2;
        String viewId=addViewContainerToDash(dashId,tenantId,pipelineName,2,colSize);
        BaseViewComponentCfg cfg=createBaseCfg("time",Arrays.asList("rS","wS"),false,null,false,null);
        addViewComponent(metricId,viewId,tenantId,"Read/Write Count Per Second", Jackson.toJsonString(cfg),dashId,colSize,1);

        cfg=createBaseCfg("time",Arrays.asList("rkbS","wkbS"),false,null,false,null);
        addViewComponent(metricId,viewId,tenantId,"Read/Write sectors per second", Jackson.toJsonString(cfg),dashId,colSize,2);

        cfg=createBaseCfg("time",Arrays.asList("rrqmS","wrgmS"),false,null,false,null);
        addViewComponent(metricId,viewId,tenantId,"The number of merge read/write operations per second", Jackson.toJsonString(cfg),dashId,colSize,3);


        cfg=createBaseCfg("time",Arrays.asList("rAwait","wAwait"),false,null,false,null);
        addViewComponent(metricId,viewId,tenantId,"Average wait time per device IO read/write(MS) ", Jackson.toJsonString(cfg),dashId,colSize,4);


        cfg=createBaseCfg("time",Arrays.asList("avgRqSz"),false,null,false,null);
        addViewComponent(metricId,viewId,tenantId,"Average data size (sector) per device I/O operation", Jackson.toJsonString(cfg),dashId,colSize,5);

        cfg=createBaseCfg("time",Arrays.asList("avgQSz"),false,null,false,null);
        addViewComponent(metricId,viewId,tenantId,"Average I/O queue length", Jackson.toJsonString(cfg),dashId,colSize,6);

        cfg=createBaseCfg("time",Arrays.asList("svctm"),false,null,false,null);
        addViewComponent(metricId,viewId,tenantId,"Average service time per device I / O operation (MS)", Jackson.toJsonString(cfg),dashId,colSize,7);

        cfg=createBaseCfg("time",Arrays.asList("util"),false,null,false,null);
        addViewComponent(metricId,viewId,tenantId,"cpu util of io", Jackson.toJsonString(cfg),dashId,colSize,8);

        cfg=createBaseCfg("time",Arrays.asList("await"),false,null,false,null);
        addViewComponent(metricId,viewId,tenantId,"Average waiting time per device I / O operation (MS)", Jackson.toJsonString(cfg),dashId,colSize,8);
    }


    private void addDemoMonitorPipeline(String tenantName, String tenantId, String dataParseId ){
        String pipelineName="demo";
        String pipeLineId= addPipeline(tenantId,pipelineName,1,dataParseId);
        addMonitorGroovy(tenantId, dataParseId, pipeLineId);
        addField(tenantName, tenantId, pipelineName, pipeLineId,"hostName",FieldTypeEnum.string.name());
        addField(tenantName, tenantId, pipelineName, pipeLineId,"ip",FieldTypeEnum.string.name());
        addField(tenantName, tenantId, pipelineName, pipeLineId,"device",FieldTypeEnum.string.name());
        addField(tenantName, tenantId, pipelineName, pipeLineId,"clusterId",FieldTypeEnum.string.name());
        addField(tenantName, tenantId, pipelineName, pipeLineId,"regionId",FieldTypeEnum.string.name());

        addField(tenantName, tenantId, pipelineName, pipeLineId,"demoNum",FieldTypeEnum.number.name());

    }


    @Autowired
    DataParserGroovyProcessorHandler dataParserGroovyProcessorHandler;
    private void addMonitorGroovy(String tenantId, String dataParseId, String pipeLineId) {

        String name="getBasic";
        List<DataParserGroovyProcessorDO> records= dataParserGroovyProcessorHandler.listByPipelineIds(Arrays.asList(pipeLineId));
        if(!CollectionUtils.isEmpty(records)){
            for(DataParserGroovyProcessorDO parserGroovyProcessorDO:records){
                if(parserGroovyProcessorDO.getName().equals(name)){
                    return;
                }
            }
        }

        DataParserGroovyProcessorInput input=new DataParserGroovyProcessorInput();
        input.setUserId(userId);
        input.setTenantId(tenantId);
        input.setDataParserId(dataParseId);
        input.setDataParserPipelineId(pipeLineId);
        input.setName(name);
        input.setInvokeFunction("getBasic");
        input.setParseRule("def getBasic(Map input) {\n" +
                "    def inputTime=null\n" +
                "    if(input.logDate){\n" +
                "      inputTime=input.logDate\n" +
                "    }else if(input.logdate){\n" +
                "      inputTime=input.logdate\n" +
                "    }\n" +
                "    def collectTime= new Date().parse(\"yyyy-MM-dd HH:mm:ss:SSS\", inputTime).time;\n" +
                "input.put(\"ts\",collectTime);\n" +
                "input.put(\"clusterId\",input.appcluster);\n" +
                "  if(input.logstream){\n" +
                "     String[] basic= input.logstream.split('_');\n" +
                "      input.put(\"instanceId\",basic[1]);\n" +
                "     input.put(\"hostName\",basic[2]);\n" +
                "     if(basic.size()>3){\n" +
                "        input.put(\"regionId\",basic[3]);\n" +
                "     }\n" +
                "  }\n" +
                "}");
        input.setOrder(1);
        dataParserService.addGroovyProcessor(input);
    }

    private void addNetWork(String tenantName, String tenantId, String dataParseId,String dashId) {
        String pipelineName="network";
        String pipeLineId= addPipeline(tenantId,pipelineName,1,dataParseId);
        addRemapper(tenantId, dataParseId, pipeLineId);
        addField(tenantName, tenantId, pipelineName, pipeLineId,"packetErrOut", FieldTypeEnum.number.name());
        addField(tenantName, tenantId, pipelineName, pipeLineId,"byteIn",FieldTypeEnum.number.name());
        addField(tenantName, tenantId, pipelineName, pipeLineId,"byteOut",FieldTypeEnum.number.name());
        addField(tenantName, tenantId, pipelineName, pipeLineId,"packetOut",FieldTypeEnum.number.name());
        addField(tenantName, tenantId, pipelineName, pipeLineId,"packetIn",FieldTypeEnum.number.name());
        addField(tenantName, tenantId, pipelineName, pipeLineId,"packetErrIn",FieldTypeEnum.number.name());
        addField(tenantName, tenantId, pipelineName, pipeLineId,"packetErrOut",FieldTypeEnum.number.name());


        addField(tenantName, tenantId, pipelineName, pipeLineId,"hostName",FieldTypeEnum.string.name());
        addField(tenantName, tenantId, pipelineName, pipeLineId,"ip",FieldTypeEnum.string.name());
        addField(tenantName, tenantId, pipelineName, pipeLineId,"device",FieldTypeEnum.string.name());
        addField(tenantName, tenantId, pipelineName, pipeLineId,"clusterId",FieldTypeEnum.string.name());
        addField(tenantName, tenantId, pipelineName, pipeLineId,"regionId",FieldTypeEnum.string.name());


        List<MetricsFieldInput> fieldInputList=new ArrayList<>();
        fieldInputList.add(newMetricsFieldInput("packetErrOut", MetricsFieldTypeEnum.number.getValue()));
        fieldInputList.add(newMetricsFieldInput("packetErrIn", MetricsFieldTypeEnum.number.getValue()));
        fieldInputList.add(newMetricsFieldInput("byteIn", MetricsFieldTypeEnum.number.getValue()));
        fieldInputList.add(newMetricsFieldInput("packetOut", MetricsFieldTypeEnum.number.getValue()));
        fieldInputList.add(newMetricsFieldInput("packetIn", MetricsFieldTypeEnum.number.getValue()));
        fieldInputList.add(newMetricsFieldInput("byteOut", MetricsFieldTypeEnum.number.getValue()));

        String metricId=addMetrics(tenantId, pipelineName, pipeLineId, Arrays.asList("hostName","ip","clusterId","regionId","device"),fieldInputList);
        Integer colSize=2;
        String viewId=addViewContainerToDash(dashId,tenantId,pipelineName,1,colSize);
        BaseViewComponentCfg cfg=createBaseCfg("time",Arrays.asList("byteIn"),false,null,true,Arrays.asList("device"));
        addViewComponent(metricId,viewId,tenantId,"Incoming Bytes(KB)", Jackson.toJsonString(cfg),dashId,colSize,1);
        cfg=createBaseCfg("time",Arrays.asList("byteOut"),false,null,true,Arrays.asList("device"));
        addViewComponent(metricId,viewId,tenantId,"Outgoing Bytes(KB)", Jackson.toJsonString(cfg),dashId,colSize,2);

        cfg=createBaseCfg("time",Arrays.asList("packetErrOut","packetErrIn"),false,null,true,Arrays.asList("device"));
        addViewComponent(metricId,viewId,tenantId,"PacketErrOut/PacketErrIn", Jackson.toJsonString(cfg),dashId,colSize,3);

        cfg=createBaseCfg("time",Arrays.asList("packetOut","packetIn"),false,null,true,Arrays.asList("device"));
        addViewComponent(metricId,viewId,tenantId,"PacketOut/PacketIn", Jackson.toJsonString(cfg),dashId,colSize,4);
    }


    private BaseViewComponentCfg createBaseCfg(String x,List<String> y ,Boolean needCompress,Integer compressStep,boolean needGroupBy,List<String> groupByFields){
        BaseViewComponentCfg cfg=new BaseViewComponentCfg();
        cfg.setX(x);
        cfg.setY(y);
        cfg.setTimeFieldName("time");
        cfg.setNeedCompress(needCompress);
        cfg.setTimeStep(compressStep);
        cfg.setNeedGroupBy(needGroupBy);
        cfg.setGroupbyField(groupByFields);
        return cfg;
    }
    @Autowired
    ViewComponentDAO viewComponentDAO;

    @Autowired
    ViewContainerComponentRelaDAO viewContainerComponentRelaDAO;
    private void addViewComponent(String metricId,String viewId, String tenantId,String title,String configs,String dashId,Integer colSize,Integer order) {

        String comId=null;
        List<ViewComponentDO> viewComponentDOS= viewComponentDAO.listByContainerAndTitle(viewId,title);

        if(!CollectionUtils.isEmpty(viewComponentDOS)){
            for(ViewComponentDO viewComponentDO:viewComponentDOS){
                if(viewComponentDO.getTitle().equals(title)){
                    comId=viewComponentDO.getId();
                }
            }
        }

        if(comId ==null ){
            ViewComponentDO viewComponentDO=new ViewComponentDO();
            viewComponentDO.setId(IdUtils.generateId());
            comId=viewComponentDO.getId();
            viewComponentDO.setType(ViewComponentTypeEnum.line.name());
            viewComponentDO.setTenantId(tenantId);
            viewComponentDO.setTitle(title);
            viewComponentDO.setTargetType(ViewComponentTargetTypeEnum.host.name());
            viewComponentDO.setConfigs(configs);
            viewComponentDO.setMetricId(metricId);
            viewComponentDAO.add(viewComponentDO);
        }

        ViewContainerComponentRelaDO viewContainerComponentRelaDO=new ViewContainerComponentRelaDO();
        viewContainerComponentRelaDO.setId(IdUtils.generateId());
        viewContainerComponentRelaDO.setDashboardId(dashId);
        viewContainerComponentRelaDO.setColSize(colSize);
        viewContainerComponentRelaDO.setOrder(order);
        viewContainerComponentRelaDO.setViewContainerId(viewId);
        viewContainerComponentRelaDO.setViewComponentId(comId);
        viewContainerComponentRelaDAO.add(viewContainerComponentRelaDO);

    }

    @Autowired
    us.zoom.infra.dao.service.ViewContainerDAO viewContainerDAO;

    @Autowired
    us.zoom.infra.dao.service.DashboardViewContainerRelaDAO dashboardViewContainerRelaDAO;
    private String addViewContainerToDash(String dashId, String tenantId,String title,Integer order,Integer colSize) {

        List<ViewContainerDO> viewContainerDOS=viewContainerDAO.listByDashboardAndTitle(dashId,title);

        String viewId=null;
        if(!CollectionUtils.isEmpty(viewContainerDOS)){
            for(ViewContainerDO viewContainerDO:viewContainerDOS){
                if(viewContainerDO.getTitle().equals(title)){
                    viewId=viewContainerDO.getId();
                }
            }
        }

        if(null == viewId){
            ViewContainerDO viewContainerDO=new ViewContainerDO();
            viewContainerDO.setId(IdUtils.generateId());
            viewId=viewContainerDO.getId();
            viewContainerDO.setTenantId(tenantId);
            viewContainerDO.setColSize(colSize);
            viewContainerDO.setTitle(title);
            viewContainerDAO.add(viewContainerDO );
        }


        DashboardViewContainerRelaDO dashboardViewContainerRelaDO=new DashboardViewContainerRelaDO();
        dashboardViewContainerRelaDO.setId(IdUtils.generateId());
        dashboardViewContainerRelaDO.setOrder(order);
        dashboardViewContainerRelaDO.setDashboardId(dashId);
        dashboardViewContainerRelaDO.setViewContainerId(viewId);
        dashboardViewContainerRelaDAO.add(dashboardViewContainerRelaDO);
        return viewId;
    }

    private MetricsFieldInput newMetricsFieldInput(String name, int type) {
        MetricsFieldInput metricsFieldInput=new MetricsFieldInput();
        metricsFieldInput.setFieldName(name);
        metricsFieldInput.setFieldType(type);
        return metricsFieldInput;
    }

    @Autowired
    MetricsHandler metricsHandler;
    private String  addMetrics(String tenantId, String pipelineName, String pipeLineId, List<String> tagNames, List<MetricsFieldInput> fieldInputList) {
        MetricsDO metricsDO= metricsHandler.getMetricsByNameOfTenant(tenantId,pipelineName);
        if(null != metricsDO){
            return metricsDO.getId();
        }
        MetricsInput metricsInput = new MetricsInput();
        metricsInput.setUserId(userId);
        metricsInput.setTenantId(tenantId);
        metricsInput.setType(MetricsTypeEnum.ORIGINAL.getValue());
        metricsInput.setMetricsName(pipelineName);
        DataParserPipelineDO pipelineDO=dataParserPipelineHandler.getPipelineById(pipeLineId);
        metricsInput.setCollectorId(pipelineDO.getCollectorId());
        metricsInput.setFromBigData(false);
        metricsInput.setTagNames(tagNames);
        metricsInput.setMetricsFieldList(fieldInputList);
       return metricsService.addMetrics(metricsInput).getData().toString();
    }

    @Autowired
    DataParserPipelineHandler dataParserPipelineHandler;


    @Autowired
    CollectorFieldDAO collectorFieldDAO;
    private void addField(String tenantName, String tenantId, String pipelineName, String pipeLineId,String fieldName,String fieldType) {
        CollectorFieldInput fieldInput=new CollectorFieldInput();
        fieldInput.setUserId(userId);
        fieldInput.setTenantId(tenantId);
        fieldInput.setTargetField(fieldName);
        fieldInput.setSourceField(fieldName);
        fieldInput.setFieldType(fieldType);
        fieldInput.setPipelineId(pipeLineId);
        fieldInput.setPipelineName(pipelineName);
        fieldInput.setDataParserName(tenantName+"_infra_parser");
        DataParserPipelineDO pipelineDO=dataParserPipelineHandler.getPipelineById(pipeLineId);
        if(StringUtils.isNotBlank(pipelineDO.getCollectorId())){
            fieldInput.setCollectorId(pipelineDO.getCollectorId());
            List<CollectorFieldDO> fieldDOS=  collectorFieldDAO.listFieldByCollectorIds(Arrays.asList(pipelineDO.getCollectorId()));

            if(!CollectionUtils.isEmpty(fieldDOS)){
                for(CollectorFieldDO fieldDO:fieldDOS){
                    if(fieldDO.getSourceField().equals(fieldName)){
                        return;
                    }
                }
            }

        }


        dataParserService.addField(fieldInput);
    }


    @Autowired
    DataParserRemapperProcessorHandler dataParserRemapperProcessorHandler;
    private void addRemapper(String tenantId, String dataParseId, String pipeLineId) {
        String name="hostRegionremapper";
        List<DataParserRemapperProcessorDO> dataParserRemapperProcessorDOS= dataParserRemapperProcessorHandler.listByPipelineIds(Arrays.asList(pipeLineId));
        if(!CollectionUtils.isEmpty(dataParserRemapperProcessorDOS)){
            for(DataParserRemapperProcessorDO dataParserRemapperProcessorDO:dataParserRemapperProcessorDOS){
                if(dataParserRemapperProcessorDO.getName().equals(name)){
                    return;
                }
            }
        }
        DataParserRemapperProcessorInput remapper=new DataParserRemapperProcessorInput();
//        remapper.setUserId(userId);
        remapper.setTenantId(tenantId);
        remapper.setDataParserId(dataParseId);
        remapper.setDataParserPipelineId(pipeLineId);
        remapper.setName(name);
        remapper.setSourceFileds("host,zoneId");
        remapper.setTargetFields("hostName,regionId");
        remapper.setOrder(1);
        dataParserService.addRemapperProcessor(remapper);
    }

    private void addResetPid(String tenantId, String dataParseId, String pipeLineId) {
        String name="processResetPidGroovy";
        List<DataParserGroovyProcessorDO> dataParserGroovyProcessorDOS= dataParserGroovyProcessorHandler.listByPipelineIds(Arrays.asList(pipeLineId));
        if(!CollectionUtils.isEmpty(dataParserGroovyProcessorDOS)){
            for(DataParserGroovyProcessorDO dataParserGroovyProcessorDO:dataParserGroovyProcessorDOS){
                if(dataParserGroovyProcessorDO.getName().equals(name)){
                    return;
                }
            }
        }
        DataParserGroovyProcessorInput groovyProcessorInput=new DataParserGroovyProcessorInput();
//        groovyProcessorInput.setUserId(userId);
        groovyProcessorInput.setTenantId(tenantId);
        groovyProcessorInput.setDataParserId(dataParseId);
        groovyProcessorInput.setDataParserPipelineId(pipeLineId);
        groovyProcessorInput.setName(name);
        groovyProcessorInput.setOrder(1);
        groovyProcessorInput.setInvokeFunction("resetPid");
        groovyProcessorInput.setParseRule("def resetPid(Map input) {\n" +
                "  def haveJava=input.processName.toLowerCase().indexOf('java')>=0 ;\n" +
                "  if(input.cpuPercent <20){\n" +
                "         input.put('pid',-1);\n" +
                "      }\n" +
                "}");
        dataParserService.addGroovyProcessor(groovyProcessorInput);
    }


    private String addPipeline(String tenantId,String name,Integer order,String dataParserId) {
        List<DataParserPipelineDO> pipelineDOS= dataParserPipelineHandler.listPipelinesByDataParserId(dataParserId);
        if(!CollectionUtils.isEmpty(pipelineDOS)){
            for(DataParserPipelineDO pipelineDO:pipelineDOS ){
                if(pipelineDO.getName().equals(name)){
                    return  pipelineDO.getId();
                }
            }
        }
        ResponseObject responseObject;
        DataParserPipelineInput pipeline=new DataParserPipelineInput();
        pipeline.setName(name);
        pipeline.setDataParserId(dataParserId);
        pipeline.setFilterRule("infraType=='infra."+name+"'");
        pipeline.setUserId(userId);
        pipeline.setTenantId(tenantId);
        pipeline.setUseStatus(PipelineUseStatusEnum.use.getValue());
        pipeline.setOrder(order);
        responseObject= dataParserService.addPipeline(pipeline);
        return  responseObject.getData().toString();
    }


    @Autowired
    AsyncQueueHandler asyncQueueHandler;
    private String addAq(String tenantName, String tenantId, String aqGroupId) {
        String name=tenantName.toLowerCase()+"_infra_aq";

        List<AsyncQueueDO> asyncQueueDOS= null;
        try {
            asyncQueueDOS = asyncQueueHandler.listAll();
        } catch (Exception e) {
            e.printStackTrace();
        }

        if(!CollectionUtils.isEmpty(asyncQueueDOS)){
            for(AsyncQueueDO asyncQueueDO:asyncQueueDOS){
                if(asyncQueueDO.getAsyncQueueGroupId().equals(aqGroupId) && asyncQueueDO.getName().equals(name)){
                    return asyncQueueDO.getId();
                }
            }
        }

        ResponseObject responseObject;
        AqInput aqInput=new AqInput();
        aqInput.setUserId(userId);
        aqInput.setName(name);
        aqInput.setTenantId(tenantId);
        aqInput.setAsyncQueueGroupId(aqGroupId);
        aqInput.setClusterId("dev");
        aqInput.setRegionId("stable");
        aqInput.setPollSize(20);
        aqInput.setServers("ec2-50-16-132-120.compute-1.amazonaws.com:9093,ec2-3-86-37-86.compute-1.amazonaws.com:9093,ec2-34-227-170-219.compute-1.amazonaws.com:9093");
        aqInput.setSessionTimeOut(30000);
        aqInput.setThreadCount(1);
        aqInput.setTopics(tenantName.toLowerCase()+"_infra");
        aqInput.setGroupId("infra_consumer");
        aqInput.setTruststorePath(" ");
        aqInput.setKeystorePath(" ");
        aqInput.setIdenAlgorithm(" ");
        aqInput.setJaasConfig(" ");
        aqInput.setAuthEncrType(2);
        try {
            responseObject=asyncQueueService.addAq(aqInput);
            return responseObject.getData().toString();
        } catch (Exception e) {
            e.printStackTrace();
        }

        return  null;

    }


    private String addMonitorAq(String tenantName, String tenantId, String aqGroupId) {
        String name=tenantName.toLowerCase()+"_monitor_aq";

        List<AsyncQueueDO> asyncQueueDOS= null;
        try {
            asyncQueueDOS = asyncQueueHandler.listAll();
        } catch (Exception e) {
            e.printStackTrace();
        }

        if(!CollectionUtils.isEmpty(asyncQueueDOS)){
            for(AsyncQueueDO asyncQueueDO:asyncQueueDOS){
                if(asyncQueueDO.getAsyncQueueGroupId().equals(aqGroupId) && asyncQueueDO.getName().equals(name)){
                    return asyncQueueDO.getId();
                }
            }
        }

        ResponseObject responseObject;
        AqInput aqInput=new AqInput();
        aqInput.setUserId(userId);
        aqInput.setName(name);
        aqInput.setTenantId(tenantId);
        aqInput.setAsyncQueueGroupId(aqGroupId);
        aqInput.setClusterId("dev");
        aqInput.setRegionId("stable");
        aqInput.setPollSize(20);
        aqInput.setServers("ec2-50-16-132-120.compute-1.amazonaws.com:9093,ec2-3-86-37-86.compute-1.amazonaws.com:9093,ec2-34-227-170-219.compute-1.amazonaws.com:9093");
        aqInput.setSessionTimeOut(30000);
        aqInput.setThreadCount(1);
        aqInput.setTopics(tenantName.toLowerCase()+"_monitor");
        aqInput.setGroupId("monitor_consumer");
        aqInput.setTruststorePath(" ");
        aqInput.setKeystorePath(" ");
        aqInput.setIdenAlgorithm(" ");
        aqInput.setJaasConfig(" ");
        aqInput.setAuthEncrType(2);
        try {
            responseObject=asyncQueueService.addAq(aqInput);
            return responseObject.getData().toString();

        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Autowired
    AsyncQueueGroupHandler asyncQueueGroupHandler;
    private String addAqGroup(String tenantName, String tenantId) {
        List<AsyncQueueGroupDO>  asyncQueueGroupDOS= asyncQueueGroupHandler.listAllForTenant(tenantId);
        String name=tenantName.toLowerCase()+"_infra_group";

        if(!CollectionUtils.isEmpty(asyncQueueGroupDOS)){
            for(AsyncQueueGroupDO asyncQueueGroupDO:asyncQueueGroupDOS){
                if(asyncQueueGroupDO.getName().equals(name)){
                    return  asyncQueueGroupDO.getId();
                }
            }
        }


        AqGroupInput aqGroupInput=new AqGroupInput();
        aqGroupInput.setUserId(userId);
        aqGroupInput.setTenantId(tenantId);
        aqGroupInput.setCorrelationStrategy("clusterId,regionId");
        aqGroupInput.setName(name);
        ThreadLocalStore.setTenantInfoLocal(tenantId);
        ResponseObject responseObject= asyncQueueGroupService.addAqGroup(aqGroupInput);
        return responseObject.getData().toString();
    }

    private String addMonitorGroup(String tenantName, String tenantId) {
        List<AsyncQueueGroupDO>  asyncQueueGroupDOS= asyncQueueGroupHandler.listAllForTenant(tenantId);
        String name=tenantName.toLowerCase()+"_monitor_group";

        if(!CollectionUtils.isEmpty(asyncQueueGroupDOS)){
            for(AsyncQueueGroupDO asyncQueueGroupDO:asyncQueueGroupDOS){
                if(asyncQueueGroupDO.getName().equals(name)){
                    return  asyncQueueGroupDO.getId();
                }
            }
        }


        AqGroupInput aqGroupInput=new AqGroupInput();
        aqGroupInput.setUserId(userId);
        aqGroupInput.setTenantId(tenantId);
        aqGroupInput.setCorrelationStrategy("clusterId,regionId");
        aqGroupInput.setName(name);
        ResponseObject responseObject= asyncQueueGroupService.addAqGroup(aqGroupInput);
        return responseObject.getData().toString();
    }

    private String  addAndGetTenantId(String tenantName) {
        TenantDO tenantDO= tenantService.getTenantByName(tenantName);
        if(null != tenantDO){
            return tenantDO.getId();
        }
        TenantInput tenantInput=new TenantInput();
        tenantInput.setUserId(userId);
        tenantInput.setName(tenantName);
        ThreadLocalStore.setUserInfoLocal(userId);
        ResponseObject responseObject= tenantService.addTenant(tenantInput);
        String tenantId=responseObject.getData().toString();
        return tenantId;
    }
}
