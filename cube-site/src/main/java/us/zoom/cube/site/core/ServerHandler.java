package us.zoom.cube.site.core;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import us.zoom.cube.lib.common.*;
import us.zoom.cube.lib.utils.ExceptionStackUtils;
import us.zoom.cube.lib.utils.JsonUtils;
import us.zoom.cube.sdk.util.MonitorLogReporter;
import us.zoom.cube.site.api.web.StatusEnum;
import us.zoom.cube.site.biz.SysParaService;
import us.zoom.cube.site.infra.enums.ServerStatusEnum;
import us.zoom.cube.site.infra.utils.IdUtils;
import us.zoom.cube.site.lib.IdListPara;
import us.zoom.cube.site.lib.input.ServerInput;
import us.zoom.cube.site.lib.monitor.LoadServerHeartCache;
import us.zoom.cube.site.lib.query.HostInfoQueryParam;
import us.zoom.cube.site.lib.query.PageQuery;
import us.zoom.cube.site.lib.query.ServerQuery;
import us.zoom.infra.dao.model.*;
import us.zoom.infra.dao.service.*;
import us.zoom.infra.enums.AgentStatusEnum;
import us.zoom.infra.model.HostInfoFilter;
import us.zoom.infra.model.TenantServerQuery;
import us.zoom.infra.thread.NamedThreadFactory;
import us.zoom.infra.utils.*;

import jakarta.annotation.PostConstruct;
import java.text.ParseException;
import java.util.*;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class ServerHandler {

    @Autowired
    private ServerDAO serverDAO;
    @Autowired
    private ServerTenantRelaDAO serverTenantRelaDAO;
    @Autowired
    private HubConnectionDAO hubConnectionDAO;

    @Autowired
    private TenantDAO tenantDAO;
    @Autowired
    private HostInfoDetailDAO hostInfoDetailDAO;

    @Autowired
    private SysParaService sysParaService;

    @Autowired
    private TenantHandler tenantHandler;

    @Autowired
    private ZdcaConfigCheckResultDAO zdcaConfigCheckResultDAO;

    private final AtomicReference<Map<String, ServerHeartInfoDO>> serverHeartInfoMapRef = new AtomicReference<>(new HashMap<>());

    private static final int LOAD_INITIAL_DELAY_IN_SECONDS = 5;

    private static final int LOAD_INTERVAL_IN_SECONDS = 10 * 60;

    private static final String ALL = "all";

    private final Logger monitorLog = LoggerFactory.getLogger("Monitor");

    @PostConstruct
    public void init() {
        Executors.newScheduledThreadPool(1, new NamedThreadFactory("serverHeartInfo Loader")).scheduleAtFixedRate(new Runnable() {
            @Override
            public void run() {
                cacheLoader();
            }
        }, LOAD_INITIAL_DELAY_IN_SECONDS, LOAD_INTERVAL_IN_SECONDS, TimeUnit.SECONDS);
    }

    private void cacheLoader() {
        long startTime = System.currentTimeMillis();
        boolean isSuccess = true;
        LoadServerHeartCache loadServerHeartCache;
        log.info("start cacheServerInfo");
        try {
            List<ServerHeartInfoDO> serverHeartInfoDOList = selectAllHeartInfo();
            if (CollectionUtils.isEmpty(serverHeartInfoDOList)) {
                loadServerHeartCache = new LoadServerHeartCache(IpUtils.getLocalIP(), StatusEnum.FAIL.getStatus(), System.currentTimeMillis() - startTime, "no data");
                MonitorLogReporter.report(monitorLog, loadServerHeartCache);
                return;
            }
            Map<String, ServerHeartInfoDO> serverBaseDOMap = serverHeartInfoDOList.stream()
                    .collect(Collectors.toMap(item -> item.getTenantId() + CommonSplitConstants.SPLIT + item.getHost() + CommonSplitConstants.SPLIT + item.getInstanceId() + CommonSplitConstants.SPLIT + ServerTypeEnum.agent.name(), item -> item,
                            (existingValue, newValue) -> existingValue));
            serverHeartInfoMapRef.set(serverBaseDOMap);
            loadServerHeartCache = new LoadServerHeartCache(IpUtils.getLocalIP(), StatusEnum.SUCCESS.getStatus(), System.currentTimeMillis() - startTime, null);
        } catch (Exception e) {
            isSuccess = false;
            log.error("cacheServerInfo failed.", e);
            loadServerHeartCache = new LoadServerHeartCache(IpUtils.getLocalIP(), StatusEnum.FAIL.getStatus(), System.currentTimeMillis() - startTime, ExceptionStackUtils.parseExceptionStackToString(e));
        }
        log.info("timing cacheServerInfo success={},cost:{}ms", isSuccess, System.currentTimeMillis() - startTime);
        MonitorLogReporter.report(monitorLog, loadServerHeartCache);
    }

    private List<ServerHeartInfoDO> selectAllHeartInfo() {
        List<ServerHeartInfoDO> serverHeartInfoDOList = new ArrayList<>();
        long counts = serverDAO.queryCounts();
        int pageSize = CubeConstants.CUBE_SQL_BATCH_QUERY_SIZE;
        int totalPages = (int) Math.ceil((double) counts / pageSize);
        for (int i = 1; i <= totalPages; i++) {
            serverHeartInfoDOList.addAll(serverDAO.listBatch((long) pageSize * (i - 1), pageSize));
        }
        return serverHeartInfoDOList;
    }

    public ServerHeartInfoDO getServerBaseDO(String uniqueKey) {
        return serverHeartInfoMapRef.get().get(uniqueKey);
    }

    public List<ServerHostTenantDO> listAllServerHostTenant() {
        return serverDAO.listAllServerHostTenant();
    }

    public List<ServerDO> findByNameLikeWithTenant(PageQuery<ServerQuery> pageQuery) {
        ServerQuery serverQuery = pageQuery.getQueryPara();
        if (StringUtils.isNotBlank(serverQuery.getCsp())){
            serverQuery.setCloudType(CloudTypeEnum.fromCsp(serverQuery.getCsp()).getType());
        }
        List<String> ipList = new ArrayList<>();
        if (StringUtils.isNotBlank(serverQuery.getIp())){
            ipList = Arrays.asList(serverQuery.getIp().split(CommonSplitConstants.COMMA_SPLIT));
            ipList.replaceAll(String::trim);
        }
        List<String> ipPublicList = new ArrayList<>();
        if (StringUtils.isNotBlank(serverQuery.getIpPublic())){
            ipPublicList = Arrays.asList(serverQuery.getIpPublic().split(CommonSplitConstants.COMMA_SPLIT));
            ipPublicList.replaceAll(String::trim);
        }
        return serverDAO.findByParam(serverQuery.getName(),
                serverQuery.getType(),
                StringUtils.isBlank(pageQuery.getQueryPara().getServiceId()) || ALL.equals(pageQuery.getQueryPara().getServiceId())
                        ? null : pageQuery.getQueryPara().getServiceId(),
                serverQuery.getStatus(),
                serverQuery.getUnitTag(),
                ipList,
                ipPublicList,
                serverQuery.getClusterId(),
                serverQuery.getRegionId(),
                serverQuery.getEnv(),
                serverQuery.getZoneName(),
                serverQuery.getCloudType(),
                serverQuery.getLogicDc(),
                StringUtils.isBlank(serverQuery.getTunnel()) || ALL.equals(serverQuery.getTunnel()) ? null : serverQuery.getTunnel() ,
                serverQuery.getZcpTenant(),
                serverQuery.getK8sCluster(),
                pageQuery.getPageSize() * (pageQuery.getPageIndex() - 1), pageQuery.getPageSize());
    }

    public int getCountByNameLikeWithTenant(PageQuery<ServerQuery> pageQuery) {
        ServerQuery serverQuery = pageQuery.getQueryPara();
        if (StringUtils.isNotBlank(serverQuery.getCsp())){
            serverQuery.setCloudType(CloudTypeEnum.fromCsp(serverQuery.getCsp()).getType());
        }
        List<String> ipList = new ArrayList<>();
        if (StringUtils.isNotBlank(serverQuery.getIp())){
            ipList = Arrays.asList(serverQuery.getIp().split(CommonSplitConstants.COMMA_SPLIT));
            ipList.replaceAll(String::trim);
        }
        List<String> ipPublicList = new ArrayList<>();
        if (StringUtils.isNotBlank(serverQuery.getIpPublic())){
            ipPublicList = Arrays.asList(serverQuery.getIpPublic().split(CommonSplitConstants.COMMA_SPLIT));
            ipPublicList.replaceAll(String::trim);
        }
        return serverDAO.getCountByParam(serverQuery.getName(),
                serverQuery.getType(),
                StringUtils.isBlank(pageQuery.getQueryPara().getServiceId()) || ALL.equals(pageQuery.getQueryPara().getServiceId())
                        ? null : pageQuery.getQueryPara().getServiceId(),
                serverQuery.getStatus(),
                serverQuery.getUnitTag(),
                ipList,
                ipPublicList,
                serverQuery.getClusterId(),
                serverQuery.getRegionId(),
                serverQuery.getEnv(),
                serverQuery.getZoneName(),
                serverQuery.getCloudType(),
                serverQuery.getLogicDc(),
                serverQuery.getTunnel(),
                serverQuery.getZcpTenant(),
                serverQuery.getK8sCluster());
    }


    public List<ServerDO> findByNameLike(PageQuery<ServerQuery> pageQuery) {
        ServerQuery serverQuery = pageQuery.getQueryPara();
        return serverDAO.findByNameLikeNoTenant(serverQuery.getIp(),
                serverQuery.getClusterId(),
                serverQuery.getRegionId(),
                serverQuery.getName(),
                serverQuery.getType(),
                serverQuery.getStatus(),
                serverQuery.getUnitTag(),
                serverQuery.getVersion(),
                serverQuery.getEnv(),
                pageQuery.getPageSize() * (pageQuery.getPageIndex() - 1), pageQuery.getPageSize());
    }

    public List<ServerDO> findByTypeAndStatus(String type,Integer status){
        return serverDAO.findByTypeAndStatus(type,  status);
    }

    public int getCountByNameLike(PageQuery<ServerQuery> pageQuery) {
        ServerQuery serverQuery = pageQuery.getQueryPara();
        return serverDAO.getCountByNameLikeNoTenant(serverQuery.getIp(),
                serverQuery.getClusterId(),
                serverQuery.getRegionId(),
                serverQuery.getName(), serverQuery.getType(), serverQuery.getStatus(), serverQuery.getUnitTag(), serverQuery.getVersion(),
                serverQuery.getEnv());
    }

    @Transactional(rollbackFor = Exception.class)
    public String addServer(ServerInput serverInput) {
        ServerDO serverDO = new ServerDO();
        BeanUtils.copyProperties(serverInput, serverDO);
        serverDO.setId(IdUtils.generateId());
        serverDO.setUseZip(UseZipEnum.NO.getType());
        fixNullValue(serverDO);
        serverDAO.addServer(serverDO);
        return serverDO.getId();
    }

    private void fixNullValue(ServerDO serverDO) {
        if (null == serverDO.getServiceId()) {
            serverDO.setServiceId("");
        }
        if (null == serverDO.getUpStatusTime()) {
            try {
                serverDO.setUpStatusTime(DateUtils.parseDate(DateUtils.DATE_DEFAULT, DateUtils.FORMART1));
            } catch (ParseException e) {
                log.error("format date string error,", e);
            }
        }
        if (null == serverDO.getChannelType()) {
            serverDO.setChannelType("");
        }
        if (null == serverDO.getJmxPort()) {
            serverDO.setJmxPort("");
        }
        if(null == serverDO.getUnitTag()){
            serverDO.setUnitTag("");
        }
    }

    public void editServer(ServerDO serverDO) {
        fixNullValue(serverDO);
        serverDAO.editServer(serverDO);
    }

    public ServerDO getServerById(String id) {
        return serverDAO.getServerById(id);
    }

    public void delServer(String id) {
        serverDAO.delServer(id);
        hostInfoDetailDAO.deleteByServerId(id);
        zdcaConfigCheckResultDAO.deleteByServerId(id);
    }

    public void batchDelServer(List<String> ids) {
        serverDAO.batchDeleteByIds(ids);
        hostInfoDetailDAO.batchDeleteByServerIds(ids);
        zdcaConfigCheckResultDAO.batchDeleteByServerIds(ids);
    }

    public ServerDO getServerByHost(String host, String serverType) {
        return serverDAO.getServerByHost(host, serverType);
    }

    public void updateUpStatusTimByHostType(String host,String type) {
        serverDAO.updateUpStatusTimByHostType(host,type);
    }

    public void updateUpStatusTimeByIpType(String ip,String type) {
        serverDAO.updateUpStatusTimeByIpType(ip,type);
    }

    public void updateUpStatusTimeAndVersionByIpType(String ip, String version, String type) {
        serverDAO.updateUpStatusTimeAndVersionByIpType(ip, version, type);
    }

    public void addServerTenantRela(ServerInput serverInput, String serverId) {
        ServerTenantRelaDO relaDO = serverTenantRelaDAO.getRela(serverId, serverInput.getTenantId(), serverInput.getServiceId());
        if (null != relaDO) {
            return;
        }
        relaDO = new ServerTenantRelaDO();
        relaDO.setId(IdUtils.generateId());
        relaDO.setServerId(serverId);
        relaDO.setTenantId(serverInput.getTenantId());
        relaDO.setServiceId(serverInput.getServiceId());
        serverTenantRelaDAO.addServerTenantRela(relaDO);
    }

    public ServerDO geByTenantAndHost(String tenantId, String host,String type) {
        return serverDAO.geByTenantAndHost(tenantId, host,type);
    }

    public List<ServerDO> listServerByClusterAndRegion(String clusterId, String regionId, String type) {
        return serverDAO.listServerByClusterAndRegion(clusterId, regionId, type);
    }

    public void saveOrUpdateHubConn(String agentHost, String agentIp, String hubIp, HubChannelType hubChannelType) {
        HubConnectionDO hubConnectionDO = hubConnectionDAO.getConnByAgentHostAndAgentIp(agentHost, agentIp, hubChannelType.name());
        if (hubConnectionDO == null) {
            hubConnectionDAO.addConnection(agentHost, agentIp, hubIp, IdUtils.generateId(), hubChannelType.name());
        } else {
            hubConnectionDAO.updateConnection(agentHost, agentIp, hubIp, hubChannelType.name());
        }
    }

    public void adjustData(ServerDO serverDO){
        if(StringUtils.isBlank(serverDO.getAgentKey())){
            serverDO.setAgentKey("");
        }

        if(StringUtils.isBlank(serverDO.getServiceId())){
            serverDO.setServiceId("");
        }

        if(StringUtils.isBlank(serverDO.getJmxPort())){
            serverDO.setJmxPort("");
        }

        if(StringUtils.isBlank(serverDO.getChannelType())){
            serverDO.setChannelType(HubChannelType.normal.name());
        }

        if(StringUtils.isBlank(serverDO.getUnitTag())){
            serverDO.setUnitTag("");
        }

        if(serverDO.getUseZip() == null){
            serverDO.setUseZip(UseZipEnum.NO.getType());
        }

        if(serverDO.getCloudType() == null){
            serverDO.setCloudType(CloudTypeEnum.COLO.getType());
        }

        if (StringUtils.isBlank(serverDO.getDmidecodeMd5())){
            serverDO.setDmidecodeMd5("");
        }

        if (StringUtils.isBlank(serverDO.getLogicDc())){
            serverDO.setLogicDc("");
        }

        if (serverDO.getStatus()==null){
            serverDO.setStatus(AgentStatusEnum.healthy.getCode());
        }
    }
    public List<HubConnectionDO> listHubConnByHubIps(List<String> hubIps) {
        if (CollectionUtils.isEmpty(hubIps)) {
            return Collections.emptyList();
        }
        return hubConnectionDAO.listHubConnByHubIps(hubIps);
    }

    public void delConnByAgentHostAndAgentIp(String agentHost, String agentIp, HubChannelType hubChannelType) {
        hubConnectionDAO.delConnByAgentHostAndAgentIp(agentHost, agentIp, hubChannelType.name());
    }

    public void clearOverDueConn() {
        Long overDueDate = DateUtils.addMinute(new Date(), -30);
        hubConnectionDAO.clearOverDueConn(new Date(overDueDate));
    }

    public List<String> listClusterIds(String tenantId,String type) {
        return  this.serverDAO.listClusterIds(tenantId,type);
    }

    public List<String> listRegionIds(String tenantId, String type, String clusterId) {
        return  this.serverDAO.listRegionIds(tenantId,type,clusterId);
    }

    public List<String> listHosts(String tenantId, String type, String clusterId, String regionId) {
        return  this.serverDAO.listHosts(tenantId,type,clusterId,regionId);
    }

    public ServerDO getHubServerByIp(String ip) {
        return serverDAO.getHubServerByIp(ServerTypeEnum.hub.name(), ip);
    }

    public ServerDO getByIpAndType(String ip, String type) {
        return serverDAO.getHubServerByIp(type, ip);
    }

    public List<ServerDO> listServerNoHeart(List<String> tenantIds, Integer heartCheckIntenalInMinutes, ServerTypeEnum serverTypeEnum) {
        long upStatusTimeBefore=DateUtils.addMinute(new Date(),- heartCheckIntenalInMinutes);
        return  this.serverDAO.listServerNoHeartBeforeTime(tenantIds,new Date(upStatusTimeBefore),serverTypeEnum.name());
    }


    public List<ServerDO> listServerNoHeart(Integer heartCheckIntenalInMinutes,ServerTypeEnum serverTypeEnum) {
        long upStatusTimeBefore=DateUtils.addMinute(new Date(),- heartCheckIntenalInMinutes);
        return  this.serverDAO.listServerNoHeartBeforeTimeByType(new Date(upStatusTimeBefore),serverTypeEnum.name());
    }

    public List<ServerHeartInfoDO> listServer(List<TenantServerQuery> serverQueries) {
        return  serverDAO.listServer(serverQueries);
    }

    private Integer batchUpdateSize = 400;

    public void bachUpdateStatus(List<String> ids, ServerStatusEnum serverStatusEnum) {
        if (ids.size() > batchUpdateSize) {
            List<List<String>> idArray = ListUtils.partition(ids, batchUpdateSize);
            for (List<String> idsOne : idArray) {
                serverDAO.bachUpdateStatus(idsOne, serverStatusEnum.getCode());
            }
        } else {
            serverDAO.bachUpdateStatus(ids, serverStatusEnum.getCode());
        }
    }

    public void offlineServer(Integer heartCheckIntenalInMinutes){
        Date maxHeartDate=new Date(DateUtils.addMinute(new Date(),-heartCheckIntenalInMinutes)) ;
        serverDAO.offlineServer(maxHeartDate);
    }

    public void batchAdd(List<ServerDO> toAddServers) {
        if(CollectionUtils.isEmpty(toAddServers)){
            return;
        }
        toAddServers.forEach(this::adjustData);
        List<List<ServerDO>> toAddServersBatches = ListUtils.partition(toAddServers, CubeConstants.CUBE_SQL_BATCH_ADD_SIZE);
        toAddServersBatches.forEach(item -> {
            try {
                serverDAO.batchAdd(item);
            } catch (Exception e) {
                log.error("agent heart batchAdd error, servers:{}", JsonUtils.toJsonStringIgnoreExp(item), e);
            }
        });
    }

    public void batchAddOrUpdate(List<ServerDO> servers) {
        if (CollectionUtils.isEmpty(servers)) {
            return;
        }
        servers.forEach(this::adjustData);
        serverDAO.batchAddOrUpdate(servers);
    }

    public void batchUpdateInstanceInfo(List<ServerDO> toUpdateServer) {
        if(CollectionUtils.isEmpty(toUpdateServer)){
            return;
        }
        List<List<ServerDO>> toUpdateServersBatches = ListUtils.partition(toUpdateServer, CubeConstants.CUBE_SQL_BATCH_UPDATE_SIZE);
        toUpdateServersBatches.forEach(item->{
            serverDAO.batchUpdateInstanceInfo(item);
        });
    }

    public void batchUpdateUptimeAndStatus(List<String> ids) {
        if(CollectionUtils.isEmpty(ids)){
            return;
        }
        List<List<String>> idBatches =  ListUtils.partition(ids, CubeConstants.CUBE_SQL_BATCH_UPDATE_SIZE);
        idBatches.forEach(item->{
            serverDAO.batchUpdateUptimeAndStatus(item);
        });
    }


    public List<ServerDO> listServerNoHeartLimitCount(List<String> tenantIds, ServerTypeEnum serverTypeEnum, Integer maxCount) {
        if (ServerTypeEnum.agent == serverTypeEnum) {
            List<ServerDO> servers = new ArrayList<>();
            for (String tenantId : tenantIds) {
                List<ServerDO> items = this.serverDAO.listServerNoHeartLimitCount(tenantId, serverTypeEnum.name(), maxCount);
                if (CollectionUtils.isNotEmpty(items)) {
                    servers.addAll(items);
                }
            }
            return servers;
        } else {
            List<ServerDO> serverDOS = this.serverDAO.listManageServerNoHeartLimitCount(serverTypeEnum.name(), maxCount);
            if (CollectionUtils.isNotEmpty(serverDOS)) {
                TenantDO tenantDO = tenantDAO.getTenantByName(sysParaService.getManagerService());
                serverDOS.forEach(item -> item.setTenantId(tenantDO.getId()));
            }
            return serverDOS;
        }
    }

    public int delOverdueServer(Integer serverOverdueTimeInDay,ServerTypeEnum serverTypeEnum,List<String> tenantIds) {
        Date overDueTime = new Date(DateUtils.addDay(new Date(),-serverOverdueTimeInDay));
        return  serverDAO.delOverdueServer(overDueTime,serverTypeEnum.name(),tenantIds);
    }

    public int delOverdueServerForSomeTenant(Map<String,Integer> serviceDelInfoMap,ServerTypeEnum serverTypeEnum) {
        if(MapUtils.isEmpty(serviceDelInfoMap)){
            return 0;
        }
        List<ServiceDelDateModel> serviceDelDates = new ArrayList<>();
        serviceDelInfoMap.forEach((tenantId,overdueDay)->{
            Date date = new Date( DateUtils.addDay(new Date(),- overdueDay));
            serviceDelDates.add(new ServiceDelDateModel(tenantId,date));
        });
      return   serverDAO.delOverdueServerForSomeTenant(serviceDelDates,serverTypeEnum.name());
    }

    public List<ServerDO> listCountByUnitTag(String unitTag, String type) {
        return serverDAO.listCountByUnitTag(unitTag,type);
    }

    public List<ServerDO> listHubServer(String env) {
        return serverDAO.listHubServer(env);
    }


    public ServerDO selectServerByParam(HostInfoQueryParam hostInfoQueryParam) {
        String tenantId = tenantDAO.getTenantByName(hostInfoQueryParam.getService()).getId();
        return serverDAO.selectServerByParam(hostInfoQueryParam.getHost(),hostInfoQueryParam.getInstanceId(),hostInfoQueryParam.getIp(),tenantId);
    }

    public List<ServerDO> listServerByParam(List<HostInfoFilter> hostInfoFilters, List<String> tenantIdFilterList, String ip, String host, String clusterId, String regionId, String zoneName, String instanceId, int pageIndex, int pageSize) {
        hostInfoFilters = validateHostInfo(hostInfoFilters);
        return serverDAO.listServerByParam(hostInfoFilters, hostInfoFilters.size(), tenantIdFilterList, ip, host, clusterId, regionId, zoneName, instanceId, pageIndex, pageSize);
    }

    public int countServerByParam(List<HostInfoFilter> hostInfoFilters, List<String> tenantIdFilterList, String ip, String host, String clusterId, String regionId, String zoneName, String instanceId) {
        hostInfoFilters = validateHostInfo(hostInfoFilters);
        return serverDAO.countServerByParam(hostInfoFilters, hostInfoFilters.size(), tenantIdFilterList, ip, host, clusterId, regionId, zoneName, instanceId);
    }

    public List<ServerCountDO> queryClassifyResult(List<HostInfoFilter> hostInfoFilters, List<String> tenantIdFilterList, String ip, String host, String clusterId, String regionId, String zoneName, String instanceId, String groupByKey) {
        hostInfoFilters = validateHostInfo(hostInfoFilters);
        return serverDAO.queryClassifyResult(hostInfoFilters, hostInfoFilters.size(), tenantIdFilterList, ip, host, clusterId, regionId, zoneName, instanceId, groupByKey);
    }

    private List<HostInfoFilter> validateHostInfo(List<HostInfoFilter> hostInfoFilters) {
        if (hostInfoFilters==null){
            return new ArrayList<>();
        }
        hostInfoFilters = hostInfoFilters.stream()
                .filter(filter -> filter.getHostInfoType() != null && filter.getField() != null && filter.getValue() != null)
                .collect(Collectors.toList());
        return hostInfoFilters;
    }

    public void batchDeleteServer(IdListPara idListPara) {
        if (CollectionUtils.isEmpty(idListPara.getIds())){
            return ;
        }
        batchDelServer(idListPara.getIds());
    }


    public List<ServerDO> listServerByIds(List<String> ids,String type) {
        if (CollectionUtils.isEmpty(ids)){
            return new ArrayList<>();
        }
        return serverDAO.listServerByIds(ids,type);
    }

    public List<ServerDO> selectByParam(ServerQuery serverQuery) {
        PageQuery<ServerQuery> pageQuery = new PageQuery<>();
        pageQuery.setQueryPara(serverQuery);
        int totalQueryCount = getCountByNameLikeWithTenant(pageQuery);
        pageQuery.setPageSize(totalQueryCount);
        return findByNameLikeWithTenant(pageQuery);
    }

    public void batchDeleteServerByParam(String tenantId, List<String> publicIpList) {
        if (CollectionUtils.isEmpty(publicIpList)){
            return;
        }
        List<ServerDO> serverDOList = serverDAO.findByParam(null, ServerTypeEnum.agent.name(), tenantId, null, null, null, publicIpList, null, null, null, null, null, null,  null,null, null, 0, publicIpList.size());
        if (CollectionUtils.isEmpty(serverDOList)){
            return;
        }
        publicIpList = serverDOList.stream().map(ServerDO::getIpPublic).collect(Collectors.toList());
        TenantDO tenantAdmin = tenantDAO.getTenantByName(CubeConstants.SYSTEMS_ADMINISTRATION);
        if (!tenantId.equals(tenantAdmin.getId())) {
            serverDOList.addAll(serverDAO.findByParam(null, ServerTypeEnum.agent.name(), tenantAdmin.getId(), null, null, null, publicIpList, null, null, null, null, null, null, null, null, null, 0, publicIpList.size()));
        }
        List<String> serverIds = Instance.ofNullable(serverDOList).stream().map(ServerDO::getId).collect(Collectors.toList());
        batchDelServer(serverIds);
    }
}
