package us.zoom.cube.site.lib.monitor;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import us.zoom.cube.sdk.annotation.CubeMonitorLog;
import us.zoom.cube.sdk.annotation.Field;
import us.zoom.cube.sdk.annotation.Tag;
import us.zoom.cube.sdk.util.MonitorLogReporter;

@CubeMonitorLog(measure = "SitePerformanceMonitorMetrics")
@Data
public class SitePerformanceMonitorLogEntity {
    @Tag
    private String env;
    @Tag
    private String ip;
    @Tag
    private String tid;
    @Tag
    private String requestId;
    @Tag
    private String type;
    @Tag
    private String phase;
    @Field
    private long cost;
    @Field
    private String error;
    @Field
    private String context;

    private Logger logger;

    private Long begin;


    public SitePerformanceMonitorLogEntity(Logger logger, Long begin, String env, String ip, String tid, String requestId, String type, String context) {
        this.logger = logger;
        this.begin = begin;
        this.env = env;
        this.ip = ip;
        this.tid = tid;
        this.requestId = requestId;
        this.type = type;
        this.context = context;
    }

    public void printPhase(String phase){
        if(StringUtils.isNotEmpty(phase)){
            this.phase = phase;
        }
        this.cost = System.currentTimeMillis() - begin;
        MonitorLogReporter.report(this.logger, this);
    }

}
