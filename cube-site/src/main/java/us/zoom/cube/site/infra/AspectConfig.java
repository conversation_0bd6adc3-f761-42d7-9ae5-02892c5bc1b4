package us.zoom.cube.site.infra;

import com.google.common.collect.Sets;
import com.zoom.op.monitor.domain.BaseEntity;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import us.zoom.cube.site.api.intercept.AuthInterceptor;
import us.zoom.cube.site.biz.AuthService;

import java.util.Date;
import java.util.Set;


@Aspect
@Configuration
public class AspectConfig {

    private AuthService authService;

    @Autowired
    public void setAuthService(AuthService authService) {
        this.authService = authService;
    }


    public static final Set<String> readMethodPrefixes =
            Sets.newHashSet("get", "list", "page", "query", "find", "search");

    @Before("execution(public * us.zoom.cube.site.api.web.alarm.*Controller.*(..))")
    public void baseFieldAutowire(JoinPoint joinPoint) {
        String methodName = joinPoint.getSignature().getName();
        if (writeMethods.contains(methodName)) {

//            authService.mustTenantAdminOrAdmin(AuthInterceptor.getUserId(), AuthInterceptor.getTenantId());
            authService.checkAuth(AuthInterceptor.getUserId(),AuthInterceptor.getTenantId(),AuthInterceptor.getApiPath());
        }

        if (joinPoint.getArgs().length == 0) return;

        Boolean isRead =
                readMethodPrefixes.stream()
                        .map(methodName::startsWith)
                        .reduce((b1, b2) -> b1 || b2).orElse(Boolean.FALSE);

        if (isRead) return;

        Object entity = joinPoint.getArgs()[0];
        if (entity instanceof BaseEntity) {

            BaseEntity<?> baseEntity = (BaseEntity<?>) entity;
            if (baseEntity.getId() == null) {
                baseEntity.setCreateTime(new Date());
            }
            baseEntity.setUserId(AuthInterceptor.getUserId());
            baseEntity.setTenantId(AuthInterceptor.getTenantId());
            baseEntity.setModifyTime(new Date());
        }
    }

    public static final Set<String> writeMethods = Sets.newHashSet("save", "update", "delete");

    /*
    @Before("execution(public * us.zoom.cube.site.biz.alarm.*Service.*(..))")
    public void permissionCheck(JoinPoint joinPoint) {

        String methodName = joinPoint.getSignature().getName();

        if (writeMethods.contains(methodName)) {

//            authService.mustTenantAdminOrAdmin(AuthInterceptor.getUserId(), AuthInterceptor.getTenantId());
            authService.checkAuth(AuthInterceptor.getUserId(),AuthInterceptor.getTenantId(),AuthInterceptor.getApiPath());
        }
    }
    */
}
