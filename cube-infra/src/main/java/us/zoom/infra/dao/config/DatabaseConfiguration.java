package us.zoom.infra.dao.config;

import org.apache.ibatis.plugin.Interceptor;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.transaction.support.TransactionTemplate;
import us.zoom.cube.lib.common.TaskStatusEnum;
import us.zoom.infra.dao.config.handler.EnumNameHandler;

import javax.sql.DataSource;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2019/11/20 3:20
 */
@Configuration
@EnableTransactionManagement
@MapperScan(basePackages = "us.zoom.infra.dao.service", sqlSessionFactoryRef = "myBatisSqlSessionFactory", annotationClass = Repository.class)
@ConditionalOnProperty(name = "mysql.datasource.enabled", havingValue = "true", matchIfMissing = true)
public class DatabaseConfiguration {

    @Autowired
    @Qualifier("dynamicDataSource")
    private DataSource dynamicDataSource;

    @Autowired
    @Qualifier("metricTransactionManager")
    private PlatformTransactionManager platformTransactionManager;

    private static final List<Class<? extends Enum>> ENUM_CLASSES = Arrays.asList(
            TaskStatusEnum.class
    );

    @Bean(value = "myBatisSqlSessionFactory") @Primary
    public SqlSessionFactoryBean sqlSessionFactoryBean(@Autowired Interceptor mybatisInterceptor,
                                                       @Autowired @Qualifier("pageInterceptor") Interceptor pageInterceptor) throws Exception {
        SqlSessionFactoryBean sqlSessionFactoryBean = new SqlSessionFactoryBean();
        org.apache.ibatis.session.Configuration configuration = new org.apache.ibatis.session.Configuration();
        configuration.setMapUnderscoreToCamelCase(true);
        configuration.addInterceptor(mybatisInterceptor);
        configuration.addInterceptor(pageInterceptor);
        configuration.setDefaultEnumTypeHandler(org.apache.ibatis.type.EnumOrdinalTypeHandler.class);
        ENUM_CLASSES.forEach(enumClass -> 
            configuration.getTypeHandlerRegistry().register(enumClass, new EnumNameHandler(enumClass))
        );
        configuration.setDefaultScriptingLanguage(TraceIdPropagatingLanguageDriver.class);
        sqlSessionFactoryBean.setConfiguration(configuration);
        sqlSessionFactoryBean.setDataSource(dynamicDataSource);
        return sqlSessionFactoryBean;
    }

    @Bean
    public TransactionTemplate transactionTemplate(){
        return new TransactionTemplate(platformTransactionManager);
    }


    @Bean
    public TransactionDefinition transactionDefinition() throws Exception {
        DefaultTransactionDefinition definition = new DefaultTransactionDefinition();
        definition.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRED);
        return definition;
    }

}