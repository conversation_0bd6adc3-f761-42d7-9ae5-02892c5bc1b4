package us.zoom.cube.site.lib.output.metric;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;
import us.zoom.infra.model.aggregation.AggQueryItem;
import us.zoom.infra.utils.Instance;
import us.zoom.infra.utils.StringBuilderUtils;

import java.util.ArrayList;
import java.util.List;

public class MetricItem {
    private static final Logger LOG= LoggerFactory.getLogger(MetricItem.class.getName());

    private List<AggQueryItem> aggItems;
    private List<String> groupFields;


    public List<AggQueryItem> getAggItems() {
        return aggItems;
    }

    public void setAggItems(List<AggQueryItem> aggItems) {
        this.aggItems = aggItems;
    }

    public List<String> getGroupFields() {
        return groupFields;
    }

    public void setGroupFields(List<String> groupFields) {
        this.groupFields = groupFields;
    }


    @Override
    public String toString() {
        StringBuilder result=new StringBuilder();
        result.append("{\"groupFields\":[");
        groupFields.forEach(field->result.append("\"").append(field).append("\","));
        StringBuilderUtils.deleteLastComma(result);
        result.append("],\"items\":[");
        aggItems.forEach(item->result.append(item.toString()).append(","));
        StringBuilderUtils.deleteLastComma(result);
        result.append("]}");
        return result.toString();
    }

    public G2PlotOut outG2PlotObj(){
        G2PlotOut result=new G2PlotOut();
//        result.setGroupFields(Instance.ofNullable(groupFields).stream().map(item->new ValueText(item,item)).collect(Collectors.toList()));
        result.setGroupFields(groupFields);
        result.setItems(new ArrayList<>());
        Instance.ofNullable(aggItems).forEach(item->{
           Instance.ofNullable( item.getPeriodDatas()).forEach((k,v)->{
               ItemOut itemOut=new ItemOut();
                   itemOut.setPeriodTime(item.getPeriodTime());
                   itemOut.setType(k);
                   itemOut.setValue(v);
                   result.getItems().add(itemOut);
//               } catch (ParseException e) {
//                   LOG.error("parse date error!",e);
//               }
            });
        });
        return  result;
    }


    public String outG2Plot(){
        StringBuilder result=new StringBuilder();
        result.append("{");
        if(!CollectionUtils.isEmpty(groupFields)){
            result.append("\"groupFields\":[");
            Instance.ofNullable(groupFields).forEach(field->result.append("\"").append(field).append("\","));
            StringBuilderUtils.deleteLastComma(result);
            result.append("]");
        }

        result.append(",\"items\":[");
        Instance.ofNullable(aggItems).forEach(item->{
            String value=item.outG2Plot();
            if(StringUtils.isNotBlank(value)){
                result.append(value).append(",");
            }
        });
        StringBuilderUtils.deleteLastComma(result);
        result.append("]");
        result.append("}");


        return result.toString();
    }
}
