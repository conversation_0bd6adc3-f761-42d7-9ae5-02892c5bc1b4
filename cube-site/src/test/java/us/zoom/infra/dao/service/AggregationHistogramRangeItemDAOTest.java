package us.zoom.infra.dao.service;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import us.zoom.cube.site.BaseSpringTest;
import us.zoom.infra.dao.model.AggregationHistogramRangeItemDO;
import us.zoom.cube.lib.utils.JsonUtils;

import java.util.List;

public class AggregationHistogramRangeItemDAOTest extends BaseSpringTest {


    @Autowired
    AggregationHistogramRangeItemDAO  aggregationHistogramRangeItemDAO;

    @Test
    public void insert(){
        String id = "1234";
        AggregationHistogramRangeItemDO aggregationHistogramRangeItemDO = new AggregationHistogramRangeItemDO();
        aggregationHistogramRangeItemDO.setId(id);
        aggregationHistogramRangeItemDO.setMetricsAggregationRuleId("ceb140bc-3a96-4332-9bee-d0b33cdac84a");
        aggregationHistogramRangeItemDO.setLowerLimit(10.0);
        aggregationHistogramRangeItemDO.setUpperLimit(20.0);
        aggregationHistogramRangeItemDO.setLowerIntervalNotation(0);
        aggregationHistogramRangeItemDO.setUpperIntervalNotation(1);
        aggregationHistogramRangeItemDO.setTargetFieldId("target1234");
        int insertRes = aggregationHistogramRangeItemDAO.insertAggregationHistogramRange(aggregationHistogramRangeItemDO);
        System.out.println("insertRes = " + insertRes);

        aggregationHistogramRangeItemDO.setId("4567");
        aggregationHistogramRangeItemDAO.insertAggregationHistogramRange(aggregationHistogramRangeItemDO);

    }

    @Test
    public void crud(){
        String id = "1234";
        AggregationHistogramRangeItemDO aggregationHistogramRangeItemDO = new AggregationHistogramRangeItemDO();
        aggregationHistogramRangeItemDO.setId(id);
        aggregationHistogramRangeItemDO.setMetricsAggregationRuleId("rule1234");
        aggregationHistogramRangeItemDO.setLowerLimit(10.0);
        aggregationHistogramRangeItemDO.setUpperLimit(20.0);
        aggregationHistogramRangeItemDO.setLowerIntervalNotation(0);
        aggregationHistogramRangeItemDO.setUpperIntervalNotation(1);
        aggregationHistogramRangeItemDO.setTargetFieldId("target1234");
        int insertRes = aggregationHistogramRangeItemDAO.insertAggregationHistogramRange(aggregationHistogramRangeItemDO);
        System.out.println("insertRes = " + insertRes);


        AggregationHistogramRangeItemDO queryed = aggregationHistogramRangeItemDAO.getById(id);
        System.out.println("queryed = " + JsonUtils.toJsonString(queryed));

        Assertions.assertNotNull(queryed);

        List<AggregationHistogramRangeItemDO> listed = aggregationHistogramRangeItemDAO.listAll();
        System.out.println("listed = " + JsonUtils.toJsonString(listed));
        aggregationHistogramRangeItemDAO.deleteById(id);

        AggregationHistogramRangeItemDO queryed1 = aggregationHistogramRangeItemDAO.getById(id);
        Assertions.assertNull(queryed1);
    }

}
