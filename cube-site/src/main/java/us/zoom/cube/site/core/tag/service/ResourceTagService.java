package us.zoom.cube.site.core.tag.service;

import com.google.common.collect.Lists;
import com.zoom.op.monitor.dao.alarm.AlarmDefinitionDao;
import com.zoom.op.monitor.domain.TagInputDo;
import com.zoom.op.monitor.domain.alarm.AlarmDefinition;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.site.api.intercept.AuthInterceptor;
import us.zoom.cube.site.core.AdCfgHandler;
import us.zoom.cube.site.core.model.ad.AdCfgDataOut;
import us.zoom.cube.site.core.tag.ResourceTypeConstant;
import us.zoom.cube.site.core.tag.TagConvertUtil;
import us.zoom.cube.site.core.tag.TagPermissionUtil;
import us.zoom.cube.site.infra.utils.IdUtils;
import us.zoom.cube.site.lib.ResponseObject;
import us.zoom.cube.site.lib.input.tag.input.ResourceTagInfoInput;
import us.zoom.cube.site.lib.output.tag.ResourceTagOut;
import us.zoom.cube.site.lib.input.tag.ResourceTagQuery;
import us.zoom.cube.site.lib.input.tag.input.ResourcesTagsInfoInput;
import us.zoom.cube.site.lib.output.PageResult;
import us.zoom.cube.site.lib.output.tag.TagInfoOut;
import us.zoom.cube.site.lib.query.PageQuery;
import us.zoom.infra.dao.model.*;
import us.zoom.infra.dao.service.*;
import us.zoom.infra.thread.ThreadLocalStore;

import java.util.*;
import java.util.stream.Collectors;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @date 2024/12/31 16:33
 */
@Component
@Slf4j
public class ResourceTagService {

    @Autowired
    private ResourceTagDAO resourceTagDAO;

    @Autowired
    private TagDAO tagDAO;

    @Autowired
    private TagTypeDAO tagTypeDAO;

    @Autowired
    private MetricsDAO metricsDAO;

    @Autowired
    private AlarmDefinitionDao alarmDefinitionDao;

    @Autowired
    private AdCfgHandler adCfgHandler;

    @Autowired
    private TagPermissionUtil tagPermissionUtil;

    @Autowired
    private TenantDAO tenantDAO;
    @Autowired
    private TagTypeScopeDAO tagTypeScopeDAO;

    @Transactional(rollbackFor = Exception.class)
    public ResponseObject addResourceTag(ResourceTagInfoInput resourceTagInfoInput) {

        TagNameDo byId = tagDAO.findById(resourceTagInfoInput.getTagId());
        if (byId == null) {
            throw new RuntimeException("label not found");
        }

        ResourceTagDo resourceTagDo = ResourceTagDo.builder()
                .tagId(resourceTagInfoInput.getTagId())
                .resource(resourceTagInfoInput.getResource())
                .resourceType(resourceTagInfoInput.getResourceType())
                .tenantId(resourceTagInfoInput.getTenantId())
                .id(IdUtils.generateId())
                .creator(resourceTagInfoInput.getUserId())
                .modifier(resourceTagInfoInput.getUserId())
                .build();

        try{
            resourceTagDAO.add(resourceTagDo);
        }catch (DuplicateKeyException e){
            //ignore it
        }
        return ResponseObject.success(resourceTagDo.getId());
    }


    public void batchAddResourceTag(List<String> tagIds, String resourceType, String resource, String operation) {
        ResourcesTagsInfoInput resourcesTagsInfoInput = new ResourcesTagsInfoInput();
        resourcesTagsInfoInput.setTagIds(tagIds);
        resourcesTagsInfoInput.setResourceType(ThreadLocalStore.getTenantInfoLocal());
        resourcesTagsInfoInput.setUserId(AuthInterceptor.getUserId());
        resourcesTagsInfoInput.setResourceType(resourceType);
        resourcesTagsInfoInput.setResources(Lists.newArrayList(resource));
        resourcesTagsInfoInput.setOperation(operation);
        batchOperatorResourcesTags(resourcesTagsInfoInput);
    }

    @Transactional
    public ResponseObject batchOperatorResourcesTags(ResourcesTagsInfoInput resourcesTagsInfoInput) {
        resourcesTagsInfoInput.getResources().forEach(resource -> {
            if(resourcesTagsInfoInput.getOperation().equals(ResourceTypeConstant.RESOURCE_TAG_OPERATION_ADD)){
                List<ResourceTagDo> resourceTagDos = resourcesTagsInfoInput.getTagIds().stream().map(tagId -> {
                            ResourceTagDo resourceTagDo = ResourceTagDo.builder()
                                    .tagId(tagId)
                                    .resource(resource)
                                    .resourceType(resourcesTagsInfoInput.getResourceType())
                                    .tenantId(resourcesTagsInfoInput.getTenantId())
                                    .id(IdUtils.generateId())
                                    .creator(resourcesTagsInfoInput.getUserId())
                                    .modifier(resourcesTagsInfoInput.getUserId())
                                    .build();
                            return resourceTagDo;
                        }
                ).collect(Collectors.toList());
                resourceTagDAO.batchAddResourceTag(resourceTagDos);
            }else{
                 resourcesTagsInfoInput.getTagIds().stream().forEach(
                        tagId -> {
                            ResourceTagDo resourceTagDo = ResourceTagDo.builder()
                                    .tagId(tagId)
                                    .resource(resource)
                                    .resourceType(resourcesTagsInfoInput.getResourceType())
                                    .tenantId(resourcesTagsInfoInput.getTenantId())
                                    .build();
                            resourceTagDAO.deleteResourceTag(resourceTagDo);
                        }
                );
            }
        });

        return ResponseObject.success(null);
    }

    public ResponseObject delResourceTag(ResourceTagInfoInput resourceTagInfoInput) {
        resourceTagDAO.deleteById(resourceTagInfoInput.getId());
        return ResponseObject.success(resourceTagInfoInput.getId());
    }

    public int deleteResourceTagByName(String resource, String resourceType) {
        return resourceTagDAO.deleteResourceTagByName(resource, resourceType);
    }

    public int deleteResourceTagByNames(List<String> resources, String resourceType) {
        return resourceTagDAO.deleteResourceTagByNames(resources, resourceType);
    }

    public List<TagNameDo> listResourceTags(String tenantId, String resource, String resourceType) {
        List<ResourceTagDo> resourceTagDos = resourceTagDAO.findByResourceIds(Lists.newArrayList(resource), resourceType, tenantId);

        List<String> tagIds = resourceTagDos.stream()
                .map(ResourceTagDo::getTagId)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(tagIds)) {
            return Collections.emptyList();
        }

        Map<String, TagNameDo> tagMap = tagDAO.findByIds(tagIds)
                .stream()
                .collect(Collectors.toMap(TagNameDo::getId, tag -> tag));

        List<String> tagTypeIds = tagMap.values().stream()
                .map(TagNameDo::getTagTypeId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(tagTypeIds)) {
            return Collections.emptyList();
        }

        Map<String, TagTypeDo> tagTypeMap = tagTypeDAO.findByIds(tagTypeIds)
                .stream()
                .collect(Collectors.toMap(TagTypeDo::getId, tagType -> tagType));

        return tagMap.values().stream()
                .peek(tag -> tag.setTagTypeDo(tagTypeMap.get(tag.getTagTypeId())))
                .collect(Collectors.toList());
    }

    public List<TagInputDo> listResourceTagOutDo(String tenantId, String resource, String resourceType) {
        return listTagDoByConvertFunc(tenantId, resource, resourceType, TagConvertUtil::toTagInfoOutDo);
    }

    public List<TagInputDo> listResourceTagOutDoV2(String tenantId, String resource, String resourceType) {
        return listTagDoByConvertFunc(tenantId, resource, resourceType, TagConvertUtil::toTagInfoInputDo);
    }

    private List<TagInputDo> listTagDoByConvertFunc(String tenantId, String resource, String resourceType,
                                                    Function<TagNameDo, TagInputDo> convertFunc) {
        List<ResourceTagDo> resourceTagDos = resourceTagDAO.findByResourceIds(Lists.newArrayList(resource), resourceType, tenantId);
        if (CollectionUtils.isEmpty(resourceTagDos)) {
            return Collections.emptyList();
        }

        List<String> tagIds = resourceTagDos.stream()
                .map(ResourceTagDo::getTagId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(tagIds)) {
            return Collections.emptyList();
        }

        Map<String, TagNameDo> tagMap = tagDAO.findByIds(tagIds).stream()
                .collect(Collectors.toMap(TagNameDo::getId, Function.identity()));

        if (CollectionUtils.isEmpty(tagMap)) {
            return Collections.emptyList();
        }

        List<String> tagTypeIds = tagMap.values().stream()
                .map(TagNameDo::getTagTypeId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        Map<String, TagTypeDo> tagTypeMap = tagTypeDAO.findByIds(tagTypeIds).stream()
                .collect(Collectors.toMap(TagTypeDo::getId, Function.identity()));

        Map<String, List<String>> tagTypeIdToScopes = tagTypeIds.stream()
                .collect(Collectors.toMap(
                        Function.identity(),
                        tagTypeId -> tagTypeScopeDAO.listByTagTypeId(tagTypeId).stream()
                                .map(TagTypeScopeDo::getScope)
                                .collect(Collectors.toList())
                ));

        return tagMap.values().stream()
                .peek(tag -> {
                    String tagTypeId = tag.getTagTypeId();
                    TagTypeDo tagTypeDo = tagTypeMap.get(tagTypeId);
                    if (tagTypeDo != null) {
                        tagTypeDo.setScopes(tagTypeIdToScopes.getOrDefault(tagTypeId, Collections.emptyList()));
                        tag.setTagTypeDo(tagTypeDo);
                    }
                })
                .map(convertFunc)
                .collect(Collectors.toList());
    }



    public List<TagInfoOut> listResourceTagOuts(String tenantId, String resource, String resourceType) {
        List<ResourceTagDo> resourceTagDos = resourceTagDAO.findByResourceIds(Lists.newArrayList(resource), resourceType, tenantId);
        if (CollectionUtils.isEmpty(resourceTagDos)) {
            return Collections.emptyList();
        }

        List<String> tagIds = resourceTagDos.stream()
                .map(ResourceTagDo::getTagId)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(tagIds)) {
            return Collections.emptyList();
        }

        Map<String, TagNameDo> tagMap = tagDAO.findByIds(tagIds)
                .stream()
                .collect(Collectors.toMap(TagNameDo::getId, tag -> tag));

        List<String> tagTypeIds = tagMap.values().stream()
                .map(TagNameDo::getTagTypeId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(tagTypeIds)) {
            return Collections.emptyList();
        }

        Map<String, TagTypeDo> tagTypeMap = tagTypeDAO.findByIds(tagTypeIds)
                .stream()
                .collect(Collectors.toMap(TagTypeDo::getId, tagType -> tagType));

        Map<String, List<String>> tagTypeIdToScopes = new HashMap<>();
        for (String tagTypeId : tagTypeIds) {
            List<TagTypeScopeDo> scopeDos = tagTypeScopeDAO.listByTagTypeId(tagTypeId);
            if (!CollectionUtils.isEmpty(scopeDos)) {
                List<String> scopes = scopeDos.stream()
                        .map(TagTypeScopeDo::getScope)
                        .collect(Collectors.toList());
                tagTypeIdToScopes.put(tagTypeId, scopes);
            }
        }

        for (TagNameDo tag : tagMap.values()) {
            TagTypeDo tagTypeDo = tagTypeMap.get(tag.getTagTypeId());
            if (tagTypeDo != null) {
                List<String> scopes = tagTypeIdToScopes.get(tag.getTagTypeId());
                tagTypeDo.setScopes(scopes);
                tag.setTagTypeDo(tagTypeDo);
            }
        }

        return tagMap.values().stream()
                .map(TagConvertUtil::toTagInfoOut)
                .collect(Collectors.toList());
    }

    public ResponseObject queryResourceByTag(PageQuery<ResourceTagQuery> pageQuery) {
        // if admin role, return all tenant resoruce
        boolean adminRole = tagPermissionUtil.isAdminRole(pageQuery.getUserId());

        int count = resourceTagDAO.countResourceByTagIdsOr(adminRole ? null:pageQuery.getTenantId(), pageQuery.getQueryPara().getTagIds(),
                pageQuery.getQueryPara().getResource(), pageQuery.getQueryPara().getResourceType());


        if(count > 0){
            List<ResourceTagDo> resourceByTagIds = resourceTagDAO.findResourceByTagIdsOrPage(
                    adminRole ? null : pageQuery.getTenantId(),
                    pageQuery.getQueryPara().getTagIds(),
                    pageQuery.getQueryPara().getResource(), pageQuery.getQueryPara().getResourceType(),
                    (pageQuery.getPageIndex() - 1) * pageQuery.getPageSize(), pageQuery.getPageSize());

            List<String> tenantIds = resourceByTagIds.stream().map(ResourceTagDo::getTenantId).collect(Collectors.toList());
            Map<String, String> tenantMap = new HashMap();
            tenantDAO.listByIds(tenantIds).stream().forEach(tenantDO -> {
                tenantMap.put(tenantDO.getId(), tenantDO.getName());
            });

            List<ResourceTagOut> resourceTagOuts = resourceByTagIds.stream().map(resourceTagDo -> {
                ResourceTagOut resourceTagOut = new ResourceTagOut();
                resourceTagOut.setTenantId(resourceTagDo.getTenantId());
                resourceTagOut.setResourceType(resourceTagDo.getResourceType());
                if (resourceTagDo.getResourceType().equals(ResourceTypeConstant.RESOURCE_METRIC_TYPE)) {
                    MetricsDO metricsById = metricsDAO.getMetricsById(resourceTagDo.getResource());
                    resourceTagOut.setResource(metricsById);
                } else if (resourceTagDo.getResourceType().equals(ResourceTypeConstant.RESOURCE_AlARM_TYPE)) {
                    Optional<AlarmDefinition> byId = alarmDefinitionDao.findById(resourceTagDo.getResource());
                    resourceTagOut.setResource(byId.get());
                }else if(resourceTagDo.getResourceType().equals(ResourceTypeConstant.RESOURCE_AI_MODEL)){
                    List<AdCfgDataOut> adCfgById = adCfgHandler.getAdCfgById(resourceTagDo.getResource());
                    if(CollectionUtils.isEmpty(adCfgById)){
                        resourceTagOut.setResource(Collections.emptyList());
                    }else{
                        resourceTagOut.setResource(adCfgById.get(0));
                    }
                }
                resourceTagOut.setTenantName(tenantMap.get(resourceTagDo.getTenantId()));
                return resourceTagOut;
            }).collect(Collectors.toList());

            return ResponseObject.success(pageQuery.getOperId(), new PageResult(count, resourceTagOuts));
        }else{
            return ResponseObject.success(pageQuery.getOperId(), new PageResult(count, Collections.emptyList()));
        }
    }


    public ResponseObject queryTagByResource(ResourceTagInfoInput resourceTagInfoInput) {
        return ResponseObject.success(listResourceTags(resourceTagInfoInput.getTenantId(), resourceTagInfoInput.getResource(), resourceTagInfoInput.getResourceType()));
    }


    @Transactional(rollbackFor = Exception.class)
    public void handleEditResourceTag(List<String> currentTags, String tenantId, String resource, String resourceType){
        currentTags.removeIf(Objects::isNull);
        List<TagNameDo> tagNameDos = listResourceTags(tenantId, resource, resourceType);
        List<String> dbTags = Optional.ofNullable(tagNameDos).orElse(Collections.emptyList()).
                stream().map(tagNameDo -> tagNameDo.getId()).filter(Objects::nonNull).collect(Collectors.toList());
        ResourcesTagsInfoInput resourcesTagsInfoInput = new ResourcesTagsInfoInput();
        resourcesTagsInfoInput.setResources(Lists.newArrayList(resource));
        resourcesTagsInfoInput.setResourceType(resourceType);
        if(!CollectionUtils.isEmpty(currentTags)){
            if(dbTags != null && !dbTags.isEmpty()){
                List<String> addTagIds = currentTags.stream()
                        .filter(element -> !dbTags.contains(element))
                        .collect(Collectors.toList());
                if(addTagIds != null && !addTagIds.isEmpty()){
                    resourcesTagsInfoInput.setTagIds(addTagIds);
                    resourcesTagsInfoInput.setOperation(ResourceTypeConstant.RESOURCE_TAG_OPERATION_ADD);
                    batchOperatorResourcesTags(resourcesTagsInfoInput);
                }

                List<String> delTagIds = dbTags.stream()
                        .filter(element -> !currentTags.contains(element))
                        .collect(Collectors.toList());
                if(delTagIds != null && !delTagIds.isEmpty()){
                    resourcesTagsInfoInput.setTagIds(delTagIds);
                    resourcesTagsInfoInput.setOperation(ResourceTypeConstant.RESOURCE_TAG_OPERATION_DELETE);
                    batchOperatorResourcesTags(resourcesTagsInfoInput);
                }
            }else{
                resourcesTagsInfoInput.setTagIds((List<String>) currentTags);
                resourcesTagsInfoInput.setResources(Lists.newArrayList(resource));
                resourcesTagsInfoInput.setResourceType(resourceType);
                resourcesTagsInfoInput.setOperation(ResourceTypeConstant.RESOURCE_TAG_OPERATION_ADD);
                batchOperatorResourcesTags(resourcesTagsInfoInput);
            }
        }else{
            //delete db tags
            if(dbTags != null && !dbTags.isEmpty()){
                resourcesTagsInfoInput.setTagIds(dbTags);
                resourcesTagsInfoInput.setOperation(ResourceTypeConstant.RESOURCE_TAG_OPERATION_DELETE);
                batchOperatorResourcesTags(resourcesTagsInfoInput);
            }
        }
    }
}
