{"dataParser": {"name": "TEL", "rawDataType": 1, "rawDataParseType": "groovy", "invokeFunction": "parseMessage", "rawDataParseRule": "import groovy.json.JsonSlurper\nimport java.text.SimpleDateFormat;\n\n    def parseMessage(String message) {\n        def jsonSlurper = new JsonSlurper()\n        List<Map<String, Object>> messageJson = jsonSlurper.parseText(message)\n        List<Map<String, Object>> result = new ArrayList(messageJson.size())\n        SimpleDateFormat format = new SimpleDateFormat(\"MM/dd/yyyy HH:mm:ss:SSS\")\n\n        for (Map<String, Object> map : messageJson) {\n            String log = map.message\n            String[] logArray = log.split(\",\")\n            Long collectTime = format.parse(logArray[1]).time\n\n            Map<String, Object> eleMap = new HashMap()\n\n            eleMap.put(\"region\", map.region)\n            eleMap.put(\"hostname\", map.hostname)\n            eleMap.put(\"regionId\", parseZoneAndRegion(map.regionId))\n            eleMap.put(\"zoneName\", parseZoneAndRegion(map.zoneName))\n            eleMap.put(\"mmc\", map.mmc)\n            eleMap.put(\"capacity\", map.capacity)\n            eleMap.put(\"kafkaRegion\", map.kafka_region)\n\n            String instance = getInstance(map.source)\n            eleMap.put(\"instance\", instance)\n            String publicIp = getPublicIpFromInstanceId(map.instance_id)\n            eleMap.put(\"publicIp\", publicIp)\n            String instanceId = publicIp + \"#\" + instance\n            eleMap.put(\"instanceId\", instanceId)\n            String cluster = getCluster(map.cluster_env)\n            eleMap.put(\"cluster\", cluster)\n\n            eleMap.put(\"clusterEnv\", map.cluster_env)\n\n\n            eleMap.put(\"ts\", collectTime)\n            eleMap.put(\"meetingID\", getMeetingID(logArray[2]))\n            eleMap.put(\"dataType\", logArray[3])\n\n            // Added 04-20,  missing ENV and Zone\n            eleMap.put(\"Region\", map.region)\n            eleMap.put(\"Hostname\", map.hostname)\n            eleMap.put(\"Instance\", getInstance(map.source))\n            eleMap.put(\"InstanceId\", instanceId)\n            eleMap.put(\"MMC\", map.mmc)\n            eleMap.put(\"Cluster\", cluster)\n            eleMap.put(\"PublicIp\", publicIp)\n            eleMap.put(\"MeetingId\", getMeetingID(logArray[2]))\n            eleMap.put(\"Capacity\", map.capacity)\n\n            eleMap.put(\"message\", log.substring(log.indexOf(logArray[3]) + logArray[3].length() + 1))\n\n            result.add(eleMap)\n        }\n        return result\n    }\n\n    public String getInstance(String source) {\n        if (source==null || source.isBlank()) {\n            return source;\n        }\n        int endIndex = source.length();\n        while (endIndex > 0 && source.charAt(endIndex - 1) == '/') {\n            endIndex--;\n        }\n        int startIndex = endIndex - 1\n        while (startIndex > 0 && source.charAt(startIndex - 1) != '/') {\n            startIndex--\n        }\n        int first = source.indexOf(\"_\", startIndex + 1);\n        if (first <= 0) {\n            return null\n        }\n        int second = source.indexOf(\"_\", first + 1)\n//        if (StringUtils.containsAny(source, \"monitor_lttgw_srvr\", \"e2e_server\")) {\n        if (source.contains(\"monitor_lttgw_srvr\") || source.contains(\"e2e_server\")) {\n            second = source.indexOf(\"_\", second + 1)\n        }\n        if (second <= 0) {\n            return null\n        }\n        return source.substring(first + 1, second)\n    }\n\n    public String getPublicIpFromInstanceId(String instance) {\n        if (instance==null || instance.isBlank()) {\n            return instance\n    }\n        int index = instance.indexOf(\".\")\n        if (index <= 0) {\n            return instance\n    }\n        index = instance.indexOf(\".\", index + 1)\n        if (index <= 0) {\n            return instance\n    }\n        index = instance.indexOf(\".\", index + 1)\n        if (index <= 0) {\n            return instance\n    }\n        char ch = 0\n        for (index++; index < instance.length(); index++) {\n            ch = instance.charAt(index)\n            if (ch < '0' || ch > '9') {\n                return instance.substring(0, index);\n        }\n    }\n        return instance\n}\n\n    public String getCluster(String env) {\n        if (env != null && env.contains(\",\")) {\n            String[] array = env.split(\",\")\n            return array[0]\n        }\n        return env\n    }\n\n    public String getMeetingID(String meetingIdStr) {\n        if (meetingIdStr==null || meetingIdStr.isBlank()) {\n            return \"\"\n        }\n        int i = meetingIdStr.indexOf(\" \")\n        if (i != -1) {\n            return meetingIdStr.substring(0, i)\n        }\n        return meetingIdStr\n    }\n\n    public String parseZoneAndRegion(String input) {\n        if (input==null || input.isBlank()) {\n            return input;\n        }\n        return substringBefore(substringBefore(input, \";\"), \"#\").trim();\n    }\n\n    public String substringBefore(final String str, final String separator) {\n        final int pos = str.indexOf(separator);\n        return pos == -1 ? str : str.substring(0, pos);\n    }\n\n    public String parseClusterEnv(String env) {\n        if (env==null || env.isBlank()) {\n            return env\n        }\n        return env.replace(\",\", \"|\")\n    }", "useStatus": 1}, "pipeline": [{"name": "TEL_CURL_WEB_FAIL", "filterRule": "dataType=='CURL-WEB-FAIL'", "useStatus": 1, "order": 0, "fields": [{"fieldName": "hostname", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "mmc", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "instance", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "instanceid", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "zone", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "zmserviceip", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "gwtype", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "region", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "call_web_fail_count", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}], "processores": [{"name": "split", "order": 0, "type": "split", "sourceField": "message", "schema": {"separator": ",", "entries": [{"index": 0, "name": "responseCode", "type": "number"}]}}, {"name": "expression", "order": 1, "type": "expression", "schema": {"entries": [{"name": "instanceid", "expression": "instanceId", "type": "string"}, {"name": "call_web_fail_count", "expression": "responseCode", "type": "number"}, {"name": "gwtype", "expression": "string.startsWith(instance, 'telgw')?'TELGW':'TSPGW'", "type": "string"}, {"name": "instance", "expression": "instance", "type": "string"}, {"name": "zone", "expression": "zoneName", "type": "string"}, {"name": "hostname", "expression": "hostname", "type": "string"}, {"name": "mmc", "expression": "mmc", "type": "string"}, {"name": "zmserviceip", "expression": "publicIp", "type": "string"}, {"name": "region", "expression": "region", "type": "string"}]}}]}, {"name": "TEL_JOIN_FAIL", "filterRule": "dataType=='JOIN-FAIL'", "useStatus": 1, "order": 1, "fields": [{"fieldName": "hostname", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "mmc", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "instanceid", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "join_zc_mmr_fail_count", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "zone", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "zmserviceip", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "gwtype", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "region", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}], "processores": [{"name": "split", "order": 0, "type": "split", "sourceField": "message", "schema": {"separator": ",", "entries": [{"index": 0, "name": "error_code", "type": "string"}, {"index": 1, "name": "address", "type": "string"}]}}, {"name": "expression", "order": 1, "type": "expression", "schema": {"entries": [{"name": "instanceid", "expression": "instanceId", "type": "string"}, {"name": "gwtype", "expression": "string.startsWith(instance, 'telgw')?'TELGW':'TSPGW'", "type": "string"}, {"name": "zone", "expression": "zoneName", "type": "string"}, {"name": "hostname", "expression": "hostname", "type": "string"}, {"name": "mmc", "expression": "mmc", "type": "string"}, {"name": "zmserviceip", "expression": "publicIp", "type": "string"}, {"name": "region", "expression": "region", "type": "string"}, {"name": "join_zc_mmr_fail_count", "expression": "error_code", "type": "number"}]}}]}, {"name": "TEL_SERVER_STAT", "filterRule": "dataType=='SERVER-STAT'", "useStatus": 1, "order": 2, "fields": [{"fieldName": "meeting_count_peak", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "hostname", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "mmc", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "instance", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "instanceid", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "zone", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "zmserviceip", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "gwtype", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "region", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "capacity", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "telephony_count_peak", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}], "processores": [{"name": "split", "order": 0, "type": "split", "sourceField": "message", "schema": {"separator": ",", "entries": [{"index": 0, "name": "meetingCount", "type": "number"}, {"index": 1, "name": "p2pMeetings", "type": "number"}, {"index": 2, "name": "clientCount", "type": "number"}, {"index": 3, "name": "p2pClientCount", "type": "number"}, {"index": 4, "name": "telephonyCount", "type": "number"}]}}, {"name": "expression", "order": 1, "type": "expression", "schema": {"entries": [{"name": "instanceid", "expression": "instanceId", "type": "string"}, {"name": "gwtype", "expression": "string.startsWith(instance, 'telgw')?'TELGW':'TSPGW'", "type": "string"}, {"name": "instance", "expression": "instance", "type": "string"}, {"name": "zone", "expression": "zoneName", "type": "string"}, {"name": "hostname", "expression": "hostname", "type": "string"}, {"name": "capacity", "expression": "capacity", "type": "string"}, {"name": "meeting_count_peak", "expression": "meetingCount", "type": "number"}, {"name": "mmc", "expression": "mmc", "type": "string"}, {"name": "telephony_count_peak", "expression": "telephonyCount", "type": "number"}, {"name": "zmserviceip", "expression": "publicIp", "type": "string"}, {"name": "region", "expression": "region", "type": "string"}]}}]}, {"name": "TEL_SERVER_STATUS", "filterRule": "dataType=='SERVER-STATUS'", "useStatus": 1, "order": 3, "fields": [{"fieldName": "service_scale_in", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "service_open", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "cluster", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "mmc", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "instance", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "service_not_support_create", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "zmserviceip", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "service_failover", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "env", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "gwtype", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "isprimary", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "service_idle", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "hostname", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "instanceid", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "zone", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "service_status", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "service_closed", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "region", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "service_closing", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "service_suspend", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "status", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}], "processores": [{"name": "split", "order": 0, "type": "split", "sourceField": "message", "schema": {"separator": ",", "entries": [{"index": 0, "name": "statusValue", "type": "number"}, {"index": 1, "name": "isPrimary", "type": "number"}]}}, {"name": "expression", "order": 1, "type": "expression", "schema": {"entries": [{"name": "instanceid", "expression": "instanceId", "type": "string"}, {"name": "service_open", "expression": "statusValue==1?1:0", "type": "number"}, {"name": "service_failover", "expression": "statusValue==5?1:0", "type": "number"}, {"name": "isprimary", "expression": "isPrimary", "type": "number"}, {"name": "instance", "expression": "instance", "type": "string"}, {"name": "service_closing", "expression": "statusValue==7?1:0", "type": "number"}, {"name": "service_scale_in", "expression": "statusValue==4?1:0", "type": "number"}, {"name": "zone", "expression": "zoneName", "type": "string"}, {"name": "hostname", "expression": "hostname", "type": "string"}, {"name": "zmserviceip", "expression": "publicIp", "type": "string"}, {"name": "service_idle", "expression": "statusValue==0?1:0", "type": "number"}, {"name": "cluster", "expression": "cluster", "type": "string"}, {"name": "region", "expression": "region", "type": "string"}, {"name": "status", "expression": "statusValue", "type": "number"}, {"name": "gwtype", "expression": "string.startsWith(instance, 'telgw')?'TELGW':(string.startsWith(instance, 'tspgw')?'TSPGW':'TC')", "type": "string"}, {"name": "service_not_support_create", "expression": "statusValue==6?1:0", "type": "number"}, {"name": "service_suspend", "expression": "statusValue==2?1:0", "type": "number"}, {"name": "service_closed", "expression": "statusValue==3?1:0", "type": "number"}, {"name": "service_status", "expression": "statusValue", "type": "number"}, {"name": "env", "expression": "env", "type": "string"}, {"name": "mmc", "expression": "mmc", "type": "string"}]}}]}, {"name": "TEL_MS_DATA_EXCEPTIONAL", "filterRule": "dataType=='MS-DATA-EXCEPTIONAL'", "useStatus": 1, "order": 4, "fields": [{"fieldName": "mmc", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "instanceid", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "ms_data_exceptional_count", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "gwtype", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "region", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}], "processores": [{"name": "split", "order": 0, "type": "split", "sourceField": "message", "schema": {"separator": ",", "entries": [{"index": 0, "name": "exceptionalCode", "type": "number"}]}}, {"name": "expression", "order": 1, "type": "expression", "schema": {"entries": [{"name": "instanceid", "expression": "instanceId", "type": "string"}, {"name": "gwtype", "expression": "string.startsWith(instance, 'telgw')?'TELGW':'TSPGW'", "type": "string"}, {"name": "ms_data_exceptional_count", "expression": "exceptionalCode", "type": "number"}, {"name": "mmc", "expression": "mmc", "type": "string"}, {"name": "region", "expression": "region", "type": "string"}]}}]}, {"name": "TEL_CONNECT_ADAPTOR_FAIL", "filterRule": "dataType=='CONNECT-ADAPTOR-FAIL'", "useStatus": 1, "order": 5, "fields": [{"fieldName": "mmc", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "instanceid", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "zmserviceip", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "gwtype", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "region", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "connect_adaptor_fail_count", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}], "processores": [{"name": "split", "order": 0, "type": "split", "sourceField": "message", "schema": {"separator": ",", "entries": [{"index": 0, "name": "adaptor", "type": "string"}, {"index": 1, "name": "IP", "type": "string"}, {"index": 2, "name": "fail_msg", "type": "string"}]}}, {"name": "expression", "order": 1, "type": "expression", "schema": {"entries": [{"name": "instanceid", "expression": "instanceId", "type": "string"}, {"name": "gwtype", "expression": "'TSPGW'", "type": "string"}, {"name": "connect_adaptor_fail_count", "expression": "fail_msg", "type": "number"}, {"name": "mmc", "expression": "mmc", "type": "string"}, {"name": "zmserviceip", "expression": "publicIp", "type": "string"}, {"name": "region", "expression": "region", "type": "string"}]}}]}, {"name": "TEL_CREATE_BRIDGE_CALL_FAIL", "filterRule": "dataType=='CREATE-BRIDGE-CALL-FAIL'", "useStatus": 1, "order": 6, "fields": [{"fieldName": "mmc", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "instanceid", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "create_bridge_call_fail_count", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "gwtype", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "region", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}], "processores": [{"name": "split", "order": 0, "type": "split", "sourceField": "message", "schema": {"separator": ",", "entries": [{"index": 0, "name": "bridge", "type": "string"}, {"index": 1, "name": "IP", "type": "string"}, {"index": 2, "name": "fail_msg", "type": "string"}]}}, {"name": "expression", "order": 1, "type": "expression", "schema": {"entries": [{"name": "instanceid", "expression": "instanceId", "type": "string"}, {"name": "gwtype", "expression": "'TSPGW'", "type": "string"}, {"name": "create_bridge_call_fail_count", "expression": "fail_msg", "type": "number"}, {"name": "mmc", "expression": "mmc", "type": "string"}, {"name": "region", "expression": "region", "type": "string"}]}}]}, {"name": "TEL_CREATE_CONF_FAIL", "filterRule": "dataType=='CREATE-CONF-FAIL'", "useStatus": 1, "order": 7, "fields": [{"fieldName": "create_conf_fail_count", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "mmc", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "instanceid", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "zmserviceip", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "gwtype", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "region", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}], "processores": [{"name": "split", "order": 0, "type": "split", "sourceField": "message", "schema": {"separator": ",", "entries": [{"index": 0, "name": "IP", "type": "string"}, {"index": 1, "name": "fail_msg", "type": "string"}]}}, {"name": "expression", "order": 1, "type": "expression", "schema": {"entries": [{"name": "instanceid", "expression": "instanceId", "type": "string"}, {"name": "gwtype", "expression": "'TSPGW'", "type": "string"}, {"name": "create_conf_fail_count", "expression": "fail_msg", "type": "number"}, {"name": "mmc", "expression": "mmc", "type": "string"}, {"name": "zmserviceip", "expression": "publicIp", "type": "string"}, {"name": "region", "expression": "region", "type": "string"}]}}]}, {"name": "TEL_TSP_URL_STAT", "filterRule": "dataType=='TSP_URL_STAT'", "useStatus": 1, "order": 8, "fields": [{"fieldName": "meeting_count_peak", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "mmc", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "instanceid", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "vendor", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "phone_count_peak", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "gwtype", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "region", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "url", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}], "processores": [{"name": "split", "order": 0, "type": "split", "sourceField": "message", "schema": {"separator": ",", "entries": [{"index": 0, "name": "vendor", "type": "string"}, {"index": 1, "name": "url", "type": "string"}, {"index": 2, "name": "max_conference_size", "type": "number"}, {"index": 3, "name": "PSTN_user", "type": "number"}]}}, {"name": "expression", "order": 1, "type": "expression", "schema": {"entries": [{"name": "instanceid", "expression": "instanceId", "type": "string"}, {"name": "gwtype", "expression": "'TSPGW'", "type": "string"}, {"name": "phone_count_peak", "expression": "PSTN_user", "type": "number"}, {"name": "meeting_count_peak", "expression": "max_conference_size", "type": "number"}, {"name": "url", "expression": "url", "type": "string"}, {"name": "vendor", "expression": "vendor", "type": "string"}, {"name": "mmc", "expression": "mmc", "type": "string"}, {"name": "region", "expression": "region", "type": "string"}]}}]}, {"name": "TEL_VENDOR_STAT", "filterRule": "dataType=='VENDOR-STAT'", "useStatus": 1, "order": 9, "fields": [{"fieldName": "mmc", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "instanceid", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "callin_internal_phones", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "zone", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "zmserviceip", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "vendor", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "callout_internal_phones", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "tollfree_phones", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "phones", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "toll_phones", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "others_phones", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "callout_phones", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}], "processores": [{"name": "split", "order": 0, "type": "split", "sourceField": "message", "schema": {"separator": ",", "entries": [{"index": 0, "name": "vendor", "type": "string"}, {"index": 1, "name": "othersPhones", "type": "number"}, {"index": 2, "name": "tollPhones", "type": "number"}, {"index": 3, "name": "tollfreePhones", "type": "number"}, {"index": 4, "name": "calloutPhones", "type": "number"}, {"index": 5, "name": "calloutInternalPhones", "type": "number"}, {"index": 6, "name": "callinInternalPhones", "type": "number"}]}}, {"name": "expression", "order": 1, "type": "expression", "schema": {"entries": [{"name": "instanceid", "expression": "instanceId", "type": "string"}, {"name": "callout_internal_phones", "expression": "calloutInternalPhones", "type": "number"}, {"name": "phones", "expression": "tollPhones+tollfreePhones", "type": "number"}, {"name": "others_phones", "expression": "othersPhones", "type": "number"}, {"name": "callin_internal_phones", "expression": "callinInternalPhones", "type": "number"}, {"name": "toll_phones", "expression": "tollPhones", "type": "number"}, {"name": "callout_phones", "expression": "calloutPhones", "type": "number"}, {"name": "zone", "expression": "zoneName", "type": "string"}, {"name": "vendor", "expression": "vendor", "type": "string"}, {"name": "mmc", "expression": "mmc", "type": "string"}, {"name": "tollfree_phones", "expression": "tollfreePhones", "type": "number"}, {"name": "zmserviceip", "expression": "publicIp", "type": "string"}]}}]}, {"name": "TEL_GLOBAL_STAT", "filterRule": "dataType=='GLOBAL-STAT'", "useStatus": 1, "order": 10, "fields": [{"fieldName": "cluster", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "mmc", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "instance", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "incomingcalls", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "ongoingconference_max", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "mstimeouts", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "ongoingconferences", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "zmserviceip", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "joinmeetingfailures", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "outdialfailures", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "env", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "gwtype", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "onlinephonecount", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "hostname", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "ongoingcall_max", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "instanceid", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "mserrors", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "zone", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "deadmscount", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "region", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}], "processores": [{"name": "split", "order": 0, "type": "split", "sourceField": "message", "schema": {"separator": ",", "entries": [{"index": 0, "name": "ongoingConferenceCount", "type": "number"}, {"index": 1, "name": "onlinePhoneCount", "type": "number"}, {"index": 2, "name": "incomingCallCount", "type": "number"}, {"index": 3, "name": "outDailFailureCount", "type": "number"}, {"index": 4, "name": "joinMeetingFailureCount", "type": "number"}, {"index": 5, "name": "msTimeoutCount", "type": "number"}, {"index": 6, "name": "msError<PERSON>ount", "type": "number"}, {"index": 7, "name": "deadMsCount", "type": "number"}]}}, {"name": "expression", "order": 1, "type": "expression", "schema": {"entries": [{"name": "mstimeouts", "expression": "msTimeoutCount", "type": "number"}, {"name": "instanceid", "expression": "instanceId", "type": "string"}, {"name": "mserrors", "expression": "msError<PERSON>ount", "type": "number"}, {"name": "instance", "expression": "instance", "type": "string"}, {"name": "joinmeetingfailures", "expression": "joinMeetingFailureCount", "type": "number"}, {"name": "hostname", "expression": "hostname", "type": "string"}, {"name": "zone", "expression": "zoneName", "type": "string"}, {"name": "incomingcalls", "expression": "incomingCallCount", "type": "number"}, {"name": "ongoingcall_max", "expression": "onlinePhoneCount", "type": "number"}, {"name": "zmserviceip", "expression": "publicIp", "type": "string"}, {"name": "cluster", "expression": "cluster", "type": "string"}, {"name": "region", "expression": "region", "type": "string"}, {"name": "deadmscount", "expression": "deadMsCount", "type": "number"}, {"name": "onlinephonecount", "expression": "onlinePhoneCount", "type": "number"}, {"name": "ongoingconferences", "expression": "ongoingConferenceCount", "type": "number"}, {"name": "gwtype", "expression": "string.startsWith(instance, 'telgw')?'TELGW':'TSPGW'", "type": "string"}, {"name": "outdialfailures", "expression": "outDailFailureCount", "type": "number"}, {"name": "env", "expression": "env", "type": "string"}, {"name": "mmc", "expression": "mmc", "type": "string"}, {"name": "ongoingconference_max", "expression": "ongoingConferenceCount", "type": "number"}]}}]}, {"name": "TEL_CC_LOST", "filterRule": "dataType=='CC-LOST'", "useStatus": 1, "order": 11, "fields": [{"fieldName": "cluster", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "mmc", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "hostname", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "instanceid", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "instance", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "zone", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "connect_cc_fail_count", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "region", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}], "processores": [{"name": "split", "order": 0, "type": "split", "sourceField": "message", "schema": {"separator": ",", "entries": [{"index": 0, "name": "address", "type": "string"}, {"index": 1, "name": "reason", "type": "number"}]}}, {"name": "expression", "order": 1, "type": "expression", "schema": {"entries": [{"name": "instanceid", "expression": "instanceId", "type": "string"}, {"name": "connect_cc_fail_count", "expression": "reason", "type": "number"}, {"name": "instance", "expression": "instance", "type": "string"}, {"name": "zone", "expression": "zoneName", "type": "string"}, {"name": "hostname", "expression": "hostname", "type": "string"}, {"name": "mmc", "expression": "mmc", "type": "string"}, {"name": "region", "expression": "region", "type": "string"}, {"name": "cluster", "expression": "cluster", "type": "string"}]}}]}, {"name": "TEL_TC_STAT", "filterRule": "dataType=='TC-STAT'", "useStatus": 1, "order": 12, "fields": [{"fieldName": "telgw_count", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "cluster", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "meeting_count_peak", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "mmc", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "hostname", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "suspend_telgw_count", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "instanceid", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "instance", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "zone", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "region", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "primary", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "telephony_count_peak", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}], "processores": [{"name": "split", "order": 0, "type": "split", "sourceField": "message", "schema": {"separator": ",", "entries": [{"index": 0, "name": "meeting_count", "type": "number"}, {"index": 1, "name": "phone_count", "type": "number"}, {"index": 2, "name": "telgw_count", "type": "number"}, {"index": 3, "name": "suspend_telgw_count", "type": "number"}, {"index": 4, "name": "primary", "type": "number"}]}}, {"name": "expression", "order": 1, "type": "expression", "schema": {"entries": [{"name": "instanceid", "expression": "instanceId", "type": "string"}, {"name": "suspend_telgw_count", "expression": "suspend_telgw_count", "type": "number"}, {"name": "instance", "expression": "instance", "type": "string"}, {"name": "primary", "expression": "primary", "type": "number"}, {"name": "telgw_count", "expression": "telgw_count", "type": "number"}, {"name": "zone", "expression": "zoneName", "type": "string"}, {"name": "hostname", "expression": "hostname", "type": "string"}, {"name": "meeting_count_peak", "expression": "meeting_count", "type": "number"}, {"name": "mmc", "expression": "mmc", "type": "string"}, {"name": "telephony_count_peak", "expression": "phone_count", "type": "number"}, {"name": "region", "expression": "region", "type": "string"}, {"name": "cluster", "expression": "cluster", "type": "string"}]}}]}, {"name": "TEL_CALL_JOIN_FAIL", "filterRule": "dataType=='CALL-JOIN-FAIL'", "useStatus": 1, "order": 13, "fields": [{"fieldName": "ivr_end_block_too_frequent", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "ivr_end_not_allow_join_after_host_remove", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "vendor_bics", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "vendor_iristel_new", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "ivr_end_tf_jbh_block", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "ivr_end_siptrunk_error", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "vendor_xo", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "ivr_end_phonenumber_invalid", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "vendor_colt", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "ivr_end_geo_restrict_phone", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "hostname", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "ivr_end_meeting_not_exist", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "vendor_rogers", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "zone", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "ivr_end_no_media_proxy", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "ivr_end_no_conf_found", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "ivr_end_meeting_locked", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "errorconnecttimeout", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "ivr_end_redirect_to_meeting", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "ivr_end_default", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "ip", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "ivr_callout_number_blocked", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "gwtype", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "vendor_vodafone", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "vendor_inteliquent_intl", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "country_code", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "errorringtimeout", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "instanceid", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "error_code", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "region", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "ivr_end_no_input", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "vendor_tata", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "ivr_end_meeting_full", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "i<PERSON><PERSON><PERSON><PERSON>", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "mmc", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "deloitte", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "ivr_end_not_allow_pstn", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "vendor_bandwidth", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "vendor_tnzi", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "ivr_end_unmatch_phonenumber", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "vendor_peerless", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "ivr_end_block_no_host", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "ivr_end_system_error", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "vendor_symbio", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "ivr_end_ms_error", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "vendor", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "vendor_bandwidth_int", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "country_name", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "ivr_end_user_hangup", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "vendor_inteliquent", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "vendor_otw", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "ivr_end_callout_no_press_one", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "vendor_iristel", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "ivr_end_no_callout_privilege", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "ivr_end_incorrect_input", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "vendor_level3", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "vendor_voxbone", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "vendor_iristel_int_new", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "ivr_client_kickoff_or_leavemeeting", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "ivr_end_meeting_not_startedd", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "ivr_end_meeting_not_active", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "vendor_union400", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "ivr_end_meeting_closed", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "vendor_orangecarrier", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "ivr_end_client_cancel", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "vendor_iristel_int", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "vendor_thinq", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}], "processores": [{"name": "split", "order": 0, "type": "split", "sourceField": "message", "schema": {"separator": ",", "entries": [{"index": 0, "name": "callType", "type": "string"}, {"index": 1, "name": "billingType", "type": "number"}, {"index": 2, "name": "callerId", "type": "number"}, {"index": 3, "name": "ivrReason", "type": "number"}, {"index": 4, "name": "sbcVIP", "type": "string"}, {"index": 5, "name": "vendorType", "type": "string"}, {"index": 6, "name": "errorCode", "type": "number"}, {"index": 7, "name": "countryCode", "type": "string"}, {"index": 8, "name": "countryName", "type": "string"}, {"index": 9, "name": "sipCallId", "type": "string"}, {"index": 10, "name": "vendor", "type": "string"}]}}, {"name": "expression", "order": 1, "type": "expression", "schema": {"entries": [{"name": "ivr_callout_number_blocked", "expression": "ivrReason == 18 ? 1:0", "type": "number"}, {"name": "country_name", "expression": "countryName", "type": "string"}, {"name": "vendor_symbio", "expression": "sbcVIP == '*******0' ? 1:0", "type": "number"}, {"name": "ivr_end_block_too_frequent", "expression": "ivrReason == 22 ? 1:0", "type": "number"}, {"name": "ivr_end_meeting_not_active", "expression": "ivrReason == 12 ? 1:0", "type": "number"}, {"name": "ip", "expression": "publicIp", "type": "string"}, {"name": "vendor_orangecarrier", "expression": "sbcVIP == '********' ? 1:0", "type": "number"}, {"name": "ivr_end_no_conf_found", "expression": "ivrReason == 19 ? 1:0", "type": "number"}, {"name": "vendor_level3", "expression": "(sbcVIP == '*******' || sbcVIP == '*******') ? 1:0", "type": "number"}, {"name": "ivr_end_not_allow_join_after_host_remove", "expression": "ivrReason == 28 ? 1:0", "type": "number"}, {"name": "ivr_end_tf_jbh_block", "expression": "ivrReason == 24 ? 1:0", "type": "number"}, {"name": "ivr_end_meeting_not_exist", "expression": "ivrReason == 1 ? 1:0", "type": "number"}, {"name": "vendor_union400", "expression": "sbcVIP == '**************' ? 1:0", "type": "number"}, {"name": "country_code", "expression": "countryCode", "type": "string"}, {"name": "ivr_end_geo_restrict_phone", "expression": "ivrReason == 26 ? 1:0", "type": "number"}, {"name": "ivr_end_client_cancel", "expression": "ivrReason == 16 ? 1:0", "type": "number"}, {"name": "errorringtimeout", "expression": "ivrReason == 20 ? 1:0", "type": "number"}, {"name": "ivr_end_incorrect_input", "expression": "ivrReason == 10 ? 1:0", "type": "number"}, {"name": "vendor", "expression": "vendor", "type": "string"}, {"name": "mmc", "expression": "mmc", "type": "string"}, {"name": "ivr_end_meeting_closed", "expression": "ivrReason == 11 ? 1:0", "type": "number"}, {"name": "vendor_iristel_int_new", "expression": "sbcVIP == '*********' ? 1:0", "type": "number"}, {"name": "ivr_end_no_input", "expression": "ivrReason == 9 ? 1:0", "type": "number"}, {"name": "instanceid", "expression": "instanceId", "type": "string"}, {"name": "ivr_end_redirect_to_meeting", "expression": "ivrReason == 8 ? 1:0", "type": "number"}, {"name": "vendor_iristel_new", "expression": "sbcVIP =='********'?1:0", "type": "number"}, {"name": "ivr_end_meeting_full", "expression": "ivrReason == 5 ? 1:0", "type": "number"}, {"name": "zone", "expression": "zoneName", "type": "string"}, {"name": "errorconnecttimeout", "expression": "ivrReason == 29 ? 1:0", "type": "number"}, {"name": "ivr_end_meeting_locked", "expression": "ivrReason == 4 ? 1:0", "type": "number"}, {"name": "vendor_inteliquent", "expression": "(sbcVIP=='*******' || sbcVIP=='*******') ? 1:0", "type": "number"}, {"name": "i<PERSON><PERSON><PERSON><PERSON>", "expression": "ivrReason", "type": "number"}, {"name": "ivr_end_system_error", "expression": "ivrReason == 6 ? 1:0", "type": "number"}, {"name": "gwtype", "expression": "string.startsWith(instance, 'telgw')?'TELGW':'TSPGW'", "type": "string"}, {"name": "ivr_end_no_callout_privilege", "expression": "ivrReason == 23 ? 1:0", "type": "number"}, {"name": "vendor_bandwidth", "expression": "(sbcVIP == '*******' || sbcVIP == '*******') ? 1:0", "type": "number"}, {"name": "ivr_end_block_no_host", "expression": "ivrReason == 21 ? 1:0", "type": "number"}, {"name": "ivr_end_no_media_proxy", "expression": "ivrReason == 25 ? 1:0", "type": "number"}, {"name": "ivr_end_phonenumber_invalid", "expression": "ivrReason == 27 ? 1:0", "type": "number"}, {"name": "ivr_end_not_allow_pstn", "expression": "ivrReason == 3 ? 1:0", "type": "number"}, {"name": "vendor_iristel", "expression": "sbcVIP =='********' ? 1:0", "type": "number"}, {"name": "deloitte", "expression": "sbcVIP =='**************' ? 1:0", "type": "number"}, {"name": "region", "expression": "region", "type": "string"}, {"name": "ivr_end_siptrunk_error", "expression": "ivrReason == 14 ? 1:0", "type": "number"}, {"name": "vendor_vodafone", "expression": "sbcVIP == '********' ? 1:0", "type": "number"}, {"name": "vendor_peerless", "expression": "(sbcVIP == '*******' || sbcVIP=='*******') ? 1:0", "type": "number"}, {"name": "vendor_bandwidth_int", "expression": "(sbcVIP == '*******1' || sbcVIP=='*******1') ? 1:0", "type": "number"}, {"name": "vendor_inteliquent_intl", "expression": "sbcVIP == '********' ? 1:0", "type": "number"}, {"name": "vendor_colt", "expression": "sbcVIP == '*******' ? 1:0", "type": "number"}, {"name": "ivr_end_meeting_not_startedd", "expression": "ivrReason == 0 ? 1:0", "type": "number"}, {"name": "vendor_voxbone", "expression": "sbcVIP == '*******' ? 1:0", "type": "number"}, {"name": "ivr_end_unmatch_phonenumber", "expression": "ivrReason == 2 ? 1:0", "type": "number"}, {"name": "ivr_client_kickoff_or_leavemeeting", "expression": "ivrReason == 17 ? 1:0", "type": "number"}, {"name": "vendor_iristel_int", "expression": "sbcVIP == '********1' ? 1:0", "type": "number"}, {"name": "vendor_tata", "expression": "sbcVIP == '*******' ? 1:0", "type": "number"}, {"name": "hostname", "expression": "hostname", "type": "string"}, {"name": "vendor_bics", "expression": "sbcVIP == '********' ? 1:0", "type": "number"}, {"name": "ivr_end_default", "expression": "ivrReason == -1 ? 1:0", "type": "number"}, {"name": "vendor_xo", "expression": "sbcVIP == '*******' ? 1:0", "type": "number"}, {"name": "ivr_end_callout_no_press_one", "expression": "ivrReason == 13 ? 1:0", "type": "number"}, {"name": "vendor_otw", "expression": "sbcVIP == '********' ? 1:0", "type": "number"}, {"name": "vendor_rogers", "expression": "sbcVIP == '*******1' ? 1:0", "type": "number"}, {"name": "vendor_thinq", "expression": "sbcVIP == '*******' ? 1:0", "type": "number"}, {"name": "ivr_end_user_hangup", "expression": "ivrReason == 7 ? 1:0", "type": "number"}, {"name": "error_code", "expression": "errorCode", "type": "string"}, {"name": "vendor_tnzi", "expression": "sbcVIP == '********' ? 1:0", "type": "number"}, {"name": "ivr_end_ms_error", "expression": "ivrReason == 15 ? 1:0", "type": "number"}]}}]}, {"name": "TEL_ASYNCMQ_PRACTICE_MODE", "filterRule": "dataType=='ASYNCMQ_PRACTICE_MODE'", "useStatus": 1, "order": 14, "fields": [{"fieldName": "hostname", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "practice", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "instanceid", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "zone", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "publicip", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "region", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}], "processores": [{"name": "split", "order": 0, "type": "split", "sourceField": "message", "schema": {"separator": ",", "entries": [{"index": 0, "name": "modeValue", "type": "string"}]}}, {"name": "expression", "order": 1, "type": "expression", "schema": {"entries": [{"name": "instanceid", "expression": "instanceId", "type": "string"}, {"name": "publicip", "expression": "publicIp", "type": "string"}, {"name": "practice", "expression": "modeValue?1:0", "type": "number"}, {"name": "zone", "expression": "zoneName", "type": "string"}, {"name": "hostname", "expression": "hostname", "type": "string"}, {"name": "region", "expression": "region", "type": "string"}]}}]}, {"name": "TEL_QUERY_BLOCKLIST_FAIL", "filterRule": "dataType=='QUERY-BLOCKLIST-FAIL'", "useStatus": 1, "order": 15, "fields": [{"fieldName": "hostname", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "mmc", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "instance", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "instanceid", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "zone", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "zmserviceip", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "call_queryblocklist_fail", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "region", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}], "processores": [{"name": "split", "order": 0, "type": "split", "sourceField": "message", "schema": {"separator": ",", "entries": [{"index": 0, "name": "eventType", "type": "number"}]}}, {"name": "expression", "order": 1, "type": "expression", "schema": {"entries": [{"name": "instanceid", "expression": "instanceId", "type": "string"}, {"name": "instance", "expression": "instance", "type": "string"}, {"name": "zone", "expression": "zoneName", "type": "string"}, {"name": "hostname", "expression": "hostname", "type": "string"}, {"name": "call_queryblocklist_fail", "expression": "eventType", "type": "number"}, {"name": "mmc", "expression": "mmc", "type": "string"}, {"name": "zmserviceip", "expression": "publicIp", "type": "string"}, {"name": "region", "expression": "region", "type": "string"}]}}]}, {"name": "TEL_CALL_JOIN_SUCCESS", "filterRule": "dataType=='CALL-JOIN-SUCCESS'", "useStatus": 1, "order": 16, "fields": [{"fieldName": "country_code", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "hostname", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "mmc", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "instanceid", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "zone", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "vendor", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "ip", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "call_join_success_count", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "region", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}], "processores": [{"name": "split", "order": 0, "type": "split", "sourceField": "message", "schema": {"separator": ",", "entries": [{"index": 0, "name": "callType", "type": "string"}, {"index": 1, "name": "billingType", "type": "number"}, {"index": 2, "name": "callerID", "type": "number"}, {"index": 3, "name": "sbcVIP", "type": "string"}, {"index": 4, "name": "vendorType", "type": "string"}, {"index": 5, "name": "countryCode", "type": "string"}, {"index": 6, "name": "countryName", "type": "string"}, {"index": 7, "name": "mgSipCallID", "type": "string"}, {"index": 8, "name": "bFail<PERSON>", "type": "string"}, {"index": 9, "name": "MSIP", "type": "string"}, {"index": 10, "name": "brdigeCallType", "type": "number"}]}}, {"name": "expression", "order": 1, "type": "expression", "schema": {"entries": [{"name": "call_join_success_count", "expression": "callerID", "type": "number"}, {"name": "instanceid", "expression": "instanceId", "type": "string"}, {"name": "country_code", "expression": "countryCode", "type": "string"}, {"name": "ip", "expression": "publicIp", "type": "string"}, {"name": "zone", "expression": "zoneName", "type": "string"}, {"name": "hostname", "expression": "hostname", "type": "string"}, {"name": "vendor", "expression": "vendorType", "type": "string"}, {"name": "mmc", "expression": "mmc", "type": "string"}, {"name": "region", "expression": "region", "type": "string"}]}}]}, {"name": "TEL_ASYNMQ_STATUS", "filterRule": "dataType=='ASYNMQ_STATUS'", "useStatus": 1, "order": 17, "fields": [{"fieldName": "mmc", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "instance", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "zmserviceip", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "produce_success_count", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "gwtype", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "produce_count", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "consume_fail_count", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "hostname", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "instanceid", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "zone", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "produce_fail_count", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "consume_count", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "consume_success_count", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "region", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "status", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}], "processores": [{"name": "split", "order": 0, "type": "split", "sourceField": "message", "schema": {"separator": ",", "entries": [{"index": 0, "name": "status", "type": "number"}, {"index": 1, "name": "connect_error_msg", "type": "string"}, {"index": 2, "name": "consume_count", "type": "number"}, {"index": 3, "name": "consume_success_count", "type": "number"}, {"index": 4, "name": "consume_fail_count", "type": "number"}, {"index": 5, "name": "produce_count", "type": "number"}, {"index": 6, "name": "produce_success_count", "type": "number"}, {"index": 7, "name": "produce_fail_count", "type": "number"}]}}, {"name": "expression", "order": 1, "type": "expression", "schema": {"entries": [{"name": "instanceid", "expression": "instanceId", "type": "string"}, {"name": "produce_count", "expression": "produce_count", "type": "number"}, {"name": "produce_fail_count", "expression": "produce_fail_count", "type": "number"}, {"name": "instance", "expression": "instance", "type": "string"}, {"name": "consume_fail_count", "expression": "consume_fail_count", "type": "number"}, {"name": "zone", "expression": "zoneName", "type": "string"}, {"name": "hostname", "expression": "hostname", "type": "string"}, {"name": "zmserviceip", "expression": "publicIp", "type": "string"}, {"name": "region", "expression": "region", "type": "string"}, {"name": "status", "expression": "status", "type": "number"}, {"name": "gwtype", "expression": "TC", "type": "string"}, {"name": "produce_success_count", "expression": "produce_success_count", "type": "number"}, {"name": "consume_success_count", "expression": "consume_success_count", "type": "number"}, {"name": "consume_count", "expression": "consume_count", "type": "number"}, {"name": "mmc", "expression": "mmc", "type": "string"}]}}]}, {"name": "TEL_MS_ERROR", "filterRule": "dataType=='MS-ERROR'", "useStatus": 1, "order": 18, "fields": [{"fieldName": "statuscode", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "mmc", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "instanceid", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "zone", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}], "processores": [{"name": "split", "order": 0, "type": "split", "sourceField": "message", "schema": {"separator": ",", "entries": [{"index": 0, "name": "statusCode", "type": "number"}, {"index": 1, "name": "msIP", "type": "string"}]}}, {"name": "expression", "order": 1, "type": "expression", "schema": {"entries": [{"name": "instanceid", "expression": "instanceId", "type": "string"}, {"name": "statuscode", "expression": "statusCode", "type": "number"}, {"name": "zone", "expression": "zoneName", "type": "string"}, {"name": "mmc", "expression": "mmc", "type": "string"}]}}]}], "metrics": [{"metricsName": "TEL_CURL_WEB_FAIL", "oldMetricsName": "TEL_CURL-WEB-FAIL", "tagNames": ["instanceid", "mmc", "region", "gwtype", "hostname", "zone", "instance", "zmserviceip"], "tagDetails": [{"tagName": "instanceid", "oldTagName": "InstanceId"}, {"tagName": "mmc", "oldTagName": "MMC"}, {"tagName": "region", "oldTagName": "Region"}, {"tagName": "gwtype", "oldTagName": "GWType"}, {"tagName": "hostname", "oldTagName": "Hostname"}, {"tagName": "zone", "oldTagName": "Zone"}, {"tagName": "instance", "oldTagName": "Instance"}, {"tagName": "zmserviceip"}], "type": 1, "enabled": true, "metricsFieldList": [{"fieldName": "call_web_fail_count.count", "oldFieldName": "call.web.fail.count", "fieldType": 1}], "metricsAggregation": {"aggPeriod": 60, "waitTime": 0, "filterCondition": "string.startsWith(instance, 'telgw')||string.startsWith(instance, 'tspgw')", "aggregationResultFilter": "", "metricsAggregationRuleCompose": {"common": [{"aggField": "call_web_fail_count", "fieldType": "number", "aggTypes": [{"value": "count", "condition": "", "fieldName": "call_web_fail_count.count"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "call_web_fail_count"}], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "TEL_CURL_WEB_FAIL"}, {"metricsName": "TEL_JOIN_FAIL", "oldMetricsName": "TEL_JOIN-FAIL", "tagNames": ["instanceid", "mmc", "region", "gwtype", "hostname", "zone", "zmserviceip"], "tagDetails": [{"tagName": "instanceid", "oldTagName": "InstanceId"}, {"tagName": "mmc", "oldTagName": "MMC"}, {"tagName": "region", "oldTagName": "Region"}, {"tagName": "gwtype", "oldTagName": "GWType"}, {"tagName": "hostname", "oldTagName": "Hostname"}, {"tagName": "zone", "oldTagName": "Zone"}, {"tagName": "zmserviceip"}], "type": 1, "enabled": true, "metricsFieldList": [{"fieldName": "join_zc_mmr_fail_count.count", "oldFieldName": "join.zc.mmr.fail.count", "fieldType": 1}], "metricsAggregation": {"aggPeriod": 60, "waitTime": 0, "filterCondition": "string.startsWith(instance, 'telgw')||string.startsWith(instance, 'tspgw')", "aggregationResultFilter": "", "metricsAggregationRuleCompose": {"common": [{"aggField": "join_zc_mmr_fail_count", "fieldType": "number", "aggTypes": [{"value": "count", "condition": "", "fieldName": "join_zc_mmr_fail_count.count"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "join_zc_mmr_fail_count"}], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "TEL_JOIN_FAIL"}, {"metricsName": "TEL_SERVER_STAT", "oldMetricsName": "TEL_SERVER-STAT", "tagNames": ["instanceid", "mmc", "region", "gwtype", "hostname", "zone", "zmserviceip", "capacity"], "tagDetails": [{"tagName": "instanceid", "oldTagName": "InstanceId"}, {"tagName": "mmc", "oldTagName": "MMC"}, {"tagName": "region", "oldTagName": "Region"}, {"tagName": "gwtype", "oldTagName": "GWType"}, {"tagName": "hostname", "oldTagName": "Hostname"}, {"tagName": "zone", "oldTagName": "Zone"}, {"tagName": "zmserviceip"}, {"tagName": "capacity", "oldTagName": "Capacity"}], "type": 1, "enabled": true, "metricsFieldList": [{"fieldName": "meeting_count_peak.max", "oldFieldName": "meeting.count.peak", "fieldType": 1}, {"fieldName": "telephony_count_peak.max", "oldFieldName": "telephony.count.peak", "fieldType": 1}], "metricsAggregation": {"aggPeriod": 60, "waitTime": 0, "filterCondition": "string.startsWith(instance, 'telgw')||string.startsWith(instance, 'tspgw')", "aggregationResultFilter": "", "metricsAggregationRuleCompose": {"common": [{"aggField": "meeting_count_peak", "fieldType": "number", "aggTypes": [{"value": "max", "condition": "", "fieldName": "meeting_count_peak.max"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "meeting_count_peak"}, {"aggField": "telephony_count_peak", "fieldType": "number", "aggTypes": [{"value": "max", "condition": "", "fieldName": "telephony_count_peak.max"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "telephony_count_peak"}], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "TEL_SERVER_STAT"}, {"metricsName": "TEL_SERVER_STATUS", "oldMetricsName": "TEL_SERVER-STATUS", "tagNames": ["instanceid", "mmc", "region", "gwtype", "hostname", "instance", "zone", "zmserviceip"], "tagDetails": [{"tagName": "instanceid", "oldTagName": "InstanceId"}, {"tagName": "mmc", "oldTagName": "MMC"}, {"tagName": "region", "oldTagName": "Region"}, {"tagName": "gwtype", "oldTagName": "GWType"}, {"tagName": "hostname", "oldTagName": "Hostname"}, {"tagName": "instance", "oldTagName": "Instance"}, {"tagName": "zone", "oldTagName": "Zone"}, {"tagName": "zmserviceip"}], "type": 1, "enabled": true, "metricsFieldList": [{"fieldName": "service_status.max", "oldFieldName": "service.status", "fieldType": 1}, {"fieldName": "service_idle.max", "oldFieldName": "service.IDLE", "fieldType": 1}, {"fieldName": "service_open.max", "oldFieldName": "service.OPEN", "fieldType": 1}, {"fieldName": "service_suspend.max", "oldFieldName": "service.SUSPEND", "fieldType": 1}, {"fieldName": "service_closed.max", "oldFieldName": "service.CLOSED", "fieldType": 1}, {"fieldName": "service_scale_in.max", "oldFieldName": "service.SCALE_IN", "fieldType": 1}, {"fieldName": "service_failover.max", "oldFieldName": "service.FAILOVER", "fieldType": 1}, {"fieldName": "service_not_support_create.max", "oldFieldName": "service.NOT_SUPPORT_CREATE", "fieldType": 1}, {"fieldName": "service_closing.max", "oldFieldName": "service.CLOSING", "fieldType": 1}, {"fieldName": "isprimary.max", "oldFieldName": "isPrimary", "fieldType": 1}], "metricsAggregation": {"aggPeriod": 60, "waitTime": 0, "filterCondition": "string.startsWith(instance, 'telgw')||string.startsWith(instance, 'tspgw')||string.startsWith(instance, 'tctrl')", "aggregationResultFilter": "", "metricsAggregationRuleCompose": {"common": [{"aggField": "service_status", "fieldType": "number", "aggTypes": [{"value": "max", "condition": "", "fieldName": "service_status.max"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "service_status"}, {"aggField": "service_idle", "fieldType": "number", "aggTypes": [{"value": "max", "condition": "", "fieldName": "service_idle.max"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "service_idle"}, {"aggField": "service_open", "fieldType": "number", "aggTypes": [{"value": "max", "condition": "", "fieldName": "service_open.max"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "service_open"}, {"aggField": "service_suspend", "fieldType": "number", "aggTypes": [{"value": "max", "condition": "", "fieldName": "service_suspend.max"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "service_suspend"}, {"aggField": "service_closed", "fieldType": "number", "aggTypes": [{"value": "max", "condition": "", "fieldName": "service_closed.max"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "service_closed"}, {"aggField": "service_scale_in", "fieldType": "number", "aggTypes": [{"value": "max", "condition": "", "fieldName": "service_scale_in.max"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "service_scale_in"}, {"aggField": "service_failover", "fieldType": "number", "aggTypes": [{"value": "max", "condition": "", "fieldName": "service_failover.max"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "service_failover"}, {"aggField": "service_not_support_create", "fieldType": "number", "aggTypes": [{"value": "max", "condition": "", "fieldName": "service_not_support_create.max"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "service_not_support_create"}, {"aggField": "service_closing", "fieldType": "number", "aggTypes": [{"value": "max", "condition": "", "fieldName": "service_closing.max"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "service_closing"}, {"aggField": "isprimary", "fieldType": "number", "aggTypes": [{"value": "max", "condition": "", "fieldName": "isprimary.max"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "isprimary"}], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "TEL_SERVER_STATUS"}, {"metricsName": "TEL_MS_DATA_EXCEPTIONAL", "oldMetricsName": "TEL_MS-DATA-EXCEPTIONAL", "tagNames": ["instanceid", "mmc", "region", "gwtype"], "tagDetails": [{"tagName": "instanceid", "oldTagName": "InstanceId"}, {"tagName": "mmc", "oldTagName": "MMC"}, {"tagName": "region", "oldTagName": "Region"}, {"tagName": "gwtype", "oldTagName": "GWType"}], "type": 1, "enabled": true, "metricsFieldList": [{"fieldName": "ms_data_exceptional_count.count", "oldFieldName": "ms.data.exceptional.count", "fieldType": 1}], "metricsAggregation": {"aggPeriod": 60, "waitTime": 0, "filterCondition": "string.startsWith(instance, 'telgw')||string.startsWith(instance, 'tspgw')", "aggregationResultFilter": "", "metricsAggregationRuleCompose": {"common": [{"aggField": "ms_data_exceptional_count", "fieldType": "number", "aggTypes": [{"value": "count", "condition": "", "fieldName": "ms_data_exceptional_count.count"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "ms_data_exceptional_count"}], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "TEL_MS_DATA_EXCEPTIONAL"}, {"metricsName": "TSP_CONNECT_ADAPTOR_FAIL", "oldMetricsName": "TSP_CONNECT-ADAPTOR-FAIL", "tagNames": ["instanceid", "mmc", "region", "gwtype", "zmserviceip"], "tagDetails": [{"tagName": "instanceid", "oldTagName": "InstanceId"}, {"tagName": "mmc", "oldTagName": "MMC"}, {"tagName": "region", "oldTagName": "Region"}, {"tagName": "gwtype", "oldTagName": "GWType"}, {"tagName": "zmserviceip"}], "type": 1, "enabled": true, "metricsFieldList": [{"fieldName": "connect_adaptor_fail_count.count", "oldFieldName": "connect.adaptor.fail.count", "fieldType": 1}], "metricsAggregation": {"aggPeriod": 60, "waitTime": 0, "filterCondition": "string.startsWith(instance, 'tspgw')", "aggregationResultFilter": "", "metricsAggregationRuleCompose": {"common": [{"aggField": "connect_adaptor_fail_count", "fieldType": "number", "aggTypes": [{"value": "count", "condition": "", "fieldName": "connect_adaptor_fail_count.count"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "connect_adaptor_fail_count"}], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "TEL_CONNECT_ADAPTOR_FAIL"}, {"metricsName": "TSP_CREATE_BRIDGE_CALL_FAIL", "oldMetricsName": "TSP_CREATE-BRIDGE-CALL-FAIL", "tagNames": ["instanceid", "mmc", "region", "gwtype"], "tagDetails": [{"tagName": "instanceid", "oldTagName": "InstanceId"}, {"tagName": "mmc", "oldTagName": "MMC"}, {"tagName": "region", "oldTagName": "Region"}, {"tagName": "gwtype", "oldTagName": "GWType"}], "type": 1, "enabled": true, "metricsFieldList": [{"fieldName": "create_bridge_call_fail_count.count", "oldFieldName": "create.bridge.call.fail.count", "fieldType": 1}], "metricsAggregation": {"aggPeriod": 60, "waitTime": 0, "filterCondition": "string.startsWith(instance, 'tspgw')", "aggregationResultFilter": "", "metricsAggregationRuleCompose": {"common": [{"aggField": "create_bridge_call_fail_count", "fieldType": "number", "aggTypes": [{"value": "count", "condition": "", "fieldName": "create_bridge_call_fail_count.count"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "create_bridge_call_fail_count"}], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "TEL_CREATE_BRIDGE_CALL_FAIL"}, {"metricsName": "TSP_CREATE_CONF_FAIL", "oldMetricsName": "TSP_CREATE-CONF-FAIL", "tagNames": ["instanceid", "mmc", "region", "gwtype", "zmserviceip"], "tagDetails": [{"tagName": "instanceid", "oldTagName": "InstanceId"}, {"tagName": "mmc", "oldTagName": "MMC"}, {"tagName": "region", "oldTagName": "Region"}, {"tagName": "gwtype", "oldTagName": "GWType"}, {"tagName": "zmserviceip"}], "type": 1, "enabled": true, "metricsFieldList": [{"fieldName": "create_conf_fail_count.count", "oldFieldName": "create.conf.fail.count", "fieldType": 1}], "metricsAggregation": {"aggPeriod": 60, "waitTime": 0, "filterCondition": "string.startsWith(instance, 'tspgw')", "aggregationResultFilter": "", "metricsAggregationRuleCompose": {"common": [{"aggField": "create_conf_fail_count", "fieldType": "number", "aggTypes": [{"value": "count", "condition": "", "fieldName": "create_conf_fail_count.count"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "create_conf_fail_count"}], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "TEL_CREATE_CONF_FAIL"}, {"metricsName": "TSP_URL_STAT", "tagNames": ["instanceid", "mmc", "region", "gwtype", "vendor", "url"], "tagDetails": [{"tagName": "instanceid", "oldTagName": "InstanceId"}, {"tagName": "mmc", "oldTagName": "MMC"}, {"tagName": "region", "oldTagName": "Region"}, {"tagName": "gwtype", "oldTagName": "GWType"}, {"tagName": "vendor", "oldTagName": "<PERSON><PERSON><PERSON>"}, {"tagName": "url", "oldTagName": "URL"}], "type": 1, "enabled": true, "metricsFieldList": [{"fieldName": "meeting_count_peak.max", "oldFieldName": "meeting.count.peak", "fieldType": 1}, {"fieldName": "phone_count_peak.max", "oldFieldName": "phone.count.peak", "fieldType": 1}], "metricsAggregation": {"aggPeriod": 60, "waitTime": 0, "filterCondition": "string.startsWith(instance, 'tspgw')", "aggregationResultFilter": "", "metricsAggregationRuleCompose": {"common": [{"aggField": "meeting_count_peak", "fieldType": "number", "aggTypes": [{"value": "max", "condition": "", "fieldName": "meeting_count_peak.max"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "meeting_count_peak"}, {"aggField": "phone_count_peak", "fieldType": "number", "aggTypes": [{"value": "max", "condition": "", "fieldName": "phone_count_peak.max"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "phone_count_peak"}], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "TEL_TSP_URL_STAT"}, {"metricsName": "TEL_VENDOR_STAT", "oldMetricsName": "TEL_VENDOR-STAT", "tagNames": ["instanceid", "mmc", "zone", "vendor", "zmserviceip"], "tagDetails": [{"tagName": "instanceid", "oldTagName": "InstanceId"}, {"tagName": "mmc", "oldTagName": "MMC"}, {"tagName": "zone", "oldTagName": "Zone"}, {"tagName": "vendor", "oldTagName": "<PERSON><PERSON><PERSON>"}, {"tagName": "zmserviceip"}], "type": 2, "enabled": true, "metricsFieldList": [{"fieldName": "callin_internal_phones", "fieldType": 1}, {"fieldName": "callout_internal_phones", "fieldType": 1}, {"fieldName": "callout_phones", "fieldType": 1}, {"fieldName": "others_phones", "fieldType": 1}, {"fieldName": "toll_phones", "fieldType": 1}, {"fieldName": "tollfree_phones", "fieldType": 1}, {"fieldName": "phones", "fieldType": 1}], "metricsAggregation": {"metricsAggregationRuleCompose": {"common": [], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "TEL_VENDOR_STAT"}, {"metricsName": "TEL_GLOBAL_STAT", "oldMetricsName": "TEL_GLOBAL-STAT", "tagNames": ["instanceid", "mmc", "zone", "gwtype", "instance", "region"], "tagDetails": [{"tagName": "instanceid", "oldTagName": "InstanceId"}, {"tagName": "mmc", "oldTagName": "MMC"}, {"tagName": "zone", "oldTagName": "Zone"}, {"tagName": "gwtype", "oldTagName": "GWType"}, {"tagName": "instance", "oldTagName": "Instance"}, {"tagName": "region", "oldTagName": "Region"}], "type": 2, "enabled": true, "metricsFieldList": [{"fieldName": "ongoingconferences", "oldFieldName": "OngoingConferences", "fieldType": 1}, {"fieldName": "onlinephonecount", "oldFieldName": "OnlinePhoneCount", "fieldType": 1}, {"fieldName": "incomingcalls", "oldFieldName": "IncomingCalls", "fieldType": 1}, {"fieldName": "outdialfailures", "oldFieldName": "OutDialFailures", "fieldType": 1}, {"fieldName": "joinmeetingfailures", "oldFieldName": "JoinMeetingFailures", "fieldType": 1}, {"fieldName": "mstimeouts", "oldFieldName": "MSTimeouts", "fieldType": 1}, {"fieldName": "mserrors", "oldFieldName": "MSErrors", "fieldType": 1}, {"fieldName": "deadmscount", "oldFieldName": "DeadMSCount", "fieldType": 1}], "metricsAggregation": {"metricsAggregationRuleCompose": {"common": [], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "TEL_GLOBAL_STAT"}, {"metricsName": "TC_CC_LOST", "oldMetricsName": "TC_CC-LOST", "tagNames": ["cluster", "hostname", "instance", "instanceid", "mmc", "region", "zone"], "tagDetails": [{"tagName": "cluster", "oldTagName": "Cluster"}, {"tagName": "hostname", "oldTagName": "Hostname"}, {"tagName": "instance", "oldTagName": "Instance"}, {"tagName": "instanceid", "oldTagName": "InstanceId"}, {"tagName": "mmc", "oldTagName": "MMC"}, {"tagName": "region", "oldTagName": "Region"}, {"tagName": "zone", "oldTagName": "Zone"}], "type": 1, "enabled": true, "metricsFieldList": [{"fieldName": "connect_cc_fail_count.count", "oldFieldName": "connect.cc.fail.count", "fieldType": 1}], "metricsAggregation": {"aggPeriod": 60, "waitTime": 0, "filterCondition": "", "aggregationResultFilter": "", "metricsAggregationRuleCompose": {"common": [{"aggField": "connect_cc_fail_count", "fieldType": "number", "aggTypes": [{"value": "count", "condition": "", "fieldName": "connect_cc_fail_count.count"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "connect_cc_fail_count"}], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "TEL_CC_LOST"}, {"metricsName": "TC_TC_STAT", "oldMetricsName": "TC_TC-STAT", "tagNames": ["cluster", "hostname", "instance", "instanceid", "mmc", "region", "zone"], "tagDetails": [{"tagName": "cluster", "oldTagName": "Cluster"}, {"tagName": "hostname", "oldTagName": "Hostname"}, {"tagName": "instance", "oldTagName": "Instance"}, {"tagName": "instanceid", "oldTagName": "InstanceId"}, {"tagName": "mmc", "oldTagName": "MMC"}, {"tagName": "region", "oldTagName": "Region"}, {"tagName": "zone", "oldTagName": "Zone"}], "type": 1, "enabled": true, "metricsFieldList": [{"fieldName": "meeting_count_peak.max", "oldFieldName": "meeting.count.peak", "fieldType": 1}, {"fieldName": "telephony_count_peak.max", "oldFieldName": "telephony.count.peak", "fieldType": 1}, {"fieldName": "telgw_count.max", "oldFieldName": "telgw.count", "fieldType": 1}, {"fieldName": "suspend_telgw_count.max", "oldFieldName": "suspend.telgw.count", "fieldType": 1}, {"fieldName": "primary.max", "fieldType": 1}], "metricsAggregation": {"aggPeriod": 60, "waitTime": 0, "filterCondition": "", "aggregationResultFilter": "", "metricsAggregationRuleCompose": {"common": [{"aggField": "meeting_count_peak", "fieldType": "number", "aggTypes": [{"value": "max", "condition": "", "fieldName": "meeting_count_peak.max"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "meeting_count_peak"}, {"aggField": "telephony_count_peak", "fieldType": "number", "aggTypes": [{"value": "max", "condition": "", "fieldName": "telephony_count_peak.max"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "telephony_count_peak"}, {"aggField": "telgw_count", "fieldType": "number", "aggTypes": [{"value": "max", "condition": "", "fieldName": "telgw_count.max"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "telgw_count"}, {"aggField": "suspend_telgw_count", "fieldType": "number", "aggTypes": [{"value": "max", "condition": "", "fieldName": "suspend_telgw_count.max"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "suspend_telgw_count"}, {"aggField": "primary", "fieldType": "number", "aggTypes": [{"value": "max", "condition": "", "fieldName": "primary.max"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "primary"}], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "TEL_TC_STAT"}, {"metricsName": "TEL_SERVER_STAT_TEST", "oldMetricsName": "TEL_SERVER-STAT_TEST", "tagNames": ["hostname", "instance", "instanceid"], "tagDetails": [{"tagName": "hostname", "oldTagName": "Hostname"}, {"tagName": "instance", "oldTagName": "Instance"}, {"tagName": "instanceid", "oldTagName": "InstanceId"}], "type": 1, "enabled": true, "metricsFieldList": [{"fieldName": "meeting_count_peak.max", "oldFieldName": "meeting.count.peak", "fieldType": 1}], "metricsAggregation": {"aggPeriod": 60, "waitTime": 0, "filterCondition": "", "aggregationResultFilter": "", "metricsAggregationRuleCompose": {"common": [{"aggField": "meeting_count_peak", "fieldType": "number", "aggTypes": [{"value": "max", "condition": "", "fieldName": "meeting_count_peak.max"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "meeting_count_peak"}], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "TEL_SERVER_STAT"}, {"metricsName": "TEL_SERVER_STATUS_TEST", "oldMetricsName": "TEL_SERVER-STATUS-TEST", "tagNames": ["cluster", "env", "hostname", "instance", "mmc", "region", "zone", "instanceid"], "tagDetails": [{"tagName": "cluster", "oldTagName": "Cluster"}, {"tagName": "env", "oldTagName": "ENV"}, {"tagName": "hostname", "oldTagName": "Hostname"}, {"tagName": "instance", "oldTagName": "Instance"}, {"tagName": "mmc", "oldTagName": "MMC"}, {"tagName": "region", "oldTagName": "Region"}, {"tagName": "zone", "oldTagName": "Zone"}, {"tagName": "instanceid", "oldTagName": "InstanceId"}], "type": 1, "enabled": true, "metricsFieldList": [{"fieldName": "status.count", "fieldType": 1}], "metricsAggregation": {"aggPeriod": 60, "waitTime": 0, "filterCondition": "", "aggregationResultFilter": "", "metricsAggregationRuleCompose": {"common": [{"aggField": "status", "fieldType": "number", "aggTypes": [{"value": "count", "condition": "", "fieldName": "status.count"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "status"}], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "TEL_SERVER_STATUS"}, {"metricsName": "TEL_GLOBAL_STAT_MAX", "oldMetricsName": "TEL_GLOBAL-STAT_MAX", "tagNames": ["cluster", "env", "hostname", "instanceid", "mmc", "region", "zone", "zmserviceip"], "tagDetails": [{"tagName": "cluster", "oldTagName": "Cluster"}, {"tagName": "env", "oldTagName": "ENV"}, {"tagName": "hostname", "oldTagName": "Hostname"}, {"tagName": "instanceid", "oldTagName": "InstanceId"}, {"tagName": "mmc", "oldTagName": "MMC"}, {"tagName": "region", "oldTagName": "Region"}, {"tagName": "zone", "oldTagName": "Zone"}, {"tagName": "zmserviceip"}], "type": 1, "enabled": true, "metricsFieldList": [{"fieldName": "ongoingconference_max.max", "oldFieldName": "Ongoingconference_MAX", "fieldType": 1}, {"fieldName": "ongoingcall_max.max", "oldFieldName": "Ongoingcall_MAX", "fieldType": 1}], "metricsAggregation": {"aggPeriod": 60, "waitTime": 0, "filterCondition": "string.startsWith(instance, 'telgw')||string.startsWith(instance, 'tspgw')", "aggregationResultFilter": "", "metricsAggregationRuleCompose": {"common": [{"aggField": "ongoingconference_max", "fieldType": "number", "aggTypes": [{"value": "max", "condition": "", "fieldName": "ongoingconference_max.max"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "ongoingconference_max"}, {"aggField": "ongoingcall_max", "fieldType": "number", "aggTypes": [{"value": "max", "condition": "", "fieldName": "ongoingcall_max.max"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "ongoingcall_max"}], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "TEL_GLOBAL_STAT"}, {"metricsName": "TEL_CALL_JOIN_FAIL", "oldMetricsName": "TEL_CALL-JOIN-FAIL", "tagNames": ["instanceid", "mmc", "region", "gwtype", "hostname", "zone", "ip", "error_code", "country_code", "country_name", "vendor"], "tagDetails": [{"tagName": "instanceid", "oldTagName": "InstanceId"}, {"tagName": "mmc", "oldTagName": "MMC"}, {"tagName": "region", "oldTagName": "Region"}, {"tagName": "gwtype", "oldTagName": "GWType"}, {"tagName": "hostname", "oldTagName": "Hostname"}, {"tagName": "zone", "oldTagName": "Zone"}, {"tagName": "ip"}, {"tagName": "error_code", "oldTagName": "error_Code"}, {"tagName": "country_code", "oldTagName": "country_Code"}, {"tagName": "country_name", "oldTagName": "country_Name"}, {"tagName": "vendor", "oldTagName": "<PERSON><PERSON><PERSON>"}], "type": 2, "enabled": true, "metricsFieldList": [{"fieldName": "i<PERSON><PERSON><PERSON><PERSON>", "oldFieldName": "ivrReason", "fieldType": 1}, {"fieldName": "vendor_thinq", "oldFieldName": "vendor.ThinQ", "fieldType": 1}, {"fieldName": "vendor_union400", "oldFieldName": "vendor.Union400", "fieldType": 1}, {"fieldName": "vendor_iristel", "oldFieldName": "vendor.Iristel", "fieldType": 1}, {"fieldName": "vendor_iristel_new", "oldFieldName": "vendor.Iristel_new", "fieldType": 1}, {"fieldName": "vendor_iristel_int", "oldFieldName": "vendor.Iristel_Int", "fieldType": 1}, {"fieldName": "vendor_iristel_int_new", "oldFieldName": "vendor.Iristel_Int_new", "fieldType": 1}, {"fieldName": "vendor_rogers", "oldFieldName": "vendor.<PERSON>", "fieldType": 1}, {"fieldName": "vendor_level3", "oldFieldName": "vendor.Level3", "fieldType": 1}, {"fieldName": "vendor_tata", "oldFieldName": "vendor.TATA", "fieldType": 1}, {"fieldName": "vendor_bandwidth", "oldFieldName": "vendor.Bandwidth", "fieldType": 1}, {"fieldName": "vendor_xo", "oldFieldName": "vendor.XO", "fieldType": 1}, {"fieldName": "vendor_peerless", "oldFieldName": "vendor.Peerless", "fieldType": 1}, {"fieldName": "vendor_bandwidth_int", "oldFieldName": "vendor.Bandwidth_Int", "fieldType": 1}, {"fieldName": "vendor_inteliquent", "oldFieldName": "vendor.Inteliquent", "fieldType": 1}, {"fieldName": "vendor_inteliquent_intl", "oldFieldName": "vendor.Inteliquent_INTL", "fieldType": 1}, {"fieldName": "vendor_orangecarrier", "oldFieldName": "vendor.orangecarrier", "fieldType": 1}, {"fieldName": "vendor_bics", "oldFieldName": "vendor.bics", "fieldType": 1}, {"fieldName": "vendor_vodafone", "oldFieldName": "vendor.vodafone", "fieldType": 1}, {"fieldName": "vendor_colt", "oldFieldName": "vendor.Colt", "fieldType": 1}, {"fieldName": "vendor_voxbone", "oldFieldName": "vendor.VoxBone", "fieldType": 1}, {"fieldName": "vendor_symbio", "oldFieldName": "vendor.Symbio", "fieldType": 1}, {"fieldName": "vendor_tnzi", "oldFieldName": "vendor.TNZI", "fieldType": 1}, {"fieldName": "vendor_otw", "oldFieldName": "vendor.OTW", "fieldType": 1}, {"fieldName": "errorringtimeout", "oldFieldName": "errorRingTimeout", "fieldType": 1}, {"fieldName": "errorconnecttimeout", "oldFieldName": "errorConnectTimeout", "fieldType": 1}, {"fieldName": "ivr_end_default", "oldFieldName": "IVR_END_DEFAULT", "fieldType": 1}, {"fieldName": "ivr_end_meeting_not_startedd", "oldFieldName": "IVR_END_MEETING_NOT_STARTEDD", "fieldType": 1}, {"fieldName": "ivr_end_meeting_not_exist", "oldFieldName": "IVR_END_MEETING_NOT_EXIST", "fieldType": 1}, {"fieldName": "ivr_end_unmatch_phonenumber", "oldFieldName": "IVR_END_UNMATCH_PHONENUMBER", "fieldType": 1}, {"fieldName": "ivr_end_not_allow_pstn", "oldFieldName": "IVR_END_NOT_ALLOW_PSTN", "fieldType": 1}, {"fieldName": "ivr_end_meeting_locked", "oldFieldName": "IVR_END_MEETING_LOCKED", "fieldType": 1}, {"fieldName": "ivr_end_meeting_full", "oldFieldName": "IVR_END_MEETING_FULL", "fieldType": 1}, {"fieldName": "ivr_end_system_error", "oldFieldName": "IVR_END_SYSTEM_ERROR", "fieldType": 1}, {"fieldName": "ivr_end_user_hangup", "oldFieldName": "IVR_END_USER_HANGUP", "fieldType": 1}, {"fieldName": "ivr_end_redirect_to_meeting", "oldFieldName": "IVR_END_REDIRECT_TO_MEETING", "fieldType": 1}, {"fieldName": "ivr_end_no_input", "oldFieldName": "IVR_END_NO_INPUT", "fieldType": 1}, {"fieldName": "ivr_end_incorrect_input", "oldFieldName": "IVR_END_INCORRECT_INPUT", "fieldType": 1}, {"fieldName": "ivr_end_meeting_closed", "oldFieldName": "IVR_END_MEETING_CLOSED", "fieldType": 1}, {"fieldName": "ivr_end_meeting_not_active", "oldFieldName": "IVR_END_MEETING_NOT_ACTIVE", "fieldType": 1}, {"fieldName": "ivr_end_callout_no_press_one", "oldFieldName": "IVR_END_CALLOUT_NO_PRESS_ONE", "fieldType": 1}, {"fieldName": "ivr_end_siptrunk_error", "oldFieldName": "IVR_END_SIPTRUNK_ERROR", "fieldType": 1}, {"fieldName": "ivr_end_ms_error", "oldFieldName": "IVR_END_MS_ERROR", "fieldType": 1}, {"fieldName": "ivr_end_client_cancel", "oldFieldName": "IVR_END_CLIENT_CANCEL", "fieldType": 1}, {"fieldName": "ivr_client_kickoff_or_leavemeeting", "oldFieldName": "IVR_CLIENT_KICKOFF_OR_LEAVEMEETING", "fieldType": 1}, {"fieldName": "ivr_callout_number_blocked", "oldFieldName": "IVR_CALLOUT_NUMBER_BLOCKED", "fieldType": 1}, {"fieldName": "ivr_end_no_conf_found", "oldFieldName": "IVR_END_NO_CONF_FOUND", "fieldType": 1}, {"fieldName": "ivr_end_block_no_host", "oldFieldName": "IVR_END_BLOCK_NO_HOST", "fieldType": 1}, {"fieldName": "ivr_end_block_too_frequent", "oldFieldName": "IVR_END_BLOCK_TOO_FREQUENT", "fieldType": 1}, {"fieldName": "ivr_end_no_callout_privilege", "oldFieldName": "IVR_END_NO_CALLOUT_PRIVILEGE", "fieldType": 1}, {"fieldName": "ivr_end_tf_jbh_block", "oldFieldName": "IVR_END_TF_JBH_BLOCK", "fieldType": 1}, {"fieldName": "ivr_end_no_media_proxy", "oldFieldName": "IVR_END_NO_MEDIA_PROXY", "fieldType": 1}, {"fieldName": "ivr_end_geo_restrict_phone", "oldFieldName": "IVR_END_GEO_RESTRICT_PHONE", "fieldType": 1}, {"fieldName": "ivr_end_phonenumber_invalid", "oldFieldName": "IVR_END_PHONENUMBER_INVALID", "fieldType": 1}, {"fieldName": "ivr_end_not_allow_join_after_host_remove", "oldFieldName": "IVR_END_NOT_ALLOW_JOIN_AFTER_HOST_REMOVE", "fieldType": 1}], "metricsAggregation": {"metricsAggregationRuleCompose": {"common": [], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "TEL_CALL_JOIN_FAIL"}, {"metricsName": "TEL_CALL_JOIN_FAIL_AI", "oldMetricsName": "TEL_CALL-JOIN-FAIL_AI", "tagNames": ["region", "zone", "error_code", "country_code"], "tagDetails": [{"tagName": "region", "oldTagName": "Region"}, {"tagName": "zone", "oldTagName": "Zone"}, {"tagName": "error_code", "oldTagName": "error_Code"}, {"tagName": "country_code", "oldTagName": "country_Code"}], "type": 1, "enabled": true, "metricsFieldList": [{"fieldName": "ivrreason.sum", "oldFieldName": "ivrReason", "fieldType": 1}, {"fieldName": "vendor_thinq.sum", "oldFieldName": "vendor.ThinQ", "fieldType": 1}, {"fieldName": "vendor_union400.sum", "oldFieldName": "vendor.Union400", "fieldType": 1}, {"fieldName": "vendor_iristel.sum", "oldFieldName": "vendor.Iristel", "fieldType": 1}, {"fieldName": "vendor_iristel_new.sum", "oldFieldName": "vendor.Iristel_new", "fieldType": 1}, {"fieldName": "vendor_iristel_int.sum", "oldFieldName": "vendor.Iristel_Int", "fieldType": 1}, {"fieldName": "vendor_iristel_int_new.sum", "oldFieldName": "vendor.Iristel_Int_new", "fieldType": 1}, {"fieldName": "vendor_rogers.sum", "oldFieldName": "vendor.<PERSON>", "fieldType": 1}, {"fieldName": "vendor_level3.sum", "oldFieldName": "vendor.Level3", "fieldType": 1}, {"fieldName": "vendor_tata.sum", "oldFieldName": "vendor.TATA", "fieldType": 1}, {"fieldName": "vendor_bandwidth.sum", "oldFieldName": "vendor.Bandwidth", "fieldType": 1}, {"fieldName": "vendor_xo.sum", "oldFieldName": "vendor.XO", "fieldType": 1}, {"fieldName": "vendor_peerless.sum", "oldFieldName": "vendor.Peerless", "fieldType": 1}, {"fieldName": "vendor_bandwidth_int.sum", "oldFieldName": "vendor.Bandwidth_Int", "fieldType": 1}, {"fieldName": "vendor_inteliquent.sum", "oldFieldName": "vendor.Inteliquent", "fieldType": 1}, {"fieldName": "vendor_inteliquent_intl.sum", "oldFieldName": "vendor.Inteliquent_INTL", "fieldType": 1}, {"fieldName": "vendor_orangecarrier.sum", "oldFieldName": "vendor.orangecarrier", "fieldType": 1}, {"fieldName": "vendor_bics.sum", "oldFieldName": "vendor.bics", "fieldType": 1}, {"fieldName": "vendor_vodafone.sum", "oldFieldName": "vendor.vodafone", "fieldType": 1}, {"fieldName": "vendor_colt.sum", "oldFieldName": "vendor.Colt", "fieldType": 1}, {"fieldName": "vendor_voxbone.sum", "oldFieldName": "vendor.VoxBone", "fieldType": 1}, {"fieldName": "vendor_symbio.sum", "oldFieldName": "vendor.Symbio", "fieldType": 1}, {"fieldName": "vendor_tnzi.sum", "oldFieldName": "vendor.TNZI", "fieldType": 1}, {"fieldName": "vendor_otw.sum", "oldFieldName": "vendor.OTW", "fieldType": 1}, {"fieldName": "errorringtimeout.sum", "oldFieldName": "errorRingTimeout", "fieldType": 1}, {"fieldName": "errorconnecttimeout.sum", "oldFieldName": "errorConnectTimeout", "fieldType": 1}], "metricsAggregation": {"aggPeriod": 300, "waitTime": 0, "filterCondition": "string.startsWith(instance, 'telgw')||string.startsWith(instance, 'tspgw')", "aggregationResultFilter": "", "metricsAggregationRuleCompose": {"common": [{"aggField": "i<PERSON><PERSON><PERSON><PERSON>", "fieldType": "number", "aggTypes": [{"value": "sum", "condition": "", "fieldName": "ivrreason.sum"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "i<PERSON><PERSON><PERSON><PERSON>"}, {"aggField": "vendor_thinq", "fieldType": "number", "aggTypes": [{"value": "sum", "condition": "", "fieldName": "vendor_thinq.sum"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "vendor_thinq"}, {"aggField": "vendor_union400", "fieldType": "number", "aggTypes": [{"value": "sum", "condition": "", "fieldName": "vendor_union400.sum"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "vendor_union400"}, {"aggField": "vendor_iristel", "fieldType": "number", "aggTypes": [{"value": "sum", "condition": "", "fieldName": "vendor_iristel.sum"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "vendor_iristel"}, {"aggField": "vendor_iristel_new", "fieldType": "number", "aggTypes": [{"value": "sum", "condition": "", "fieldName": "vendor_iristel_new.sum"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "vendor_iristel_new"}, {"aggField": "vendor_iristel_int", "fieldType": "number", "aggTypes": [{"value": "sum", "condition": "", "fieldName": "vendor_iristel_int.sum"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "vendor_iristel_int"}, {"aggField": "vendor_iristel_int_new", "fieldType": "number", "aggTypes": [{"value": "sum", "condition": "", "fieldName": "vendor_iristel_int_new.sum"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "vendor_iristel_int_new"}, {"aggField": "vendor_rogers", "fieldType": "number", "aggTypes": [{"value": "sum", "condition": "", "fieldName": "vendor_rogers.sum"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "vendor_rogers"}, {"aggField": "vendor_level3", "fieldType": "number", "aggTypes": [{"value": "sum", "condition": "", "fieldName": "vendor_level3.sum"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "vendor_level3"}, {"aggField": "vendor_tata", "fieldType": "number", "aggTypes": [{"value": "sum", "condition": "", "fieldName": "vendor_tata.sum"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "vendor_tata"}, {"aggField": "vendor_bandwidth", "fieldType": "number", "aggTypes": [{"value": "sum", "condition": "", "fieldName": "vendor_bandwidth.sum"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "vendor_bandwidth"}, {"aggField": "vendor_xo", "fieldType": "number", "aggTypes": [{"value": "sum", "condition": "", "fieldName": "vendor_xo.sum"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "vendor_xo"}, {"aggField": "vendor_peerless", "fieldType": "number", "aggTypes": [{"value": "sum", "condition": "", "fieldName": "vendor_peerless.sum"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "vendor_peerless"}, {"aggField": "vendor_bandwidth_int", "fieldType": "number", "aggTypes": [{"value": "sum", "condition": "", "fieldName": "vendor_bandwidth_int.sum"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "vendor_bandwidth_int"}, {"aggField": "vendor_inteliquent", "fieldType": "number", "aggTypes": [{"value": "sum", "condition": "", "fieldName": "vendor_inteliquent.sum"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "vendor_inteliquent"}, {"aggField": "vendor_inteliquent_intl", "fieldType": "number", "aggTypes": [{"value": "sum", "condition": "", "fieldName": "vendor_inteliquent_intl.sum"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "vendor_inteliquent_intl"}, {"aggField": "vendor_orangecarrier", "fieldType": "number", "aggTypes": [{"value": "sum", "condition": "", "fieldName": "vendor_orangecarrier.sum"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "vendor_orangecarrier"}, {"aggField": "vendor_bics", "fieldType": "number", "aggTypes": [{"value": "sum", "condition": "", "fieldName": "vendor_bics.sum"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "vendor_bics"}, {"aggField": "vendor_vodafone", "fieldType": "number", "aggTypes": [{"value": "sum", "condition": "", "fieldName": "vendor_vodafone.sum"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "vendor_vodafone"}, {"aggField": "vendor_colt", "fieldType": "number", "aggTypes": [{"value": "sum", "condition": "", "fieldName": "vendor_colt.sum"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "vendor_colt"}, {"aggField": "vendor_voxbone", "fieldType": "number", "aggTypes": [{"value": "sum", "condition": "", "fieldName": "vendor_voxbone.sum"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "vendor_voxbone"}, {"aggField": "vendor_symbio", "fieldType": "number", "aggTypes": [{"value": "sum", "condition": "", "fieldName": "vendor_symbio.sum"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "vendor_symbio"}, {"aggField": "vendor_tnzi", "fieldType": "number", "aggTypes": [{"value": "sum", "condition": "", "fieldName": "vendor_tnzi.sum"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "vendor_tnzi"}, {"aggField": "vendor_otw", "fieldType": "number", "aggTypes": [{"value": "sum", "condition": "", "fieldName": "vendor_otw.sum"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "vendor_otw"}, {"aggField": "errorringtimeout", "fieldType": "number", "aggTypes": [{"value": "sum", "condition": "", "fieldName": "errorringtimeout.sum"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "errorringtimeout"}, {"aggField": "errorconnecttimeout", "fieldType": "number", "aggTypes": [{"value": "sum", "condition": "", "fieldName": "errorconnecttimeout.sum"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "errorconnecttimeout"}], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "TEL_CALL_JOIN_FAIL"}, {"metricsName": "TEL_MQ_PRACTICE_MODE", "tagNames": ["instanceid", "hostname", "region", "zone", "publicip"], "tagDetails": [{"tagName": "instanceid", "oldTagName": "InstanceId"}, {"tagName": "hostname", "oldTagName": "Hostname"}, {"tagName": "region", "oldTagName": "Region"}, {"tagName": "zone", "oldTagName": "Zone"}, {"tagName": "publicip", "oldTagName": "PublicIp"}], "type": 2, "enabled": true, "metricsFieldList": [{"fieldName": "practice", "fieldType": 1}], "metricsAggregation": {"metricsAggregationRuleCompose": {"common": [], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "TEL_ASYNCMQ_PRACTICE_MODE"}, {"metricsName": "TEL_QUERY_BLOCKLIST_FAIL", "oldMetricsName": "TEL_QUERY-BLOCKLIST-FAIL", "tagNames": ["instanceid", "mmc", "region", "hostname", "zone", "instance", "zmserviceip"], "tagDetails": [{"tagName": "instanceid", "oldTagName": "InstanceId"}, {"tagName": "mmc", "oldTagName": "MMC"}, {"tagName": "region", "oldTagName": "Region"}, {"tagName": "hostname", "oldTagName": "Hostname"}, {"tagName": "zone", "oldTagName": "Zone"}, {"tagName": "instance", "oldTagName": "Instance"}, {"tagName": "zmserviceip"}], "type": 1, "enabled": true, "metricsFieldList": [{"fieldName": "call_queryblocklist_fail.count", "oldFieldName": "call.queryblocklist_fail", "fieldType": 1}], "metricsAggregation": {"aggPeriod": 60, "waitTime": 0, "filterCondition": "string.startsWith(instance, 'telgw')", "aggregationResultFilter": "", "metricsAggregationRuleCompose": {"common": [{"aggField": "call_queryblocklist_fail", "fieldType": "number", "aggTypes": [{"value": "count", "condition": "", "fieldName": "call_queryblocklist_fail.count"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "call_queryblocklist_fail"}], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "TEL_QUERY_BLOCKLIST_FAIL"}, {"metricsName": "TEL_CALL_JOIN_SUCCESS", "oldMetricsName": "TEL_CALL-JOIN-SUCCESS", "tagNames": ["instanceid", "mmc", "region", "hostname", "zone", "ip", "country_code", "vendor"], "tagDetails": [{"tagName": "instanceid", "oldTagName": "InstanceId"}, {"tagName": "mmc", "oldTagName": "MMC"}, {"tagName": "region", "oldTagName": "Region"}, {"tagName": "hostname", "oldTagName": "Hostname"}, {"tagName": "zone", "oldTagName": "Zone"}, {"tagName": "ip"}, {"tagName": "country_code", "oldTagName": "country_Code"}, {"tagName": "vendor", "oldTagName": "<PERSON><PERSON><PERSON>"}], "type": 2, "enabled": true, "metricsFieldList": [{"fieldName": "call_join_success_count", "fieldType": 1}], "metricsAggregation": {"metricsAggregationRuleCompose": {"common": [], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "TEL_CALL_JOIN_SUCCESS"}, {"metricsName": "TEL_TC_SERVER_STATUS_ALERT", "oldMetricsName": "TEL_TC_SERVER-STATUS_ALERT", "tagNames": ["zone"], "tagDetails": [{"tagName": "zone", "oldTagName": "Zone"}], "type": 1, "enabled": true, "metricsFieldList": [{"fieldName": "isprimary.max", "oldFieldName": "isPrimary", "fieldType": 1}, {"fieldName": "status.sum", "fieldType": 1}], "metricsAggregation": {"aggPeriod": 60, "waitTime": 0, "filterCondition": "string.startsWith(instance, 'tctrl')", "aggregationResultFilter": "", "metricsAggregationRuleCompose": {"common": [{"aggField": "isprimary", "fieldType": "number", "aggTypes": [{"value": "max", "condition": "", "fieldName": "isprimary.max"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "isprimary"}, {"aggField": "status", "fieldType": "number", "aggTypes": [{"value": "sum", "condition": "", "fieldName": "status.sum"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "status"}], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "TEL_SERVER_STATUS"}, {"metricsName": "TEL_TC_ASYNMQ_STATUS", "tagNames": ["instanceid", "mmc", "region", "gwtype", "hostname", "instance", "zone", "zmserviceip"], "tagDetails": [{"tagName": "instanceid", "oldTagName": "InstanceId"}, {"tagName": "mmc", "oldTagName": "MMC"}, {"tagName": "region", "oldTagName": "Region"}, {"tagName": "gwtype", "oldTagName": "GWType"}, {"tagName": "hostname", "oldTagName": "Hostname"}, {"tagName": "instance", "oldTagName": "Instance"}, {"tagName": "zone", "oldTagName": "Zone"}, {"tagName": "zmserviceip"}], "type": 1, "enabled": true, "metricsFieldList": [{"fieldName": "status.max", "fieldType": 1}, {"fieldName": "consume_count.max", "fieldType": 1}, {"fieldName": "consume_success_count.max", "fieldType": 1}, {"fieldName": "consume_fail_count.max", "fieldType": 1}, {"fieldName": "produce_count.max", "fieldType": 1}, {"fieldName": "produce_success_count.max", "fieldType": 1}, {"fieldName": "produce_fail_count.max", "fieldType": 1}], "metricsAggregation": {"aggPeriod": 60, "waitTime": 0, "filterCondition": "string.startsWith(instance, 'tctrl')", "aggregationResultFilter": "", "metricsAggregationRuleCompose": {"common": [{"aggField": "status", "fieldType": "number", "aggTypes": [{"value": "max", "condition": "", "fieldName": "status.max"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "status"}, {"aggField": "consume_count", "fieldType": "number", "aggTypes": [{"value": "max", "condition": "", "fieldName": "consume_count.max"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "consume_count"}, {"aggField": "consume_success_count", "fieldType": "number", "aggTypes": [{"value": "max", "condition": "", "fieldName": "consume_success_count.max"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "consume_success_count"}, {"aggField": "consume_fail_count", "fieldType": "number", "aggTypes": [{"value": "max", "condition": "", "fieldName": "consume_fail_count.max"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "consume_fail_count"}, {"aggField": "produce_count", "fieldType": "number", "aggTypes": [{"value": "max", "condition": "", "fieldName": "produce_count.max"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "produce_count"}, {"aggField": "produce_success_count", "fieldType": "number", "aggTypes": [{"value": "max", "condition": "", "fieldName": "produce_success_count.max"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "produce_success_count"}, {"aggField": "produce_fail_count", "fieldType": "number", "aggTypes": [{"value": "max", "condition": "", "fieldName": "produce_fail_count.max"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "produce_fail_count"}], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "TEL_ASYNMQ_STATUS"}, {"metricsName": "TEL_MS_ERROR", "tagNames": ["instanceid", "mmc", "zone", "msip"], "tagDetails": [{"tagName": "instanceid", "oldTagName": "InstanceId"}, {"tagName": "mmc", "oldTagName": "MMC"}, {"tagName": "zone", "oldTagName": "Zone"}, {"tagName": "msip", "oldTagName": "msIP"}], "type": 1, "enabled": true, "metricsFieldList": [{"fieldName": "statuscode.count", "oldFieldName": "statusCode", "fieldType": 1}], "metricsAggregation": {"aggPeriod": 60, "waitTime": 0, "filterCondition": "string.startsWith(instance, 'telgw')", "aggregationResultFilter": "", "metricsAggregationRuleCompose": {"common": [{"aggField": "statuscode", "fieldType": "number", "aggTypes": [{"value": "count", "condition": "", "fieldName": "statuscode.count"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "statuscode"}], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "TEL_MS_ERROR"}, {"metricsName": "TEL_CALL_JOIN_IVR_AI", "oldMetricsName": "TEL-CALL-JOIN_IVR_AI", "tagNames": ["zone", "country_code", "sbcvip"], "tagDetails": [{"tagName": "zone", "oldTagName": "Zone"}, {"tagName": "country_code", "oldTagName": "country_Code"}, {"tagName": "sbcvip", "oldTagName": "sbcVIP"}], "type": 1, "enabled": true, "metricsFieldList": [{"fieldName": "errorconnecttimeout.sum", "oldFieldName": "errorConnectTimeout", "fieldType": 1}, {"fieldName": "errorringtimeout.sum", "oldFieldName": "errorRingTimeout", "fieldType": 1}, {"fieldName": "ivr_end_client_cancel.sum", "oldFieldName": "IVR_END_CLIENT_CANCEL", "fieldType": 1}], "metricsAggregation": {"aggPeriod": 300, "waitTime": 0, "filterCondition": "string.startsWith(instance, 'telgw')", "aggregationResultFilter": "", "metricsAggregationRuleCompose": {"common": [{"aggField": "errorconnecttimeout", "fieldType": "number", "aggTypes": [{"value": "sum", "condition": "", "fieldName": "errorconnecttimeout.sum"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "errorconnecttimeout"}, {"aggField": "errorringtimeout", "fieldType": "number", "aggTypes": [{"value": "sum", "condition": "", "fieldName": "errorringtimeout.sum"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "errorringtimeout"}, {"aggField": "ivr_end_client_cancel", "fieldType": "number", "aggTypes": [{"value": "sum", "condition": "", "fieldName": "ivr_end_client_cancel.sum"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "ivr_end_client_cancel"}], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "TEL_CALL_JOIN_FAIL"}, {"metricsName": "TEL_CALL_IVRreason_AI", "oldMetricsName": "TEL-CALL-IVRreason_AI", "tagNames": ["zone", "error_code", "country_code", "sbcvip"], "tagDetails": [{"tagName": "zone", "oldTagName": "Zone"}, {"tagName": "error_code"}, {"tagName": "country_code"}, {"tagName": "sbcvip", "oldTagName": "sbcVIP"}], "type": 1, "enabled": true, "metricsFieldList": [{"fieldName": "ivrreason.sum", "oldFieldName": "ivrReason", "fieldType": 1}], "metricsAggregation": {"aggPeriod": 300, "waitTime": 0, "filterCondition": "string.startsWith(instance, 'telgw')", "aggregationResultFilter": "", "metricsAggregationRuleCompose": {"common": [{"aggField": "i<PERSON><PERSON><PERSON><PERSON>", "fieldType": "number", "aggTypes": [{"value": "sum", "condition": "", "fieldName": "ivrreason.sum"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "i<PERSON><PERSON><PERSON><PERSON>"}], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "TEL_CALL_JOIN_FAIL"}, {"metricsName": "SCA_FAIL", "oldMetricsName": "SCA-FAIL", "tagNames": ["i<PERSON><PERSON><PERSON><PERSON>"], "tagDetails": [{"tagName": "i<PERSON><PERSON><PERSON><PERSON>", "oldTagName": "ivrReason"}], "type": 2, "enabled": true, "metricsFieldList": [{"fieldName": "deloitte", "oldFieldName": "Deloitte", "fieldType": 1}], "metricsAggregation": {"metricsAggregationRuleCompose": {"common": [], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "TEL_CALL_JOIN_FAIL"}], "alarms": [{"name": "TG_call_web_fail_count", "rules": [{"level": "ERROR", "needHits": 5, "hitCount": 5, "timeWindow": 300, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "call_web_fail_count.count", "operator": ">", "threshold": "0"}]}, {"level": "WARN", "needHits": 2, "hitCount": 2, "timeWindow": 120, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "call_web_fail_count.count", "operator": ">", "threshold": "0"}]}], "timesInPeriod": 1, "periodInMinutes": 30, "enabled": false, "notifications": [{"channel": {"name": "Telephony_cube_alerts(VOIP)_P2_P3_ALERTS", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "TG call.web.fail.count", "content": "MMC: ${mmc}\nREGION: ${region}\nZone: ${zone}\nHostname: ${hostname}\nInstance: ${instanceid}\nCall Web fail count(${call_web_fail_count.count}) over 1.", "whichLevels": "WARN,ERROR,FATAL", "repeatedSendAsyncmq": false, "isMarkdown": false, "mentions": "[]", "pdGroupTags": ""}], "alarmExtensionRelations": [], "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "TEL_CURL_WEB_FAIL"}, {"name": "TelGW_warning_MS_data_exceptional_count", "rules": [{"level": "WARN", "needHits": 5, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "ms_data_exceptional_count.count", "operator": ">", "threshold": "0"}]}], "timesInPeriod": 3, "periodInMinutes": 30, "enabled": false, "notifications": [{"channel": {"name": "Cloud-Watch-Channel", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "TelGW warning MS data exceptional count", "content": "${alarmName}\n${instanceid}\n${gwtype}\n${region}\nms.data.exceptional.count: ${ms_data_exceptional_count.count}", "whichLevels": "WARN,ERROR,FATAL", "repeatedSendAsyncmq": false, "isMarkdown": false}], "alarmExtensionRelations": [], "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "TEL_MS_DATA_EXCEPTIONAL"}, {"name": "TELE_SERVICE_STATUS", "rules": [{"level": "ERROR", "needHits": 1, "hitCount": 1, "timeWindow": 60, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "service_status.max", "operator": "==", "threshold": "0"}]}, {"level": "WARN", "needHits": 1, "hitCount": 1, "timeWindow": 60, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "service_status.max", "operator": "==", "threshold": "2"}, {"conditionType": "TAG", "name": "hostname", "operator": "not-in", "threshold": "ny-r40-svr16.ipa.zoom.us,sj-r8-svr05.ipa.zoom.us"}]}], "timesInPeriod": 3, "periodInMinutes": 60, "enabled": false, "notifications": [{"channel": {"name": "Telephony_cube_alerts(VOIP)_P0_P1_ALERTS", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "TELE_SERVER_STATUS", "content": "Alarm Time=${alarmTime}\nHost=${hostname}\nIP=${instanceid}\nRegion=${region}\nApp=${instance}\nServicestatus=${service_status.max}", "whichLevels": "ERROR,FATAL", "repeatedSendAsyncmq": false, "isMarkdown": false, "mentions": "[{\"displayName\":\"<PERSON><PERSON><PERSON>\",\"email\":\"<EMAIL>\"},{\"displayName\":\"<PERSON>\",\"email\":\"<EMAIL>\"}]", "pdGroupTags": ""}, {"channel": {"name": "Telephony_cube_alerts(VOIP)_P2_P3_ALERTS", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "TELE_SERVER_STATUS", "content": "Alarm Time=${alarmTime}\nHost=${hostname}\nIP=${instanceid}\nRegion=${region}\nApp=${instance}\nServicestatus=${service_status.max}", "whichLevels": "WARN,INFO", "repeatedSendAsyncmq": false, "isMarkdown": false, "mentions": "[{\"displayName\":\"<PERSON><PERSON><PERSON>\",\"email\":\"<EMAIL>\"},{\"displayName\":\"<PERSON>\",\"email\":\"<EMAIL>\"},{\"displayName\":\"<PERSON>\",\"email\":\"<EMAIL>\"}]", "pdGroupTags": ""}], "alarmExtensionRelations": [], "description": "this is the Service status alert if 2 is Suspend and 0 is Down ", "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "groupTags": "", "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "TEL_SERVER_STATUS"}, {"name": "TEL_CALL-JOIN-FAIL_AI", "rules": [{"level": "WARN", "needHits": 3, "hitCount": 4, "timeWindow": 1200, "isContinuous": true, "conditions": [{"conditionType": "AI", "name": "vendor.Bandwidth", "operator": "AI", "threshold": "5", "expression": "e5c392e8-284e-4504-9304-2cbb0741f010", "extension": "{\"detectMode\":\"Upper\"}"}]}], "timesInPeriod": 1, "periodInMinutes": 30, "enabled": false, "notifications": [{"channel": {"name": "Error code Capacity Monitoring", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "TEL_CALL-JOIN-FAIL_AI", "content": "${alarmDetailUrl}\n${alarmTime}\n${alarmName}\n${alarmMetricsName}\ncountry_code :${country_code}\nerror_code${error_code}\n${zone}", "whichLevels": "WARN", "repeatedSendAsyncmq": false, "isMarkdown": false, "mentions": "[]", "pdGroupTags": ""}], "alarmExtensionRelations": [], "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "groupTags": "", "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "TEL_CALL_JOIN_FAIL_AI"}, {"name": "TEL_CALL-JOIN-FAIL-COLT_AI", "rules": [{"level": "WARN", "needHits": 3, "hitCount": 4, "timeWindow": 1200, "isContinuous": true, "conditions": [{"conditionType": "AI", "name": "vendor.Colt", "operator": "AI", "threshold": "5", "expression": "3b4e8cee-5315-4180-996f-6c855bfebc9f", "extension": "{\"detectMode\":\"Upper\"}"}]}], "timesInPeriod": 1, "periodInMinutes": 30, "enabled": false, "notifications": [{"channel": {"name": "Error code Capacity Monitoring", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "TEL_CALL-JOIN-FAIL-COLT_AI", "content": "alarm URL ${alarmDetailUrl}\nalarm time ${alarmTime}\nalarm name :${alarmName}\nmetric name :${alarmMetricsName}\nerror code : ${error_code}\ncountry code :${country_code}", "whichLevels": "WARN", "repeatedSendAsyncmq": false, "isMarkdown": false, "pdGroupTags": ""}], "alarmExtensionRelations": [], "description": "TEL_CALL-JOIN-FAIL-COLT_AI", "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "groupTags": "", "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "TEL_CALL_JOIN_FAIL_AI"}, {"name": "TEL_CALL-JOIN-FAIL_AI_vendor_OTW_alarm", "rules": [{"level": "WARN", "needHits": 1, "hitCount": 4, "timeWindow": 1200, "isContinuous": true, "conditions": [{"conditionType": "AI", "name": "vendor.OTW", "operator": "AI", "threshold": "5", "expression": "3cab0ef0-b51f-4ca7-94e0-3d80c1dbd1e5", "extension": "{\"detectMode\":\"Upper\"}"}]}], "timesInPeriod": 1, "periodInMinutes": 30, "enabled": false, "notifications": [{"channel": {"name": "Error code Capacity Monitoring", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "TEL_CALL-JOIN-FAIL_AI_vendor_OTW_alarm", "content": "alarm URL ${alarmDetailUrl}\nalarm time ${alarmTime}\nalarm name :${alarmName}\nmetric name :${alarmMetricsName}\nerror code : ${error_code}\ncountry code :${country_code}", "whichLevels": "WARN", "repeatedSendAsyncmq": false, "isMarkdown": false, "pdGroupTags": ""}], "alarmExtensionRelations": [], "description": "TEL_CALL-JOIN-FAIL_AI_vendor_OTW_alarm", "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "groupTags": "", "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "TEL_CALL_JOIN_FAIL_AI"}, {"name": "TEL_CALL-JOIN-FAIL-INTLQ-INTL_AI", "rules": [{"level": "WARN", "needHits": 3, "hitCount": 4, "timeWindow": 1200, "isContinuous": true, "conditions": [{"conditionType": "AI", "name": "vendor.Inteliquent_INTL", "operator": "AI", "threshold": "5", "expression": "6cc3fed4-88cb-4e0e-a93c-d723f377b22d", "extension": "{\"detectMode\":\"Upper\"}"}]}], "timesInPeriod": 1, "periodInMinutes": 30, "enabled": false, "notifications": [{"channel": {"name": "Error code Capacity Monitoring", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "TEL_CALL-JOIN-FAIL-INTLQ-INTL_AI", "content": "alarm URL ${alarmDetailUrl}\nalarm time ${alarmTime}\nalarm name :${alarmName}\nmetric name :${alarmMetricsName}\nerror code : ${error_code}\ncountry code :${country_code}", "whichLevels": "WARN", "repeatedSendAsyncmq": false, "isMarkdown": false, "pdGroupTags": ""}], "alarmExtensionRelations": [], "description": "TEL_CALL-JOIN-FAIL-INTLQ-INTL_AI", "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "groupTags": "", "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "TEL_CALL_JOIN_FAIL_AI"}, {"name": "TEL_CALL-JOIN-FAIL-Rogers_AI_alarm", "rules": [{"level": "WARN", "needHits": 1, "hitCount": 4, "timeWindow": 1200, "isContinuous": true, "conditions": [{"conditionType": "AI", "name": "vendor.<PERSON>", "operator": "AI", "threshold": "5", "expression": "a08ecad1-b116-4fea-bb2f-a4500560f5ae", "extension": "{\"detectMode\":\"Upper\"}"}]}], "timesInPeriod": 1, "periodInMinutes": 30, "enabled": false, "notifications": [{"channel": {"name": "Error code Capacity Monitoring", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "TEL_CALL-JOIN-FAIL-Rogers_AI_alarm", "content": "alarm URL ${alarmDetailUrl}\nalarm time ${alarmTime}\nalarm name :${alarmName}\nmetric name :${alarmMetricsName}\nerror code : ${error_code}\ncountry code :${country_code}", "whichLevels": "WARN", "repeatedSendAsyncmq": false, "isMarkdown": false, "pdGroupTags": ""}], "alarmExtensionRelations": [], "description": "TEL_CALL-JOIN-FAIL-Rogers_AI_alarm", "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "groupTags": "", "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "TEL_CALL_JOIN_FAIL_AI"}, {"name": "TEL_CALL-JOIN-FAIL-TNZI_AI", "rules": [{"level": "WARN", "needHits": 3, "hitCount": 4, "timeWindow": 1200, "isContinuous": true, "conditions": [{"conditionType": "AI", "name": "vendor.TNZI", "operator": "AI", "threshold": "5", "expression": "e57950c6-d19f-4a6c-b642-7a8bdf367928", "extension": "{\"detectMode\":\"Upper\"}"}]}], "timesInPeriod": 1, "periodInMinutes": 30, "enabled": false, "notifications": [{"channel": {"name": "Error code Capacity Monitoring", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "TEL_CALL-JOIN-FAIL-TNZI_AI", "content": "alarm URL ${alarmDetailUrl}\nalarm time ${alarmTime}\nalarm name :${alarmName}\nmetric name :${alarmMetricsName}\nerror code : ${error_code}\ncountry code :${country_code}", "whichLevels": "WARN", "repeatedSendAsyncmq": false, "isMarkdown": false, "pdGroupTags": ""}], "alarmExtensionRelations": [], "description": "TEL_CALL-JOIN-FAIL-TNZI_AI", "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "groupTags": "", "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "TEL_CALL_JOIN_FAIL_AI"}, {"name": "TEL_CALL-JOIN-FAIL-BW-INTL_AI", "rules": [{"level": "ERROR", "needHits": 3, "hitCount": 4, "timeWindow": 1200, "isContinuous": true, "conditions": [{"conditionType": "AI", "name": "vendor.Bandwidth_Int", "operator": "AI", "threshold": "5", "expression": "ccac3ed6-d09d-4989-b063-9d2856d86697", "extension": "{\"detectMode\":\"Upper\"}"}]}, {"level": "WARN", "needHits": 3, "hitCount": 3, "timeWindow": 900, "isContinuous": true, "conditions": [{"conditionType": "AI", "name": "vendor.Bandwidth_Int", "operator": "AI", "threshold": "4", "expression": "ccac3ed6-d09d-4989-b063-9d2856d86697", "extension": "{\"detectMode\":\"Upper\"}"}]}], "timesInPeriod": 1, "periodInMinutes": 30, "enabled": false, "notifications": [{"channel": {"name": "Error code Capacity Monitoring", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "TEL_CALL-JOIN-FAIL-BW-INTL_AI", "content": "alarm URL ${alarmDetailUrl}\nalarm time ${alarmTime}\nalarm name :${alarmName}\nmetric name :${alarmMetricsName}\nerror code : ${error_code}\ncountry code :${country_code}", "whichLevels": "ERROR", "repeatedSendAsyncmq": false, "isMarkdown": false, "pdGroupTags": ""}, {"channel": {"name": "PS/PR alerts", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "TEL_CALL-JOIN-FAIL-BW-INTL_AI", "content": "alarm URL ${alarmDetailUrl}\nalarm time ${alarmTime}\nalarm name :${alarmName}\nmetric name :${alarmMetricsName}\nerror code : ${error_code}\ncountry code :${country_code}", "whichLevels": "WARN", "repeatedSendAsyncmq": false, "isMarkdown": false, "pdGroupTags": ""}], "alarmExtensionRelations": [], "description": "TEL_CALL-JOIN-FAIL-BW-INTL_AI", "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "groupTags": "", "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "TEL_CALL_JOIN_FAIL_AI"}, {"name": "TEL_CALL-JOIN-FAIL_AI_vendor_Level3_300s_ai_alarm", "rules": [{"level": "ERROR", "needHits": 4, "hitCount": 4, "timeWindow": 1200, "isContinuous": true, "conditions": [{"conditionType": "AI", "name": "vendor.Level3", "operator": "AI", "threshold": "5", "expression": "d4cfe269-823d-4c74-8439-d4d801aa3a85", "extension": "{\"detectMode\":\"Upper\"}"}]}], "timesInPeriod": 1, "periodInMinutes": 30, "enabled": true, "notifications": [{"channel": {"name": "Error code Capacity Monitoring", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "TEL_CALL-JOIN-FAIL_AI_vendor_Level3_300s_ai_alarm", "content": "alarm URL ${alarmDetailUrl}\nalarm time ${alarmTime}\nalarm name :${alarmName}\nmetric name :${alarmMetricsName}\nerror code : ${error_code}\ncountry code :${country_code}", "whichLevels": "INFO,WARN,ERROR,FATAL", "repeatedSendAsyncmq": false, "isMarkdown": false, "pdGroupTags": ""}], "alarmExtensionRelations": [], "description": "TEL_CALL-JOIN-FAIL_AI_vendor_Level3_300s_ai_alarm", "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "TEL_CALL_JOIN_FAIL_AI"}, {"name": "TEL_CALL-JOIN-FAIL_AI_vendor_Peerless_300s_ai_alarm", "rules": [{"level": "ERROR", "needHits": 4, "hitCount": 4, "timeWindow": 1200, "isContinuous": true, "conditions": [{"conditionType": "AI", "name": "vendor.Peerless", "operator": "AI", "threshold": "5", "expression": "aaa3cf43-0d6a-4767-b7c7-bb5a3b61db96", "extension": "{\"detectMode\":\"Upper\"}"}]}], "timesInPeriod": 1, "periodInMinutes": 30, "enabled": true, "notifications": [{"channel": {"name": "Error code Capacity Monitoring", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "TEL_CALL-JOIN-FAIL_AI_vendor_Peerless_300s_ai_alarm", "content": "alarm URL ${alarmDetailUrl}\nalarm time ${alarmTime}\nalarm name :${alarmName}\nmetric name :${alarmMetricsName}\nerror code : ${error_code}\ncountry code :${country_code}", "whichLevels": "INFO,ERROR,WARN,FATAL", "repeatedSendAsyncmq": false, "isMarkdown": false, "pdGroupTags": ""}], "alarmExtensionRelations": [], "description": "TEL_CALL-JOIN-FAIL_AI_vendor_Peerless_300s_ai_alarm", "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "TEL_CALL_JOIN_FAIL_AI"}, {"name": "TEL_CALL-JOIN-FAIL-Iristel_AI", "rules": [{"level": "WARN", "needHits": 3, "hitCount": 4, "timeWindow": 1200, "isContinuous": true, "conditions": [{"conditionType": "AI", "name": "vendor.Iristel_Int", "operator": "AI", "threshold": "5", "expression": "0f9addae-6dbb-47cf-8906-6828d7b1bc77", "extension": "{\"detectMode\":\"Upper\"}"}]}], "timesInPeriod": 1, "periodInMinutes": 30, "enabled": false, "notifications": [{"channel": {"name": "Error code Capacity Monitoring", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "TEL_CALL-JOIN-FAIL-Iristel_AI", "content": "alarm URL ${alarmDetailUrl}\nalarm time ${alarmTime}\nalarm name :${alarmName}\nmetric name :${alarmMetricsName}\nerror code : ${error_code}\ncountry code :${country_code}", "whichLevels": "WARN", "repeatedSendAsyncmq": false, "isMarkdown": false, "pdGroupTags": ""}], "alarmExtensionRelations": [], "description": "TEL_CALL-JOIN-FAIL-Iristel_AI", "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "groupTags": "", "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "TEL_CALL_JOIN_FAIL_AI"}, {"name": "TEL_CALL-JOIN-FAIL-Iristel-new_AI", "rules": [{"level": "WARN", "needHits": 3, "hitCount": 4, "timeWindow": 1200, "isContinuous": true, "conditions": [{"conditionType": "AI", "name": "vendor.Iristel_Int_new", "operator": "AI", "threshold": "5", "expression": "9625fc9e-4ca7-42e2-9d99-4effab4aa142", "extension": "{\"detectMode\":\"Upper\"}"}]}], "timesInPeriod": 1, "periodInMinutes": 30, "enabled": false, "notifications": [{"channel": {"name": "Error code Capacity Monitoring", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "TEL_CALL-JOIN-FAIL-Iristel-new_AI", "content": "alarm URL ${alarmDetailUrl}\nalarm time ${alarmTime}\nalarm name :${alarmName}\nmetric name :${alarmMetricsName}\nerror code : ${error_code}\ncountry code :${country_code}", "whichLevels": "WARN", "repeatedSendAsyncmq": false, "isMarkdown": false, "pdGroupTags": ""}], "alarmExtensionRelations": [], "description": "TEL_CALL-JOIN-FAIL-Iristel-new_AI", "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "groupTags": "", "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "TEL_CALL_JOIN_FAIL_AI"}, {"name": "TEL_CALL-JOIN-FAIL-UNION400_AI", "rules": [{"level": "WARN", "needHits": 3, "hitCount": 4, "timeWindow": 1200, "isContinuous": true, "conditions": [{"conditionType": "AI", "name": "vendor.Union400", "operator": "AI", "threshold": "5", "expression": "d76915b3-1abf-41e9-a303-ff544ba50046", "extension": "{\"detectMode\":\"Upper\"}"}]}], "timesInPeriod": 1, "periodInMinutes": 30, "enabled": false, "notifications": [{"channel": {"name": "Error code Capacity Monitoring", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "TEL_CALL-JOIN-FAIL-UNION400_AI", "content": "alarm URL ${alarmDetailUrl}\nalarm time ${alarmTime}\nalarm name :${alarmName}\nmetric name :${alarmMetricsName}\nerror code : ${error_code}\ncountry code :${country_code}", "whichLevels": "WARN", "repeatedSendAsyncmq": false, "isMarkdown": false, "pdGroupTags": ""}], "alarmExtensionRelations": [], "description": "TEL_CALL-JOIN-FAIL-UNION400_AI", "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "groupTags": "", "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "TEL_CALL_JOIN_FAIL_AI"}, {"name": "TEL_CALL-JOIN-FAIL_AI_vendor_Symbio_300s_ai_alarm", "rules": [{"level": "ERROR", "needHits": 4, "hitCount": 4, "timeWindow": 1200, "isContinuous": true, "conditions": [{"conditionType": "AI", "name": "vendor.Symbio", "operator": "AI", "threshold": "5", "expression": "d425a2c4-66bb-47d2-8089-17d3ba2f6efa", "extension": "{\"detectMode\":\"Upper\"}"}]}], "timesInPeriod": 1, "periodInMinutes": 30, "enabled": true, "notifications": [{"channel": {"name": "Error code Capacity Monitoring", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "TEL_CALL-JOIN-FAIL_AI_vendor.Symbio_300s_ai", "content": "alarm URL ${alarmDetailUrl}\nalarm time ${alarmTime}\nalarm name :${alarmName}\nmetric name :${alarmMetricsName}\nerror code : ${error_code}\ncountry code :${country_code}", "whichLevels": "INFO,WARN,ERROR,FATAL", "repeatedSendAsyncmq": false, "isMarkdown": false, "pdGroupTags": ""}], "alarmExtensionRelations": [], "description": "TEL_CALL-JOIN-FAIL_AI_vendor.Symbio_300s_ai", "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "TEL_CALL_JOIN_FAIL_AI"}, {"name": "TEL_CALL-JOIN-FAIL-BICS_AI", "rules": [{"level": "WARN", "needHits": 3, "hitCount": 4, "timeWindow": 1200, "isContinuous": true, "conditions": [{"conditionType": "AI", "name": "vendor.bics", "operator": "AI", "threshold": "5", "expression": "bed1389d-7875-45c1-ac02-5136c57eefc3", "extension": "{\"detectMode\":\"Upper\"}"}]}], "timesInPeriod": 1, "periodInMinutes": 30, "enabled": false, "notifications": [{"channel": {"name": "Error code Capacity Monitoring", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "TEL_CALL-JOIN-FAIL-BICS_AI", "content": "alarm URL ${alarmDetailUrl}\nalarm time ${alarmTime}\nalarm name :${alarmName}\nmetric name :${alarmMetricsName}\nerror code : ${error_code}\ncountry code :${country_code}", "whichLevels": "WARN", "repeatedSendAsyncmq": false, "isMarkdown": false, "pdGroupTags": ""}], "alarmExtensionRelations": [], "description": "TEL_CALL-JOIN-FAIL-BICS_AI", "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "groupTags": "", "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "TEL_CALL_JOIN_FAIL_AI"}, {"name": "TEL_CALL-JOIN-FAIL-ORANGE_AI", "rules": [{"level": "WARN", "needHits": 3, "hitCount": 4, "timeWindow": 1200, "isContinuous": true, "conditions": [{"conditionType": "AI", "name": "vendor.orangecarrier", "operator": "AI", "threshold": "5", "expression": "443fbabe-a80b-40e5-a7a1-af170df7bfed", "extension": "{\"detectMode\":\"Upper\"}"}]}], "timesInPeriod": 1, "periodInMinutes": 30, "enabled": false, "notifications": [{"channel": {"name": "Error code Capacity Monitoring", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "TEL_CALL-JOIN-FAIL-ORANGE_AI", "content": "alarm URL ${alarmDetailUrl}\nalarm time ${alarmTime}\nalarm name :${alarmName}\nmetric name :${alarmMetricsName}\nerror code : ${error_code}\ncountry code :${country_code}", "whichLevels": "WARN", "repeatedSendAsyncmq": false, "isMarkdown": false, "pdGroupTags": ""}], "alarmExtensionRelations": [], "description": "TEL_CALL-JOIN-FAIL-ORANGE_AI", "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "groupTags": "", "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "TEL_CALL_JOIN_FAIL_AI"}, {"name": "TEL_CALL-JOIN-FAIL-TATA_AI", "rules": [{"level": "ERROR", "needHits": 3, "hitCount": 4, "timeWindow": 1200, "isContinuous": true, "conditions": [{"conditionType": "AI", "name": "vendor.TATA", "operator": "AI", "threshold": "5", "expression": "c521cfb8-9827-4051-b01b-5fe482f30a5b", "extension": "{\"detectMode\":\"Upper\"}"}]}, {"level": "WARN", "needHits": 3, "hitCount": 3, "timeWindow": 900, "isContinuous": true, "conditions": [{"conditionType": "AI", "name": "vendor.TATA", "operator": "AI", "threshold": "4", "expression": "c521cfb8-9827-4051-b01b-5fe482f30a5b", "extension": "{\"detectMode\":\"Upper\"}"}]}], "timesInPeriod": 1, "periodInMinutes": 30, "enabled": false, "notifications": [{"channel": {"name": "Error code Capacity Monitoring", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "TEL_CALL-JOIN-FAIL-TATA_AI", "content": "alarm URL ${alarmDetailUrl}\nalarm time ${alarmTime}\nalarm name :${alarmName}\nmetric name :${alarmMetricsName}\nerror code : ${error_code}\ncountry code :${country_code}", "whichLevels": "ERROR", "repeatedSendAsyncmq": false, "isMarkdown": false, "pdGroupTags": ""}, {"channel": {"name": "PS/PR alerts", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "TEL_CALL-JOIN-FAIL-TATA_AI", "content": "alarm URL ${alarmDetailUrl}\nalarm time ${alarmTime}\nalarm name :${alarmName}\nmetric name :${alarmMetricsName}\nerror code : ${error_code}\ncountry code :${country_code}", "whichLevels": "WARN", "repeatedSendAsyncmq": false, "isMarkdown": false, "pdGroupTags": ""}], "alarmExtensionRelations": [], "description": "TEL_CALL-JOIN-FAIL-TATA_AI", "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "groupTags": "", "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "TEL_CALL_JOIN_FAIL_AI"}, {"name": "TEL_CALL-JOIN-FAIL-INTLQ_AI", "rules": [{"level": "WARN", "needHits": 3, "hitCount": 4, "timeWindow": 1200, "isContinuous": true, "conditions": [{"conditionType": "AI", "name": "vendor.Inteliquent", "operator": "AI", "threshold": "5", "expression": "a73903b5-d124-4b3a-a90d-b5fd34bc8f20", "extension": "{\"detectMode\":\"Upper\"}"}]}], "timesInPeriod": 1, "periodInMinutes": 30, "enabled": false, "notifications": [{"channel": {"name": "Error code Capacity Monitoring", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "TEL_CALL-JOIN-FAIL-INTLQ_AI", "content": "alarm URL ${alarmDetailUrl}\nalarm time ${alarmTime}\nalarm name :${alarmName}\nmetric name :${alarmMetricsName}\nerror code : ${error_code}\ncountry code :${country_code}", "whichLevels": "WARN", "repeatedSendAsyncmq": false, "isMarkdown": false, "pdGroupTags": ""}], "alarmExtensionRelations": [], "description": "TEL_CALL-JOIN-FAIL-INTLQ_AI", "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "groupTags": "", "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "TEL_CALL_JOIN_FAIL_AI"}, {"name": "TelGW_join_zc_mmr_fail_count_alert", "rules": [{"level": "WARN", "needHits": 2, "hitCount": 2, "timeWindow": 120, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "join_zc_mmr_fail_count.count", "operator": ">", "threshold": "0"}]}, {"level": "ERROR", "needHits": 5, "hitCount": 5, "timeWindow": 300, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "join_zc_mmr_fail_count.count", "operator": ">", "threshold": "0"}]}], "timesInPeriod": 1, "periodInMinutes": 30, "enabled": false, "notifications": [{"channel": {"name": "Test Alarm Channel", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "TelGW join.zc.mmr.fail.count alert", "content": "MMC: ${mmc}\nREGION: ${region}\nZone: ${zone}\nHostname: ${hostname}\nInstance: ${instanceid}\njoin.zc.mmr.fail.count: ${join_zc_mmr_fail_count.count}", "whichLevels": "WARN,ERROR", "repeatedSendAsyncmq": false, "isMarkdown": false}, {"channel": {"name": "Telephony_cube_alerts(VOIP)_P0_P1_ALERTS", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "TelGW join.zc.mmr.fail.count alert", "content": "MMC: ${mmc}\nREGION: ${region}\nZone: ${zone}\nHostname: ${hostname}\nInstance: ${instanceid}\njoin.zc.mmr.fail.count: ${join_zc_mmr_fail_count.count}", "whichLevels": "WARN,ERROR", "repeatedSendAsyncmq": false, "isMarkdown": false}], "alarmExtensionRelations": [], "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "TEL_JOIN_FAIL"}, {"name": "TG_Conf_Capacity", "rules": [{"level": "ERROR", "needHits": 3, "hitCount": 3, "timeWindow": 180, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "ongoingconference_max.max", "operator": ">=", "threshold": "200"}]}], "timesInPeriod": 3, "periodInMinutes": 30, "enabled": false, "notifications": [{"channel": {"name": "Telephony_cube_alerts(VOIP)_P0_P1_ALERTS", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "TELGW_MEETING_CAPACITY_PER_SERVER", "content": "Alarm Time=${alarmTime}\nIP=${instanceid}\nZone=${zone}\nOngoingConferences =${ongoingconference_max.max}", "whichLevels": "FATAL,ERROR", "repeatedSendAsyncmq": false, "isMarkdown": false}], "alarmExtensionRelations": [], "description": "TG meeting capacity per server", "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "TEL_GLOBAL_STAT_MAX"}, {"name": "NO_PRIMARY_TC", "rules": [{"level": "INFO", "needHits": 1, "hitCount": 1, "timeWindow": 60, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "isprimary.max", "operator": "==", "threshold": "0"}, {"conditionType": "TAG", "name": "zone", "operator": "not-in", "threshold": "GO_DV_TSP,NY_TSP,SC_TSP,GO_SJC_TELE2"}]}], "timesInPeriod": 2, "periodInMinutes": 30, "enabled": false, "notifications": [{"channel": {"name": "Telephony_cube_alerts(VOIP)_P2_P3_ALERTS", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "NO Primary TC in One Zone", "content": "Alarm Time=${alarmTime}\nZone=${zone}", "whichLevels": "INFO", "repeatedSendAsyncmq": false, "isMarkdown": false, "mentions": "[]", "pdGroupTags": ""}], "alarmExtensionRelations": [], "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "groupTags": "", "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "TEL_TC_SERVER_STATUS_ALERT"}, {"name": "TEL_NO_ACTIVE_TC", "rules": [{"level": "ERROR", "needHits": 1, "hitCount": 1, "timeWindow": 60, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "status.sum", "operator": "==", "threshold": "0"}, {"conditionType": "TAG", "name": "zone", "operator": "not-in", "threshold": "GO_DV_TSP,GO_SJC_TELE2"}, {"conditionType": "TAG", "name": "zone", "operator": "not-in", "threshold": "NY_TSP,AU01_SY_TELE,AU01_ME_TELE,SC_TSP"}]}], "timesInPeriod": 2, "periodInMinutes": 30, "enabled": false, "notifications": [{"channel": {"name": "Telephony_cube_alerts(VOIP)_P0_P1_ALERTS", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "NO active TC in one zone", "content": "Alarm Time=${alarmTime}\nZone=${zone}", "whichLevels": "ERROR", "repeatedSendAsyncmq": false, "isMarkdown": false, "mentions": "[{\"displayName\":\"<PERSON><PERSON><PERSON>\",\"email\":\"<EMAIL>\"},{\"displayName\":\"<PERSON>\",\"email\":\"<EMAIL>\"},{\"displayName\":\"<PERSON>\",\"email\":\"<EMAIL>\"}]", "pdGroupTags": ""}], "alarmExtensionRelations": [], "description": "NO active TC in one zone", "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "groupTags": "", "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "TEL_TC_SERVER_STATUS_ALERT"}, {"name": "TelGW_meeting_count_peak_alert", "rules": [{"level": "WARN", "needHits": 2, "hitCount": 2, "timeWindow": 120, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "meeting_count_peak.max", "operator": ">", "threshold": "250"}]}, {"level": "ERROR", "needHits": 5, "hitCount": 5, "timeWindow": 300, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "meeting_count_peak.max", "operator": ">", "threshold": "250"}]}], "timesInPeriod": 1, "periodInMinutes": 30, "enabled": false, "notifications": [{"channel": {"name": "Test Alarm Channel", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "TelGW meeting.count.peak alert", "content": "MMC: ${mmc}\nREGION: ${region}\nZone: ${zone}\nHostname: ${hostname}\nInstance: ${instanceid}\nmeeting.count.peak: ${meeting_count_peak.max}", "whichLevels": "WARN,ERROR", "repeatedSendAsyncmq": false, "isMarkdown": false, "mentions": "[]", "pdGroupTags": ""}, {"channel": {"name": "Telephony_cube_alerts(VOIP)_P0_P1_ALERTS", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "TelGW meeting.count.peak alert", "content": "MMC: ${mmc}\nREGION: ${region}\nZone: ${zone}\nHostname: ${hostname}\nInstance: ${instanceid}\nmeeting.count.peak: ${meeting_count_peak.max}", "whichLevels": "WARN,ERROR", "repeatedSendAsyncmq": false, "isMarkdown": false, "mentions": "[]", "pdGroupTags": ""}], "alarmExtensionRelations": [], "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "TEL_SERVER_STAT"}, {"name": "TEL-CALL-JOIN_IVR_AI_IVR_END_CLIENT_CANCEL_300s_ai_alarm", "rules": [{"level": "ERROR", "needHits": 3, "hitCount": 4, "timeWindow": 1200, "isContinuous": true, "conditions": [{"conditionType": "AI", "name": "IVR_END_CLIENT_CANCEL", "operator": "AI", "threshold": "5", "expression": "6f4d6717-4a6b-46a1-bef8-a40fbbc1e2bc", "extension": "{\"detectMode\":\"Upper\"}"}]}], "timesInPeriod": 1, "periodInMinutes": 30, "enabled": false, "notifications": [{"channel": {"name": "PS/PR alerts", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "TEL-CALL-JOIN_IVR_AI_IVR_END_CLIENT_CANCEL_300s_ai_alarm", "content": "Value of Client cancel : ${ivr_end_client_cancel.sum}\nCountry Code : ${country_code}\nVendor SBC IP : ${sbcvip}\nAlarm URL ${alarmDetailUrl}", "whichLevels": "INFO,WARN,ERROR", "repeatedSendAsyncmq": false, "isMarkdown": false, "pdGroupTags": ""}], "alarmExtensionRelations": [], "description": "TEL-CALL-JOIN_IVR_AI_IVR_END_CLIENT_CANCEL_300s_ai_alarm", "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "groupTags": "", "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "TEL_CALL_JOIN_IVR_AI"}, {"name": "TEL-CALL-JOIN_IVR_AI_errorConnectTimeout_300s_ai_alarm", "rules": [{"level": "ERROR", "needHits": 4, "hitCount": 3, "timeWindow": 900, "isContinuous": true, "conditions": [{"conditionType": "AI", "name": "errorConnectTimeout", "operator": "AI", "threshold": "2", "expression": "22d3472e-670e-4ffb-a6e5-9a39d4025e90", "extension": "{\"detectMode\":\"Upper\"}"}]}], "timesInPeriod": 1, "periodInMinutes": 30, "enabled": false, "notifications": [{"channel": {"name": "PS/PR alerts", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "TEL-CALL-JOIN_IVR_AI_errorConnectTimeout_300s_ai_alarm", "content": "${alarmDetailUrl}\n${alarmTime}\n${alarmName}\n${alarmMetricsName}\nerrorConnectTimeout ${errorconnecttimeout.sum}\n${country_code}\n${zone}", "whichLevels": "INFO,WARN,ERROR", "repeatedSendAsyncmq": false, "isMarkdown": false, "pdGroupTags": ""}], "alarmExtensionRelations": [], "description": "Connection timeout", "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "groupTags": "", "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "TEL_CALL_JOIN_IVR_AI"}, {"name": "TEL-CALL-JOIN_IVR_AI_errorRingTimeout_300s_ai_alarm", "rules": [{"level": "ERROR", "needHits": 4, "hitCount": 3, "timeWindow": 900, "isContinuous": true, "conditions": [{"conditionType": "AI", "name": "errorRingTimeout", "operator": "AI", "threshold": "2", "expression": "4be128e3-8e16-4569-809f-58d61e53ea4b", "extension": "{\"detectMode\":\"Upper\"}"}]}], "timesInPeriod": 2, "periodInMinutes": 30, "enabled": false, "notifications": [{"channel": {"name": "PS/PR alerts", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "Error ring timeout", "content": "${alarmDetailUrl}\n${alarmTime}\n${alarmName}\n${alarmMetricsName}\n${errorringtimeout.sum}\n${country_code}\n${sbcvip}\n${zone}", "whichLevels": "INFO,WARN,ERROR", "repeatedSendAsyncmq": false, "isMarkdown": false, "pdGroupTags": ""}], "alarmExtensionRelations": [], "description": "Error ring timeout", "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "groupTags": "", "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "TEL_CALL_JOIN_IVR_AI"}, {"name": "TSP-Create-Bride-Call-fail", "rules": [{"level": "ERROR", "needHits": 5, "hitCount": 5, "timeWindow": 300, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "create_bridge_call_fail_count.count", "operator": ">=", "threshold": "5"}]}], "timesInPeriod": 3, "periodInMinutes": 30, "enabled": false, "notifications": [{"channel": {"name": "Telephony_cube_alerts(VOIP)_P0_P1_ALERTS", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "TSP-Bridge-Call-Fail for ${Region}", "content": "${alarmTime}\n${alarmName}\n${alarmMetricName} is more then ${create_bridge_call_fail_count.count}\n${region}\n${instanceid}\n", "whichLevels": "ERROR", "repeatedSendAsyncmq": false, "isMarkdown": false, "mentions": "[{\"displayName\":\"<PERSON>\",\"email\":\"<EMAIL>\"},{\"displayName\":\"<PERSON>\",\"email\":\"<EMAIL>\"},{\"displayName\":\"<PERSON><PERSON><PERSON>\",\"email\":\"<EMAIL>\"},{\"displayName\":\"<PERSON><PERSON>\",\"email\":\"<EMAIL>\"}]", "pdGroupTags": ""}], "alarmExtensionRelations": [], "description": "TSP-Bridge-Call-Fail ", "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "groupTags": "", "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "TSP_CREATE_BRIDGE_CALL_FAIL"}, {"name": "TSP-Create-Conf-Fail", "rules": [{"level": "ERROR", "needHits": 5, "hitCount": 5, "timeWindow": 300, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "create_conf_fail_count.count", "operator": ">=", "threshold": "5"}]}], "timesInPeriod": 3, "periodInMinutes": 30, "enabled": false, "notifications": [{"channel": {"name": "Telephony_cube_alerts(VOIP)_P0_P1_ALERTS", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "TSP-Create-Conf-Fail-${Region}", "content": "${alarmTime}\n${alarmName}\n${alarmMetricName} is more then ${create_conf_fail_count.count}\n${instanceid}", "whichLevels": "ERROR", "repeatedSendAsyncmq": false, "isMarkdown": false, "mentions": "[{\"displayName\":\"<PERSON>\",\"email\":\"<EMAIL>\"},{\"displayName\":\"<PERSON>\",\"email\":\"<EMAIL>\"},{\"displayName\":\"<PERSON><PERSON><PERSON>\",\"email\":\"<EMAIL>\"},{\"displayName\":\"<PERSON><PERSON>\",\"email\":\"<EMAIL>\"}]", "pdGroupTags": ""}], "alarmExtensionRelations": [], "description": "TSP-Create-Conf-Fail", "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "groupTags": "", "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "TSP_CREATE_CONF_FAIL"}, {"name": "ASYCMQ_Consume_Fail", "rules": [{"level": "WARN", "needHits": 1, "hitCount": 1, "timeWindow": 60, "isContinuous": true, "conditions": [{"conditionType": "EXPRESSION", "expression": "consume_fail_count.max > _cpv_consume_fail_count.max"}]}, {"level": "ERROR", "needHits": 2, "hitCount": 2, "timeWindow": 120, "isContinuous": true, "conditions": [{"conditionType": "EXPRESSION", "expression": "consume_fail_count.max > _cpv_consume_fail_count.max"}]}], "timesInPeriod": 2, "periodInMinutes": 30, "enabled": false, "notifications": [{"channel": {"name": "Telephony_cube_alerts(VOIP)_P0_P1_ALERTS", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "ASYCMQ consume fail on TC", "content": "Alarm Time=${alarmTime}\nHost=${hostname}\nIP=${instanceid}\nRegion=${region}\nApp=${instance}", "whichLevels": "ERROR,FATAL", "repeatedSendAsyncmq": false, "isMarkdown": false, "pdGroupTags": ""}, {"channel": {"name": "Telephony_cube_alerts(VOIP)_P2_P3_ALERTS", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "ASYCMQ consume fail on TC", "content": "Alarm Time=${alarmTime}\nHost=${hostname}\nIP=${instanceid}\nRegion=${region}\nApp=${instance}", "whichLevels": "INFO,WARN", "repeatedSendAsyncmq": false, "isMarkdown": false, "pdGroupTags": ""}], "alarmExtensionRelations": [], "description": "ASYNCMQ consume fail on TC", "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "groupTags": "", "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "TEL_TC_ASYNMQ_STATUS"}, {"name": "ASYCMQ_Produce_Fail", "rules": [{"level": "WARN", "needHits": 1, "hitCount": 1, "timeWindow": 60, "isContinuous": true, "conditions": [{"conditionType": "EXPRESSION", "expression": "produce_fail_count.max > _cpv_produce_fail_count.max"}]}, {"level": "ERROR", "needHits": 2, "hitCount": 2, "timeWindow": 120, "isContinuous": true, "conditions": [{"conditionType": "EXPRESSION", "expression": "produce_fail_count.max > _cpv_produce_fail_count.max"}]}], "timesInPeriod": 2, "periodInMinutes": 30, "enabled": false, "notifications": [{"channel": {"name": "Telephony_cube_alerts(VOIP)_P0_P1_ALERTS", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "ASYCMQ produce fail on TC", "content": "Alarm Time=${alarmTime}\nHost=${hostname}\nIP=${instanceid}\nRegion=${region}\nApp=${instance}\ncurrent_value=${produce_fail_count.max}\nlast_value=${_cpv_produce_fail_count}", "whichLevels": "ERROR,FATAL", "repeatedSendAsyncmq": false, "isMarkdown": false, "mentions": "[]", "pdGroupTags": ""}, {"channel": {"name": "Telephony_cube_alerts(VOIP)_P2_P3_ALERTS", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "ASYCMQ produce fail on TC", "content": "Alarm Time=${alarmTime}\nHost=${hostname}\nIP=${instanceid}\nRegion=${region}\nApp=${instance}\ncurrent_value=${produce_fail_count.max}\nlast_value=${_cpv_produce_fail_count}", "whichLevels": "WARN,INFO", "repeatedSendAsyncmq": false, "isMarkdown": false, "mentions": "[]", "pdGroupTags": ""}], "alarmExtensionRelations": [], "description": "ASYNCMQ produce fail on TC", "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "groupTags": "", "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "TEL_TC_ASYNMQ_STATUS"}, {"name": "TSP-Connect-to-Adaptor-Fail", "rules": [{"level": "ERROR", "needHits": 5, "hitCount": 5, "timeWindow": 300, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "connect_adaptor_fail_count.count", "operator": ">=", "threshold": "5"}]}], "timesInPeriod": 3, "periodInMinutes": 30, "enabled": false, "notifications": [{"channel": {"name": "Telephony_cube_alerts(VOIP)_P0_P1_ALERTS", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "TSP-Connect-to-Adaptor-Fail-${Region}", "content": "${alarmTime}\n${alarmName}\n${alarmMetricName} is more then ${connect_adaptor_fail_count.count} for ${region}\n${instanceid}", "whichLevels": "ERROR", "repeatedSendAsyncmq": false, "isMarkdown": false, "mentions": "[{\"displayName\":\"<PERSON>\",\"email\":\"<EMAIL>\"},{\"displayName\":\"<PERSON>\",\"email\":\"<EMAIL>\"},{\"displayName\":\"<PERSON><PERSON><PERSON>\",\"email\":\"<EMAIL>\"},{\"displayName\":\"<PERSON><PERSON>\",\"email\":\"<EMAIL>\"}]", "pdGroupTags": ""}], "alarmExtensionRelations": [], "description": "TSP-Connect-to-Adaptor-Fail", "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "groupTags": "", "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "TSP_CONNECT_ADAPTOR_FAIL"}, {"name": "TCtrl_connect_cc_fail_count_alert", "rules": [{"level": "WARN", "needHits": 2, "hitCount": 2, "timeWindow": 120, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "connect_cc_fail_count.count", "operator": ">", "threshold": "0"}]}, {"level": "ERROR", "needHits": 3, "hitCount": 3, "timeWindow": 180, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "connect_cc_fail_count.count", "operator": ">", "threshold": "0"}]}], "timesInPeriod": 1, "periodInMinutes": 30, "enabled": false, "notifications": [{"channel": {"name": "Test Alarm Channel", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "TCTRL connect.cc.fail.count alert", "content": "MMC: ${mmc}\nREGION: ${region}\nZone: ${zone}\nHostname: ${hostname}\nInstance: ${instanceid}\nconnect.cc.fail.count: ${connect_cc_fail_count.count}", "whichLevels": "WARN,ERROR", "repeatedSendAsyncmq": false, "isMarkdown": false}, {"channel": {"name": "Telephony_cube_alerts(VOIP)_P0_P1_ALERTS", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "TCTRL connect.cc.fail.count alert", "content": "MMC: ${mmc}\nREGION: ${region}\nZone: ${zone}\nHostname: ${hostname}\nInstance: ${instanceid}\nconnect.cc.fail.count: ${connect_cc_fail_count.count}", "whichLevels": "WARN,ERROR", "repeatedSendAsyncmq": false, "isMarkdown": false}], "alarmExtensionRelations": [], "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "TC_CC_LOST"}, {"name": "TEL_MSErrors", "rules": [{"level": "WARN", "needHits": 2, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "mserrors", "operator": ">", "threshold": "0"}]}, {"level": "ERROR", "needHits": 5, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "mserrors", "operator": ">", "threshold": "0"}]}], "timesInPeriod": 3, "periodInMinutes": 30, "enabled": false, "notifications": [{"channel": {"name": "Cloud-Watch-Channel", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "TEL MSErrors", "content": "MMC: ${mmc}\nREGION: ${region}\nInstance: ${instanceid}\nMS Errors(${mserrors}) over 0.", "whichLevels": "WARN,ERROR,FATAL", "repeatedSendAsyncmq": false, "isMarkdown": false}, {"channel": {"name": "Telephony_cube_alerts(VOIP)_P0_P1_ALERTS", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "TEL MSErrors", "content": "MMC: ${mmc}\nREGION: ${region}\nInstance: ${instanceid}\nMS Errors(${mserrors}) over 0.", "whichLevels": "FATAL,ERROR", "repeatedSendAsyncmq": false, "isMarkdown": false}, {"channel": {"name": "Telephony_cube_alerts(VOIP)_P2_P3_ALERTS", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "TEL MSErrors", "content": "MMC: ${mmc}\nREGION: ${region}\nInstance: ${instanceid}\nMS Errors(${mserrors}) over 0.", "whichLevels": "WARN,INFO", "repeatedSendAsyncmq": false, "isMarkdown": false}], "alarmExtensionRelations": [], "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "TEL_GLOBAL_STAT"}, {"name": "TEL_MSTimeouts", "rules": [{"level": "ERROR", "needHits": 5, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "mstimeouts", "operator": ">", "threshold": "0"}]}, {"level": "WARN", "needHits": 2, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "mstimeouts", "operator": ">", "threshold": "0"}]}], "timesInPeriod": 3, "periodInMinutes": 30, "enabled": false, "notifications": [{"channel": {"name": "Cloud-Watch-Channel", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "TEL MSTimeouts", "content": "MMC: ${mmc}\nREGION: ${region}\nInstance: ${instanceid}\nMS Timeouts(${mstimeouts}) over 0.", "whichLevels": "WARN,ERROR,FATAL", "repeatedSendAsyncmq": false, "isMarkdown": false}, {"channel": {"name": "Telephony_cube_alerts(VOIP)_P0_P1_ALERTS", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "TEL MSTimeouts", "content": "MMC: ${mmc}\nREGION: ${region}\nInstance: ${instanceid}\nMS Timeouts(${mstimeouts}) over 0.", "whichLevels": "ERROR,FATAL", "repeatedSendAsyncmq": false, "isMarkdown": false}, {"channel": {"name": "Telephony_cube_alerts(VOIP)_P2_P3_ALERTS", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "TEL MSTimeouts", "content": "MMC: ${mmc}\nREGION: ${region}\nInstance: ${instanceid}\nMS Timeouts(${mstimeouts}) over 0.", "whichLevels": "WARN,INFO", "repeatedSendAsyncmq": false, "isMarkdown": false}], "alarmExtensionRelations": [], "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "TEL_GLOBAL_STAT"}, {"name": "TEL_DeadMSCount", "rules": [{"level": "ERROR", "needHits": 5, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "deadmscount", "operator": ">", "threshold": "0"}]}, {"level": "WARN", "needHits": 2, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "deadmscount", "operator": ">", "threshold": "0"}]}], "timesInPeriod": 3, "periodInMinutes": 30, "enabled": false, "notifications": [{"channel": {"name": "Cloud-Watch-Channel", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "TG DeadMSCount", "content": "MMC: ${mmc}\nREGION: ${region}\nInstance: ${instanceid}\nDead MS Count over 0.", "whichLevels": "WARN,ERROR,FATAL", "repeatedSendAsyncmq": false, "isMarkdown": false}, {"channel": {"name": "Telephony_cube_alerts(VOIP)_P0_P1_ALERTS", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "TG DeadMSCount", "content": "MMC: ${mmc}\nREGION: ${region}\nInstance: ${instanceid}\nDead MS Count over 0.", "whichLevels": "FATAL,ERROR", "repeatedSendAsyncmq": false, "isMarkdown": false}, {"channel": {"name": "Telephony_cube_alerts(VOIP)_P2_P3_ALERTS", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "TG DeadMSCount", "content": "MMC: ${mmc}\nREGION: ${region}\nInstance: ${instanceid}\nDead MS Count over 0.", "whichLevels": "WARN,INFO", "repeatedSendAsyncmq": false, "isMarkdown": false}], "alarmExtensionRelations": [], "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "TEL_GLOBAL_STAT"}, {"name": "TEL_OutDialFailures", "rules": [{"level": "WARN", "needHits": 5, "hitCount": 5, "timeWindow": 300, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "outdialfailures", "operator": ">", "threshold": "1"}]}, {"level": "ERROR", "needHits": 8, "hitCount": 8, "timeWindow": 480, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "outdialfailures", "operator": ">", "threshold": "1"}]}], "timesInPeriod": 3, "periodInMinutes": 30, "enabled": false, "notifications": [{"channel": {"name": "Cloud-Watch-Channel", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "TEL OutDialFailures", "content": "MMC: ${mmc}\nREGION: ${region}\nInstance: ${instanceid}\nOut Dial Failures(${outdialfailures}) over 1.", "whichLevels": "WARN,ERROR,FATAL", "repeatedSendAsyncmq": false, "isMarkdown": false, "mentions": "[]", "pdGroupTags": ""}, {"channel": {"name": "Telephony_cube_alerts(VOIP)_P0_P1_ALERTS", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "TG_Dialout_failure", "content": "MMC: ${mmc}\nREGION: ${region}\nInstance: ${instanceid}\nOut Dial Failures(${outdialfailures}) over 1.", "whichLevels": "ERROR,FATAL", "repeatedSendAsyncmq": false, "isMarkdown": false, "mentions": "[]", "pdGroupTags": ""}, {"channel": {"name": "Telephony_cube_alerts(VOIP)_P2_P3_ALERTS", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "TG_Dialout_failure", "content": "MMC: ${mmc}\nREGION: ${region}\nInstance: ${instanceid}\nOut Dial Failures(${outdialfailures}) over 1.", "whichLevels": "WARN,INFO", "repeatedSendAsyncmq": false, "isMarkdown": false, "mentions": "[]", "pdGroupTags": ""}], "alarmExtensionRelations": [], "description": "Dialout calls getting failed , need to check the TG logs to verify the cause of failure ", "levelsSendIncident": "FATAL,ERROR", "alarmMatchMode": 0, "groupTags": "", "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "TEL_GLOBAL_STAT"}, {"name": "TEL_JoinMeetingFailures", "rules": [{"level": "ERROR", "needHits": 8, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "joinmeetingfailures", "operator": ">", "threshold": "1"}]}, {"level": "WARN", "needHits": 5, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "joinmeetingfailures", "operator": ">", "threshold": "1"}]}], "timesInPeriod": 3, "periodInMinutes": 30, "enabled": false, "notifications": [{"channel": {"name": "Cloud-Watch-Channel", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "TEL JoinMeetingFailures", "content": "MMC: ${mmc}\nREGION: ${region}\nInstance: ${instanceid}\nJoin Meeting Failures(${joinmeetingfailures}) over 1.", "whichLevels": "WARN,ERROR,FATAL", "repeatedSendAsyncmq": false, "isMarkdown": false}, {"channel": {"name": "Telephony_cube_alerts(VOIP)_P0_P1_ALERTS", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "TG_JoinMeetingFailures", "content": "MMC: ${mmc}\nREGION: ${region}\nInstance: ${instanceid}\nJoin Meeting Failures(${joinmeetingfailures}) over 1.", "whichLevels": "WARN,ERROR", "repeatedSendAsyncmq": false, "isMarkdown": false}], "alarmExtensionRelations": [], "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "TEL_GLOBAL_STAT"}]}