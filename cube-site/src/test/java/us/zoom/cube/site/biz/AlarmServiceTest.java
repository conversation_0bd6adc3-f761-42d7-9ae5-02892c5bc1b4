package us.zoom.cube.site.biz;


import com.google.common.collect.Lists;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import us.zoom.cube.lib.integrations.MetricsFieldTypeEnum;
import us.zoom.cube.site.core.AlarmHandler;
import us.zoom.cube.site.core.MetricsHandler;
import us.zoom.cube.site.infra.enums.WebCodeEnum;
import us.zoom.cube.site.infra.utils.IdUtils;
import us.zoom.cube.site.lib.IdPara;
import us.zoom.cube.site.lib.MetricsIdPara;
import us.zoom.cube.site.lib.ResponseObject;
import us.zoom.cube.site.lib.input.AlarmInput;
import us.zoom.cube.site.lib.input.AlarmRuleContentInput;
import us.zoom.cube.site.lib.input.AlarmRuleInput;
import us.zoom.cube.site.lib.input.AlarmRuleSingleContentInput;
import us.zoom.cube.site.lib.output.alarm.FieldAlarmRelationOut;
import us.zoom.cube.site.lib.output.alarm.MetricsAlarmsRelationOut;
import us.zoom.infra.dao.model.AlarmDO;
import us.zoom.infra.dao.model.AlarmRuleDO;
import us.zoom.infra.dao.model.MetricsFieldDO;
import us.zoom.infra.model.alarm.AlarmLevel;
import us.zoom.cube.lib.utils.JsonUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.mockito.Mockito.when;

//Extension that initializes mocks and handles strict stubbings. This extension is the JUnit Jupiter equivalent of our JUnit4 MockitoJUnitRunner
//If you would like to configure the used strictness for the test class, use MockitoSettings.
@ExtendWith(MockitoExtension.class)
public class AlarmServiceTest {

    private static final String alarmId = "alarmId1";
    private static final String metricsId = "metricsId1";

    @InjectMocks
    private AlarmService alarmService = new AlarmService();

    @Mock
    private AuthService authService;
    @Mock
    private AlarmHandler alarmHandler;

    @Mock
    private MetricsHandler metricsHandler;

    @Test
    public void addAlarm() throws Exception {
        when(authService.hasSuchTenant(Mockito.any(), Mockito.any())).thenReturn(false);
        ResponseObject<Boolean> result = alarmService.addAlarm(new AlarmInput<>());
        Assertions.assertEquals(WebCodeEnum.HasNoSuchTenant.getCode(), result.getOperCode());

        when(authService.hasSuchTenant(Mockito.any(), Mockito.any())).thenReturn(true);
        AlarmInput<AlarmRuleInput> alarmInput = buildAlarmInput();
        result = alarmService.addAlarm(alarmInput);
        Assertions.assertEquals(result.getStatus(), "success");
    }

    @Test
    public void editAlarm() throws Exception {
//        when(authService.hasSuchAlarm(Mockito.any(), Mockito.any())).thenReturn(false);
        ResponseObject<Boolean> result = alarmService.editAlarmById(new AlarmInput<>());
        Assertions.assertEquals(WebCodeEnum.HasNoSuchAlarm.getCode(), result.getOperCode());

//        when(authService.hasSuchAlarm(Mockito.any(), Mockito.any())).thenReturn(true);
        AlarmInput<AlarmRuleInput> alarmInput = buildAlarmInput();
        result = alarmService.editAlarmById(alarmInput);
        Assertions.assertEquals(result.getStatus(), "success");
    }

    private AlarmInput<AlarmRuleInput> buildAlarmInput() {
        AlarmInput<AlarmRuleInput> alarmInput = new AlarmInput<>();
        alarmInput.setId(IdUtils.generateId());
        alarmInput.setAlarmName("testAlarmName");
        alarmInput.setTitle("testAlarmTitle");
        alarmInput.setMetricsId(IdUtils.generateId());
        alarmInput.setAlarmContent("testAlarmContent");
        alarmInput.setAlarmCycle(Lists.newArrayList("Monday", "Tuesday", "Wednesday", "Thursday", "Friday"));
        alarmInput.setFromTime("00:00:00");
        alarmInput.setToTime("23:59:59");
        alarmInput.setTenantId("web1");

        List<AlarmRuleInput> alarmRules = buildAlarmRuleInputs();
        alarmInput.setAlarmRules(alarmRules);
        return alarmInput;
    }

    private List<AlarmRuleInput> buildAlarmRuleInputs() {
        List<AlarmRuleInput> alarmRuleInputList = Lists.newArrayList();
        AlarmRuleInput alarmRuleInput1 = new AlarmRuleInput();
        alarmRuleInput1.setAlarmType(0);
        alarmRuleInput1.setAlarmLevel(AlarmLevel.FATAL.getLevel());

        AlarmRuleContentInput alarmRuleContent = new AlarmRuleContentInput();
        AlarmRuleSingleContentInput tagRule = new AlarmRuleSingleContentInput();
        tagRule.setType(3);
        tagRule.setTag("host_name");
        tagRule.setOperator("==");
        tagRule.setCmpValue("dev_web001");
        List<AlarmRuleSingleContentInput> tagRules = Lists.newArrayList(tagRule);
        alarmRuleContent.setTagRules(tagRules);

        AlarmRuleSingleContentInput fieldRule1 = new AlarmRuleSingleContentInput();
        fieldRule1.setType(0);
        fieldRule1.setField("cost.avg");
        fieldRule1.setFieldType(MetricsFieldTypeEnum.number.name());
        fieldRule1.setOperator(">=");
        fieldRule1.setCmpValue("200");
        AlarmRuleSingleContentInput fieldRule2 = new AlarmRuleSingleContentInput();
        fieldRule2.setType(1);
        fieldRule2.setField("cost.avg");
        fieldRule2.setFieldType(MetricsFieldTypeEnum.number.name());
        fieldRule2.setOperator("crIncrease");
        fieldRule2.setCmpValue("0.2");
        List<AlarmRuleSingleContentInput> fieldRules = Lists.newArrayList(fieldRule1, fieldRule2);
        alarmRuleContent.setFieldRules(fieldRules);

        AlarmRuleSingleContentInput functionRule = new AlarmRuleSingleContentInput();
        functionRule.setType(4);
        functionRule.setExpression("dataExist('web_agg_joinmeeting','ip',122)");
        List<AlarmRuleSingleContentInput> functionRules = Lists.newArrayList(functionRule);
        alarmRuleContent.setFunctionRules(functionRules);

        alarmRuleInput1.setAlarmRuleContent(alarmRuleContent);
        alarmRuleInputList.add(alarmRuleInput1);
        return alarmRuleInputList;
    }

    @Test
    public void getAlarm() {
        AlarmDO alarmDO = buildAlarmDO();
        when(alarmHandler.getAlarmById(alarmId)).thenReturn(alarmDO);
        when(alarmHandler.getMetricsIdByAlarmId(alarmId)).thenReturn(metricsId);
        List<AlarmRuleDO> alarmRuleDOList = buildAlarmRuleDOList();
        when(alarmHandler.listRuleByAlarmIds(Mockito.anyList())).thenReturn(alarmRuleDOList);

        IdPara idPara = buildIdPara();
        ResponseObject result = alarmService.getAlarm(idPara);
        Assertions.assertEquals(result.getStatus(), "success");
    }

    private AlarmDO buildAlarmDO() {
        AlarmDO alarmDO = new AlarmDO();
        alarmDO.setId(alarmId);
        alarmDO.setName("testAlarmName");
        alarmDO.setTitle("testAlarmTitle");
        alarmDO.setAlarmContent("testAlarmContent");
        alarmDO.setAlarmCycle("[\"Monday\",\"Tuesday\",\"Wednesday\",\"Thursday\",\"Friday\"]");
        alarmDO.setFromTime("00:00:00");
        alarmDO.setToTime("23:59:59");
        alarmDO.setTenantId("web1");
        alarmDO.setCreateTime(new Date());
        alarmDO.setModifyTime(new Date());
        return alarmDO;
    }

    private List<AlarmRuleDO> buildAlarmRuleDOList() {
        List<AlarmRuleDO> alarmRuleDOList = Lists.newArrayList();
        AlarmRuleDO alarmRuleDO = new AlarmRuleDO();
        alarmRuleDO.setId(IdUtils.generateId());
        alarmRuleDO.setAlarmType(0);
        alarmRuleDO.setAlarmLevel(AlarmLevel.FATAL.getLevel());
        alarmRuleDO.setAlarmRuleContent("[{\"type\":3,\"tag\":\"host_name\",\"field\":null,\"fieldType\":null,\"operator\":\"==\",\"cmpValue\":\"dev_web001\",\"expression\":null},{\"type\":0,\"tag\":null,\"field\":\"cost.avg\",\"fieldType\":\"number\",\"operator\":\">=\",\"cmpValue\":\"200\",\"expression\":null},{\"type\":1,\"tag\":null,\"field\":\"cost.avg\",\"fieldType\":\"number\",\"operator\":\"crIncrease\",\"cmpValue\":\"0.2\",\"expression\":null},{\"type\":1,\"tag\":null,\"field\":\"cost.avg\",\"fieldType\":\"number\",\"operator\":\"crDecrease\",\"cmpValue\":\"0.2\",\"expression\":null},{\"type\":4,\"tag\":null,\"field\":null,\"fieldType\":null,\"operator\":null,\"cmpValue\":null,\"expression\":\"dataExist('web_agg_joinmeeting','ip',122)\"}]\n");
        alarmRuleDO.setCreateTime(new Date());
        alarmRuleDO.setModifyTime(new Date());
        alarmRuleDOList.add(alarmRuleDO);
        return alarmRuleDOList;
    }

    private IdPara buildIdPara() {
        IdPara idPara = new IdPara();
        idPara.setId(alarmId);
        return idPara;
    }


    @Test
    public void getFieldAlarmRelationsByMetricsIdNoAlarms() {
        String metricsId = "metricsId123";
        MetricsIdPara metricsIdPara = Mockito.mock(MetricsIdPara.class);
        when(metricsIdPara.getMetricsId()).thenReturn(metricsId);
        List<AlarmDO> alarmDOList = new ArrayList<>();
        when(alarmHandler.listAlarmsByMetricsId("metricsId123")).thenReturn(alarmDOList);

        List<MetricsFieldDO> metricsFieldDOList = new ArrayList<>();
        MetricsFieldDO metricsFieldDO1 = new MetricsFieldDO();
        metricsFieldDO1.setId("id1");
        metricsFieldDO1.setFieldName("name1");
        metricsFieldDOList.add(metricsFieldDO1);

        MetricsFieldDO metricsFieldDO2 = new MetricsFieldDO();
        metricsFieldDO2.setId("id2");
        metricsFieldDO2.setFieldName("name2");
        metricsFieldDOList.add(metricsFieldDO2);

        MetricsFieldDO metricsFieldDO3 = new MetricsFieldDO();
        metricsFieldDO3.setId("id3");
        metricsFieldDO3.setFieldName("name3");
        metricsFieldDOList.add(metricsFieldDO3);

        when(metricsHandler.listFieldsByMetricsIds(Lists.newArrayList(metricsId))).thenReturn(metricsFieldDOList);

        List<AlarmRuleDO> rules = new ArrayList<>();
        AlarmRuleDO alarmRuleDO1 = new AlarmRuleDO();
        alarmRuleDO1.setAlarmRuleContent("[{\"cmpValue\":\"100\",\"expression\":\"downlink.avg.loss.ratio > 100\",\"field\":\"name1\",\"fieldType\":\"number\",\"operator\":\">\",\"type\":0}, {\"cmpValue\":\"100\",\"expression\":\"downlink.avg.loss.ratio > 100\",\"field\":\"name5\",\"fieldType\":\"number\",\"operator\":\">\",\"type\":0}]");
        AlarmRuleDO alarmRuleDO2 = new AlarmRuleDO();
        alarmRuleDO2.setAlarmRuleContent("[{\"cmpValue\":\"100\",\"expression\":\"downlink.avg.loss.ratio > 100\",\"field\":\"name1\",\"fieldType\":\"number\",\"operator\":\">\",\"type\":0}, {\"cmpValue\":\"100\",\"expression\":\"downlink.avg.loss.ratio > 100\",\"field\":\"name2\",\"fieldType\":\"number\",\"operator\":\">\",\"type\":0}]");
        rules.add(alarmRuleDO1);
        rules.add(alarmRuleDO2);
        when(alarmHandler.listRuleByAlarmIds(Lists.newArrayList("alarm1"))).thenReturn(rules);
        when(alarmHandler.listRuleByAlarmIds(Lists.newArrayList("alarm2"))).thenReturn(Lists.newArrayList(alarmRuleDO1));

        ResponseObject responseObject = alarmService.getFieldAlarmRelationsByMetricsId(metricsIdPara);
        Assertions.assertEquals("success", responseObject.getStatus());
        FieldAlarmRelationOut out = (FieldAlarmRelationOut)responseObject.getData();
        System.out.println("out = " + JsonUtils.toJsonString(out));
        Assertions.assertEquals(0, out.getRelations().size());
    }

    @Test
    public void getAlarmsByMetricsIdHasAlarms() {
        String metricsId = "ecb9f353-31f0-45e0-9909-1ca7c130b3bf";
        MetricsIdPara metricsIdPara = Mockito.mock(MetricsIdPara.class);
        when(metricsIdPara.getMetricsId()).thenReturn(metricsId);
        List<AlarmDO> alarmDOList = new ArrayList<>();
        AlarmDO alarmDO1 = new AlarmDO();
        AlarmDO alarmDO2 = new AlarmDO();
        alarmDO1.setId("1");
        alarmDO1.setName("a1");
        alarmDO2.setId("2");
        alarmDO2.setName("a2");
        alarmDOList.add(alarmDO1);
        alarmDOList.add(alarmDO2);
        when(alarmHandler.listAlarmsByMetricsId(metricsId)).thenReturn(alarmDOList);
        ResponseObject responseObject = alarmService.getAlarmsByMetricsId(metricsIdPara);
        Assertions.assertEquals("success", responseObject.getStatus());
        MetricsAlarmsRelationOut metricsAlarmsRelationOut = (MetricsAlarmsRelationOut)responseObject.getData();
        System.out.println("metricsAlarmsRelationOut = " + JsonUtils.toJsonString(metricsAlarmsRelationOut));
        Assertions.assertEquals(2, metricsAlarmsRelationOut.getAlarms().size());
        Assertions.assertEquals("a1", metricsAlarmsRelationOut.getAlarms().get(0).getName());

    }

    @Test
    public void getAlarmsByMetricsIdNoAlarms() {
        String metricsId = "testNoAlarms";
        MetricsIdPara metricsIdPara = Mockito.mock(MetricsIdPara.class);
        when(metricsIdPara.getMetricsId()).thenReturn(metricsId);
        List<AlarmDO> alarmDOList = new ArrayList<>();
        when(alarmHandler.listAlarmsByMetricsId(metricsId)).thenReturn(alarmDOList);
        ResponseObject responseObject = alarmService.getAlarmsByMetricsId(metricsIdPara);
        Assertions.assertEquals("success", responseObject.getStatus());
        MetricsAlarmsRelationOut metricsAlarmsRelationOut = (MetricsAlarmsRelationOut)responseObject.getData();
        System.out.println("metricsAlarmsRelationOut = " + JsonUtils.toJsonString(metricsAlarmsRelationOut));
        Assertions.assertEquals(0, metricsAlarmsRelationOut.getAlarms().size());

    }

}
