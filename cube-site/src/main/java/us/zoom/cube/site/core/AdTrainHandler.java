package us.zoom.cube.site.core;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import us.zoom.cube.site.core.model.ad.AdTrainData;
import us.zoom.cube.site.core.model.ad.AdTrainDataOut;
import us.zoom.infra.dao.model.AdCfgDO;
import us.zoom.infra.dao.model.AdTrainDO;
import us.zoom.infra.dao.service.AdCfgDAO;
import us.zoom.infra.dao.service.AdTrainDAO;
import us.zoom.infra.enums.AdTrainStatusEnum;
import us.zoom.infra.utils.IdUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-01-04 13:21
 */
@Component
@Slf4j
public class AdTrainHandler {

    @Autowired
    private AdTrainDAO adTrainDAO;

    @Autowired
    private AdCfgDAO adCfgDAO;

    public List<AdTrainDataOut> getAdTrainByAdId(String adId) {
        checkAndAddAdTrainStatus(adId);
        List<AdTrainDataOut> adTrainDataOutList = new ArrayList<>();
        List<AdTrainDO> adTrainDOList = adTrainDAO.getAdTrainByAdId(adId);
        if (!CollectionUtils.isEmpty(adTrainDOList)) {
            for (AdTrainDO adTrainDO : adTrainDOList) {
                AdTrainDataOut adTrainDataOut = new AdTrainDataOut();
                BeanUtils.copyProperties(adTrainDO, adTrainDataOut);
                adTrainDataOutList.add(adTrainDataOut);
            }
        }
        return adTrainDataOutList;
    }

    @Transactional(rollbackFor = Exception.class)
    public void addAdTrainStatus(AdTrainData adTrainData) {
        List<AdTrainDO> adTrainDOList = adTrainDAO.getAdTrainByAdId(adTrainData.getAdConfigId());
        if (CollectionUtils.isEmpty(adTrainDOList)) {
            AdTrainDO adTrainDO = new AdTrainDO();
            BeanUtils.copyProperties(adTrainData, adTrainDO);
            adTrainDO.setId(IdUtils.generateId());
            adTrainDO.setAdTrainStatus(adTrainData.getAdTrainStatus());
            adTrainDAO.add(adTrainDO);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void editAdTrainStatus(AdTrainData adTrainData) {
        List<AdTrainDO> adTrainDOList = adTrainDAO.getAdTrainByAdId(adTrainData.getAdConfigId());
        AdTrainDO adTrainDO = new AdTrainDO();
        BeanUtils.copyProperties(adTrainData, adTrainDO);
        adTrainDO.setAdTrainStatus(adTrainData.getAdTrainStatus());
        if (CollectionUtils.isEmpty(adTrainDOList)) {
            adTrainDO.setId(IdUtils.generateId());
            adTrainDAO.add(adTrainDO);
        }
        adTrainDAO.update(adTrainDO);
    }

    public void checkAndAddAdTrainStatus(String adId) {
        List<AdTrainDO> adTrainDOList = adTrainDAO.getAdTrainByAdId(adId);
        List<AdCfgDO> adCfgDOList = adCfgDAO.getById(adId);
        if (CollectionUtils.isEmpty(adTrainDOList)) {
            if (!CollectionUtils.isEmpty(adCfgDOList)) {
                AdTrainData adTrainData = new AdTrainData();
                adTrainData.setAdTrainStatus(AdTrainStatusEnum.complete.getStatus());
                adTrainData.setMetricsId(adCfgDOList.get(0).getMetricsId());
                adTrainData.setAdConfigId(adCfgDOList.get(0).getId());
                addAdTrainStatus(adTrainData);
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteByAdId(String adId) {
        adTrainDAO.deleteByAdId(adId);
    }

    public List<AdTrainDO> getAdTrainByAdId(List<String> adIds) {
        return adTrainDAO.getAdTrainByAdIds(adIds);
    }
}
