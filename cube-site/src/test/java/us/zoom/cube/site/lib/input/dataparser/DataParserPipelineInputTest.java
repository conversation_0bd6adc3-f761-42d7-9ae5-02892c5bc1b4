package us.zoom.cube.site.lib.input.dataparser;

import org.junit.jupiter.api.Test;
import us.zoom.cube.lib.common.DataParserUseStatusEnum;
import us.zoom.cube.site.infra.utils.IdUtils;
import us.zoom.cube.site.lib.common.dataparser.ProcessorInput;
import us.zoom.cube.site.lib.input.CollectorFieldInput;
import us.zoom.cube.lib.utils.JsonUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * @Author: luis.zheng
 * @Date: 2021/8/26 9:05
 */
public class DataParserPipelineInputTest {
    @Test
    public void toJson() {
        DataParserPipelineInput input = new DataParserPipelineInput();

        List<DataField> fields = new ArrayList<>();
        fields.add(new DataField(IdUtils.generateId(), IdUtils.generateId(), "fieldName", "fieldType", true,0,"", ""));
        input.setFields(fields);
        List<DataParserPipelineInput> sons = new ArrayList<>();
        input.setSons(sons);
        List<DataParserFilterProcessorInput> filterProcessores = new ArrayList<>();
        DataParserFilterProcessorInput dataParserFilterProcessorInput = new DataParserFilterProcessorInput();
        dataParserFilterProcessorInput.setFilterRule("filterRule");
        dataParserFilterProcessorInput.setId(IdUtils.generateId());
        dataParserFilterProcessorInput.setDataParserId(IdUtils.generateId());
        dataParserFilterProcessorInput.setDataParserPipelineId(IdUtils.generateId());
        dataParserFilterProcessorInput.setName(IdUtils.generateId());
        dataParserFilterProcessorInput.setOrder(1);
        dataParserFilterProcessorInput.setType("type");
        dataParserFilterProcessorInput.setParas(new HashMap<>());
        filterProcessores.add(dataParserFilterProcessorInput);
        input.setFilterProcessores(filterProcessores);
        List<DataParserGrokProcessorInput> grokProcessores = new ArrayList<>();
        DataParserGrokProcessorInput dataParserGrokProcessorInput = new DataParserGrokProcessorInput();
        dataParserGrokProcessorInput.setParseRule("setParseRule");
        dataParserGrokProcessorInput.setTargetField("setTargetField");
        dataParserGrokProcessorInput.setId(IdUtils.generateId());
        dataParserGrokProcessorInput.setDataParserId(IdUtils.generateId());
        dataParserGrokProcessorInput.setDataParserPipelineId(IdUtils.generateId());
        dataParserGrokProcessorInput.setName(IdUtils.generateId());
        dataParserGrokProcessorInput.setOrder(1);
        dataParserGrokProcessorInput.setType("type");
        dataParserGrokProcessorInput.setParas(new HashMap<>());
        grokProcessores.add(dataParserGrokProcessorInput);
        input.setGrokProcessores(grokProcessores);


        List<DataParserRemapperProcessorInput> remapperProcessores = new ArrayList<>();
        DataParserRemapperProcessorInput dataParserRemapperProcessorInput = new DataParserRemapperProcessorInput();
        dataParserRemapperProcessorInput.setSourceFileds("setSourceFileds");
        dataParserRemapperProcessorInput.setTargetFields("setTargetFields");
        remapperProcessores.add(dataParserRemapperProcessorInput);
        input.setRemapperProcessores(remapperProcessores);


        input.setDataParserId(IdUtils.generateId());
        input.setId(IdUtils.generateId());
        input.setName("name");
        input.setId("filterRule");
        input.setCollectorId(IdUtils.generateId());
        input.setParentId(IdUtils.generateId());
        input.setUseStatus(DataParserUseStatusEnum.stop.getValue());
        input.setTopic("topic");
        input.setOrder(1);

        System.out.println(JsonUtils.toJsonString(input));
    }

    @Test
    public void testProcessorInputJson() {
        ProcessorInput input = new ProcessorInput();
        input.setId(IdUtils.generateId());
        input.setDataParserPipelineId(IdUtils.generateId());
        input.setDataParserId(IdUtils.generateId());
        input.setName("name");
        input.setOrder(1);
        input.setType("type");
        input.setParas(new HashMap<>());

        System.out.println(JsonUtils.toJsonString(input));
    }

    @Test
    public void testCollectorFieldInput() {
        CollectorFieldInput collectorFieldInput = new CollectorFieldInput();
        collectorFieldInput.setId(IdUtils.generateId());
        collectorFieldInput.setCollectorId(IdUtils.generateId());
        collectorFieldInput.setPipelineId(IdUtils.generateId());
        collectorFieldInput.setPipelineName("PipelineName");
        collectorFieldInput.setDataParserName("DataParserName");
        collectorFieldInput.setSourceField("SourceField");
        collectorFieldInput.setTargetField("TargetField");
        collectorFieldInput.setFieldType("FieldType");
        collectorFieldInput.setCanEdit(true);
        System.out.println(JsonUtils.toJsonString(collectorFieldInput));
    }
}
