package us.zoom.cube.site.lib.output.codeperformance;

import lombok.Data;


/**
 * @author: tobey.zhu
 * @date: /2023/07/20
 * @description:
 */
@Data
public class FlameGraphCountSummaryOut {
    private String dimension;
    private Long sampleCount;
    public FlameGraphCountSummaryOut(Object dimension, Object sampleCount) {
        this.dimension = (String)dimension;
        this.sampleCount = Long.parseLong(sampleCount.toString());
    }
}
