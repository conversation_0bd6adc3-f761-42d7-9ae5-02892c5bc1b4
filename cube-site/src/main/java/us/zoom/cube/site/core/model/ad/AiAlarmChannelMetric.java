package us.zoom.cube.site.core.model.ad;

import lombok.Data;
import us.zoom.cube.sdk.annotation.CubeMonitorLog;
import us.zoom.cube.sdk.annotation.Field;
import us.zoom.cube.sdk.annotation.Tag;

/**
 * <AUTHOR>
 * @date 2024-07-10 14:35
 */
@Data
@CubeMonitorLog(measure = "AiAlarmChannelMetric")
public class AiAlarmChannelMetric {
    @Tag
    private String host;
    @Tag
    private String ip;
    @Tag
    private String serviceName;
    @Field
    private String alarmName;
    @Field
    private String channelName;
    @Field
    private String engineName;

    public AiAlarmChannelMetric(String host, String ip, String serviceName, String alarmName,
                                String channelName, String engineName) {
        this.host = host;
        this.ip = ip;
        this.serviceName = serviceName;
        this.channelName = channelName;
        this.alarmName = alarmName;
        this.engineName = engineName;
    }
}
