package us.zoom.cube.site.core;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RestController;
import us.zoom.cube.site.lib.query.AlarmRecordsQuery;
import us.zoom.infra.dao.result.MonitorResult;
import us.zoom.infra.dao.service.MonitorDAO;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@RestController
public class MonitorHandler {
    @Autowired
    private MonitorDAO monitorDAO;
    public List<MonitorResult> findByAlarmNameLike(AlarmRecordsQuery alarmRecordsQuery, int pageIndex, int pageSize) throws Exception {
            return monitorDAO.findByAlarmNameLike(alarmRecordsQuery.getName(),alarmRecordsQuery.getBegin(),alarmRecordsQuery.getEnd(), pageIndex, pageSize);
    }
}
