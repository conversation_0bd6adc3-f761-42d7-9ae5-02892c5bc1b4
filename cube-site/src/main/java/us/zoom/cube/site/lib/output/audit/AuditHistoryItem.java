package us.zoom.cube.site.lib.output.audit;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AuditHistoryItem {
    private String operator;
    private Object source;
    private String queryError;
    private String requestURI;
    private String trackingId;
    private String txId;
    private Date updateTime;
    private String resourceId;
    private String comment;
}
