package us.zoom.cube.site.core.auth;


import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import us.zoom.cube.lib.monitor.MonitorWrapper;
import us.zoom.cube.site.api.intercept.AuthInterceptor;
import us.zoom.infra.dao.model.UserRoleDO;
import us.zoom.infra.dao.service.RoleMenuRelaDAO;
import us.zoom.infra.dao.service.UserRoleDAO;
import us.zoom.cube.lib.utils.JsonUtils;

import jakarta.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class UserRoleHandler {

    @Autowired
    private UserRoleDAO userRoleDAO;

    @Autowired
    private RoleMenuRelaDAO roleMenuRelaDAO;
    private Logger monitorLog = LoggerFactory.getLogger("Monitor");


    /**
     * Map<role,userRoleDO>
     */
    private static final AtomicReference<Map<String,UserRoleDO>> userRoleCacheRef = new AtomicReference<>(new HashMap<>());
    /**
     * Set<role>
     */
    private static final AtomicReference<Set<String>> crossServiceRoleCacheRef = new AtomicReference<>(new HashSet<>());


    /**
     * Set<role>
     */
    private static final AtomicReference<Set<String>> piiRoleCacheRef = new AtomicReference<>(new HashSet<>());

    @PostConstruct
    public void loadRoleCache() {
        MonitorWrapper monitorWrapper = new MonitorWrapper("load_role");
        long begin = System.currentTimeMillis();
        try{

            List<UserRoleDO> allRoles = userRoleDAO.listAll();
            monitorWrapper.addField("queryDbTime",System.currentTimeMillis());
            if(CollectionUtils.isEmpty(allRoles)){
                monitorWrapper.addTag("phase","no_role");
                monitorWrapper.addTag("status","fail");
                printMonitorLog(monitorWrapper, begin);
                return;
            }
            Map<String,UserRoleDO> userRoleDOMap = new HashMap<>();
            Set<String>  crossServiceRoleCache = new HashSet<>();
            Set<String>  piiRoleCache = new HashSet<>();
            allRoles.forEach(role->{
                role.setRoleDesc(null);
                role.setCreateTime(null);
                role.setModifyTime(null);
                userRoleDOMap.put(role.getRole(),role);
                if(role.getCrossService() != null && role.getCrossService() == true){
                    crossServiceRoleCache.add(role.getRole());
                }

                if(role.getPiiAccess() != null && role.getPiiAccess() == true){
                    piiRoleCache.add(role.getRole());
                }
            });

            userRoleCacheRef.set(userRoleDOMap);
            crossServiceRoleCacheRef.set(crossServiceRoleCache);
            piiRoleCacheRef.set(piiRoleCache);
            monitorWrapper.addTag("status","success");
        }catch (Exception e){
            log.error("loadRoleCache error ",e);
            monitorWrapper.addTag("status","fail");
            monitorWrapper.addField("msg",e.getMessage());
            monitorWrapper.addField("exp",JsonUtils.toJsonStringIgnoreExp(e));
        }
        printMonitorLog(monitorWrapper, begin);

    }


    public UserRoleDO getRoleByNameFromCache(String role){
        return  userRoleCacheRef.get().get(role);
    }
    /**
     *
     * @param roles
     * @return
     */
    public List<UserRoleDO> getRoleByNamesFromCache(List<String> roles){
        if(CollectionUtils.isEmpty(roles)){
            return Collections.emptyList();
        }
        List<UserRoleDO> result  = new ArrayList<>(roles.size());
        roles.forEach(role->{
            UserRoleDO userRoleDO =   userRoleCacheRef.get().get(role);
            if(null == userRoleDO){
                result.add(userRoleDO);
            }
        });
        return result;
    }

    private void printMonitorLog(MonitorWrapper monitorWrapper, long begin) {
        monitorWrapper.setTs(System.currentTimeMillis());
        monitorWrapper.addField("totalTime",System.currentTimeMillis()-begin);
        monitorLog.info(JsonUtils.toJsonStringIgnoreExp(monitorWrapper));
    }


    public void addRole(UserRoleDO userRoleDO) {
        userRoleDAO.addRole(userRoleDO);
    }

    public void editRole(UserRoleDO userRoleDO) {
        userRoleDAO.editRole(userRoleDO);
    }

    @Transactional
    public void deleteRoleByName(String roleName) {
        userRoleDAO.deleteRoleByName(roleName);
        roleMenuRelaDAO.deleteRelaByRoleNames(Arrays.asList(roleName));
    }

    public List<UserRoleDO> findByNameLike(String roleName, int pageIndex, int pageSize) {
        return userRoleDAO.findByNameLike(roleName,pageSize*(pageIndex-1), pageSize);
    }

    public int getCountByNameLike(String roleName) {
        return userRoleDAO.getCountByNameLike(roleName);
    }

    public void editPiiAccess(String role, Boolean piiAccess) {
        if(null == piiAccess){
            piiAccess = false;
        }
        userRoleDAO.editPiiAccess(role, piiAccess, AuthInterceptor.getUserName());
    }

    public void editCanOperate(String role, Boolean canOperate) {
        if(null == canOperate){
            canOperate = false;
        }
        userRoleDAO.editCanOperate(role, canOperate, AuthInterceptor.getUserName());
    }


    public UserRoleDO getRoleByName(String roleName){
       return userRoleDAO.getRoleByName(roleName);
    }

    public Set<String> getCrossServiceRole(Set<String> inputRoles) {
        if(CollectionUtils.isEmpty(inputRoles)){
            return  Collections.emptySet();
        }
        Set<String> result = new HashSet<>();
        inputRoles.forEach(item->{
            if(crossServiceRoleCacheRef.get().contains(item)){
                result.add(item);
            }
        });
        return result;
    }

    public Set<String> getAllRoles(){
        return userRoleCacheRef.get().values().stream().map(x -> x.getRole()).collect(Collectors.toSet());
    }

    public Set<String>  getPiiRole(Set<String> inputRoles) {
        if(CollectionUtils.isEmpty(inputRoles)){
            return  Collections.emptySet();
        }
        Set<String> result = new HashSet<>();
        inputRoles.forEach(item->{
            if(piiRoleCacheRef.get().contains(item)){
                result.add(item);
            }
        });
        return result;
    }
}
