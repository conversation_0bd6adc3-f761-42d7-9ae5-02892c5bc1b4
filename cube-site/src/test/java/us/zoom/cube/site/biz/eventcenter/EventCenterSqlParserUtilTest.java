package us.zoom.cube.site.biz.eventcenter;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import us.zoom.cube.site.lib.input.eventcenter.EventCenterQuery;
import us.zoom.cube.site.lib.query.PageQuery;

public class EventCenterSqlParserUtilTest {

    @Test
    public void testBuildSearchSql() {
        PageQuery<EventCenterQuery> pageQuery = new PageQuery<>();
        EventCenterQuery queryPara = new EventCenterQuery();

        queryPara.setCategory("TestCate");
        queryPara.setAction("TestAction");
        queryPara.setEndTime(11111111111L);
        queryPara.setStartTime(11111111199L);

        pageQuery.setPageSize(10);
        pageQuery.setQueryPara(queryPara);
        pageQuery.setPageIndex(1);
        String s = EventCenterSqlParserUtil.buildSearchSql(pageQuery);

        Assertions.assertTrue(s.contains("TestCate") && s.contains("TestAction"));
    }
}
