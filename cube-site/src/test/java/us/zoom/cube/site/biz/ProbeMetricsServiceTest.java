package us.zoom.cube.site.biz;

import cn.hutool.core.util.ReflectUtil;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import us.zoom.cube.site.core.ProbeTaskHandler;
import us.zoom.cube.site.core.config.ClickhouseHandlerFactory;
import us.zoom.cube.site.infra.utils.IdUtils;
import us.zoom.cube.site.lib.output.e2e.ProbeGroupResult;
import us.zoom.cube.site.lib.output.e2e.ProbeResultTags;
import us.zoom.cube.site.lib.output.e2e.StepResult;
import us.zoom.cube.site.lib.query.PageQuery;
import us.zoom.cube.site.lib.query.ProbeMetricsQuery;
import us.zoom.infra.utils.DateUtils;
import us.zoom.cube.lib.utils.JsonUtils;

import java.lang.reflect.Method;
import java.sql.Timestamp;
import java.text.ParseException;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * @Author: <EMAIL>
 * @ModuleOwner: <EMAIL>
 * @Date: 07/25/2022 14:26
 * @Description:
 */
public class ProbeMetricsServiceTest {
    private ProbeMetricsService probeMetricsService;
    private String TABLE_NAME = "cube_probe.probe_metrics4";
    private String SERVICE_NAME = "cube-probe";

    @BeforeEach
    public void before() {
        ClickhouseHandlerFactory clickhouseHandlerFactory = new ClickhouseHandlerFactory();
        ProbeTaskHandler probeTaskHandler = new ProbeTaskHandler();
        probeMetricsService = new ProbeMetricsService();
        probeMetricsService.setProbeMetricsServiceName(SERVICE_NAME);
        probeMetricsService.setProbeMetricsTableName(TABLE_NAME);
    }

    @Test
    public void testSqlSummary() throws ParseException {
        PageQuery<ProbeMetricsQuery> qb = new PageQuery<>();
        ProbeMetricsQuery probeMetricsQuery = new ProbeMetricsQuery();
        probeMetricsQuery.setGroupBy(Arrays.asList("service"));

        probeMetricsQuery.setStartTime(DateUtils.parseDate("2022-07-03 06:15:50", DateUtils.FORMART1).getTime());
        probeMetricsQuery.setEndTime(DateUtils.parseDate("2022-07-04 08:15:50", DateUtils.FORMART1).getTime());

        qb.setQueryPara(probeMetricsQuery);
        System.out.println(probeMetricsService.sqlSummary(probeMetricsQuery));
        Assertions.assertEquals("select service, MAX(status) AS status, toStartOfInterval(time, INTERVAL 3600 SECOND) AS time from " +
                        "cube_probe.probe_metrics4" +
                        " where time > '2022-07-03 06:15:50' AND time < '2022-07-04 08:15:50'   GROUP BY time,service",
                probeMetricsService.sqlSummary(probeMetricsQuery).trim());
    }

    @Test
    public void testProbeResultSummary() throws Exception {
        ProbeMetricsQuery query = new ProbeMetricsQuery();
        query.setGranularity(10 * 60);
        query.setGroupBy(Arrays.asList("service"));
        query.setStartTime(DateUtils.parseDate("2022-08-01 19:23:20", DateUtils.FORMART1).getTime());
        query.setEndTime(DateUtils.parseDate("2022-08-01 20:34:20", DateUtils.FORMART1).getTime());
        query.check();

        List<Map<String, Object>> queryResult = new ArrayList<>();
        Map<String, Object> mapRealTime1 = new HashMap<>();
        mapRealTime1.put("time", new Timestamp(DateUtils.parseDate("2022-08-01 19:20:00", DateUtils.FORMART1).getTime()));
        mapRealTime1.put("status", Double.parseDouble("2"));
        mapRealTime1.put("service", "realtime");
        Map<String, Object> mapRealTime2 = new HashMap<>();
        mapRealTime2.put("time", new Timestamp(DateUtils.parseDate("2022-08-01 19:40:00", DateUtils.FORMART1).getTime()));
        mapRealTime2.put("status", Double.parseDouble("1"));
        mapRealTime2.put("service", "realtime");
        Map<String, Object> mapRealTime3 = new HashMap<>();
        mapRealTime3.put("time", new Timestamp(DateUtils.parseDate("2022-08-01 20:10:00", DateUtils.FORMART1).getTime()));
        mapRealTime3.put("status", Double.parseDouble("1"));
        mapRealTime3.put("service", "realtime");
        Map<String, Object> mapRealTime4 = new HashMap<>();
        mapRealTime4.put("time", new Timestamp(DateUtils.parseDate("2022-08-01 20:40:00", DateUtils.FORMART1).getTime()));
        mapRealTime4.put("status", Double.parseDouble("0"));
        mapRealTime4.put("service", "realtime");
        queryResult.add(mapRealTime1);
        queryResult.add(mapRealTime2);
        queryResult.add(mapRealTime3);
        queryResult.add(mapRealTime4);

        Map<String, Object> mapWeb1 = new HashMap<>();
        mapWeb1.put("time", new Timestamp(DateUtils.parseDate("2022-08-01 20:20:00", DateUtils.FORMART1).getTime()));
        mapWeb1.put("status", Double.parseDouble("0"));
        mapWeb1.put("service", "web");
        Map<String, Object> mapWeb2 = new HashMap<>();
        mapWeb2.put("time", new Timestamp(DateUtils.parseDate("2022-08-01 20:30:00", DateUtils.FORMART1).getTime()));
        mapWeb2.put("status", Double.parseDouble("1"));
        mapWeb2.put("service", "web");
        Map<String, Object> mapWeb3 = new HashMap<>();
        mapWeb3.put("time", new Timestamp(DateUtils.parseDate("2022-08-01 20:40:00", DateUtils.FORMART1).getTime()));
        mapWeb3.put("status", Double.parseDouble("0"));
        mapWeb3.put("service", "web");
        queryResult.add(mapWeb1);
        queryResult.add(mapWeb2);
        queryResult.add(mapWeb3);

        Map<String, Object> mapMmr1 = new HashMap<>();
        mapMmr1.put("time", new Timestamp(DateUtils.parseDate("2022-08-01 19:20:00", DateUtils.FORMART1).getTime()));
        mapMmr1.put("status", Double.parseDouble("0"));
        mapMmr1.put("service", "mmr");
        Map<String, Object> mapMmr2 = new HashMap<>();
        mapMmr2.put("time", new Timestamp(DateUtils.parseDate("2022-08-01 20:40:00", DateUtils.FORMART1).getTime()));
        mapMmr2.put("status", Double.parseDouble("1"));
        mapMmr2.put("service", "mmr");
        queryResult.add(mapMmr1);
        queryResult.add(mapMmr2);

        Map<ProbeResultTags, TreeMap<Long, Integer>> result = probeMetricsService.parseSummaryResult(queryResult, query);
        TreeMap<Long, Integer> mmr = result.get(new ProbeResultTags("mmr", null, null));
        AtomicInteger mmrNum = new AtomicInteger();
        mmr.forEach((key, value) -> {
            if (value == null) {
                mmrNum.getAndIncrement();
            }
        });
        Assertions.assertEquals(mmrNum.get(), 7);

        TreeMap<Long, Integer> web = result.get(new ProbeResultTags("web", null, null));
        AtomicInteger webNum = new AtomicInteger();
        web.forEach((key, value) -> {
            if (value == null) {
                webNum.getAndIncrement();
            }
        });
        Assertions.assertEquals(webNum.get(), 6);

        TreeMap<Long, Integer> realtime = result.get(new ProbeResultTags("realtime", null, null));
        AtomicInteger realtimeNum = new AtomicInteger();
        realtime.forEach((key, value) -> {
            if (value == null) {
                realtimeNum.getAndIncrement();
            }
        });
        Assertions.assertEquals(realtimeNum.get(), 5);
    }

    private String probeTaskId = IdUtils.generateId();

    @Test
    public void testParseDetailResult() throws Exception {
        ProbeMetricsQuery query = new ProbeMetricsQuery();
        query.setStartTime(DateUtils.parseDate("2022-08-01 19:23:20", DateUtils.FORMART1).getTime());
        query.setEndTime(DateUtils.parseDate("2022-08-01 20:34:20", DateUtils.FORMART1).getTime());
        query.setService("realtime");
        query.setProbeTaskId(probeTaskId);
        query.setProbeTaskName("probeTaskName");
        query.checkDetailParam();

        List<Map<String, Object>> queryResult = new ArrayList<>();
        Map<String, Object> group1Time1 = new HashMap<>();
        group1Time1.put("time", new Timestamp(DateUtils.parseDate("2022-08-01 19:20:21", DateUtils.FORMART1).getTime()));
        group1Time1.put("rt", Double.parseDouble("201"));
        group1Time1.put("status", Double.parseDouble("2"));
        group1Time1.put("probePointGroupName", "group1");
        group1Time1.put("result", "[{\"name\":\"step_name1\",\"ts\":1659063747286,\"status\":1,\"rt\":100,\"traceId\":\"id1\",\"customResult\":{\"tag1\":false,\"tag4\":[\"value1\",\"value21\",\"value31\"],\"tag5\":{\"mapkey1\":1},\"tag2\":1,\"tag3\":1.3},\"extra\":null},{\"name\":\"step_name2\",\"ts\":1659063747286,\"status\":1,\"rt\":100,\"traceId\":\"id1\",\"customResult\":null,\"extra\":null}]");

        Map<String, Object> group1Time2 = new HashMap<>();
        group1Time2.put("time", new Timestamp(DateUtils.parseDate("2022-08-01 19:32:21", DateUtils.FORMART1).getTime()));
        group1Time2.put("rt", Double.parseDouble("330"));
        group1Time2.put("status", Double.parseDouble("1"));
        group1Time2.put("probePointGroupName", "group1");
        group1Time2.put("result", "[{\"name\":\"step_name1\",\"ts\":1659063747286,\"status\":1,\"rt\":100,\"traceId\":\"id1\",\"customResult\":{\"tag1\":false,\"tag4\":[\"value1\",\"value21\",\"value31\"],\"tag5\":{\"mapkey1\":1},\"tag2\":1,\"tag3\":1.3},\"extra\":null},{\"name\":\"step_name2\",\"ts\":1659063747286,\"status\":1,\"rt\":100,\"traceId\":\"id1\",\"customResult\":null,\"extra\":null}]");

        Map<String, Object> group1Time3 = new HashMap<>();
        group1Time3.put("time", new Timestamp(DateUtils.parseDate("2022-08-01 20:32:21", DateUtils.FORMART1).getTime()));
        group1Time3.put("rt", Double.parseDouble("530"));
        group1Time3.put("status", Double.parseDouble("1"));
        group1Time3.put("probePointGroupName", "group1");
        group1Time3.put("result", "[{\"name\":\"step_name1\",\"ts\":1659063747286,\"status\":1,\"rt\":100,\"traceId\":\"id1\",\"customResult\":{\"tag1\":false,\"tag4\":[\"value1\",\"value21\",\"value31\"],\"tag5\":{\"mapkey1\":1},\"tag2\":1,\"tag3\":1.3},\"extra\":null},{\"name\":\"step_name2\",\"ts\":1659063747286,\"status\":1,\"rt\":100,\"traceId\":\"id1\",\"customResult\":null,\"extra\":null}]");

        //---group2
        Map<String, Object> group2Time1 = new HashMap<>();
        group2Time1.put("time", new Timestamp(DateUtils.parseDate("2022-08-01 19:20:21", DateUtils.FORMART1).getTime()));
        group2Time1.put("rt", Double.parseDouble("530"));
        group2Time1.put("status", Double.parseDouble("1"));
        group2Time1.put("probePointGroupName", "group2");
        group2Time1.put("result", "[{\"name\":\"step_name1\",\"ts\":1659063747286,\"status\":1,\"rt\":100,\"traceId\":\"id1\",\"customResult\":{\"tag1\":false,\"tag4\":[\"value1\",\"value21\",\"value31\"],\"tag5\":{\"mapkey1\":1},\"tag2\":1,\"tag3\":1.3},\"extra\":null},{\"name\":\"step_name2\",\"ts\":1659063747286,\"status\":1,\"rt\":100,\"traceId\":\"id1\",\"customResult\":null,\"extra\":null}]");

        Map<String, Object> group2Time2 = new HashMap<>();
        group2Time2.put("time", new Timestamp(DateUtils.parseDate("2022-08-01 19:28:21", DateUtils.FORMART1).getTime()));
        group2Time2.put("rt", Double.parseDouble("530"));
        group2Time2.put("status", Double.parseDouble("1"));
        group2Time2.put("probePointGroupName", "group2");
        group2Time2.put("result", "[{\"name\":\"step_name1\",\"ts\":1659063747286,\"status\":1,\"rt\":100,\"traceId\":\"id1\",\"customResult\":{\"tag1\":false,\"tag4\":[\"value1\",\"value21\",\"value31\"],\"tag5\":{\"mapkey1\":1},\"tag2\":1,\"tag3\":1.3},\"extra\":null},{\"name\":\"step_name2\",\"ts\":1659063747286,\"status\":1,\"rt\":100,\"traceId\":\"id1\",\"customResult\":null,\"extra\":null}]");

        Map<String, Object> group2Time3 = new HashMap<>();
        group2Time3.put("time", new Timestamp(DateUtils.parseDate("2022-08-01 20:32:21", DateUtils.FORMART1).getTime()));
        group2Time3.put("rt", Double.parseDouble("530"));
        group2Time3.put("status", Double.parseDouble("1"));
        group2Time3.put("probePointGroupName", "group2");
        group2Time3.put("result", "[{\"name\":\"step_name1\",\"ts\":1659063747286,\"status\":1,\"rt\":100,\"traceId\":\"id1\",\"customResult\":{\"tag1\":false,\"tag4\":[\"value1\",\"value21\",\"value31\"],\"tag5\":{\"mapkey1\":1},\"tag2\":1,\"tag3\":1.3},\"extra\":null},{\"name\":\"step_name2\",\"ts\":1659063747286,\"status\":1,\"rt\":100,\"traceId\":\"id1\",\"customResult\":null,\"extra\":null}]");

        //---group3
        Map<String, Object> group3Time1 = new HashMap<>();
        group3Time1.put("time", new Timestamp(DateUtils.parseDate("2022-08-01 19:20:21", DateUtils.FORMART1).getTime()));
        group3Time1.put("rt", Double.parseDouble("530"));
        group3Time1.put("status", Double.parseDouble("1"));
        group3Time1.put("probePointGroupName", "group3");
        group3Time1.put("result", "[{\"name\":\"step_name1\",\"ts\":1659063747286,\"status\":1,\"rt\":100,\"traceId\":\"id1\",\"customResult\":{\"tag1\":false,\"tag4\":[\"value1\",\"value21\",\"value31\"],\"tag5\":{\"mapkey1\":1},\"tag2\":1,\"tag3\":1.3},\"extra\":null},{\"name\":\"step_name2\",\"ts\":1659063747286,\"status\":1,\"rt\":100,\"traceId\":\"id1\",\"customResult\":null,\"extra\":null}]");

        Map<String, Object> group3Time2 = new HashMap<>();
        group3Time2.put("time", new Timestamp(DateUtils.parseDate("2022-08-01 19:29:21", DateUtils.FORMART1).getTime()));
        group3Time2.put("rt", Double.parseDouble("530"));
        group3Time2.put("status", Double.parseDouble("1"));
        group3Time2.put("probePointGroupName", "group3");
        group3Time2.put("result", "[{\"name\":\"step_name1\",\"ts\":1659063747286,\"status\":1,\"rt\":100,\"traceId\":\"id1\",\"customResult\":{\"tag1\":false,\"tag4\":[\"value1\",\"value21\",\"value31\"],\"tag5\":{\"mapkey1\":1},\"tag2\":1,\"tag3\":1.3},\"extra\":null},{\"name\":\"step_name2\",\"ts\":1659063747286,\"status\":1,\"rt\":100,\"traceId\":\"id1\",\"customResult\":null,\"extra\":null}]");

        Map<String, Object> group3Time3 = new HashMap<>();
        group3Time3.put("time", new Timestamp(DateUtils.parseDate("2022-08-01 20:32:21", DateUtils.FORMART1).getTime()));
        group3Time3.put("rt", Double.parseDouble("530"));
        group3Time3.put("status", Double.parseDouble("1"));
        group3Time3.put("probePointGroupName", "group3");
        group3Time3.put("result", "[{\"name\":\"step_name1\",\"ts\":1659063747286,\"status\":1,\"rt\":100,\"traceId\":\"id1\",\"customResult\":{\"tag1\":false,\"tag4\":[\"value1\",\"value21\",\"value31\"],\"tag5\":{\"mapkey1\":1},\"tag2\":1,\"tag3\":1.3},\"extra\":null},{\"name\":\"step_name2\",\"ts\":1659063747286,\"status\":1,\"rt\":100,\"traceId\":\"id1\",\"customResult\":null,\"extra\":null}]");

        Map<String, Object> group3Time4 = new HashMap<>();
        group3Time4.put("time", new Timestamp(DateUtils.parseDate("2022-08-01 20:35:21", DateUtils.FORMART1).getTime()));
        group3Time4.put("rt", Double.parseDouble("530"));
        group3Time4.put("status", Double.parseDouble("1"));
        group3Time4.put("probePointGroupName", "group3");
        group3Time4.put("result", "[{\"name\":\"step_name1\",\"ts\":1659063747286,\"status\":1,\"rt\":100,\"traceId\":\"id1\",\"customResult\":{\"tag1\":false,\"tag4\":[\"value1\",\"value21\",\"value31\"],\"tag5\":{\"mapkey1\":1},\"tag2\":1,\"tag3\":1.3},\"extra\":null},{\"name\":\"step_name2\",\"ts\":1659063747286,\"status\":1,\"rt\":100,\"traceId\":\"id1\",\"customResult\":null,\"extra\":null}]");

        queryResult.add(group1Time1);
        queryResult.add(group1Time2);
        queryResult.add(group1Time3);

        queryResult.add(group2Time1);
        queryResult.add(group2Time2);
        queryResult.add(group2Time3);

        queryResult.add(group3Time1);
        queryResult.add(group3Time2);
        queryResult.add(group3Time3);
        queryResult.add(group3Time4);


        TreeMap<String, TreeMap<Long, ProbeGroupResult>> result = probeMetricsService.parseDetailResult(queryResult);

        result.forEach((key, value) -> {
            System.out.println(key);
            value.forEach((time, re) -> {
                System.out.println("---->" + DateUtils.format(new Date(time), DateUtils.FORMART1) + ":  -->" + (re == null));
            });
        });
    }

    @Test
    public void test() {
        String result = "[{\"name\":\"test http get method probe\",\"ts\":1659519571986,\"status\":10,\"rt\":0,\"traceId\":null,\"customResult\":null,\"extra\":null},{\"name\":\"test http post method probe\",\"ts\":0,\"status\":0,\"rt\":0,\"traceId\":null,\"customResult\":null,\"extra\":null},{\"name\":\"test tcp probe\",\"ts\":0,\"status\":0,\"rt\":0,\"traceId\":null,\"customResult\":null,\"extra\":null},{\"name\":\"test tcp probe2\",\"ts\":0,\"status\":0,\"rt\":0,\"traceId\":null,\"customResult\":null,\"extra\":null}]";
        try {
            List<StepResult> results = JsonUtils.toObjectByTypeRef(result, ProbeMetricsService.STEPS_TYPE);
        } catch (Exception e) {
            Assertions.fail("fail");
        }
    }

    @Test
    public void testTime() throws ParseException {
        Date dateStart = DateUtils.parseDate("2022-10-20 07:22:22", DateUtils.FORMART1);
        Date dateEnd = DateUtils.parseDate("2022-10-20 10:22:22", DateUtils.FORMART1);

        System.out.println(DateUtils.format(new Date(dateStart.getTime() / (60 * 60 * 1000) * (60 * 60 * 1000)), DateUtils.FORMART1));
    }

    @Test
    public void testParseSummaryResult() throws Exception {
        ProbeMetricsService probeMetricsService = new ProbeMetricsService();
        Method method = ReflectUtil.getMethod(ProbeMetricsService.class, "highestPriorityStatus", Object.class);
        method.setAccessible(true);

        Integer status;

        status = (Integer) method.invoke(probeMetricsService, "0,1,10,20");
        Assertions.assertEquals(10, status.intValue());

        status = (Integer) method.invoke(probeMetricsService, "0,1,10");
        Assertions.assertEquals(10, status.intValue());

        status = (Integer) method.invoke(probeMetricsService, "0,1,20");
        Assertions.assertEquals(20, status.intValue());

        status = (Integer) method.invoke(probeMetricsService, "0");
        Assertions.assertEquals(0, status.intValue());

        status = (Integer) method.invoke(probeMetricsService, "1.0");
        Assertions.assertEquals(1, status.intValue());

        status = (Integer) method.invoke(probeMetricsService, "10.0");
        Assertions.assertEquals(10, status.intValue());

        status = (Integer) method.invoke(probeMetricsService, "30.0");
        Assertions.assertEquals(0, status.intValue());

        status = (Integer) method.invoke(probeMetricsService, "wrong");
        Assertions.assertEquals(0, status.intValue());

        status = (Integer) method.invoke(probeMetricsService, ",");
        Assertions.assertEquals(0, status.intValue());

        status = (Integer) method.invoke(probeMetricsService, "");
        Assertions.assertEquals(0, status.intValue());

        status = (Integer) method.invoke(probeMetricsService, "0,1.0,10.0,20.0");
        Assertions.assertEquals(10, status.intValue());

        status = (Integer) method.invoke(probeMetricsService, "0, 1.0, 10.0, 20.0");
        Assertions.assertEquals(10, status.intValue());

        status = (Integer) method.invoke(probeMetricsService, "0,1.0");
        Assertions.assertEquals(1, status.intValue());

        status = (Integer) method.invoke(probeMetricsService, "0,10.0");
        Assertions.assertEquals(10, status.intValue());

        status = (Integer) method.invoke(probeMetricsService, "20.0,10.0");
        Assertions.assertEquals(10, status.intValue());
    }
}