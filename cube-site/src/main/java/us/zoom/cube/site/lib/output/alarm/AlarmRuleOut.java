package us.zoom.cube.site.lib.output.alarm;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import org.springframework.format.annotation.DateTimeFormat;
import us.zoom.cube.site.lib.input.AlarmRuleSingleContentInput;

import java.util.Date;
import java.util.List;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AlarmRuleOut {

    String id;

    int lastCount;

    int alarmType;

    String alarmLevel;

    AlarmRuleContentOutput alarmRuleContent;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    Date createTime = new Date();

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    Date modifyTime = new Date();

}
