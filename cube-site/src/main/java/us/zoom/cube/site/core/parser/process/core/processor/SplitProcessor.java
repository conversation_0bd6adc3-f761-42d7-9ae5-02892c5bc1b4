package us.zoom.cube.site.core.parser.process.core.processor;


import us.zoom.cube.lib.hub.BaseProcessorCfg;
import us.zoom.cube.lib.hub.SplitEntryCfg;
import us.zoom.cube.lib.hub.SplitProcessorCfg;
import us.zoom.cube.lib.hub.SplitSchema;

import java.util.Map;
import java.util.regex.Pattern;

/**
 * @Author: <EMAIL>
 * @ModuleOwner: <EMAIL>
 * @Date: 04/03/2024 16:47
 * @Description:
 */
public class SplitProcessor extends Processor {

    public SplitProcessor() {
    }

    @Override
    public ProcessorResp process(Map<String, Object> messageMap, BaseProcessorCfg processorCfg) {
        ProcessorResp resp = new ProcessorResp(true,messageMap);
        try {
            SplitProcessorCfg splitProcessorCfg = (SplitProcessorCfg) processorCfg;
            String sourceFieldValue = splitProcessorCfg.getSourceField();
            SplitSchema splitSchema = splitProcessorCfg.getSchema();
            String[] array = ((String) messageMap.get(sourceFieldValue)).split(Pattern.quote(splitSchema.getSeparator()));
            if (array == null) {
                return resp;
            }
            splitSchema.getEntries().forEach(entryCfg -> {
                Object value = getValue(array, entryCfg);
                if (entryCfg.getNext() == null) {
                    messageMap.put(entryCfg.getName(), value);
                } else {
                    if (value != null) {
                        String splitStr = (String) value;
                        SplitSchema nextSplitSchema = entryCfg.getNext();
                        String[] nextArray = splitStr.split(Pattern.quote(nextSplitSchema.getSeparator()));
                        nextSplitSchema.getEntries().forEach(elementConfig -> {
                            messageMap.put(elementConfig.getName(), getValue(nextArray, elementConfig));
                        });
                    }
                }

            });
            logSuccess(messageMap,resp);
        } catch (Exception e) {
            logException(e, processorCfg,resp);
        }finally {
            logProcessor(processorCfg,resp);
        }
        return resp;
    }

    private Object getValue(String[] array, SplitEntryCfg cfg) {
        try {
            switch (cfg.getType()) {
                case number -> {
                    return Double.valueOf(array[cfg.getIndex()]);
                }
                default -> {
                    return array[cfg.getIndex()];
                }
            }
        } catch (Exception e) {
            return null;
        }
    }
}
