package us.zoom.cube.site.core;


import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.site.biz.MetricsService;
import us.zoom.cube.site.biz.model.CardConfigs;
import us.zoom.cube.site.biz.model.CardQueryConfig;
import us.zoom.cube.site.infra.utils.IdUtils;
import us.zoom.cube.site.lib.input.BatchCardInput;
import us.zoom.cube.site.lib.input.CardInput;
import us.zoom.cube.site.lib.query.NameQuery;
import us.zoom.cube.site.lib.query.PageQuery;
import us.zoom.infra.clickhouse.ChTable;
import us.zoom.infra.dao.model.*;
import us.zoom.infra.dao.service.CardDAO;
import us.zoom.infra.dao.service.DashCardRelaDAO;
import us.zoom.infra.utils.CommonSplitConstants;
import us.zoom.cube.lib.utils.JsonUtils;

import jakarta.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

/**
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class CardHandler {

    private static final String INFLUXDB_CLAUSE = "\"database\":{\"db\"";
    @Autowired
    private CardDAO cardDAO;

    @Autowired
    private DashCardRelaDAO dashCardRelaDAO;

    @Autowired
    private MetricsService metricsService;

    @Autowired
    private DashHandler dashHandler;

    public List<CardDO> searchCard(PageQuery<NameQuery> pageQuery) {
        return cardDAO.searchCardByName(getName(pageQuery), pageQuery.getPageSize() * (pageQuery.getPageIndex() - 1), pageQuery.getPageSize());
    }

    public Integer countCardByName(PageQuery<NameQuery> pageQuery) {
        return cardDAO.countCardByName(getName(pageQuery));

    }

    private String getName(PageQuery<NameQuery> pageQuery) {
        return (pageQuery == null || null == pageQuery.getQueryPara()) ? "" : pageQuery.getQueryPara().getName();
    }

    public List<CardDO> listCardByDash(String dashId) {
      return   cardDAO.listCardByDash(dashId);
    }



    public static final  String CARD_DB="db";
    public static final  String CARD_QUERYCFG="queryConfig";

    @Autowired
    private TenantHandler tenantHandler;
    @Transactional(rollbackFor = Exception.class)
    public String add(@Valid CardInput cardInput) {
        CardDO cardDO = new CardDO();
        BeanUtils.copyProperties(cardInput, cardDO);
        cardDO.setId(IdUtils.generateId());
        cardDO.setConfigs(JsonUtils.toJsonString(cardInput.getConfigs()));
        cardDO.setCreator(cardInput.getUserName());
        cardDO.setModifier(cardInput.getUserName());

        adjustData(cardDO);

        setTenantId(cardDO);

        cardDAO.add(cardDO);

        DashCardRelaDO relaDO = new DashCardRelaDO();
        relaDO.setCardId(cardDO.getId());
        relaDO.setDashId(cardInput.getDashId());
        relaDO.setId(IdUtils.generateId());
        dashCardRelaDAO.add(relaDO);

        return cardDO.getId();
    }


    /**
     * rescan the cards in a dash to rebuild the dash-tenant map
     *
     * @param cardDO
     */
    private void setTenantId(CardDO cardDO) {
        List<String> tenantIdsInCard = getTenantIdsInCard(cardDO.getConfigs());
        if (!CollectionUtils.isEmpty(tenantIdsInCard)) {
            cardDO.setTenantId(StringUtils.join(tenantIdsInCard, CommonSplitConstants.COMMA_SPLIT));
        } else {
            cardDO.setTenantId(null);
        }

    }

    public CardDO getById(String cardId) {
        return cardDAO.getById(cardId);
    }


    public String getDashIdByCardId(String cardId) {
        return cardDAO.getDashIdByCardId(cardId);
    }


    public void edit(@Valid CardInput cardInput) {
        Assert.notNull(cardInput.getId(), "id is null!");
        CardDO cardDO = new CardDO();
        BeanUtils.copyProperties(cardInput, cardDO);
        //setTenantId(cardDO);
        cardDO.setModifier(cardInput.getUserName());
        adjustData(cardDO);

        cardDAO.edit(cardDO);
    }

    private void adjustData(CardDO cardDO){
        if(StringUtils.isBlank(cardDO.getConfigs())){
            cardDO.setConfigs("");
        }

        if(StringUtils.isBlank(cardDO.getDescription())){
            cardDO.setDescription("");
        }

    }

    public void delete(String cardId) {
        cardDAO.delete(cardId);
        dashCardRelaDAO.delete(cardId);
    }

    public void batchDelete(List<String> cardIds) {
        if (CollectionUtils.isEmpty(cardIds)) {
            return;
        }

        cardDAO.batchDelete(cardIds);
        dashCardRelaDAO.batchDelete(cardIds);
    }

    public void editConfigs(CardInput cardInput) {
        Assert.notNull(cardInput.getId(), "id is null");
        Assert.notNull(cardInput.getConfigs(), "config is null");
        CardDO cardDO = new CardDO();
        BeanUtils.copyProperties(cardInput, cardDO);
        cardDO.setConfigs(JsonUtils.toJsonString(cardInput.getConfigs()));
        setTenantId(cardDO);
        cardDO.setModifier(cardInput.getUserName());

        cardDAO.editConfigs(cardDO);

    }

    public Map<String, String> batchAdd(BatchCardInput cardInputs) {
        Assert.notNull(cardInputs.getDashId(), "dashboard is null");
        Assert.notEmpty(cardInputs.getCardInputs(), "card is empty!");
        //Map<previousId, newId>
        Map<String, String> cardIds = new TreeMap<>();
        for (CardInput cardInput : cardInputs.getCardInputs()) {
            Assert.notNull(cardInput.getName(), "name is null");
            Assert.notNull(cardInput.getType(), "type is null");
            cardInput.checkNameAndDesc();
            cardInput.setDashId(cardInputs.getDashId());
            String previousId = cardInput.getId();
            String cardId = add(cardInput);
            cardIds.put(null == previousId ? cardId : previousId, cardId);
        }

        dashHandler.rescanDashTenant(cardInputs.getDashId());

        return cardIds;
    }

    @Transactional(rollbackFor = Exception.class)
    public HashMap<String,String> copyCard(String dashId,String newDashId,boolean needChangeDB,String serviceNameNew,String serviceNameOld) {
        List<DashCardRelaDO> dashCardRelaDOList = dashCardRelaDAO.getDashCardRelaByDashId(dashId);
        HashMap<String,String> idMap = new HashMap();
        if(!CollectionUtils.isEmpty(dashCardRelaDOList)){
            for (DashCardRelaDO dashCardRelaDO: dashCardRelaDOList){
                CardDO cardDO = cardDAO.getById(dashCardRelaDO.getCardId());
                CardInput cardInput = new CardInput();
                BeanUtils.copyProperties(cardDO,cardInput);
                setTenantId(cardDO);
                cardInput.setDashId(newDashId);
                if(!StringUtils.isEmpty(cardDO.getConfigs())){
                    cardInput.setConfigs(JsonUtils.toObject(cardDO.getConfigs(), Map.class));
                }
                if(StringUtils.isEmpty(serviceNameNew)){
                    needChangeDB=false;
                }
                if(needChangeDB==true){
                    changeCardSqlCfg(cardInput,serviceNameNew,serviceNameOld);
                }
                String id = add(cardInput);
                idMap.put(cardDO.getId(),id);
            }
        }

        dashHandler.rescanDashTenant(newDashId);

        return idMap;
    }

    public List<CardDO> getCardByDashId(String dashId) {
        return cardDAO.listCardByDash(dashId);
    }

    public void changeCardSqlCfg(CardInput cardInput,String serviceNameNew,String serviceNameOld){
        if(!CollectionUtils.isEmpty(cardInput.getConfigs())){
            Map cfgMap = cardInput.getConfigs();
            Map queryMap = (Map)cardInput.getConfigs().get("queryConfig");
            if(!CollectionUtils.isEmpty(queryMap)){
                queryMap.put("db",serviceNameNew);
                if(null != queryMap.get("rawText")){
                    StringBuffer sql = new StringBuffer();
                    sql.append(StringUtils.replace((String) queryMap.get("rawText"),serviceNameOld,serviceNameNew));
                    queryMap.put("rawText",(Object) sql);
                }
            }
            if(!CollectionUtils.isEmpty(queryMap)){
                cfgMap.put("queryConfig",queryMap);
            }
            cardInput.setConfigs(cfgMap);
        }
    }
    public List<DashCardRelaDO> getDashCardRelaByDashId(String dashId){
        return dashCardRelaDAO.getDashCardRelaByDashId(dashId);
    }

    public List<String> getTenantIdsInCard(String config) {
        List<String> tenantIds = new ArrayList<>();

        //dirty data with influxdb sql
        if (StringUtils.isBlank(config) || config.contains(INFLUXDB_CLAUSE)) {
            return tenantIds;
        }

        CardConfigs configs;
        try {
            configs = JsonUtils.toObject(config, CardConfigs.class);
        } catch (Exception e) {
            //JsonUtils will print the error msg
            return tenantIds;
        }
        if (configs == null) {
            return tenantIds;
        }
        CardQueryConfig queryConfig = configs.getQueryConfig();
        if (null == queryConfig) {
            return tenantIds;
        }


        String rawText = queryConfig.getRawText();
        // check if rawText is empty, if yes, use db or database
        if (StringUtils.isNotBlank(rawText)) {
            rawText = rawText.replace(CommonSplitConstants.COLON, CommonSplitConstants.UNDER_SCORE_SPLIT);
            List<ChTable> tables = metricsService.getTables(rawText);
            if (!CollectionUtils.isEmpty(tables)) {
                tenantIds = tables.stream().map(x -> x.getDbName()).map(x -> {
                    TenantDO tenantByName = tenantHandler.getTenantByName(x);
                    if (null != tenantByName) {
                        return tenantByName.getId();
                    }
                    return null;
                }).filter(x -> StringUtils.isNotBlank(x)).distinct().sorted().collect(Collectors.toList());
            }
        } else {
            String tenantName = StringUtils.isNotBlank(queryConfig.getDb()) ? queryConfig.getDb() : queryConfig.getDatabase();
            if (StringUtils.isBlank(tenantName)) {
                return tenantIds;
            }
            TenantDO tenant = tenantHandler.getTenantByName(tenantName);
            if (null != tenant) {
                tenantIds.add(tenant.getId());
            }
        }

        return tenantIds;

    }


    /**
     *
     * @deprecated  only be used in some special case
     * @return
     */
    public List<CardDO> listAllCard() {
        return cardDAO.listAllCard();
    }

    public List<String> listCardNameByDashId(String dashId) {
        return cardDAO.listCardNameByDashId(dashId);
    }

    public List<DashCardRelaDO> findIncorrectTenantIdDash(int pageIndex, int pageSize) {
        return cardDAO.findIncorrectTenantIdDash( pageIndex, pageSize);
    }
}
