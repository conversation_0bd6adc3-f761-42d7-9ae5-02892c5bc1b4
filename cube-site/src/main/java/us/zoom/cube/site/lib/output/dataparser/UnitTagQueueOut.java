package us.zoom.cube.site.lib.output.dataparser;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: luis.zheng
 * @Date: 2021/8/31 1:53
 */
public class UnitTagQueueOut {
    private String unitTagId;
    private String unitTagName;
    private List<DataParserQueueOut> queues = new ArrayList();

    public void addQueue(DataParserQueueOut out) {
        queues.add(out);
    }

    public String getUnitTagName() {
        return unitTagName;
    }

    public void setUnitTagName(String unitTagName) {
        this.unitTagName = unitTagName;
    }

    public String getUnitTagId() {
        return unitTagId;
    }

    public void setUnitTagId(String unitTagId) {
        this.unitTagId = unitTagId;
    }

    public List<DataParserQueueOut> getQueues() {
        return queues;
    }

    public void setQueues(List<DataParserQueueOut> queues) {
        this.queues = queues;
    }
}
