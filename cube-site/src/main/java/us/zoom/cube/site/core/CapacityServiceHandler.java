package us.zoom.cube.site.core;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import us.zoom.cube.site.lib.query.NameQuery;
import us.zoom.cube.site.lib.query.PageQuery;
import us.zoom.infra.dao.model.CapacityServiceDO;
import us.zoom.infra.dao.service.CapacityServiceDAO;

import jakarta.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-07-06 17:05
 */
@Component
public class CapacityServiceHandler {
    @Autowired
    private CapacityServiceDAO capacityServiceDAO;

    @Transactional(rollbackFor = Exception.class)
    public String add(@Valid CapacityServiceDO capacityServiceDO) {
        capacityServiceDAO.add(capacityServiceDO);
        return capacityServiceDO.getId();
    }

    @Transactional(rollbackFor = Exception.class)
    public void edit(@Valid CapacityServiceDO capacityServiceDO) {
        capacityServiceDAO.edit(capacityServiceDO.getId(),capacityServiceDO.getName(),capacityServiceDO.getNormal(),capacityServiceDO.getUpperThreshold(),capacityServiceDO.getLowerThreshold());
    }

    @Transactional(rollbackFor = Exception.class)
    public void delete(String id) {
        capacityServiceDAO.del(id);
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteByDashId(String id) {
        capacityServiceDAO.delBydashId(id);
    }


    public List<CapacityServiceDO> getServiceByDashId(String id) {
        List<CapacityServiceDO> capacityServiceDOList = capacityServiceDAO.getServiceByDashId(id);
        return capacityServiceDOList;
    }

    public List<CapacityServiceDO> searchService(PageQuery<NameQuery> pageQuery,String DashId) {
        return capacityServiceDAO.searchService(getName(pageQuery),pageQuery.getPageSize()*(pageQuery.getPageIndex()-1), pageQuery.getPageSize(),DashId);
    }

    public Integer getServiceCount(PageQuery<NameQuery> pageQuery, String DashId) {
        return capacityServiceDAO.getServiceCount(getName(pageQuery),pageQuery.getPageSize()*(pageQuery.getPageIndex()-1), pageQuery.getPageSize(),DashId);

    }

    private String getName(PageQuery<NameQuery> pageQuery) {
        return (pageQuery == null || null == pageQuery.getQueryPara()) ? "" : pageQuery.getQueryPara().getName();
    }

}
