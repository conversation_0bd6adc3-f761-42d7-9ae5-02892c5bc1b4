package us.zoom.cube.site.lib.output.network;

import lombok.Data;

@Data
public class PathNode {
    private String ip;
    private double avgLoss;
    private double avgLatency;
    private String color;


    public void setColorByLossAndLate() {
        if (avgLoss > 90) {
            color = "#FF0000";
        } else if (avgLoss >= 70 && avgLoss <= 90) {
            color = "#FFFF00";
        } else {
            color = "#00FF00";
        }
    }
}
