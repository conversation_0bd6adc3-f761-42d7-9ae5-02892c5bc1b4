package us.zoom.cube.site.lib.output.alarm.silence;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import us.zoom.cube.site.lib.output.alarm.group.AlarmGroupTagItemOutput;

import java.util.List;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AlarmRelatedSilenceOutput {
    Boolean exist;
    String silenceTimeZone;
    String startDateTimeStr;
    String endDateTimeStr;
    String comment;

    List<AlarmGroupTagItemOutput> groupTagItemList;
}
