package us.zoom.cube.site.infra.e2e;

import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import us.zoom.cloud.secrets.rotate.handler.RotateHandlerRegister;
import us.zoom.infra.utils.IpUtils;
import us.zoom.mq.client.AsyncMQ;
import us.zoom.mq.client.DefaultAsyncMQ;
import us.zoom.mq.client.clients.consumer.Consumer;
import us.zoom.mq.client.clients.consumer.RetryableStraw;
import us.zoom.mq.client.clients.producer.Producer;
import us.zoom.mq.client.pojo.Subscriber;
import us.zoom.mq.common.enums.ProtocolStrategy;

import java.util.ArrayList;
import java.util.List;

public class AsyncMqE2ESingleton {

    private static final Logger logger = LoggerFactory.getLogger(AsyncMqE2ESingleton.class.getName());

    private volatile static Consumer consumer;
    private volatile static AsyncMQ asyncMQ;
    private volatile static AsyncMqE2ESingleton instance;
    private volatile static List<Producer> producers = new ArrayList<>();
    private volatile static List<AsyncMQ> asyncMQS = new ArrayList<>();
    public static final String ASYNC_MQ_TYPE = "cube_probe_aggregation";

    private static int producerPoolSize = 20;

    private AsyncMqE2ESingleton() {
    }

    public static void init(String endpoint, String username, String password) {
        if (instance == null) {
            synchronized (AsyncMqE2ESingleton.class) {
                if (instance == null) {
                    logger.info("init AsyncMqSingleton start.");
                    if (StringUtils.isBlank(endpoint) || StringUtils.isBlank(username) || StringUtils.isBlank(password)) {
                        throw new RuntimeException("endpoint, username and password cannot be empty");
                    }
                    asyncMQ = new DefaultAsyncMQ(endpoint, username, password);
                    consumer = asyncMQ.consumer();
                    asyncMQS.add(asyncMQ);

                    logger.info("async producer size : " + producerPoolSize);

                    for (int i = 0; i < producerPoolSize; i++) {
                        AsyncMQ asyncMQ = new DefaultAsyncMQ(endpoint, username, password);
                        asyncMQ.setClientId("async-client-" + IpUtils.getLocalIP() + "-" + i);
                        Producer producer = asyncMQ.producer();
                        producer.setProtocolStrategy(ProtocolStrategy.SIMPLE);
                        producers.add(producer);
                        asyncMQS.add(asyncMQ);
                    }

                    instance = new AsyncMqE2ESingleton();
                    consumer.start();
                    logger.info("init AsyncMqSingleton success.");

                }
            }
        }
    }

    public Producer getProducer() {
        return producers.get(RandomUtils.nextInt(0, producers.size()));
    }


    public void shutdownProducer(String topic) {
        try {
            producers.forEach(producer -> {
                logger.info("begin shutdownProducer, topic:" + topic);
                producer.shutdown(topic);
                logger.info("shutdownProducer, topic:" + topic);
            });
        } catch (Exception e) {
            logger.error("shutdownProducer failed, topic:" + topic, e.getMessage());
        }
    }

    public static AsyncMqE2ESingleton getInstance() {
        return instance;
    }

    public void shutdownConsumer(String topic, String groupId) {
        try {
            consumer.shutdownCleanStartParam(consumer.registerSubscriber(topic, groupId));
            logger.info("shutdown consumer end, topic:{}, groupId:{}", topic, groupId);
        } catch (Exception e) {
            logger.error("shutdownConsumer failed, topic:{}, groupId:{}", e.getMessage(), topic, groupId);
        }
    }

    public void registerConsumer(String topic, String groupId, int receiveCount, RetryableStraw<byte[]> batchHandler) {
        Subscriber subscriber = consumer.registerSubscriber(topic, groupId);
        consumer.setStraw(subscriber, batchHandler);
        // With the Straw interface, the consumeCount setting is invalid
        consumer.start(subscriber, receiveCount, 1);
        logger.info("start consumer, topic:{}, group id:{}, receiveCount:{}", topic, groupId, receiveCount);
    }

    public void registerRotateHandler(String username, String secretKey) {
        RotateHandlerRegister.registerCurrentVersionHandler(List.of(secretKey), new ProbeCurrentVersionKVSecretHandler(asyncMQ, username));
    }


}
