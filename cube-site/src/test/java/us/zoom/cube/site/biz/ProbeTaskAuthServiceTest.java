package us.zoom.cube.site.biz;

import org.apache.commons.lang3.tuple.Pair;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import us.zoom.cube.lib.probe.BaseProbeStepCfg;
import us.zoom.cube.site.biz.syspara.E2EParaService;
import us.zoom.cube.site.core.ProbeTaskHandler;
import us.zoom.cube.site.lib.dto.probe.ProbeTaskDTO;
import us.zoom.cube.site.lib.input.ProbeTaskInput;
import us.zoom.cube.site.lib.probe.ProbeTaskConverter;

import java.util.List;

/**
 * @author: eason.jia
 * @date: 2024/1/18
 */
@ExtendWith(SpringExtension.class)
public class ProbeTaskAuthServiceTest {

    @InjectMocks
    private ProbeTaskAuthService probeTaskAuthService;
    @Mock
    private ProbeTaskHandler probeTaskHandler;
    @Mock
    private E2EParaService e2eParaService;
    @Spy
    private ProbeTaskConverter probeTaskConverter = new ProbeTaskConverter();

    @Test
    public void testNeedSensitiveTaskEditPermission_TurnOffApprovalSwitch() {
        Mockito.when(e2eParaService.getSensitiveTaskApprovalSwitch()).thenReturn(false);
        ProbeTaskInput probeTaskInput = buildCreateSensitiveTaskInput_ExecutableProgramWithoutGroovy();
        List<BaseProbeStepCfg> stepConfigs = probeTaskConverter.convertStepConfigs(probeTaskInput.getSteps());
        boolean needSensitiveTaskEditPermission = probeTaskAuthService.needSensitiveTaskEditPermission(probeTaskInput, stepConfigs);
        Assertions.assertFalse(needSensitiveTaskEditPermission);
    }

    @Test
    public void testNeedSensitiveTaskEditPermission_Create_ExecutableProgramTaskWithoutGroovy() {
        Mockito.when(e2eParaService.getSensitiveTaskApprovalSwitch()).thenReturn(true);
        ProbeTaskInput probeTaskInput = buildCreateSensitiveTaskInput_ExecutableProgramWithoutGroovy();
        List<BaseProbeStepCfg> stepConfigs = probeTaskConverter.convertStepConfigs(probeTaskInput.getSteps());
        boolean needSensitiveTaskEditPermission = probeTaskAuthService.needSensitiveTaskEditPermission(probeTaskInput, stepConfigs);
        Assertions.assertTrue(needSensitiveTaskEditPermission);
    }

    @Test
    public void testNeedSensitiveTaskEditPermission_Create_HttpTaskWithGroovy() {
        Mockito.when(e2eParaService.getSensitiveTaskApprovalSwitch()).thenReturn(true);
        ProbeTaskInput probeTaskInput = buildCreateSensitiveTaskInput_HttpWithGroovy();
        List<BaseProbeStepCfg> stepConfigs = probeTaskConverter.convertStepConfigs(probeTaskInput.getSteps());
        boolean needSensitiveTaskEditPermission = probeTaskAuthService.needSensitiveTaskEditPermission(probeTaskInput, stepConfigs);
        Assertions.assertTrue(needSensitiveTaskEditPermission);
    }

    @Test
    public void testNeedSensitiveTaskEditPermission_Create_HttpTaskWithoutGroovy() {
        Mockito.when(e2eParaService.getSensitiveTaskApprovalSwitch()).thenReturn(true);
        ProbeTaskInput probeTaskInput = buildCreateNonSensitiveTaskInput_HttpWithoutGroovy();
        List<BaseProbeStepCfg> stepConfigs = probeTaskConverter.convertStepConfigs(probeTaskInput.getSteps());
        boolean needSensitiveTaskEditPermission = probeTaskAuthService.needSensitiveTaskEditPermission(probeTaskInput, stepConfigs);
        Assertions.assertFalse(needSensitiveTaskEditPermission);
    }

    @Test
    public void testNeedSensitiveTaskEditPermission_Create_ExecutableProgramAndHttpSteps() {
        Mockito.when(e2eParaService.getSensitiveTaskApprovalSwitch()).thenReturn(true);
        ProbeTaskInput probeTaskInput = buildCreateSensitiveTaskInput_ExecutableProgramAndHttpSteps();
        List<BaseProbeStepCfg> stepConfigs = probeTaskConverter.convertStepConfigs(probeTaskInput.getSteps());
        boolean needSensitiveTaskEditPermission = probeTaskAuthService.needSensitiveTaskEditPermission(probeTaskInput, stepConfigs);
        Assertions.assertTrue(needSensitiveTaskEditPermission);
    }

    @Test
    public void testNeedSensitiveTaskEditPermission_Update_AddNonSensitiveStep() {
        Mockito.when(e2eParaService.getSensitiveTaskApprovalSwitch()).thenReturn(true);

        Pair<ProbeTaskDTO, ProbeTaskInput> pair = buildUpdateTaskInputPair_AddNonSensitiveStep();
        ProbeTaskDTO original = pair.getLeft();
        ProbeTaskInput updated = pair.getRight();

        Mockito.when(probeTaskHandler.getProbeTaskById(updated.getId())).thenReturn(original);

        List<BaseProbeStepCfg> stepConfigs = probeTaskConverter.convertStepConfigs(updated.getSteps());
        boolean needSensitiveTaskEditPermission = probeTaskAuthService.needSensitiveTaskEditPermission(updated, stepConfigs);
        Assertions.assertFalse(needSensitiveTaskEditPermission);
    }

    @Test
    public void testNeedSensitiveTaskEditPermission_Update_AddSensitiveStep() {
        Mockito.when(e2eParaService.getSensitiveTaskApprovalSwitch()).thenReturn(true);

        Pair<ProbeTaskDTO, ProbeTaskInput> pair = buildUpdateTaskInputPair_AddSensitiveStep();
        ProbeTaskDTO original = pair.getLeft();
        ProbeTaskInput updated = pair.getRight();

        Mockito.when(probeTaskHandler.getProbeTaskById(updated.getId())).thenReturn(original);

        List<BaseProbeStepCfg> stepConfigs = probeTaskConverter.convertStepConfigs(updated.getSteps());
        boolean needSensitiveTaskEditPermission = probeTaskAuthService.needSensitiveTaskEditPermission(updated, stepConfigs);
        Assertions.assertTrue(needSensitiveTaskEditPermission);
    }

    @Test
    public void testNeedSensitiveTaskEditPermission_Update_ModifyNonSensitiveStep() {
        Mockito.when(e2eParaService.getSensitiveTaskApprovalSwitch()).thenReturn(true);

        Pair<ProbeTaskDTO, ProbeTaskInput> pair = buildUpdateTaskInputPair_ModifyNonSensitiveStep();
        ProbeTaskDTO original = pair.getLeft();
        ProbeTaskInput updated = pair.getRight();

        Mockito.when(probeTaskHandler.getProbeTaskById(updated.getId())).thenReturn(original);

        List<BaseProbeStepCfg> stepConfigs = probeTaskConverter.convertStepConfigs(updated.getSteps());
        boolean needSensitiveTaskEditPermission = probeTaskAuthService.needSensitiveTaskEditPermission(updated, stepConfigs);
        Assertions.assertFalse(needSensitiveTaskEditPermission);
    }

    @Test
    public void testNeedSensitiveTaskEditPermission_Update_ModifySensitiveStep_NonSensitiveField() {
        Mockito.when(e2eParaService.getSensitiveTaskApprovalSwitch()).thenReturn(true);

        Pair<ProbeTaskDTO, ProbeTaskInput> pair = buildUpdateTaskInputPair_ModifySensitiveStep_NonSensitiveField();
        ProbeTaskDTO original = pair.getLeft();
        ProbeTaskInput updated = pair.getRight();

        Mockito.when(probeTaskHandler.getProbeTaskById(updated.getId())).thenReturn(original);

        List<BaseProbeStepCfg> stepConfigs = probeTaskConverter.convertStepConfigs(updated.getSteps());
        boolean needSensitiveTaskEditPermission = probeTaskAuthService.needSensitiveTaskEditPermission(updated, stepConfigs);
        Assertions.assertFalse(needSensitiveTaskEditPermission);
    }

    @Test
    public void testNeedSensitiveTaskEditPermission_Update_ModifySensitiveStep_SensitiveField() {
        Mockito.when(e2eParaService.getSensitiveTaskApprovalSwitch()).thenReturn(true);

        Pair<ProbeTaskDTO, ProbeTaskInput> pair = buildUpdateTaskInputPair_ModifySensitiveStep_SensitiveField();
        ProbeTaskDTO original = pair.getLeft();
        ProbeTaskInput updated = pair.getRight();

        Mockito.when(probeTaskHandler.getProbeTaskById(updated.getId())).thenReturn(original);

        List<BaseProbeStepCfg> stepConfigs = probeTaskConverter.convertStepConfigs(updated.getSteps());
        boolean needSensitiveTaskEditPermission = probeTaskAuthService.needSensitiveTaskEditPermission(updated, stepConfigs);
        Assertions.assertTrue(needSensitiveTaskEditPermission);
    }

    @Test
    public void testNeedSensitiveTaskEditPermission_Update_AddNonSensitiveStepWithDuplicateName() {
        Mockito.when(e2eParaService.getSensitiveTaskApprovalSwitch()).thenReturn(true);

        Pair<ProbeTaskDTO, ProbeTaskInput> pair = buildUpdateTaskInputPair_AddNonSensitiveStepWithDuplicateName();
        ProbeTaskDTO original = pair.getLeft();
        ProbeTaskInput updated = pair.getRight();

        Mockito.when(probeTaskHandler.getProbeTaskById(updated.getId())).thenReturn(original);

        List<BaseProbeStepCfg> stepConfigs = probeTaskConverter.convertStepConfigs(updated.getSteps());
        boolean needSensitiveTaskEditPermission = probeTaskAuthService.needSensitiveTaskEditPermission(updated, stepConfigs);
        Assertions.assertFalse(needSensitiveTaskEditPermission);
    }

    @Test
    public void testNeedSensitiveTaskEditPermission_Update_AddSensitiveStepWithDuplicateName() {
        Mockito.when(e2eParaService.getSensitiveTaskApprovalSwitch()).thenReturn(true);

        Pair<ProbeTaskDTO, ProbeTaskInput> pair = buildUpdateTaskInputPair_AddSensitiveStepWithDuplicateName();
        ProbeTaskDTO original = pair.getLeft();
        ProbeTaskInput updated = pair.getRight();

        Mockito.when(probeTaskHandler.getProbeTaskById(updated.getId())).thenReturn(original);

        List<BaseProbeStepCfg> stepConfigs = probeTaskConverter.convertStepConfigs(updated.getSteps());
        boolean needSensitiveTaskEditPermission = probeTaskAuthService.needSensitiveTaskEditPermission(updated, stepConfigs);
        Assertions.assertTrue(needSensitiveTaskEditPermission);
    }

    @Test
    public void testNeedSensitiveTaskEditPermission_Update_ModifySensitiveStepResource() {
        Mockito.when(e2eParaService.getSensitiveTaskApprovalSwitch()).thenReturn(true);

        Pair<ProbeTaskDTO, ProbeTaskInput> pair = buildUpdateTaskInputPair_ModifySensitiveStepResource();
        ProbeTaskDTO original = pair.getLeft();
        ProbeTaskInput updated = pair.getRight();

        Mockito.when(probeTaskHandler.getProbeTaskById(updated.getId())).thenReturn(original);

        List<BaseProbeStepCfg> stepConfigs = probeTaskConverter.convertStepConfigs(updated.getSteps());
        boolean needSensitiveTaskEditPermission = probeTaskAuthService.needSensitiveTaskEditPermission(updated, stepConfigs);
        Assertions.assertFalse(needSensitiveTaskEditPermission);
    }

    // ------------------ build test model ------------------
    private ProbeTaskInput buildCreateSensitiveTaskInput_ExecutableProgramWithoutGroovy() {
        ProbeTaskInput probeTaskInput = new ProbeTaskInput();
        probeTaskInput.setSteps("[{\"probeType\":\"executable_program\",\"name\":\"executable_package_eason_test\",\"responseParser\":\"\",\"customFields\":{},\"path\":\"s3://zoomdev-op/release/web_arch/cube-probe/script/cube-probe/cube-probe-test-*******65.jar\",\"storageType\":\"S3\",\"command\":\"java -cp cube-probe-test-*******65.jar us.zoom.cube.probe.NormalSuccess\",\"timeout\":10,\"runMode\":\"docker\",\"image\":\"java_17\",\"cpu\":200,\"memory\":200,\"url\":\"\",\"method\":\"get\",\"contentType\":\"\",\"jwtConfig\":{\"needJwtToken\":false}}]");
        return probeTaskInput;
    }

    private ProbeTaskInput buildCreateSensitiveTaskInput_HttpWithGroovy() {
        ProbeTaskInput probeTaskInput = new ProbeTaskInput();
        probeTaskInput.setSteps("[{\"probeType\":\"http\",\"name\":\"ZE_DEV_Yarn_monitor\",\"responseParser\":\"import groovy.json.JsonSlurper\\n\\n\\nstatic def parse(String message) {\\n\\n    // threshold\\n    def activeNodeLTThreshold = 5\\n    def unHealthyBThreshold = 3\\n\\n    def UNHEALTHY = \\\"UNHEALTHY\\\"\\n    def RUNNING = \\\"RUNNING\\\"\\n\\n    def jsonSlurper = new JsonSlurper()\\n\\n    Map<String, Object> messageMap = jsonSlurper.parseText(message) as Map<String, String>\\n\\n    def liveNodeManagers = jsonSlurper.parseText(messageMap.get(\\\"beans\\\").find().LiveNodeManagers) as List<String>\\n\\n    Map<String, List<String>> nodesStatus = new HashMap<String, List<String>>()\\n    nodesStatus.put(UNHEALTHY, [])\\n    nodesStatus.put(RUNNING, [])\\n\\n    liveNodeManagers.each { data ->\\n        if (data.State == UNHEALTHY) {\\n            nodesStatus.get(UNHEALTHY).add(data.HostName+\\\" is unthealthy\\\")\\n        } else if (data.State == RUNNING) {\\n            nodesStatus.get(RUNNING).add(data.HostName+\\\" is running\\\")\\n        }\\n    }\\n\\n    Map<String, Object> responseMap = new HashMap<String, Object>()\\n\\n    def isUnthealthy = nodesStatus.get(UNHEALTHY).size() > unHealthyBThreshold\\n    def isActive = nodesStatus.get(RUNNING).size() < activeNodeLTThreshold\\n\\n    if (isUnthealthy || isActive) {\\n        responseMap.status = 10\\n        responseMap.response = nodesStatus\\n\\n        def description = \\\"\\\"\\n        if (isUnthealthy) {\\n\\n            description+=\\\"Unthealthy nodes numbers is \\\"+nodesStatus.get(UNHEALTHY).size()+\\\" bigger than threshold \\\"+unHealthyBThreshold+\\\"!!!\\\"\\n        }\\n\\n        if(isActive){\\n            description+=\\\"Active Nodes \\\"+nodesStatus.get(RUNNING).size()+\\\" less than threshold \\\"+activeNodeLTThreshold+\\\"!!!\\\"\\n        }\\n\\n        responseMap.description = description\\n\\n    } else {\\n        responseMap.status = 1\\n    }\\n    return responseMap\\n\\n}\",\"customFields\":{\"description\":\"string\"},\"url\":\"https://ze-emr.zoomdev.us:8088/jmx?qry=Hadoop:service=ResourceManager,name=RMNMInfo\",\"method\":\"get\",\"contentType\":\"\",\"timeout\":10,\"headers\":{},\"body\":\"\",\"runMode\":\"docker\",\"image\":\"java_17\",\"storageType\":\"S3\",\"cpu\":200,\"memory\":200,\"jwtConfig\":{\"needJwtToken\":false}}]");
        return probeTaskInput;
    }

    private ProbeTaskInput buildCreateNonSensitiveTaskInput_HttpWithoutGroovy() {
        ProbeTaskInput probeTaskInput = new ProbeTaskInput();
        probeTaskInput.setSteps("[{\"probeType\":\"http\",\"name\":\"login_page\",\"responseParser\":\"\",\"customFields\":{},\"url\":\"https://zoom.us/signin#/login\",\"method\":\"head\",\"contentType\":\"\",\"timeout\":10,\"headers\":{},\"body\":\"\",\"runMode\":\"docker\",\"image\":\"java_17\",\"storageType\":\"S3\",\"cpu\":200,\"memory\":200}]");
        return probeTaskInput;
    }

    private ProbeTaskInput buildCreateSensitiveTaskInput_ExecutableProgramAndHttpSteps() {
        ProbeTaskInput probeTaskInput = new ProbeTaskInput();
        probeTaskInput.setSteps("[{\"probeType\":\"http\",\"name\":\"login_page\",\"responseParser\":\"\",\"customFields\":{},\"url\":\"https://zoom.us/signin#/login\",\"method\":\"head\",\"contentType\":\"\",\"timeout\":10,\"headers\":{},\"body\":\"\",\"runMode\":\"docker\",\"image\":\"java_17\",\"storageType\":\"S3\",\"cpu\":200,\"memory\":200}, {\"probeType\":\"executable_program\",\"name\":\"executable_package_eason_test\",\"responseParser\":\"\",\"customFields\":{},\"path\":\"s3://zoomdev-op/release/web_arch/cube-probe/script/cube-probe/cube-probe-test-*******65.jar\",\"storageType\":\"S3\",\"command\":\"java -cp cube-probe-test-*******65.jar us.zoom.cube.probe.NormalSuccess\",\"timeout\":10,\"runMode\":\"docker\",\"image\":\"java_17\",\"cpu\":200,\"memory\":200,\"url\":\"\",\"method\":\"get\",\"contentType\":\"\",\"jwtConfig\":{\"needJwtToken\":false}}]");
        return probeTaskInput;
    }

    private Pair<ProbeTaskDTO, ProbeTaskInput> buildUpdateTaskInputPair_AddNonSensitiveStep() {
        ProbeTaskDTO originalProbeTaskDTO = new ProbeTaskDTO();
        originalProbeTaskDTO.setId("123");
        originalProbeTaskDTO.setSteps("[{\"probeType\":\"http\",\"name\":\"login_page\",\"responseParser\":\"\",\"customFields\":{},\"url\":\"https://zoom.us/signin#/login\",\"method\":\"head\",\"contentType\":\"\",\"timeout\":10,\"headers\":{},\"body\":\"\",\"runMode\":\"docker\",\"image\":\"java_17\",\"storageType\":\"S3\",\"cpu\":200,\"memory\":200}, {\"probeType\":\"executable_program\",\"name\":\"executable_package_eason_test\",\"responseParser\":\"\",\"customFields\":{},\"path\":\"s3://zoomdev-op/release/web_arch/cube-probe/script/cube-probe/cube-probe-test-*******65.jar\",\"storageType\":\"S3\",\"command\":\"java -cp cube-probe-test-*******65.jar us.zoom.cube.probe.NormalSuccess\",\"timeout\":10,\"runMode\":\"docker\",\"image\":\"java_17\",\"cpu\":200,\"memory\":200,\"url\":\"\",\"method\":\"get\",\"contentType\":\"\",\"jwtConfig\":{\"needJwtToken\":false}}]");

        ProbeTaskInput updatedProbeTaskInput = new ProbeTaskInput();
        updatedProbeTaskInput.setId("123");
        updatedProbeTaskInput.setSteps("[{\"probeType\":\"http\",\"name\":\"login_page\",\"responseParser\":\"\",\"customFields\":{},\"url\":\"https://zoom.us/signin#/login\",\"method\":\"head\",\"contentType\":\"\",\"timeout\":10,\"headers\":{},\"body\":\"\",\"runMode\":\"docker\",\"image\":\"java_17\",\"storageType\":\"S3\",\"cpu\":200,\"memory\":200}, {\"probeType\":\"executable_program\",\"name\":\"executable_package_eason_test\",\"responseParser\":\"\",\"customFields\":{},\"path\":\"s3://zoomdev-op/release/web_arch/cube-probe/script/cube-probe/cube-probe-test-*******65.jar\",\"storageType\":\"S3\",\"command\":\"java -cp cube-probe-test-*******65.jar us.zoom.cube.probe.NormalSuccess\",\"timeout\":10,\"runMode\":\"docker\",\"image\":\"java_17\",\"cpu\":200,\"memory\":200,\"url\":\"\",\"method\":\"get\",\"contentType\":\"\",\"jwtConfig\":{\"needJwtToken\":false}},{\"probeType\":\"http\",\"name\":\"login_page_2\",\"responseParser\":\"\",\"customFields\":{},\"url\":\"https://zoom.us/signin#/login\",\"method\":\"head\",\"contentType\":\"\",\"timeout\":10,\"headers\":{},\"body\":\"\",\"runMode\":\"docker\",\"image\":\"java_17\",\"storageType\":\"S3\",\"cpu\":200,\"memory\":200}]");

        return Pair.of(originalProbeTaskDTO, updatedProbeTaskInput);
    }

    private Pair<ProbeTaskDTO, ProbeTaskInput> buildUpdateTaskInputPair_AddSensitiveStep() {
        ProbeTaskDTO originalProbeTaskDTO = new ProbeTaskDTO();
        originalProbeTaskDTO.setId("123");
        originalProbeTaskDTO.setSteps("[{\"probeType\":\"http\",\"name\":\"login_page\",\"responseParser\":\"\",\"customFields\":{},\"url\":\"https://zoom.us/signin#/login\",\"method\":\"head\",\"contentType\":\"\",\"timeout\":10,\"headers\":{},\"body\":\"\",\"runMode\":\"docker\",\"image\":\"java_17\",\"storageType\":\"S3\",\"cpu\":200,\"memory\":200}, {\"probeType\":\"executable_program\",\"name\":\"executable_package_eason_test\",\"responseParser\":\"\",\"customFields\":{},\"path\":\"s3://zoomdev-op/release/web_arch/cube-probe/script/cube-probe/cube-probe-test-*******65.jar\",\"storageType\":\"S3\",\"command\":\"java -cp cube-probe-test-*******65.jar us.zoom.cube.probe.NormalSuccess\",\"timeout\":10,\"runMode\":\"docker\",\"image\":\"java_17\",\"cpu\":200,\"memory\":200,\"url\":\"\",\"method\":\"get\",\"contentType\":\"\",\"jwtConfig\":{\"needJwtToken\":false}}]");

        ProbeTaskInput updatedProbeTaskInput = new ProbeTaskInput();
        updatedProbeTaskInput.setId("123");
        updatedProbeTaskInput.setSteps("[{\"probeType\":\"http\",\"name\":\"login_page\",\"responseParser\":\"\",\"customFields\":{},\"url\":\"https://zoom.us/signin#/login\",\"method\":\"head\",\"contentType\":\"\",\"timeout\":10,\"headers\":{},\"body\":\"\",\"runMode\":\"docker\",\"image\":\"java_17\",\"storageType\":\"S3\",\"cpu\":200,\"memory\":200}, {\"probeType\":\"executable_program\",\"name\":\"executable_package_eason_test\",\"responseParser\":\"\",\"customFields\":{},\"path\":\"s3://zoomdev-op/release/web_arch/cube-probe/script/cube-probe/cube-probe-test-*******65.jar\",\"storageType\":\"S3\",\"command\":\"java -cp cube-probe-test-*******65.jar us.zoom.cube.probe.NormalSuccess\",\"timeout\":10,\"runMode\":\"docker\",\"image\":\"java_17\",\"cpu\":200,\"memory\":200,\"url\":\"\",\"method\":\"get\",\"contentType\":\"\",\"jwtConfig\":{\"needJwtToken\":false}}, {\"probeType\":\"executable_program\",\"name\":\"executable_package_eason_test_2\",\"responseParser\":\"\",\"customFields\":{},\"path\":\"s3://zoomdev-op/release/web_arch/cube-probe/script/cube-probe/cube-probe-test-*******65.jar\",\"storageType\":\"S3\",\"command\":\"java -cp cube-probe-test-*******65.jar us.zoom.cube.probe.NormalSuccess\",\"timeout\":10,\"runMode\":\"docker\",\"image\":\"java_17\",\"cpu\":200,\"memory\":200,\"url\":\"\",\"method\":\"get\",\"contentType\":\"\",\"jwtConfig\":{\"needJwtToken\":false}}]");

        return Pair.of(originalProbeTaskDTO, updatedProbeTaskInput);
    }

    private Pair<ProbeTaskDTO, ProbeTaskInput> buildUpdateTaskInputPair_ModifyNonSensitiveStep() {
        ProbeTaskDTO originalProbeTaskDTO = new ProbeTaskDTO();
        originalProbeTaskDTO.setId("123");
        originalProbeTaskDTO.setSteps("[{\"probeType\":\"http\",\"name\":\"login_page\",\"responseParser\":\"\",\"customFields\":{},\"url\":\"https://zoom.us/signin#/login\",\"method\":\"head\",\"contentType\":\"\",\"timeout\":10,\"headers\":{},\"body\":\"\",\"runMode\":\"docker\",\"image\":\"java_17\",\"storageType\":\"S3\",\"cpu\":200,\"memory\":200}, {\"probeType\":\"executable_program\",\"name\":\"executable_package_eason_test\",\"responseParser\":\"\",\"customFields\":{},\"path\":\"s3://zoomdev-op/release/web_arch/cube-probe/script/cube-probe/cube-probe-test-*******65.jar\",\"storageType\":\"S3\",\"command\":\"java -cp cube-probe-test-*******65.jar us.zoom.cube.probe.NormalSuccess\",\"timeout\":10,\"runMode\":\"docker\",\"image\":\"java_17\",\"cpu\":200,\"memory\":200,\"url\":\"\",\"method\":\"get\",\"contentType\":\"\",\"jwtConfig\":{\"needJwtToken\":false}}]");

        ProbeTaskInput updatedProbeTaskInput = new ProbeTaskInput();
        updatedProbeTaskInput.setId("123");
        updatedProbeTaskInput.setSteps("[{\"probeType\":\"http\",\"name\":\"login_page\",\"responseParser\":\"\",\"customFields\":{},\"url\":\"https://zoom.us/signin#/login222\",\"method\":\"head\",\"contentType\":\"\",\"timeout\":10,\"headers\":{},\"body\":\"\",\"runMode\":\"docker\",\"image\":\"java_17\",\"storageType\":\"S3\",\"cpu\":200,\"memory\":200}, {\"probeType\":\"executable_program\",\"name\":\"executable_package_eason_test\",\"responseParser\":\"\",\"customFields\":{},\"path\":\"s3://zoomdev-op/release/web_arch/cube-probe/script/cube-probe/cube-probe-test-*******65.jar\",\"storageType\":\"S3\",\"command\":\"java -cp cube-probe-test-*******65.jar us.zoom.cube.probe.NormalSuccess\",\"timeout\":10,\"runMode\":\"docker\",\"image\":\"java_17\",\"cpu\":200,\"memory\":200,\"url\":\"\",\"method\":\"get\",\"contentType\":\"\",\"jwtConfig\":{\"needJwtToken\":false}}]");

        return Pair.of(originalProbeTaskDTO, updatedProbeTaskInput);
    }

    private Pair<ProbeTaskDTO, ProbeTaskInput> buildUpdateTaskInputPair_ModifySensitiveStep_NonSensitiveField() {
        ProbeTaskDTO originalProbeTaskDTO = new ProbeTaskDTO();
        originalProbeTaskDTO.setId("123");
        originalProbeTaskDTO.setSteps("[{\"probeType\":\"http\",\"name\":\"login_page\",\"responseParser\":\"\",\"customFields\":{},\"url\":\"https://zoom.us/signin#/login\",\"method\":\"head\",\"contentType\":\"\",\"timeout\":10,\"headers\":{},\"body\":\"\",\"runMode\":\"docker\",\"image\":\"java_17\",\"storageType\":\"S3\",\"cpu\":200,\"memory\":200}, {\"probeType\":\"executable_program\",\"name\":\"executable_package_eason_test\",\"responseParser\":\"\",\"customFields\":{},\"path\":\"s3://zoomdev-op/release/web_arch/cube-probe/script/cube-probe/cube-probe-test-*******65.jar\",\"storageType\":\"S3\",\"command\":\"java -cp cube-probe-test-*******65.jar us.zoom.cube.probe.NormalSuccess\",\"timeout\":10,\"runMode\":\"docker\",\"image\":\"java_17\",\"cpu\":200,\"memory\":200,\"url\":\"\",\"method\":\"get\",\"contentType\":\"\",\"jwtConfig\":{\"needJwtToken\":false}}]");

        ProbeTaskInput updatedProbeTaskInput = new ProbeTaskInput();
        updatedProbeTaskInput.setId("123");
        updatedProbeTaskInput.setSteps("[{\"probeType\":\"http\",\"name\":\"login_page\",\"responseParser\":\"\",\"customFields\":{},\"url\":\"https://zoom.us/signin#/login\",\"method\":\"head\",\"contentType\":\"\",\"timeout\":10,\"headers\":{},\"body\":\"\",\"runMode\":\"docker\",\"image\":\"java_17\",\"storageType\":\"S3\",\"cpu\":200,\"memory\":200}, {\"probeType\":\"executable_program\",\"name\":\"executable_package_eason_test\",\"responseParser\":\"\",\"customFields\":{},\"path\":\"s3://zoomdev-op/release/web_arch/cube-probe/script/cube-probe/cube-probe-test-*******65.jar\",\"storageType\":\"S3\",\"command\":\"java -cp cube-probe-test-*******65.jar us.zoom.cube.probe.NormalSuccess\",\"timeout\":100,\"runMode\":\"docker\",\"image\":\"java_17\",\"cpu\":200,\"memory\":200,\"url\":\"\",\"method\":\"get\",\"contentType\":\"\",\"jwtConfig\":{\"needJwtToken\":false}}]");

        return Pair.of(originalProbeTaskDTO, updatedProbeTaskInput);
    }

    private Pair<ProbeTaskDTO, ProbeTaskInput> buildUpdateTaskInputPair_ModifySensitiveStep_SensitiveField() {
        ProbeTaskDTO originalProbeTaskDTO = new ProbeTaskDTO();
        originalProbeTaskDTO.setId("123");
        originalProbeTaskDTO.setSteps("[{\"probeType\":\"http\",\"name\":\"login_page\",\"responseParser\":\"\",\"customFields\":{},\"url\":\"https://zoom.us/signin#/login\",\"method\":\"head\",\"contentType\":\"\",\"timeout\":10,\"headers\":{},\"body\":\"\",\"runMode\":\"docker\",\"image\":\"java_17\",\"storageType\":\"S3\",\"cpu\":200,\"memory\":200}, {\"probeType\":\"executable_program\",\"name\":\"executable_package_eason_test\",\"responseParser\":\"\",\"customFields\":{},\"path\":\"s3://zoomdev-op/release/web_arch/cube-probe/script/cube-probe/cube-probe-test-*******65.jar\",\"storageType\":\"S3\",\"command\":\"java -cp cube-probe-test-*******65.jar us.zoom.cube.probe.NormalSuccess\",\"timeout\":10,\"runMode\":\"docker\",\"image\":\"java_17\",\"cpu\":200,\"memory\":200,\"url\":\"\",\"method\":\"get\",\"contentType\":\"\",\"jwtConfig\":{\"needJwtToken\":false}}]");

        ProbeTaskInput updatedProbeTaskInput = new ProbeTaskInput();
        updatedProbeTaskInput.setId("123");
        updatedProbeTaskInput.setSteps("[{\"probeType\":\"http\",\"name\":\"login_page\",\"responseParser\":\"\",\"customFields\":{},\"url\":\"https://zoom.us/signin#/login\",\"method\":\"head\",\"contentType\":\"\",\"timeout\":10,\"headers\":{},\"body\":\"\",\"runMode\":\"docker\",\"image\":\"java_17\",\"storageType\":\"S3\",\"cpu\":200,\"memory\":200}, {\"probeType\":\"executable_program\",\"name\":\"executable_package_eason_test\",\"responseParser\":\"\",\"customFields\":{},\"path\":\"s3://zoomdev-op/release/web_arch/cube-probe/script/cube-probe/cube-probe-test-*******66.jar\",\"storageType\":\"S3\",\"command\":\"java -cp cube-probe-test-*******66.jar us.zoom.cube.probe.NormalSuccess\",\"timeout\":10,\"runMode\":\"docker\",\"image\":\"java_17\",\"cpu\":200,\"memory\":200,\"url\":\"\",\"method\":\"get\",\"contentType\":\"\",\"jwtConfig\":{\"needJwtToken\":false}}]");

        return Pair.of(originalProbeTaskDTO, updatedProbeTaskInput);
    }

    private Pair<ProbeTaskDTO, ProbeTaskInput> buildUpdateTaskInputPair_AddNonSensitiveStepWithDuplicateName() {
        ProbeTaskDTO originalProbeTaskDTO = new ProbeTaskDTO();
        originalProbeTaskDTO.setId("123");
        originalProbeTaskDTO.setSteps("[{\"probeType\":\"http\",\"name\":\"login_page\",\"responseParser\":\"\",\"customFields\":{},\"url\":\"https://zoom.us/signin#/login\",\"method\":\"head\",\"contentType\":\"\",\"timeout\":10,\"headers\":{},\"body\":\"\",\"runMode\":\"docker\",\"image\":\"java_17\",\"storageType\":\"S3\",\"cpu\":200,\"memory\":200}, {\"probeType\":\"executable_program\",\"name\":\"executable_package_eason_test\",\"responseParser\":\"\",\"customFields\":{},\"path\":\"s3://zoomdev-op/release/web_arch/cube-probe/script/cube-probe/cube-probe-test-*******65.jar\",\"storageType\":\"S3\",\"command\":\"java -cp cube-probe-test-*******65.jar us.zoom.cube.probe.NormalSuccess\",\"timeout\":10,\"runMode\":\"docker\",\"image\":\"java_17\",\"cpu\":200,\"memory\":200,\"url\":\"\",\"method\":\"get\",\"contentType\":\"\",\"jwtConfig\":{\"needJwtToken\":false}}]");

        ProbeTaskInput updatedProbeTaskInput = new ProbeTaskInput();
        updatedProbeTaskInput.setId("123");
        updatedProbeTaskInput.setSteps("[{\"probeType\":\"http\",\"name\":\"login_page\",\"responseParser\":\"\",\"customFields\":{},\"url\":\"https://zoom.us/signin#/login\",\"method\":\"head\",\"contentType\":\"\",\"timeout\":10,\"headers\":{},\"body\":\"\",\"runMode\":\"docker\",\"image\":\"java_17\",\"storageType\":\"S3\",\"cpu\":200,\"memory\":200}, {\"probeType\":\"executable_program\",\"name\":\"executable_package_eason_test\",\"responseParser\":\"\",\"customFields\":{},\"path\":\"s3://zoomdev-op/release/web_arch/cube-probe/script/cube-probe/cube-probe-test-*******65.jar\",\"storageType\":\"S3\",\"command\":\"java -cp cube-probe-test-*******65.jar us.zoom.cube.probe.NormalSuccess\",\"timeout\":10,\"runMode\":\"docker\",\"image\":\"java_17\",\"cpu\":200,\"memory\":200,\"url\":\"\",\"method\":\"get\",\"contentType\":\"\",\"jwtConfig\":{\"needJwtToken\":false}},{\"probeType\":\"http\",\"name\":\"login_page\",\"responseParser\":\"\",\"customFields\":{},\"url\":\"https://zoom.us/signin#/login2\",\"method\":\"head\",\"contentType\":\"\",\"timeout\":10,\"headers\":{},\"body\":\"\",\"runMode\":\"docker\",\"image\":\"java_17\",\"storageType\":\"S3\",\"cpu\":200,\"memory\":200}]");

        return Pair.of(originalProbeTaskDTO, updatedProbeTaskInput);
    }

    private Pair<ProbeTaskDTO, ProbeTaskInput> buildUpdateTaskInputPair_AddSensitiveStepWithDuplicateName() {
        ProbeTaskDTO originalProbeTaskDTO = new ProbeTaskDTO();
        originalProbeTaskDTO.setId("123");
        originalProbeTaskDTO.setSteps("[{\"probeType\":\"http\",\"name\":\"login_page\",\"responseParser\":\"\",\"customFields\":{},\"url\":\"https://zoom.us/signin#/login\",\"method\":\"head\",\"contentType\":\"\",\"timeout\":10,\"headers\":{},\"body\":\"\",\"runMode\":\"docker\",\"image\":\"java_17\",\"storageType\":\"S3\",\"cpu\":200,\"memory\":200}, {\"probeType\":\"executable_program\",\"name\":\"executable_package_eason_test\",\"responseParser\":\"\",\"customFields\":{},\"path\":\"s3://zoomdev-op/release/web_arch/cube-probe/script/cube-probe/cube-probe-test-*******65.jar\",\"storageType\":\"S3\",\"command\":\"java -cp cube-probe-test-*******65.jar us.zoom.cube.probe.NormalSuccess\",\"timeout\":10,\"runMode\":\"docker\",\"image\":\"java_17\",\"cpu\":200,\"memory\":200,\"url\":\"\",\"method\":\"get\",\"contentType\":\"\",\"jwtConfig\":{\"needJwtToken\":false}}]");

        ProbeTaskInput updatedProbeTaskInput = new ProbeTaskInput();
        updatedProbeTaskInput.setId("123");
        updatedProbeTaskInput.setSteps("[{\"probeType\":\"http\",\"name\":\"login_page\",\"responseParser\":\"\",\"customFields\":{},\"url\":\"https://zoom.us/signin#/login\",\"method\":\"head\",\"contentType\":\"\",\"timeout\":10,\"headers\":{},\"body\":\"\",\"runMode\":\"docker\",\"image\":\"java_17\",\"storageType\":\"S3\",\"cpu\":200,\"memory\":200}, {\"probeType\":\"executable_program\",\"name\":\"executable_package_eason_test\",\"responseParser\":\"\",\"customFields\":{},\"path\":\"s3://zoomdev-op/release/web_arch/cube-probe/script/cube-probe/cube-probe-test-*******65.jar\",\"storageType\":\"S3\",\"command\":\"java -cp cube-probe-test-*******65.jar us.zoom.cube.probe.NormalSuccess\",\"timeout\":10,\"runMode\":\"docker\",\"image\":\"java_17\",\"cpu\":200,\"memory\":200,\"url\":\"\",\"method\":\"get\",\"contentType\":\"\",\"jwtConfig\":{\"needJwtToken\":false}}, {\"probeType\":\"executable_program\",\"name\":\"executable_package_eason_test\",\"responseParser\":\"\",\"customFields\":{},\"path\":\"s3://zoomdev-op/release/web_arch/cube-probe/script/cube-probe/cube-probe-test-*******66.jar\",\"storageType\":\"S3\",\"command\":\"java -cp cube-probe-test-*******66.jar us.zoom.cube.probe.NormalSuccess\",\"timeout\":10,\"runMode\":\"docker\",\"image\":\"java_17\",\"cpu\":200,\"memory\":200,\"url\":\"\",\"method\":\"get\",\"contentType\":\"\",\"jwtConfig\":{\"needJwtToken\":false}}]");

        return Pair.of(originalProbeTaskDTO, updatedProbeTaskInput);
    }

    private Pair<ProbeTaskDTO, ProbeTaskInput> buildUpdateTaskInputPair_ModifySensitiveStepResource() {
        ProbeTaskDTO originalProbeTaskDTO = new ProbeTaskDTO();
        originalProbeTaskDTO.setId("123");
        originalProbeTaskDTO.setSteps("[{\"probeType\":\"http\",\"name\":\"login_page\",\"responseParser\":\"\",\"customFields\":{},\"url\":\"https://zoom.us/signin#/login\",\"method\":\"head\",\"contentType\":\"\",\"timeout\":10,\"headers\":{},\"body\":\"\",\"runMode\":\"docker\",\"image\":\"java_17\",\"storageType\":\"S3\",\"cpu\":200,\"memory\":200}, {\"probeType\":\"executable_program\",\"name\":\"executable_package_eason_test\",\"responseParser\":\"\",\"customFields\":{},\"path\":\"s3://zoomdev-op/release/web_arch/cube-probe/script/cube-probe/cube-probe-test-*******65.jar\",\"storageType\":\"S3\",\"command\":\"java -cp cube-probe-test-*******65.jar us.zoom.cube.probe.NormalSuccess\",\"timeout\":10,\"runMode\":\"docker\",\"image\":\"java_17\",\"cpu\":200,\"memory\":200,\"url\":\"\",\"method\":\"get\",\"contentType\":\"\",\"jwtConfig\":{\"needJwtToken\":false}}]");

        ProbeTaskInput updatedProbeTaskInput = new ProbeTaskInput();
        updatedProbeTaskInput.setId("123");
        updatedProbeTaskInput.setSteps("[{\"probeType\":\"http\",\"name\":\"login_page\",\"responseParser\":\"\",\"customFields\":{},\"url\":\"https://zoom.us/signin#/login\",\"method\":\"head\",\"contentType\":\"\",\"timeout\":10,\"headers\":{},\"body\":\"\",\"runMode\":\"docker\",\"image\":\"java_17\",\"storageType\":\"S3\",\"cpu\":200,\"memory\":200}, {\"probeType\":\"executable_program\",\"name\":\"executable_package_eason_test\",\"responseParser\":\"\",\"customFields\":{},\"path\":\"s3://zoomdev-op/release/web_arch/cube-probe/script/cube-probe/cube-probe-test-*******65.jar\",\"storageType\":\"S3\",\"command\":\"java -cp cube-probe-test-*******65.jar us.zoom.cube.probe.NormalSuccess\",\"timeout\":10,\"runMode\":\"docker\",\"image\":\"java_17\",\"cpu\":400,\"memory\":500,\"url\":\"\",\"method\":\"get\",\"contentType\":\"\",\"jwtConfig\":{\"needJwtToken\":false}}]");

        return Pair.of(originalProbeTaskDTO, updatedProbeTaskInput);
    }
}