package us.zoom.cube.site.external.metadata.enums;

public enum ServiceStatusEnum {
    ACTIVE("active"),
    RETIRE("retire");

    private final String value;

    ServiceStatusEnum(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public static ServiceStatusEnum fromValue(String value) {
        if (value == null) {
            return null;
        }
        for (ServiceStatusEnum status : ServiceStatusEnum.values()) {
            if (status.getValue().equals(value)) {
                return status;
            }
        }
        return null;
    }
} 