package us.zoom.cube.site.infra.utils;

import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import us.zoom.async.mq.openapi.model.result.topic.TopicResult;
import us.zoom.cube.lib.common.AsyncMqQueueTopicStatusEnum;

import java.util.List;
import java.util.Optional;
import java.util.stream.IntStream;

import static us.zoom.cube.lib.common.CubeConstants.ASYNC_MQ_MAX_PAGE_SIZE;

/**
 * <AUTHOR>
 */
public class AsyncMqUtils {


    public static List<List<String>> splitIntoMultipleBatches(List<String> topicList) {
        if (CollectionUtils.isEmpty(topicList)) {
            return Lists.newArrayList();
        }
        return Lists.partition(topicList, ASYNC_MQ_MAX_PAGE_SIZE);

    }

    public static boolean isChanged(Integer topicStatus, String oldDcName, String oldKafkaClusterName, String oldTopicId, Integer oldPartition,
                                    TopicResult topicResult) {

        AsyncMqQueueTopicStatusEnum topicStatusEnum = AsyncMqQueueTopicStatusEnum.fromType(topicStatus);

        if (topicResult == null) {
            return !AsyncMqQueueTopicStatusEnum.notExist.equals(topicStatusEnum);
        }

        AsyncMqQueueTopicStatusEnum asyncMqTopicStatus = AsyncMqQueueTopicStatusEnum.fromEnabledStatus(topicResult.getEnabled());

        return !asyncMqTopicStatus.equals(topicStatusEnum) ||
                !StringUtils.equals(oldDcName, topicResult.getDcName()) || !StringUtils.equals(oldKafkaClusterName, topicResult.getClusterName()) ||
                !StringUtils.equals(oldTopicId, topicResult.getId()) || !topicResult.getPartitions().equals(oldPartition);
    }


}
