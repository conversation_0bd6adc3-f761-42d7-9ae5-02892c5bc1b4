package us.zoom.cube.site.lib.output.network;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.*;

@Data
public class DestinationDetail {
    @JsonProperty("sDc")
    private String sDc;

    @JsonProperty("sServiceIpMap")
    private Map<String, Set<String>> sServiceIpRela = new LinkedHashMap<>();

    @JsonProperty("dDc")
    private String dDc;

    @JsonProperty("dServiceIpMap")
    private Map<String, Set<String>> dServiceIpRela = new LinkedHashMap<>();

    private int failCount;
    private int successCount;
    private double successRate;
    private double lossP90;
    private double latencyP90;
    private double latencyWrst;
    private String color;

    public void setColorBySuccessRate() {
        if (successRate > 90) {
            color = "#00FF00";
        } else if (successRate >= 70 && successRate <= 90) {
            color = "#FFFF00";
        } else {
            color = "#FF0000";
        }
    }
}
