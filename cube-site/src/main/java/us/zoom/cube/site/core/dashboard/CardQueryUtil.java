package us.zoom.cube.site.core.dashboard;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.util.*;

public class CardQueryUtil {

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    @SuppressWarnings("unchecked")
    public static Map<String, Object> transCardQueryConfigsToSet(String configStr) {
        try {
            Map<String, Object> configs = OBJECT_MAPPER.readValue(
                    configStr, new TypeReference<Map<String, Object>>() {
                    }
            );

            Map<String, Object> queryConfig =
                    (Map<String, Object>) configs.getOrDefault("queryConfig", new LinkedHashMap<>());

            String type = asString(configs.get("type"));
            if ("row".equals(type)) {
                return configs;
            }

            String rawText = asString(queryConfig.get("rawText"));

            Object visualConfigs = configs.containsKey("visualConfigs")
                    ? configs.get("visualConfigs")
                    : queryConfig.get("visualConfigs");

            Map<String, Object> newQueryConfig = new LinkedHashMap<>();
            newQueryConfig.put("dbType", "clickhouse");
            newQueryConfig.put("useClickHouse", true);
            newQueryConfig.put("groupByTime", defaultIfBlank(asString(queryConfig.get("groupByTime")), "auto"));
            newQueryConfig.put("compareTime", defaultIfBlank(asString(queryConfig.get("compareTime")), "none"));
            newQueryConfig.put("fill", queryConfig.get("fill"));
            newQueryConfig.put("db", queryConfig.get("db"));
            newQueryConfig.put("measurement", queryConfig.get("measurement"));
            newQueryConfig.put("rp", queryConfig.get("rp"));

            // fields -> Set
            newQueryConfig.put("fields", toLinkedHashSet(queryConfig.get("fields")));

            // fieldsAlias -> Map (default {})
            Map<String, Object> fieldsAlias =
                    (Map<String, Object>) queryConfig.getOrDefault("fieldsAlias", new LinkedHashMap<>());
            newQueryConfig.put("fieldsAlias", fieldsAlias);

            // functionSelectors: if value is List -> convert to Map<item, empty List>
            Object functionSelectorsObj = queryConfig.get("functionSelectors");
            Map<String, Object> functionSelectors = new LinkedHashMap<>();
            if (functionSelectorsObj instanceof Map<?, ?> fsMapRaw) {
                for (Map.Entry<?, ?> entry : ((Map<?, ?>) functionSelectorsObj).entrySet()) {
                    String key = String.valueOf(entry.getKey());
                    Object value = entry.getValue();
                    if (value instanceof List<?> list) {
                        Map<String, List<Object>> converted = new LinkedHashMap<>();
                        for (Object item : list) {
                            converted.put(String.valueOf(item), new ArrayList<>());
                        }
                        functionSelectors.put(key, converted);
                    } else {
                        functionSelectors.put(key, value);
                    }
                }
            }
            newQueryConfig.put("functionSelectors", functionSelectors);

            // groupByTagKey -> Set
            newQueryConfig.put("groupByTagKey", toLinkedHashSet(queryConfig.get("groupByTagKey")));

            // tagValues: Map<String, Set<String>>
            Map<String, Object> tagValuesConverted = new LinkedHashMap<>();
            Object tagValuesObj = queryConfig.get("tagValues");
            if (tagValuesObj instanceof Map<?, ?> tagValues) {
                for (Map.Entry<?, ?> entry : ((Map<?, ?>) tagValuesObj).entrySet()) {
                    String key = String.valueOf(entry.getKey());
                    tagValuesConverted.put(key, toLinkedHashSet(entry.getValue()));
                }
            }
            newQueryConfig.put("tagValues", tagValuesConverted);

            newQueryConfig.put("rawText", queryConfig.get("rawText"));
            newQueryConfig.put("visualConfigs", visualConfigs);
            newQueryConfig.put("buckets", queryConfig.get("buckets"));
            newQueryConfig.put("snippet", queryConfig.get("snippet"));

            Map<String, Object> ret = new LinkedHashMap<>();
            ret.put("queryConfig", newQueryConfig);
            ret.put("gridPos", configs.getOrDefault("gridPos", defaultGridPos()));
            ret.put("type", type);
            ret.put("defaultCollapsed", configs.get("defaultCollapsed"));
            ret.put("visualConfigs", visualConfigs);

            boolean batch = asBoolean(configs.get("batch"));
            if (batch && (rawText == null || rawText.isEmpty())) {
                // Implement this according to your TS logic if needed
                ret.put(
                        "queryConfig",
                        transCardQueryConfigsForBatchAddCard(newQueryConfig, queryConfig)
                );
            }

            if (configs.containsKey("refreshConfig")) {
                ret.put("refreshConfig", configs.get("refreshConfig"));
            }

            return ret;
        } catch (Exception e) {
            throw new RuntimeException("Failed to parse or transform card configs", e);
        }
    }

    // Stub: implement detailed logic if batch-creation needs transformation like TS version
    private static Map<String, Object> transCardQueryConfigsForBatchAddCard(
            Map<String, Object> queryConfigs,
            Map<String, Object> originalQueryConfigs
    ) {
        return queryConfigs;
    }

    private static Map<String, Object> defaultGridPos() {
        Map<String, Object> grid = new LinkedHashMap<>();
        grid.put("x", 0);
        grid.put("y", 0);
        grid.put("h", 8);
        grid.put("w", 6);
        return grid;
    }

    @SuppressWarnings("unchecked")
    private static Set<String> toLinkedHashSet(Object value) {
        if (value instanceof Set<?> set) {
            LinkedHashSet<String> out = new LinkedHashSet<>();
            for (Object v : set) out.add(String.valueOf(v));
            return out;
        }
        if (value instanceof List<?> list) {
            LinkedHashSet<String> out = new LinkedHashSet<>();
            for (Object v : list) out.add(String.valueOf(v));
            return out;
        }
        return new LinkedHashSet<>();
    }

    private static String asString(Object v) {
        return v == null ? null : String.valueOf(v);
    }

    private static boolean asBoolean(Object v) {
        if (v instanceof Boolean b) return b;
        if (v instanceof String s) return Boolean.parseBoolean(s);
        if (v instanceof Number n) return n.intValue() != 0;
        return false;
    }

    private static String defaultIfBlank(String v, String def) {
        return (v == null || v.isEmpty()) ? def : v;
    }
}