package us.zoom.cube.site.infra.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * from command-center team
 */
public enum CommandCenterCategoryType {

    ASYNC_SEARCH_FAILOVER("ASF", "Async Search Failover"),
    ASYNC_FAILOVER("ASYNC_WF", "Async Failover"),
    AWS_SERVICE_FAILOVER("AWSF", "AWS Service Failover"),

    CC_FAILOVER("CCFAILOVER", "CC Failover"),
    CCI_SERVICE_FAILOVER("CSF", "CCI Service Failover"),
    CDN_FAILOVER("CDN", "CDN Failover"),
    CIDR_RULE("CIDR", "CIDR Rule"),
    COMMAND_UPDATE("COMMANDCHANGE", "Command Own Op"),
    DB_PATCH("DBPATCH", "DB PATCH"),
    DEDICATED_NUMBER("D_NUM", "Dedicated Numbers"),
    DYNAMIC_FAILOVER("DF", "Dynamic Failover"),
    DEVOPS("DEVOPS", "Devops Failover"),

    EMAILGATEWAY_FAILOVER("EGWF", "Email Gateway Failover"),
    FRAUD("FRAUD", "Fraud"),
    FREESWITCH_BLACKLIST("FREESWITCH", "FreeSwitch Blacklist"),
    FREESWITCH_LINETYPEMAP("FREESWITCH_LINETYPEMAP", "FreeSwitch LineTypeMap"),
    GLOBAL_ACTION("GLOBAL_ACTION", "Global Action"),

    HOTKEY_MEMCACHE_FAILOVER("HMCF", "HotKey Memcache Failover"),
    HYBRID_CROSS("HYBRID_CROSS", "zm_cross_hybird_zc"),
    HYBRID_PROXY("HYBRID_PROXY", "zm_proxy_zc"),
    HYBRID_PROXY_CONFIG("HYBRID_PROXY_CONFIG", "zm_proxy_zc_config"),
    ISF_SERVICE_FAILOVER("ISF", "Integration Service Failover"),
    IHSF_SERVICE_FAILOVER("IHSF", "Integration Healthcare Service Failover"),
    LAUNCH_MMR_BCS("L_MMR_BCS", "Launch MMR/BCS"),

    TUNNEL("TUNNEL", "zm_tunnel"),
    LOGIN("LOGIN", "Login"),

    LOOKUP_MIRROR_OPERATE("LMO", "Lookup Mirror Operate"),
    LOOKUP_CLUSTER_OPERATE("LCO", "Lookup Cluster Operate"),
    LOOKUP_FAILOVER("LF", "Lookup Failover"),
    LOOKUP_SERVICE_FAILOVER("LOOKUP", "Lookup Service Failover"),
    LOOKUP_SERVICE_MONITOR("LSM", "Lookup Service Monitor"),

    MEETING_SERVICE_FAILOVER("MSF", "Meeting Service Failover"),
    MEMCACHE_FAILOVER("MCF", "Memcache Failover"),
    MEMCACHE_REPLACE("MCR", "Memcache Replace"),
    MESSAGE_FAILOVER("MF", "Message Failover"),
    MLAR_DATA_CHANNEL_SWITCH("MLAR_DCS", "Mlar Data Channel Switch"),
    MP_FAILOVER("MP", "MarketPlace Failover"),
    MP_UPDATE_ACCOUNT_REGION("MP_UAR", "MarketPlace Update Account"),
    MP_BATCH_UPDATE_ACCOUNT_REGION("MP_BUAR", "MarketPlace Batch Update Account"),

    MP_DYNAMIC_ROUTE_OPERATION("MP_DRO", "MarketPlace Dynamic Route Operation"),

    NACOS_FAILOVER("NACOS_F", "Nacos Failover"),
    NWS_SERVICE_FAILOVER("NSF", "NWS Service Failover"),


    OCS_SERVICE_FAILOVER("OSF", "OCS Service Failover"),
    ONZOOM_FAILOVER("ONZOOM", "OnZoom Failover"),
    OZM_SERVICE_FAILOVER("OZMSF", "Onzoom Service Failover"),
    BOT_SERVICE_FAILOVER("BOTSF", "Bot Service Failover"),

    ZMAIZCAL_SERVICE_FAILOVER("ZMAILZCAL", "Zmail And Zcal Service Failover"),
    EVENTSERVICE_FAILOVER("EVENTSERVICE", "EventService Failover"),
    PBX_BLUEGREEN_SWITCH("PBGF", "PBX Blue Green Switch"),
    PBX_DB_SWITCH("PBX_DB", "PBX DB Switch"),
    PBX_SIPZONE_E("PBX_SZ_E", "PBX SipZone Emergency"),
    PBX_SIPZONE_M("PBX_SZ_M", "PBX SipZone Maintenance"),
    PBX_WEB("PBX_WEB", "PBX Web"),
    PBX_SERVICE_CHANGE("PSF", "PBX Service Change"),
    PBX_ROS("PBX_ROS", "PBX ROS Change"),


    PBX_QOS("PBX_QOS", "PBX QOS Change"),
    PERMISSION_SETTINGS("PS", "Permission Settings"),

    SSO_LOGIN("SSOLOGIN", "SSO Login"),
    SWITCH_NGINX_PROXY("SNP", "Switch Nginx Proxy"),
    SYSTEM_PROPERTIES("SP", "System Properties"),

    THIRD_SYSTEM_LOGIN("TSL", "Third System Login"),
    TOLL_FREE_NUMBER("TOLL_FREE", "Toll-free Numbers"),
    TOLL_NUMBER("TOLL", "Toll Numbers"),

    UCS_SERVICE_FAILOVER("USF", "UCS Service Failover"),

    XMPP_SERVICE_ROLLBACK("XMMPR", "XMPP Service Rollback"),

    WCG_FAILOVER("WCGF", "WCG Failover"),
    WEB_APP_MONITOR("WAM", "Web App Monitor"),
    WEB_FAILOVER("WF", "Web Failover"),
    WEB_FAILOVER_LOG("WFL", "Web Failover Log"),
    WEB_NGINX_BLOCK_IPS("WNBL", "Web Nginx Block Ips"),
    WEB_SWITCH_CLOUD_FLARE("WSCF", "Web switch Cloud flare"),

    WEB_REDISCLUSTER_FAILOVER("WRF", "Web RedisCluster Failover"),
    WEBINAR_FAILOVER("WBF", "Webinar Failover"),


    ZM_HOST("HOST", "ZM_HOST"),
    ZM_ZC("ZMZC", "ZM_ZC"),
    ZM_MEETING_ZONE("ZMMEETINGZONE", "ZM_MEETING_ZONE"),
    ZCC_BG("ZCC_BG", "ZCC BLUE/GREEN Switch"),
    ZCC_M("ZCC_M", "ZCC Maintenance");

    CommandCenterCategoryType(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    private String key;
    private String desc;

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }

    public static String getDescByKey(String key) {
        String desc = "";
        if (StringUtils.isEmpty(key)) {
            desc = "";
        } else {
            for (CommandCenterCategoryType type : CommandCenterCategoryType.values()) {
                if (key.equals(type.getKey())) {
                    desc = type.getDesc();
                }
            }
        }
        return desc;
    }
}
