package us.zoom.cube.site.config;


import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import us.zoom.cube.site.biz.SysParaService;
import us.zoom.cube.site.core.TenantHandler;
import us.zoom.cube.site.core.auth.AuthHandler;
import us.zoom.cube.site.core.config.ClickhouseHandlerFactory;
import us.zoom.cube.site.core.config.PiiTableCacheLoader;
import us.zoom.infra.clickhouse.ChTable;
import us.zoom.infra.clickhouse.ClickhouseEnvProxy;
import us.zoom.infra.dao.model.CollectorFieldDO;
import us.zoom.infra.dao.model.CollectorMetricsDO;
import us.zoom.infra.dao.model.MetricsDO;
import us.zoom.infra.dao.model.TenantDO;
import us.zoom.infra.dao.service.CollectorFieldDAO;
import us.zoom.infra.dao.service.CollectorMetricsDAO;
import us.zoom.infra.dao.service.MetricsDAO;
import us.zoom.infra.dao.service.MetricsFieldDAO;

import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Set;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;

/**
 * <AUTHOR> Wang
 * @date 2022/12/29 9:32 AM
 */
@ExtendWith(MockitoExtension.class)
public class PiiTableLoaderTest {

    @InjectMocks
    private PiiTableCacheLoader piiTableLoader;


    @Mock
    private CollectorFieldDAO collectorFieldDAO;

    @Mock
    private CollectorMetricsDAO collectorMetricsDAO;

    @Mock
    private MetricsDAO metricsDAO;

    @Mock
    private MetricsFieldDAO metricsFieldDAO;

    @Mock
    private TenantHandler tenantHandler;

    @Mock
    private AuthHandler authHandler;


    @Mock
    private ClickhouseHandlerFactory clickhouseHandlerFactory;

    @Mock
    private SysParaService sysParaService;


    @BeforeEach
    public void initMocks(){
        MockitoAnnotations.initMocks(this);
        List<CollectorFieldDO> piiFields =new LinkedList<>();
        CollectorFieldDO collectorFieldDO=new CollectorFieldDO();
        collectorFieldDO.setCollectorId("collector");
        collectorFieldDO.setTargetField("piiField");
        piiFields.add(collectorFieldDO);
        Mockito.when(collectorFieldDAO.getPiiFields()).thenReturn(piiFields);

        List<CollectorMetricsDO> collectorMetricsDOS =new LinkedList<>();
        CollectorMetricsDO collectorMetricsDO=new CollectorMetricsDO();
        collectorMetricsDO.setCollectorId("collector");
        collectorMetricsDO.setMetricsId("metric");
        collectorMetricsDOS.add(collectorMetricsDO);
        Mockito.when(collectorMetricsDAO.listMetricsCollectorByCollectorIds(any())).thenReturn(collectorMetricsDOS);

        List<MetricsDO>metricsDOS=new LinkedList<>();
        MetricsDO metricsDO=new MetricsDO();
        metricsDO.setMetricsName("metricName");
        metricsDO.setId("metric");
        metricsDO.setTenantId("tenantId");
        metricsDO.setTagNames("piiField");
        metricsDOS.add(metricsDO);
        Mockito.when(metricsDAO.listMetricsByMetricsIds(any())).thenReturn(metricsDOS);

        Mockito.when(metricsFieldDAO.listFieldByMetricsId(anyString())).thenReturn(new LinkedList<>());

        Mockito.when(tenantHandler.matchInfluxDbNameToTenantName("dbName",false)).thenReturn("dbName");
        TenantDO tenant =new TenantDO();
        tenant.setName("dbName");
        Mockito.when(tenantHandler.getTenantByIdFromCache(anyString())).thenReturn(tenant);

        Mockito.when(authHandler.hasPiiAuth(anyString(),anyString())).thenReturn(false);

        ClickhouseEnvProxy clickhouseEnvProxy=new ClickhouseEnvProxy();
        Mockito.when(clickhouseHandlerFactory.get()).thenReturn(clickhouseEnvProxy);

        Set<String> ip = new HashSet<>();
        ip.add("china");
        Mockito.when(sysParaService.getCNOutboundFirewall()).thenReturn(ip);
    }

    @Test
    public void testLoad(){
        piiTableLoader.load();
        Assertions.assertTrue(piiTableLoader.getPiiTables().size()>0);
    }

    @Test
    public void testAuth(){
        piiTableLoader.load();
        ChTable t=new ChTable("dbName","metricName");
        boolean auth = piiTableLoader.auth(t, "1", "");
        Assertions.assertFalse(auth);
    }

    @Test
    public void testAuth2(){
        piiTableLoader.load();
        Mockito.when(authHandler.hasPiiAuth(anyString(),anyString())).thenReturn(true);
        ChTable t=new ChTable("dbName","metricName");
        boolean auth = piiTableLoader.auth(t, "1", "us");
        Assertions.assertTrue(auth);
    }

}
