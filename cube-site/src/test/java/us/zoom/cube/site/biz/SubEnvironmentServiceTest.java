package us.zoom.cube.site.biz;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import us.zoom.cube.lib.common.SubEnvironmentEnum;
import us.zoom.cube.site.lib.IdPara;
import us.zoom.cube.site.lib.ResponseObject;
import us.zoom.cube.site.lib.input.SubEnvironmentInput;
import us.zoom.cube.site.lib.query.PageQuery;
import us.zoom.cube.site.lib.query.SubEnvironmentQuery;
import us.zoom.cube.lib.utils.JsonUtils;

import java.util.Date;

/**
 * @author: canyon.li
 * @date: 2023/03/14
 **/
public class SubEnvironmentServiceTest extends BaseTest {

    @Autowired
    private SubEnvironmentService subEnvironmentService;

    @Test
    public void addSubEnvironment() {

        SubEnvironmentInput subEnvironmentInput = new SubEnvironmentInput();
        subEnvironmentInput.setSubId("39caa432-8f3f-4679-ad39-3f254a203be4");
        subEnvironmentInput.setSubName("Canyon_test_1");
        subEnvironmentInput.setType(SubEnvironmentEnum.service.name());
        subEnvironmentInput.setAlarmInsightEnv("STAND");
        subEnvironmentInput.setDataQueryEnv("STAND");
        subEnvironmentInput.setAlarmActionEnv("STAND");
        subEnvironmentInput.setCreateTime(new Date());
        subEnvironmentInput.setModifyTime(new Date());
        String id = subEnvironmentService.addEnvironment(subEnvironmentInput);
        System.out.println("addSubEnvironment success, id = " + id);
    }

    @Test
    public void editSubEnvironment() {

        SubEnvironmentInput subEnvironmentInput = new SubEnvironmentInput();
        subEnvironmentInput.setId("19af42b7-8811-43b0-a315-5376edd494f8");
        subEnvironmentInput.setType(SubEnvironmentEnum.service.name());
        subEnvironmentInput.setAlarmActionEnv("stand");
        subEnvironmentInput.setDataQueryEnv("main");
        subEnvironmentInput.setModifyTime(new Date());

        String id = subEnvironmentService.editEnvironment(subEnvironmentInput);
        System.out.println("editSubEnvironment success, id = " + id);
    }

    @Test
    public void deleteSubEnvironment() {

        IdPara idPara = new IdPara();
        idPara.setId("19af42b7-8811-43b0-a315-5376edd494f8");
        subEnvironmentService.delEnvironment(idPara);
    }

    @Test
    public void searchSubEnvironmentById() {
        IdPara idPara = new IdPara();
        idPara.setId("19af42b7-8811-43b0-a315-5376edd494f8");
        ResponseObject res = subEnvironmentService.searchSubEnvironmentById(idPara);
        System.out.println("searchSubEnvironmentById success, result = " + JsonUtils.toJsonString(res));
    }


    @Test
    public void searchSubEnvironment() {
        PageQuery<SubEnvironmentQuery> pageQuery = new PageQuery<>();
//        SubEnvironmentQuery subEnvironmentQuery = new SubEnvironmentQuery();
//        subEnvironmentQuery.setSubName("canyon");
        pageQuery.setPageIndex(1);
        pageQuery.setPageSize(10);
//        pageQuery.setQueryPara(subEnvironmentQuery);
        ResponseObject res = subEnvironmentService.searchSubEnvironmentWithNormal(pageQuery);
        System.out.println("searchSubEnvironment success, result = " + JsonUtils.toJsonString(res));
    }

    @Test
    public void searchSubEnvironmentWithNormal() {
        PageQuery<SubEnvironmentQuery> pageQuery = new PageQuery<>();
//        SubEnvironmentQuery subEnvironmentQuery = new SubEnvironmentQuery();
//        subEnvironmentQuery.setSubName("canyon");
        pageQuery.setPageIndex(1);
        pageQuery.setPageSize(10);
//        pageQuery.setQueryPara(subEnvironmentQuery);

        ResponseObject res = subEnvironmentService.searchSubEnvironmentWithNormal(pageQuery);
        pageQuery.setPageIndex(2);
        pageQuery.setPageSize(5);
        res = subEnvironmentService.searchSubEnvironmentWithNormal(pageQuery);

        System.out.println("searchSubEnvironmentWithNormal success, result = " + JsonUtils.toJsonString(res));
    }


    @Test
    public void test() {
        addSubEnvironment();
        searchSubEnvironmentById();
        deleteSubEnvironment();
    }


}
