package us.zoom.cube.site.lib.output.dataparser;

import com.google.gson.Gson;
import lombok.Data;
import us.zoom.cube.lib.common.*;
import us.zoom.cube.site.lib.input.dataparser.OutputQueueCfg;
import us.zoom.infra.dao.model.AsyncMqQueueDO;
import us.zoom.infra.dao.model.KafkaClusterDO;
import us.zoom.infra.dao.model.KafkaQueueDO;
import us.zoom.infra.dao.model.TaskQueueDO;

import java.util.Map;

/**
 * @Author: luis.zheng
 * @Date: 2021/8/20 10:13
 */
@Data
public class DataParserQueueOut {
    private String asyncmqClusterId;
    private String asyncmqClusterName;
    private Integer order;
    private String topic;
    private String groupId;
    private String clusterName;
    private String dcName;
    private int partition;
    /**
     * @see us.zoom.cube.lib.common.AsyncMqQueueStatusEnum
     */
    private String status;

    public DataParserQueueOut(AsyncMqQueueDO asyncMqQueueDO, Map<String, AsyncMqTopicOut> asyncMqTopicOutMap) {
        this.asyncmqClusterId = asyncMqQueueDO.getAsyncmqClusterId();
        this.asyncmqClusterName = "";
        this.topic = asyncMqQueueDO.getTopic();
        this.order = asyncMqQueueDO.getOrder();
        this.groupId = asyncMqQueueDO.getGroupId();
        AsyncMqTopicOut asyncMqTopicOut = asyncMqTopicOutMap.get(asyncMqQueueDO.getTopic());
        this.clusterName = asyncMqTopicOut.getClusterName();
        this.dcName = asyncMqTopicOut.getDcName();
        this.partition = asyncMqTopicOut.getPartition();
        AsyncMqQueueStatusEnum enumObj = AsyncMqQueueStatusEnum.fromType(asyncMqQueueDO.getStatus());
        this.status = enumObj == null ? "" : enumObj.name();
    }
}
