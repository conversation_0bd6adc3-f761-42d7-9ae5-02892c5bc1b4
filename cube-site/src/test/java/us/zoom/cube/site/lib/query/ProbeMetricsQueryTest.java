package us.zoom.cube.site.lib.query;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Assertions;
import us.zoom.infra.utils.DateUtils;
import us.zoom.cube.lib.utils.JsonUtils;

import java.text.ParseException;
import java.util.Arrays;
import java.util.Date;

/**
 * @Author: <EMAIL>
 * @ModuleOwner: <EMAIL>
 * @Date: 07/27/2022 15:24
 * @Description:
 */
public class ProbeMetricsQueryTest {


//    public void check() {
//        Assert.isTrue(!CollectionUtils.isEmpty(groupBy), "group by cannot be null");
//        if (granularity == null) {
//            granularity = ONE_HOUR;
//        }
//        try {
//            rounding = granularity * 1000L;
//            startTimeMillisecond = DateUtils.parseDate(startTime, DateUtils.FORMART1).getTime() / rounding * rounding + rounding;
//            endTimeMillisecond = DateUtils.parseDate(endTime, DateUtils.FORMART1).getTime() / rounding * rounding;
//        } catch (Exception e) {
//            Assert.isTrue(false, "Incorrect format of startTime and endTime time, format:" + DateUtils.FORMART1);
//        }
//    }

    @Test
    public void testCheckGroupBy() {
        ProbeMetricsQuery query = new ProbeMetricsQuery();
        query.setGroupBy(null);
        try {
            query.check();
        } catch (Exception e) {
            Assertions.assertEquals("group by cannot be null", e.getMessage());
        }
        query.setGroupBy(Arrays.asList());
        try {
            query.check();
        } catch (Exception e) {
            Assertions.assertEquals("group by cannot be null", e.getMessage());
        }
    }

    @Test
    public void testCheckGranularity() throws ParseException {
        ProbeMetricsQuery query = new ProbeMetricsQuery();
        query.setGroupBy(Arrays.asList("service"));
        query.setStartTime(DateUtils.parseDate("2022-02-22 10:10:10", DateUtils.FORMART1).getTime());
        query.setEndTime(DateUtils.parseDate("2022-02-25 10:10:10", DateUtils.FORMART1).getTime());
        query.setGroupBy(Arrays.asList("service"));
        query.check();
        int granularity = query.getGranularity();
        Assertions.assertEquals(granularity, 60 * 60);
        long rounding = query.getRounding();
        Assertions.assertEquals(rounding, 60 * 60 * 1000);
    }

    @Test
    public void testCheckTime() throws ParseException {
        ProbeMetricsQuery query = new ProbeMetricsQuery();
        query.setStartTime(DateUtils.parseDate("2022-08-01 10:10:10 111", DateUtils.FORMART1).getTime());
        query.setGroupBy(Arrays.asList("service"));
        try {
            query.check();
        } catch (Exception e) {
            Assertions.assertEquals("Incorrect format of startTime and endTime time, format:" + DateUtils.FORMART1, e.getMessage());
        }
    }

    @Test
    public void testCheckTime1() throws ParseException {
        ProbeMetricsQuery query = new ProbeMetricsQuery();
        query.setStartTime(DateUtils.parseDate("2022-08-01 10:09:10", DateUtils.FORMART1).getTime());
        query.setEndTime(DateUtils.parseDate("2022-08-04 10:12:33", DateUtils.FORMART1).getTime());
        query.setGroupBy(Arrays.asList("service"));
        query.setGranularity(60 * 10);
        query.check();
        Assertions.assertEquals(DateUtils.format(new Date(query.getStartTimeMillisecond()), DateUtils.FORMART1), "2022-08-01 10:00:00");
        Assertions.assertEquals(DateUtils.format(new Date(query.getEndTimeMillisecond()), DateUtils.FORMART1), "2022-08-04 10:20:00");
    }

    @Test
    public void testCheckTime2() {
        ProbeMetricsQuery query = new ProbeMetricsQuery();
        System.out.println(DateUtils.format(new Date(1660441985000L), DateUtils.FORMART1));
        System.out.println(DateUtils.format(new Date(1660528385000L), DateUtils.FORMART1));

        System.out.println(DateUtils.format(new Date(1660438800000L), DateUtils.FORMART1));
        System.out.println(DateUtils.format(new Date(1660525200000L), DateUtils.FORMART1));

        query.setStartTime(new Date(1660441985000L).getTime());
        query.setEndTime(new Date(1660528385000L).getTime());
        query.setGroupBy(Arrays.asList("service"));
        try {
            query.check();

        } catch (Exception e) {
            Assertions.assertEquals("Incorrect format of startTime and endTime time, format:" + DateUtils.FORMART1, e.getMessage());
        }
    }

    @Test
    public void testCheckTime3() {
//        2022-08-14 11:00:00 -> 1660446000
        long rounding = 3600000;
        long startTime = 1660446000;
        Long startTimeForSecond = new ProbeMetricsQuery().generateStartTimeForSecond(startTime, rounding);
        System.out.println(DateUtils.format(new Date(startTimeForSecond), DateUtils.FORMART1));
        Assertions.assertEquals(DateUtils.format(new Date(startTimeForSecond), DateUtils.FORMART1), "2022-08-14 11:00:00");


//        2022-08-14 11:11:00 -> 1660446660
        startTime = 1660446660;
        startTimeForSecond = new ProbeMetricsQuery().generateStartTimeForSecond(startTime, rounding);
        System.out.println(DateUtils.format(new Date(startTimeForSecond), DateUtils.FORMART1));
        Assertions.assertEquals(DateUtils.format(new Date(startTimeForSecond), DateUtils.FORMART1), "2022-08-14 11:00:00");
    }

    @Test
    public void testCheckTime4() {
//        2022-08-14 11:00:00 -> 1660446000
        long rounding = 6 * 60 * 1000;
        long startTime = 1660446000;
        Long startTimeForSecond = new ProbeMetricsQuery().generateStartTimeForSecond(startTime, rounding);
        System.out.println(DateUtils.format(new Date(startTimeForSecond), DateUtils.FORMART1));
        Assertions.assertEquals(DateUtils.format(new Date(startTimeForSecond), DateUtils.FORMART1), "2022-08-14 11:00:00");


//        2022-08-14 11:11:00 -> 1660446660
        startTime = 1660446660;
        startTimeForSecond = new ProbeMetricsQuery().generateStartTimeForSecond(startTime, rounding);
        System.out.println(DateUtils.format(new Date(startTimeForSecond), DateUtils.FORMART1));
        Assertions.assertEquals(DateUtils.format(new Date(startTimeForSecond), DateUtils.FORMART1), "2022-08-14 11:12:00");
    }

    @Test
    public void testCheckDetailService() {
        ProbeMetricsQuery query = new ProbeMetricsQuery();
        query.setService("");
        try {
            query.checkDetailParam();
        } catch (Exception e) {
            Assertions.assertEquals("service cannot be null", e.getMessage());
        }
        query.setService("  ");
        try {
            query.checkDetailParam();
        } catch (Exception e) {
            Assertions.assertEquals("service cannot be null", e.getMessage());
        }
        query.setService(null);
        try {
            query.checkDetailParam();
        } catch (Exception e) {
            Assertions.assertEquals("service cannot be null", e.getMessage());
        }
    }

    @Test
    public void testCheckDetailProbeTaskId() {
        ProbeMetricsQuery query = new ProbeMetricsQuery();
        query.setService("realtime");
        query.setProbeTaskName("realtime");
        query.setProbeTaskId("");
        try {
            query.checkDetailParam();
        } catch (Exception e) {
            Assertions.assertEquals("probeTaskId cannot be null", e.getMessage());
        }
        query.setProbeTaskId("  ");
        try {
            query.checkDetailParam();
        } catch (Exception e) {
            Assertions.assertEquals("probeTaskId cannot be null", e.getMessage());
        }
        query.setProbeTaskId(null);
        try {
            query.checkDetailParam();
        } catch (Exception e) {
            Assertions.assertEquals("probeTaskId cannot be null", e.getMessage());
        }
    }

    @Test
    public void testCheckDetailProbeTaskName() {
        ProbeMetricsQuery query = new ProbeMetricsQuery();
        query.setService("realtime");
        query.setProbeTaskId("realtime");
        query.setProbeTaskName("");
        try {
            query.checkDetailParam();
        } catch (Exception e) {
            Assertions.assertEquals("probeTaskName cannot be null", e.getMessage());
        }
        query.setProbeTaskName("  ");
        try {
            query.checkDetailParam();
        } catch (Exception e) {
            Assertions.assertEquals("probeTaskName cannot be null", e.getMessage());
        }
        query.setProbeTaskName(null);
        try {
            query.checkDetailParam();
        } catch (Exception e) {
            Assertions.assertEquals("probeTaskName cannot be null", e.getMessage());
        }
    }

    @Test
    public void test() {
        ProbeMetricsQuery query = new ProbeMetricsQuery();
        query.setGroupBy(Arrays.asList("service"));
        query.setGranularity(1);
        System.out.println(JsonUtils.toJsonString(query));
    }

    @Test
    public void testDate() throws ParseException {
        Date date = DateUtils.parseDate("1970-1-1 00:00:00", DateUtils.FORMART1);
        System.out.println(date.getTime());
        Date date1 = DateUtils.parseDate("1970-1-1 08:00:00", DateUtils.FORMART1);
        System.out.println(date1.getTime());
        System.out.println(8 * 60 * 60 * 1000);


        Date date2 = DateUtils.parseDate("2022-07-23 07:00:00", DateUtils.FORMART1);
        for (int i = 1; i < 15; i++) {
            System.out.println(DateUtils.format(new Date(date2.getTime() + i * 60 * 1000), DateUtils.FORMART1));
        }
    }
}
