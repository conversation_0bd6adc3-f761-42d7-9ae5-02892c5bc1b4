package us.zoom.cube.site.core.parser.process.core.processor;

import com.google.common.collect.Maps;
import us.zoom.cube.lib.hub.CustomerLabelInfoSource;

import java.util.Map;

/**
 * <AUTHOR>
 */
public class CustomerProcessorCache {

    private Map<String, CustomerLabelInfoSource> processorCacheDataMap = Maps.newConcurrentMap();

    private CustomerProcessorCache() {
    }

    private static final class SingletonHolder {
        private static final CustomerProcessorCache SINGLETON = new CustomerProcessorCache();
    }

    public static CustomerProcessorCache getInstance() {
        return SingletonHolder.SINGLETON;
    }

    public void setProcessorCacheData(Map<String, CustomerLabelInfoSource> processorCacheData) {
        processorCacheDataMap = processorCacheData;
    }


    public Map<String, CustomerLabelInfoSource> getProcessorCacheDataMap() {
        return processorCacheDataMap;
    }
}
