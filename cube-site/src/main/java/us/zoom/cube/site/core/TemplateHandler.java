package us.zoom.cube.site.core;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.zoom.op.monitor.dao.alarm.AlarmDefinitionDao;
import com.zoom.op.monitor.domain.IdAndMetricIdAndTenantId;
import com.zoom.op.monitor.domain.alarm.AlarmDefinition;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RestController;
import us.zoom.cube.lib.common.TenantType;
import us.zoom.cube.lib.utils.TenantTypeUtil;
import us.zoom.cube.site.api.intercept.AuthInterceptor;
import us.zoom.cube.site.biz.AuthService;
import us.zoom.cube.site.biz.DataParserService;
import us.zoom.cube.site.infra.utils.ExceptionUtils;
import us.zoom.cube.site.infra.utils.IdUtils;
import us.zoom.cube.site.lib.SiteException;
import us.zoom.cube.site.lib.dto.*;
import us.zoom.cube.site.lib.input.tpl.ApplyTplInput;
import us.zoom.cube.site.lib.input.tpl.RevokeTplInput;
import us.zoom.infra.dao.model.*;
import us.zoom.infra.dao.service.*;
import us.zoom.infra.enums.TemplateSceneEnum;
import us.zoom.infra.utils.Instance;
import us.zoom.cube.lib.utils.JsonUtils;

import java.util.*;
import java.util.stream.Collectors;

import static us.zoom.cube.site.infra.enums.WebCodeEnum.InnerError;


/**
 * <AUTHOR>
 */
@Service
@RestController
@Slf4j
public class TemplateHandler {
    @Autowired
    private DataParserHandler dataParserHandler;

    @Autowired
    private DataParserPipelineHandler dataParserPipelineHandler;

    @Autowired
    private DataParserGrokProcessorHandler dataParserGrokProcessorHandler;

    @Autowired
    private DataParserFilterProcessorHandler dataParserFilterProcessorHandler;

    @Autowired
    private DataParserRemapperProcessorHandler dataParserRemapperProcessorHandler;

    @Autowired
    private DataParserEncryptionProcessorHandler dataParserEncryptionProcessorHandler;

    @Autowired
    private DataParserLabelMysqlProcessorHandler dataParserLabelMysqlProcessorHandler;

    @Autowired
    private DataParserLabelRedisProcessorHandler dataParserLabelRedisProcessorHandler;

    @Autowired
    private DataParserGroovyProcessorHandler dataParserGroovyProcessorHandler;

    @Autowired
    private AuthService authService;

    @Autowired
    private CollectorHandler collectorHandler;

    @Autowired
    private DataParserService dataParserService;

    @Autowired
    private AsyncQueueGroupHandler asyncQueueGroupHandler;

    @Autowired
    private AsyncQueueHandler asyncQueueHandler;

    @Autowired
    private DataParserDAO dataParserDAO;

    @Autowired
    private DataParserSourceDAO dataParserSourceDAO;

    @Autowired
    private MetricsHandler metricsHandler;

    @Autowired
    private MetricsAggregationHandler metricsAggregationHandler;

    @Autowired
    private MetricsAggregationRuleHandler metricsAggregationRuleHandler;

    @Autowired
    private AggregationCustomFieldRuleHandler aggregationCustomFieldRuleHandler;

    @Autowired
    private MetricsDAO metricsDAO;

    @Autowired
    private DataParserPipelineDAO dataParserPipelineDAO;

    @Autowired
    private CollectorMetricsDAO collectorMetricsDAO;

    @Autowired
    private MetricsFieldHandler metricsFieldHandler;

    @Autowired
    private AggregationFunctionItemHandler aggregationFunctionItemHandler;

    @Autowired
    private AggregationHistogramRangeItemHandler aggregationHistogramRangeItemHandler;

    @Autowired
    private AggregationPercentileItemHandler aggregationPercentileItemHandler;

    @Autowired
    private TplTenantRelaHandler tplTenantRelaHandler;

    @Autowired
    private AlarmDefinitionHandler alarmDefinitionHandler;

    @Autowired
    private AlarmDefinitionDao alarmDefinitionDao;

    @Autowired
    private TenantHandler tenantHandler;

    @Autowired
    private DataFlowDataParserRelationHandler dataFlowDataParserRelationHandler;

    private static final int pageSize = 1000;

    @Transactional(rollbackFor = Exception.class)
    public void applyTemplateInDB(List<AsyncQueueGroupDO> addAsyncQueueGroupDOList,
                                  List<AsyncQueueDO> addAsyncQueueDOList,
                                  List<DataParserDO> addDataParserDOList,
                                  List<DataParserDO> updateDataParserDOList,
                                  List<DataParserPipelineDO> addPipelineDOList,
                                  List<DataParserPipelineDO> updatePipelineDOList,
                                  List<DataParserRemapperProcessorDO> addDataParserRemapperProcessorDOList,
                                  List<DataParserFilterProcessorDO> addDataParserFilterProcessorDOList,
                                  List<DataParserGroovyProcessorDO> addDataParserGroovyProcessorDOList,
                                  List<DataParserGrokProcessorDO> addDataParserGrokProcessorDOList,
                                  List<DataParserEncryptionProcessorDO> addDataParserEncryptionProcessorDOList,
                                  List<DataParserLabelMysqlProcessorDO> addDataParserLabelMysqlProcessorDOList,
                                  List<DataParserLabelRedisProcessorDO> addDataParserLabelRedisProcessorDOList,
                                  List<CollectorDO> addCollectorDOList,
                                  List<CollectorFieldDO> addCollectorFieldDOList,
                                  List<MetricsDO> addMetricsDOList,
                                  List<CollectorMetricsDO> addCollectorMetricsDOList,
                                  List<MetricsFieldDO> addMetricsFieldDOList,
                                  List<MetricsAggregationDO> addMetricsAggregationDOList,
                                  List<MetricsAggregationRuleDO> addMetricsAggregationRuleDOList,
                                  List<AggregationFunctionItemDO> addAggregationFunctionItemDOList,
                                  List<AggregationHistogramRangeItemDO> addAggregationHistogramRangeItemDOList,
                                  List<AggregationPercentileItemDO> addAggregationPercentileItemDOList,
                                  List<AggregationCustomFieldRuleDO> addAggregationCustomFieldRuleDO,
                                  List<AlarmDefinition> addAlarmDefinitionList,
                                  List<TplTenantRelaDO> addTplTenantRelaDOList,
                                  List<TplTenantRelaDO> updateTplTenantRelaDOList,
                                  ApplyTplInput applyTplInput
    ) {
        try {
            log.info("<=====================start log key component when applying template in db operation====================>");
            log.info("addAsyncQueueGroupDOList = {}", JsonUtils.toJsonStringIgnoreExp(addAsyncQueueGroupDOList));
            log.info("addAsyncQueueDOList = {}", JsonUtils.toJsonStringIgnoreExp(addAsyncQueueDOList));
            log.info("addDataParserDOList = {}", JsonUtils.toJsonStringIgnoreExp(addDataParserDOList));
            log.info("updateDataParserDOList = {}", JsonUtils.toJsonStringIgnoreExp(updateDataParserDOList));
            log.info("addPipelineDOList = {}", JsonUtils.toJsonStringIgnoreExp(addPipelineDOList));
            log.info("updatePipelineDOList = {}", JsonUtils.toJsonStringIgnoreExp(updatePipelineDOList));
            log.info("addDataParserRemapperProcessorDOList = {}", JsonUtils.toJsonStringIgnoreExp(addDataParserRemapperProcessorDOList));
            log.info("addDataParserFilterProcessorDOList = {}", JsonUtils.toJsonStringIgnoreExp(addDataParserFilterProcessorDOList));
            log.info("addDataParserGroovyProcessorDOList = {}", JsonUtils.toJsonStringIgnoreExp(addDataParserGroovyProcessorDOList));
            log.info("addDataParserGrokProcessorDOList = {}", JsonUtils.toJsonStringIgnoreExp(addDataParserGrokProcessorDOList));
            log.info("addDataParserEncryptionProcessorDOList = {}", JsonUtils.toJsonStringIgnoreExp(addDataParserEncryptionProcessorDOList));
            log.info("addDataParserLabelMysqlProcessorDOList = {}", JsonUtils.toJsonStringIgnoreExp(addDataParserLabelMysqlProcessorDOList));
            log.info("addDataParserLabelRedisProcessorDOList = {}", JsonUtils.toJsonStringIgnoreExp(addDataParserLabelRedisProcessorDOList));
            log.info("addCollectorDOList = {}", JsonUtils.toJsonStringIgnoreExp(addCollectorDOList));
            log.info("addCollectorFieldDOList = {}", JsonUtils.toJsonStringIgnoreExp(addCollectorFieldDOList));
            log.info("addMetricsDOList = {}", JsonUtils.toJsonStringIgnoreExp(addMetricsDOList));
            log.info("addCollectorMetricsDOList = {}", JsonUtils.toJsonStringIgnoreExp(addCollectorMetricsDOList));
            log.info("addMetricsFieldDOList = {}", JsonUtils.toJsonStringIgnoreExp(addMetricsFieldDOList));
            log.info("addMetricsAggregationDOList = {}", JsonUtils.toJsonStringIgnoreExp(addMetricsAggregationDOList));
            log.info("addMetricsAggregationRuleDOList = {}", JsonUtils.toJsonStringIgnoreExp(addMetricsAggregationRuleDOList));
            log.info("addAggregationFunctionItemDOList = {}", JsonUtils.toJsonStringIgnoreExp(addAggregationFunctionItemDOList));
            log.info("addAggregationHistogramRangeItemDOList = {}", JsonUtils.toJsonStringIgnoreExp(addAggregationHistogramRangeItemDOList));
            log.info("addAggregationPercentileItemDOList = {}", JsonUtils.toJsonStringIgnoreExp(addAggregationPercentileItemDOList));
            log.info("addAggregationCustomFieldRuleDO = {}", JsonUtils.toJsonStringIgnoreExp(addAggregationCustomFieldRuleDO));
            log.info("addAlarmDefinitionList = {}", JsonUtils.toJsonStringIgnoreExp(addAlarmDefinitionList));
            log.info("addTplTenantRelaDOList = {}", JsonUtils.toJsonStringIgnoreExp(addTplTenantRelaDOList));
            log.info("updateTplTenantRelaDOList = {}", JsonUtils.toJsonStringIgnoreExp(updateTplTenantRelaDOList));

            log.info("<=====================end log key component when applying template in db operation====================>");

            if (!applyTplInput.isApplyInDB()) {
                log.info("will not apply in DB");
                return;
            }
            addAsyncQueueGroupDOList.forEach(e -> asyncQueueGroupHandler.addAqGroup(e));
            for (AsyncQueueDO asyncQueueDO : addAsyncQueueDOList) {
                asyncQueueHandler.addAq(asyncQueueDO);
            }
            addDataParserDOList.forEach(e -> dataParserDAO.addDataParser(e));
            updateDataParserDOList.forEach(e -> dataParserDAO.editDataParser(e));
            insertIntoDB(addPipelineDOList, updatePipelineDOList, addDataParserRemapperProcessorDOList,
                    addDataParserFilterProcessorDOList, addDataParserGroovyProcessorDOList, addDataParserGrokProcessorDOList,
                    addDataParserEncryptionProcessorDOList, addDataParserLabelMysqlProcessorDOList, addDataParserLabelRedisProcessorDOList,
                    addCollectorDOList, addCollectorFieldDOList, addMetricsDOList, addCollectorMetricsDOList, addMetricsFieldDOList, addMetricsAggregationDOList,
                    addMetricsAggregationRuleDOList, addAggregationFunctionItemDOList, addAggregationHistogramRangeItemDOList, addAggregationPercentileItemDOList,
                    addAggregationCustomFieldRuleDO, addAlarmDefinitionList, addTplTenantRelaDOList, updateTplTenantRelaDOList, new ArrayList<>());
        } catch (Exception e) {
            log.error(ExceptionUtils.getTrace(e));
            throw new SiteException(InnerError.getCode(), InnerError.getErrMsg());
        }

    }

    @Transactional(rollbackFor = Exception.class)
    public void applyTemplateInDB(List<DataParserSourceDO> addDataParserSourceDOList,
                                  List<DataParserSourceDO> updateDataParserSourceDOList,
                                  List<DataParserPipelineDO> addPipelineDOList,
                                  List<DataParserPipelineDO> updatePipelineDOList,
                                  List<DataParserRemapperProcessorDO> addDataParserRemapperProcessorDOList,
                                  List<DataParserFilterProcessorDO> addDataParserFilterProcessorDOList,
                                  List<DataParserGroovyProcessorDO> addDataParserGroovyProcessorDOList,
                                  List<DataParserGrokProcessorDO> addDataParserGrokProcessorDOList,
                                  List<DataParserEncryptionProcessorDO> addDataParserEncryptionProcessorDOList,
                                  List<DataParserLabelMysqlProcessorDO> addDataParserLabelMysqlProcessorDOList,
                                  List<DataParserLabelRedisProcessorDO> addDataParserLabelRedisProcessorDOList,
                                  List<CollectorDO> addCollectorDOList,
                                  List<CollectorFieldDO> addCollectorFieldDOList,
                                  List<MetricsDO> addMetricsDOList,
                                  List<CollectorMetricsDO> addCollectorMetricsDOList,
                                  List<MetricsFieldDO> addMetricsFieldDOList,
                                  List<MetricsAggregationDO> addMetricsAggregationDOList,
                                  List<MetricsAggregationRuleDO> addMetricsAggregationRuleDOList,
                                  List<AggregationFunctionItemDO> addAggregationFunctionItemDOList,
                                  List<AggregationHistogramRangeItemDO> addAggregationHistogramRangeItemDOList,
                                  List<AggregationPercentileItemDO> addAggregationPercentileItemDOList,
                                  List<AggregationCustomFieldRuleDO> addAggregationCustomFieldRuleDO,
                                  List<AlarmDefinition> addAlarmDefinitionList,
                                  List<TplTenantRelaDO> addTplTenantRelaDOList,
                                  List<TplTenantRelaDO> updateTplTenantRelaDOList,
                                  ApplyTplInput applyTplInput,
                                  List<DataFlowDataParserRelationDO> addDataFlowDataParserRelationDOS) {
        try {
            if (!applyTplInput.isApplyInDB()) {
                log.info("will not apply in DB");
                return;
            }
            addDataParserSourceDOList.forEach(e -> dataParserSourceDAO.addDataParserSource(e));
            updateDataParserSourceDOList.forEach(e -> dataParserSourceDAO.editDataParserSource(e));
            insertIntoDB(addPipelineDOList, updatePipelineDOList, addDataParserRemapperProcessorDOList,
                    addDataParserFilterProcessorDOList, addDataParserGroovyProcessorDOList, addDataParserGrokProcessorDOList,
                    addDataParserEncryptionProcessorDOList, addDataParserLabelMysqlProcessorDOList, addDataParserLabelRedisProcessorDOList,
                    addCollectorDOList, addCollectorFieldDOList, addMetricsDOList, addCollectorMetricsDOList, addMetricsFieldDOList, addMetricsAggregationDOList,
                    addMetricsAggregationRuleDOList, addAggregationFunctionItemDOList, addAggregationHistogramRangeItemDOList, addAggregationPercentileItemDOList,
                    addAggregationCustomFieldRuleDO, addAlarmDefinitionList, addTplTenantRelaDOList, updateTplTenantRelaDOList, addDataFlowDataParserRelationDOS);
        } catch (Exception e) {
            log.error(ExceptionUtils.getTrace(e));
            throw new SiteException(InnerError.getCode(), InnerError.getErrMsg());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void insertIntoDB(List<DataParserPipelineDO> addPipelineDOList,
                             List<DataParserPipelineDO> updatePipelineDOList,
                             List<DataParserRemapperProcessorDO> addDataParserRemapperProcessorDOList,
                             List<DataParserFilterProcessorDO> addDataParserFilterProcessorDOList,
                             List<DataParserGroovyProcessorDO> addDataParserGroovyProcessorDOList,
                             List<DataParserGrokProcessorDO> addDataParserGrokProcessorDOList,
                             List<DataParserEncryptionProcessorDO> addDataParserEncryptionProcessorDOList,
                             List<DataParserLabelMysqlProcessorDO> addDataParserLabelMysqlProcessorDOList,
                             List<DataParserLabelRedisProcessorDO> addDataParserLabelRedisProcessorDOList,
                             List<CollectorDO> addCollectorDOList,
                             List<CollectorFieldDO> addCollectorFieldDOList,
                             List<MetricsDO> addMetricsDOList,
                             List<CollectorMetricsDO> addCollectorMetricsDOList,
                             List<MetricsFieldDO> addMetricsFieldDOList,
                             List<MetricsAggregationDO> addMetricsAggregationDOList,
                             List<MetricsAggregationRuleDO> addMetricsAggregationRuleDOList,
                             List<AggregationFunctionItemDO> addAggregationFunctionItemDOList,
                             List<AggregationHistogramRangeItemDO> addAggregationHistogramRangeItemDOList,
                             List<AggregationPercentileItemDO> addAggregationPercentileItemDOList,
                             List<AggregationCustomFieldRuleDO> addAggregationCustomFieldRuleDO,
                             List<AlarmDefinition> addAlarmDefinitionList,
                             List<TplTenantRelaDO> addTplTenantRelaDOList,
                             List<TplTenantRelaDO> updateTplTenantRelaDOList,
                             List<DataFlowDataParserRelationDO> addDataFlowDataParserRelationDOS) {
        try {
            addPipelineDOList.forEach(e -> dataParserPipelineHandler.addPipeline(e));
            updatePipelineDOList.forEach(e -> dataParserPipelineDAO.edit(e));

            addDataParserRemapperProcessorDOList.forEach(e -> dataParserRemapperProcessorHandler.addRemapperProcessor(e));
            addDataParserFilterProcessorDOList.forEach(e -> dataParserFilterProcessorHandler.addFilterProcessor(e));
            addDataParserGroovyProcessorDOList.forEach(e -> dataParserGroovyProcessorHandler.addGroovyProcessor(e));
            addDataParserGrokProcessorDOList.forEach(e -> dataParserGrokProcessorHandler.addGrokProcessor(e));
            addDataParserEncryptionProcessorDOList.forEach(e -> dataParserEncryptionProcessorHandler.addEncryptionProcessor(e));
            addDataParserLabelMysqlProcessorDOList.forEach(e -> dataParserLabelMysqlProcessorHandler.addLabelMysqlProcessor(e));
            addDataParserLabelRedisProcessorDOList.forEach(e -> dataParserLabelRedisProcessorHandler.addLabelRedisProcessor(e));

            addCollectorDOList.forEach(e -> collectorHandler.insertCollector(e));
            addCollectorFieldDOList.forEach(e -> collectorHandler.insertCollectorField(e));

            addMetricsDOList.forEach(e -> metricsDAO.insertMetrics(e));
            addCollectorMetricsDOList.forEach(e -> collectorMetricsDAO.insertCollectorMetrics(e));
            addMetricsFieldDOList.forEach(e -> metricsFieldHandler.insertMetricsField(e));

            addMetricsAggregationDOList.forEach(e -> metricsAggregationHandler.insertMetricsAggregation(e));
            metricsAggregationRuleHandler.batchInsertMetricsAggregationRule(addMetricsAggregationRuleDOList);
            aggregationFunctionItemHandler.batchInsertAggregationFunction(addAggregationFunctionItemDOList);
            aggregationHistogramRangeItemHandler.batchInsertAggregationHistogramRange(addAggregationHistogramRangeItemDOList);
            aggregationPercentileItemHandler.batchInsertAggregationPercentile(addAggregationPercentileItemDOList);
            aggregationCustomFieldRuleHandler.batchInsertCustomFieldRule(addAggregationCustomFieldRuleDO);
            dataFlowDataParserRelationHandler.batchAdd(addDataFlowDataParserRelationDOS);

            alarmDefinitionHandler.batchInsert(addAlarmDefinitionList);

            addTplTenantRelaDOList.forEach(e -> tplTenantRelaHandler.insertTplTenantRela(e));
            updateTplTenantRelaDOList.forEach(e -> tplTenantRelaHandler.updateStatus(e));
        } catch (Exception e) {
            log.error(ExceptionUtils.getTrace(e));
            throw new SiteException(InnerError.getCode(), InnerError.getErrMsg());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void revokePublicInfo(List<String> delPipelineDOList,
                                 List<String> delCollectorDOList,
                                 List<String> delCollectorFieldDOList,
                                 List<String> delMetricsDOList,
                                 List<String> delAlarmDefinitionList,
                                 List<String> delTplTenantRelaDOList) {
        try {
            dataParserPipelineDAO.delByIds(delPipelineDOList);
            dataParserRemapperProcessorHandler.delRemapperProcessorByPipelineIds(delPipelineDOList);
            dataParserFilterProcessorHandler.delFilterProcessorByPipelineIds(delPipelineDOList);
            dataParserGroovyProcessorHandler.delGroovyProcessorByPipelineIds(delPipelineDOList);
            dataParserGrokProcessorHandler.delGrokProcessorByPipelineIds(delPipelineDOList);
            dataParserEncryptionProcessorHandler.delEncryptionProcessorByPipelineIds(delPipelineDOList);
            dataParserLabelMysqlProcessorHandler.delLabelMysqlProcessorByPipelineIds(delPipelineDOList);
            dataParserLabelRedisProcessorHandler.delLabelRedisProcessorByPipelineIds(delPipelineDOList);

            delCollectorDOList.forEach(e -> collectorHandler.delCollector(e));
            delCollectorFieldDOList.forEach(e -> collectorHandler.delField(e));
            delAlarmDefinitionList.forEach(e -> alarmDefinitionDao.deleteById(e));
            delMetricsDOList.forEach(e -> metricsHandler.deleteMetrics(e));
            delTplTenantRelaDOList.forEach(e -> tplTenantRelaHandler.deleteById(e));
        } catch (Exception e) {
            log.error(ExceptionUtils.getTrace(e));
            throw new SiteException(InnerError.getCode(), InnerError.getErrMsg());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void revokeTemplateInDB(List<String> delAsyncQueueGroupDOList,
                                   List<String> delDataParserDOList,
                                   List<String> delPipelineDOList,
                                   List<String> delCollectorDOList,
                                   List<String> delCollectorFieldDOList,
                                   List<String> delMetricsDOList,
                                   List<String> delAlarmDefinitionList,
                                   List<String> delTplTenantRelaDOList,
                                   RevokeTplInput revokeTplInput
    ) {
        try {
            log.info("<=====================start log key component when applying template in db operation====================>");
            log.info("delAsyncQueueGroupDOList = {}", JsonUtils.toJsonStringIgnoreExp(delAsyncQueueGroupDOList));
            log.info("delDataParserDOList = {}", JsonUtils.toJsonStringIgnoreExp(delDataParserDOList));
            log.info("delPipelineDOList = {}", JsonUtils.toJsonStringIgnoreExp(delPipelineDOList));
            log.info("delCollectorDOList = {}", JsonUtils.toJsonStringIgnoreExp(delCollectorDOList));
            log.info("delCollectorFieldDOList = {}", JsonUtils.toJsonStringIgnoreExp(delCollectorFieldDOList));
            log.info("delMetricsDOList = {}", JsonUtils.toJsonStringIgnoreExp(delMetricsDOList));
            log.info("delAlarmDefinitionList = {}", JsonUtils.toJsonStringIgnoreExp(delAlarmDefinitionList));
            log.info("delTplTenantRelaDOList = {}", JsonUtils.toJsonStringIgnoreExp(delTplTenantRelaDOList));

            log.info("<=====================end log key component when applying template in db operation====================>");

            if (!revokeTplInput.isApplyInDB()) {
                log.info("will not apply in DB");
                return;
            }
            delAsyncQueueGroupDOList.forEach(e -> asyncQueueGroupHandler.deleteAqGroupById(e));

            delDataParserDOList.forEach(e -> dataParserHandler.delDataParser(e));
            revokePublicInfo(delPipelineDOList, delCollectorDOList, delCollectorFieldDOList, delMetricsDOList, delAlarmDefinitionList, delTplTenantRelaDOList);
        } catch (Exception e) {
            log.error(ExceptionUtils.getTrace(e));
            throw new SiteException(InnerError.getCode(), InnerError.getErrMsg());
        }

    }

    @Transactional(rollbackFor = Exception.class)
    public void revokeTemplateInDB(List<String> delDataParserSourceDOList,
                                   List<String> delPipelineDOList,
                                   List<String> delCollectorDOList,
                                   List<String> delCollectorFieldDOList,
                                   List<String> delMetricsDOList,
                                   List<String> delAlarmDefinitionList,
                                   List<String> delTplTenantRelaDOList,
                                   RevokeTplInput revokeTplInput) {

        try {
            if (!revokeTplInput.isApplyInDB()) {
                log.info("will not apply in DB");
                return;
            }
            delDataParserSourceDOList.forEach(e -> dataParserSourceDAO.deleteById(e));
            revokePublicInfo(delPipelineDOList, delCollectorDOList, delCollectorFieldDOList, delMetricsDOList, delAlarmDefinitionList, delTplTenantRelaDOList);
        } catch (Exception e) {
            log.error(ExceptionUtils.getTrace(e));
            throw new SiteException(InnerError.getCode(), InnerError.getErrMsg());
        }

    }

    public TplMetricsSynTenantIdDTO queryNeedTplMetricsSynTenantIdsByMetricsId(String tplName, String collectorId, String metricsName, String tenantId, String userId, String destTenantScope, List<String> includeTenantNames, List<String> excludeTenantNames, boolean override) {
        TplMetricsSynTenantIdDTO tplMetricsSynTenantIdDTO = new TplMetricsSynTenantIdDTO();
        List<String> errorList = new ArrayList<>();
        List<String> tenantIds = queryTplRelationTenantIds(tplName, collectorId, tenantId, userId, includeTenantNames, excludeTenantNames, errorList);
        tenantIds = filterByTenantScope(includeTenantNames, destTenantScope, tenantIds, errorList);
        List<MetricsDO> existList = getExistMetricsTenantIds(metricsName, tenantIds);
        if (!override) {
            if (!CollectionUtils.isEmpty(existList)) {
                tenantIds.removeAll(existList.stream().map(e -> e.getTenantId()).collect(Collectors.toList()));
                tplMetricsSynTenantIdDTO.setNeedSkipList(existList.stream().map(e -> e.getTenantId()).collect(Collectors.toList()));
            }
            tplMetricsSynTenantIdDTO.setNeedAddList(tenantIds);
        } else {
            tplMetricsSynTenantIdDTO.setNeedAddList(tenantIds);
            tplMetricsSynTenantIdDTO.setNeedOverrideList(existList.stream().map(e -> new TplMetricsOverrideDTO(e.getId(), e.getTenantId())).collect(Collectors.toList()));
        }
        tplMetricsSynTenantIdDTO.setErrorList(errorList);
        return tplMetricsSynTenantIdDTO;
    }

    public TplPipelineSynTenantIdDTO queryNeedTplPipelineSynTenantIds(DataParserPipelineDO pipelineDO, String destTenantScope, List<String> includeTenantNames, List<String> excludeTenantNames, boolean override) {
        TplPipelineSynTenantIdDTO tplPipelineSynTenantIdDTO = new TplPipelineSynTenantIdDTO();
        List<String> errorList = new ArrayList<>();
        List<String> tenantIds = extractTplTenantIds(pipelineDO.getDataParserId(), includeTenantNames, excludeTenantNames, errorList);
        tenantIds = filterByTenantScope(includeTenantNames, destTenantScope, tenantIds, errorList);
        List<DataParserPipelineDO> existList = getExistPipelineTenantIds(pipelineDO.getName(), tenantIds);
        if (!override) {
            if (!CollectionUtils.isEmpty(existList)) {
                tenantIds.removeAll(existList.stream().map(e -> e.getTenantId()).collect(Collectors.toList()));
                tplPipelineSynTenantIdDTO.setNeedSkipList(existList.stream().map(e -> e.getTenantId()).collect(Collectors.toList()));
            }
            tplPipelineSynTenantIdDTO.setNeedAddList(tenantIds);
        } else {
            tplPipelineSynTenantIdDTO.setNeedAddList(tenantIds);
            tplPipelineSynTenantIdDTO.setNeedOverrideList(existList.stream().map(e -> new TplPipelineOverrideDTO(e.getId(), e.getTenantId())).collect(Collectors.toList()));
        }
        tplPipelineSynTenantIdDTO.setErrorList(errorList);
        return tplPipelineSynTenantIdDTO;
    }


    private List<String> queryTplRelationTenantIds(String tplName, String collectorId, String tenantId, String userId, List<String> includeTenantNames, List<String> excludeTenantNames, List<String> errorList) {
        String dataParserId = dataParserPipelineDAO.getDataParseIdByCollectorId(collectorId);
        Assert.isTrue(!StringUtils.isEmpty(dataParserId), "metrics not exist");
        DataParserDO dataParserDO = dataParserHandler.getById(dataParserId);
        Assert.isTrue(dataParserDO != null, "dataParse not exist");
        Assert.isTrue(dataParserDO.getName().equalsIgnoreCase(tplName), "metrics is not in the specific dataParser");
        Assert.isTrue(TemplateSceneEnum.isValidScene(dataParserDO.getTplScene() == null ? 0 : dataParserDO.getTplScene()), "only support template dataParser");
        Assert.isTrue(authService.isTenantExist(tenantId), "tenant not exists!");
//        authService.mustAdmin(userId);
        authService.checkAuth(userId, tenantId, AuthInterceptor.getApiPath());

        Map<String/**tenantId**/, String/**tenantName**/> tenantMap = new HashMap<>();
        List<String> includeTenants = new ArrayList<>();
        List<String> includeNames = new ArrayList<>(includeTenantNames == null ? new ArrayList<>() : includeTenantNames);
        if (!CollectionUtils.isEmpty(includeTenantNames)) {
            buildTenantByTenantNames(includeTenantNames).stream().forEach(e -> {
                tenantMap.put(e.getId(), e.getName());
                includeTenants.add(e.getId());
                includeNames.remove(e.getName());
            });
            errorList.addAll(includeNames);
        }
        List<String> excludeTenants = new ArrayList<>();
        List<String> excludeNames = new ArrayList<>(excludeTenantNames == null ? new ArrayList<>() : excludeTenantNames);
        if (!CollectionUtils.isEmpty(excludeTenantNames)) {
            buildTenantByTenantNames(excludeTenantNames).stream().forEach(e -> {
                tenantMap.put(e.getId(), e.getName());
                excludeTenants.add(e.getId());
                excludeNames.remove(e.getName());
            });
            errorList.addAll(excludeNames);
        }
        List<String> tenantIds = null;
        if (CollectionUtils.isEmpty(includeTenants) && !CollectionUtils.isEmpty(includeTenantNames)) {
            tenantIds = new ArrayList<>();
        } else {
            tenantIds = queryTenantIdByTplId(dataParserDO.getId(), includeTenants);
        }
        setErrorTenants(includeTenants, null, tenantIds, errorList, tenantMap);
        if (!CollectionUtils.isEmpty(excludeTenants)) {
            List<String> excludeTenantIds = queryTenantIdByTplId(dataParserDO.getId(), excludeTenants);
            setErrorTenants(null, excludeTenants, excludeTenantIds, errorList, tenantMap);
        }
        if (!CollectionUtils.isEmpty(tenantIds) && !CollectionUtils.isEmpty(excludeTenants)) {
            tenantIds.removeAll(excludeTenants);
        }
        return tenantIds;
    }

    private List<TenantInfoDO> buildTenantByTenantNames(List<String> tenantNames) {
        List<TenantInfoDO> result = Collections.synchronizedList(new ArrayList<>());
        List<List<String>> tenantNameList = Lists.partition(tenantNames, pageSize);
        tenantNameList.parallelStream().forEach(e -> {
            List<TenantInfoDO> tenantInfoDOS = tenantHandler.queryTenantInfoByTenantNames(e);
            if (!CollectionUtils.isEmpty(tenantInfoDOS)) {
                result.addAll(tenantInfoDOS);
            }
        });
        return result;
    }

    public List<TenantInfoDO> buildTenantByTenantIds(List<String> tenantIds) {
        List<TenantInfoDO> result = Collections.synchronizedList(new ArrayList<>());
        List<List<String>> tenantIdsList = Lists.partition(tenantIds, pageSize);
        tenantIdsList.parallelStream().forEach(e -> {
            List<TenantInfoDO> tenantInfoDOS = tenantHandler.queryTenantInfoByTenantIds(e);
            if (!CollectionUtils.isEmpty(tenantInfoDOS)) {
                result.addAll(tenantInfoDOS);
            }
        });
        return result;
    }


    private List<String> extractTplTenantIds(String tplId, List<String> includeTenantNames, List<String> excludeTenantNames, List<String> errorList) {
        Map<String/**tenantId**/, String/**tenantName**/> tenantMap = new HashMap<>();
        List<String> includeTenants = new ArrayList<>();
        List<String> includeNames = new ArrayList<>(includeTenantNames == null ? new ArrayList<>() : includeTenantNames);
        if (!CollectionUtils.isEmpty(includeTenantNames)) {
            buildTenantByTenantNames(includeTenantNames).stream().forEach(e -> {
                tenantMap.put(e.getId(), e.getName());
                includeTenants.add(e.getId());
                includeNames.remove(e.getName());
            });
            errorList.addAll(includeNames);
        }
        List<String> excludeTenants = new ArrayList<>();
        List<String> excludeNames = new ArrayList<>(excludeTenantNames == null ? new ArrayList<>() : excludeTenantNames);
        if (!CollectionUtils.isEmpty(excludeTenantNames)) {
            buildTenantByTenantNames(excludeTenantNames).stream().forEach(e -> {
                tenantMap.put(e.getId(), e.getName());
                excludeTenants.add(e.getId());
                excludeNames.remove(e.getName());
            });
            errorList.addAll(excludeNames);
        }
        List<String> tenantIds = queryTenantIdByTplId(tplId, includeTenants);
        setErrorTenants(includeTenants, null, tenantIds, errorList, tenantMap);
        if (!CollectionUtils.isEmpty(excludeTenants)) {
            List<String> excludeTenantIds = queryTenantIdByTplId(tplId, excludeTenants);
            setErrorTenants(null, excludeTenants, excludeTenantIds, errorList, tenantMap);
        }
        if (!CollectionUtils.isEmpty(tenantIds) && !CollectionUtils.isEmpty(excludeTenants)) {
            tenantIds.removeAll(excludeTenants);
        }
        return tenantIds;
    }


    private void setErrorTenants(List<String> includeTenants, List<String> excludeTenants, List<String> tenantIds, List<String> errorList, Map<String/**tenantId**/, String/**tenantName**/> tenantMap) {
        if (!CollectionUtils.isEmpty(includeTenants)) {
            Set<String> set = Sets.difference(Sets.newHashSet(includeTenants), Sets.newHashSet(tenantIds));
            if (!CollectionUtils.isEmpty(set)) {
                set.stream().forEach(e -> {
                    if (tenantMap.get(e) != null) {
                        errorList.add(tenantMap.get(e));
                    }
                });
            }
        }
        if (!CollectionUtils.isEmpty(excludeTenants)) {
            Set<String> set = Sets.difference(Sets.newHashSet(excludeTenants), Sets.newHashSet(tenantIds));
            if (!CollectionUtils.isEmpty(set)) {
                set.stream().forEach(e -> {
                    if (tenantMap.get(e) != null) {
                        errorList.add(tenantMap.get(e));
                    }
                });
            }
        }
    }


    private List<String> queryTenantIdByTplId(String tplId, List<String> includeTenants) {
        List<String> tenantIds = new ArrayList<>();
        List<TplTenantRelaDO> tplTenantRelaDOList = null;
        TplQueryDO tplQueryDO = new TplQueryDO();
        tplQueryDO.setTplId(tplId);
        tplQueryDO.setPageIndex(1);
        tplQueryDO.setPageSize(pageSize);
        if (!CollectionUtils.isEmpty(includeTenants)) {
            tplQueryDO.setTenantIds(includeTenants);
        }
        do {
            tplTenantRelaDOList = tplTenantRelaHandler.queryByTplId(tplQueryDO);
            if (!CollectionUtils.isEmpty(tplTenantRelaDOList)) {
                tenantIds.addAll(tplTenantRelaDOList.stream().map(e -> e.getTenantId()).collect(Collectors.toList()));
            }
            tplQueryDO.setPageIndex(tplQueryDO.getPageIndex() + 1);
        } while (!CollectionUtils.isEmpty(tplTenantRelaDOList) && tplTenantRelaDOList.size() == pageSize);
        return tenantIds;
    }


    private List<String> filterByTenantScope(List<String> includeTenantNames, String destTenantScope, List<String> tenantIds, List<String> errorList) {
        TenantType tenantType = TenantType.getTenantTypeByCode(destTenantScope);
        if (tenantType.equals(TenantType.ALL)) {
            return tenantIds;
        }
        List<String> result = Collections.synchronizedList(new ArrayList<>());
        List<Integer> types = TenantTypeUtil.getTypes(tenantType);
        List<List<String>> tenantList = Lists.partition(tenantIds, pageSize);
        tenantList.parallelStream().forEach(e -> {
            List<String> ids = tenantHandler.existTenantByIdsAndType(e, types);
            if (!CollectionUtils.isEmpty(includeTenantNames)) {
                Set<String> filterTenantIdList = Sets.difference(Sets.newHashSet(e), Sets.newHashSet(ids));
                List<String> filterTenantNameList = buildTenantByTenantIds(filterTenantIdList.stream().collect(Collectors.toList())).stream().map(TenantInfoDO::getName).collect(Collectors.toList());
                errorList.addAll(filterTenantNameList);
            }
            result.addAll(ids);
        });
        return result;
    }


    private List<MetricsDO> getExistMetricsTenantIds(String metricsName, List<String> tenantIds) {
        List<MetricsDO> result = Collections.synchronizedList(new ArrayList<>());
        List<List<String>> partition = Lists.partition(tenantIds, pageSize);
        partition.parallelStream().forEach(e -> {
            MetricsQueryDO metricsQueryDO = new MetricsQueryDO();
            metricsQueryDO.setPageIndex(1);
            metricsQueryDO.setPageSize(pageSize);
            metricsQueryDO.setMetricsName(metricsName);
            metricsQueryDO.setTenantIds(e);
            List<MetricsDO> metricsDOList = metricsHandler.queryExistTenantsByMetricsNameAndTenantIds(metricsQueryDO);
            if (!CollectionUtils.isEmpty(metricsDOList)) {
                result.addAll(metricsDOList);
            }
        });
        return result;
    }

    private List<DataParserPipelineDO> getExistPipelineTenantIds(String pipelineName, List<String> tenantIds) {
        List<DataParserPipelineDO> result = Collections.synchronizedList(new ArrayList<>());
        List<List<String>> partition = Lists.partition(tenantIds, pageSize);
        partition.parallelStream().forEach(e -> {
            PipelineQueryDO pipelineQueryDO = new PipelineQueryDO();
            pipelineQueryDO.setPageIndex(1);
            pipelineQueryDO.setPageSize(pageSize);
            pipelineQueryDO.setPipelineName(pipelineName);
            pipelineQueryDO.setTenantIds(e);
            List<DataParserPipelineDO> pipelineDOList = dataParserPipelineHandler.queryExistTenantsByPipelineNameAndTenantIds(pipelineQueryDO);
            if (!CollectionUtils.isEmpty(pipelineDOList)) {
                result.addAll(pipelineDOList);
            }
        });
        return result;
    }


    public TplAlarmSynTenantIdDTO queryNeedTplAlarmSynTenantIdsByMetricsIdAndAlarmName(String metricsId, String alarmName, String tenantId, String userId, String tplName, String destTenantScope, List<String> includeTenantNames, List<String> excludeTenantNames, boolean override) {
        TplAlarmSynTenantIdDTO tplAlarmSynTenantIdDTO = new TplAlarmSynTenantIdDTO();
        List<String> errorList = new ArrayList<>();
        String collectorId = collectorHandler.getCollectorIdByMetricsId(metricsId);
        List<String> tenantIds = queryTplRelationTenantIds(tplName, collectorId, tenantId, userId, includeTenantNames, excludeTenantNames, errorList);
        tenantIds = filterByTenantScope(includeTenantNames, destTenantScope, tenantIds, errorList);
        List<IdAndMetricIdAndTenantId> existList = getNoExistAlarmTenantIds(alarmName, tenantIds);
        if (!override) {
            if (!CollectionUtils.isEmpty(existList)) {
                tenantIds.removeAll(existList.stream().map(e -> e.getTenantId()).collect(Collectors.toList()));
                tplAlarmSynTenantIdDTO.setNeedSkipList(existList.stream().map(e -> e.getTenantId()).collect(Collectors.toList()));
            }
            tplAlarmSynTenantIdDTO.setNeedAddList(tenantIds);
        } else {
            tplAlarmSynTenantIdDTO.setNeedAddList(tenantIds);
            tplAlarmSynTenantIdDTO.setNeedOverrideList(existList.stream().map(e -> new TplAlarmOverrideDTO(e.getMetricId(), e.getId(), e.getTenantId())).collect(Collectors.toList()));
        }
        tplAlarmSynTenantIdDTO.setErrorList(errorList);
        return tplAlarmSynTenantIdDTO;
    }


    private List<IdAndMetricIdAndTenantId> getNoExistAlarmTenantIds(String alarmName, List<String> tenantIds) {
        List<IdAndMetricIdAndTenantId> result = Collections.synchronizedList(new ArrayList<>());
        List<List<String>> partition = Lists.partition(tenantIds, pageSize);
        partition.parallelStream().forEach(e -> {
            List<IdAndMetricIdAndTenantId> ids = alarmDefinitionDao.findByAlarmNameAndTenantId(alarmName, e);
            if (!CollectionUtils.isEmpty(ids)) {
                result.addAll(ids);
            }
        });
        return result;
    }

    public List<PipelineSynDO> buildTplRelationShipTenantPipeline(String tplName, DataParserPipelineDO pipelineDO, String destTenantScope, List<String> includeTenantNames, List<String> excludeTenantNames, boolean override) {
        List<PipelineSynDO> list = new ArrayList();
        TplPipelineSynTenantIdDTO tplPipelineSynTenantIdDTO = queryNeedTplPipelineSynTenantIds(pipelineDO, destTenantScope, includeTenantNames, excludeTenantNames, override);
        if (CollectionUtils.isEmpty(tplPipelineSynTenantIdDTO.getNeedAddList())
                && CollectionUtils.isEmpty(tplPipelineSynTenantIdDTO.getNeedOverrideList())
                && CollectionUtils.isEmpty(tplPipelineSynTenantIdDTO.getNeedSkipList())
                && CollectionUtils.isEmpty(tplPipelineSynTenantIdDTO.getErrorList())) {
            return list;
        }
        // check dataParser in dest tenants
        Set<String> checkFailedTenantIdList = tplPipelineSynTenantIdDTO.getNeedAddList().stream().filter(id -> {
            DataParserDO tplDataParserDO = dataParserService.getDataParserByTenantIdAndName(id, tplName);
            return tplDataParserDO == null;
        }).collect(Collectors.toSet());
        List<String> checkFailedTenantNameList = buildTenantByTenantIds(checkFailedTenantIdList.stream().collect(Collectors.toList())).stream().map(TenantInfoDO::getName).collect(Collectors.toList());
        tplPipelineSynTenantIdDTO.getNeedAddList().removeAll(checkFailedTenantIdList);
        tplPipelineSynTenantIdDTO.getErrorList().addAll(checkFailedTenantNameList);
        List<PipelineSynDO> result = buildTenantPipelineList(tplName, tplPipelineSynTenantIdDTO, pipelineDO);
        if (!CollectionUtils.isEmpty(result)) {
            list.addAll(result);
        }
        return list;
    }

    private List<PipelineSynDO> buildTenantPipelineList(String tplName, TplPipelineSynTenantIdDTO tplPipelineSynTenantIdDTO, DataParserPipelineDO sourcePipeline) {
        List<PipelineSynDO> pipelineSynDOList = new ArrayList<>();
        Set<String> tenantSet = new HashSet<>();
        Set<String> errorTenantSet = new HashSet<>();
        if (!CollectionUtils.isEmpty(tplPipelineSynTenantIdDTO.getNeedAddList())) {
            tenantSet.addAll(tplPipelineSynTenantIdDTO.getNeedAddList());
        }
        if (!CollectionUtils.isEmpty(tplPipelineSynTenantIdDTO.getNeedSkipList())) {
            tenantSet.addAll(tplPipelineSynTenantIdDTO.getNeedSkipList());
        }
        Map<String, TplPipelineOverrideDTO> overrideDTOMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(tplPipelineSynTenantIdDTO.getNeedOverrideList())) {
            overrideDTOMap.putAll(tplPipelineSynTenantIdDTO.getNeedOverrideList().stream().collect(Collectors.toMap(TplPipelineOverrideDTO::getTenantId, e -> e)));
            tenantSet.addAll(tplPipelineSynTenantIdDTO.getNeedOverrideList().stream().map(e -> e.getTenantId()).collect(Collectors.toList()));
        }

        if (!CollectionUtils.isEmpty(tplPipelineSynTenantIdDTO.getErrorList())) {
            errorTenantSet.addAll(tplPipelineSynTenantIdDTO.getErrorList());
        }
        Map<String/**tenantId**/, String/**tenantName**/> tenantMap = new HashMap<>();
        List<TenantInfoDO> tenantInfoDOS = buildTenantByTenantIds(new ArrayList<>(tenantSet));
        tenantMap.putAll(tenantInfoDOS.stream().collect(Collectors.toMap(TenantInfoDO::getId, e -> e.getName())));

        if (!CollectionUtils.isEmpty(tenantSet)) {
            tenantSet.stream().forEach(e -> {
                PipelineSynDO pipelineSynDO = new PipelineSynDO();
                if (tplPipelineSynTenantIdDTO.getNeedAddList().contains(e)) {
                    List<DataParserPipelineDO> addPipelineDOList = Lists.newArrayList();
                    List<DataParserRemapperProcessorDO> addDataParserRemapperProcessorDOList = Lists.newArrayList();
                    List<DataParserFilterProcessorDO> addDataParserFilterProcessorDOList = Lists.newArrayList();
                    List<DataParserGroovyProcessorDO> addDataParserGroovyProcessorDOList = Lists.newArrayList();
                    List<DataParserGrokProcessorDO> addDataParserGrokProcessorDOList = Lists.newArrayList();
                    List<DataParserEncryptionProcessorDO> addDataParserEncryptionProcessorDOList = Lists.newArrayList();
                    List<DataParserLabelMysqlProcessorDO> addDataParserLabelMysqlProcessorDOList = Lists.newArrayList();
                    List<DataParserLabelRedisProcessorDO> addDataParserLabelRedisProcessorDOList = Lists.newArrayList();
                    List<CollectorDO> addCollectorDOList = Lists.newArrayList();
                    List<CollectorFieldDO> addCollectorFieldDOList = Lists.newArrayList();

                    DataParserDO destDataParserDO = dataParserService.getDataParserByTenantIdAndName(e, tplName);
                    DataParserPipelineDO destPipelineDO = copyPipeline(sourcePipeline, destDataParserDO, addPipelineDOList, null, true, false);
                    copyProcessor(sourcePipeline, destPipelineDO.getId(), addDataParserEncryptionProcessorDOList, addDataParserFilterProcessorDOList,
                            addDataParserGrokProcessorDOList, addDataParserGroovyProcessorDOList, addDataParserLabelMysqlProcessorDOList,
                            addDataParserLabelRedisProcessorDOList, addDataParserRemapperProcessorDOList);

                    String sourceCollectorId = sourcePipeline.getCollectorId();
                    if (StringUtils.isNotEmpty(sourceCollectorId)) {
                        CollectorDO destCollectorDO = copyCollector(sourcePipeline, destDataParserDO.getTenantId(), destPipelineDO, addCollectorDOList, true);
                        copyCollectorFields(sourceCollectorId, destCollectorDO.getId(), addCollectorFieldDOList, true);
                    }

                    PipelineSynAddDO pipelineSynAddDO = new PipelineSynAddDO();

                    pipelineSynAddDO.setAddPipelineDOList(addPipelineDOList);
                    pipelineSynAddDO.setAddDataParserRemapperProcessorDOList(addDataParserRemapperProcessorDOList);
                    pipelineSynAddDO.setAddDataParserFilterProcessorDOList(addDataParserFilterProcessorDOList);
                    pipelineSynAddDO.setAddDataParserGroovyProcessorDOList(addDataParserGroovyProcessorDOList);
                    pipelineSynAddDO.setAddDataParserGrokProcessorDOList(addDataParserGrokProcessorDOList);
                    pipelineSynAddDO.setAddDataParserEncryptionProcessorDOList(addDataParserEncryptionProcessorDOList);
                    pipelineSynAddDO.setAddDataParserLabelMysqlProcessorDOList(addDataParserLabelMysqlProcessorDOList);
                    pipelineSynAddDO.setAddDataParserLabelRedisProcessorDOList(addDataParserLabelRedisProcessorDOList);
                    pipelineSynAddDO.setAddCollectorDOList(addCollectorDOList);
                    pipelineSynAddDO.setAddCollectorFieldDOList(addCollectorFieldDOList);
                    pipelineSynAddDO.setTenantName(tenantMap.get(e));
                    pipelineSynDO.setPipelineSynAddDO(pipelineSynAddDO);
                }
                if (tplPipelineSynTenantIdDTO.getNeedSkipList().contains(e)) {
                    PipelineSynSkipDO pipelineSynSkipDO = new PipelineSynSkipDO();
                    pipelineSynSkipDO.setTenantId(e);
                    pipelineSynSkipDO.setTenantName(tenantMap.get(e));
                    pipelineSynDO.setPipelineSynSkipDO(pipelineSynSkipDO);
                }
                if (overrideDTOMap.containsKey(e)) {
                    TplPipelineOverrideDTO tplPipelineOverrideDTO = overrideDTOMap.get(e);
                    PipelineSynUpdateDO pipelineSynUpdateDO = new PipelineSynUpdateDO();
                    pipelineSynUpdateDO.setPipelineId(tplPipelineOverrideDTO.getPipelineId());
                    pipelineSynUpdateDO.setTenantId(e);
                    pipelineSynUpdateDO.setTenantName(tenantMap.get(e));
                    pipelineSynDO.setPipelineSynUpdateDO(pipelineSynUpdateDO);
                }
                pipelineSynDOList.add(pipelineSynDO);
            });
        }
        if (!CollectionUtils.isEmpty(errorTenantSet)) {
            errorTenantSet.stream().forEach(e -> {
                if (tplPipelineSynTenantIdDTO.getErrorList().contains(e)) {
                    PipelineSynDO pipelineSynDO = new PipelineSynDO();
                    PipelineSynErrorDO pipelineSynErrorDO = new PipelineSynErrorDO();
                    pipelineSynErrorDO.setTenantName(e);
                    pipelineSynDO.setPipelineSynErrorDO(pipelineSynErrorDO);
                    pipelineSynDOList.add(pipelineSynDO);
                }
            });
        }
        return pipelineSynDOList;
    }

    public void copyProcessor(DataParserPipelineDO sourcePipeline, String destPipelineId, List<DataParserEncryptionProcessorDO> addDataParserEncryptionProcessorDOList,
                              List<DataParserFilterProcessorDO> addDataParserFilterProcessorDOList, List<DataParserGrokProcessorDO> addDataParserGrokProcessorDOList,
                              List<DataParserGroovyProcessorDO> addDataParserGroovyProcessorDOList, List<DataParserLabelMysqlProcessorDO> addDataParserLabelMysqlProcessorDOList,
                              List<DataParserLabelRedisProcessorDO> addDataParserLabelRedisProcessorDOList, List<DataParserRemapperProcessorDO> addDataParserRemapperProcessorDOList) {
        copyEncryption(sourcePipeline, destPipelineId, addDataParserEncryptionProcessorDOList);
        copyFilter(sourcePipeline, destPipelineId, addDataParserFilterProcessorDOList);
        copyGrok(sourcePipeline, destPipelineId, addDataParserGrokProcessorDOList);
        copyGroovy(sourcePipeline, destPipelineId, addDataParserGroovyProcessorDOList);
        copyLabelMysql(sourcePipeline, destPipelineId, addDataParserLabelMysqlProcessorDOList);
        copyLabelRedis(sourcePipeline, destPipelineId, addDataParserLabelRedisProcessorDOList);
        copyRemapper(sourcePipeline, destPipelineId, addDataParserRemapperProcessorDOList);
    }

    public void copyProcessor(DataParserPipelineDO sourcePipeline, String destPipelineId, List<DataParserFilterProcessorDO> addDataParserFilterProcessorDOList,
                              List<DataParserGrokProcessorDO> addDataParserGrokProcessorDOList, List<DataParserGroovyProcessorDO> addDataParserGroovyProcessorDOList,
                              List<DataParserRemapperProcessorDO> addDataParserRemapperProcessorDOList) {
        copyFilter(sourcePipeline, destPipelineId, addDataParserFilterProcessorDOList);
        copyGrok(sourcePipeline, destPipelineId, addDataParserGrokProcessorDOList);
        copyGroovy(sourcePipeline, destPipelineId, addDataParserGroovyProcessorDOList);
        copyRemapper(sourcePipeline, destPipelineId, addDataParserRemapperProcessorDOList);
    }


    public void copyLabelRedis(DataParserPipelineDO sourcePipeline, String destPipelineId, List<DataParserLabelRedisProcessorDO> addDataParserLabelRedisProcessorDOList) {
        List<DataParserLabelRedisProcessorDO> sourceProcessorDOList = dataParserLabelRedisProcessorHandler.listByPipelineIds(Arrays.asList(sourcePipeline.getId()));
        List<DataParserLabelRedisProcessorDO> destParserProcessorDOList = dataParserLabelRedisProcessorHandler.listByPipelineIds(Arrays.asList(destPipelineId));
        if (!CollectionUtils.isEmpty(sourceProcessorDOList)) {
            Assert.isTrue(false, "redis label processor should not be appear in template dataParser");
        }
//        if (CollectionUtils.isEmpty(destParserProcessorDOList)) {
//            sourceProcessorDOList.forEach(remapperProcessor -> {
//                DataParserLabelRedisProcessorDO destProcessor = new DataParserLabelRedisProcessorDO();
//                BeanUtils.copyProperties(remapperProcessor, destProcessor);
//                destProcessor.setId(IdUtils.generateId());
//                destProcessor.setDataParserPipelineId(destPipelineId);
//                destProcessor.setGmtCreate(new Date());
//                destProcessor.setGmtModify(new Date());
//                addDataParserLabelRedisProcessorDOList.add(destProcessor);
//            });
//
//        } else {
//            // just check source and dest, and throw exception
//            if (sourceProcessorDOList.size() != destParserProcessorDOList.size()) {
//                Assert.isTrue(false, "sourceLabelRedisProcessorDOList.size() != destParserLabelRedisProcessorDOList.size()");
//            }
//            sourceProcessorDOList.forEach(e -> {
//                boolean exist = destParserProcessorDOList.stream().anyMatch(t -> {
//                    return t.getName().equals(e.getName()) && t.getOrder().equals(e.getOrder());
//                });
//                if (!exist) {
//                    Assert.isTrue(false, "sourceLabelRedisProcessorDOList are not equal to destParserLabelRedisProcessorDOList");
//                }
//            });
//        }
    }

    public void copyLabelMysql(DataParserPipelineDO sourcePipeline, String destPipelineId, List<DataParserLabelMysqlProcessorDO> addDataParserLabelMysqlProcessorDOList) {
        List<DataParserLabelMysqlProcessorDO> sourceProcessorDOList = dataParserLabelMysqlProcessorHandler.listByPipelineIds(Arrays.asList(sourcePipeline.getId()));
        List<DataParserLabelMysqlProcessorDO> destParserProcessorDOList = dataParserLabelMysqlProcessorHandler.listByPipelineIds(Arrays.asList(destPipelineId));
        if (!CollectionUtils.isEmpty(sourceProcessorDOList)) {
            Assert.isTrue(false, "mysql label processor should not be appear in template dataParser");
        }
//        if (CollectionUtils.isEmpty(destParserProcessorDOList)) {
//            sourceProcessorDOList.forEach(remapperProcessor -> {
//                DataParserLabelMysqlProcessorDO destProcessor = new DataParserLabelMysqlProcessorDO();
//                BeanUtils.copyProperties(remapperProcessor, destProcessor);
//                destProcessor.setId(IdUtils.generateId());
//                destProcessor.setDataParserPipelineId(destPipelineId);
//                destProcessor.setGmtCreate(new Date());
//                destProcessor.setGmtModify(new Date());
//                addDataParserLabelMysqlProcessorDOList.add(destProcessor);
//            });
//
//        } else {
//            // just check source and dest, and throw exception
//            if (sourceProcessorDOList.size() != destParserProcessorDOList.size()) {
//                Assert.isTrue(false, "sourceLabelMysqlProcessorDOList.size() != destParserLabelMysqlProcessorDOList.size()");
//            }
//            sourceProcessorDOList.forEach(e -> {
//                boolean exist = destParserProcessorDOList.stream().anyMatch(t -> {
//                    return t.getName().equals(e.getName()) && t.getOrder().equals(e.getOrder());
//                });
//                if (!exist) {
//                    Assert.isTrue(false, "sourceLabelMysqlProcessorDOList are not equal to destParserLabelMysqlProcessorDOList");
//                }
//            });
//        }
    }


    public void copyGroovy(DataParserPipelineDO sourcePipeline, String destPipelineId, List<DataParserGroovyProcessorDO> addDataParserGroovyProcessorDOList) {
        List<DataParserGroovyProcessorDO> sourceProcessorDOList = dataParserGroovyProcessorHandler.listByPipelineIds(Arrays.asList(sourcePipeline.getId()));
        List<DataParserGroovyProcessorDO> destParserProcessorDOList = dataParserGroovyProcessorHandler.listByPipelineIds(Arrays.asList(destPipelineId));
        if (CollectionUtils.isEmpty(sourceProcessorDOList)) {
            return;
        }
        if (CollectionUtils.isEmpty(destParserProcessorDOList)) {
            sourceProcessorDOList.forEach(remapperProcessor -> {
                DataParserGroovyProcessorDO destProcessor = new DataParserGroovyProcessorDO();
                BeanUtils.copyProperties(remapperProcessor, destProcessor);
                destProcessor.setId(IdUtils.generateId());
                destProcessor.setDataParserPipelineId(destPipelineId);
                destProcessor.setGmtCreate(new Date());
                destProcessor.setGmtModify(new Date());
                addDataParserGroovyProcessorDOList.add(destProcessor);
            });

        } else {
            // just check source and dest, and throw exception
            if (sourceProcessorDOList.size() != destParserProcessorDOList.size()) {
                Assert.isTrue(false, "sourceGroovyProcessorDOList.size() != destParserGroovyProcessorDOList.size()");
            }
            sourceProcessorDOList.forEach(e -> {
                boolean exist = destParserProcessorDOList.stream().anyMatch(t -> {
                    return t.getName().equals(e.getName()) && t.getOrder().equals(e.getOrder());
                });
                if (!exist) {
                    Assert.isTrue(false, "sourceGroovyProcessorDOList are not equal to destParserGroovyProcessorDOList");
                }
            });
        }
    }


    private void copyGrok(DataParserPipelineDO sourcePipeline, String destPipelineId, List<DataParserGrokProcessorDO> addDataParserGrokProcessorDOList) {
        List<DataParserGrokProcessorDO> sourceProcessorDOList = dataParserGrokProcessorHandler.listByPipelineIds(Arrays.asList(sourcePipeline.getId()));
        List<DataParserGrokProcessorDO> destParserProcessorDOList = dataParserGrokProcessorHandler.listByPipelineIds(Arrays.asList(destPipelineId));
        if (CollectionUtils.isEmpty(sourceProcessorDOList)) {
            return;
        }
        if (CollectionUtils.isEmpty(destParserProcessorDOList)) {
            sourceProcessorDOList.forEach(remapperProcessor -> {
                DataParserGrokProcessorDO destProcessor = new DataParserGrokProcessorDO();
                BeanUtils.copyProperties(remapperProcessor, destProcessor);
                destProcessor.setId(IdUtils.generateId());
                destProcessor.setDataParserPipelineId(destPipelineId);
                destProcessor.setGmtCreate(new Date());
                destProcessor.setGmtModify(new Date());
                addDataParserGrokProcessorDOList.add(destProcessor);
            });

        } else {
            // just check source and dest, and throw exception
            if (sourceProcessorDOList.size() != destParserProcessorDOList.size()) {
                Assert.isTrue(false, "sourceGrokProcessorDOList.size() != destParserGrokProcessorDOList.size()");
            }
            sourceProcessorDOList.forEach(e -> {
                boolean exist = destParserProcessorDOList.stream().anyMatch(t -> {
                    return t.getName().equals(e.getName()) && t.getOrder().equals(e.getOrder());
                });
                if (!exist) {
                    Assert.isTrue(false, "sourceGrokProcessorDOList are not equal to destParserGrokProcessorDOList");
                }
            });
        }
    }

    public void copyEncryption(DataParserPipelineDO sourcePipeline, String destPipelineId, List<DataParserEncryptionProcessorDO> addDataParserEncryptionProcessorDOList) {
        List<DataParserEncryptionProcessorDO> sourceProcessorDOList = dataParserEncryptionProcessorHandler.listByPipelineIds(Arrays.asList(sourcePipeline.getId()));
        List<DataParserEncryptionProcessorDO> destParserProcessorDOList = dataParserEncryptionProcessorHandler.listByPipelineIds(Arrays.asList(destPipelineId));
        if (CollectionUtils.isEmpty(sourceProcessorDOList)) {
            return;
        }
        if (CollectionUtils.isEmpty(destParserProcessorDOList)) {
            sourceProcessorDOList.forEach(remapperProcessor -> {
                DataParserEncryptionProcessorDO destProcessor = new DataParserEncryptionProcessorDO();
                BeanUtils.copyProperties(remapperProcessor, destProcessor);
                destProcessor.setId(IdUtils.generateId());
                destProcessor.setDataParserPipelineId(destPipelineId);
                destProcessor.setGmtCreate(new Date());
                destProcessor.setGmtModify(new Date());
                addDataParserEncryptionProcessorDOList.add(destProcessor);
            });

        } else {
            // just check source and dest, and throw exception
            if (sourceProcessorDOList.size() != destParserProcessorDOList.size()) {
                Assert.isTrue(false, "sourceRemapperProcessorDOList.size() != destParserRemapperProcessorDOList.size()");
            }
            sourceProcessorDOList.forEach(e -> {
                boolean exist = destParserProcessorDOList.stream().anyMatch(t -> {
                    return t.getName().equals(e.getName()) && t.getOrder().equals(e.getOrder());
                });
                if (!exist) {
                    Assert.isTrue(false, "sourceRemapperProcessorDOList are not equal to destParserRemapperProcessorDOList");
                }
            });
        }
    }

    public void copyRemapper(DataParserPipelineDO sourcePipeline, String destPipelineId, List<DataParserRemapperProcessorDO> addDataParserRemapperProcessorDOList) {
        List<DataParserRemapperProcessorDO> sourceRemapperProcessorDOList = dataParserRemapperProcessorHandler.listByPipelineIds(Arrays.asList(sourcePipeline.getId()));
        List<DataParserRemapperProcessorDO> destParserRemapperProcessorDOList = dataParserRemapperProcessorHandler.listByPipelineIds(Arrays.asList(destPipelineId));
        if (CollectionUtils.isEmpty(sourceRemapperProcessorDOList)) {
            return;
        }
        if (CollectionUtils.isEmpty(destParserRemapperProcessorDOList)) {
            sourceRemapperProcessorDOList.forEach(remapperProcessor -> {
                DataParserRemapperProcessorDO destProcessor = new DataParserRemapperProcessorDO();
                BeanUtils.copyProperties(remapperProcessor, destProcessor);
                destProcessor.setId(IdUtils.generateId());
                destProcessor.setDataParserPipelineId(destPipelineId);
                destProcessor.setGmtCreate(new Date());
                destProcessor.setGmtModify(new Date());
                addDataParserRemapperProcessorDOList.add(destProcessor);
            });

        } else {
            // just check source and dest, and throw exception
            if (sourceRemapperProcessorDOList.size() != destParserRemapperProcessorDOList.size()) {
                Assert.isTrue(false, "sourceRemapperProcessorDOList.size() != destParserRemapperProcessorDOList.size()");
            }
            sourceRemapperProcessorDOList.forEach(e -> {
                boolean exist = destParserRemapperProcessorDOList.stream().anyMatch(t -> {
                    return t.getName().equals(e.getName()) && t.getOrder().equals(e.getOrder());
                });
                if (!exist) {
                    Assert.isTrue(false, "sourceRemapperProcessorDOList are not equal to destParserRemapperProcessorDOList");
                }
            });
        }
    }

    public void copyFilter(DataParserPipelineDO sourcePipeline, String destPipelineId, List<DataParserFilterProcessorDO> addDataParserFilterProcessorDOList) {

        List<DataParserFilterProcessorDO> sourceFilterProcessorDOList = dataParserFilterProcessorHandler.listByPipelineIds(Arrays.asList(sourcePipeline.getId()));
        List<DataParserFilterProcessorDO> destFilterProcessorDOList = dataParserFilterProcessorHandler.listByPipelineIds(Arrays.asList(destPipelineId));
        if (CollectionUtils.isEmpty(sourceFilterProcessorDOList)) {
            return;
        }
        if (CollectionUtils.isEmpty(destFilterProcessorDOList)) {
            sourceFilterProcessorDOList.forEach(filterProcessor -> {
                DataParserFilterProcessorDO destProcessor = new DataParserFilterProcessorDO();
                BeanUtils.copyProperties(filterProcessor, destProcessor);
                destProcessor.setId(IdUtils.generateId());
                destProcessor.setDataParserPipelineId(destPipelineId);
                destProcessor.setGmtCreate(new Date());
                destProcessor.setGmtModify(new Date());
                addDataParserFilterProcessorDOList.add(destProcessor);
            });

        } else {
            // just check source and dest, and throw exception
            if (sourceFilterProcessorDOList.size() != destFilterProcessorDOList.size()) {
                Assert.isTrue(false, "sourceFilterProcessorDOList.size() != destFilterProcessorDOList.size()");
            }
            sourceFilterProcessorDOList.forEach(e -> {
                boolean exist = destFilterProcessorDOList.stream().anyMatch(t -> {
                    return t.getName().equals(e.getName()) && t.getOrder().equals(e.getOrder());
                });
                if (!exist) {
                    Assert.isTrue(false, "sourceFilterProcessorDOList are not equal to destFilterProcessorDOList");
                }
            });
        }
    }

    public CollectorDO copyCollector(DataParserPipelineDO sourcePipeline, String tenantId, DataParserPipelineDO destPipeLine, List<CollectorDO> addCollectorDOList, Boolean interruptWhenExist) {
        if (org.springframework.util.StringUtils.isEmpty(sourcePipeline.getCollectorId())) {
            return null;
        }

        CollectorDO sourceCollector = collectorHandler.getCollectorById(sourcePipeline.getCollectorId());
        if (null == sourceCollector) {
            return null;
        }

        String destCollectorId = destPipeLine.getCollectorId();
        CollectorDO destCollector = null;

        if (org.springframework.util.StringUtils.isEmpty(destCollectorId)) {
            destCollectorId = IdUtils.generateId();
            destCollector = new CollectorDO();
            addCopyCollector(destCollectorId, destCollector, sourceCollector, tenantId, addCollectorDOList, destPipeLine);
        } else {
            destCollector = collectorHandler.getCollectorById(destCollectorId);
            if (destCollector == null) {
                Assert.isTrue(false, "destCollector is missing ! The collectorId = " + destCollectorId);
            } else {
                log.info("already exist dest collector, collectorId = {}", destCollectorId);
                if (interruptWhenExist) {
                    Assert.isTrue(false, "already exist dest collector, collectorId = " + destCollectorId);
                }
            }

        }
        return destCollector;
    }

    public void addCopyCollector(String destCollectorId, CollectorDO destCollector, CollectorDO sourceCollector, String tenantId,
                                 List<CollectorDO> addCollectorDOList, DataParserPipelineDO destPipeLine) {
        BeanUtils.copyProperties(sourceCollector, destCollector);
        destCollector.setModifyTime(new Date());
        destCollector.setCreateTime(new Date());
        destCollector.setTenantId(tenantId);
        destCollector.setId(destCollectorId);
        addCollectorDOList.add(destCollector);
        destPipeLine.setCollectorId(destCollectorId);
    }

    public void copyCollectorFields(String sourceCollectorId, String destCollectorId, List<CollectorFieldDO> addCollectorFieldDOList, Boolean interruptWhenExist) {
        List<CollectorFieldDO> sourceFields = collectorHandler.listFieldByCollectorIds(Arrays.asList(sourceCollectorId));
        if (CollectionUtils.isEmpty(sourceFields)) {
            return;
        }

        List<CollectorFieldDO> destFields = collectorHandler.listFieldByCollectorIds(Arrays.asList(destCollectorId));
        Set<String> destFieldsName = Instance.ofNullable(destFields).stream().map(item -> item.getSourceField()).collect(Collectors.toSet());

        for (CollectorFieldDO sourceField : sourceFields) {
            if (destFieldsName.contains(sourceField.getSourceField())) {
                log.info("already exists collectorField, name = {}", sourceField.getSourceField());
                if (interruptWhenExist) {
                    Assert.isTrue(false, "already exists collectorField, name = " + sourceField.getSourceField());
                }
                continue;
            }
            CollectorFieldDO destField = new CollectorFieldDO();
            addCopyCollectorFields(destField, sourceField, destCollectorId, addCollectorFieldDOList);
        }
    }

    public void addCopyCollectorFields(CollectorFieldDO destField, CollectorFieldDO sourceField, String destCollectorId, List<CollectorFieldDO> addCollectorFieldDOList) {
        BeanUtils.copyProperties(sourceField, destField);
        destField.setCollectorId(destCollectorId);
        destField.setCreateTime(new Date());
        destField.setModifyTime(new Date());
        destField.setId(IdUtils.generateId());
        addCollectorFieldDOList.add(destField);
    }

    public DataParserPipelineDO copyPipeline(DataParserPipelineDO sourcePipeline, DataParserDO
            destDataParser, List<DataParserPipelineDO> addDataParserPipelineDOList, List<DataParserPipelineDO> updateDataParserPipelineDOList,
                                             Boolean interruptWhenExist, Boolean overridePipeline) {
        String name = sourcePipeline.getName();
        String destTenantId = destDataParser.getTenantId();
        DataParserPipelineDO destPipeline = dataParserPipelineHandler.getByTenantIdAndName(destTenantId, name);
        destPipeline = addPipelineDOList(destPipeline, sourcePipeline, addDataParserPipelineDOList, updateDataParserPipelineDOList, interruptWhenExist,
                overridePipeline, name, destDataParser.getId(), destTenantId);
        return destPipeline;
    }

    private DataParserPipelineDO addPipelineDOList(DataParserPipelineDO destPipeline, DataParserPipelineDO sourcePipeline, List<DataParserPipelineDO>
            addDataParserPipelineDOList, List<DataParserPipelineDO> updateDataParserPipelineDOList, Boolean interruptWhenExist,
                                                   Boolean overridePipeline, String name, String id, String destTenantId) {
        if (null == destPipeline) {
            destPipeline = new DataParserPipelineDO();
            addCopyPipelineDO(destPipeline, sourcePipeline, id, destTenantId, addDataParserPipelineDOList);
        } else {
            log.info("already exists pipeline, name = {}", name);
            if (interruptWhenExist) {
                Assert.isTrue(false, "already exists pipeline, name = " + name);
            }
            if (overridePipeline) {
                DataParserPipelineDO tmpDO = new DataParserPipelineDO();
                updateCopyPipelineDO(tmpDO, destPipeline, sourcePipeline, updateDataParserPipelineDOList);
            }
        }
        return destPipeline;
    }

    public void addCopyPipelineDO(DataParserPipelineDO destPipeline, DataParserPipelineDO sourcePipeline, String id,
                                  String destTenantId, List<DataParserPipelineDO> addDataParserPipelineDOList) {
        BeanUtils.copyProperties(sourcePipeline, destPipeline);
        destPipeline.setDataParserId(id);
        destPipeline.setGmtCreate(new Date());
        destPipeline.setGmtModify(new Date());
        destPipeline.setTenantId(destTenantId);
        destPipeline.setId(IdUtils.generateId());
        destPipeline.setCollectorId("");
        addDataParserPipelineDOList.add(destPipeline);
    }

    public void updateCopyPipelineDO(DataParserPipelineDO tmpDO, DataParserPipelineDO destPipeline, DataParserPipelineDO sourcePipeline,
                                     List<DataParserPipelineDO> updateDataParserPipelineDOList) {
        BeanUtils.copyProperties(destPipeline, tmpDO);
        BeanUtils.copyProperties(sourcePipeline, destPipeline);
        destPipeline.setId(tmpDO.getId());
        destPipeline.setDataParserId(tmpDO.getDataParserId());
        destPipeline.setCollectorId(tmpDO.getCollectorId());
        destPipeline.setGmtModify(new Date());
        destPipeline.setOrder(tmpDO.getOrder());
        destPipeline.setParentId(tmpDO.getParentId());
        destPipeline.setTenantId(tmpDO.getTenantId());
        destPipeline.setCollectorId(tmpDO.getCollectorId());
        updateDataParserPipelineDOList.add(destPipeline);
    }

    public DataParserPipelineDO copyPipeline(DataParserPipelineDO sourcePipeline, DataParserSourceDO
            dataParserSourceDO, List<DataParserPipelineDO> addDataParserPipelineDOList, List<DataParserPipelineDO> updateDataParserPipelineDOList,
                                             Boolean interruptWhenExist, Boolean overridePipeline) {
        String name = sourcePipeline.getName();
        String destTenantId = dataParserSourceDO.getTenantId();
        DataParserPipelineDO destPipeline = dataParserPipelineHandler.getByTenantIdAndName(destTenantId, name);
        destPipeline = addPipelineDOList(destPipeline, sourcePipeline, addDataParserPipelineDOList, updateDataParserPipelineDOList, interruptWhenExist,
                overridePipeline, name, dataParserSourceDO.getId(), destTenantId);
        return destPipeline;
    }

    @Transactional(rollbackFor = Exception.class)
    public void syncAddPipelineInDB(
            List<DataParserPipelineDO> addPipelineDOList,
            List<DataParserRemapperProcessorDO> addDataParserRemapperProcessorDOList,
            List<DataParserFilterProcessorDO> addDataParserFilterProcessorDOList,
            List<DataParserGroovyProcessorDO> addDataParserGroovyProcessorDOList,
            List<DataParserGrokProcessorDO> addDataParserGrokProcessorDOList,
            List<DataParserEncryptionProcessorDO> addDataParserEncryptionProcessorDOList,
            List<DataParserLabelMysqlProcessorDO> addDataParserLabelMysqlProcessorDOList,
            List<DataParserLabelRedisProcessorDO> addDataParserLabelRedisProcessorDOList,
            List<CollectorDO> addCollectorDOList,
            List<CollectorFieldDO> addCollectorFieldDOList
    ) {
        try {
            log.info("<=====================start syncAdd pipeline in db operation====================>");
            log.info("addPipelineDOList = {}", JsonUtils.toJsonStringIgnoreExp(addPipelineDOList));
            log.info("addDataParserRemapperProcessorDOList = {}", JsonUtils.toJsonStringIgnoreExp(addDataParserRemapperProcessorDOList));
            log.info("addDataParserFilterProcessorDOList = {}", JsonUtils.toJsonStringIgnoreExp(addDataParserFilterProcessorDOList));
            log.info("addDataParserGroovyProcessorDOList = {}", JsonUtils.toJsonStringIgnoreExp(addDataParserGroovyProcessorDOList));
            log.info("addDataParserGrokProcessorDOList = {}", JsonUtils.toJsonStringIgnoreExp(addDataParserGrokProcessorDOList));
            log.info("addDataParserEncryptionProcessorDOList = {}", JsonUtils.toJsonStringIgnoreExp(addDataParserEncryptionProcessorDOList));
            log.info("addDataParserLabelMysqlProcessorDOList = {}", JsonUtils.toJsonStringIgnoreExp(addDataParserLabelMysqlProcessorDOList));
            log.info("addDataParserLabelRedisProcessorDOList = {}", JsonUtils.toJsonStringIgnoreExp(addDataParserLabelRedisProcessorDOList));
            log.info("addCollectorDOList = {}", JsonUtils.toJsonStringIgnoreExp(addCollectorDOList));
            log.info("addCollectorFieldDOList = {}", JsonUtils.toJsonStringIgnoreExp(addCollectorFieldDOList));
            log.info("<=====================end syncAdd pipeline in db operation====================>");

            addPipelineDOList.forEach(e -> dataParserPipelineHandler.addPipeline(e));
            addDataParserRemapperProcessorDOList.forEach(e -> dataParserRemapperProcessorHandler.addRemapperProcessor(e));
            addDataParserFilterProcessorDOList.forEach(e -> dataParserFilterProcessorHandler.addFilterProcessor(e));
            addDataParserGroovyProcessorDOList.forEach(e -> dataParserGroovyProcessorHandler.addGroovyProcessor(e));
            addDataParserGrokProcessorDOList.forEach(e -> dataParserGrokProcessorHandler.addGrokProcessor(e));
            addDataParserEncryptionProcessorDOList.forEach(e -> dataParserEncryptionProcessorHandler.addEncryptionProcessor(e));
            addDataParserLabelMysqlProcessorDOList.forEach(e -> dataParserLabelMysqlProcessorHandler.addLabelMysqlProcessor(e));
            addDataParserLabelRedisProcessorDOList.forEach(e -> dataParserLabelRedisProcessorHandler.addLabelRedisProcessor(e));
            addCollectorDOList.forEach(e -> collectorHandler.insertCollector(e));
            addCollectorFieldDOList.forEach(e -> collectorHandler.insertCollectorField(e));
        } catch (Exception e) {
            log.error(ExceptionUtils.getTrace(e));
            throw new SiteException(InnerError.getCode(), InnerError.getErrMsg());
        }

    }
}