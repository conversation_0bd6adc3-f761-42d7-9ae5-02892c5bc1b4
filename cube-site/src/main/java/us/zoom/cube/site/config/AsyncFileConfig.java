package us.zoom.cube.site.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;
import us.zoom.async.file.FileServerClient;

import jakarta.annotation.PostConstruct;
import us.zoom.cloud.secrets.spring.annotation.SecretValue;

/**
 *
 * @author: <PERSON>
 * @date: 2023/02/20 10:47
 */
@Component
@Slf4j
public class AsyncFileConfig {

    public static String URL;

    public static String appName = "sharp_eyes";

//    @SecretValue("async.file.secret")
    public String SECRET;

    public static final String ASYNC_FILE_SECRET_KEY = "async.file.secret";

    public static final String ASYNC_FILE_URL_KEY = "async.file.url";

    public static final String JWT_ISSUE_NAME = "cube";

    @Autowired
    private Environment environment;


    @PostConstruct
    public void init() {
//        SECRET = environment.getRequiredProperty(ASYNC_FILE_SECRET_KEY);
//        URL = environment.getRequiredProperty(ASYNC_FILE_URL_KEY);
    }


    @Bean
    public FileServerClient fileServerClientBean() {
        FileServerClient fileServerClient = null;
        try{
            fileServerClient = FileServerClient.FileServerClientBuilder
                    .standard(URL)
                    .withTimeOut(5000, 10000)
                    .withAppInfo(JWT_ISSUE_NAME)
                    .withClients(2)
                    .build();
            return fileServerClient;
        }catch (Exception e){
            e.printStackTrace();
            log.error("fileServerClientBean error",e);
        }
        return fileServerClient;

    }


}
