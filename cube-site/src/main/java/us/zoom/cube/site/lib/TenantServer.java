package us.zoom.cube.site.lib;

import us.zoom.infra.dao.model.ServerDO;
import java.util.List;
/**
 * <AUTHOR>
 *
 */
public class TenantServer {
    private String name;
    private List<ServerDO> servers;
    private Integer originSize;


    public TenantServer(String name, List<ServerDO> servers) {
        this.name = name;
        this.servers = servers;
    }


    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public List<ServerDO> getServers() {
        return servers;
    }

    public void setServers(List<ServerDO> servers) {
        this.servers = servers;
    }

    public Integer getOriginSize() {
        return originSize;
    }

    public void setOriginSize(Integer originSize) {
        this.originSize = originSize;
    }
}
