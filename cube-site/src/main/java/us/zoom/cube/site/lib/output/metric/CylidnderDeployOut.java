package us.zoom.cube.site.lib.output.metric;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class CylidnderDeployOut {

    List<Map<String,Object>> cylidnerData;
    List<Map<String,Object>> deployDatas;

    private String deployLabelName;

    private String deployValueField;

    /**
     * x
     */
    private String cylinderXField;

    /**
     * y，
     */
    private List<String> cylinderYFields;

    public CylidnderDeployOut(List<Map<String, Object>> cylidnerData, List<Map<String, Object>> deployDatas) {
        this.cylidnerData = cylidnerData;
        this.deployDatas = deployDatas;
    }

    public List<Map<String, Object>> getCylidnerData() {
        return cylidnerData;
    }

    public void setCylidnerData(List<Map<String, Object>> cylidnerData) {
        this.cylidnerData = cylidnerData;
    }

    public List<Map<String, Object>> getDeployDatas() {
        return deployDatas;
    }

    public void setDeployDatas(List<Map<String, Object>> deployDatas) {
        this.deployDatas = deployDatas;
    }

    public String getDeployLabelName() {
        return deployLabelName;
    }

    public void setDeployLabelName(String deployLabelName) {
        this.deployLabelName = deployLabelName;
    }

    public String getDeployValueField() {
        return deployValueField;
    }

    public void setDeployValueField(String deployValueField) {
        this.deployValueField = deployValueField;
    }

    public String getCylinderXField() {
        return cylinderXField;
    }

    public void setCylinderXField(String cylinderXField) {
        this.cylinderXField = cylinderXField;
    }

    public List<String> getCylinderYFields() {
        return cylinderYFields;
    }

    public void setCylinderYFields(List<String> cylinderYFields) {
        this.cylinderYFields = cylinderYFields;
    }
}
