package us.zoom.cube.site.biz;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import us.zoom.cube.lib.utils.JsonUtils;
import us.zoom.cube.site.lib.ResponseObject;
import us.zoom.cube.site.lib.input.template.BindTemplateListInput;

import static org.junit.jupiter.api.Assertions.*;

public class TemplateServiceV2Test extends BaseTest{

    @Autowired
    private TemplateServiceV2 templateServiceV2;

    @Test
    public void requiredSelectDcs() {
        String str = "{\n" +
                "  \"templateIdList\": [\n" +
                "    \"a89a973d-8fbd-4f9c-839e-e45f9da1db74\",\n" +
                "    \"17b6ba35-2530-44a0-8c96-3873366d51bd\"\n" +
                "  ],\n" +
                "  \"serviceId\": \"54e308d5-d724-4452-9425-688bbaf9f452\"\n" +
                "}";

        BindTemplateListInput input = JsonUtils.toObject(str,BindTemplateListInput.class);
        ResponseObject<Boolean> result = templateServiceV2.requiredSelectDcs(input);
        Assertions.assertTrue(result.getData());

        String str1 = "{\n" +
                "  \"templateIdList\": [\n" +
                "    \"a89a973d-8fbd-4f9c-839e-e45f9da1db74\"\n" +
                "  ],\n" +
                "  \"serviceId\": \"54e308d5-d724-4452-9425-688bbaf9f452\"\n" +
                "}";

        BindTemplateListInput input1 = JsonUtils.toObject(str1,BindTemplateListInput.class);
        ResponseObject<Boolean> result1 = templateServiceV2.requiredSelectDcs(input1);
        Assertions.assertFalse(result1.getData());

        String str3 = "{\n" +
                "  \"templateIdList\": [\n" +
                "    \"bdb5888a-63fb-45a5-97a0-df5d252871b0\"\n" +
                "  ],\n" +
                "  \"serviceId\": \"54e308d5-d724-4452-9425-688bbaf9f452\"\n" +
                "}";

        BindTemplateListInput input3 = JsonUtils.toObject(str3,BindTemplateListInput.class);
        ResponseObject<Boolean> result3 = templateServiceV2.requiredSelectDcs(input3);
        Assertions.assertTrue(result3.getData());


    }
}