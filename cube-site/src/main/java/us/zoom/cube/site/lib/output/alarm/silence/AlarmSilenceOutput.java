package us.zoom.cube.site.lib.output.alarm.silence;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import org.springframework.format.annotation.DateTimeFormat;
import us.zoom.cube.site.lib.output.alarm.group.AlarmGroupServiceItemOutput;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AlarmSilenceOutput {
    String id;
    String name;
    String alarmGroupId;
    String alarmGroupName;
    Integer type;
    String comment;

    String startDateTimeStr;
    String endDateTimeStr;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    Date createTime;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    Date modifyTime;

    Integer needNotify;

    String creator;
    String editor;
    String silenceTimeZone;
    AlarmSilenceRecurringScheduleOutput alarmSilenceRecurringSchedule;

    Integer status;
    Integer createType;

    List<AlarmGroupServiceItemOutput> serviceItemList = new ArrayList<>();
    Integer autoDelete;
}
