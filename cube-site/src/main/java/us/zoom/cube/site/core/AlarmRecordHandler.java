package us.zoom.cube.site.core;

import com.zoom.op.monitor.dao.MetricDefinitionDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import us.zoom.cube.site.biz.StorageService;
import us.zoom.cube.site.lib.query.AlarmRecordsQuery;
import us.zoom.infra.dao.model.TenantDO;
import us.zoom.infra.dao.service.MetricsDAO;
import us.zoom.infra.influx.model.alarm.AlarmMatchRecord;
import us.zoom.infra.influx.model.alarm.PageRequest;
import us.zoom.infra.influx.model.alarm.PageResult;
import us.zoom.infra.influx.service.alarm.InfluxAlarmService;
import us.zoom.infra.model.alarm.AlarmLevel;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Component
public class AlarmRecordHandler {

    @Autowired
    private MetricDefinitionDao metricDefinitionDao;

    @Autowired
    private MetricsDAO metricsDAO;
    @Autowired
    private InfluxAlarmService influxAlarmService;

    @Autowired
    private StorageService storageService;

    @Autowired
    private TenantHandler tenantHandler;
    public PageResult searchAlarmRecordFromInflux(String tenantId, AlarmRecordsQuery query, int pageIndex, int pageSize) throws IOException {
       TenantDO tenantDO= tenantHandler.getTenantById(tenantId);
        PageRequest pageRequest=new PageRequest();
        pageRequest.setPage(pageIndex);
        pageRequest.setPageSize(pageSize);
        if(null != query.getBegin()){
            pageRequest.setStartTimestamp(query.getBegin());
        }

        if(null != query.getEnd()){
            pageRequest.setEndTimestamp(query.getEnd());
        }
        pageRequest.setAlarmName(query.getName());
        pageRequest.setTenantName(tenantDO.getName());
        pageRequest.setAlarmLevel(AlarmLevel.fromLevel(query.getAlarmLevel()));
        pageRequest.setIsNoticed(query.getNoticed());
        PageResult pageResult = storageService.searchAlarmPages(pageRequest);
        List<AlarmMatchRecord> newAlarmMatchRecordList = new ArrayList<>();
        List<AlarmMatchRecord> alarmMatchRecordList = pageResult.getRecords();
        for (AlarmMatchRecord alarmMatchRecord : alarmMatchRecordList){
//            alarmMatchRecord.setMetricsName(metricDefinitionDao.getOne(Long.parseLong(alarmMatchRecord.getMetricsId())).getName());
            alarmMatchRecord.setMetricsName(metricsDAO.getMetricsById(alarmMatchRecord.getMetricsId()).getMetricsName());
            newAlarmMatchRecordList.add(alarmMatchRecord);
        }
        pageResult.setRecords(newAlarmMatchRecordList);
       return pageResult;
    }
}
