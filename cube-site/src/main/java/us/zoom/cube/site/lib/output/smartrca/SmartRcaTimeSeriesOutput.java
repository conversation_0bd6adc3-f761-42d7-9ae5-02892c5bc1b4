package us.zoom.cube.site.lib.output.smartrca;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SmartRcaTimeSeriesOutput {

    String id;

    String serviceNode;

    String serviceName;

    String metricName;

    String fieldName;

    String metricType;

    String sql;

    String alarmId;

    String alarmName;

    String selfAlarmId;

    String selfAlarmName;

    Map<String, List<String>> tagCondition = new HashMap<>();

    List<RelateAlarmData> relateAlarmDataList = new ArrayList<>();

}
