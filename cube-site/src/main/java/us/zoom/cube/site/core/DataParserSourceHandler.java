package us.zoom.cube.site.core;

import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.lib.common.CubeConstants;
import us.zoom.infra.dao.model.DataParserSourceDO;
import us.zoom.infra.dao.service.DataParserSourceDAO;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * @Author: <EMAIL>
 * @ModuleOwner: <EMAIL>
 * @Date: 03/09/2022 11:06
 * @Description:
 */
@Component
public class DataParserSourceHandler {
    @Autowired
    private DataParserSourceDAO dataParserSourceDAO;

    public List<DataParserSourceDO> listAll() {
        List<DataParserSourceDO> dataParserSourceDOList = new ArrayList<>();
        long counts = dataParserSourceDAO.queryCounts();
        int pageSize = CubeConstants.CUBE_SQL_BATCH_QUERY_SIZE;
        long pageNum = counts / pageSize + (counts % pageSize == 0 ? 0 : 1);
        for (int i = 1 ; i <= pageNum ; i++){
            dataParserSourceDOList.addAll(dataParserSourceDAO.listBatch(pageSize * (i - 1),pageSize));
        }
        return dataParserSourceDOList;
    }

    public void addDataParserSource(DataParserSourceDO dataParserSourceDO) {
        dataParserSourceDAO.addDataParserSource(dataParserSourceDO);
    }

    public void editDataParserSource(DataParserSourceDO dataParserSourceDO) {
        dataParserSourceDAO.editDataParserSource(dataParserSourceDO);
    }

    public List<DataParserSourceDO> findByParam(String tenantId, String name, String collectType, String source, String rawDataFormat, String status, int pageIndex, int pageSize) throws Exception {
        return dataParserSourceDAO.findByParam(tenantId, name, collectType, source, rawDataFormat, status, pageSize * (pageIndex - 1), pageSize);
    }

    public List<DataParserSourceDO> findByParamAndDataFlowId(String dataFlowId,String tenantId, String name, String collectType, String source, String rawDataFormat, String status, int pageIndex, int pageSize) throws Exception {
        return dataParserSourceDAO.findByParamAndDataFlowId(dataFlowId,tenantId, name, collectType, source, rawDataFormat, status, pageSize * (pageIndex - 1), pageSize);
    }

    public Integer getCountByParam(String tenantId, String name, String collectType, String source, String rawDateType, String status) throws Exception {
        return dataParserSourceDAO.getCountByParam(tenantId, name, collectType, source, rawDateType, status);
    }

    public Integer getCountByParamAndDataFlowId(String dataFlowId,String tenantId, String name, String collectType, String source, String rawDateType, String status) throws Exception {
        return dataParserSourceDAO.getCountByParamAndDataFlowId(dataFlowId,tenantId, name, collectType, source, rawDateType, status);
    }

    public DataParserSourceDO getById(String id) {
        return dataParserSourceDAO.getById(id);
    }

    public List<DataParserSourceDO> listBy(@Param("ids") List<String> ids){
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return dataParserSourceDAO.listBy(ids);
    }

    public void deleteById(String id) {
        dataParserSourceDAO.deleteById(id);
    }

    public Integer getCountByName(String tenantId, String name) {
        return dataParserSourceDAO.getCountByParamNotId(tenantId, name, null, null, null);
    }

    public Integer getCountByNameNotId(String tenantId, String name, String id) {
        return dataParserSourceDAO.getCountByParamNotId(tenantId, name, null, null, id);
    }

    public Integer getCountByCollectTypeAndSource(String tenantId, String collectType, String source) {
        return dataParserSourceDAO.getCountByParamNotId(tenantId, null, collectType, source, null);
    }

    public Integer getCountByCollectTypeAndSourceNotId(String tenantId, String collectType, String source, String id) {
        return dataParserSourceDAO.getCountByParamNotId(tenantId, null, collectType, source, id);
    }

    public DataParserSourceDO getDataParserByTenantIdAndName(String tenantId, String name) {
        return dataParserSourceDAO.getDataParserByTenantIdAndName(tenantId, name);
    }

    public List<DataParserSourceDO> getDataParserByTenantId(String tenantId) {
        return dataParserSourceDAO.getDataParserByTenantId(tenantId);
    }
}
