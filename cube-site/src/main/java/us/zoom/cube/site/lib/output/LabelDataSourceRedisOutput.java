package us.zoom.cube.site.lib.output;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

/**
 * <AUTHOR>
 * @create 2020/5/27 9:14 AM
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class LabelDataSourceRedisOutput {

    String id;

    String name;

    String type;

    String nodes;

    String password;

    Integer maxActive;

    Integer maxIdle;

    Integer maxWaitMillis;

    Boolean testOnBorrow;

    Boolean testOnReturn;

    Integer connectTimeout;

    Integer soTimeout;

    Integer maxAttempts;
}
