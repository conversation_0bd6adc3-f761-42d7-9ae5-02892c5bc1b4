package us.zoom.cube.site.core.processors;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import us.zoom.cube.lib.hub.ProcessorTypeEnum;
import us.zoom.cube.lib.hub.RemapperProcessorCfg;
import us.zoom.cube.site.core.parser.process.core.processor.Processor;
import us.zoom.cube.site.core.parser.process.core.processor.ProcessorFactory;
import us.zoom.cube.site.core.parser.process.core.processor.ProcessorResp;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author: <EMAIL>
 * @ModuleOwner: <EMAIL>
 * @Date: 01/11/2023 17:36
 * @Description:
 */
public class ReMapperProcessorTest {
    @Test
    public void test() {
        Processor processor = ProcessorFactory.getInstance(ProcessorTypeEnum.RemapperProcessorCfg.name());
        Map<String, Object> map = new HashMap<>();
        map.put("measure", "test");
        map.put("key11", "test11");
        map.put("key12", "test12");
        map.put("key13", "test13");
        map.put("key14", "test14");
        RemapperProcessorCfg processorCfg = new RemapperProcessorCfg();
        Map<String, String> fieldMap = new HashMap<>();
        fieldMap.put("key11", "key21");
        fieldMap.put("key12", "key22");
        fieldMap.put("key13", "key23");
        fieldMap.put("key14", "key24");
        processorCfg.setFieldsMap(fieldMap);
        ProcessorResp resp = processor.process(map, processorCfg);
        Assertions.assertEquals(resp.isContinue(), true);
        Assertions.assertEquals(map.size(), 9);
    }


}
