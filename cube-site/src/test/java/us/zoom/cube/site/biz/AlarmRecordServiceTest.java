package us.zoom.cube.site.biz;

/**
 * <AUTHOR>
 * @Date 2020/11/10 4:13
 */

import org.apache.commons.lang3.time.DateUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import us.zoom.cube.site.core.alarm.insight.AlarmRecordEventHandler;
import us.zoom.cube.site.lib.ResponseObject;
import us.zoom.cube.site.lib.input.alarm.definition.AlarmRecordInput;
import us.zoom.cube.site.lib.output.alarm.insight.AlarmRecordsOutputEntry;
import us.zoom.cube.site.lib.query.AlarmRecordsQuery;
import us.zoom.cube.site.lib.query.PageQuery;
import us.zoom.cube.lib.utils.JsonUtils;

import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2020/11/10 2:45
 */
public class AlarmRecordServiceTest extends BaseTest {

    @Autowired
    private AlarmRecordService alarmRecordService;

    @Autowired
    private AlarmRecordEventHandler alarmRecordEventHandler;

    @Test
    public void searchAlarmRecord() throws Exception {
        PageQuery<AlarmRecordsQuery> pageQuery = new PageQuery<>();
        AlarmRecordsQuery alarmRecordsQuery = new AlarmRecordsQuery();
        alarmRecordsQuery.setAlarmTenantId("bb6a0a17-0703-4e06-affc-92834a5d9079");
        alarmRecordsQuery.setName("cache_sync_up_consumer_app_rt");
        alarmRecordsQuery.setBegin(DateUtils.addDays(new Date(), -1).getTime());
        alarmRecordsQuery.setEnd(new Date().getTime());
        pageQuery.setQueryPara(alarmRecordsQuery);
        pageQuery.setPageSize(100);
        pageQuery.setPageIndex(3);
        //pageQuery.setUserId("28ea4e66-0cc3-43fb-8ba3-3df65c8ed0f7");
        //pageQuery.setTenantId("bb6a0a17-0703-4e06-affc-92834a5d9079");
        ResponseObject responseObject = alarmRecordService.findByAlarmNameLike(pageQuery);
        Assertions.assertNotNull(responseObject);
    }

    @Test
    public void testAll() {
        alarmRecordAck();
        getRecentChangeRecord();
    }

    @Test
    public void alarmRecordAck() {
        try {
            for (int i = 0; i < 1; i++) {
                AlarmRecordInput alarmRecordInput = new AlarmRecordInput();
                alarmRecordInput.setAlarmRecordId("dde173ea093b02ca6da7c9c86c5cfa30");
                alarmRecordInput.setTenantId("39caa432-8f3f-4679-ad39-3f254a203be4");
                alarmRecordInput.setAckExpirePeriod(600L);
                ResponseObject res = alarmRecordService.alarmRecordAck(alarmRecordInput);
                System.out.println(JsonUtils.toJsonString(res));
            }
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
    }

    @Test
    public void getRecentChangeRecord() {

        Map<String, AlarmRecordsOutputEntry> result = alarmRecordEventHandler.getRecentChangeRecord("06c9943a-76e5-4a2b-af88-235a347878bf");
        System.out.println(JsonUtils.toJsonString(result));
    }

    @Test
    public void alarmRecordResolve() {
        try {
            AlarmRecordInput alarmRecordInput = new AlarmRecordInput();
            alarmRecordInput.setAlarmRecordId("7982a070d8ff80ccd907d5ba9caf9e0e");
            alarmRecordInput.setTenantId("39caa432-8f3f-4679-ad39-3f254a203be4");
            ResponseObject res = alarmRecordService.alarmRecordResolved(alarmRecordInput);
            System.out.println(JsonUtils.toJsonString(res));
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
    }
}

