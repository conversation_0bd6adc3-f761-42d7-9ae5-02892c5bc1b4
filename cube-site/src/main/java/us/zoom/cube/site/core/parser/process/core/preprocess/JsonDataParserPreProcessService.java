package us.zoom.cube.site.core.parser.process.core.preprocess;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.NullNode;
import com.google.common.collect.Maps;
import us.zoom.cube.lib.common.LogTypeEnum;
import us.zoom.cube.site.core.parser.process.core.common.DataParserLogTypeEnum;
import us.zoom.cube.site.core.parser.process.core.common.constant.Constants;
import us.zoom.cube.site.core.parser.process.core.monitoring.Measure;
import us.zoom.cube.site.core.parser.process.core.response.PreProcessDO;
import us.zoom.cube.site.core.parser.process.core.response.PreResp;
import us.zoom.cube.site.infra.enums.WebCodeEnum;
import us.zoom.cube.site.lib.SiteException;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

public class JsonDataParserPreProcessService implements DataParserPreProcessService {
    private final static ObjectMapper mapper = new ObjectMapper();

    public JsonDataParserPreProcessService() {
        this.mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    @Override
    public PreResp handle(String monitorLog) {
        List<PreProcessDO> results = new ArrayList<>();
        try {
            JsonNode jsonNode = mapper.readValue(monitorLog, JsonNode.class);
            if (jsonNode instanceof ArrayNode) {
                Iterator<JsonNode> iterator = jsonNode.iterator();
                while (iterator.hasNext()) {
                    JsonNode sonJsonNode = iterator.next();
                    results.addAll(processCollectLog(sonJsonNode));
                }
            } else {
                results.addAll(processCollectLog(jsonNode));
            }
        } catch (Exception e) {
            throw new SiteException(WebCodeEnum.DataParserPreprocessDebugError.getCode(),e.getMessage());
        }
        return new PreResp(results);
    }

    public List<PreProcessDO> processCollectLog(JsonNode jsonNode) {
        List<PreProcessDO> results = new ArrayList<>();
        LogTypeEnum logTypeEnum = getLogType(jsonNode);
        if (logTypeEnum == LogTypeEnum.JSON) {
            results.add(isStandardLog(jsonNode) ?
                    transferMeasure(DataParserLogTypeEnum.standard, jsonNode) :
                    transferJson(DataParserLogTypeEnum.json, jsonNode));
        } else {
            // agent common field
            Map<String, Object> commonMap = agentBaseInfoMap(logTypeEnum, jsonNode);

            JsonNode msgJsonNode = jsonNode.get(Constants.AGENT_MESSAGE);
            if (msgJsonNode != null && msgJsonNode.isTextual()) {
                String message = msgJsonNode.asText();
                String[] messages = message.split(Constants.ENTER);
                for (String msg : messages) {
                    processAgentMessageField(msg, results, commonMap);
                }
            }
        }
        return results;
    }

    private void processAgentMessageField(String msg, List<PreProcessDO> results, Map<String, Object> commonMap) {
        JsonNode sonJsonNode = null;
        try {
            sonJsonNode = mapper.readValue(msg, JsonNode.class);
        } catch (Exception e) {
            throw new SiteException(WebCodeEnum.InnerError.getCode(),e.getMessage());
        }

        if (sonJsonNode instanceof ArrayNode) {
            Iterator<JsonNode> iterator = sonJsonNode.iterator();
            while (iterator.hasNext()) {
                JsonNode jn = iterator.next();
                results.add(isStandardLog(jn) ?
                        transferMeasure(DataParserLogTypeEnum.agent_standard, jn, commonMap) :
                        transferJson(DataParserLogTypeEnum.agent_json, jn, commonMap));
            }
        } else {
            results.add(isStandardLog(sonJsonNode) ?
                    transferMeasure(DataParserLogTypeEnum.agent_standard, sonJsonNode, commonMap) :
                    transferJson(DataParserLogTypeEnum.agent_json, sonJsonNode, commonMap));
        }
    }

    protected boolean isStandardLog(JsonNode jsonNode) {
        if (jsonNode.has(Constants.MEASURE) && jsonNode.has(Constants.MEASURE_TS)
                && jsonNode.has(Constants.MEASURE_TAG) && jsonNode.has(Constants.MEASURE_FIELD)) {
            return true;
        }
        return false;
    }

    protected LogTypeEnum getLogType(JsonNode jsonNode) {
        // zdca log contain: message, appName, topType, labelList, logstream
        // zdca base log contain: message, appName, topType, labelList, but not logstream
        // other is json
        if (jsonNode.has(Constants.AGENT_MESSAGE)
                && jsonNode.has(Constants.AGENT_APP_NAME)
                && jsonNode.has(Constants.AGENT_TOP_TYPE)
                && jsonNode.has(Constants.AGENT_LABEL_LIST)) {
            if (jsonNode.has(Constants.AGENT_LOG_STREAM)) {
                return LogTypeEnum.AGENT_LOG;
            } else {
                return LogTypeEnum.AGENT_BASE_INFO;
            }
        }
        return LogTypeEnum.JSON;
    }

    protected PreProcessDO transferMeasure(DataParserLogTypeEnum logType, JsonNode jsonNode, Map<String, Object> commonMap) {
        try {
            Measure measure = mapper.convertValue(jsonNode, Measure.class);
            measure.checkTagFiled();
            commonMap.forEach((key, value) -> {
                if (!Constants.NOT_PUT_MEASURE.contains(key)) {
                    measure.getTag().putIfAbsent(key, value);
                }
            });
            Map<String, Object> lastMap = new HashMap<>();
            parseMapField(jsonNode, measure);
            lastMap.putAll(measure.toMap());
            return new PreProcessDO(logType, lastMap, measure);
        } catch (Exception e) {
            return null;
        }
    }

    protected PreProcessDO transferMeasure(DataParserLogTypeEnum logType, JsonNode jsonNode) {
        try {
            Measure measure = mapper.convertValue(jsonNode, Measure.class);
            measure.checkTagFiled();
            parseMapField(jsonNode, measure);
            return new PreProcessDO(logType, measure.toMap(), measure);
        } catch (Exception e) {
            return null;
        }
    }

    private void parseMapField(JsonNode jsonNode, Measure measure) {
        JsonNode jn = jsonNode.get(Constants.STANDARD_MAP_FIELD);
        if (jn != null && !jn.isNull()) {
            Map<String, Object> mapField = Maps.newHashMap();
            recursionParseJson(jn, mapField);
            measure.setMapField(mapField);
        }
    }

    protected PreProcessDO transferJson(DataParserLogTypeEnum logType, JsonNode jsonNode, Map<String, Object> commonMap) {
        try {
            Map<String, Object> resultMap = new HashMap<>();
            commonMap.forEach((key, value) -> {
                if (!Constants.NOT_PUT_JSON.contains(key)) {
                    resultMap.put(key, value);
                }
            });
            //support infra json contain labelList
            Map<String, Object> labelMap = labelList(jsonNode.get(Constants.AGENT_LABEL_LIST));
            resultMap.putAll(labelMap);

            recursionParseJson(jsonNode, resultMap);

            return new PreProcessDO(logType, resultMap);
        } catch (Exception e) {
            return null;
        }
    }

    public PreProcessDO transferJson(DataParserLogTypeEnum logType, JsonNode jsonNode) {
        return transferJson(logType, jsonNode, Maps.newHashMapWithExpectedSize(1));
    }

    private void recursionParseJson(JsonNode jsonNode, Map<String, Object> resultMap) {
        Map<String, Object> map = mapper.convertValue(jsonNode, new TypeReference<Map<String, Object>>() {
        });
        map.forEach((key, value) -> {
            if (value instanceof Map) {
                resultMap.put(key, value);
                Map<String, Object> mmm = mapper.convertValue(value, new TypeReference<Map<String, Object>>() {
                });
                recursion(key, mmm, resultMap);
            } else {
                resultMap.put(key, value);
            }
        });
    }

    private void recursion(String prefixKey, Map<String, Object> map, Map<String, Object> resultMap) {
        map.forEach((key, value) -> {
            if (value instanceof Map) {
                resultMap.put(prefixKey + "." + key, value);
                Map<String, Object> mmm = mapper.convertValue(value, new TypeReference<Map<String, Object>>() {
                });
                recursion(prefixKey + "." + key, mmm, resultMap);
            } else {
                resultMap.put(prefixKey + "." + key, value);
            }
        });
    }

    protected Map<String, Object> agentBaseInfoMap(LogTypeEnum logTypeEnum, JsonNode jsonNode) {
        Map<String, Object> baseInfoMap = new HashMap<>();
        switch (logTypeEnum) {
            case AGENT_LOG: {
                baseInfoMap.putAll(basicMap(jsonNode));
                baseInfoMap.putAll(labelList(jsonNode.get(Constants.AGENT_LABEL_LIST)));
                break;
            }
            case AGENT_BASE_INFO: {
                baseInfoMap.putAll(basicInfoMap(jsonNode));
                baseInfoMap.putAll(labelList(jsonNode.get(Constants.AGENT_LABEL_LIST)));
                break;
            }
        }
        return baseInfoMap;
    }

    public Map<String, Object> labelList(JsonNode jsonNode) {
        try {
            if (jsonNode == null || jsonNode instanceof NullNode) {
                return Maps.newHashMapWithExpectedSize(1);
            }
            return mapper.convertValue(jsonNode, mapTypeReference);
        } catch (Exception e) {
            return Maps.newHashMapWithExpectedSize(1);
        }
    }

    private void setCommonStringField(Map<String, Object> map, String targetKey, String sourceKey, JsonNode field) {
        try {
            if (field.get(sourceKey) != null) {
                map.put(targetKey, field.get(sourceKey).asText());
            }
        } catch (Exception e) {
        }
    }

    private void setCommonLongField(Map<String, Object> map, String targetKey, String sourceKey, JsonNode field) {
        try {
            if (field.get(sourceKey) != null) {
                map.put(targetKey, field.get(sourceKey).asLong());
            }
        } catch (Exception e) {
        }
    }

    // 13 ts, appName, topType, host, ip, clusterId, regionId, zoneName, instanceId, ipPublic, infraType, isK8S, csp
    protected Map<String, Object> basicInfoMap(JsonNode jsonNode) {
        Map<String, Object> map = new HashMap<>(13);
        setCommonLongField(map, Constants.COMMON_FIELD_TS, Constants.AGENT_TS, jsonNode);

        setCommonStringField(map, Constants.COMMON_FIELD_APP_NAME, Constants.AGENT_APP_NAME, jsonNode);
        setCommonStringField(map, Constants.COMMON_FIELD_TOP_TYPE, Constants.AGENT_TOP_TYPE, jsonNode);

        setCommonStringField(map, Constants.COMMON_FIELD_HOST, Constants.AGENT_HOST, jsonNode);
        setCommonStringField(map, Constants.COMMON_FIELD_IP, Constants.AGENT_IP, jsonNode);
        setCommonStringField(map, Constants.COMMON_FIELD_CLUSTER_ID, Constants.AGENT_CLUSTER_ID, jsonNode);
        setCommonStringField(map, Constants.COMMON_FIELD_REGION_ID, Constants.AGENT_REGION_ID, jsonNode);
        setCommonStringField(map, Constants.COMMON_FIELD_ZONE_NAME, Constants.AGENT_ZONE_NAME, jsonNode);
        setCommonStringField(map, Constants.COMMON_FIELD_INSTANCE_ID, Constants.AGENT_INSTANCE_ID, jsonNode);
        setCommonStringField(map, Constants.COMMON_FIELD_IP_PUBLIC, Constants.AGENT_IP_PUBLIC, jsonNode);
        setCommonStringField(map, Constants.COMMON_FIELD_INFRA_TYPE, Constants.AGENT_INFRA_TYPE, jsonNode);
        setCommonLongField(map, Constants.COMMON_FIELD_IS_K8S, Constants.AGENT_IS_K8S, jsonNode);
        setCommonStringField(map, Constants.COMMON_FIELD_CSP, Constants.AGENT_CSP, jsonNode);
        setCommonStringField(map, Constants.COMMON_FIELD_CELL, Constants.AGENT_CELL, jsonNode);
        setCommonStringField(map, Constants.COMMON_FIELD_STACK, Constants.AGENT_STACK, jsonNode);
        setCommonStringField(map, Constants.COMMON_FIELD_NODE_NAME, Constants.AGENT_NODE_NAME, jsonNode);
        setCommonStringField(map, Constants.COMMON_FIELD_NAMESPACE, Constants.AGENT_NAMESPACE, jsonNode);
        setCommonStringField(map, Constants.COMMON_FIELD_POD_NAME, Constants.AGENT_POD_NAME, jsonNode);
        setCommonStringField(map, Constants.COMMON_FIELD_CONTAINER_NAME, Constants.AGENT_CONTAINER_NAME, jsonNode);
        setCommonStringField(map, Constants.COMMON_FIELD_CONTAINER_ID, Constants.AGENT_CONTAINER_ID, jsonNode);
        setCommonStringField(map, Constants.COMMON_FIELD_TENANT, Constants.AGENT_TENANT, jsonNode);
        return map;
    }

    public Map<String, Object> basicMap(JsonNode jsonNode) {
        Map<String, Object> map = new HashMap<>(9);
        //clusterId、instanceId、hostName、regionId
        try {
            if (jsonNode.get(Constants.AGENT_LOG_STREAM) != null) {
                String[] basic = jsonNode.get(Constants.AGENT_LOG_STREAM).asText().split("_");
                map.put(Constants.COMMON_FIELD_INSTANCE_ID, basic[1]);
                map.put(Constants.COMMON_FIELD_HOST_NAME, basic[2]);
                if (basic.length > 3) {
                    map.put(Constants.COMMON_FIELD_REGION_ID, basic[3]);
                }
            }
        } catch (Exception e) {
        }
        setCommonStringField(map, Constants.COMMON_FIELD_CLUSTER_ID, Constants.AGENT_APP_CLUSTER, jsonNode);

        setCommonLongField(map, Constants.COMMON_FIELD_TS, Constants.AGENT_TIMESTAMP, jsonNode);

        setCommonStringField(map, Constants.COMMON_FIELD_APP_NAME, Constants.AGENT_APP_NAME, jsonNode);
        setCommonStringField(map, Constants.COMMON_FIELD_TOP_TYPE, Constants.AGENT_TOP_TYPE, jsonNode);
        setCommonStringField(map, Constants.COMMON_FIELD_PATH_TEMPLATE, Constants.AGENT_PATH_TEMPLATE, jsonNode);
        return map;
    }

}
