package us.zoom.cube.site.lib.output.dataparser;

/**
 * <AUTHOR>
 * @create 2020/6/2 5:17 AM
 */
public class GroovyProcessorOut extends BaseProcessorOut {

    private String parseRule;

    private String invokeFunction;

    public GroovyProcessorOut() {}

    public GroovyProcessorOut(String id, String name, Integer order, String dataParserPipelineId, String dataParserId, String type, String parseRule, String invokeFunction) {
        super(id, name, order, dataParserPipelineId, dataParserId, type);
        this.parseRule = parseRule;
        this.invokeFunction = invokeFunction;
    }

    public String getParseRule() {
        return parseRule;
    }

    public void setParseRule(String parseRule) {
        this.parseRule = parseRule;
    }

    public String getInvokeFunction() {
        return invokeFunction;
    }

    public void setInvokeFunction(String invokeFunction) {
        this.invokeFunction = invokeFunction;
    }
}
