package us.zoom.cube.site.core;

import cn.hutool.core.lang.Assert;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.site.infra.utils.IdUtils;
import us.zoom.infra.dao.model.TemplateGroupDO;
import us.zoom.infra.dao.service.TemplateGroupDAO;

import java.util.Collections;
import java.util.List;

/**
 * @Author: <PERSON>
 * @Date:12/13/2022 16:50
 * @Description:
 */
@Component
@RequiredArgsConstructor
public class TemplateGroupHandler {

    private final TemplateGroupDAO templateGroupDAO;

    public String addTemplateGroup(TemplateGroupDO templateGroup) {
        Assert.notNull(templateGroup, "template group can't be null");
        Assert.isTrue(StringUtils.isNotEmpty(templateGroup.getName()), "template name can't be null");
        templateGroup.setId(IdUtils.generateId());
        templateGroupDAO.addTemplateGroup(templateGroup);
        return templateGroup.getId();
    }

    public TemplateGroupDO findByName(String name) {
        return templateGroupDAO.getByName(name);
    }

    public TemplateGroupDO findById(String id) {
        return templateGroupDAO.getById(id);
    }

    public int edit(TemplateGroupDO updateInputDO) {
        return templateGroupDAO.edit(updateInputDO);
    }

    public void deleteById(String id) {
        templateGroupDAO.deleteById(id);
    }

    public List<TemplateGroupDO> listAllGroup(Integer type) {
        return templateGroupDAO.listGroupByType(type);
    }

    public List<TemplateGroupDO> listTemplateGroupList(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return templateGroupDAO.listByIds(ids);
    }

    public PageInfo<TemplateGroupDO> listAllGroupByPage(int pageIndex, int pageSize, Integer type) {
        PageHelper.startPage(pageIndex, pageSize);
        List<TemplateGroupDO> groups = templateGroupDAO.listGroupByType(type);
        return new PageInfo<>(groups);
    }

}
