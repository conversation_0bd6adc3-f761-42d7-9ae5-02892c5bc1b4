package us.zoom.cube.site.lib.output.dataparser;

import java.util.List;

public class IpProcessorOut extends BaseProcessorOut{
    private String ipField;
    private List<String> geoFields;
    private String prefix;

    public String getIpField() {
        return ipField;
    }

    public void setIpField(String ipField) {
        this.ipField = ipField;
    }

    public List<String> getGeoFields() {
        return geoFields;
    }

    public void setGeoFields(List<String> geoField) {
        this.geoFields = geoField;
    }

    public String getPrefix() {
        return prefix;
    }

    public void setPrefix(String prefix) {
        this.prefix = prefix;
    }

    public IpProcessorOut(){}

    public IpProcessorOut(String id,String name,Integer order,String dataParserPipelineId,String dataParserId,String type,String ipField,List<String> geoFields,String prefix){
        super(id,name,order,dataParserPipelineId,dataParserId,type);
        this.ipField=ipField;
        this.geoFields=geoFields;
        this.prefix=prefix;
    }
}
