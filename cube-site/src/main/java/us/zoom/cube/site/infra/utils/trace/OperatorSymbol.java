package us.zoom.cube.site.infra.utils.trace;

/**
 * @author: eason.jia
 * @date: 2024/8/19
 */
public enum OperatorSymbol {
    VALUE,
    LITERAL,
    NOOP,
    EQ,
    NEQ,
    GT,
    LT,
    GTE,
    LTE,
    REQ,
    NREQ,
    IN,
    AND,
    OR,
    PLUS,
    MINUS,
    BITWISE_AND,
    BITWISE_OR,
    BITWISE_XOR,
    BITWISE_LSHIFT,
    BITWISE_RSHIFT,
    MULTIPLY,
    DIVIDE,
    MODULUS,
    EXPONENT,
    NEGATE,
    INVERT,
    BITWISE_NOT,
    TERNARY_TRUE,
    TERNARY_FALSE,
    COALESCE,
    FUNCTIONAL,
    ACCESS,
    SEPARATE;
}
