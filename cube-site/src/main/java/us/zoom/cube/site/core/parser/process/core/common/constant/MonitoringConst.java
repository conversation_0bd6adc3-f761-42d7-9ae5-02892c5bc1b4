package us.zoom.cube.site.core.parser.process.core.common.constant;

public class MonitoringConst {
    public final static String MONITOR_LOG_NAME = "Monitor";

    public final static String ERROR_METRICS = "error_metrics";

    public final static String LOAD_METRICS = "load_metrics";

    public final static String DATA_FORWARD_ERROR_METRICS = "data_forward_error_metrics";

    public final static String MMR_CACHE_METRICS = "mmr_cache_metrics";

    public final static String CLOSE_INVALID_TOPIC_METRICS = "close_invalid_topic_metrics";

    public final static String TOPIC_NUM_METRICS = "topic_num_metrics";

    public final static String INIT_TOPIC_METRICS = "init_topic_metrics";

    public final static String INIT_ASYNC_MQ_METRICS = "init_async_mq_metrics";

    public final static String LOAD_HUB_CFG_METRICS = "load_hub_cfg_metrics";

    public final static String REPORT_PIPELINE_METRICS = "report_pipeline_metrics";

    public final static String REPORT_QUEUE_METRICS = "report_queue_metrics";

    public static final String DATA_PARSER_METRICS = "data_parser_metrics";

    public static final String REDIS_METRICS = "redis_metrics";

    public static final String DATA_FLOW_METRICS = "data_flow_metrics";

    public static final String DATA_FORWARD_METRICS = "data_forward_metrics";

    public static final String INIT_PRODUCER_METRICS = "init_producer_metrics";

    public final static Float PERCENTILE_COMPRESSION = 6f;
}
