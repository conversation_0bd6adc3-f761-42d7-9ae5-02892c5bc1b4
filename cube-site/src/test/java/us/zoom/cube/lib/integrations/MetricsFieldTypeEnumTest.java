package us.zoom.cube.lib.integrations;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

/**
 * @Author: <EMAIL>
 * @ModuleOwner: <EMAIL>
 * @Date: 08/15/2024 10:48
 * @Description:
 */
public class MetricsFieldTypeEnumTest {
    @Test
    public void isExist() {
        Assertions.assertEquals(MetricsFieldTypeEnum.fromName("test"), null);
        Assertions.assertEquals(MetricsFieldTypeEnum.fromName("mapNumber"), MetricsFieldTypeEnum.mapNumber);
    }
}
