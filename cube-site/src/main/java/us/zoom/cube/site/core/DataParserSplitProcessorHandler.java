package us.zoom.cube.site.core;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import us.zoom.infra.dao.model.DataParserSplitProcessorDO;
import us.zoom.infra.dao.service.DataParserSplitProcessorDAO;

import java.util.Arrays;
import java.util.List;

@Component
public class DataParserSplitProcessorHandler {
    @Autowired
    private DataParserSplitProcessorDAO dataParserSplitProcessorDAO;

    public void addProcessor(DataParserSplitProcessorDO splitProcessorDO) {
        Assert.notNull(splitProcessorDO, "split processor is null !");
        dataParserSplitProcessorDAO.add(splitProcessorDO);
    }

    public void editProcessor(DataParserSplitProcessorDO splitProcessorDO) {
        Assert.notNull(splitProcessorDO, "split processor is null !");
        dataParserSplitProcessorDAO.edit(splitProcessorDO);
    }

    public void delProcessor(String id) {
        Assert.notNull(id, "id is null !");
        dataParserSplitProcessorDAO.del(id);
    }


    public DataParserSplitProcessorDO getProcessorById(String id) {
        Assert.notNull(id, "id is null !");
        return dataParserSplitProcessorDAO.getSplitProcessorById(id);
    }

    public List<DataParserSplitProcessorDO> listByPipelineIds(List<String> pipeLineIds) {
        return dataParserSplitProcessorDAO.listByPipelineIds(pipeLineIds);
    }

    public void delSplitProcessorByPipelineId(String pipelineId) {
        Assert.notNull(pipelineId, "pipelineId is null !");
        dataParserSplitProcessorDAO.delByPipelineIds(Arrays.asList(pipelineId));
    }

    public void delSplitProcessorByPipelineIds(List<String> pipelineIds) {
        if (CollectionUtils.isEmpty(pipelineIds)) {
            return;
        }
        dataParserSplitProcessorDAO.delByPipelineIds(pipelineIds);
    }
}
