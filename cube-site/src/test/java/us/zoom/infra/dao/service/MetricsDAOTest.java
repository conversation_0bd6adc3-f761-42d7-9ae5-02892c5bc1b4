package us.zoom.infra.dao.service;

import org.assertj.core.util.Lists;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import us.zoom.cube.site.BaseSpringTest;
import us.zoom.infra.dao.model.MetricsDO;
import us.zoom.cube.lib.utils.JsonUtils;

import java.util.List;

public class MetricsDAOTest extends BaseSpringTest {
    @Autowired
    private MetricsDAO metricsDAO;

    @Test
    public void findByMetricsNameLikeAndType(){
        String metricsName = "";
        Integer type = 3;
        List<String> tenantIds = Lists.newArrayList("09361b9b-d815-4de2-bd65-dbcc0d87df85");
        int pageIndex = 0;
        int pageSize = 20;
        List<MetricsDO> metricsDOList = metricsDAO.findByMetricsNameLikeAndType(metricsName, type, tenantIds, pageIndex, pageSize);
        System.out.println("metricsDOList = " + JsonUtils.toJsonStringIgnoreExp(metricsDOList));
        Assertions.assertEquals(9, metricsDOList.size());
    }

    @Test
    public void getCountByMetricsNameLikeAndType(){
        String metricsName = "";
        Integer type = 3;
        List<String> tenantIds = Lists.newArrayList("09361b9b-d815-4de2-bd65-dbcc0d87df85");
        int count = metricsDAO.getCountByMetricsNameLikeAndType(metricsName, type, tenantIds);
        Assertions.assertEquals(9, count);
    }
}
