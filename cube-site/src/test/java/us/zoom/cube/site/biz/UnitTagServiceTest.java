package us.zoom.cube.site.biz;


import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import us.zoom.cube.lib.common.TaskTypeEnum;
import us.zoom.cube.site.api.web.StatusEnum;
import us.zoom.cube.site.core.AsyncMqQueueHandler;
import us.zoom.cube.site.core.KafkaQueueHandler;
import us.zoom.cube.site.core.TaskQueueHandler;
import us.zoom.cube.site.core.UnitTagHandler;
import us.zoom.cube.site.lib.BasePara;
import us.zoom.cube.site.lib.IdPara;
import us.zoom.cube.site.lib.ResponseObject;
import us.zoom.cube.site.lib.input.UnitTagInput;
import us.zoom.cube.site.lib.output.PageResult;
import us.zoom.cube.site.lib.query.NameQuery;
import us.zoom.cube.site.lib.query.PageQuery;
import us.zoom.cube.site.lib.query.UnitTagQuery;
import us.zoom.infra.dao.model.UnitTagDO;
import us.zoom.infra.enums.CommonStatusEnum;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @Author: luis.zheng
 * @Date: 2021/8/10 1:14 
 */
public class UnitTagServiceTest {

    private UnitTagHandler unitTagHandler;
    private KafkaQueueHandler kafkaQueueHandler;
    private AsyncMqQueueHandler asyncMqQueueHandler;
    private AuthService authService;
    private UnitTagService unitTagService;
    private TaskQueueHandler taskQueueHandler;

    @BeforeEach
    public void before() {
        unitTagHandler = Mockito.mock(UnitTagHandler.class);
        kafkaQueueHandler = Mockito.mock(KafkaQueueHandler.class);
        asyncMqQueueHandler = Mockito.mock(AsyncMqQueueHandler.class);
        taskQueueHandler = Mockito.mock(TaskQueueHandler.class);
        authService = Mockito.mock(AuthService.class);
        unitTagService = new UnitTagService(unitTagHandler, kafkaQueueHandler, asyncMqQueueHandler, authService, taskQueueHandler);
    }

    @Test
    public void testSearchByName() {
        int total = 40;
        List<UnitTagDO> unitTagDOS = new ArrayList<>();
        unitTagDOS.add(createUnitTagDO(1));
        unitTagDOS.add(createUnitTagDO(2));
        unitTagDOS.add(createUnitTagDO(3));
//        Mockito.doNothing().when(authService).mustAdmin("userId");
        Mockito.doNothing().when(authService).checkAuth(new BasePara());

        Mockito.when(unitTagHandler.findByParam("name", TaskTypeEnum.alarm.name(), 1, 20)).thenReturn(unitTagDOS);
        Mockito.when(unitTagHandler.getCountByParam("name", TaskTypeEnum.alarm.name())).thenReturn(total);

        PageQuery<UnitTagQuery> query = new PageQuery<>();
        query.setUserId("userId");
        UnitTagQuery unitTagQuery = new UnitTagQuery();
        unitTagQuery.setName("name");
        unitTagQuery.setTaskType(TaskTypeEnum.alarm.name());
        query.setQueryPara(unitTagQuery);

        ResponseObject<PageResult<UnitTagDO>> responseObject = unitTagService.searchByName(query);
        Assertions.assertEquals(responseObject.getData().getTotal(), total);
        Assertions.assertEquals(responseObject.getData().getItems().size(), unitTagDOS.size());
    }

    @Test
    public void testEditUnitTag() {
        String id = "1111";
        UnitTagInput input = new UnitTagInput();
        input.setId(id);
        input.setDesc("desc");
        input.setName("name");
        input.setTaskType(TaskTypeEnum.calc.name());
        input.setStatus(CommonStatusEnum.enable.toString());
        Mockito.when(unitTagHandler.getById(id)).thenReturn(new UnitTagDO());
        ResponseObject responseObject = unitTagService.editUnitTag(input);
        Assertions.assertEquals(responseObject.getStatus(), StatusEnum.SUCCESS.getStatus());
    }

    @Test
    public void testEditUnitTagOldNull() {
        String id = "1111";
        UnitTagInput input = new UnitTagInput();
        input.setId(id);
        input.setDesc("desc");
        input.setName("name");
        input.setTaskType(TaskTypeEnum.calc.name());
        input.setStatus(CommonStatusEnum.enable.name());
        Mockito.when(unitTagHandler.getById(id)).thenReturn(new UnitTagDO());

        Mockito.when(unitTagHandler.getById(id)).thenReturn(null);
        Assertions.assertEquals("old UnitTag is null", Assertions.assertThrows(IllegalArgumentException.class, () ->
                unitTagService.editUnitTag(input)).getMessage());
    }

    @Test
    public void testDeleteUnitTagById() {
        String id = "1111";
        UnitTagDO unitTagDO = new UnitTagDO();
        unitTagDO.setId(id);
        unitTagDO.setName("ds-01");
        Mockito.when(unitTagHandler.getById(id)).thenReturn(unitTagDO);
        IdPara idPara = new IdPara();
        idPara.setId(id);

        ResponseObject responseObject = unitTagService.deleteUnitTagById(idPara);
        Assertions.assertEquals(responseObject.getStatus(), StatusEnum.SUCCESS.getStatus());

        Mockito.when(kafkaQueueHandler.countByUnitTagId(unitTagDO.getId())).thenReturn(1);
        Assertions.assertEquals("unit tag is in use", Assertions.assertThrows(IllegalArgumentException.class, () ->
                unitTagService.deleteUnitTagById(idPara)).getMessage());
    }

    @Test
    public void testCheckUnitTagNameExistTrue() {
        NameQuery nameQuery = new NameQuery();
        nameQuery.setName("xxx");
        Mockito.when(unitTagHandler.getCountByName("xxx")).thenReturn(0);
        ResponseObject responseObject = unitTagService.checkUnitTagNameExist(nameQuery);
        Assertions.assertEquals(responseObject.getData(), false);
    }

    @Test
    public void testCheckUnitTagNameExistFalse() {
        NameQuery nameQuery = new NameQuery();
        nameQuery.setName("xxx");
        nameQuery.setId("id");
        Mockito.when(unitTagHandler.getCountByNameNotId("xxx", "id")).thenReturn(3);
        ResponseObject responseObject = unitTagService.checkUnitTagNameExist(nameQuery);
        Assertions.assertEquals(responseObject.getData(), true);
    }

    private UnitTagDO createUnitTagDO(Integer random) {
        UnitTagDO unitTagDO = new UnitTagDO();
        unitTagDO.setId("1" + random);
        unitTagDO.setName("name" + random);
        unitTagDO.setDesc("desc" + random);
        unitTagDO.setTaskType(TaskTypeEnum.calc.name());
        unitTagDO.setStatus(CommonStatusEnum.enable.name());
        unitTagDO.setCreateTime(new Date());
        unitTagDO.setModifyTime(new Date());
        return unitTagDO;
    }
}
