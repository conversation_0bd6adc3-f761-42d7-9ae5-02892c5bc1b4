package us.zoom.cube.site.config;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.security.config.Customizer;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.annotation.web.configurers.HeadersConfigurer;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.csrf.CsrfTokenRequestAttributeHandler;
import us.zoom.cube.site.config.properties.JwtAuthProperties;

import java.io.IOException;

/**
 * <AUTHOR>
 * @date : 2023/12/20 10:55
 **/
@ConditionalOnMissingBean(JwtAuthProperties.class)
@EnableWebSecurity
@Configuration
@Order(Ordered.HIGHEST_PRECEDENCE)
@Slf4j
public class WebSecurityConfig {
    private final String POLICY_DIRECTIVES = "frame-src https://form.asana.com/ https://*.zoomdev.us:*/ https://*.zoom.us:*/";

    @Value("${site.oauth2.filter-url}")
    private String filterUrl;
    @Value("${site.oauth2.filter-url2}")
    private String filterUrl2;

    @Autowired
    private UnauthenticatedRequestHandler unauthenticatedRequestHandler;

    @Autowired
    private CustomizedAuthenticationSuccessHandler successHandler;

    @Value("${okta.csrf.disabled:false}")
    private boolean csrfDisabled;

    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http.authorizeHttpRequests((requests) -> requests
                        .requestMatchers(filterUrl, filterUrl2, "/dash/**", "/out/**", "/actuator/health").anonymous()
                        .anyRequest().authenticated())
                .logout(Customizer.withDefaults())
                .oauth2Client(Customizer.withDefaults())
                .oauth2Login(oauth2 -> oauth2.successHandler(successHandler))
                .exceptionHandling(Customizer.withDefaults())
                .httpBasic(hbc -> hbc.authenticationEntryPoint(unauthenticatedRequestHandler));
        if (!csrfDisabled) {
            http.csrf(csrf -> csrf.csrfTokenRepository(CustomizedAuthenticationSuccessHandler.cookieCsrfTokenRepository)
                    .csrfTokenRequestHandler(new CsrfTokenRequestAttributeHandler())
                    .ignoringRequestMatchers(filterUrl, filterUrl2, "/dash/**", "/cfg/**", "/api/op/**", "/out/**", "/api/metric/downloadClickHouse"));
        } else {
            http.csrf(AbstractHttpConfigurer::disable);
        }

        http.headers(httpSecurityHeadersConfigurer -> {
            httpSecurityHeadersConfigurer.frameOptions(HeadersConfigurer.FrameOptionsConfig::disable);
            httpSecurityHeadersConfigurer.contentSecurityPolicy(contentSecurityPolicyConfig
                    -> contentSecurityPolicyConfig.policyDirectives(POLICY_DIRECTIVES));
        });

        return http.build();
    }

    @Bean
    UnauthenticatedRequestHandler unauthenticatedRequestHandler() {
        return new UnauthenticatedRequestHandler();
    }

    static class UnauthenticatedRequestHandler implements AuthenticationEntryPoint {
        @Value("${site.okta.redirect-uri}")
        private String redirectUrl;
        private final static String REDIRECT_URL = "RedirectUrl";

        @Override
        public void commence(HttpServletRequest request, HttpServletResponse response, AuthenticationException authException) throws IOException {
            log.info("UnauthenticatedRequestHandler, request path [{}]", request.getServletPath());
            if (request.getServletPath().startsWith("/api/") || request.getServletPath().startsWith("/error")) {
                response.setStatus(403);
                response.setHeader(REDIRECT_URL, redirectUrl);
            } else {
                response.sendRedirect(redirectUrl);

            }
        }
    }
}
