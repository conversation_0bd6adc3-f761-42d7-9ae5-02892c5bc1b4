package us.zoom.cube.site.lib;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-05-18 10:08
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class CapacityDashCfg {
   String id;
   String dashName;
   String metricsField;
   //0 (percent), 1 (num)
   Integer valueType;
   Integer simplePeriod;
   //0 close,1 open
   Integer status;
   String tags;
   String defaultTags;
   CapacityClacCfg capacityClacCfg;
   List<CapacityServiceCfg> capacityServiceCfgList;

   public String getId() {
      return id;
   }

   public void setId(String id) {
      this.id = id;
   }

   public String getDashName() {
      return dashName;
   }

   public void setDashName(String dashName) {
      this.dashName = dashName;
   }

   public String getMetricsField() {
      return metricsField;
   }

   public void setMetricsField(String metricsField) {
      this.metricsField = metricsField;
   }

   public Integer getValueType() {
      return valueType;
   }

   public void setValueType(Integer valueType) {
      this.valueType = valueType;
   }

   public Integer getSimplePeriod() {
      return simplePeriod;
   }

   public void setSimplePeriod(Integer simplePeriod) {
      this.simplePeriod = simplePeriod;
   }

   public Integer getStatus() {
      return status;
   }

   public void setStatus(Integer status) {
      this.status = status;
   }

   public CapacityClacCfg getCapacityClacCfg() {
      return capacityClacCfg;
   }

   public void setCapacityClacCfg(CapacityClacCfg capacityClacCfg) {
      this.capacityClacCfg = capacityClacCfg;
   }

   public String getTags() {
      return tags;
   }

   public void setTags(String tags) {
      this.tags = tags;
   }

   public List<CapacityServiceCfg> getCapacityServiceCfgList() {
      return capacityServiceCfgList;
   }

   public void setCapacityServiceCfgList(List<CapacityServiceCfg> capacityServiceCfgList) {
      this.capacityServiceCfgList = capacityServiceCfgList;
   }

   public String getDefaultTags() {
      return defaultTags;
   }

   public void setDefaultTags(String defaultTags) {
      this.defaultTags = defaultTags;
   }
}
