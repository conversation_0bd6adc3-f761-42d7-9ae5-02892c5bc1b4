package us.zoom.cube.site.core;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;
import us.zoom.cube.lib.monitor.MonitorWrapper;
import us.zoom.cube.site.biz.eventcenter.EventCenterSqlParserUtil;
import us.zoom.cube.site.biz.eventcenter.EventCenterSqlParserUtilV2;
import us.zoom.cube.site.core.config.ClickhouseHandlerFactory;
import us.zoom.cube.site.lib.dto.WebOpAuditResponse;
import us.zoom.cube.site.lib.dto.eventcenter.EventCenterDTO;
import us.zoom.cube.site.lib.dto.eventcenter.v2.EventCenterV2DTO;
import us.zoom.infra.dao.model.EventCenterDO;
import us.zoom.infra.dao.model.EventCenterV2DO;
import us.zoom.infra.utils.DateUtils;
import us.zoom.infra.utils.HttpUtils;
import us.zoom.infra.utils.JWTUtils;
import us.zoom.cube.lib.utils.JsonUtils;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
@Slf4j
public class EventCenterHandler {

    private static final Logger monitorLog = LoggerFactory.getLogger("Monitor");

    private final static int MAX_RETRY_TIME = 3;
    private final static String AUDIT_API_URL = "/api/v2/audit/search";

    public static  Function<List<EventCenterV2DO>, List<EventCenterV2DTO>> searchEventV2Convert =
            (List<EventCenterV2DO> doList) -> Optional.ofNullable(doList).orElse(new ArrayList<>()).stream().map(x -> {
                EventCenterV2DTO dto = new EventCenterV2DTO();
                BeanUtils.copyProperties(x, dto);
                dto.setEventTime(DateUtils.fromLongToDate(x.getEventTime(), DateUtils.FORMART18));
                if (x.getDetail() != null) {
                    dto.setDetail(JsonUtils.toJsonString(x.getDetail()));
                }

                return dto;
            }).collect(Collectors.toList());

    public static  Function<List<EventCenterDO>, List<EventCenterDTO>> dbManagerCovert = (doList) -> Optional.ofNullable(doList).orElse(new ArrayList<>()).stream().map(x -> {
        EventCenterDTO dto = new EventCenterDTO();
        dto.setClusterId(x.getClusterId());
        dto.setCategory(x.getEventLevel4());
        dto.setAction(x.getEventLevel5());
        dto.setActionDetail(x.getEventLevel6());
        dto.setContent(x.getEventLevel12());
        dto.setOperator(x.getEventSrc());
        dto.setItemId(x.getEventLevel9());
        dto.setSubServiceName(x.getEventLevel8());
        dto.setCreateTime(DateUtils.changeUTC2Date(x.getBeginTime()));

        return dto;
    }).collect(Collectors.toList());

    public static Function<List<EventCenterDO>, List<EventCenterDTO>> originalConvert = (doList) -> Optional.ofNullable(doList).orElse(new ArrayList<>()).stream().map(x -> {
        EventCenterDTO dto = new EventCenterDTO();
        dto.setClusterId(x.getClusterId());
        dto.setCategory(x.getEventLevel5());
        dto.setAction(x.getEventLevel6());
        dto.setActionDetail("");
        dto.setContent(x.getContent());
        dto.setOperator(x.getEventSrc());
        dto.setRegionId(x.getEventLevel9());
        dto.setItemId(x.getEventLevel7());
        dto.setCreateTime(DateUtils.changeUTC2Date(x.getBeginTime()));

        return dto;
    }).collect(Collectors.toList());

    public static Function<List<EventCenterDO>, List<EventCenterDTO>> nacosConvert = (doList) -> Optional.ofNullable(doList).orElse(new ArrayList<>()).stream().map(x -> {
        EventCenterDTO dto = new EventCenterDTO();
        dto.setClusterId(x.getEventLevel8());
        dto.setCategory(x.getEventLevel5());
        dto.setAction(x.getEventLevel6());
        dto.setActionDetail(x.getEventLevel7());
        dto.setContent(x.getContent());
        dto.setOperator(x.getEventSrc());
        dto.setRegionId(x.getEventLevel9());
        dto.setItemId(x.getEventLevel7());
        dto.setCreateTime(DateUtils.changeUTC2Date(x.getBeginTime()));

        return dto;
    }).collect(Collectors.toList());

    @Value("#{${webop.api.url}}")
    private Map<String, String> webOpUrl;

    @Autowired
    private ClickhouseHandlerFactory clickhouseHandlerFactory;

    public List<EventCenterDTO> searchEvent(String dbName, String sql, Function<List<EventCenterDO>, List<EventCenterDTO>> function) {

        List<Map<String, Object>> query = clickhouseHandlerFactory.get().query(dbName, sql);

        List<EventCenterDO> result = EventCenterSqlParserUtil.parseQueryResult(query);

        return function.apply(result);

    }

    public List<EventCenterV2DTO> searchEventV2(String dbName, String sql, Function<List<EventCenterV2DO>, List<EventCenterV2DTO>> function) {

        List<Map<String, Object>> query = clickhouseHandlerFactory.get().query(dbName, sql);

        List<EventCenterV2DO> result = EventCenterSqlParserUtilV2.parseQueryResult(query);

        return function.apply(result);

    }

    public List<String> fetchData(String dbName, String sql, String column) {

        List<Map<String, Object>> query = clickhouseHandlerFactory.get().query(dbName, sql);

        List<String> result = EventCenterSqlParserUtilV2.parseFetchResult(query, column);

        return result;

    }

    public int countEvent(String dbName, String sql) {
        try {
            List<Map<String, Object>> query = clickhouseHandlerFactory.get().query(dbName, sql);
            return Integer.parseInt(query.get(0).get(EventCenterSqlParserUtil.COUNT_FIELD).toString());

        } catch (Exception e) {
            log.error("count event center query failed, ", e);
        }

        return 0;
    }

    public WebOpAuditResponse searchWebOpEvent(Map<String, String> request, String clusterId) {
        Map<String, String> headerParams = new HashMap<>(2);
        headerParams.put("Authorization", JWTUtils.generateToken("webop_api", "webop", 3600L, null, null));
        AtomicInteger statusCode = new AtomicInteger();
        String url = webOpUrl.get(clusterId);
        if (url == null) {
            log.error("get webop url failed, cluster[{}], webOpUrl map size[{}]", clusterId, webOpUrl.size());
            return null;
        }

        StopWatch sw = new StopWatch();
        sw.start();
        String content = HttpUtils.doGet(Lists.newArrayList(url + AUDIT_API_URL), request, headerParams, null, null, MAX_RETRY_TIME, statusCode);

        if (StringUtils.isBlank(content)) {
            monitorLog(clusterId, statusCode, sw, "false", 0);
            return null;
        }

        WebOpAuditResponse response = JsonUtils.toObject(content, WebOpAuditResponse.class);

        monitorLog(clusterId, statusCode, sw, response.getStatus().toString(), response.getResult().getData().size());

        return response;
    }

    private static void monitorLog(String clusterId, AtomicInteger statusCode, StopWatch sw, String resultCode, int dataSize) {
        MonitorWrapper monitorWrapper = new MonitorWrapper("webop_api_monitor");
        monitorWrapper.addTag("api", AUDIT_API_URL);
        monitorWrapper.addTag("clusterId", clusterId);
        monitorWrapper.addTag("statusCode", statusCode);
        monitorWrapper.addTag("resultCode", resultCode);
        monitorWrapper.setTs(System.currentTimeMillis());
        monitorWrapper.addField("totalTime", sw.getTotalTimeMillis());
        monitorWrapper.addField("dataSize", dataSize);

        monitorLog.info(JsonUtils.toJsonStringIgnoreExp(monitorWrapper));
    }
}
