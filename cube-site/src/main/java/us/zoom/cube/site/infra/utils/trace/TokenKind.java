package us.zoom.cube.site.infra.utils.trace;

/**
 * @author: eason.jia
 * @date: 2024/8/19
 */
public enum TokenKind {

    UNKNOWN("UNKNOWN"),
    PREFIX("PREFIX"),
    NUMERIC("NUMER<PERSON>"),
    BOOLEAN("B<PERSON><PERSON><PERSON>N"),
    STRING("STRING"),
    PATTERN("PATTERN"),
    TIME("TIME"),
    VARI<PERSON>LE("VARIABLE"),
    FUNCTION("FUNCTION"),
    SEPARATOR("SEPARATOR"),
    ACCESSOR("ACCESSOR"),
    COMPARATOR("COMPARATOR"),
    LOGICALOP("LOGICALOP"),
    MODIFIER("MODIFIER"),
    CLAUSE("CLAUSE"),
    CLAUSE_CLOSE("CLAUSE_CLOSE"),
    TERNARY("TERNARY");

    String kind;

    TokenKind(String kind) {
        this.kind = kind;
    }

    public String getKind() {
        return kind;
    }
}
