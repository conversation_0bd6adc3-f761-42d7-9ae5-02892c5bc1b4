package us.zoom.cube.site.lib.monitor;

import lombok.Builder;
import lombok.Data;
import us.zoom.cube.sdk.annotation.CubeMonitorLog;
import us.zoom.cube.sdk.annotation.Field;
import us.zoom.cube.sdk.annotation.Tag;

/**
 * @author: <PERSON><PERSON>g
 * @date: 2024/8/29 15:26
 * @desc:
 */
@Data
@Builder
@CubeMonitorLog(measure = "SameTagFunctionMetric")
public class SameTagFunctionMetric {

    @Tag
    private String serviceId;

    @Tag
    private String metricsId;

    @Tag
    private String metricsName;

    @Field
    private String sameTagAndFunctionFields;

    @Field
    private String editor;

}
