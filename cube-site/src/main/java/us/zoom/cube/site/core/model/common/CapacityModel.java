package us.zoom.cube.site.core.model.common;

/**
 * <AUTHOR>
 * @date 2022-08-03 11:12
 */
public class CapacityModel {
    private String serviceName = "serviceName";
    private String serverCount = "serverCount";
    private String max_us = "max_us";
    private String oneAReduceCount = "1AReduceCount";
    private String twoAReduceCount = "2AReduceCount";
    private String threeAReduceCount = "3AReduceCount";
    private String tags = "serviceName,clusterId,regionId";

    public String getServiceName() {
        return serviceName;
    }

    public void setServiceName(String serviceName) {
        this.serviceName = serviceName;
    }

    public String getServerCount() {
        return serverCount;
    }

    public void setServerCount(String serverCount) {
        this.serverCount = serverCount;
    }

    public String getMax_us() {
        return max_us;
    }

    public void setMax_us(String max_us) {
        this.max_us = max_us;
    }

    public String getOneAReduceCount() {
        return oneAReduceCount;
    }

    public void setOneAReduceCount(String oneAReduceCount) {
        this.oneAReduceCount = oneAReduceCount;
    }

    public String getTwoAReduceCount() {
        return twoAReduceCount;
    }

    public void setTwoAReduceCount(String twoAReduceCount) {
        this.twoAReduceCount = twoAReduceCount;
    }

    public String getThreeAReduceCount() {
        return threeAReduceCount;
    }

    public void setThreeAReduceCount(String threeAReduceCount) {
        this.threeAReduceCount = threeAReduceCount;
    }

    public String getTags() {
        return tags;
    }

    public void setTags(String tags) {
        this.tags = tags;
    }
}
