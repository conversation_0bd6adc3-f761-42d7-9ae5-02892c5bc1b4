package us.zoom.cube.site.lib.output.dataparser;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;
import us.zoom.cube.site.lib.input.DiscoverItem;
import us.zoom.cube.site.lib.input.dataparser.DataField;

import java.util.*;

@Data
public class DiscoverPipelineOut {
    private String id;
    private String tenantId;
    private String tenantName;
    private String dataParserId;
    private String dataParserName;
    private String pipelineName;
    private String filterRule;
    private int status;
    private String action;
    private List<DataField> fields = new ArrayList<>();
    private List<DiscoverItem> tagItems = new ArrayList<>();
    private List<DiscoverItem> fieldItems = new ArrayList<>();
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    private List<Map<String,String>> existIn;
}
