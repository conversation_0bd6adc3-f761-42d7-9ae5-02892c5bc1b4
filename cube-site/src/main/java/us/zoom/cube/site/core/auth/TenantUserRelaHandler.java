package us.zoom.cube.site.core.auth;


import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import us.zoom.cube.lib.common.RoleTypeEnum;
import us.zoom.cube.lib.monitor.MonitorWrapper;
import us.zoom.cube.lib.utils.JsonUtils;
import us.zoom.cube.site.biz.syspara.AuthParaService;
import us.zoom.cube.site.core.TenantHandler;
import us.zoom.infra.dao.model.*;
import us.zoom.infra.dao.service.TenantUserRelaDAO;
import us.zoom.infra.dao.service.UserGroupDAO;
import us.zoom.infra.dao.service.UserGroupTenantRelaDAO;
import us.zoom.infra.dao.service.UserGroupUserRelaDAO;
import us.zoom.infra.model.usergroup.UserGroupCfg;
import us.zoom.infra.thread.NamedThreadFactory;
import us.zoom.infra.utils.IdUtils;
import us.zoom.infra.utils.Instance;

import java.util.*;
import java.util.concurrent.CopyOnWriteArraySet;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.*;


/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class TenantUserRelaHandler implements InitializingBean {

    private Logger monitorLog = LoggerFactory.getLogger("Monitor");

    @Value("${user.default.services:systems_administration,service_monitor,Infra_NWS_hello-zcp,Infra_WebArch_keda-operator}")
    private String userDefaultServices;

    private Set<String> userIdList = new CopyOnWriteArraySet();

    /**
     * Map<userId ,Map<serviceId,Set<RoleName>><
     */
    private static  final AtomicReference<Map<String,Map<String,Set<String>>>> userIdServiceRoleMapRef = new AtomicReference<>(new HashMap<>());

    private static ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1, new NamedThreadFactory("user role scheduler"));

    /**
     * Map<groupId ,UserGroupCfg>
     */
    private static final AtomicReference<Map<String, UserGroupCfg>> userGroupRef = new AtomicReference<>(new HashMap<>());

    @Autowired
    private AuthParaService authParaService;

    @Autowired
    private TenantUserRelaDAO tenantUserRelaDAO;

    @Autowired
    private MenuHandler menuHandler;

    @Autowired
    private TenantHandler tenantHandler;

    @Autowired
    private UserRoleHandler userRoleHandler;

    @Autowired
    private UserGroupDAO userGroupDAO;

    @Autowired
    private UserGroupUserRelaDAO userGroupUserRelaDAO;

    @Autowired
    private UserGroupTenantRelaDAO userGroupTenantRelaDAO;

    @PostConstruct
    public void loadRela() {
        long begin = System.currentTimeMillis();
        MonitorWrapper monitorWrapper = new MonitorWrapper("load_tenant_user_rela");

        try{
            List<TenantUserRelaDO> allRelas = tenantUserRelaDAO.listAll();
            monitorWrapper.addField("query_db_cost",System.currentTimeMillis() - begin);
            if(CollectionUtils.isEmpty(allRelas)){
                monitorWrapper.addTag("phase","no_rela");
                monitorWrapper.addTag("status","fail");
                printMonitorLog(monitorWrapper,begin);
                return;
            }
            Map<String, Map<String, Set<String>>> userIdServiceRoleMap = getIdServiceRoleMap(allRelas);

            if(MapUtils.isNotEmpty(userIdServiceRoleMap)){
                userIdServiceRoleMapRef.set(userIdServiceRoleMap);
            }
            monitorWrapper.addTag("status","success");
        }catch (Exception e){
            log.error("load Role Menu Rela error ",e);
            monitorWrapper.addTag("status","fail");
            monitorWrapper.addField("msg",e.getMessage());
            monitorWrapper.addField("exp",JsonUtils.toJsonStringIgnoreExp(e));
        }
        printMonitorLog(monitorWrapper,begin);

        Map<String, Map<String, Set<String>>> userServiceRoleMapFromUserGroup = loadUserGroupRela();

        if (null == userServiceRoleMapFromUserGroup) {
            return;
        }
        //merge role in each tenant
        for (Map.Entry<String, Map<String, Set<String>>> entry : userServiceRoleMapFromUserGroup.entrySet()) {
            Map<String, Set<String>> serviceRoleMap = userIdServiceRoleMapRef.get().get(entry.getKey());
            if (null == serviceRoleMap) {
                userIdServiceRoleMapRef.get().put(entry.getKey(), entry.getValue());
            } else {
                for (Map.Entry<String, Set<String>> innerEntry : entry.getValue().entrySet()) {
                    Set<String> roles = serviceRoleMap.get(innerEntry.getKey());
                    if (null == roles) {
                        roles = innerEntry.getValue();
                    } else {
                        roles.addAll(innerEntry.getValue());
                    }

                    serviceRoleMap.put(innerEntry.getKey(), roles);
                }
            }
        }
    }

    private Map<String, Map<String, Set<String>>> getIdServiceRoleMap(List<TenantUserRelaDO> allRelas) {
        Map<String,Map<String,Set<String>>> userIdServiceRoleMap = new HashMap<>();

        for(TenantUserRelaDO relaDO : allRelas){
            Map<String,Set<String>> serviceRoleMap =  userIdServiceRoleMap.computeIfAbsent(relaDO.getUserId(),f -> new HashMap<>());
            Set<String> roles = serviceRoleMap.computeIfAbsent( relaDO.getTenantId(), f -> new HashSet<>());
            if(StringUtils.isNotBlank(relaDO.getRole())){
                roles.add(relaDO.getRole());
            }
        }
        return userIdServiceRoleMap;
    }


    public Map<String, Map<String, Set<String>>>  loadUserGroupRela(){

        long begin = System.currentTimeMillis();
        MonitorWrapper monitorWrapper = new MonitorWrapper("load_user_group");
        //Map<userId,Map<tenantId,Set<Role>>>
        Map<String,Map<String,Set<String>>> userIdServiceRoleMap = new HashMap<>();
        try{
            List<UserGroupDO> allGroups= userGroupDAO.listAll();

            List<UserGroupTenantRelaDO> userGroupTenantRelaDOS = userGroupTenantRelaDAO.listAll();

            List<UserGroupUserRelaDO> userGroupUserRelaDOS =  userGroupUserRelaDAO.listAll();

            if(CollectionUtils.isEmpty(allGroups) || CollectionUtils.isEmpty(userGroupTenantRelaDOS) || CollectionUtils.isEmpty(userGroupUserRelaDOS)){
                monitorWrapper.addTag("phase","no_rela");
                monitorWrapper.addTag("status","fail");
                printMonitorLog(monitorWrapper,begin);
                return userIdServiceRoleMap;
            }
            //Map<groupId,UserGroupDO>
            Map<String,UserGroupDO> idGroupMap = new HashMap<>();
            allGroups.forEach(item->idGroupMap.put(item.getId(),item));

            //Map<groupId,List<UserGroupTenantRelaDO>>
            Map<String,List<UserGroupTenantRelaDO>> groupServiceMap = new HashMap<>();
            userGroupTenantRelaDOS.forEach(item->{
                List<UserGroupTenantRelaDO> relaForOneGroup = groupServiceMap.computeIfAbsent(item.getGroupId(),f->new ArrayList<>());
                relaForOneGroup.add(item);
            });
            monitorWrapper.addField("query_db_cost",System.currentTimeMillis() - begin);


            for (UserGroupUserRelaDO userGroupUserRelaDO : userGroupUserRelaDOS) {

                Map<String, Set<String>> serviceRoleMap = userIdServiceRoleMap.computeIfAbsent(userGroupUserRelaDO.getUserId(), f -> new HashMap<>());
                if (StringUtils.isBlank(userGroupUserRelaDO.getGroupId())) {
                    continue;
                }

                UserGroupDO userGroupDO = idGroupMap.get(userGroupUserRelaDO.getGroupId());
                if (null == userGroupDO || StringUtils.isBlank(userGroupDO.getRole())) {
                    continue;
                }

                List<UserGroupTenantRelaDO> relaForOneGroup = groupServiceMap.get(userGroupUserRelaDO.getGroupId());
                if (CollectionUtils.isEmpty(relaForOneGroup)) {
                    continue;
                }

                for(UserGroupTenantRelaDO userGroupTenantRelaDO: relaForOneGroup){
                    Set<String> roles = serviceRoleMap.computeIfAbsent( userGroupTenantRelaDO.getTenantId(), f -> new HashSet<>());
                    roles.add(userGroupDO.getRole());
                }
            }

            Map<String, UserGroupCfg> userGroupCfgMap = new HashMap<>();
            Map<String, List<String>> groupUserMap = Instance.ofNullable(userGroupUserRelaDOS)
                    .stream().collect(groupingBy(UserGroupUserRelaDO::getGroupId, mapping(UserGroupUserRelaDO::getUserId, toList())));
            Map<String, List<String>> groupTenantMap = Instance.ofNullable(userGroupTenantRelaDOS)
                    .stream().collect(groupingBy(UserGroupTenantRelaDO::getGroupId, mapping(UserGroupTenantRelaDO::getTenantId, toList())));

            Instance.ofNullable(allGroups).forEach(x -> {
                UserGroupCfg userGroupCfg = new UserGroupCfg();
                userGroupCfg.setId(x.getId());
                userGroupCfg.setName(x.getName());
                userGroupCfg.setRole(x.getRole());
                if (null != groupUserMap.get(x.getId())) {
                    userGroupCfg.setUserIdList(groupUserMap.get(x.getId()));
                }
                if (null != groupTenantMap.get(x.getId())) {
                    userGroupCfg.setTenantIdList(groupTenantMap.get(x.getId()));
                }

                userGroupCfgMap.put(x.getId(), userGroupCfg);
            });

            userGroupRef.set(userGroupCfgMap);

            monitorWrapper.addTag("status","success");
        }catch (Exception e){
            log.error("load user group error ",e);
            monitorWrapper.addTag("status","fail");
            monitorWrapper.addField("msg",e.getMessage());
            monitorWrapper.addField("exp", JsonUtils.toJsonStringIgnoreExp(e));
        }
        printMonitorLog(monitorWrapper,begin);
        return userIdServiceRoleMap;
    }

    private void printMonitorLog(MonitorWrapper monitorWrapper, long begin) {
        monitorWrapper.setTs(System.currentTimeMillis());
        monitorWrapper.addField("totalTime",System.currentTimeMillis()-begin);
        monitorLog.info(JsonUtils.toJsonStringIgnoreExp(monitorWrapper));
    }

    public Map<String, Set<String>> getUserTenantRoles(String userId) {
        userIdList.add(userId);
        return userIdServiceRoleMapRef.get().get(userId);
    }

    public boolean checkContainServiceByUserId(String userId, String serviceId) {
        Map<String, Set<String>> userTenantRoles = getUserTenantRoles(userId);
        return userTenantRoles.containsKey(serviceId);
    }

    public boolean checkContainRoleByUserId(String userId, String roleId) {
        boolean result = false;
        Map<String, Set<String>> userTenantRoles = getUserTenantRoles(userId);
        for (String serviceId : userTenantRoles.keySet()) {
            Set<String> roles = userTenantRoles.get(serviceId);
            if(roles.contains(roleId)){
                result = true;
                break;
            }
        }

        return result;
    }



    public Set<String> getCanOperateUsers(String tenantId) {
        //get all cross service role
        Set<String> crossServiceRoles = userRoleHandler.getCrossServiceRole(userRoleHandler.getAllRoles());

        //user have cross role
//        Set<String> adminUser = userIdServiceRoleMapRef.get().entrySet().stream().filter(entry -> {
//            Set<String> allRoles = entry.getValue().values().stream().flatMap(Collection::stream).collect(Collectors.toSet());
//            for (String crossServiceRole : crossServiceRoles) {
//                if (allRoles.contains(crossServiceRole)) {
//                    return true;
//                }
//            }
//            return false;
//        }).map(entry -> entry.getKey()).collect(Collectors.toSet());

        //get all can operate user
        Set<String> operateUsers = userIdServiceRoleMapRef.get().entrySet().stream().filter(entry -> {
            Set<String> roleInTenant = entry.getValue().get(tenantId);
            if (CollectionUtils.isEmpty(roleInTenant)) {
                return false;
            }
            for (String role : roleInTenant) {
                UserRoleDO roleByName = userRoleHandler.getRoleByName(role);
                if (roleByName != null && roleByName.getCanOperate()) {
                    return true;
                }
            }
            return false;
        }).map(entry -> entry.getKey()).collect(Collectors.toSet());

//        adminUser.addAll(operateUsers);
        return operateUsers;
    }

    public Map<String, Map<String, Set<String>>> getUserTenantRolesFromDbAndCache(List<String> userIdList, boolean includeCache) {
        Map<String, Map<String, Set<String>>> ret = new HashMap<>();
        if (CollectionUtils.isEmpty(userIdList)) {
            return ret;
        }
        List<TenantUserRelaDO> allRelas = tenantUserRelaDAO.getRelationsByUserIdListAndTenantId(userIdList, null);

        Set<TenantUserRelaDO> tenantUserRelaDOSet = new HashSet<>();
        tenantUserRelaDOSet.addAll(allRelas);

        if (includeCache) {
            userIdList.forEach(userId -> {
                //merge user group role from cache
                Map<String, Set<String>> userTenantRoles = getUserTenantRoles(userId);
                Instance.ofNullable(userTenantRoles).entrySet().forEach(entry -> {

                    String tenantId = entry.getKey();
                    Set<String> roles = entry.getValue();
                    roles.forEach(role -> {
                        TenantUserRelaDO temp = new TenantUserRelaDO();
                        temp.setTenantId(tenantId);
                        temp.setRole(role);
                        temp.setUserId(userId);

                        tenantUserRelaDOSet.add(temp);
                    });
                });
            });
        }

        Map<String, Map<String, Set<String>>> userIdServiceRoleMap = getIdServiceRoleMap(new ArrayList<>(tenantUserRelaDOSet));

        return userIdServiceRoleMap;
    }

    public List<TenantUserRelaDO> getTenantUserRelaByUserIdsAndTenantId(List<String> userIds, String tenantId){
        return tenantUserRelaDAO.getRelationsByUserIdListAndTenantId(userIds, tenantId);
    }

    public Map<String, Set<String>> getUserTenantRolesFromDbAndCache(String userId, boolean includeCache) {

        Map<String, Map<String, Set<String>>> userTenantRolesFromDbAndCache = this.getUserTenantRolesFromDbAndCache(Lists.newArrayList(userId), includeCache);

        return userTenantRolesFromDbAndCache.get(userId);
    }

    /**
     * reload user and tenant rela into cache
     * @param userId
     */
    public void reloadUserToCache(String userId) {
        Map<String, Set<String>> userTenantRolesFromDB = getUserTenantRolesFromDbAndCache(userId, false);
        while (true) {
            Map<String, Map<String, Set<String>>> prevUserRoleMap = userIdServiceRoleMapRef.get();
            Map<String, Map<String, Set<String>>> update = new HashMap<>(prevUserRoleMap.size() + 1);
            update.putAll(prevUserRoleMap);
            update.put(userId, userTenantRolesFromDB);
            if (userIdServiceRoleMapRef.compareAndSet(prevUserRoleMap, update)) {
                break;
            }
        }

    }


    public List<TenantUserRelaDO> getRelaByRoleFromDb(String role) {
        return tenantUserRelaDAO.getRelaByRole(role);
    }

    public List<TenantUserRelaDO> getRelaByUserId(String userId){
        return tenantUserRelaDAO.findTenantUserRelationByUseId(userId);
    }

    public List<TenantUserRelaDO> listRelaByRoleAndTenantId(String role, String tenantId) {
        return tenantUserRelaDAO.listRelaByRoleAndTenantId(role,tenantId);
    }

    public List<TenantUserRelaDO>  listRelaByRoleAndTenantIdAndUserId(String role, String tenantId, String userId) {
        return tenantUserRelaDAO.listRelaByRoleAndTenantIdAndUserId(role,tenantId,userId);
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        scheduler.scheduleAtFixedRate(() -> {
            try {
                checkUserRole();
            } catch (Exception e) {
                log.error("Exception when checkUserRole, ", e);
            }
        }, 0, RandomUtils.nextLong(30, 120), TimeUnit.MINUTES);
    }

    /**
     * check if user has default role
     */
    private void checkUserRole() {
        //read the list from SysParam, then properties file
        List<String> userDefaultServicesList = authParaService.getUserDefaultServices();
        List<String> split;
        if (CollectionUtils.isNotEmpty(userDefaultServicesList)) {
            split = userDefaultServicesList;
        } else {
            split = Splitter.on(",").trimResults().splitToList(userDefaultServices);
        }

        userIdList.forEach(x -> {
            if (StringUtils.isBlank(x)) {
                return;
            }

            List<TenantUserRelaDO> relaDOS = new ArrayList<>();
            split.forEach(y -> {
                Map<String, Set<String>> userTenantRoles = this.getUserTenantRoles(x);
                if (userTenantRoles == null) {
                    userTenantRoles = new HashMap<>();
                }
                TenantDO tenantByNameIgnoreCase = tenantHandler.getTenantByNameIgNoreCase(y);
                if (tenantByNameIgnoreCase == null) {
                    return;
                }
                Set<String> roles = userTenantRoles.get(tenantByNameIgnoreCase.getId());
                if (CollectionUtils.isNotEmpty(roles)) {
                    return;
                }
                TenantUserRelaDO e = new TenantUserRelaDO();
                e.setRole(RoleTypeEnum.applicationGuests.name());
                e.setTenantId(tenantByNameIgnoreCase.getId());
                e.setId(IdUtils.generateId());
                e.setUserId(x);
                relaDOS.add(e);

                HashSet<String> roleSet = Sets.newHashSet(RoleTypeEnum.applicationGuests.name());
                userTenantRoles.put(tenantByNameIgnoreCase.getId(), roleSet);
                userIdServiceRoleMapRef.get().put(x, userTenantRoles);
            });
            try {
                if (CollectionUtils.isNotEmpty(relaDOS)) {
                    this.tenantHandler.addTenantAll(relaDOS);
                }
            } catch (Exception e) {
                log.error("check role error, ", e);
            } finally {
                userIdList.remove(x);
            }

        });
    }

    public Map<String,Map<String,Set<String>>> getUserIdServiceRoleMap(){
        return userIdServiceRoleMapRef.get();
    }

    public Map<String, UserGroupCfg> getUserGroupMap(){
        return userGroupRef.get();
    }


}
