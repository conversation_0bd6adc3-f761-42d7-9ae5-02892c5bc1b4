package us.zoom.cube.site.lib.profiling;

import lombok.Data;
import us.zoom.cube.site.lib.profiling.showdetails.ShowDisplayDetailsDto;

import java.util.Map;

/**
 * @author: <PERSON><PERSON> Ding
 * @date: 2024/12/2 09:35
 * @desc:
 */
@Data
public class ProfilingUserSettingDto {

    private ShowDisplayDetailsDto showDetails;

    private String colorSchema = "classic";

    public ProfilingUserSettingDto() {
    }

    public ProfilingUserSettingDto(Map<String, Boolean> hostDisplay, Map<String, Boolean> podDisplay, String colorSchema) {
        this.showDetails = new ShowDisplayDetailsDto(hostDisplay, podDisplay);
        this.colorSchema = colorSchema;
    }
}
