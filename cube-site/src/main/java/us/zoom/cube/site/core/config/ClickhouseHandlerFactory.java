package us.zoom.cube.site.core.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.EnvironmentAware;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;
import us.zoom.infra.clickhouse.ClickhouseEnvProxy;
import us.zoom.infra.clickhouse.ClickhouseMultiHandler;
import us.zoom.infra.clickhouse.ClickhouseWriter;
import us.zoom.infra.dao.service.EnvironmentDAO;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022-01-19 10:11
 */
@Component
@Slf4j
@Configuration
public class ClickhouseHandlerFactory implements EnvironmentAware {

    private Environment environment;

    private ClickhouseMultiHandler handler;

    private boolean runOnLocal=false;

    private ClickhouseWriter clickhouseWriter;

    private ClickhouseEnvProxy clickhouseEnvProxy;

    private String cnFirewall="";

    @Autowired
    private EnvironmentDAO environmentDAO;

    @Override
    public void setEnvironment(Environment environment) {
        this.environment = environment;
        initRunOnLocal();
        initHandler();
        initClickhouseEnvProxy();
        initClickhouseWriter();
        initCnFirewall();
    }

    private void initRunOnLocal() {
        runOnLocal = environment.getProperty("spring.profiles.active", "").endsWith("_local");
    }

    private void initHandler() {
        this.handler = new ClickhouseMultiHandler();
    }

    private void initClickhouseEnvProxy() {
        clickhouseEnvProxy = new ClickhouseEnvProxy();
    }

    private void initClickhouseWriter() {
        int flushMajorIntervalMillSecond = Integer.parseInt(environment.getProperty("cube.clickhouse.flush.major.interval.millsecond", "20000"));
        int flushMinorIntervalMillSecond = Integer.parseInt(environment.getProperty("cube.clickhouse.flush.minor.interval.millsecond", "5000"));
        int flushMaxRecordCount = Integer.parseInt(environment.getProperty("cube.clickhouse.flush.max.batch.size", "100000"));
        int flushMinRecordCountPerTable = Integer.parseInt(environment.getProperty("cube.clickhouse.flush.min.batch.size", "500"));
        int threadPoolSize = Integer.parseInt(environment.getProperty("cube.clickhouse.thread.pool.size", "2"));
        int flushParallelism = Integer.parseInt(environment.getProperty("cube.clickhouse.flush.parallelism", "4"));
        long queueMemoryLimit = Long.parseLong(environment.getProperty("cube.clickhouse.queue.memory.limit", "5368709120"));
        long singleTableMemoryLimit = Long.parseLong(environment.getProperty("cube.clickhouse.single.table.limit", "209715200"));
        Set<String> allEnv = environmentDAO.searchAll().stream().map(u -> u.getName()).collect(Collectors.toSet());
        clickhouseWriter = new ClickhouseWriter(this.clickhouseEnvProxy)
                .setFlushMajorIntervalMillSecond(flushMajorIntervalMillSecond)
                .setFlushMinorIntervalMillSecond(flushMinorIntervalMillSecond)
                .setFlushMaxRecordCount(flushMaxRecordCount)
                .setFlushMinRecordCountPerTable(flushMinRecordCountPerTable)
                .setThreadPoolSize(threadPoolSize)
                .setFlushParallelism(flushParallelism)
                .setQueueMemoryLimit(queueMemoryLimit)
                .setSingleTableMemoryLimit(singleTableMemoryLimit)
                .setAllEnvs(allEnv)
                .start();
    }

    private void initCnFirewall() {
        cnFirewall = environment.getProperty("cube.site.clickhouse.cn.outbound.firewall", "");
    }

    public Set<String>getCnFireWall(){
        return new HashSet<>(Arrays.asList(cnFirewall.split(",")));
    }

    public boolean isRunOnLocal() {
        return runOnLocal;
    }


    public ClickhouseEnvProxy get(){
        return clickhouseEnvProxy;
    }

    public ClickhouseWriter getClickhouseWriter() {
        return clickhouseWriter;
    }
}
