package us.zoom.cube.site.core.parser.process.core.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import us.zoom.cube.site.core.parser.process.core.common.DataParserLogTypeEnum;
import us.zoom.cube.site.core.parser.process.core.monitoring.Measure;

import java.util.Map;

/**
 * @Author: <EMAIL>
 * @ModuleOwner: <EMAIL>
 * @Date: 12/28/2022 10:26
 * @Description:
 */
@Data
@AllArgsConstructor
public class PreProcessDO {
    private DataParserLogTypeEnum type;
    private Map<String, Object> map;
    //Only the types of agent_standard and standard, measure is not null
    private Measure measure;

    public PreProcessDO(DataParserLogTypeEnum type, Map<String, Object> map) {
        this.type = type;
        this.map = map;
    }
}
