package us.zoom.cube.site.core;

import com.zoom.op.monitor.dao.alarm.ChannelDao;
import com.zoom.op.monitor.domain.alarm.Channel;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import us.zoom.infra.dao.model.DataParserDO;
import us.zoom.infra.dao.service.DataParserDAO;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

@Component
public class ChannelHandler {

    @Autowired
    private ChannelDao channelDao;

    public Optional<Channel> findByNameAndTenantId(String name, String tenantId){
        return channelDao.findByNameAndTenantId(name,tenantId);
    }

    public void deleteById(String id){
        channelDao.deleteById(id);
    }

    public List<Channel> findByTenantId(String tenantId){
        return channelDao.findByTenantId(tenantId);
    }

    public Optional<Channel> findById(String id) {
        Assert.hasText(id, "id is empty");
        return channelDao.findById(id);
    }

    public List<Channel> listChannelNotExistInTenantMatchByName(List<String> includeChannelIds, String tenantId){

        if(CollectionUtils.isEmpty(includeChannelIds)|| StringUtils.isBlank(tenantId)){
            return Collections.emptyList();
        }
        return channelDao.listChannelNotExistInTenantMatchByName(includeChannelIds,tenantId);
    }

    public List<Channel> batchSave(List<Channel> channels){
        if(CollectionUtils.isEmpty(channels)){
            return Collections.emptyList();
        }
        return channelDao.saveAll(channels);
    }



}
