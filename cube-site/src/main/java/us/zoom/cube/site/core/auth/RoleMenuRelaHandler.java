package us.zoom.cube.site.core.auth;


import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import us.zoom.cube.lib.monitor.MonitorWrapper;
import us.zoom.infra.dao.model.RoleMenuRelaDO;
import us.zoom.infra.dao.service.RoleMenuRelaDAO;
import us.zoom.infra.model.menu.MenuTree;
import us.zoom.cube.lib.utils.JsonUtils;

import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class RoleMenuRelaHandler {

    private Logger monitorLog = LoggerFactory.getLogger("Monitor");

    /**
     * Map<RoleName,MenuResourceId></RoleName,MenuResourceId>
     */
    private static  final AtomicReference<Map<String,Set<String>>> roleNameResIdMapRef = new AtomicReference<>(new HashMap<>());

    @Autowired
    private RoleMenuRelaDAO roleMenuRelaDAO;

    @Autowired
    private MenuHandler menuHandler;

    @PostConstruct
    public void loadRela() {
        long begin = System.currentTimeMillis();
        MonitorWrapper monitorWrapper = new MonitorWrapper("load_role_menu_rea");

        try{
            List<RoleMenuRelaDO> allRelas = roleMenuRelaDAO.listAll();
            monitorWrapper.addField("query_db_cost",System.currentTimeMillis() - begin);
            if(CollectionUtils.isEmpty(allRelas)){
                monitorWrapper.addTag("phase","no_rela");
                monitorWrapper.addTag("status","fail");
                printMonitorLog(monitorWrapper,begin);
                return;
            }
            Map<String,Set<String>> roleNameMenuResIdMap = new HashMap<>();

            for(RoleMenuRelaDO relaDO : allRelas ){
                Set<String> menuResIds = roleNameMenuResIdMap.computeIfAbsent(relaDO.getRoleName(),f-> new HashSet<>());
                if(StringUtils.isNotBlank(relaDO.getMenuResId())){
                    menuResIds.add(relaDO.getMenuResId());
                }
            }

            if(MapUtils.isNotEmpty(roleNameMenuResIdMap)){
                roleNameResIdMapRef.set(roleNameMenuResIdMap);
            }

            monitorWrapper.addTag("status","success");
        }catch (Exception e){
            log.error("load Role Menu Rela error ",e);
            monitorWrapper.addTag("status","fail");
            monitorWrapper.addField("msg",e.getMessage());
            monitorWrapper.addField("exp",JsonUtils.toJsonStringIgnoreExp(e));
        }
        printMonitorLog(monitorWrapper,begin);
    }


    /**
     * get menu resource id set by auth name
     * @param roleName
     * @return
     */
    public Set<String> getMenuResIdsByRoleName(String roleName){

        if(StringUtils.isBlank(roleName)){
            return Collections.EMPTY_SET;
        }
        Set<String> result = roleNameResIdMapRef.get().get(roleName);
        return  null == result ? Collections.EMPTY_SET : result;
    }

    /**
     * get menu resource url set by auth name
     * @param roleName
     * @return
     */
    public Set<String> getMenuResUrlByRoleName(String roleName){

        if(StringUtils.isBlank(roleName)){
            return Collections.EMPTY_SET;
        }
        Set<String> resIds = roleNameResIdMapRef.get().get(roleName);
        if(CollectionUtils.isEmpty(resIds)){
            return Collections.EMPTY_SET;
        }
        return menuHandler.getMenuResUrlsByResIds(resIds);
    }


    private void printMonitorLog(MonitorWrapper monitorWrapper, long begin) {
        monitorWrapper.setTs(System.currentTimeMillis());
        monitorWrapper.addField("totalTime",System.currentTimeMillis()-begin);
        monitorLog.info(JsonUtils.toJsonStringIgnoreExp(monitorWrapper));
    }


    public MenuTree getRoleMenuTree(String roleName) {
        if(StringUtils.isBlank(roleName)){
            return MenuTree.emptyTree();
        }
        List<RoleMenuRelaDO> roleMenuRelaDOS =   roleMenuRelaDAO.listByRoleName(roleName);
        if(CollectionUtils.isEmpty(roleMenuRelaDOS)){
            return MenuTree.emptyTree();
        }
        Set<String> menuResIds = roleMenuRelaDOS.stream().filter(item->StringUtils.isNotBlank(item.getMenuResId())).map(item->item.getMenuResId()).collect(Collectors.toSet());
        return  menuHandler.getMenuTreeWitchCheck(menuResIds);
    }


    @Transactional
    public void assignMenu(List<RoleMenuRelaDO> roleMenuRelas){
        if(CollectionUtils.isEmpty(roleMenuRelas)){
            return;
        }

        List<String> roleNames = roleMenuRelas.stream().map(RoleMenuRelaDO::getRoleName).distinct().collect(Collectors.toList());
        roleMenuRelaDAO.deleteRelaByRoleNames(roleNames);
        roleMenuRelaDAO.batchAddRela(roleMenuRelas);
    }


    public List<RoleMenuRelaDO> getRelaByRoleFromDB(String roleName) {
        return roleMenuRelaDAO.listByRoleName(roleName);
    }

    public List<RoleMenuRelaDO> listAll() {
        return roleMenuRelaDAO.listAll();
    }

    public void batchAddRela(List<RoleMenuRelaDO> roleMenuRelas) {
        roleMenuRelaDAO.batchAddRela(roleMenuRelas);
    }
}
