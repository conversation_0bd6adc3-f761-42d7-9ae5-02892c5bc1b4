package us.zoom.cube.site.lib.output.codeperformance;

import lombok.Data;

import java.util.List;

/**
 * @author: tobey.zhu
 * @date: /2023/07/20
 * @description:
 */
@Data
public class ServiceInfoOut {
    private List<String> processList;
    private List<Dimension> dimensions;
    
    @Data
    public static class Dimension {
        private String clusterId;
        private String regionId;
        private String host;
        private String zoneName;
    }

    public ServiceInfoOut(List<String> processList, List<Dimension> dimensions) {
        this.processList = processList;
        this.dimensions = dimensions;
    }
}
