package us.zoom.cube.site.core;


import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import us.zoom.infra.dao.model.TaskInfluxdbClusterRelationDO;
import us.zoom.infra.dao.model.TaskQueueDO;
import us.zoom.infra.dao.service.TaskInfluxdbClusterRelationDAO;
import us.zoom.infra.dao.service.TaskQueueDAO;

import java.util.Collections;
import java.util.LinkedList;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;


/**
 * <AUTHOR>
 * @date 2021/12/31 9:32 AM
 */
@ExtendWith(MockitoExtension.class)
public class TaskQueueHandlerTest {

    @InjectMocks
    private TaskQueueHandler taskQueueHandler;

    @Mock
    private TaskQueueDAO taskQueueDAO;

    @Mock
    private TaskInfluxdbClusterRelationDAO taskInfluxdbClusterRelationDAO;

    @Test
    public void testFindByParam() {
        List<TaskQueueDO> kafkaClusterDOS = new LinkedList<>();
        TaskQueueDO t = new TaskQueueDO();
        t.setId("1");
//        t.setInfluxdbClusters(Collections.singletonList("1"));
        kafkaClusterDOS.add(t);
        Mockito.when(taskQueueDAO.findByParam(anyString(), anyString(), anyString(), anyInt(), anyInt())).thenReturn(kafkaClusterDOS);
        List<TaskInfluxdbClusterRelationDO> tifcrdList = new LinkedList<>();
        TaskInfluxdbClusterRelationDO tifcr = new TaskInfluxdbClusterRelationDO();
        tifcr.setTaskId("1");
        tifcr.setInfluxClusterId("cluster");
        tifcrdList.add(tifcr);
        Mockito.when(taskInfluxdbClusterRelationDAO.getByTaskIds(any())).thenReturn(tifcrdList);
        List<TaskQueueDO> ret = taskQueueHandler.findByParam("name", "topic", "type", 1, 1);
        Assertions.assertNotNull(ret);
    }
}
