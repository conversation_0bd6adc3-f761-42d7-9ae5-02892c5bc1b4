package us.zoom.cube.site.lib.output.dataparser;

public class TimestampProcessorOut extends BaseProcessorOut{
    private String sourceField;
    private String targetField;
    private String dateFormat;

    public String getSourceField() {
        return sourceField;
    }

    public void setSourceField(String sourceField) {
        this.sourceField = sourceField;
    }

    public String getTargetField() {
        return targetField;
    }

    public void setTargetField(String targetField) {
        this.targetField = targetField;
    }

    public String getDateFormat() {
        return dateFormat;
    }

    public void setDateFormat(String dateFormat) {
        this.dateFormat = dateFormat;
    }

    public TimestampProcessorOut(){}

    public TimestampProcessorOut(String id,String name,Integer order,String dataParserPipelineId,String dataParserId,String type,String sourceField,String targetField,String dateFormat){
        super(id,name,order,dataParserPipelineId,dataParserId,type);
        this.sourceField=sourceField;
        this.targetField=targetField;
        this.dateFormat=dateFormat;
    }
}
