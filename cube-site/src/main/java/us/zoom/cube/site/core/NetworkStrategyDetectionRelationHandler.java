package us.zoom.cube.site.core;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import us.zoom.infra.dao.model.NetworkStrategyDetectionRelationDO;
import us.zoom.infra.dao.service.NetworkStrategyDetectionRelationDAO;

import java.util.List;

@Component
public class NetworkStrategyDetectionRelationHandler {
    @Autowired
    private NetworkStrategyDetectionRelationDAO networkStrategyDetectionRelationDAO;

    public int insertBatch(List<NetworkStrategyDetectionRelationDO> networkStrategyDetectionRelationDOList) {
        return networkStrategyDetectionRelationDAO.insertBatch(networkStrategyDetectionRelationDOList);
    }

    public List<NetworkStrategyDetectionRelationDO> selectByStrategyId(String strategyId) {
        return networkStrategyDetectionRelationDAO.selectByStrategyId(strategyId);
    }

    public List<NetworkStrategyDetectionRelationDO> selectByStrategyAndDetection(String strategyId, List<String> detectionIds) {
        return networkStrategyDetectionRelationDAO.selectByStrategyAndDetection(strategyId,detectionIds);
    }
}
