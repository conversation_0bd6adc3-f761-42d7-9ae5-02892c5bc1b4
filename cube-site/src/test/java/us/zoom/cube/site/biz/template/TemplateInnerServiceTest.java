package us.zoom.cube.site.biz.template;


import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ActiveProfiles;
import us.zoom.cube.site.biz.BaseTest;
import us.zoom.cube.site.biz.DataParserDeleteService;
import us.zoom.cube.site.core.ChannelHandler;
import us.zoom.cube.site.lib.ResponseObject;
import us.zoom.cube.lib.utils.JsonUtils;

import java.util.*;
import java.util.stream.Collectors;

import static us.zoom.cube.site.biz.template.TemplateInnerService.*;

@ActiveProfiles("perf_for_local")
//@ExtendWith(MockitoExtension.class)
public class TemplateInnerServiceTest extends BaseTest {

    @Autowired
    TemplateInnerService templateInnerService;

    @Mock
    ChannelHandler channelHandler;

    @Test
    public void returnValue() {
        Map<String, Object> result = new HashMap<>();
        result.put("type", "update");
        Map<String, Object> value = new HashMap<>();
        value.put("metrics", "cpu");
        value.put("field", "name");
        result.put("value", value);
        Map<String, Object> topic = new HashMap<>();
        topic.put("topic1", "success");
        topic.put("topic2", "faile");
        result.put("topic", topic);
        ResponseObject<Map<String, Object>> success = ResponseObject.success(result);
        System.out.println(JsonUtils.toJsonString(success));
        ResponseObject<Map<String, String>> fail = ResponseObject.fail("error");
        System.out.println(JsonUtils.toJsonString(fail));
    }

    @Test
    public void testCraeteTopic() {

        String destTagNames = "clusterId,test1";
        String sourceTagNames = "clusterId,test2";

        List<String> d = Arrays.stream(destTagNames.split(",")).collect(Collectors.toList());
        // hostName,instanceId,regionId,pool,clusterId,test2
        List<String> s = Arrays.stream(sourceTagNames.split(",")).collect(Collectors.toList());
        boolean b = d.addAll(s);
        System.out.println("222222" + d.stream().distinct().collect(Collectors.joining(",")));

//        TopicCreateParam topicCreateParam = templateInnerService.createTopicPara("us_fred_created");
//        TopicResult topicResult = AsyncmqAdminInstance.getInstance().createTopic(topicParam);
//        System.out.println(topicResult);
    }

    @Test
    public void testAsyncCreateTopic() throws Exception {
        String topic = "fred_test_asyncmq_2";
        Set<String> topicList = new HashSet<>();
        topicList.add(topic);
        topicList.add("ds01_cube_test_1_infra");
        topicList.add("mil_cube_test_1_infra");
        topicList.add("us_cube_test_1_infra");

        Map<String, Object> result = templateInnerService.asyncCreateTopic(topicList);
        if (!result.isEmpty()) {
            Assertions.assertEquals("success", result.get(topic));
        }
    }

    @Test
    public void testMatch() {
        String topic1 = "us_cube_metrics_sync_log";
        if (!matchPlaceholder(topic1)) {
            String dc = topic1.split("_")[0];
            topic1 = topic1.substring(dc.length() + 1);
        }
        Assertions.assertEquals("cube_metrics_sync_log", topic1);

        String topic2 = "{dc}_zcp_kube_resource_metrics";
        if (!matchPlaceholder(topic2)) {
            String dc = topic2.split("_")[0];
            topic2 = topic1.substring(dc.length());
        }
        Assertions.assertEquals("{dc}_zcp_kube_resource_metrics", topic2);

        String topic3 = "~{global.asyncmq.dc}_~{tenant}_infra";
        String dc = topic3.split("_")[0];
        topic3 = topic3.substring(dc.length() + 1);
        Assertions.assertEquals("~{tenant}_infra", topic3);
    }

    @Test
    public void testRemoveTopic() {
        String topic1 = "{dc}_mw_apm_{appName}_metrics";
        Assertions.assertTrue(DataParserDeleteService.removeTopic(topic1));

        String topic2 = "~{global.asyncmq.dc}_mw_apm_~{cmdbApp}_metrics";
        Assertions.assertTrue(DataParserDeleteService.removeTopic(topic2));

        String topic3 = "us_cube_metrics_sync_log";
        Assertions.assertFalse(DataParserDeleteService.removeTopic(topic3));

        String topic4 = "{dc}_cube_probe_monitor_log";
        Assertions.assertFalse(DataParserDeleteService.removeTopic(topic4));

        String topic5 = null;
        Assertions.assertFalse(DataParserDeleteService.removeTopic(topic5));
    }
}