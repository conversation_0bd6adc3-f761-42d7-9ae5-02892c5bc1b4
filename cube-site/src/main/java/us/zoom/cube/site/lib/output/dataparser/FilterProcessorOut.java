package us.zoom.cube.site.lib.output.dataparser;


/**
 * <AUTHOR> @date 2020/3/10
 */
public class FilterProcessorOut extends  BaseProcessorOut{

    private String filterRule;


    public FilterProcessorOut(){

    }

    public FilterProcessorOut(String id, String name, Integer order, String dataParserPipelineId, String dataParserId, String type, String filterRule) {
        super(id, name, order, dataParserPipelineId, dataParserId, type);
        this.filterRule = filterRule;
    }

    public String getFilterRule() {
        return filterRule;
    }

    public void setFilterRule(String filterRule) {
        this.filterRule = filterRule;
    }
}
