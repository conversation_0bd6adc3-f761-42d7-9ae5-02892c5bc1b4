package us.zoom.cube.site.core;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import us.zoom.infra.dao.model.DataParserGrokProcessorDO;
import us.zoom.infra.dao.service.DataParserGrokProcessorDAO;

import java.util.List;

@Component
public class DataParserGrokProcessorHandler {


    @Autowired
    private DataParserGrokProcessorDAO dataParserGrokProcessorDAO;


    public void addGrokProcessor(DataParserGrokProcessorDO dataParserGrokProcessorDO) {
        Assert.notNull(dataParserGrokProcessorDO,"grok processor is null !");
        dataParserGrokProcessorDAO.add(dataParserGrokProcessorDO);
    }



    public void editGrokProcessor(DataParserGrokProcessorDO dataParserGrokProcessorDO) {
        Assert.notNull(dataParserGrokProcessorDO,"grok processor   is null !");
        dataParserGrokProcessorDAO.edit(dataParserGrokProcessorDO);
    }

    public void delGrokProcessorByDataParserPipelineId(String dataParserPipelineId) {
        Assert.notNull(dataParserPipelineId, "dataParserPipelineId is null !");
        dataParserGrokProcessorDAO.delGrokProcessorByDataParserPipelineId(dataParserPipelineId);
    }

    public void delGrokProcessor(String id) {
        Assert.notNull(id,"id is null !");
        dataParserGrokProcessorDAO.delGrokProcessor(id);
    }

    public DataParserGrokProcessorDO getGrokProcessorById(String grokId) {
        Assert.notNull(grokId,"grokId is null !");
       return   dataParserGrokProcessorDAO.getGrokProcessorById(grokId);
    }

    public List<DataParserGrokProcessorDO> listByPipelineIds(List<String> pipeLineIds) {
        return dataParserGrokProcessorDAO.listByPipelineIds(pipeLineIds);
    }

    public void delGrokProcessorByPipelineIds(List<String> pipeLineIds) {
        if(CollectionUtils.isEmpty(pipeLineIds)){
            return;
        }
        dataParserGrokProcessorDAO.delByPipeIds(pipeLineIds);
    }
}
