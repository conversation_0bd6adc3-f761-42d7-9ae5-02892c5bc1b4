package us.zoom.cube.site.core.parser.process.core.preprocess;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class NestedJsonUnwrapper {

    private static final ObjectMapper mapper = new ObjectMapper();

    /**
     * Deserializes the message field (nested JSON string) in a JSON array string into a nested JSON object.
     *
     * @param rawJson Raw JSON string
     * @return Cleaned JSON string
     * @throws Exception Parsing exception
     */
    public static String unwrapMessageField(String rawJson) throws Exception {
        JsonNode root = mapper.readTree(rawJson);
        if (!root.isArray()) {
            throw new IllegalArgumentException("Input must be a JSON array");
        }

        for (JsonNode node : root) {
            if (node.has("message") && node.get("message").isTextual()) {
                String messageStr = node.get("message").asText();
                try {
                    JsonNode parsed = mapper.readTree(messageStr);
                    // Convert the structure into a string (no line breaks)
                    String compact = mapper.writeValueAsString(parsed);
                    ((ObjectNode) node).put("message", compact);
                } catch (Exception e) {
                    log.error("JSON parsing of the message field failed, retaining the original string: " + e.getMessage());
                }
            }
        }

        return mapper.writerWithDefaultPrettyPrinter().writeValueAsString(root);
    }


}
