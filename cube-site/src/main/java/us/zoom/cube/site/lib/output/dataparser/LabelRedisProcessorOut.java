package us.zoom.cube.site.lib.output.dataparser;

/**
 * @<PERSON> <PERSON>
 * @create 2020/6/2 1:25 AM
 */
public class LabelRedisProcessorOut extends BaseProcessorOut {

    private String labelDataSourceId;

    private Integer redisDataType;

    private String redisKeyGenerator;

    private String keyFields;

    private String nameSpace;

    private String returnField;

    public LabelRedisProcessorOut() {}

    public LabelRedisProcessorOut(String id, String name, Integer order, String dataParserPipelineId, String dataParserId, String type,
                                  String labelDataSourceId, Integer redisDataType, String redisKeyGenerator, String keyFields, String nameSpace, String returnField) {
        super(id, name, order, dataParserPipelineId, dataParserId, type);
        this.labelDataSourceId = labelDataSourceId;
        this.redisDataType = redisDataType;
        this.redisKeyGenerator = redisKeyGenerator;
        this.keyFields = keyFields;
        this.nameSpace = nameSpace;
        this.returnField = returnField;
    }

    public String getLabelDataSourceId() {
        return labelDataSourceId;
    }

    public void setLabelDataSourceId(String labelDataSourceId) {
        this.labelDataSourceId = labelDataSourceId;
    }

    public Integer getRedisDataType() {
        return redisDataType;
    }

    public void setRedisDataType(Integer redisDataType) {
        this.redisDataType = redisDataType;
    }

    public String getRedisKeyGenerator() {
        return redisKeyGenerator;
    }

    public void setRedisKeyGenerator(String redisKeyGenerator) {
        this.redisKeyGenerator = redisKeyGenerator;
    }

    public String getKeyFields() {
        return keyFields;
    }

    public void setKeyFields(String keyFields) {
        this.keyFields = keyFields;
    }

    public String getNameSpace() {
        return nameSpace;
    }

    public void setNameSpace(String nameSpace) {
        this.nameSpace = nameSpace;
    }

    public String getReturnField() {
        return returnField;
    }

    public void setReturnField(String returnField) {
        this.returnField = returnField;
    }
}
