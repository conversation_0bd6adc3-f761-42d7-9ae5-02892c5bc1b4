package us.zoom.cube.site.biz.servicemonitor;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import us.zoom.cube.site.core.MetricsHandler;
import us.zoom.cube.site.core.TenantHandler;
import us.zoom.cube.site.lib.input.migration.MetricsMigrationCheckInput;
import us.zoom.cube.site.lib.output.migration.MetricsTagCheckResult;
import us.zoom.cube.site.lib.output.migration.TagDebugEntry;
import us.zoom.infra.dao.model.MetricsDO;

import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;
import java.util.stream.Collectors;

/**
 * @author: <PERSON><PERSON> Ding
 * @date: 30/07/2025 18:29
 * @desc:
 */
@Slf4j
@Service
public class MetricsTagCompareService {

    public static final long ONE_MINUTE_IN_MS = 60 * 1000;

    @Autowired
    private TenantHandler tenantHandler;
    @Autowired
    private MetricsHandler metricsHandler;
    @Autowired
    private MockClickhouseMetricsService mockClickhouseMetricsService;


    public MetricsTagCheckResult validateTagsValuesCorrection(MetricsMigrationCheckInput input) {
        MetricsTagCheckResult result = new MetricsTagCheckResult();

        try {

            TimeRange timeRange = formatTimeRangeResult(input, result);

            List<String> sourceTagNames = getTagsFromMetric(input.getSourceService(), input.getSourceMetrics());
            List<String> targetTagNames = getTagsFromMetric(input.getTargetService(), input.getTargetMetrics());

            //check if both metrics have the same tags name list, if no, record the difference list
            statisticTagConfigCount(sourceTagNames, targetTagNames, result);

            //Although base on sourceTag,but if targetTag doesn't exist in sourceTag, the sourceTargetTagMap will not contain the sourceTag
            Map<String, String> sourceTargetTagMap = mappingTags(sourceTagNames, targetTagNames);

            //check if the tag value count between source metric and target metric is equal
            checkTagValueDistinctCount(input, sourceTargetTagMap, timeRange, result);

            //compare every tag value between source metric and target metric
            compareTagValue(input, sourceTargetTagMap, timeRange, result);

            result.finalCheck();
        } catch (Exception e) {
            log.error("MetricsTagCompareService validateTagsValuesCorrection error:" + e.getMessage(), e);
            result.setErrorException(e.getMessage());
        }
        return result;
    }

    //We avoid checking for total quantity equality, as the lists could differ in content but still have the same count.
    private void statisticTagConfigCount(List<String> sourceTagNames, List<String> targetTagNames, MetricsTagCheckResult result) {
        result.setTargetMissTags(diffListContentBySmallCase(sourceTagNames, targetTagNames));
        result.setSourceMissTags(diffListContentBySmallCase(targetTagNames, sourceTagNames));
    }

    private Map<String, String> mappingTags(List<String> sourceTagNames, List<String> targetTagNames) {
        Map<String, String> sourceTagSmallCaseMap = sourceTagNames.stream().collect(Collectors.toMap(e -> e, e -> convertToSmallCase(e)));
        Map<String, String> smallCaseTargetTagMap = targetTagNames.stream().collect(Collectors.toMap(e -> convertToSmallCase(e), e -> e));

        //Map<sourceTag, targetTag>, skip if smallCaseTargetTagMap don't have the smallCaseSourceTag
        Map<String, String> sourceTargetTagMap = sourceTagSmallCaseMap.entrySet().stream()
                .filter(e -> smallCaseTargetTagMap.containsKey(e.getValue()))
                .collect(Collectors.toMap(Map.Entry::getKey, e -> smallCaseTargetTagMap.get(e.getValue())));
        return sourceTargetTagMap;
    }


    private List<String> getTagsFromMetric(String serviceName, String metricName) {
        String serviceId = tenantHandler.getTenantByName(serviceName).getId();
        MetricsDO metricsDO = metricsHandler.queryMetricsByName(metricName, serviceId);
        Assert.notNull(metricsDO, String.format("Can't find metrics[%s] in service[%s], please check if the metrics is existed.", metricName, serviceName));
        List<String> sourceTagNames = Arrays.stream(metricsDO.getTagNames().split(",")).toList();
        return sourceTagNames;
    }


    private void compareTagValue(MetricsMigrationCheckInput input, Map<String, String> sourceTargetTagMap, TimeRange timeRange, MetricsTagCheckResult result) {
        for (Map.Entry<String, String> sourceTargetTag : sourceTargetTagMap.entrySet()) {
            String sourceTag = sourceTargetTag.getKey();
            String targetTag = sourceTargetTag.getValue(); //here targetTag is already guaranteed to map to sourceTag

            List<String> sourceTagValues = mockClickhouseMetricsService.getDistinctValueOfTag(input.getSourceService(), input.getSourceMetrics(), sourceTag,
                    timeRange.getStartTime() / 1000, timeRange.getEndTime() / 1000, input.isDebugMode());

            List<String> targetTagValues = mockClickhouseMetricsService.getDistinctValueOfTag(input.getTargetService(), input.getTargetMetrics(), targetTag,
                    timeRange.getStartTime() / 1000, timeRange.getEndTime() / 1000, input.isDebugMode());

            if (input.isDebugMode()) {
                result.getDebugStatistic().add(new TagDebugEntry(sourceTag, sourceTagValues, targetTag, targetTagValues));
            }

            if (sourceTagValues.size() == 0 || (sourceTagValues.size() == 1 && StringUtils.isBlank(sourceTagValues.get(0)))) {
                result.getEmptyTagValueWarning().add(sourceTag);
            }

            int diffCount = 0;
            //check if every source value is existed in the target value
            for (String sourceTagValue : sourceTagValues) {
                if (!targetTagValues.contains(sourceTagValue)) {
                    if (diffCount == 0) {
                        result.getNotEqualValuesExample().put(sourceTag, sourceTagValue);
                    }
                    diffCount++;
                }
            }
            if (diffCount > 0) {
                result.getNotEqualValuesCount().put(sourceTag, diffCount);
            }
        }
    }


    private void checkTagValueDistinctCount(MetricsMigrationCheckInput input, Map<String, String> sourceTargetTagMap, TimeRange timeRange, MetricsTagCheckResult result) {
        Map<String, Long> sourceTagsUniqMap = mockClickhouseMetricsService.getDistinctCountOfTag(input.getSourceService(), input.getSourceMetrics(),
                sourceTargetTagMap.keySet().stream().toList(), timeRange.getStartTime() / 1000, timeRange.getEndTime() / 1000, input.isDebugMode());
        Map<String, Long> targetTagsUniqMap = mockClickhouseMetricsService.getDistinctCountOfTag(input.getTargetService(), input.getTargetMetrics(),
                sourceTargetTagMap.values().stream().toList(), timeRange.getStartTime() / 1000, timeRange.getEndTime() / 1000, input.isDebugMode());

        //check if target tag uniq is not equal to source tag uniq, if no, return the difference list
        for (Map.Entry<String, Long> sourceTagsUniq : sourceTagsUniqMap.entrySet()) {
            String sourceTag = sourceTagsUniq.getKey();
            long souceTagCount = sourceTagsUniq.getValue();
            if (0 == souceTagCount) {
                result.getZeroCountTagWarning().add(sourceTag);
            }

            String targetTag = sourceTargetTagMap.get(sourceTag);
            long targetTagCount = targetTagsUniqMap.get(targetTag);
            log.info("MetricsTagCompareService checkTagValueDistinctCount, sourceTag={}, targetTag={}, souceTagCount={}, targetTagCount={}", sourceTag, targetTag, souceTagCount, targetTagCount);

            if (souceTagCount != targetTagCount) {
                result.getNotEqualCountTags().add(Map.of(sourceTag, souceTagCount, targetTag, targetTagCount));
            }
        }
    }

    // Get the elements from aList that not exist in the bList, by converting both lists to lowercase first, then compare them
    private List<String> diffListContentBySmallCase(List<String> aList, List<String> bList) {
        //convert bList to smallcase bList
        List<String> smallCaseBList = bList.stream().map(e -> convertToSmallCase(e)).toList();
        return aList.stream().filter(e -> !smallCaseBList.contains(convertToSmallCase(e))).toList();
    }

    private TimeRange getValidTimeRange(Integer lastMinute, Long startTime, Long endTime) {
        if (null == lastMinute) {
            return new TimeRange(startTime, endTime);
        } else {
            long now = System.currentTimeMillis() / ONE_MINUTE_IN_MS * ONE_MINUTE_IN_MS;
            //query from lastMinute to latest 3 minutes
            return new TimeRange(now - lastMinute * ONE_MINUTE_IN_MS, now - ONE_MINUTE_IN_MS * 3);
        }
    }

    public TimeRange formatTimeRangeResult(MetricsMigrationCheckInput input, MetricsTagCheckResult result) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
        //comfirm the query time range
        TimeRange timeRange = getValidTimeRange(input.getLastMinute(), input.getStartTime(), input.getEndTime());
        String startTimeStr = sdf.format(timeRange.getStartTime());
        String endTimeStr = sdf.format(timeRange.getEndTime());
        log.info("MetricsTagCompareService compareTagsValues time range is:{}~{}", startTimeStr, endTimeStr);
        result.setStartTime(startTimeStr);
        result.setEndTime(endTimeStr);
        return timeRange;
    }

    private String convertToSmallCase(String value) {
        return value.replaceAll("[^a-zA-Z0-9]", "").toLowerCase();
    }


    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    static class TimeRange {
        long startTime;
        long endTime;
    }


}
