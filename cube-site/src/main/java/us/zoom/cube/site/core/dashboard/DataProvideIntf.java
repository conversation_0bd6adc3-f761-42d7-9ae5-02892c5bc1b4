package us.zoom.cube.site.core.dashboard;

import us.zoom.cube.site.lib.query.DashboardViewQuery;
import us.zoom.infra.dao.model.ViewComponentDO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface DataProvideIntf {
    /**
     *
     * @param dashboardViewQuery
     * @param viewComponentDO
     * @return
     */
      List<Map<String,Object>> getData(DashboardViewQuery dashboardViewQuery, ViewComponentDO viewComponentDO);
}
