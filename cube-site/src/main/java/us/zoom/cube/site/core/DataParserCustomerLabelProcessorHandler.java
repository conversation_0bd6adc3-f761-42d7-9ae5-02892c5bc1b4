package us.zoom.cube.site.core;

import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import us.zoom.infra.dao.model.DataParserCustomerLabelProcessorDO;
import us.zoom.infra.dao.service.DataParserCustomerLabelProcessorDAO;

import java.util.List;

@Component
public class DataParserCustomerLabelProcessorHandler {


    @Autowired
    private DataParserCustomerLabelProcessorDAO customerLabelProcessorDAO;


    public void addProcessor(DataParserCustomerLabelProcessorDO dataParserCustomerLabelProcessorDO) {
        Assert.notNull(dataParserCustomerLabelProcessorDO,"dataParserCustomerLabelProcessorDO is null !");
        customerLabelProcessorDAO.add(dataParserCustomerLabelProcessorDO);
    }

    public DataParserCustomerLabelProcessorDO getProcessorById(String id) {
        Assert.notNull(id, "id is null!");
        return customerLabelProcessorDAO.getProcessorById(id);
    }

    public void editProcessor(DataParserCustomerLabelProcessorDO dataParserCustomerLabelProcessorDO) {
        Assert.notNull(dataParserCustomerLabelProcessorDO,"dataParserCustomerLabelProcessorDO is null !");
        customerLabelProcessorDAO.editProcessor(dataParserCustomerLabelProcessorDO);
    }

    public void delProcessor(String id) {
        Assert.notNull(id, "id is null!");
        customerLabelProcessorDAO.delProcessor(id);
    }

    public List<DataParserCustomerLabelProcessorDO> listByPipelineIds(List<String> pipeLineIds) {
        List<DataParserCustomerLabelProcessorDO> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(pipeLineIds)) {
            return result;
        }
        return customerLabelProcessorDAO.listByPipelineIds(pipeLineIds);
    }

    public void delByPipelineIds(List<String> pipeLineIds) {
        if(CollectionUtils.isEmpty(pipeLineIds)){
            return;
        }
        customerLabelProcessorDAO.delByPipeIds(pipeLineIds);
    }
}
