package us.zoom.cube.site.core;

import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import us.zoom.infra.dao.model.LabelDataSourceMysqlDO;
import us.zoom.infra.dao.service.LabelDataSourceMysqlDAO;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2020/5/27 6:32 AM
 */
@Component
public class LabelDataSourceMysqlHandler {

    @Autowired
    private LabelDataSourceMysqlDAO labelDataSourceMysqlDAO;

    public void AddLabelDataSourceMysql(LabelDataSourceMysqlDO labelDataSourceMysqlDO) {
        labelDataSourceMysqlDAO.addLabelDataSourceMysql(labelDataSourceMysqlDO);
    }

    public LabelDataSourceMysqlDO findByLabelDataSourceId(String labelDataSourceId) {
        return labelDataSourceMysqlDAO.findByLabelDataSourceId(labelDataSourceId);
    }

    public void editLabelDataSourceMysql(LabelDataSourceMysqlDO labelDataSourceMysqlDO) {
        labelDataSourceMysqlDAO.editLabelDataSourceMysql(labelDataSourceMysqlDO);
    }

    public Map<String, LabelDataSourceMysqlDO> listAll() {
        Map<String, LabelDataSourceMysqlDO> resultMap = Maps.newHashMap();
        List<LabelDataSourceMysqlDO> list = labelDataSourceMysqlDAO.listAll();
        if (CollectionUtils.isEmpty(list)) {
            return resultMap;
        }
        resultMap = list.stream().collect(Collectors.toMap(LabelDataSourceMysqlDO::getLabelDataSourceId, e -> e));
        return resultMap;
    }
}
