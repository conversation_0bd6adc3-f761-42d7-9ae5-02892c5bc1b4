package us.zoom.cube.site.core;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import us.zoom.infra.dao.model.CalculateTopicPartitionDO;
import us.zoom.infra.dao.model.ResourcePartitionDO;
import us.zoom.infra.dao.service.CalculateTopicPartitionDAO;
import us.zoom.infra.dao.service.ResourcePartitionDAO;

import java.util.List;

@Component
public class UnitHandler {

    @Autowired
    private ResourcePartitionDAO resourcePartitionDAO;

    @Autowired
    private CalculateTopicPartitionDAO calculateTopicPartitionDAO;

    public List<ResourcePartitionDO> findResourceByNameLike(String name, int pageIndex, int pageSize) {
        return  resourcePartitionDAO.findResourceByNameLike(name,pageSize*(pageIndex-1), pageSize);
    }

    public int getResourceCountByNameLike(String name) {
        return  resourcePartitionDAO.getResourceCountByNameLike(name);

    }

    public void addResourcePartion(ResourcePartitionDO resourcePartitionDO) {
        if (null == resourcePartitionDO.getStragety()) {
            resourcePartitionDO.setStragety("");
        }
        resourcePartitionDAO.insert(resourcePartitionDO);
    }

    public List<CalculateTopicPartitionDO> findCalTopicByNameLike(String name, int pageIndex, int pageSize) {
       return calculateTopicPartitionDAO.findCalTopicByNameLike(name,pageSize*(pageIndex-1), pageSize);
    }

    public int getCalTopicCountByNameLike(String name) {
        return calculateTopicPartitionDAO.getCalTopicCountByNameLike(name);
    }

    public CalculateTopicPartitionDO getCalTopicByName(String name){
        return calculateTopicPartitionDAO.getByName(name);
    }

    public void addCalTopic(CalculateTopicPartitionDO calTopic) {
        calculateTopicPartitionDAO.insert(calTopic);
    }

    public void editCalTopic(CalculateTopicPartitionDO calTopic) {
        calculateTopicPartitionDAO.editCalTopic(calTopic);

    }

    public CalculateTopicPartitionDO getCalTopicById(String id) {
        return    calculateTopicPartitionDAO.getCalTopicById(id);
    }
}
