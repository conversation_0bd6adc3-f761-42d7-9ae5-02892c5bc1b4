package us.zoom.cube.site.lib.output.dataflow;

/**
 * @Author: caesar.jiang
 * @Date: 2023/1/3 10:45
 * @Desc: SimpleDataFlowOut
 */
public class SimpleDataFlowOut {
    private String id;
    private String name;

    public SimpleDataFlowOut() {
    }

    public SimpleDataFlowOut(String id, String name) {
        this.id = id;
        this.name = name;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
