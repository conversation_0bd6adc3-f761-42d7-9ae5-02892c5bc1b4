package us.zoom.infra.dao.service;

import com.fasterxml.jackson.core.type.TypeReference;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import us.zoom.cube.site.infra.AsyncMQInstance;
import us.zoom.mq.client.clients.consumer.RetryableStraw;
import us.zoom.mq.common.entity.TaskEntity;

import java.util.List;

public class AsyncMQConsumerTest {
    private final static TypeReference typeReference = new TypeReference<String>() {
    };

    @BeforeEach
    public void before() {
        AsyncMQInstance.init("", "", "");
    }

    @Test
    public void consumer() throws InterruptedException {
        String topic = "devops_watch_cmdb_server_v1";
        String groupId = "cube_group";
        AsyncMQInstance.getInstance().registerConsumer(topic, groupId, 1, new RetryableStraw<String>() {
            @Override
            public boolean onMessage(List<TaskEntity<String>> list) {
                System.out.println(list.get(0).getPayload());
                return false;
            }

            @Override
            public TypeReference<String> type() {
                return typeReference;
            }
        });
        Thread.sleep(Integer.MAX_VALUE);
    }

}
