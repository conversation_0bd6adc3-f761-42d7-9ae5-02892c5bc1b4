package us.zoom.cube.site.lib.output.config.metrics;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class MetricsTagsAndFieldsOutput {

    public static final String TAG = "tag";
    public static final String FIELD = "field";

    String id;

    String name;

    String metaType;

    String fieldType;

    String description;

    public static MetricsTagsAndFieldsOutput newField() {
        MetricsTagsAndFieldsOutput field = new MetricsTagsAndFieldsOutput();
        field.setMetaType(FIELD);
        return field;
    }

    public static MetricsTagsAndFieldsOutput newTag() {
        MetricsTagsAndFieldsOutput tag = new MetricsTagsAndFieldsOutput();
        tag.setMetaType(TAG);
        return tag;
    }
}
