package us.zoom.cube.site.biz;

import org.springframework.util.Assert;
import us.zoom.cube.site.lib.input.usergroup.UserGroupInput;

public class UserGroupInputTest {



    public static void main(String []gs){
        checkPara();
    }
    public static void checkPara(){
        UserGroupInput userGroupInput  = mock("test","service_owner");
        userGroupInput.checkWhenAdd();


        userGroupInput.setName(null);
        String result = checkWhenAdd(userGroupInput);
        Assert.isTrue("Name can't be blank.".equals(result),"fail");

        userGroupInput.setName("dfsd sdfs");
        result = checkWhenAdd(userGroupInput);
        Assert.isTrue("Illegal name".equals(result),"fail");

        userGroupInput.setName(mockStr(65));
        result = checkWhenAdd(userGroupInput);
        Assert.isTrue("The max length is 64".equals(result),"fail");

        userGroupInput.setName("test");
        userGroupInput.setRole(null);
        result = checkWhenAdd(userGroupInput);
        Assert.isTrue("Role can't be blank.".equals(result),"fail");

        userGroupInput.setRole("dfsd sdfs");
        result = checkWhenAdd(userGroupInput);
        Assert.isTrue("Illegal role name".equals(result),"fail");

        userGroupInput.setRole(mockStr(65));
        result = checkWhenAdd(userGroupInput);
        Assert.isTrue("The max length is 64".equals(result),"fail");
    }

    private static String mockStr(int length) {
        StringBuilder builder = new StringBuilder();
        for(int i=0;i<length;i++){
            builder.append("a");
        }
        return  builder.toString();
    }



    public  static  String checkWhenAdd(UserGroupInput userGroupInput ){
        String result = null;
        try{
            userGroupInput.checkWhenAdd();
        }catch (Exception e){
            result = e.getMessage();
            System.out.println(result);
        }
        return result;
    }


    private static UserGroupInput mock(String name, String role) {
        UserGroupInput userGroupInput = new UserGroupInput();
        userGroupInput.setName(name);
        userGroupInput.setRole(role);

        return userGroupInput;
    }

}
