package us.zoom.cube.site.core;

import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import us.zoom.cube.site.biz.HubCfgCacheService;
import us.zoom.cube.site.lib.input.DiscoverItem;
import us.zoom.cube.site.lib.input.OutDiscoverPipelineInput;
import us.zoom.cube.site.lib.input.dataparser.DiscoverPipelineBase;
import us.zoom.cube.site.lib.input.dataparser.DiscoverPipelineGet;
import us.zoom.cube.site.lib.input.dataparser.DiscoverPipelineQuery;
import us.zoom.infra.dao.model.AutoDiscoverPipelineDO;
import us.zoom.infra.dao.model.DataParserDO;
import us.zoom.infra.dao.model.DataParserSourceDO;
import us.zoom.infra.dao.service.AutoDiscoverPipelineDAO;
import us.zoom.infra.dao.service.DataParserDAO;
import us.zoom.infra.dao.service.DataParserSourceDAO;
import us.zoom.infra.enums.AutoDiscoverPipelineStatusEnum;
import us.zoom.infra.thread.NamedThreadFactory;
import us.zoom.infra.utils.Instance;
import us.zoom.cube.lib.utils.JsonUtils;

import jakarta.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Component
@Slf4j
public class AutoDiscoverPipeLineHandler {
    @Autowired
    private DataParserDAO dataParserDAO;
    @Autowired
    private DataParserSourceDAO dataParserSourceDAO;
    @Autowired
    private AutoDiscoverPipelineDAO autoDiscoverPipelineDAO;
    @Autowired
    private HubCfgCacheService hubCfgCacheService;

    // private Map<String, AutoDiscoverPipelineDO> discoverPipelineMap = new HashMap<>();

    private Map<String,Map<String, DiscoverPipelineBase>> deletedDiscoverPipelineMap = new HashMap<>();

    @PostConstruct
    public void init() {
        Executors.newScheduledThreadPool(1, new NamedThreadFactory(" discoverPipeline Loader")).scheduleAtFixedRate(new Runnable() {
            @Override
            public void run() {
                cacheLoader();
            }
        }, 5, 30, TimeUnit.SECONDS);
    }

    private void cacheLoader() {
        List<AutoDiscoverPipelineDO> deletedDiscoverPipelineDOList = autoDiscoverPipelineDAO.selectPipelineByDataParserIdAndStatus(null, AutoDiscoverPipelineStatusEnum.deleted.getCode());
        Map<String,Map<String, DiscoverPipelineBase>> deletedDiscoverPipelineMapTem = new HashMap<>();
        for (AutoDiscoverPipelineDO autoDiscoverPipelineDO : deletedDiscoverPipelineDOList){
            Map<String, DiscoverPipelineBase> autoDiscoverPipelineMap = deletedDiscoverPipelineMapTem.computeIfAbsent(autoDiscoverPipelineDO.getDataParserId(),f->new HashMap<>());
            DiscoverPipelineBase base = autoDiscoverPipelineMap.computeIfAbsent(autoDiscoverPipelineDO.getPipelineName(),f->new DiscoverPipelineBase());
            List<DiscoverItem> discoverItems = StringUtils.isNotBlank(autoDiscoverPipelineDO.getTags())?JsonUtils.toObjectByTypeRef(autoDiscoverPipelineDO.getTags(), new TypeReference<List<DiscoverItem>>() {}):new ArrayList<>();
            if (StringUtils.isNotBlank(autoDiscoverPipelineDO.getFields())){
                discoverItems.addAll(JsonUtils.toObjectByTypeRef(autoDiscoverPipelineDO.getFields(), new TypeReference<List<DiscoverItem>>() {}));
            }
            if (StringUtils.isNotBlank(autoDiscoverPipelineDO.getHisFields())){
                discoverItems.addAll(JsonUtils.toObjectByTypeRef(autoDiscoverPipelineDO.getHisFields(), new TypeReference<List<DiscoverItem>>() {}));
            }
            if (StringUtils.isNotBlank(autoDiscoverPipelineDO.getSumFields())){
                discoverItems.addAll(JsonUtils.toObjectByTypeRef(autoDiscoverPipelineDO.getSumFields(), new TypeReference<List<DiscoverItem>>() {}));
            }
            Set<String> piiFields = StringUtils.isNotBlank(autoDiscoverPipelineDO.getPiiFields())?JsonUtils.toObjectByTypeRef(autoDiscoverPipelineDO.getPiiFields(), new TypeReference<HashSet<String>>() {}):new HashSet<>();
            base.getDiscoverItems().addAll(discoverItems);
            base.getPiiFields().addAll(piiFields);
        }
        deletedDiscoverPipelineMap = deletedDiscoverPipelineMapTem;
    }


    public List<AutoDiscoverPipelineDO> selectPipelineByDataParserIdAndStatus(String dataParserId, int status) {
        return autoDiscoverPipelineDAO.selectPipelineByDataParserIdAndStatus(dataParserId, status);
    }

    public int deleteById(String id,String uniqueId) {
        return autoDiscoverPipelineDAO.deleteById(id,uniqueId);
    }

    public int deleteByDataParserId(String dataParserId) {
        return autoDiscoverPipelineDAO.deleteByDataParserId(dataParserId);
    }

    public int updateStatusById(String id, Integer status,String uniqueId) {
        return autoDiscoverPipelineDAO.updateStatusById(id, status, uniqueId);
    }

    public int batchAdd(List<AutoDiscoverPipelineDO> autoDiscoverPipelineDOListToAdd) {
        return autoDiscoverPipelineDAO.batchAdd(autoDiscoverPipelineDOListToAdd);
    }

    public int batchUpdate(List<AutoDiscoverPipelineDO> autoDiscoverPipelineDOListToUpdate) {
        return autoDiscoverPipelineDAO.batchUpdate(autoDiscoverPipelineDOListToUpdate);
    }

/*    public AutoDiscoverPipelineDO existInAutoDiscoverPipeline(String key) {
        return discoverPipelineMap.get(key);
    }*/

    public boolean checkCacheIsReady() {
        if (hubCfgCacheService.getCache() == null
                || hubCfgCacheService.getCache().getIdToDataParserMap() == null
                || hubCfgCacheService.getCache().getIdToDataParserSourceMap() == null) {
            return false;
        }
        return true;
    }

    public void setTypeByTenantIdAndDataParserId(OutDiscoverPipelineInput outDiscoverPipelineInput) {
        if (hubCfgCacheService.getCache().getIdToDataParserMap().get(outDiscoverPipelineInput.getDataParserId()) != null && hubCfgCacheService.getCache().getIdToDataParserMap().get(outDiscoverPipelineInput.getDataParserId()).getTenantId().equals(outDiscoverPipelineInput.getTenantId())) {
            // data parser
            outDiscoverPipelineInput.setType(0);
        } else if (hubCfgCacheService.getCache().getIdToDataParserSourceMap().get(outDiscoverPipelineInput.getDataParserId()) != null && hubCfgCacheService.getCache().getIdToDataParserSourceMap().get(outDiscoverPipelineInput.getDataParserId()).getTenantId().equals(outDiscoverPipelineInput.getTenantId())) {
            // new data parser
            outDiscoverPipelineInput.setType(1);
        } else {
            Assert.isTrue(false, "dont have this data parser in db");
        }
    }

    public void correctAutoDiscoverPipelineDeletedFields(OutDiscoverPipelineInput outDiscoverPipelineInput) {
        if (deletedDiscoverPipelineMap.get(outDiscoverPipelineInput.getDataParserId())!=null){
            DiscoverPipelineBase discoverPipelineBaseDeleted = deletedDiscoverPipelineMap.get(outDiscoverPipelineInput.getDataParserId()).get(outDiscoverPipelineInput.getPipelineName());
            if (discoverPipelineBaseDeleted!=null){
                outDiscoverPipelineInput.setTags(outDiscoverPipelineInput.getTags().stream().filter(e->!discoverPipelineBaseDeleted.getDiscoverItems().contains(e)).collect(Collectors.toList()));
                outDiscoverPipelineInput.setFields(outDiscoverPipelineInput.getFields().stream().filter(e->!discoverPipelineBaseDeleted.getDiscoverItems().contains(e)).collect(Collectors.toList()));
                outDiscoverPipelineInput.setHisFields(outDiscoverPipelineInput.getHisFields().stream().filter(e->!discoverPipelineBaseDeleted.getDiscoverItems().contains(e)).collect(Collectors.toList()));
                outDiscoverPipelineInput.setSumFields(outDiscoverPipelineInput.getSumFields().stream().filter(e->!discoverPipelineBaseDeleted.getDiscoverItems().contains(e)).collect(Collectors.toList()));
                outDiscoverPipelineInput.setPiiFields(outDiscoverPipelineInput.getPiiFields().stream().filter(e->!discoverPipelineBaseDeleted.getPiiFields().contains(e)).collect(Collectors.toList()));
            }
        }
    }

    public List<AutoDiscoverPipelineDO> selectPipelineByParam(List<String> dataParserIds, String tenantId,String pipelineName, Integer status, int pageIndex, int pageSize) {
        return autoDiscoverPipelineDAO.selectPipelineByParam(dataParserIds,tenantId,pipelineName,status,(pageIndex-1)*pageSize,pageSize);
    }

    public int getPipelineCountByParam(List<String> dataParserIds, String tenantId, String pipelineName, Integer status) {
        return autoDiscoverPipelineDAO.getPipelineCountByParam(dataParserIds,tenantId,pipelineName,status);
    }

    public List<String> findByTenantIdAndNameFromGlobal(String tenantId, String dataParserName) {
        List<String> dataParserIds = new ArrayList<>();
        List<DataParserDO> dataParserDOList = dataParserDAO.findByTenantIdAndName(tenantId,dataParserName);
        List<DataParserSourceDO> dataParserSourceDOList = dataParserSourceDAO.findByTenantIdAndName(tenantId,dataParserName);
        dataParserIds.addAll(Instance.ofNullable(dataParserDOList).stream().map(item->item.getId()).collect(Collectors.toList()));
        dataParserIds.addAll(Instance.ofNullable(dataParserSourceDOList).stream().map(item->item.getId()).collect(Collectors.toList()));
        return dataParserIds;
    }

    public List<DataParserDO> getByIds(Set<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return dataParserDAO.getByIds(ids);
    }

    public AutoDiscoverPipelineDO getAutoDiscoverPipeline(DiscoverPipelineGet get) {
        return autoDiscoverPipelineDAO.getAutoDiscoverPipeline(get.getDataParserId(), get.getTenantId(),
                get.getPipelineName(), get.getFilterRule(), get.getStatus());
    }

    public List<DataParserSourceDO> getByIdsFromNew(List<String> dataParserIds) {
        if (CollectionUtils.isEmpty(dataParserIds)) {
            return Collections.emptyList();
        }
        return dataParserSourceDAO.listBy(dataParserIds);
    }

    public AutoDiscoverPipelineDO selectById(String id) {
        Assert.isTrue(StringUtils.isNotBlank(id),"auto discover pipeline id can not be blank");
        return autoDiscoverPipelineDAO.selectById(id);
    }

    public int updateStatusAndExistAndUniqueIdById(AutoDiscoverPipelineDO autoDiscoverPipelineDO) {
        return autoDiscoverPipelineDAO.updateStatusAndExistAndUniqueIdById(autoDiscoverPipelineDO);
    }

    public List<AutoDiscoverPipelineDO> listPipelineUnhandled(List<String> dataParserIds) {
        if (CollectionUtils.isEmpty(dataParserIds)) {
            return Collections.emptyList();
        }
        return autoDiscoverPipelineDAO.listPipelineUnhandled(dataParserIds);
    }

    public List<AutoDiscoverPipelineDO> selectDiscoverPipelineByNameAndParserId(String dataParserId, String pipelineName, int pageIndex, int pageSize) {
        return autoDiscoverPipelineDAO.selectDiscoverPipelineByNameAndParserId(dataParserId,pipelineName,(pageIndex-1)*pageSize,pageSize);
    }

    public int selectDiscoverPipelineCountByNameAndParserId(String dataParserId, String pipelineName) {
        return autoDiscoverPipelineDAO.selectDiscoverPipelineCountByNameAndParserId(dataParserId,pipelineName);
    }

    public List<AutoDiscoverPipelineDO> queryByPipelineNameList(List<String> pipelineNameList, String tenantId, String dataParserId, Integer status) {
        return autoDiscoverPipelineDAO.queryByPipelineNameList(pipelineNameList, tenantId, dataParserId, status);
    }
}
