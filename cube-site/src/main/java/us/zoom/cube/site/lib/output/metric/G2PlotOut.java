package us.zoom.cube.site.lib.output.metric;

import java.util.List;

public class G2PlotOut {
    private List<String> groupFields;
    private List<ItemOut> items;

    public List<String> getGroupFields() {
        return groupFields;
    }

    public void setGroupFields(List<String> groupFields) {
        this.groupFields = groupFields;
    }

    public List<ItemOut> getItems() {
        return items;
    }

    public void setItems(List<ItemOut> items) {
        this.items = items;
    }
}
