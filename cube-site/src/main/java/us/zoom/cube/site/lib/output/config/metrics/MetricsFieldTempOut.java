package us.zoom.cube.site.lib.output.config.metrics;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

/**
 * <AUTHOR>
 * @create 2020/7/19 10:03 AM
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class MetricsFieldTempOut {

    String id;

    String metricsId;

    String fieldName;

    String oldFieldName;

    int fieldType;

    String description;
//
//    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    Date createTime = new Date();
//
//    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    Date modifyTime = new Date();
}
