package us.zoom.cube.site.lib.output.panoramic;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
public class BusinessDetailResult<T> implements Serializable {
    @Serial
    private static final long serialVersionUID = 5635896375536423390L;
    private String title;
    @JsonIgnore
    private String columns;
    @JsonIgnore
    private String type;
    @JsonIgnore
    private String link;
    @JsonIgnore
    private String parentName;
    private T value;
}
