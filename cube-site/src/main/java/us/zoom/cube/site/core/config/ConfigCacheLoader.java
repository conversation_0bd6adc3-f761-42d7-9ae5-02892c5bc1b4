package us.zoom.cube.site.core.config;

import com.google.common.collect.Lists;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import us.zoom.cube.site.core.SubEnvironmentHandler;
import us.zoom.cube.site.core.TenantHandler;
import us.zoom.infra.thread.CacheLoaderScheduler;
import us.zoom.infra.thread.NamedThreadFactory;
import us.zoom.infra.utils.AsyncUtils;

import java.util.List;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2021/8/24 1:46 PM
 */

@Component
@Slf4j
public class ConfigCacheLoader {

    private static final int LOAD_INITIAL_DELAY_IN_SECONDS = 120;

    private static final int LOAD_INTERVAL_IN_SECONDS = 120;

    @Value("${cache-loader.startup.common-config.enable:true}")
    private Boolean loadCacheStartupEnable;

    @Autowired
    private List<CacheLoader> cacheLoaders;

    private ExecutorService executorService;


    @PostConstruct
    public void startLoad() {
        int loadInitialDelay = 0;
        executorService = new ThreadPoolExecutor(cacheLoaders.size(), cacheLoaders.size(), 2, TimeUnit.MINUTES, new ArrayBlockingQueue<>(500),
                new NamedThreadFactory("siteCacheLoader"), new ThreadPoolExecutor.CallerRunsPolicy());
        if (loadCacheStartupEnable) {
            loadInitialDelay = LOAD_INITIAL_DELAY_IN_SECONDS;
            loadCfg2Cache();
        }
        CacheLoaderScheduler.getInstance().getScheduler().scheduleAtFixedRate(this::loadCfg2Cache, loadInitialDelay, LOAD_INTERVAL_IN_SECONDS, TimeUnit.SECONDS);
    }

    private void loadCfg2Cache() {
        long begin = System.currentTimeMillis();
        log.info("begin loadCfg2Cache!! ");
        AsyncUtils.parallelFutureJoinWithoutResult(cacheLoaders, CacheLoader::load, (e, task) -> log.error("loadCfg2Cache error, task class: {} ", task.getClass().getName(), e), executorService);
        log.info("end loadCfg2Cache,cost= " + (System.currentTimeMillis() - begin));
    }
}
