package us.zoom.cube.site.core;

import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.lib.common.CubeConstants;
import us.zoom.infra.dao.model.DataFlowDataParserRelationDO;
import us.zoom.infra.dao.service.DataFlowDataParserRelationDAO;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

@Component
public class DataFlowDataParserRelationHandler {

    @Autowired
    private DataFlowDataParserRelationDAO dataFlowDataParserRelationDAO;

    public List<DataFlowDataParserRelationDO> listAll() {
        List<DataFlowDataParserRelationDO> relationDOList = new ArrayList<>();
        long counts = dataFlowDataParserRelationDAO.queryCounts();
        int pageSize = CubeConstants.CUBE_SQL_BATCH_QUERY_SIZE;
        long pageNum = counts / pageSize + (counts % pageSize == 0 ? 0 : 1);
        for (int i = 1; i <= pageNum; i++) {
            relationDOList.addAll(dataFlowDataParserRelationDAO.listBatch(pageSize * (i - 1), pageSize));
        }
        return relationDOList;
    }

    public void batchAdd(List<DataFlowDataParserRelationDO> relations) {
        if (CollectionUtils.isEmpty(relations)) {
            return;
        }
        dataFlowDataParserRelationDAO.batchAdd(relations);
    }


    public void batchDelete(List<DataFlowDataParserRelationDO> relations) {
        if (CollectionUtils.isEmpty(relations)) {
            return;
        }
        dataFlowDataParserRelationDAO.batchDelete(relations);
    }

    public void deleteByDataParserId(String dataParserId) {
        dataFlowDataParserRelationDAO.deleteByDataParserId(dataParserId);
    }

    public List<DataFlowDataParserRelationDO> selectByUniqueId(List<DataFlowDataParserRelationDO> relations) {
        if (CollectionUtils.isEmpty(relations)) {
            return Lists.newArrayList();
        }
        return dataFlowDataParserRelationDAO.selectByUniqueId(relations);
    }


    public List<DataFlowDataParserRelationDO> findByList(DataFlowDataParserRelationDO relation) {
        return dataFlowDataParserRelationDAO.findByList(relation);
    }

    public List<DataFlowDataParserRelationDO> listByTenantId(String tenantId) {
        return dataFlowDataParserRelationDAO.listByTenantId(tenantId);
    }
    public List<DataFlowDataParserRelationDO> findByParam(String dataFlowId, String dataFlowName,
                                                     String dataParserId, String dataParserName,
                                                     String tenantId, String tenantName,
                                                     int pageIndex, int pageSize) {
        return dataFlowDataParserRelationDAO.findByParam(
                dataFlowId, dataFlowName,
                dataParserId, dataParserName,
                tenantId, tenantName, (pageIndex - 1) * pageSize, pageSize);
    }

    public int getCountByParam(String dataFlowId, String dataFlowName,
                      String dataParserId, String dataParserName,
                      String tenantId, String tenantName) {
        return dataFlowDataParserRelationDAO.getCountByParam(
                dataFlowId, dataFlowName,
                dataParserId, dataParserName,
                tenantId, tenantName);
    }

    public List<DataFlowDataParserRelationDO> getByIds(Set<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }
        return dataFlowDataParserRelationDAO.getByIds(ids);
    }


    public void update(DataFlowDataParserRelationDO relation){
        dataFlowDataParserRelationDAO.updateRelationDO(relation);
    }

}
