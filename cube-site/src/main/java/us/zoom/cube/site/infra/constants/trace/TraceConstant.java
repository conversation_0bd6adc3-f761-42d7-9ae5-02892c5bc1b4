package us.zoom.cube.site.infra.constants.trace;

/**
 * @author: eason.jia
 * @date: 2024/8/17
 */
public class TraceConstant {

    // trace database name
    public static final String TRACE_DATABASE_NAME = "cube_trace";

    public static final String TRACE_SYSTEM_PARA_TYPE = "trace";

    // trace table name
    public static final String TRACE_INDEX_TABLE = "distributed_trace_index";
    public static final String TRACE_ERROR_TABLE = "distributed_trace_error_index";
    public static final String TRACE_SPANS_TABLE = "distributed_trace_spans";
    public static final String TRACE_SPAN_ATTRIBUTE_TABLE = "distributed_span_attributes";
    public static final String TRACE_SPAN_ATTRIBUTES_KEYS_TABLE = "distributed_span_attributes_keys";
    public static final String TRACE_DEPENDENCY_GRAPH_TABLE = "distributed_dependency_graph_minutes";
    public static final String TRACE_TOP_LEVEL_OPERATIONS_TABLE = "distributed_top_level_operations";
    public static final String TRACE_USAGE_EXPLORER_TABLE = "distributed_usage_explorer";

    // trace cold table suffix
    // only trace_index, trace_error_index and trace_spans need cold storage
    public static final String LONG_TIME_STORAGE_TABLE_SUFFIX = "_cold";

    public static final String STRING_TAG_MAP   = "stringTagMap";
    public static final String NUMBER_TAG_MAP   = "numberTagMap";
    public static final String BOOL_TAG_MAP     = "boolTagMap";
    public static final String RESOURCE_TAG_MAP = "resourceTagsMap";

    public static final String TRACES_EXPLORER_VIEW_SQL_SELECT_WITH_SUB_QUERY = "SELECT distinct on (traceID) traceID, durationNano as \"subQuery.durationNano\", " +
            "serviceName as \"subQuery.serviceName\", name as \"subQuery.name\" FROM %s.%s WHERE parentSpanID = '' AND %s %s ORDER BY durationNano DESC ";

    public static final String TRACES_COUNT_VIEW_SQL_SELECT = "SELECT traceID, count() as span_count FROM %s.%s WHERE traceID IN (%s) GROUP BY traceID";
    public static final String TRACES_EXPLORER_VIEW_SQL_SELECT_QUERY = "SELECT subQuery.serviceName, subQuery.name, count() AS " +
            "span_count, subQuery.durationNano, traceID FROM %s.%s GLOBAL INNER JOIN subQuery ON %s.traceID = subQuery.traceID GROUP " +
            "BY traceID, subQuery.durationNano, subQuery.name, subQuery.serviceName ORDER BY subQuery.durationNano desc;";

    public static final String FIRST_QUERY_GRAPH_LIMIT = "first_query_graph_limit";
    public static final String SECOND_QUERY_GRAPH_LIMIT = "second_query_graph_limit";
    public static final boolean IS_PREFER_RPM = false;
    public static final String X_ZM_TRACKING_ID = "x-zm-trackingid";
    public static final String RESPONSE_DATA_ATTRIBUTE = "responseData";
    public static final String RESPONSE_DATA_ATTRIBUTE_LIST = "responseDataList";
}