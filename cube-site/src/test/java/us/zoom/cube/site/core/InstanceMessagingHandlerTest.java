package us.zoom.cube.site.core;


import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import us.zoom.cube.site.CubeSiteApplicationTests;
import us.zoom.cube.site.infra.utils.IdUtils;
import us.zoom.infra.dao.model.InstanceMessagingChannelDO;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2020/9/4 11:08 AM
 */
public class InstanceMessagingHandlerTest extends CubeSiteApplicationTests {

    private static final String TENANT_ID = "testTenantId";

    @Autowired
    private InstanceMessagingHandler instanceMessagingHandler;

    @Test
    public void add() {
//        InstanceMessagingChannelDO channelDO = new InstanceMessagingChannelDO();
//        channelDO.setId(IdUtils.generateId());
//        channelDO.setName("billing_channel");
//        channelDO.setUrl("https://inbots.zoom.us/incoming/hook/nQ44VMUPJVb1e8cn9W2-lZ7e?format=fields");
//        channelDO.setAuthorization("-3LyIyJcqSXCLWmugQt_VP3i");
//        channelDO.setCreateTime(new Date());
//        channelDO.setModifyTime(new Date());
//        channelDO.setTenantId("bb6a0a17-0703-4e06-affc-92834a5d9079");
//        instanceMessagingHandler.addChannel(channelDO);
    }

    @Test
    public void testAll() {
        InstanceMessagingChannelDO channelDO = buildChannel();
        instanceMessagingHandler.addChannel(channelDO);
        List<InstanceMessagingChannelDO> result = instanceMessagingHandler.findByChannelNameLike("", TENANT_ID, 1, 5);
        Assertions.assertTrue(result.size() > 0);

        channelDO.setUrl("new_url");
        channelDO.setName("new_name");
        channelDO.setAuthorizationEncrypted("new_token");
        instanceMessagingHandler.editChannel(channelDO);
        result = instanceMessagingHandler.listAllForTenant(TENANT_ID, "new_name");
        Assertions.assertTrue(result.size() > 0);

        instanceMessagingHandler.deleteChannelById(channelDO.getId());
    }

    private InstanceMessagingChannelDO buildChannel() {
        InstanceMessagingChannelDO channelDO = new InstanceMessagingChannelDO();
        channelDO.setId(IdUtils.generateId());
        channelDO.setName("web_im_channel");
        channelDO.setUrl("https://inbots.zoom.us/incoming/hook/nQ44VMUPJVb1e8cn9W2-lZ7e?format=fields");
        channelDO.setAuthorizationEncrypted("-3LyIyJcqSXCLWmugQt_VP3i");
        channelDO.setTenantId(TENANT_ID);
        channelDO.setCreateTime(new Date());
        channelDO.setModifyTime(new Date());
        return channelDO;
    }
}
