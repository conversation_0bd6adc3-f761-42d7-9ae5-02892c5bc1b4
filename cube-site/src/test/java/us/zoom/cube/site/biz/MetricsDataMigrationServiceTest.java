package us.zoom.cube.site.biz;

import com.google.common.collect.Lists;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import us.zoom.cube.site.lib.input.migration.AggregationRuleMigrationInput;
import us.zoom.infra.thread.ThreadLocalStore;

import java.util.stream.Collectors;

public class MetricsDataMigrationServiceTest extends  BaseTest  {
    @Autowired
    private MetricsDataMigrationService metricsDataMigrationService;

    String user1="265b4be6-652c-48dd-a8fe-a0f22256a4af";
    String tenantId="0ab0fa30-cbdc-456f-89c3-dc5188620edb";

    @Test
    public void migrateAggregationRule(){
        ThreadLocalStore.setUserInfoLocal(user1);
        ThreadLocalStore.setTenantInfoLocal(tenantId);
        AggregationRuleMigrationInput input = new AggregationRuleMigrationInput();
        input.setSourceVersion("v0");
        input.setDestVersion("v1");
        input.setAggIdList(Lists.newArrayList("aae106c3-36ca-43c5-9137-3862da8259b3","9086e4aa-3aba-4a3c-a4d4-a22e50af86fa").stream().collect(Collectors.toSet()));
        metricsDataMigrationService.migrateAggregationRule(input);
    }



}
