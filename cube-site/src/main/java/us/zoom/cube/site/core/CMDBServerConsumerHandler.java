package us.zoom.cube.site.core;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.site.lib.output.cmdb.CMDBServerInfo;
import us.zoom.mq.client.clients.consumer.RetryableStraw;
import us.zoom.mq.common.entity.TaskEntity;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
public class CMDBServerConsumerHandler implements RetryableStraw<String> {
    private final static TypeReference typeReference = new TypeReference<String>() {
    };
    private final static String INSERT = "insert";
    private final static String UPDATE = "update";
    private final static String DELETE = "delete";
    @Autowired
    private CmdbServerHandler cmdbServerHandler;

    @Override
    public boolean onMessage(List<TaskEntity<String>> taskEntities) {
        if (CollectionUtils.isEmpty(taskEntities)){
            return false;
        }
        taskEntities.forEach(taskEntity -> {
            try {
                String message = taskEntity.getPayload();
                JsonObject msgObject = JsonParser.parseString(message).getAsJsonObject();
                String operationType = msgObject.get("OperationType").getAsString();
                if (operationType.equals(INSERT)){
                    CMDBServerInfo info = makeInfo(msgObject.get("FullDocument").getAsJsonObject());
                    List<CMDBServerInfo> serverInfoAdd = new ArrayList<>();
                    serverInfoAdd.add(info);
                    cmdbServerHandler.batchAddCmdbServer(serverInfoAdd);
                }else if (operationType.equals(UPDATE)){
                    CMDBServerInfo info = makeInfo(msgObject.get("FullDocument").getAsJsonObject());
                    List<CMDBServerInfo> serverInfoUpdate = new ArrayList<>();
                    serverInfoUpdate.add(info);
                    cmdbServerHandler.batchUpdateCmdbServer(serverInfoUpdate);
                    log.info("update success");
                }else if(operationType.equals(DELETE)){
                    String serverId = msgObject.get("DocumentKey").getAsJsonObject().get("ID").getAsString();
                    cmdbServerHandler.deleteByServerId(serverId);
                }
            }catch (Exception e){
                log.info("consumer cmdb server update message error");
            }
        });
        return false;
    }

    private CMDBServerInfo makeInfo(JsonObject fullDocument) {
        CMDBServerInfo info = new Gson().fromJson(fullDocument,CMDBServerInfo.class);
        if (fullDocument.has("cloud_tags") && fullDocument.get("cloud_tags").getAsJsonObject().has("Cluster")) {
            info.setCluster(fullDocument.get("cloud_tags").getAsJsonObject().get("Cluster").getAsString());
        }
        JsonArray publicIpMac = fullDocument.get("publicip_mac").getAsJsonArray();
        if (publicIpMac.size()>0){
            info.setPublicIpMac(publicIpMac.get(0).getAsString());
        }else {
            info.setPublicIpMac("");
        }
        return info;
    }

    @Override
    public TypeReference<String> type() {
        return typeReference;
    }
}
