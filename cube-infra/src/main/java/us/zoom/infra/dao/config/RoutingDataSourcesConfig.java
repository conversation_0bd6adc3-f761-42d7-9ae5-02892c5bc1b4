package us.zoom.infra.dao.config;

import org.mybatis.spring.boot.autoconfigure.ConfigurationCustomizer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Map;

/**
 * @Author: <PERSON>
 * @ModuleOwner: <PERSON>
 * @Date:04/10/2023 16:56
 * @Description:
 */
@Configuration
@ConditionalOnProperty(name = "mysql.datasource.enabled", havingValue = "true", matchIfMissing = true)
public class RoutingDataSourcesConfig {

    @Autowired
    @Qualifier("metricDataSource")
    private DataSource metricDataSource;

    @Autowired
    @Qualifier("metricDataSourceStandby")
    private DataSource metricDataSourceStandby;

    @Bean
    @Qualifier("dynamicDataSource")
    public MultipleDataSource dynamicDataSource() {
        MultipleDataSource dynamicDataSource = new MultipleDataSource();
        Map<Object, Object> map = new HashMap<>();
        map.put(DataSourceType.MAIN.name(), metricDataSource);
        map.put(DataSourceType.STANDBY.name(), metricDataSourceStandby);
        dynamicDataSource.setTargetDataSources(map);
        dynamicDataSource.setDefaultTargetDataSource(metricDataSource);
        dynamicDataSource.afterPropertiesSet();

        return dynamicDataSource;
    }
}
