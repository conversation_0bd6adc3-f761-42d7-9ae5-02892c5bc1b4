package us.zoom.cube.site.lib.output.trace.queryrange;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * @author: eason.jia
 * @date: 2024/8/19
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Series {
    private Map<String, String> labels;
    private List<Map<String, String>> labelsArray;
    private List<Point> values;
}
