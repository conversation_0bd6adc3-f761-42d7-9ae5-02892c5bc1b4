package us.zoom.cube.site.lib.profiling.showdetails.defaults;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: <PERSON><PERSON> Ding
 * @date: 2024/11/28 14:29
 * @desc:
 */

public enum DefaultHostDisplayDetails {

    //private boolean pid = true;

    pid(true),
    ;

    private boolean display;

    DefaultHostDisplayDetails(boolean display) {
        this.display = display;
    }

    public boolean isDisplay() {
        return display;
    }

    public static Map<String, Boolean> getDefaultHostDisplayDetails() {
        return Arrays.stream(values()).collect(Collectors.toMap(DefaultHostDisplayDetails::name, DefaultHostDisplayDetails::isDisplay));
    }

}
