package us.zoom.cube.site.core.monitor;

import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import us.zoom.cloud.secrets.spring.annotation.SecretValue;
import us.zoom.cube.notice.biz.service.AlarmIMSender;
import us.zoom.infra.model.alarm.AlarmLevel;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Component
public class SelfMonitorHandler {

    @Value("${self.monitor.im.url}")
    private String url;

    @SecretValue("self.monitor.im.authorization")
    private String authorization;

    @Autowired
    AlarmIMSender alarmIMSender;


    public boolean send(String msgUrl, String alarmContent, String title, String alarmName,AlarmLevel alarmLevel, String imUrl, String authorization) {
        Map<String,Object> result = alarmIMSender.send(msgUrl,alarmContent,title,alarmName,alarmLevel,imUrl,authorization);
        return isSuccess(result);
    }


    public boolean send(String msgUrl, List<String> alarmContents, String title, String alarmName,AlarmLevel alarmLevel, String imUrl, String authorization) {
        Map<String,Object> result =  alarmIMSender.send(msgUrl,alarmContents,title,alarmName,alarmLevel,imUrl,authorization);
        return isSuccess(result);
    }

    private boolean isSuccess(Map<String, Object> result) {
        return result != null && result.get(AlarmIMSender.STATUS) != null && (boolean)result.get(AlarmIMSender.STATUS);
    }

    public boolean send(String msgUrl, List<String> alarmContents, String title, String alarmName,AlarmLevel alarmLevel, String imUrl, String authorization,boolean ignoreContent,String serviceName,String channelName) {
        Map<String,Object> result =   alarmIMSender.send(msgUrl,alarmContents,title,alarmName,alarmLevel,imUrl,authorization,ignoreContent,serviceName,channelName);
        return isSuccess(result);
    }

    public boolean send(String msgUrl,String alarmContent,String title,String alarmName,AlarmLevel alarmLevel) {
        Map<String,Object> result =   alarmIMSender.send(msgUrl,alarmContent,title,alarmName,alarmLevel,url,authorization);
        return isSuccess(result);
    }

    @Deprecated
    public boolean send(String msgUrl,List<String> alarmContent,String title,String alarmName,AlarmLevel alarmLevel) {
        Map<String,Object> result =  alarmIMSender.send(msgUrl,alarmContent,title,alarmName,alarmLevel,url,authorization);
        return isSuccess(result);
    }

    public boolean send(String msgUrl,List<String> alarmContent,String title,String alarmName,AlarmLevel alarmLevel, boolean ignoreContent,String tenantName, String channelName) {
        Map<String,Object> result =  alarmIMSender.send(msgUrl,alarmContent,title,alarmName,alarmLevel,url,authorization, ignoreContent, tenantName, channelName);
        return isSuccess(result);
    }
}
