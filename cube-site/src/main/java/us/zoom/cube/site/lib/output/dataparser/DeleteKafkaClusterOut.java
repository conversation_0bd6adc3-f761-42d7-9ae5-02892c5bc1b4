package us.zoom.cube.site.lib.output.dataparser;

import java.util.List;
import java.util.Map;

public class DeleteKafkaClusterOut {

    Map<String, List<String>> tenantToDataParsers;
    List<String> taskQueueName;

    public DeleteKafkaClusterOut(Map<String, List<String>> tenantToDataParsers, List<String> taskQueueName) {
        this.tenantToDataParsers = tenantToDataParsers;
        this.taskQueueName = taskQueueName;
    }

    public Map<String, List<String>> getTenantToDataParsers() {
        return tenantToDataParsers;
    }

    public void setTenantToDataParsers(Map<String, List<String>> tenantToDataParsers) {
        this.tenantToDataParsers = tenantToDataParsers;
    }

    public List<String> getTaskQueueName() {
        return taskQueueName;
    }

    public void setTaskQueueName(List<String> taskQueueName) {
        this.taskQueueName = taskQueueName;
    }
}
