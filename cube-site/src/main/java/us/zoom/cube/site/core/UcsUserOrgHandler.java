package us.zoom.cube.site.core;

import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;
import us.zoom.cube.lib.common.CubeConstants;
import us.zoom.infra.dao.model.UcsUserOrgInfoDO;
import us.zoom.infra.model.userorg.UcsUserOrgTree;
import us.zoom.infra.utils.CommonSplitConstants;
import us.zoom.infra.utils.Instance;

import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class UcsUserOrgHandler {
    @Autowired
    private UcsUserHanlder ucsUserHanlder;

    // Map<UcsUserId, UserOrgTree>
    private final AtomicReference<Map<String, UcsUserOrgTree>> userIdUserOrgTreeMapRef = new AtomicReference<>(new HashMap<>());
    // Map<UcsUserId, UserOrgTree>
    private final AtomicReference<Map<String, UcsUserOrgTree>> userNameUserOrgTreeMapRef = new AtomicReference<>(new HashMap<>());

    private final AtomicBoolean loading = new AtomicBoolean(false);

    private String rootEmployeeEmailList = "<EMAIL>";

    /**
     * Load organization structure into memory and build a forest of UserOrgTree
     */
    public void loadUserOrg() {
        if (loading.get()) {
            return;
        }

        StopWatch stopWatch = new StopWatch();
        stopWatch.start();

        try {
            loading.getAndSet(true);

            List<UcsUserOrgInfoDO> allUsers = ucsUserHanlder.getAllUcsUser();
            if (CollectionUtils.isEmpty(allUsers)) {
                userIdUserOrgTreeMapRef.set(new HashMap<>());
                return;
            }

            UcsUserOrgTree root = buildRootAndMap(allUsers);

            Set<String> missedParents = new HashSet<>();
            loopLoadTree(allUsers, root, missedParents);


        } catch (Exception e) {
            log.error("load user org error", e);
        } finally {
            loading.getAndSet(false);
            stopWatch.stop();
            log.info("loadUserOrg finished, cost={}ms", stopWatch.getTotalTimeMillis());
        }
    }

    private UcsUserOrgTree buildRootAndMap(List<UcsUserOrgInfoDO> allUsers) {
        // build root node and connect root employee to root node
        List<String> rootEmployee = Splitter.on(CommonSplitConstants.COMMA_SPLIT).splitToList(rootEmployeeEmailList);
        for (UcsUserOrgInfoDO user : allUsers) {
            for (String email : rootEmployee) {
                if (user.getEmail().equals(email)) {
                    user.setLeader(CubeConstants.DEFAULT_UCS_USER_ORG_ROOT);
                }
            }
        }
        UcsUserOrgInfoDO rootUserOrgInfoDO = new UcsUserOrgInfoDO();
        rootUserOrgInfoDO.setUserId(CubeConstants.DEFAULT_UCS_USER_ORG_ROOT);
        rootUserOrgInfoDO.setEmail(CubeConstants.DEFAULT_UCS_USER_ORG_ROOT);

        UcsUserOrgTree root = UcsUserOrgTree.copyFromDO(rootUserOrgInfoDO);
        Map<String, UcsUserOrgTree> userIdToNode = new HashMap<>();
        Map<String, UcsUserOrgTree> userNameToNode = new HashMap<>();
        userIdToNode.put(root.getUserId(), root);
        userNameToNode.put(root.getEmail(), root);

        for (UcsUserOrgInfoDO u : allUsers) {
            if (u == null || StringUtils.isBlank(u.getUserId()) || StringUtils.isBlank(u.getEmail())) {
                continue;
            }
            userIdToNode.put(u.getUserId(), UcsUserOrgTree.copyFromDO(u));
            userNameToNode.put(u.getEmail(), UcsUserOrgTree.copyFromDO(u));
        }

        userIdUserOrgTreeMapRef.set(userIdToNode);
        userNameUserOrgTreeMapRef.set(userNameToNode);

        return root;
    }

    private UcsUserOrgTree loopLoadTree(List<UcsUserOrgInfoDO> allItems, UcsUserOrgTree rootTree, Set<String> missedParents) {

        List<UcsUserOrgInfoDO> nextLoopItems = allItems;

        Set<String> parentSourceIds = new HashSet<>(Arrays.asList(rootTree.getUserId()));

        int loopDeepIndex = 1;
        while (CollectionUtils.isNotEmpty(parentSourceIds)) {
            Set<String> newParentIds = new HashSet<>();
            for (UcsUserOrgInfoDO nextLoopMenuItem : nextLoopItems) {

                if (parentSourceIds.contains(nextLoopMenuItem.getLeader())) {
                    UcsUserOrgTree father = userIdUserOrgTreeMapRef.get().get(nextLoopMenuItem.getLeader());
                    if (null == father) {
                        if (StringUtils.isNotBlank(nextLoopMenuItem.getLeader())) {
                            missedParents.add(nextLoopMenuItem.getLeader());
                        }
                        continue;
                    }
                    UcsUserOrgTree son = buildCurrentItem(nextLoopMenuItem);

                    father.addSon(son);
                    Collections.sort(father.getSons());
                    userIdUserOrgTreeMapRef.get().put(son.getUserId(), son);

                    newParentIds.add(son.getUserId());
                }
            }
            if (CollectionUtils.isNotEmpty(nextLoopItems)) {
                nextLoopItems = nextLoopItems.stream().filter(item -> !newParentIds.contains(item.getId())).collect(Collectors.toList());
            }

            parentSourceIds = newParentIds;

            loopDeepIndex++;
            if (loopDeepIndex > 20) {
                log.error("loopLoadTree, loopDeepIndex>20");
                break;
            }
        }

        return rootTree;
    }

    public List<String> getLeaders(String userName) {
        UcsUserOrgTree ucsUserOrgTree = userNameUserOrgTreeMapRef.get().get(userName);
        List ret = Lists.newArrayList();
        if (null == ucsUserOrgTree) {
            return Lists.newArrayList();
        }

        int i = 0;
        while (i < 20) {
            UcsUserOrgTree leader = userIdUserOrgTreeMapRef.get().get(ucsUserOrgTree.getLeader());
            if (null == leader || leader.equals(CubeConstants.DEFAULT_UCS_USER_ORG_ROOT)) {
                break;
            }
            ret.add(leader.getEmail());

            ucsUserOrgTree = leader;
            i++;
        }

        return ret;

    }

    public List<String> getTeammates(String userName) {
        UcsUserOrgTree ucsUserOrgTree = userNameUserOrgTreeMapRef.get().get(userName);
        List<String> ret = Lists.newArrayList();
        if (null == ucsUserOrgTree) {
            return Lists.newArrayList();
        }

        UcsUserOrgTree leader = userNameUserOrgTreeMapRef.get().get(ucsUserOrgTree.getLeader());

        ret.addAll(Instance.ofNullable(leader.getSons()).stream().map(UcsUserOrgTree::getEmail)
                .collect(Collectors.toList()));

        return ret;

    }

    public boolean isManager(String userName) {
        UcsUserOrgTree ucsUserOrgTree = userNameUserOrgTreeMapRef.get().get(userName);
        if (ucsUserOrgTree == null && CollectionUtils.isNotEmpty(ucsUserOrgTree.getSons())) {
            return true;
        }
        return false;
    }

    private UcsUserOrgTree buildCurrentItem(UcsUserOrgInfoDO nextLoopMenuItem) {
        UcsUserOrgTree son = UcsUserOrgTree.copyFromDO(nextLoopMenuItem);
        return son;
    }
}
