package us.zoom.cube.site.core;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import us.zoom.cube.lib.common.CubeConstants;
import us.zoom.cube.lib.common.DataParserPurposeEnum;
import us.zoom.cube.lib.common.DataParserUseStatusEnum;
import us.zoom.cube.site.lib.input.OutDiscoverPipelineInput;
import us.zoom.cube.site.lib.query.DataParserV2Query;
import us.zoom.cube.site.lib.query.PageQuery;
import us.zoom.infra.dao.model.*;
import us.zoom.infra.dao.service.*;
import us.zoom.infra.enums.AutoDiscoverPipelineStatusEnum;
import us.zoom.infra.enums.DataParserDataTypeEnum;
import us.zoom.infra.enums.FieldTypeEnum;
import us.zoom.infra.enums.LanguageEnum;
import us.zoom.infra.utils.Instance;

import java.util.*;

/**
 * <AUTHOR> @date 2020/3/10
 */
@Component
public class DataParserHandler {

    @Autowired
    private DataParserDAO dataParserDAO;

    @Autowired
    private DataParserSourceDAO dataParserSourceDAO;

    @Autowired
    private DataParserPipelineDAO dataParserPipelineDAO;


    @Autowired
    private DataParserRemapperProcessorDAO dataParserRemapperProcessorDAO;


    @Autowired
    private DataParserFilterProcessorDAO dataParserFilterProcessorDAO;


    @Autowired
    private DataParserGrokProcessorDAO dataParserGrokProcessorDAO;

    @Autowired
    private CollectorDAO collectorDAO;

    @Autowired
    private CollectorFieldDAO collectorFieldDAO;

    @Autowired
    private DataParserPipelineHandler dataParserPipelineHandler;

    @Autowired
    private AutoDiscoverPipeLineHandler autoDiscoverPipeLineHandler;


    @Transactional(rollbackFor = Exception.class)
    public void addDataParser(DataParserDO dataParserDO, List<DataParserPipelineDO> pipelines, List<Object> dataParserProcessors, Map<CollectorDO, List<CollectorFieldDO>> collectors) throws Exception {
        Assert.notNull(dataParserDO, "data parse is null!");
//        Assert.notEmpty(pipelines,"pipeline is empty!");
//        Assert.notEmpty(dataParserProcessors,"dataParserProcessors is empty!");
//        Assert.notEmpty(collectors,"collectors is empty!");

        checkNullValue(dataParserDO);
        dataParserDAO.addDataParser(dataParserDO);
        Instance.ofNullable(pipelines).forEach(pipeline -> dataParserPipelineHandler.addPipeline(pipeline));
        Instance.ofNullable(dataParserProcessors).forEach(dataParserProcessor -> {
            if (dataParserProcessor instanceof DataParserFilterProcessorDO) {
                dataParserFilterProcessorDAO.add((DataParserFilterProcessorDO) dataParserProcessor);
            } else if (dataParserProcessor instanceof DataParserGrokProcessorDO) {
                dataParserGrokProcessorDAO.add((DataParserGrokProcessorDO) dataParserProcessor);
            } else if (dataParserProcessor instanceof DataParserRemapperProcessorDO) {
                dataParserRemapperProcessorDAO.add((DataParserRemapperProcessorDO) dataParserProcessor);
            }
        });

        //add the collector
        Iterator<CollectorDO> keys = Instance.ofNullable(collectors).keySet().iterator();
        while (keys.hasNext()) {
            CollectorDO collectorDO = keys.next();
            collectorDAO.insertCollector(collectorDO);
            collectors.get(collectorDO).forEach(field -> collectorFieldDAO.insertCollectorField(field));
        }
    }

    private void checkNullValue(DataParserDO dataParserDO) {
        if (null == dataParserDO.getInfraType()) {
            dataParserDO.setInfraType("");
        }
        if (null == dataParserDO.getDataType()) {
            dataParserDO.setDataType(DataParserDataTypeEnum.app.getCode());
        }
        if (null == dataParserDO.getExcludeRule()) {
            dataParserDO.setExcludeRule("");
        }
        if (null == dataParserDO.getFilePath()) {
            dataParserDO.setFilePath("");
        }
        if (null == dataParserDO.getIncludeRule()) {
            dataParserDO.setIncludeRule("");
        }
        if (null == dataParserDO.getLanguageType()) {
            dataParserDO.setLanguageType(LanguageEnum.java.getCode());
        }
        if (null == dataParserDO.getRawDataParseRule()) {
            dataParserDO.setRawDataParseRule("");
        }
        if (null == dataParserDO.getChannelRule()) {
            dataParserDO.setChannelRule("");
        }
        if (null == dataParserDO.getInvokeFunction()) {
            dataParserDO.setInvokeFunction("");
        }
        if (null == dataParserDO.getServiceId()) {
            dataParserDO.setServiceId("");
        }
        if (null == dataParserDO.getAqId()) {
            dataParserDO.setAqId("");
        }
        if (null == dataParserDO.getAqGroupId()) {
            dataParserDO.setAqGroupId("");
        }
        if (null == dataParserDO.getRawDataParseType()) {
            dataParserDO.setRawDataParseType("");
        }

        if (null == dataParserDO.getPurpose()) {
            dataParserDO.setPurpose(DataParserPurposeEnum.handle.getValue());
        }

        if (null == dataParserDO.getOutputAqId()) {
            dataParserDO.setOutputAqId("");
        }

        if (null == dataParserDO.getTopic()) {
            dataParserDO.setTopic("");
        }

        if (null == dataParserDO.getUseStatus()) {
            dataParserDO.setUseStatus(DataParserUseStatusEnum.use.getValue());
        }

        if (null == dataParserDO.getTopicTemplate()) {
            dataParserDO.setTopicTemplate("");
        }

    }

    public void editDataParser(DataParserDO dataParserDO) {
        checkNullValue(dataParserDO);
        Assert.notNull(dataParserDO, "data parse is null!");
        dataParserDAO.editDataParser(dataParserDO);
    }


    public DataParserDO getDataParserByTenantIdAndName(String tenantId, String name) {
        return dataParserDAO.getDataParserByTenantIdAndName(tenantId, name);
    }

    public DataParserDO getDataParserByTenantIdAndNameNotId(String tenantId, String name, String id) {
        return dataParserDAO.getDataParserByTenantIdAndNameNotId(tenantId, name, id);
    }

    public List<DataParserDO> listAllDataParser() {
        List<DataParserDO> dataParserDOList = new ArrayList<>();
        long counts = dataParserDAO.queryCounts();
        int pageSize = CubeConstants.CUBE_SQL_BATCH_QUERY_SIZE;
        long pageNum = counts / pageSize + (counts % pageSize == 0 ? 0 : 1);
        for (int i = 1; i <= pageNum; i++) {
            dataParserDOList.addAll(dataParserDAO.listBatchDataParser(pageSize * (i - 1), pageSize));
        }
        return dataParserDOList;
    }

    public List<DataParserDO> listByStatus(DataParserUseStatusEnum dataParserUseStatusEnum) {
        List<DataParserDO> dataParserDOList = new ArrayList<>();
        long counts = dataParserDAO.queryCountsByStatus(dataParserUseStatusEnum.getValue());
        int pageSize = CubeConstants.CUBE_SQL_BATCH_QUERY_SIZE;
        long pageNum = counts / pageSize + (counts % pageSize == 0 ? 0 : 1);
        for (int i = 1; i <= pageNum; i++) {
            dataParserDOList.addAll(dataParserDAO.listBatchDataParserByStatus(dataParserUseStatusEnum.getValue(), pageSize * (i - 1), pageSize));
        }
        return dataParserDOList;
    }

    public List<DataParserDO> listDataParserByTenantId(String tenantId) {
        return dataParserDAO.listDataParserByTenantId(tenantId);
    }


    public List<DataParserDO> findByCollectorNameLike(String dataParserName, String tenantId, int pageIndex, int pageSize) throws Exception {
        return dataParserDAO.findByCollectorNameLike(dataParserName, tenantId, pageSize * (pageIndex - 1), pageSize);

    }

    public List<DataParserDO> findByParam(String tenantId, String name, Integer rawDataType, Integer useStatus, Set<String> ids, Integer purpose, int pageIndex, int pageSize) throws Exception {
        return dataParserDAO.findByParam(tenantId, name, rawDataType, useStatus, ids, purpose, pageSize * (pageIndex - 1), pageSize);
    }

    public Integer getCountByParam(String tenantId, String name, Integer rawDataType, Integer useStatus, Set<String> ids, Integer purpose) throws Exception {
        return dataParserDAO.getCountByParam(tenantId, name, rawDataType, useStatus, ids, purpose);
    }

    public List<String> findNameByTenantId(PageQuery<DataParserV2Query> pageQuery) {
        DataParserV2Query query = pageQuery.getQueryPara();
        return dataParserDAO.findNameByTenantId(query.getTenantId(), query.getName(),
                pageQuery.getStartIndex(), pageQuery.getPageSize());
    }

    public Integer getCountByTenantId(String tenantId, String name) throws Exception {
        return dataParserDAO.getCountByTenantId(tenantId, name);
    }

    public int getCountByCollectorNameLike(String dataParserName, String tenantId) {
        return dataParserDAO.getCountByCollectorNameLike(dataParserName, tenantId);

    }

    public DataParserDO getById(String id) {
        return dataParserDAO.getById(id);
    }

    public List<DataParserDO> getByIds(Set<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return dataParserDAO.getByIds(ids);
    }

    public List<Map<String, Object>> listDataparserNameByIds(Set<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return dataParserDAO.listDataparserNameByIds(ids);
    }

    @Transactional(rollbackFor = Exception.class)
    public void addField(CollectorFieldDO fieldDO, CollectorDO collectorDO, String pipelineId) {
        Assert.isTrue(FieldTypeEnum.matchType(fieldDO.getFieldType()), "field type is not string or number");
        if (null != collectorDO) {
            if (StringUtils.isEmpty(collectorDO.getTopic())) {
                collectorDO.setTopic("");
            }
            collectorDAO.insertCollector(collectorDO);
            dataParserPipelineDAO.updateCollectorId(collectorDO.getId(), pipelineId);
        }
        collectorFieldDAO.insertCollectorField(fieldDO);
    }

    @Transactional(rollbackFor = Exception.class)
    public void batchAddField(List<CollectorFieldDO> fieldDOS, CollectorDO collectorDO, String pipelineId) {
        if (null != collectorDO) {
            collectorDAO.insertCollector(collectorDO);
            dataParserPipelineDAO.updateCollectorId(collectorDO.getId(), pipelineId);
        }
        if (!CollectionUtils.isEmpty(fieldDOS)) {
            collectorFieldDAO.batchInsertCollectorField(fieldDOS);
        }
    }

    public void delDataParser(String id) {
        dataParserDAO.deleteById(id);
    }

    public List<String> listIdContainsPiplines(List<String> pipelineNames) {

        if (CollectionUtils.isEmpty(pipelineNames)) {
            return Collections.emptyList();
        }

        List<DataParserPipelineDO> pipelineDOS = dataParserPipelineHandler.listAll();
        if (CollectionUtils.isEmpty(pipelineDOS)) {
            return Collections.emptyList();
        }

        Map<String, List<DataParserPipelineDO>> parserPipelinesMap = new HashMap<>();
        for (DataParserPipelineDO pipelineDO : pipelineDOS) {
            List<DataParserPipelineDO> pipelinesOfParser = parserPipelinesMap.computeIfAbsent(pipelineDO.getDataParserId(), f -> new ArrayList<>());
            pipelinesOfParser.add(pipelineDO);
        }

        List<String> result = new ArrayList<>();
        parserPipelinesMap.forEach((parserId, pipelines) -> {
            boolean containsAll = true;
            for (String matchPipeName : pipelineNames) {
                boolean containsOne = false;
                for (DataParserPipelineDO pipelineDO : pipelines) {
                    if (pipelineDO.getName().equalsIgnoreCase(matchPipeName)) {
                        containsOne = true;
                        continue;
                    }
                }

                if (!containsOne) {
                    containsAll = false;
                }

                if (!containsAll) {
                    continue;
                }
            }
            if (containsAll) {
                result.add(parserId);
            }
        });
        return result;
    }

    public List<CollectorFieldDO> listFieldByCollectorIds(List<String> collectIds) {
        if (CollectionUtils.isEmpty(collectIds)) {
            return new ArrayList<>();
        }
        return collectorFieldDAO.listFieldByCollectorIds(collectIds);
    }

    @Transactional(rollbackFor = Exception.class)
    public void batchAddPipelineField(List<DataParserPipelineDO> parserPipelineDOSToUpdate, List<CollectorDO> collectorDOToAdd, List<CollectorFieldDO> fieldListToAdd) {
        if (!CollectionUtils.isEmpty(parserPipelineDOSToUpdate)) {
            dataParserPipelineHandler.batchUpdateCollectorId(parserPipelineDOSToUpdate);
        }
        if (!CollectionUtils.isEmpty(collectorDOToAdd)) {
            collectorDAO.batchInsertCollector(collectorDOToAdd);
        }
        if (!CollectionUtils.isEmpty(fieldListToAdd)) {
            collectorFieldDAO.batchInsertCollectorField(fieldListToAdd);
        }
    }

    public void addDiscoverPipeline(String id, String uniqueId) {
        autoDiscoverPipeLineHandler.updateStatusById(id, AutoDiscoverPipelineStatusEnum.added.getCode(), uniqueId);
    }

    public void updateDiscoverPipeline(String id, String uniqueId) {
        autoDiscoverPipeLineHandler.updateStatusById(id, AutoDiscoverPipelineStatusEnum.updated.getCode(), uniqueId);
    }

    public int deleteByDiscoverPipelineId(String id, String uniqueId) {
        return autoDiscoverPipeLineHandler.deleteById(id, uniqueId);
    }

    public void setTypeByTenantIdAndDataParserId(OutDiscoverPipelineInput outDiscoverPipelineInput) {
        autoDiscoverPipeLineHandler.setTypeByTenantIdAndDataParserId(outDiscoverPipelineInput);
    }

    @Transactional(rollbackFor = Exception.class, timeout = 5)
    public void saveDiscoverPipeline(List<AutoDiscoverPipelineDO> autoDiscoverPipelineDOListToAdd, List<AutoDiscoverPipelineDO> autoDiscoverPipelineDOListToUpdate) {
        if (!CollectionUtils.isEmpty(autoDiscoverPipelineDOListToAdd)) {
            autoDiscoverPipeLineHandler.batchAdd(autoDiscoverPipelineDOListToAdd);
        }
        if (!CollectionUtils.isEmpty(autoDiscoverPipelineDOListToUpdate)) {
            autoDiscoverPipeLineHandler.batchUpdate(autoDiscoverPipelineDOListToUpdate);
        }
    }

    public void correctAutoDiscoverPipelineDeletedFields(OutDiscoverPipelineInput outDiscoverPipelineInput) {
        autoDiscoverPipeLineHandler.correctAutoDiscoverPipelineDeletedFields(outDiscoverPipelineInput);
    }

    public AutoDiscoverPipelineDO getDiscoverPipelineById(String discoverPipelineInputId) {
        return autoDiscoverPipeLineHandler.selectById(discoverPipelineInputId);
    }

    public List<AutoDiscoverPipelineDO> listPipelineUnhandled(List<String> dataParserIds) {
        if (CollectionUtils.isEmpty(dataParserIds)) {
            return Collections.emptyList();
        }
        return autoDiscoverPipeLineHandler.listPipelineUnhandled(dataParserIds);
    }

    public List<AutoDiscoverPipelineDO> selectDiscoverPipelineByNameAndParserId(String dataParserId, String pipelineName, int pageIndex, int pageSize) {
        return autoDiscoverPipeLineHandler.selectDiscoverPipelineByNameAndParserId(dataParserId, pipelineName, pageIndex, pageSize);
    }

    public int selectDiscoverPipelineCountByNameAndParserId(String dataParserId, String pipelineName) {
        return autoDiscoverPipeLineHandler.selectDiscoverPipelineCountByNameAndParserId(dataParserId, pipelineName);
    }

    public void checkTenantId(String tenantId, String dataParserId) {
        DataParserDO dataParserDO = dataParserDAO.getById(dataParserId);
        if (dataParserDO != null) {
            Assert.isTrue(dataParserDO.getTenantId().equals(tenantId), "data parser or tenant id is error");
            return;
        }
        DataParserSourceDO dataParserSourceDO = dataParserSourceDAO.getById(dataParserId);
        if (dataParserSourceDO != null) {
            Assert.isTrue(dataParserSourceDO.getTenantId().equals(tenantId), "new data parser or tenant id is error");
            return;
        }
        Assert.isTrue(false, "can not find this data parser");
    }

    public List<String> getDataparserNameByIds(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return dataParserSourceDAO.listDataparserNameByIds(ids);
    }

    public List<String> getDataparserNameByTenantId(String tenantId) {
        if (StringUtils.isEmpty(tenantId)) {
            return Collections.emptyList();
        }
        return dataParserSourceDAO.listDataparserNameByTenantId(tenantId);
    }
}
