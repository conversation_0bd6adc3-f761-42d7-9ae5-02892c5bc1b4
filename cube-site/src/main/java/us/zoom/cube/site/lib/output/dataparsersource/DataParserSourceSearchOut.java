package us.zoom.cube.site.lib.output.dataparsersource;
import java.util.Date;
import java.util.List;
import java.util.Map;

public class DataParserSourceSearchOut {
    private String id;
    private String name;
    private String collectType;
    private String source;
    private List<String> paths;
    private String tenantId;

    // key:dataFlowId, value:dataFlowName
    private Map<String, String> dataFlowMap;

    private String rawDataFormat;
    private String rawDataParseType;
    private String invokeFunction;
    private String rawDataParseRule;

    private String status;
    private Date gmtCreate;
    private Date gmtModify;

    private String createUserId;
    private String editUserId;

    private String serviceName;

    private String createUserName;

    private String editUserName;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCollectType() {
        return collectType;
    }

    public void setCollectType(String collectType) {
        this.collectType = collectType;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public List<String> getPaths() {
        return paths;
    }

    public void setPaths(List<String> paths) {
        this.paths = paths;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public Map<String, String> getDataFlowMap() {
        return dataFlowMap;
    }

    public void setDataFlowMap(Map<String, String> dataFlowMap) {
        this.dataFlowMap = dataFlowMap;
    }

    public String getRawDataFormat() {
        return rawDataFormat;
    }

    public void setRawDataFormat(String rawDataFormat) {
        this.rawDataFormat = rawDataFormat;
    }

    public String getRawDataParseType() {
        return rawDataParseType;
    }

    public void setRawDataParseType(String rawDataParseType) {
        this.rawDataParseType = rawDataParseType;
    }

    public String getInvokeFunction() {
        return invokeFunction;
    }

    public void setInvokeFunction(String invokeFunction) {
        this.invokeFunction = invokeFunction;
    }

    public String getRawDataParseRule() {
        return rawDataParseRule;
    }

    public void setRawDataParseRule(String rawDataParseRule) {
        this.rawDataParseRule = rawDataParseRule;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtModify() {
        return gmtModify;
    }

    public void setGmtModify(Date gmtModify) {
        this.gmtModify = gmtModify;
    }

    public String getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(String createUserId) {
        this.createUserId = createUserId;
    }

    public String getEditUserId() {
        return editUserId;
    }

    public void setEditUserId(String editUserId) {
        this.editUserId = editUserId;
    }

    public String getCreateUserName() {
        return createUserName;
    }

    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }

    public String getEditUserName() {
        return editUserName;
    }

    public void setEditUserName(String editUserName) {
        this.editUserName = editUserName;
    }

    public String getServiceName() {
        return serviceName;
    }

    public void setServiceName(String serviceName) {
        this.serviceName = serviceName;
    }
}
