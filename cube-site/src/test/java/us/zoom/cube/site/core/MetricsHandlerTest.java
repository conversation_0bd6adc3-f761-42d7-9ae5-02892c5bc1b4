package us.zoom.cube.site.core;

import com.google.common.collect.Lists;
import com.zoom.op.monitor.domain.AggregationRuleType;
import com.zoom.op.monitor.domain.AggregationType;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.lib.integrations.MetricsFieldTypeEnum;
import us.zoom.cube.site.CubeSiteApplicationTests;
import us.zoom.cube.site.infra.utils.IdUtils;
import us.zoom.cube.site.lib.output.config.metrics.FieldOrTag;
import us.zoom.infra.dao.model.*;
import us.zoom.infra.enums.MetricsTypeEnum;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2020/7/29 2:04 PM
 */
public class MetricsHandlerTest extends CubeSiteApplicationTests {

    private static final String tenantId = "tenantId_test";

    @Autowired
    private MetricsHandler metricsHandler;

    @Autowired
    private MetricsFieldHandler metricsFieldHandler;

    @Test
    public void findByMetricsNameLike() {
        List<MetricsDO> result = metricsHandler.findByMetricsNameLike("test", Lists.newArrayList("web1"), 1, 5);
        Assertions.assertTrue(result.size() > 0);

        result = metricsHandler.findByMetricsNameLike("Tesd", Lists.newArrayList("web1"), 1, 5);
        Assertions.assertTrue(CollectionUtils.isEmpty(result));
    }

    @Test
    public void getCountByMetricsNameLike() {
        int result = metricsHandler.getCountByMetricsNameLike("test", Lists.newArrayList("web1"));
        Assertions.assertTrue(result > 0);

        result = metricsHandler.getCountByMetricsNameLike("tesD", Lists.newArrayList("web1"));
        Assertions.assertEquals(0, result);
    }

    @Test
    public void addOriginalMetrics() {
        MetricsDO metricsDO = buildMetricsDO();
        CollectorMetricsDO collectorMetricsDO = buildCollectorMetricsDO(metricsDO);
        List<MetricsFieldDO> metricsFieldDOList = buildMetricsFieldDOList(metricsDO);

        metricsHandler.addOriginalMetrics(metricsDO, collectorMetricsDO, metricsFieldDOList);

        boolean hasSameMetricsName = metricsHandler.hasSameMetricsName(metricsDO.getMetricsName(), Lists.newArrayList(tenantId));
        Assertions.assertTrue(hasSameMetricsName);

        MetricsDO result = metricsHandler.getMetricsById(metricsDO.getId());
        Assertions.assertNotNull(result);
        String collectorId = metricsHandler.getCollectorIdByMetricsId(metricsDO.getId());
        Assertions.assertNotNull(collectorId);
        List<FieldOrTag> tagAndFieldList = metricsHandler.getTagAndField(metricsDO.getId());
        Assertions.assertEquals(4, tagAndFieldList.size());

        metricsHandler.deleteMetrics(metricsDO.getId());
    }

    private MetricsDO buildMetricsDO() {
        MetricsDO metricsDO = new MetricsDO();
        metricsDO.setId(IdUtils.generateId());
        metricsDO.setMetricsName("metricsName_test_addOriginalMetrics");
        metricsDO.setTenantId(tenantId);
        metricsDO.setTagNames("test_cloud_type,test_host_name");
        metricsDO.setType(MetricsTypeEnum.ORIGINAL.getValue());
        metricsDO.setCreateTime(new Date());
        metricsDO.setModifyTime(new Date());
        return metricsDO;
    }

    private CollectorMetricsDO buildCollectorMetricsDO(MetricsDO metricsDO) {
        CollectorMetricsDO collectorMetricsDO = new CollectorMetricsDO();
        collectorMetricsDO.setId(IdUtils.generateId());
        collectorMetricsDO.setCollectorId(IdUtils.generateId());
        collectorMetricsDO.setMetricsId(metricsDO.getId());
        collectorMetricsDO.setCreateTime(new Date());
        collectorMetricsDO.setModifyTime(new Date());
        return collectorMetricsDO;
    }

    private List<MetricsFieldDO> buildMetricsFieldDOList(MetricsDO metricsDO) {
        List<MetricsFieldDO> metricsFieldDOList = Lists.newArrayList();
        MetricsFieldDO metricsFieldDO1 = new MetricsFieldDO();
        metricsFieldDO1.setId(IdUtils.generateId());
        metricsFieldDO1.setMetricsId(metricsDO.getId());
        metricsFieldDO1.setFieldName("test_fieldName1");
        metricsFieldDO1.setFieldType(MetricsFieldTypeEnum.number.getValue());
        metricsFieldDO1.setCreateTime(new Date());
        metricsFieldDO1.setModifyTime(new Date());
        metricsFieldDOList.add(metricsFieldDO1);

        MetricsFieldDO metricsFieldDO2 = new MetricsFieldDO();
        metricsFieldDO2.setId(IdUtils.generateId());
        metricsFieldDO2.setMetricsId(metricsDO.getId());
        metricsFieldDO2.setFieldName("test_fieldName2");
        metricsFieldDO2.setFieldType(MetricsFieldTypeEnum.number.getValue());
        metricsFieldDO2.setCreateTime(new Date());
        metricsFieldDO2.setModifyTime(new Date());
        metricsFieldDOList.add(metricsFieldDO2);
        return metricsFieldDOList;
    }

    @Test
    public void addAggregatedMetrics() {
        MetricsDO metricsDO = buildMetricsDO();
        metricsDO.setType(MetricsTypeEnum.AGGREGATION.getValue());
        metricsDO.setMetricsName("metricsName_test_addAggregationMetrics");
        CollectorMetricsDO collectorMetricsDO = buildCollectorMetricsDO(metricsDO);
        List<MetricsFieldDO> metricsFieldDOList = buildMetricsFieldDOList(metricsDO);

        MetricsAggregationDO metricsAggregationDO = buildMetricsAggregationDO(metricsDO);
        List<MetricsAggregationRuleDO > metricsAggregationRuleDOList = buildMetricsAggregationRuleDOList(metricsAggregationDO);
        List<AggregationFunctionItemDO> aggregationFunctionItemDOList = new ArrayList<>();
        List<AggregationHistogramRangeItemDO> aggregationHistogramRangeItemDOList = new ArrayList<>();
        List<AggregationPercentileItemDO> aggregationPercentileItemDOList = new ArrayList<>();
        List<AggregationCustomFieldRuleDO> aggregationCustomFieldRuleDOList = new ArrayList<>();
        metricsHandler.addAggregatedMetrics(metricsDO, collectorMetricsDO, metricsFieldDOList, metricsAggregationDO, metricsAggregationRuleDOList, aggregationFunctionItemDOList, aggregationHistogramRangeItemDOList, aggregationPercentileItemDOList, aggregationCustomFieldRuleDOList);
        metricsHandler.deleteMetrics(metricsDO.getId());
    }

    private MetricsAggregationDO buildMetricsAggregationDO(MetricsDO metricsDO) {
        MetricsAggregationDO metricsAggregationDO = new MetricsAggregationDO();
        metricsAggregationDO.setId(IdUtils.generateId());
        metricsAggregationDO.setMetricsId(metricsDO.getId());
        metricsAggregationDO.setAggPeriod(60);
        metricsAggregationDO.setFilterCondition("test_fieldName == 'test_666'");
        metricsAggregationDO.setWaitTime(500);
        metricsAggregationDO.setCreateTime(new Date());
        metricsAggregationDO.setModifyTime(new Date());
        return metricsAggregationDO;
    }

    private List<MetricsAggregationRuleDO> buildMetricsAggregationRuleDOList(MetricsAggregationDO metricsAggregationDO) {
        List<MetricsAggregationRuleDO> metricsAggregationRuleDOList = Lists.newArrayList();
        MetricsAggregationRuleDO metricsAggregationRuleDO = new MetricsAggregationRuleDO();
        metricsAggregationRuleDO.setId(IdUtils.generateId());
        metricsAggregationRuleDO.setAggId(metricsAggregationDO.getId());
        metricsAggregationRuleDO.setAggField("test_aggField");
        metricsAggregationRuleDO.setAggTypes("sum,max,min");
        metricsAggregationRuleDO.setCreateTime(new Date());
        metricsAggregationRuleDO.setModifyTime(new Date());
        metricsAggregationRuleDOList.add(metricsAggregationRuleDO);
        return metricsAggregationRuleDOList;
    }

    @Test
    public void getMetricsByTenant() {
        List<MetricsDO> result = metricsHandler.getMetricsByTenant("web1");
        Assertions.assertNotNull(result);
    }

    @Test
    public void hasSameId() {
        boolean result = metricsHandler.hasSameId("0a17fd97-cfc9-431d-af21-1751ed460458");
        Assertions.assertTrue(result);
        result = metricsHandler.hasSameId("metrics-id-not-exists");
        Assertions.assertFalse(result);
    }

    @Test
    public void addAggregatedMetricsNormal() {
        MetricsDO metricsDO = new MetricsDO();
        metricsDO.setId("sjj-metricsId1");
        metricsDO.setMetricsName("sjj-metricsName1");
        metricsDO.setType(MetricsTypeEnum.AGGREGATION.getValue());
        metricsDO.setTenantId("sjj-tenantId1");
        metricsDO.setTagNames("cluster");

        CollectorMetricsDO collectorMetricsDO = new CollectorMetricsDO();
        collectorMetricsDO.setId("sjj-cmId1");
        collectorMetricsDO.setMetricsId("sjj-metricsId1");
        collectorMetricsDO.setCollectorId("sjj-collectorId1");

        List<MetricsFieldDO> metricsFieldDOList = new ArrayList<>();
        MetricsFieldDO metricsFieldDO1 = new MetricsFieldDO();
        metricsFieldDO1.setId("sjj-metricsFieldId1");
        metricsFieldDO1.setMetricsId("sjj-metricsId1");
        metricsFieldDO1.setFieldType(MetricsFieldTypeEnum.number.getValue());
        metricsFieldDO1.setFieldName("sjj-metriceFieldName1");
        metricsFieldDOList.add(metricsFieldDO1);

        MetricsFieldDO metricsFieldDO2 = new MetricsFieldDO();
        metricsFieldDO2.setId("sjj-metricsFieldId2");
        metricsFieldDO2.setMetricsId("sjj-metricsId1");
        metricsFieldDO2.setFieldType(MetricsFieldTypeEnum.number.getValue());
        metricsFieldDO2.setFieldName("sjj-metriceFieldName2");
        metricsFieldDOList.add(metricsFieldDO2);

        MetricsAggregationDO metricsAggregationDO = new MetricsAggregationDO();
        metricsAggregationDO.setId("sjj-maId1");
        metricsAggregationDO.setMetricsId("sjj-metricsId1");
        metricsAggregationDO.setAggPeriod(60);
        metricsAggregationDO.setFilterCondition("abc");
        metricsAggregationDO.setWaitTime(60);

        List<MetricsAggregationRuleDO> metricsAggregationRuleDOList = new ArrayList<>();
        // function version 0
        MetricsAggregationRuleDO metricsAggregationRuleDO1 = new MetricsAggregationRuleDO();
        metricsAggregationRuleDO1.setId("sjj-mar1");
        metricsAggregationRuleDO1.setAggId("sjj-maId1");
        metricsAggregationRuleDO1.setAggField("timeMills");
        metricsAggregationRuleDO1.setAggTypes("sum,count");
        metricsAggregationRuleDO1.setFilterCondition("a>b");
        metricsAggregationRuleDO1.setAggRuleType(AggregationRuleType.function.value());
        metricsAggregationRuleDO1.setIsConditioned(0);
        metricsAggregationRuleDO1.setConditionalFieldPrefix("");
        metricsAggregationRuleDO1.setVersion(0);
        metricsAggregationRuleDOList.add(metricsAggregationRuleDO1);

        // function version 1 not conditioned
        MetricsAggregationRuleDO metricsAggregationRuleDO2 = new MetricsAggregationRuleDO();
        metricsAggregationRuleDO2.setId("sjj-mar2");
        metricsAggregationRuleDO2.setAggId("sjj-maId1");
        metricsAggregationRuleDO2.setAggField("timeMills");
        metricsAggregationRuleDO2.setAggTypes("");
        metricsAggregationRuleDO2.setFilterCondition("a>b");
        metricsAggregationRuleDO2.setAggRuleType(AggregationRuleType.function.value());
        metricsAggregationRuleDO2.setIsConditioned(0);
        metricsAggregationRuleDO2.setConditionalFieldPrefix("");
        metricsAggregationRuleDO2.setVersion(1);
        metricsAggregationRuleDOList.add(metricsAggregationRuleDO2);


        // function version 1 conditioned
        MetricsAggregationRuleDO metricsAggregationRuleDO3 = new MetricsAggregationRuleDO();
        metricsAggregationRuleDO3.setId("sjj-mar3");
        metricsAggregationRuleDO3.setAggId("sjj-maId1");
        metricsAggregationRuleDO3.setAggField("timeMills");
        metricsAggregationRuleDO3.setAggTypes("");
        metricsAggregationRuleDO3.setFilterCondition("a>b");
        metricsAggregationRuleDO3.setAggRuleType(AggregationRuleType.function.value());
        metricsAggregationRuleDO3.setIsConditioned(1);
        metricsAggregationRuleDO3.setConditionalFieldPrefix("prefix");
        metricsAggregationRuleDO3.setVersion(1);
        metricsAggregationRuleDOList.add(metricsAggregationRuleDO3);


        // histogram
        MetricsAggregationRuleDO metricsAggregationRuleDO4 = new MetricsAggregationRuleDO();
        metricsAggregationRuleDO4.setId("sjj-mar4");
        metricsAggregationRuleDO4.setAggId("sjj-maId1");
        metricsAggregationRuleDO4.setAggField("timeMills");
        metricsAggregationRuleDO4.setAggTypes("");
        metricsAggregationRuleDO4.setFilterCondition("a>b");
        metricsAggregationRuleDO4.setAggRuleType(AggregationRuleType.histogram.value());
        metricsAggregationRuleDO4.setIsConditioned(0);
        metricsAggregationRuleDO4.setConditionalFieldPrefix("");
        metricsAggregationRuleDO4.setVersion(1);
        metricsAggregationRuleDOList.add(metricsAggregationRuleDO4);

        // percentile
        MetricsAggregationRuleDO metricsAggregationRuleDO5 = new MetricsAggregationRuleDO();
        metricsAggregationRuleDO5.setId("sjj-mar5");
        metricsAggregationRuleDO5.setAggId("sjj-maId1");
        metricsAggregationRuleDO5.setAggField("timeMills");
        metricsAggregationRuleDO5.setAggTypes("");
        metricsAggregationRuleDO5.setFilterCondition("a>b");
        metricsAggregationRuleDO5.setAggRuleType(AggregationRuleType.percentile.value());
        metricsAggregationRuleDO5.setIsConditioned(0);
        metricsAggregationRuleDO5.setConditionalFieldPrefix("");
        metricsAggregationRuleDO5.setVersion(1);
        metricsAggregationRuleDOList.add(metricsAggregationRuleDO5);


        List<AggregationFunctionItemDO> aggregationFunctionItemDOList = new ArrayList<>();
        AggregationFunctionItemDO aggregationFunctionItemDO1 = new AggregationFunctionItemDO();
        aggregationFunctionItemDO1.setId("sjj-afi1");
        aggregationFunctionItemDO1.setMetricsAggregationRuleId("sjj-mar2");
        aggregationFunctionItemDO1.setAggType(AggregationType.AVG.name());
        aggregationFunctionItemDO1.setTargetFieldId("sjj-metricsFieldId1");
        aggregationFunctionItemDOList.add(aggregationFunctionItemDO1);

        AggregationFunctionItemDO aggregationFunctionItemDO2 = new AggregationFunctionItemDO();
        aggregationFunctionItemDO2.setId("sjj-afi2");
        aggregationFunctionItemDO2.setMetricsAggregationRuleId("sjj-mar3");
        aggregationFunctionItemDO2.setAggType(AggregationType.AVG.name());
        aggregationFunctionItemDO2.setTargetFieldId("sjj-metricsFieldId1");
        aggregationFunctionItemDOList.add(aggregationFunctionItemDO2);

        List<AggregationHistogramRangeItemDO> aggregationHistogramRangeItemDOList = new ArrayList<>();
        AggregationHistogramRangeItemDO aggregationHistogramRangeItemDO = new AggregationHistogramRangeItemDO();
        aggregationHistogramRangeItemDO.setId("sjj-afi3");
        aggregationHistogramRangeItemDO.setMetricsAggregationRuleId("sjj-mar4");
        aggregationHistogramRangeItemDO.setLowerLimit(30.2);
        aggregationHistogramRangeItemDO.setUpperLimit(45.2);
        aggregationHistogramRangeItemDO.setLowerIntervalNotation(0);
        aggregationHistogramRangeItemDO.setUpperIntervalNotation(1);
        aggregationHistogramRangeItemDO.setTargetFieldId("sjj-metricsFieldId1");
        aggregationHistogramRangeItemDOList.add(aggregationHistogramRangeItemDO);

        List<AggregationPercentileItemDO> aggregationPercentileItemDOList = new ArrayList<>();
        AggregationPercentileItemDO aggregationPercentileItemDO = new AggregationPercentileItemDO();
        aggregationPercentileItemDO.setId("sjj-afi4");
        aggregationPercentileItemDO.setMetricsAggregationRuleId("sjj-mar5");
        aggregationPercentileItemDO.setPercentileValue(40);
        aggregationPercentileItemDO.setTargetFieldId("sjj-metricsFieldId1");
        aggregationPercentileItemDOList.add(aggregationPercentileItemDO);

        List<AggregationCustomFieldRuleDO> aggregationCustomFieldRuleDOList = new ArrayList<>();
        metricsHandler.addAggregatedMetrics(metricsDO, collectorMetricsDO, metricsFieldDOList, metricsAggregationDO, metricsAggregationRuleDOList, aggregationFunctionItemDOList, aggregationHistogramRangeItemDOList, aggregationPercentileItemDOList, aggregationCustomFieldRuleDOList);
    }

    @Test
    public void batchInsertMetricsFields(){
        List<MetricsFieldDO> metricsFieldDOList = new ArrayList<>();
        MetricsFieldDO metricsFieldDO1 = new MetricsFieldDO();
        metricsFieldDO1.setMetricsId("123");
        metricsFieldDO1.setFieldType(1);
        metricsFieldDO1.setFieldName("sjj-1");
        metricsFieldDO1.setId(IdUtils.generateId());
        metricsFieldDOList.add(metricsFieldDO1);
        MetricsFieldDO metricsFieldDO2 = new MetricsFieldDO();
        metricsFieldDO2.setMetricsId("123");
        metricsFieldDO2.setFieldType(2);
        metricsFieldDO2.setFieldName("sjj-2");
        metricsFieldDO2.setId(IdUtils.generateId());
        metricsFieldDOList.add(metricsFieldDO2);
        metricsFieldHandler.batchInsertMetricsField(metricsFieldDOList);
    }
}
