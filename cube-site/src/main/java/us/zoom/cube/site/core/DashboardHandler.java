package us.zoom.cube.site.core;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import us.zoom.cube.site.infra.utils.IdUtils;
import us.zoom.infra.dao.model.DashboardDO;
import us.zoom.infra.dao.service.DashboardDAO;

/**
 *
 */
@Component
public class DashboardHandler {

    @Autowired
    private DashboardDAO dashboardDAO;

    public String getDashIdByType(String tenantId,Integer dashboardType) {
        return  dashboardDAO.getDashIdByType(tenantId,dashboardType);
    }

    public String addDash(String tenantId,Integer dashboardType,String title){
        String dashId=  dashboardDAO.getDashIdByType(tenantId,dashboardType);
        if(StringUtils.isNotBlank(dashId)){
            return dashId;
        }
        DashboardDO dashboardDO=new DashboardDO();
        dashboardDO.setType(dashboardType);
        dashboardDO.setTitle(title);
        dashboardDO.setTenantId(tenantId);
        dashboardDO.setId(IdUtils.generateId());
        dashboardDAO.add(dashboardDO);
        return dashboardDO.getId();
    }


//    public DashboardDO getByViewComponent(String viewComponentId) {
//        return dashboardDAO.getByViewComponent(viewComponentId);
//    }
}
