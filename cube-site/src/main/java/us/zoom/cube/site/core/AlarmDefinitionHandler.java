package us.zoom.cube.site.core;

import com.zoom.op.monitor.dao.alarm.AlarmDefinitionDao;
import com.zoom.op.monitor.domain.IdAndName;
import com.zoom.op.monitor.domain.alarm.AlarmDefinition;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.site.biz.alarm.AlarmDefinitionService;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Component
public class AlarmDefinitionHandler {

    @Autowired
    private AlarmDefinitionDao alarmDefinitionDao;

    @Autowired
    private AlarmDefinitionService alarmDefinitionService;

    public List<AlarmDefinition> findByMetricId(String metricId){
        return alarmDefinitionDao.findByMetricId(metricId);
    }

    public Optional<AlarmDefinition> findByNameAndTenantId(String name, String tenantId){
        return alarmDefinitionDao.findByNameAndTenantId(name, tenantId);
    }

    public void batchInsert(List<AlarmDefinition> alarmDefinitionList){
        alarmDefinitionDao.saveAll(alarmDefinitionList);
    }

    public List<String> listAllAlarmNames(String tenantId) {
        return alarmDefinitionDao.listAllAlarmNames(tenantId);
    }

    public List<IdAndName> listAllAlarmIdAndNames(String tenantId) {
        return alarmDefinitionDao.findByTenantId(tenantId);
    }

    public List<IdAndName> findByIdList(List<String> idList){
        if(CollectionUtils.isEmpty(idList)){
            return new ArrayList<>();
        }
        return alarmDefinitionDao.findByIdList(idList);
    }

    public List<IdAndName> getAlarmNameByMetricsIdList(List<String> metricsIdList) {
        return alarmDefinitionDao.getAlarmNameByMetricsIdList(metricsIdList);
    }

}
