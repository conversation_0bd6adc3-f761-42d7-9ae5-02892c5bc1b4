package us.zoom.cube.site.out;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import us.zoom.cube.site.BaseSpringTest;
import us.zoom.cube.site.biz.clickhouse.ClickhouseClusterService;

import java.util.Map;

/**
 * @Description:
 * @author: <PERSON>
 * @date: 2024/02/27 13:24
 */
public class TenantServiceTest extends BaseSpringTest {

    @Autowired
    private ClickhouseClusterService clickhouseClusterService;

//    @Test
    public void test() {
        clickhouseClusterService.getAllTenant2ClusterRelation();
    }

}
