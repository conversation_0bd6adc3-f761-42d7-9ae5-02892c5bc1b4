package us.zoom.cube.site.biz;

import org.apache.commons.io.FileUtils;

import java.io.File;
import java.io.IOException;
import java.util.List;

public class PIne {


    public static void main(String []sdf){
        try {
            List<String> sqls =  FileUtils.readLines(new File("D:\\\\zoom\\\\op_back.txt"),"UTF-8");
            System.out.println("delete from role_menu_rela where role_name='opOwner';");
            sqls.forEach(sql->{
                if(sql.contains("'opOwner'")){
                    System.out.println(sql);
                }
            });
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}

