package us.zoom.cube.site.lib.output.alarm;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: <PERSON><PERSON>g
 * @date: 2022/9/28 13:24
 * @desc: most column same as AlarmBoardSettingItem
 */
@Data
public class AlarmBoardFilterSummary {

    String id;

    String name;

    String tagName;

    Integer type;

    Integer subType;

    String configValue;

    Integer renderOption;

    String optionsOrder;

    Integer order;

    List<AlarmBoardFilterSummaryData> data = new ArrayList<>();

}
