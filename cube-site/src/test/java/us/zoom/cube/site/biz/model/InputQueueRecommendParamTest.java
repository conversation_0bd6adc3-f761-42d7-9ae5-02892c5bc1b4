package us.zoom.cube.site.biz.model;

import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.junit.jupiter.api.Test;
import us.zoom.cube.lib.utils.JsonUtils;

import java.util.Map;

/**
 * @Author: <EMAIL>
 * @ModuleOwner: <EMAIL>
 * @Date: 08/12/2023 17:26
 * @Description:
 */
public class InputQueueRecommendParamTest {
    @Test
    public void test() {
        InputQueueRecommendParam param = new InputQueueRecommendParam();

        Map<String, String> hubUnitTagMap = Maps.newHashMap();
        hubUnitTagMap.put("eu02", "1");
        hubUnitTagMap.put("in", "2");
        hubUnitTagMap.put("eu", "3");
        hubUnitTagMap.put("ca", "4");
        hubUnitTagMap.put("au", "5");
        hubUnitTagMap.put("sa", "6");
        hubUnitTagMap.put("sg", "7");
        hubUnitTagMap.put("ch", "8");
        hubUnitTagMap.put("jp", "9");
        hubUnitTagMap.put("us", "10");
        hubUnitTagMap.put("default", "0");
        param.setAlarmQueue("alarm123");
        param.setFlinkQueue("flink123");
        param.setHubUnitTagMap(hubUnitTagMap);
        param.setWhiteEndWith(Sets.newHashSet("public", "infra"));
        System.out.println(JsonUtils.toJsonString(param));
    }
}
