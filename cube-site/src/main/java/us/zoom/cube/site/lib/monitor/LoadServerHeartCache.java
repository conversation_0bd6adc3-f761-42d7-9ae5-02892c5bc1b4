package us.zoom.cube.site.lib.monitor;

import us.zoom.cube.sdk.annotation.CubeMonitorLog;
import us.zoom.cube.sdk.annotation.Field;
import us.zoom.cube.sdk.annotation.Tag;

@CubeMonitorLog(measure = "loadServerHeartCache")
public class LoadServerHeartCache {
    @Tag
    private String host;
    @Tag
    private String success;
    @Field
    private long cost;
    @Field
    private String error;

    public String getHost() {
        return host;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public String getSuccess() {
        return success;
    }

    public void setSuccess(String success) {
        this.success = success;
    }

    public long getCost() {
        return cost;
    }

    public void setCost(long cost) {
        this.cost = cost;
    }

    public String getError() {
        return error;
    }

    public void setError(String error) {
        this.error = error;
    }

    public LoadServerHeartCache(String host, String success, long cost, String error) {
        this.host = host;
        this.success = success;
        this.cost = cost;
        this.error = error;
    }

    public LoadServerHeartCache() {
    }
}
