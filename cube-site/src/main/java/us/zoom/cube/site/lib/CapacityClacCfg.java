package us.zoom.cube.site.lib;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

/**
 * <AUTHOR>
 * @date 2022-07-06 13:12
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class CapacityClacCfg {
    Integer averageTime;
    double oneA;
    double twoA;
    double threeA;
    String calcOpen;
    String dataBaseName;
    String tableName;


    public Integer getAverageTime() {
        return averageTime;
    }

    public void setAverageTime(Integer averageTime) {
        this.averageTime = averageTime;
    }

    public double getOneA() {
        return oneA;
    }

    public void setOneA(double oneA) {
        this.oneA = oneA;
    }

    public double getTwoA() {
        return twoA;
    }

    public void setTwoA(double twoA) {
        this.twoA = twoA;
    }

    public double getThreeA() {
        return threeA;
    }

    public void setThreeA(double threeA) {
        this.threeA = threeA;
    }

    public String getCalcOpen() {
        return calcOpen;
    }

    public void setCalcOpen(String calcOpen) {
        this.calcOpen = calcOpen;
    }
}
