package us.zoom.cube.site.lib.output.template;

import lombok.Data;

import java.util.List;

/**
 * @Author: <PERSON>
 * @Date:01/03/2023 14:35
 * @Description:
 */
@Data
public class ListUserServiceOutput {

    private String serviceName;

    private String serviceId;

    private List<TemplateOutput> bindedTemplates;

    @Data
    public static class TemplateOutput{

        private String name;

        private String id;
    }

}
