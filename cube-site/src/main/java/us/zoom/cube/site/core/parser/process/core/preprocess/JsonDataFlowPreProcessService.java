package us.zoom.cube.site.core.parser.process.core.preprocess;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import us.zoom.cube.site.core.parser.process.core.common.enums.MonitoringLogType;
import us.zoom.cube.site.core.parser.process.core.monitoring.MonitoringLogUtils;
import us.zoom.cube.site.infra.enums.WebCodeEnum;
import us.zoom.cube.site.lib.SiteException;
import us.zoom.mq.common.entity.TaskEntity;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @Author: <EMAIL>
 * @ModuleOwner: <EMAIL>
 * @Date: 12/22/2022 17:06
 * @Description:
 */
public class JsonDataFlowPreProcessService implements DataFlowPreProcessService {
    private final static ObjectMapper mapper = new ObjectMapper();
    private MonitoringLogUtils monitoringLogUtils = new MonitoringLogUtils();

    public JsonDataFlowPreProcessService() {

    }

    @Override
    public List<Map<String, Object>> handle(String message,TaskEntity<String> taskEntity) {
        try {
            JsonNode jsonNode = mapper.readValue(message, JsonNode.class);
            if (jsonNode instanceof ArrayNode) {
                return mapper.convertValue(jsonNode, listMapTypeReference);
            } else {
                List<Map<String, Object>> toMapList = new ArrayList<>(1);
                toMapList.add(mapper.convertValue(jsonNode, mapTypeReference));
                return toMapList;
            }
        } catch (Exception e) {
            monitoringLogUtils.printErrorLog(null, MonitoringLogType.jsonPreFail, e);
            throw new SiteException(WebCodeEnum.InnerError.getCode(),e.getMessage());
        }
    }
}
