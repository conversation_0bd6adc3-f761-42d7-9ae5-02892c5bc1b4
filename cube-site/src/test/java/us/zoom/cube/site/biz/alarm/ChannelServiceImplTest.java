package us.zoom.cube.site.biz.alarm;

import com.zoom.op.monitor.dao.alarm.AlarmDefinitionDao;
import com.zoom.op.monitor.dao.alarm.ChannelDao;
import com.zoom.op.monitor.domain.alarm.AlarmDefinition;
import com.zoom.op.monitor.domain.alarm.Channel;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Component;
import us.zoom.cube.site.CubeSiteApplication;
import us.zoom.cube.site.api.web.StatusEnum;
import us.zoom.cube.site.biz.AuthService;
import us.zoom.cube.site.lib.ResponseObject;
import us.zoom.infra.dao.model.AlarmMentionGroupDO;
import us.zoom.infra.influx.model.alarm.AlarmMatchRecord;
import us.zoom.infra.notification.channel.ChannelEngine;
import us.zoom.infra.notification.channel.ChannelSendModel;

import java.util.*;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.*;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;


@SpringBootTest(classes = CubeSiteApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class ChannelServiceImplTest {

    @Autowired
    private ChannelService test;

    @MockBean
    private ChannelDao channelDao;

    @MockBean
    private AlarmDefinitionDao alarmDefinitionDao;

    @SuppressWarnings("unused")
    @MockBean
    private AuthService authService;

    @Test
    public void pageBy() {

        Channel channel = new Channel("Channel", "IM");

        Pageable pageable = PageRequest.of(1, 10, Sort.by(Sort.Direction.ASC, "name"));

        Page<Channel> channelPage = Page.empty();

        when(channelDao.findAll(any(), any(Pageable.class)))
                .thenReturn(channelPage);

        ResponseObject<Page<Channel>> result = test.pageBy(channel, pageable);

        assertEquals(channelPage, result.getData());
    }

    @SuppressWarnings("unused")
    @Component
    public static class MockChannelEngine implements ChannelEngine {

        @Override
        public String getName() {
            return "mock-channel-engine";
        }

        @Override
        public List<Parameter> getParameters() {
            return new ArrayList<>();
        }

        @Override
        public void send(ChannelSendModel channelSendModel) {
        }
    }

    @Test
    public void listEngines() {

        ResponseObject<List<ChannelEngine>> response = test.listEngines();

        assertThat(
                response.getData(),
                hasItem(
                        hasProperty("name",
                                equalTo("mock-channel-engine"))));
    }

    @Test
    public void list() {

        List<Channel> mockData = Arrays.asList(
                new Channel("mock-channel1", "mock-engine1"),
                new Channel("mock-channel2", "mock-engine2"),
                new Channel("mock-channel3", "mock-engine3")
        );

        when(channelDao.findByTenantIdOrderByName("mock-tenant-id")).thenReturn(mockData);

        ResponseObject<List<Channel>> response = test.listBy("mock-tenant-id");

        assertEquals(mockData, response.getData());
    }

    @Test
    public void save() {

        Channel mockChannel = new Channel("mock-channel", "mock-engine");

        test.save(mockChannel);

        verify(channelDao).save(mockChannel);
    }

    @Test
    public void save_nameUsed_failed() {

        Channel mockChannel = new Channel("mock-channel", "mock-engine") {{
            setTenantId("mock-tenant-id");
        }};

        Optional<Channel> exists = Optional.of(
                new Channel("mock-channel", "mock-engine2"));

        when(channelDao.findByNameAndTenantId("mock-channel", "mock-tenant-id"))
                .thenReturn(exists);

        ResponseObject<Channel> response = test.save(mockChannel);

        assertEquals(StatusEnum.FAIL.getStatus(), response.getStatus());
    }

    @Test
    public void update() {

        Channel mockChannel = new Channel("mock-channel", "mock-engine") {{
            setId("100L");
        }};

        test.update(mockChannel);

        verify(channelDao).save(mockChannel);
    }

    @Test
    public void update_nameUsed_fail() {

        Channel mockChannel = new Channel("mock-channel", "mock-engine") {{
            setId("100L");
            setTenantId("mock-tenant-id");
        }};

        Optional<Channel> exists = Optional.of(
                new Channel("mock-channel", "mock-engine2") {{
                    setId("101L");
                }}
        );

        when(channelDao.findByNameAndTenantId("mock-channel", "mock-tenant-id"))
                .thenReturn(exists);

        ResponseObject<Channel> response = test.update(mockChannel);

        assertEquals(StatusEnum.FAIL.getStatus(), response.getStatus());
    }

    @Test
    public void delete() {

        Optional<Channel> mock = Optional.of(
                new Channel("mock-channel", "mock-engine") {{
                    setId("100L");
                }}
        );

        when(channelDao.findById("100L")).thenReturn(mock);

        test.delete("100L");

        verify(channelDao).delete(mock.get());
    }

    @Test
    public void delete_usedByAlarm_failed() {

        Optional<Channel> mock = Optional.of(
                new Channel("mock-channel", "mock-engine") {{
                    setId("100L");
                }}
        );

        when(channelDao.findById("100L")).thenReturn(mock);

        when(alarmDefinitionDao.count(ArgumentMatchers.<Specification<AlarmDefinition>>any())).thenReturn(10L);

        ResponseObject<Channel> response = test.delete("100L");

        assertEquals(StatusEnum.FAIL.getStatus(), response.getStatus());
    }

    @Test
    public void getById() {

        Optional<Channel> mock = Optional.of(
                new Channel("mock-channel", "mock-engine") {{
                    setId("100L");
                }}
        );

        when(channelDao.findById("100L")).thenReturn(mock);

        ResponseObject<Channel> response = test.getById("100L");

        assertEquals(mock.get(), response.getData());
    }

    @Test
    public void getById_notFound_failed() {

        when(channelDao.findById("100L")).thenReturn(Optional.empty());

        ResponseObject<Channel> response = test.getById("100L");

        assertEquals(StatusEnum.FAIL.getStatus(), response.getStatus());
    }

}