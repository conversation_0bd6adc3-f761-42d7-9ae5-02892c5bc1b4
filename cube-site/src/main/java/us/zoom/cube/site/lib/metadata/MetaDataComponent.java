package us.zoom.cube.site.lib.metadata;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * Author: charles.hu
 * Date: 2024/12/10
 * Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MetaDataComponent {
    private String id;
    private String createdAt;
    private String updatedAt;
    private String key;
    private String parentKey;
    private String name;
    private String rm;
    private String serviceOwner;
    private List<String> operationOwner;
    private String active;
    private String type;
    private List<String> securityOwner;
    private List<String> securityChampion;
    private String jiraId;
    private String team;
    private String description;
    private String status;
    private String parentName;
    private String applicationKey;
    private String applicationName;
    private String productKey;
    private String productName;
    private String serviceGroupKey;
    private String serviceGroupName;
    private String maturityRoutingKey;
    private Map<String, String> attributes;

    public static final String FIELD_KEY = "key";
    public static final String FIELD_NAME = "name";
    public static final String FIELD_STATUS = "status";
    public static final String FIELD_TYPE_NAME = "typeName";
    public static final String FIELD_FIELDS = "fields";
    public static final String FIELD_CONDITIONS = "conditions";
    public static final String FIELD_PAGE_NUMBER = "pageNumber";
    public static final String FIELD_PAGE_SIZE = "pageSize";
    public static final String FIELD_PRODUCT_KEY = "productKey";
    public static final String FIELD_SERVICE_GROUP_KEY = "serviceGroupKey";
    public static final String FIELD_PARENT_KEY = "parentKey";
    public static final String FIELD_PARENT_NAME = "parentName";
    public static final int DEFAULT_PAGE_NUMBER = 1;
    public static final int DEFAULT_PAGE_SIZE = 3000;
}
