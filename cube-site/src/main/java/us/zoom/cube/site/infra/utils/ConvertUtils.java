package us.zoom.cube.site.infra.utils;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.zoom.op.monitor.domain.alarm.AlarmDefinition;
import com.zoom.op.monitor.domain.alarm.AlarmRule;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.BeanUtils;
import org.springframework.cglib.beans.BeanMap;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.site.infra.annotation.BeanToMapIgnore;

import java.io.IOException;
import java.lang.invoke.MethodHandle;
import java.lang.invoke.MethodHandles;
import java.lang.invoke.MethodType;
import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class ConvertUtils {

    private static final ObjectMapper dbMapper = new ObjectMapper();
    private static final ObjectMapper beanMapper = new ObjectMapper();

    private static final Map<String, List<Pair<String, MethodHandle>>> OBJECT_TO_METHOD_HANDLES = Maps.newConcurrentMap();

    static {
        dbMapper.setPropertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE);
        dbMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        SimpleModule module = new SimpleModule();
        module.addDeserializer(Date.class, new DateDeserializer());
        module.addDeserializer(Boolean.class, new FlexibleBooleanDeserializer());
        dbMapper.registerModule(module);
        beanMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

    }

    /**
     * @param input
     * @param clzz
     * @param <E>
     * @param <T>
     * @return
     */
    public static <E, T> List<T> convertList2List(List<E> input, Class<T> clzz) {
        List<T> output = new ArrayList<>();
        if (!CollectionUtils.isEmpty(input)) {
            for (E source : input) {
                //T target = BeanUtils.instantiate(clzz);
                T target = BeanUtils.instantiateClass(clzz);
                BeanUtils.copyProperties(source, target);
                output.add(target);
            }
        }
        return output;
    }


    /**
     * apavabean
     *
     * @param map
     * @param bean
     * @return
     */
    public static <T> T mapToBean(Map<String, Object> map, T bean) {
        BeanMap beanMap = BeanMap.create(bean);
        beanMap.putAll(map);
        return bean;
    }

    /**
     * ist<Map<String,Object>>ist<T>
     *
     * @param maps
     * @param clazz
     * @return
     * @throws InstantiationException
     * @throws IllegalAccessException
     */
    public static <T> List<T> mapsToObjects(List<Map<String, Object>> maps, Class<T> clazz) throws InstantiationException, IllegalAccessException {
        List<T> list = new ArrayList<>();
        if (maps != null && maps.size() > 0) {
            Map<String, Object> map = null;
            T bean = null;
            for (int i = 0, size = maps.size(); i < size; i++) {
                map = maps.get(i);
                bean = clazz.newInstance();
                mapToBean(map, bean);
                list.add(bean);
            }
        }
        return list;
    }


    public static Map<String, Object> beanToMapWithReflect(Object object) {
        Class<?> clazz = object.getClass();
        List<Pair<String, MethodHandle>> methodHandles = OBJECT_TO_METHOD_HANDLES.get(clazz.getName());
        if (methodHandles == null) {
            methodHandles = OBJECT_TO_METHOD_HANDLES.computeIfAbsent(clazz.getName(), k -> {
                Class<?> current = clazz;
                List<Pair<String, MethodHandle>> handles = Lists.newArrayList();
                while (current != null && current != Object.class) {
                    Field[] declaredFields = current.getDeclaredFields();
                    for (Field field : declaredFields) {
                        if (field.isAnnotationPresent(BeanToMapIgnore.class) || Modifier.isStatic(field.getModifiers())) {
                            continue;
                        }
                        String fieldName = field.getName();
                        Class<?> fieldType = field.getType();
                        String methodName;
                        if (fieldType == boolean.class) {
                            String suffix;
                            if (fieldName.startsWith("is") && fieldName.length() > 2) {
                                suffix = capitalize(fieldName.substring(2));
                            } else {
                                suffix = capitalize(fieldName);
                            }
                            methodName = "is" + suffix; // isXxx for primitive boolean
                        } else {
                            methodName = "get" + capitalize(fieldName); // getXxx for everything else
                        }
                        MethodHandle handle = findAccessorOrNull(current, methodName, fieldType);
                        if (handle != null) {
                            handles.add(Pair.of(fieldName, handle));
                        }
                    }
                    current = current.getSuperclass();
                }
                return handles;
            });
        }
        Map<String, Object> map = Maps.newHashMap();
        for (Pair<String, MethodHandle> pair : methodHandles) {
            Object value;
            try {
                value = pair.getValue().invoke(object);
            } catch (Throwable e) {
                throw new RuntimeException(e);
            }
            map.put(pair.getKey(), value);
        }
        return map;
    }

    private static String capitalize(String s) {
        if (s == null || s.isEmpty()) return s;
        return Character.toUpperCase(s.charAt(0)) + s.substring(1);
    }

    private static MethodHandle findAccessorOrNull(Class<?> clz, String methodName, Class<?> rtClazz) {
        try {
            return MethodHandles.publicLookup()
                    .findVirtual(clz, methodName, MethodType.methodType(rtClazz));
        } catch (Throwable t) {
            log.error("Can't find method {} in class {}, rtClazz is {}", methodName, clz.getName(), rtClazz.getName(), t);
            return null;
        }
    }

    public static <T> T parseFromDbData(Object data, Class<T> clazz) {
        return dbMapper.convertValue(data, clazz);
    }

    public static <T> T beanToBean(Object map, Class<T> clazz) {
        return beanMapper.convertValue(map, clazz);
    }

    public static <T> T mapToBean(Map<String, Object> map, Class<T> clazz) {
        return beanMapper.convertValue(map, clazz);
    }


    private static class DateDeserializer extends JsonDeserializer<Date> {

        private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        @Override
        public Date deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
            String text = p.getText();
            LocalDateTime dt = LocalDateTime.parse(text, formatter);
            return new Date(dt.toInstant(ZoneOffset.UTC).toEpochMilli());
        }
    }

    private static class FlexibleBooleanDeserializer extends JsonDeserializer<Boolean> {
        @Override
        public Boolean deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
            String value = p.getText().trim();
            return "1".equals(value) || "true".equalsIgnoreCase(value);
        }
    }



}
