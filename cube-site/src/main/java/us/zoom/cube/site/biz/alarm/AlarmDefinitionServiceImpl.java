package us.zoom.cube.site.biz.alarm;

import com.zoom.op.monitor.dao.alarm.AlarmDefinitionDao;
import com.zoom.op.monitor.domain.DerivedMetric;
import com.zoom.op.monitor.domain.IdAndName;
import com.zoom.op.monitor.domain.TagInputDo;
import com.zoom.op.monitor.domain.alarm.*;
import jakarta.persistence.criteria.Join;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import jakarta.persistence.criteria.Subquery;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.*;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.lib.common.CubeConstants;
import us.zoom.cube.lib.integrations.MetricsFieldTypeEnum;
import us.zoom.cube.lib.utils.JsonUtils;
import us.zoom.cube.lib.utils.MathUtils;
import us.zoom.cube.site.api.web.StatusEnum;
import us.zoom.cube.site.biz.*;
import us.zoom.cube.site.biz.alarm.group.AlarmGroupUtilService;
import us.zoom.cube.site.biz.alarm.silence.AlarmSilenceUtilService;
import us.zoom.cube.site.biz.syspara.AlarmParaService;
import us.zoom.cube.site.core.*;
import us.zoom.cube.site.core.alarm.silence.AlarmSilenceHandler;
import us.zoom.cube.site.core.config.ClickhouseHandlerFactory;
import us.zoom.cube.site.core.config.CubeServerCacheLoader;
import us.zoom.cube.site.core.model.ad.AdCfgDataOut;
import us.zoom.cube.site.core.model.ad.AdCfgData;
import us.zoom.cube.site.core.model.ad.AdTagCfgData;
import us.zoom.cube.site.core.model.common.AlarmConditionThresholdRecModel;
import us.zoom.cube.site.biz.ai.AiCfgService;
import us.zoom.cube.site.core.tag.ResourceTypeConstant;
import us.zoom.cube.site.core.tag.TagConvertUtil;
import us.zoom.cube.site.core.tag.service.ResourceTagService;
import us.zoom.cube.site.lib.IdListPara;
import us.zoom.cube.site.lib.IdPara;
import us.zoom.cube.site.lib.ResponseObject;
import us.zoom.cube.site.lib.dto.TplAlarmOverrideDTO;
import us.zoom.cube.site.lib.dto.TplAlarmSynTenantIdDTO;
import us.zoom.cube.site.lib.input.DerivedMetricInput;
import us.zoom.cube.site.lib.input.alarm.definition.AlarmApproveStatusInput;
import us.zoom.cube.site.lib.monitor.SitePerformanceMonitorLogEntity;
import us.zoom.cube.site.lib.output.alarm.AlarmDefinitionOutput;
import us.zoom.cube.site.lib.output.config.metrics.FieldOrTag;
import us.zoom.cube.site.lib.output.tag.TagInfoOut;
import us.zoom.infra.clickhouse.ClickhouseSqlUtil;
import us.zoom.infra.dao.model.*;
import us.zoom.infra.dao.service.MetricsDAO;
import us.zoom.infra.dao.service.MetricsFieldDAO;
import us.zoom.infra.dao.service.TenantDAO;
import us.zoom.infra.enums.AlarmOperatorEnum;
import us.zoom.infra.enums.AlarmSilenceStatusEnum;
import us.zoom.infra.model.alarm.AlarmRuleSingleContent;
import us.zoom.infra.thread.ThreadLocalStore;
import us.zoom.infra.utils.*;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static org.springframework.data.domain.ExampleMatcher.GenericPropertyMatchers.exact;
import static us.zoom.cube.site.biz.MetricsService.DEFAULT_PERIOD_UNIT;
import static us.zoom.cube.site.biz.alarm.AlarmDefinitionCheckServiceImpl.HIT_THRESHOLDS;
import static us.zoom.cube.site.biz.syspara.AlarmParaService.NEED_REC_FIELD_THRESHOLD;
import static us.zoom.infra.utils.AlarmConstants.IS_GREATER;
import static us.zoom.infra.utils.AlarmConstants.IS_GREATER_EQUAL;
import static us.zoom.infra.utils.AlarmSilenceConstants.ALARM_SILENCE;
import us.zoom.cube.site.lib.output.config.metrics.CategoryType;

/**
 * <AUTHOR>
 * @date: 2023/06/06
 */
@Service
@Slf4j
public class AlarmDefinitionServiceImpl implements AlarmDefinitionService {

    private AlarmDefinitionDao alarmDefinitionDao;

    private MetricsDAO metricsDAO;

    private MetricsFieldDAO metricsFieldDAO;

    private TenantDAO tenantDAO;

    private TemplateHandler templateHandler;

    private ChannelService channelService;

    private MetricsHandler metricsHandler;

    private ChannelHandler channelHandler;

    @Autowired
    private AlarmParaService alarmParaService;

    @Autowired
    private SysParaService sysParaService;

    @Autowired
    private UserHandler userHandler;

    @Autowired
    private AlarmSilenceHandler alarmSilenceHandler;

    @Autowired
    private AlarmSilenceUtilService alarmSilenceUtilService;

    @Autowired
    private AlarmGroupUtilService alarmGroupUtilService;

    @Autowired
    private TenantHandler tenantHandler;

    @Autowired
    private CubeServerCacheLoader cubeServerCacheLoader;

    @Autowired
    private ResourceTagService resourceTagService;

    @Autowired
    private MetricsService metricsService;

    @Autowired
    private AdCfgHandler adCfgHandler;

    @Autowired
    private AlarmService alarmService;

    @Autowired
    private ClickhouseHandlerFactory clickhouseHandlerFactory;

    @Autowired
    private SubEnvironmentHandler subEnvironmentHandler;

    @Autowired
    private DerivedMetricService derivedMetricHandler;
    
    @Autowired
    private AiCfgService aiCfgService;

    @Autowired
    public void setMetricsHandler(MetricsHandler metricsHandler) {
        this.metricsHandler = metricsHandler;
    }

    @Autowired
    public void setChannelService(ChannelService channelService) {
        this.channelService = channelService;
    }

    @Autowired
    public void setChannelHandler(ChannelHandler channelHandler) {
        this.channelHandler = channelHandler;
    }

    @Autowired
    public void setTemplateHandler(TemplateHandler templateHandler) {
        this.templateHandler = templateHandler;
    }

    @Autowired
    public void setAlarmDefinitionDao(AlarmDefinitionDao alarmDefinitionDao) {
        this.alarmDefinitionDao = alarmDefinitionDao;
    }

    @Autowired
    public void setMetricsDAO(MetricsDAO metricsDAO) {
        this.metricsDAO = metricsDAO;
    }

    @Autowired
    public void setMetricsFieldDAO(MetricsFieldDAO metricsFieldDAO) {
        this.metricsFieldDAO = metricsFieldDAO;
    }

    @Autowired
    public void setTenantDAO(TenantDAO tenantDAO) {
        this.tenantDAO = tenantDAO;
    }

    @Autowired
    private AuthService authService;

    private final Logger monitorLog = LoggerFactory.getLogger("Monitor");



    public static final List<FieldOrTag> EXTRA_FIELD = new ArrayList<>();

    public static final List<String> SYSTEM_STRING_VAR = new ArrayList<>(Arrays.asList(
            CubeConstants.CUBE_ALARM_IP,
            CubeConstants.CUBE_ALARM_ENV,
            CubeConstants.CUBE_ALARM_UNIT_TAG,
            CubeConstants.CUBE_ALARM_SERVICE_NAME,
            CubeConstants.ALARM_TIME,
            CubeConstants.ALARM_NAME,
            CubeConstants.ALARM_METRIC_NAME,
            CubeConstants.ALARM_METRICS_NAME,
            CubeConstants.ALARM_LEVEL,
            CubeConstants.ALARM_DETAIL_URL
    ));

    static {
        SYSTEM_STRING_VAR.forEach(name -> {
            FieldOrTag var = FieldOrTag.newField();
            var.setFieldOrTagName(name);
            var.setValueType(MetricsFieldTypeEnum.string.name());
            var.setIsExtra(Boolean.TRUE);
            var.setCategory(CategoryType.SYSTEM_VAR);
            EXTRA_FIELD.add(var);
        });
    }



    @Override
    public ResponseObject<List<String>> listAllName(String tenantId) {
        authService.checkAuth(ThreadLocalStore.getUserInfoLocal(), ThreadLocalStore.getTenantInfoLocal(), ThreadLocalStore.getApiPath());

        return ResponseObject.success(

                alarmDefinitionDao

                        .findByTenantId(tenantId).stream()

                        .map(IdAndName::getName)

                        .collect(Collectors.toList())
        );
    }


    @Override
    public ResponseObject<Boolean> existsWithName(String tenantId, String alarmName) {
        authService.checkAuth(ThreadLocalStore.getUserInfoLocal(), ThreadLocalStore.getTenantInfoLocal(), ThreadLocalStore.getApiPath());

        AlarmDefinition alarmDefinition = new AlarmDefinition();
        alarmDefinition.setTenantId(tenantId);
        alarmDefinition.setName(alarmName);
        alarmDefinition.setEnabled(null);

        Example<AlarmDefinition> example =
                Example.of(
                        alarmDefinition,
                        ExampleMatcher.matching()
                                .withMatcher("name", exact())
                                .withMatcher("tenantId", exact()));

        return ResponseObject.success(alarmDefinitionDao.exists(example));
    }


    @Override
    public ResponseObject<Page<AlarmDefinition>> pageBy(
            String name, AlarmLevel level, String tenantId, String channelId, String metricId, String enabled, Pageable pageable) {
        authService.checkAuth(ThreadLocalStore.getUserInfoLocal(), ThreadLocalStore.getTenantInfoLocal(), ThreadLocalStore.getApiPath());

        Page<AlarmDefinition> page =

                alarmDefinitionDao.findAll((Specification<AlarmDefinition>) (root, cq, cb) -> {

                    Optional<Predicate> namePredicate =
                            Optional.ofNullable(name).map(n -> cb.like(root.get("name"), "%" + n + "%"));

                    Optional<Predicate> tenantPredicate =
                            Optional.of(tenantId).map(n -> cb.equal(root.get("tenantId"), tenantId));

                    Optional<Predicate> metricPredicate =
                            Optional.ofNullable(metricId).map(n -> cb.equal(root.get("metricId"), metricId));

                    Optional<Predicate> enablePredicate =
                            Optional.ofNullable(enabled).map(e -> cb.equal(root.get("enabled"), Boolean.valueOf(enabled)));

                    Optional<Predicate> levelPredicate = Optional.ofNullable(level).map(l -> {
                        Join<AlarmDefinition, AlarmRule> ruleJoin = root.join("rules");
                        return cb.equal(ruleJoin.<AlarmLevel>get("level"), level);
                    });

                    Optional<Predicate> channelPredicate = Optional.ofNullable(channelId).map(c -> {
                        Join<AlarmDefinition, Notification> notificationJoin = root.join("notifications");
                        Join<Notification, Channel> channelJoin = notificationJoin.join("channel");
                        return cb.equal(channelJoin.<Long>get("id"), channelId);
                    });

                    Optional<Predicate> where =
                            Stream.of(namePredicate, tenantPredicate, metricPredicate, enablePredicate, levelPredicate, channelPredicate)
                                    .flatMap(o -> o.map(Stream::of).orElseGet(Stream::empty))
                                    .reduce(cb::and);

                    where.ifPresent(predicate -> cq.where(predicate).distinct(true));

                    return cq.getRestriction();

                }, pageable);
        return ResponseObject.success(page);
    }

    @Override
    public ResponseObject<Page<AlarmDefinitionOutput>> pageByV2(
            String name, List<AlarmLevel> alarmLevels, String tenantId, String channelId, List<String> channelEngineNames, String metricId, String metricName, String enabled, List<String> labelIds, String labelMatchType, Pageable pageable) {
        long begin = System.currentTimeMillis();
        Map<String, String> monitorContext = new HashMap<>();
        monitorContext.put("serviceId", tenantId);
        monitorContext.put("name", name);
        monitorContext.put("alarmLevels", alarmLevels != null ? JsonUtils.toJsonString(alarmLevels) : null);
        monitorContext.put("channelId", channelId);
        monitorContext.put("channelEngineNames", channelEngineNames != null ? JsonUtils.toJsonString(channelEngineNames) : null);
        monitorContext.put("metricsId", metricId);
        monitorContext.put("metricName", metricName);
        monitorContext.put("enabled", enabled);
        monitorContext.put("pageable", pageable != null ? pageable.toString() : null);
        SitePerformanceMonitorLogEntity logEntity = new SitePerformanceMonitorLogEntity(monitorLog, begin, cubeServerCacheLoader.getEnv(), IpUtils.getLocalIP(), Long.toString(Thread.currentThread().getId()), IdUtils.generateId(), Thread.currentThread().getStackTrace()[1].getClassName() + Thread.currentThread().getStackTrace()[1].getMethodName(), JsonUtils.toJsonString(monitorContext));
        authService.checkAuth(ThreadLocalStore.getUserInfoLocal(), ThreadLocalStore.getTenantInfoLocal(), ThreadLocalStore.getApiPath());
        logEntity.printPhase("auth");

        //get metricId from metricName and tenantId
        if (StringUtils.isBlank(metricId) && StringUtils.isNotBlank(metricName) && StringUtils.isNotBlank(tenantId)) {
            metricId = Optional.ofNullable(metricsHandler.findMetricsByNameAndTenantId(metricName, tenantId)).map(MetricsDO::getId).orElse(null);
        }
        String finalMetricId = metricId;

        Page<AlarmDefinition> page;
        if(!CollectionUtils.isEmpty(labelIds)) {
            List<Integer> levels = Optional.ofNullable(alarmLevels).orElse(Collections.emptyList()).stream().map(Enum::ordinal).toList();
            pageable.getPageNumber();
            pageable.getPageSize();
            Sort sort = Sort.by(Sort.Direction.DESC,"modify_time");
            Pageable pageV2 = PageRequest.of(pageable.getPageNumber(), pageable.getPageSize(), sort);
            page = alarmDefinitionDao.findAllWithJoins(name, tenantId, metricId, enabled == null? null : Boolean.valueOf(enabled), channelId,
                    CollectionUtils.isEmpty(channelEngineNames) ? null: channelEngineNames,
                    CollectionUtils.isEmpty(channelEngineNames),
                    CollectionUtils.isEmpty(levels) ? null: levels,
                    CollectionUtils.isEmpty(levels),
                    labelIds, labelIds.size(), labelMatchType, pageV2);
        }else{
            page =
                    alarmDefinitionDao.findAll((Specification<AlarmDefinition>) (root, cq, cb) -> {

                        Optional<Predicate> namePredicate =
                                Optional.ofNullable(name).map(n -> cb.like(root.get("name"), "%" + n + "%"));

                        Optional<Predicate> tenantPredicate =
                                Optional.of(tenantId).map(n -> cb.equal(root.get("tenantId"), tenantId));

                        Optional<Predicate> metricPredicate =
                                Optional.ofNullable(finalMetricId).map(n -> cb.equal(root.get("metricId"), finalMetricId));

                        Optional<Predicate> enablePredicate =
                                Optional.ofNullable(enabled).map(e -> cb.equal(root.get("enabled"), Boolean.valueOf(enabled)));

                        Optional<Predicate> levelPredicate = Optional.ofNullable(alarmLevels)
                                .filter(levels -> !levels.isEmpty()).map(l -> {
                                    Join<AlarmDefinition, AlarmRule> ruleJoin = root.join("rules");
                                    return ruleJoin.<AlarmLevel>get("level").in(alarmLevels.toArray());
                                });

                        Optional<Predicate> channelPredicate = Optional.ofNullable(channelId).map(c -> {
                            Join<AlarmDefinition, Notification> notificationJoin = root.join("notifications");
                            Join<Notification, Channel> channelJoin = notificationJoin.join("channel");
                            return cb.equal(channelJoin.<Long>get("id"), channelId);
                        });

                        Optional<Predicate> channelEngineNamePredicate = Optional.ofNullable(channelEngineNames)
                                .filter(names -> !names.isEmpty()).map(names -> {
                                    Subquery<Long> subquery = cq.subquery(Long.class);
                                    Root<AlarmDefinition> subRoot = subquery.from(AlarmDefinition.class);
                                    Join<AlarmDefinition, Notification> subNotificationJoin = subRoot.join("notifications");
                                    Join<Notification, Channel> subChannelJoin = subNotificationJoin.join("channel");

                                    subquery.select(cb.literal(1L))
                                            .where(cb.and(
                                                    subChannelJoin.get("engineName").in(names.toArray()),
                                                    cb.equal(subRoot.get("id"), root.get("id"))
                                            ));
                                    return cb.exists(subquery);
                                });
                        Optional<Predicate> where =
                                Stream.of(namePredicate, tenantPredicate, metricPredicate, enablePredicate, levelPredicate, channelPredicate, channelEngineNamePredicate)
                                        .flatMap(Optional::stream)
                                        .reduce(cb::and);

                        where.ifPresent(predicate -> {
                            if (cq != null) {
                                cq.where(predicate).distinct(true);
                            }
                        });

                        return cq.getRestriction();

                    }, pageable);
        }

        logEntity.printPhase("pageQuery");

        List<AlarmDefinition> alarmDefinitionList = page.getContent();
        List<AlarmDefinitionOutput> alarmDefinitionOutputList = new ArrayList<>();

        TenantDO tenantDO = tenantHandler.getTenantFromCacheFirst(tenantId);
        Assert.isTrue(tenantDO != null, "the service is not existed");
        List<String> alarmSilenceNameList = alarmDefinitionList.stream().map(e -> {
            String serviceName = tenantDO.getName();
            String alarmName = e.getName();
            String silenceName = serviceName + CommonSplitConstants.UNDER_SCORE_SPLIT + alarmName + CommonSplitConstants.UNDER_SCORE_SPLIT + e.getId() + CommonSplitConstants.UNDER_SCORE_SPLIT + ALARM_SILENCE;
            return silenceName;
        }).collect(Collectors.toList());

        List<AlarmSilenceDO> alarmSilenceDOList = alarmSilenceHandler.findByNameList(alarmSilenceNameList);
        Map<String, AlarmSilenceDO> name2AlarmSilence = new HashMap<>();
        alarmSilenceDOList.forEach(e -> name2AlarmSilence.put(e.getName(), e));

        logEntity.printPhase("queryAlarmSilence");

        List<AlarmGroupDO> alarmGroupDOList = alarmGroupUtilService.findAlarmGroupList(tenantId, true);
        logEntity.printPhase("queryAlarmGroup");
        alarmGroupDOList = alarmGroupDOList.stream().filter(
                        e -> {
                            if (!Integer.valueOf(1).equals(e.getVisibility())) {
                                return false;
                            }
                            return alarmGroupUtilService.canOperateAlarmGroup(ThreadLocalStore.getUserInfoLocal(), e.getId());
                        })
                .collect(Collectors.toList());

        logEntity.printPhase("filterAlarmGroup");

        for (AlarmDefinition alarmDefinition : alarmDefinitionList) {
            supplyAlarmDefinitionOutputInfo(Optional.of(alarmDefinition), false);
            AlarmDefinitionOutput alarmDefinitionOutput = new AlarmDefinitionOutput();
            BeanUtils.copyProperties(alarmDefinition, alarmDefinitionOutput);
            String serviceName = tenantDO.getName();
            String alarmName = alarmDefinition.getName();
            String silenceName = serviceName + CommonSplitConstants.UNDER_SCORE_SPLIT + alarmName + CommonSplitConstants.UNDER_SCORE_SPLIT + alarmDefinitionOutput.getId() + CommonSplitConstants.UNDER_SCORE_SPLIT + ALARM_SILENCE;
            AlarmSilenceDO alarmSilenceDO = name2AlarmSilence.get(silenceName);
            if (alarmSilenceDO == null) {
                alarmDefinitionOutput.setSilenceStatus(-1);
            } else {
                AlarmSilenceStatusEnum statusEnum = null;
                try {
                    statusEnum = alarmSilenceUtilService.calcAlarmSilenceStatus(alarmSilenceDO, null, new Date());
                } catch (Exception e) {
                    log.error("calcAlarmSilenceStatus {} failed", alarmSilenceDO.getName(), e);
                    Assert.isTrue(false, "calcAlarmSilenceStatus failed");
                }
                alarmDefinitionOutput.setSilenceStatus(statusEnum.getValue());
            }

            List<AlarmGroupDO> filterAlarmGroupDOList = alarmGroupUtilService.belongsToAlarmGroupList(alarmDefinition, alarmGroupDOList);

            List<AlarmDefinitionOutput.AlarmGroupIdName> alarmGroupIdNameList = new ArrayList<>();
            for (AlarmGroupDO alarmGroupDO : filterAlarmGroupDOList) {
                AlarmDefinitionOutput.AlarmGroupIdName alarmGroupIdName = new AlarmDefinitionOutput.AlarmGroupIdName();
                alarmGroupIdName.setId(alarmGroupDO.getId());
                alarmGroupIdName.setName(alarmGroupDO.getName());
                alarmGroupIdNameList.add(alarmGroupIdName);
            }
            alarmDefinitionOutput.setAlarmGroupList(alarmGroupIdNameList);
            // add label info
            List<TagInfoOut> tagInfoOuts = Optional.ofNullable(resourceTagService.listResourceTags(
                            tenantId, alarmDefinition.getId(), ResourceTypeConstant.RESOURCE_AlARM_TYPE))
                    .orElse(Collections.emptyList())
                    .stream()
                    .map(TagConvertUtil::toTagInfoOut)
                    .collect(Collectors.toList());
            alarmDefinitionOutput.setLabelInfoList(tagInfoOuts);
            alarmDefinitionOutputList.add(alarmDefinitionOutput);
        }
        logEntity.printPhase("postProcessAlarmDefinition");
        Page<AlarmDefinitionOutput> page1 = new PageImpl<>(alarmDefinitionOutputList, page.getPageable(), page.getTotalElements());

        return ResponseObject.success(page1);
    }

    /**
     * Check and save derivedMetric for alarm definition
     * @param definition The alarm definition containing derivedMetric
     * @return ResponseObject with error message if failed, or null if succeeded
     */
    private void updateRelatedDerivedMetric(AlarmDefinition definition) {
        if (definition.getDerivedMetric() == null) {
            return;
        }
        // Set alarm definition's source and sourceType
        definition.setSource(AlarmSourceEnum.METRIC);
        definition.setSourceType(AlarmSourceTypeEnum.SQL);

        DerivedMetric derivedMetric = definition.getDerivedMetric();
        //fill the needed properties values
        derivedMetric.setCreator(definition.getCreator());
        derivedMetric.setEditor(definition.getEditor());
        derivedMetric.setTenantId(definition.getTenantId());
        derivedMetric.setMetricId(definition.getMetricId());
        
        // Convert DerivedMetric to DerivedMetricInput using the static method
        DerivedMetricInput input = DerivedMetricInput.fromDerivedMetric(derivedMetric, definition.getEnabled());

        // Check if this is a create or update operation
        if (StringUtils.isBlank(derivedMetric.getId())) {
            // Create new derivedMetric
            DerivedMetric derivedMetricOut = derivedMetricHandler.add(input);
            //reset the metricId for alarm definition
            definition.setMetricId(derivedMetricOut.getMetricId());

        } else {
            // Update existing derivedMetric
            derivedMetricHandler.edit(input);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResponseObject<AlarmDefinition> save(AlarmDefinition definition) {
        initAlarmMatchMode(definition);
        // Moving this query earlier is to reduce one user information query during the creation of derived metrics.
        String userName = userHandler.getNameById(definition.getUserId());
        definition.setCreator(userName);
        definition.setEditor(userName);

        // create derivedMetric if needed
        updateRelatedDerivedMetric(definition);

        // Create AdCfg for AI conditions if needed
        createAdCfgForAIConditions(definition);

        if (definition.getId() != null) {
            return ResponseObject.fail("Can't create alarm definition with specified id.");
        }

        if (!CollectionUtils.isEmpty(definition.getAlarmExtensionRelations())) {
            definition.getAlarmExtensionRelations().forEach(d -> {
                d.setId(null);
            });
        }


        boolean isExistedDefinition = alarmDefinitionDao.findByNameAndTenantId(definition.getName(), definition.getTenantId()).isPresent();
        if(isExistedDefinition) {
            throw new IllegalStateException("The name is duplicated.");
        }

        ResponseObject<AlarmDefinition> alarmDefinitionResponseObject = ResponseObject.success(alarmDefinitionDao.save(definition));

        // add tag info
        if(!CollectionUtils.isEmpty(definition.getLabelInfoList())) {
            List<String> tagIds = definition.getLabelInfoList().stream().map(TagInputDo::getId).filter(Objects::nonNull).collect(Collectors.toList());
            if(!CollectionUtils.isEmpty(tagIds)) {
                resourceTagService.batchAddResourceTag(tagIds, ResourceTypeConstant.RESOURCE_AlARM_TYPE, definition.getId(),
                        ResourceTypeConstant.RESOURCE_TAG_OPERATION_ADD);
            }
        }

        return alarmDefinitionResponseObject;
    }


    public List<AlarmSynDO> buildTplRelationShipTenantAlarms(AlarmDefinition definition, String userId, String tplName, String destTenantScope, List<String> includeTenantNames, List<String> excludeTenantNames, boolean override) {
        List<AlarmSynDO> list = new ArrayList<>();
        TplAlarmSynTenantIdDTO tplAlarmSynTenantIdDTO = templateHandler.queryNeedTplAlarmSynTenantIdsByMetricsIdAndAlarmName(definition.getMetricId(), definition.getName(), definition.getTenantId(), definition.getUserId(), tplName, destTenantScope, includeTenantNames, excludeTenantNames, override);
        if (CollectionUtils.isEmpty(tplAlarmSynTenantIdDTO.getNeedAddList())
                && CollectionUtils.isEmpty(tplAlarmSynTenantIdDTO.getNeedOverrideList())
                && CollectionUtils.isEmpty(tplAlarmSynTenantIdDTO.getNeedSkipList())
                && CollectionUtils.isEmpty(tplAlarmSynTenantIdDTO.getErrorList())) {
            return list;
        }
        String metricsName = metricsHandler.getMetricsById(definition.getMetricId()).getMetricsName();

        // check metrics and channel in dest tenants
        Set<String> checkFailedTenantIdList = tplAlarmSynTenantIdDTO.getNeedAddList().stream().filter(id -> {
            MetricsDO metricsDO = metricsHandler.findMetricsByNameAndTenantId(metricsName, id);
            List<Channel> channelList = channelHandler.findByTenantId(id);
            boolean hasEventChannel = channelList.stream().anyMatch(channel -> "Kafka".equalsIgnoreCase(channel.getEngineName()));
            return metricsDO == null || !hasEventChannel;
        }).collect(Collectors.toSet());
        List<String> checkFailedTenantNameList = templateHandler.buildTenantByTenantIds(new ArrayList<>(checkFailedTenantIdList)).stream().map(TenantInfoDO::getName).collect(Collectors.toList());
        tplAlarmSynTenantIdDTO.getNeedAddList().removeAll(checkFailedTenantIdList);
        tplAlarmSynTenantIdDTO.getErrorList().addAll(checkFailedTenantNameList);

        List<AlarmSynDO> result = buildTenantAlarmList(definition, tplAlarmSynTenantIdDTO, userId);
        if (!CollectionUtils.isEmpty(result)) {
            list.addAll(result);
        }
        return list;
    }


    private List<AlarmSynDO> buildTenantAlarmList(AlarmDefinition definition, TplAlarmSynTenantIdDTO tplAlarmSynTenantIdDTO, String userId) {
        List<AlarmSynDO> result = new ArrayList<>();
        Set<String> tenantSet = new HashSet<>();
        Set<String> errorTenantSet = new HashSet<>();
        if (!CollectionUtils.isEmpty(tplAlarmSynTenantIdDTO.getNeedAddList())) {
            tenantSet.addAll(tplAlarmSynTenantIdDTO.getNeedAddList());
        }
        if (!CollectionUtils.isEmpty(tplAlarmSynTenantIdDTO.getNeedSkipList())) {
            tenantSet.addAll(tplAlarmSynTenantIdDTO.getNeedSkipList());
        }
        Map<String, TplAlarmOverrideDTO> overrideDTOMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(tplAlarmSynTenantIdDTO.getNeedOverrideList())) {
            overrideDTOMap.putAll(tplAlarmSynTenantIdDTO.getNeedOverrideList().stream().collect(Collectors.toMap(TplAlarmOverrideDTO::getTenantId, e -> e)));
            tenantSet.addAll(tplAlarmSynTenantIdDTO.getNeedOverrideList().stream().map(TplAlarmOverrideDTO::getTenantId).collect(Collectors.toList()));
        }

        if (!CollectionUtils.isEmpty(tplAlarmSynTenantIdDTO.getErrorList())) {
            errorTenantSet.addAll(tplAlarmSynTenantIdDTO.getErrorList());
        }

        Map<String/**tenantId**/, String/**tenantName**/> tenantMap = new HashMap<>();
        List<TenantInfoDO> tenantInfoDOS = templateHandler.buildTenantByTenantIds(new ArrayList<>(tenantSet));
        tenantMap.putAll(tenantInfoDOS.stream().collect(Collectors.toMap(TenantInfoDO::getId, e -> e.getName())));

        if (!CollectionUtils.isEmpty(tenantSet)) {
            tenantSet.forEach(e -> {
                AlarmSynDO alarmSynDO = new AlarmSynDO();
                if (tplAlarmSynTenantIdDTO.getNeedAddList().contains(e)) {
                    AlarmSynAddDO alarmSynAddDO = new AlarmSynAddDO();

                    AlarmDefinition dto = new AlarmDefinition();
                    dto.setName(definition.getName());
                    dto.setTenantId(e);
                    dto.setUserId(userId);
                    Date date = new Date();
                    dto.setCreateTime(date);
                    dto.setModifyTime(date);
                    MetricsDO metricsDO = metricsHandler.getMetricsById(definition.getMetricId());
                    MetricsDO tenantMetricsDO = metricsHandler.findMetricsByNameAndTenantId(metricsDO.getMetricsName(), e);
                    dto.setMetricId(tenantMetricsDO.getId());

                    dto.setTimesInPeriod(definition.getTimesInPeriod());
                    dto.setPeriodInMinutes(definition.getPeriodInMinutes());
                    dto.setEnabled(definition.getEnabled());

                    /*if (!CollectionUtils.isEmpty(definition.getScope())) {
                        List<TagCondition> scopeList = new ArrayList<>();
                        definition.getScope().stream().forEach(ee -> {
                            TagCondition tagCondition = new TagCondition();
                            tagCondition.setOperand(ee.getOperand());
                            tagCondition.setOperator(ee.getOperator());
                            tagCondition.setName(ee.getName());
                            scopeList.add(tagCondition);
                        });
                        dto.setScope(scopeList);
                    }*/
                    if (!CollectionUtils.isEmpty(definition.getRules())) {
                        List<AlarmRule> alarmRuleList = new ArrayList<>();
                        definition.getRules().forEach(ee -> {
                            AlarmRule alarmRule = new AlarmRule();
                            if (!CollectionUtils.isEmpty(ee.getConditions())) {
                                List<RuleCondition> conditions = new ArrayList<>();
                                ee.getConditions().forEach(eee -> {
                                    RuleCondition ruleCondition = new RuleCondition();
                                    ruleCondition.setConditionType(eee.getConditionType());
                                    ruleCondition.setThreshold(eee.getThreshold());
                                    ruleCondition.setHistogramQuantile(eee.getHistogramQuantile());
                                    ruleCondition.setName(eee.getName());
                                    ruleCondition.setExpression(eee.getExpression());
                                    ruleCondition.setOperator(eee.getOperator());
                                    conditions.add(ruleCondition);
                                });
                                alarmRule.setConditions(conditions);
                            }
                            alarmRule.setNeedHits(ee.getNeedHits());
                            alarmRule.setLevel(ee.getLevel());

                            alarmRuleList.add(alarmRule);
                        });
                        dto.setRules(alarmRuleList);
                    }
                    if (!CollectionUtils.isEmpty(definition.getNotifications())) {
                        ResponseObject<List<Channel>> channels = channelService.listBy(e);
                        if (channels != null && !CollectionUtils.isEmpty(channels.getData())) {
                            Notification notification = definition.getNotifications().get(0);
                            dto.setNotifications(buildNotification(channels.getData(), notification));
                        }
                    }
                    alarmSynAddDO.setAlarmDefinition(dto);
                    alarmSynAddDO.setTenantName(tenantMap.get(e));
                    alarmSynDO.setAlarmSynAddDO(alarmSynAddDO);
                }
                if (tplAlarmSynTenantIdDTO.getNeedSkipList().contains(e)) {
                    AlarmSynSkipDO alarmSynSkipDO = new AlarmSynSkipDO();
                    alarmSynSkipDO.setTenantId(e);
                    alarmSynSkipDO.setTenantName(tenantMap.get(e));
                    alarmSynDO.setAlarmSynSkipDO(alarmSynSkipDO);
                }
                if (overrideDTOMap.containsKey(e)) {
                    AlarmSynUpdateDO alarmSynUpdateDO = new AlarmSynUpdateDO();
                    alarmSynUpdateDO.setTenantId(e);
                    alarmSynUpdateDO.setTenantName(tenantMap.get(e));
                    alarmSynDO.setAlarmSynUpdateDO(alarmSynUpdateDO);
                }
                result.add(alarmSynDO);
            });
        }
        if (!CollectionUtils.isEmpty(errorTenantSet)) {
            errorTenantSet.forEach(e -> {
                AlarmSynDO alarmSynDO = new AlarmSynDO();
                if (tplAlarmSynTenantIdDTO.getErrorList().contains(e)) {
                    AlarmSynErrorDO alarmSynErrorDO = new AlarmSynErrorDO();
                    alarmSynErrorDO.setTenantName(e);
                    alarmSynDO.setAlarmSynErrorDO(alarmSynErrorDO);
                    result.add(alarmSynDO);
                }
            });
        }
        return result;
    }


    private List<Notification> buildNotification(List<Channel> channels, Notification notification) {
        List<Notification> result = new ArrayList<>();
        if (!CollectionUtils.isEmpty(channels) && notification != null) {
            for (Channel channel : channels) {
                if ("KAFKA".equalsIgnoreCase(channel.getEngineName())) {
                    Notification dto = new Notification();
                    dto.setTitle(notification.getTitle());
                    dto.setContent(notification.getContent());
                    dto.setWhichLevels(notification.getWhichLevels());
                    Channel channelDTO = new Channel();
                    channelDTO.setId(channel.getId());
                    dto.setChannel(channelDTO);
                    result.add(dto);
                }
            }
        }
        return result;
    }

    private Optional<ResponseObject<AlarmDefinition>> validateTemplate(AlarmDefinition definition) {

        if (AlarmSourceTypeEnum.SQL.equals(definition.getSourceType())) {
            return Optional.empty();
        }
        MetricsDO metricsDO = metricsDAO.getMetricsById(definition.getMetricId());
        Assert.notNull(metricsDO, String.format("Metric doesn't exist for id:%s", definition.getMetricId()));

        List<MetricsFieldDO> fields =
                metricsFieldDAO.listFieldByMetricsIds(
                        Collections.singletonList(definition.getMetricId()));

        checkMetricBelongsToService(metricsDO, definition);

        checkAlarmRuleWithMetricDefinition(definition, metricsDO, fields);

        checkChannelBelongsToService(definition);

        Map<String, Object> model = mockModel(metricsDO.getTagNames(), fields);

        for (Notification notification : definition.getNotifications()) {
            try {
                TempUtils.convert(notification.getContent(), model);
            } catch (Exception e) {
                return Optional.of(ResponseObject.fail(e.getMessage()));
            }
        }

        return Optional.empty();
    }

    private void checkMetricBelongsToService(MetricsDO metricsDoFromTransfer, AlarmDefinition definition) {
        Assert.notNull(metricsDoFromTransfer, "Metric doesn't exist");
        List<MetricsDO> metricsByTenant = metricsDAO.getMetricsByTenant(definition.getTenantId());
        Assert.isTrue(metricsByTenant.stream().anyMatch(metricByTen -> metricByTen.getId().equals(metricsDoFromTransfer.getId())), "This Metric doesn't belongs to the Service");
    }

    private void checkAlarmRuleWithMetricDefinition(AlarmDefinition alarmDefinition, MetricsDO metricsDO, List<MetricsFieldDO> metricFields) {

        List<String> metricTagNames = (List<String>) CollectionUtils.arrayToList(Optional.ofNullable(metricsDO.getTagNames()).orElse(StringUtils.EMPTY).split(CommonSplitConstants.COMMA_SPLIT));
        Map<String, Integer> metricFieldAndTypes = metricFields.stream().collect(Collectors.toMap(MetricsFieldDO::getFieldName, MetricsFieldDO::getFieldType));

        alarmDefinition.getRules().forEach(alarmRule ->
                Optional.ofNullable(alarmRule.getConditions()).orElse(new ArrayList<>()).forEach(conditions -> {
                    if (ConditionType.TAG == conditions.getConditionType()) {
                        //check tag and operation
                        Assert.isTrue(metricTagNames.contains(conditions.getName()), String.format("Metric doesn't have this tag:[%s]", conditions.getName()));
                        Assert.isTrue(AlarmOperatorEnum.containsOperation(conditions.getOperator(), MetricsFieldTypeEnum.string), String.format("%s operator is not allowed in Tag", conditions.getOperator()));

                    } else if (ConditionType.FIELD == conditions.getConditionType()) {
                        //check field and operation according to type
                        Assert.isTrue(metricFieldAndTypes.containsKey(conditions.getName()), String.format("Metric doesn't have this field:[%s]", conditions.getName()));
                        Assert.isTrue(AlarmOperatorEnum.containsOperation(conditions.getOperator(), MetricsFieldTypeEnum.fromValue(metricFieldAndTypes.get(conditions.getName())))
                                , String.format("%s operator is not allowed against the specified field type or the field type is not defined", conditions.getOperator()));
                    }
                })
        );
    }

    private void checkChannelBelongsToService(AlarmDefinition definition) {
        List<String> channelIdsByTenant = channelHandler.findByTenantId(definition.getTenantId()).stream().map(Channel::getId).collect(Collectors.toList());
        definition.getNotifications().forEach(notification -> {
            Assert.isTrue(channelIdsByTenant.contains(notification.getChannel().getId()), "This channel doesn't belong to the Service");
        });
    }


    public Map<String, Object> mockModel(String tagNames, List<MetricsFieldDO> fields) {

        Map<String, Object> data = new HashMap<>();

        Arrays.stream(tagNames.split(","))
                .forEach(tagName -> data.put(tagName, "mock-" + tagName));

        fields.forEach(field -> data.put(field.getFieldName(), 100));

        data.put("alarmTime", "2020-12-20 00:00:00");

        data.put("alarmName", "mock-alarm-name");

        data.put("alarmMetricName", "mock-alarm-metric");

        data.put("alarmDetailUrl", "mock-alarm-detail-url");

        data.put("alarmLevel", AlarmLevel.ERROR.name());

        return data;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseObject<AlarmDefinition> update(AlarmDefinition definition) {
        if (definition.getId() == null) {
            return ResponseObject.fail("Can't update alarm definition without id.");
        }
        initAlarmMatchMode(definition);

        Optional<AlarmDefinition> isExistedDefinition =
                alarmDefinitionDao.findByNameAndTenantId(definition.getName(), definition.getTenantId());

        String userName = userHandler.getNameById(definition.getUserId());
        definition.setEditor(userName);

        if(isExistedDefinition.isPresent()) {
            Assert.isTrue(isExistedDefinition.get().getId().equals(definition.getId()), "The alarm definition name is duplicated.");
            Assert.isTrue(isExistedDefinition.get().getSource().equals(definition.getSource()), "Don't support changing the source of alarm definition.");
            Assert.isTrue(isExistedDefinition.get().getSourceType().equals(definition.getSourceType()), "Don't support changing the source type of alarm definition.");
            definition.setCreator(isExistedDefinition.get().getCreator());
            definition.setCreateTime(isExistedDefinition.get().getCreateTime());
        } else {
            definition.setCreator(userName);
        }

        // update the derivedMetric if needed
        updateRelatedDerivedMetric(definition);

        // Create AdCfg for AI conditions if needed
        createAdCfgForAIConditions(definition);

        ResponseObject<AlarmDefinition> alarmDefinitionResponseObject = ResponseObject.success(alarmDefinitionDao.save(definition));

        if(alarmDefinitionResponseObject.getStatus().equals(StatusEnum.SUCCESS.getStatus())){
            List<String> currentTags = Optional.ofNullable(definition.getLabelInfoList()).orElse(Collections.emptyList())
                    .stream().map(TagInputDo::getId).collect(Collectors.toList());
            resourceTagService.handleEditResourceTag(currentTags, ThreadLocalStore.getTenantInfoLocal(), definition.getId(), ResourceTypeConstant.RESOURCE_AlARM_TYPE);
            alarmDefinitionResponseObject.getData().setLabelInfoList(definition.getLabelInfoList());
        }

        return alarmDefinitionResponseObject;
    }


    @Override
    public ResponseObject<AlarmDefinition> updateEnable(String id, Boolean enable) {
        authService.checkAuth(ThreadLocalStore.getUserInfoLocal(), ThreadLocalStore.getTenantInfoLocal(), ThreadLocalStore.getApiPath());

        Optional<AlarmDefinition> optionalDefinition = alarmDefinitionDao.findById(id);

        return optionalDefinition

                .map(definition -> {
                    definition.setEnabled(enable);
                    alarmDefinitionDao.save(definition);
                    return ResponseObject.success(definition);
                })

                .orElse(ResponseObject.fail("Not found the alarm definition with id: " + id));
    }

    @Override
    public ResponseObject<List<IdAndName>> listByTenant(String tenantName) {
        authService.checkAuth(ThreadLocalStore.getUserInfoLocal(), ThreadLocalStore.getTenantInfoLocal(), ThreadLocalStore.getApiPath());

        TenantDO tenantDO = tenantDAO.getTenantByName(tenantName);

        return ResponseObject.success(
                alarmDefinitionDao.findByTenantId(tenantDO.getId())
        );
    }

    @Override
    public Page<IdAndName> pageListByTenant(String tenantName, Pageable pageable) {
        authService.checkAuth(ThreadLocalStore.getUserInfoLocal(), ThreadLocalStore.getTenantInfoLocal(), ThreadLocalStore.getApiPath());

        TenantDO serviceTenant = tenantDAO.getTenantByName(tenantName);
        AlarmDefinition alarmDefinition = new AlarmDefinition();
        alarmDefinition.setTenantId(serviceTenant.getId());
        alarmDefinition.setEnabled(null);

        return alarmDefinitionDao.findAll(Example.of(
                        alarmDefinition,
                        ExampleMatcher.matching()
                                .withMatcher("tenantId", exact()))
                , pageable).map(alarmDef -> {
            return new IdAndName() {
                @Override
                public String getId() {
                    return alarmDef.getId();
                }

                @Override
                public String getName() {
                    return alarmDef.getName();
                }
            };
        });
    }

    @Override
    public ResponseObject<Map> getThresholds() {
        authService.checkAuth(ThreadLocalStore.getUserInfoLocal(), ThreadLocalStore.getTenantInfoLocal(), ThreadLocalStore.getApiPath());

        Map thresholds = sysParaService.getCubeThresHoldsCache(HIT_THRESHOLDS);
        return ResponseObject.success(thresholds);
    }

    @Override
    public ResponseObject<AlarmDefinition> delete(IdPara idPara) {

        Optional<AlarmDefinition> alarmDefinitionOptional = alarmDefinitionDao.findById(idPara.getId());

        //delete tag
        resourceTagService.deleteResourceTagByName(idPara.getId(), ResourceTypeConstant.RESOURCE_AlARM_TYPE);

        //delete silence
        alarmService.delRelatedAlarmSilence(alarmService.createAlarmIdPara(idPara.getId(), idPara.getUserId(), idPara.getTenantId()));

        //delete definition
        alarmDefinitionDao.deleteById(idPara.getId());

        //delete derived metric and related original metric and AI model.
        if (alarmDefinitionOptional.isPresent()) {
            AlarmDefinition alarmDefinition = alarmDefinitionOptional.get();
            if (alarmDefinition.getSourceType() != null &&
                    alarmDefinition.getSourceType().equals(AlarmSourceTypeEnum.SQL) &&
                    alarmDefinition.getMetricId() != null) {

                //delete AI model
                deleteDerivedMetricRelatedAIModel(alarmDefinition);
                DerivedMetric derivedMetric = derivedMetricHandler.getByMetricId(alarmDefinition.getMetricId());
                if (derivedMetric != null) {
                    derivedMetricHandler.delete(derivedMetric.getId());
                }
            }
            return ResponseObject.success(alarmDefinition);
        } else {
            return ResponseObject.fail("The target alarm definition is not exists.");
        }
    }

    private void deleteDerivedMetricRelatedAIModel(AlarmDefinition alarmDefinition) {
        if (StringUtils.isBlank(alarmDefinition.getMetricId()) || !AlarmSourceTypeEnum.SQL.equals(alarmDefinition.getSourceType())) {
            return;
        }
        AdCfgData adCfgData = new AdCfgData();
        adCfgData.setMetricsId(alarmDefinition.getMetricId());
        aiCfgService.delAdCfgByMetricsId(adCfgData);
    }

    @Override
    public ResponseObject<String> deleteByIds(IdListPara idListPara) {

        List<String> ids = idListPara.getIds();

        for(String id: ids) {
            IdPara idPara = new IdPara();
            BeanUtils.copyProperties(idListPara, idPara);
            idPara.setId(id);

            try{
                delete(idPara);
            } catch (Exception e) {
                String errMsg = String.format("Batch delete alarm error! alarm Id: %s. ", id);
                log.error(errMsg, e);
                return ResponseObject.fail(errMsg);
            }
        }
        return ResponseObject.success("Batch delete alarm definition successfully!");
    }

    @Override
    public ResponseObject<AlarmDefinition> getById(String id) {
        authService.checkAuth(ThreadLocalStore.getUserInfoLocal(), ThreadLocalStore.getTenantInfoLocal(), ThreadLocalStore.getApiPath());
        AlarmDefinition alarmDefinition = alarmDefinitionDao.findById(id).get();
        supplyAlarmDefinitionOutputInfo(alarmDefinition, false);
        return ResponseObject.success(alarmDefinition);
    }

    private static final String FIELD_GREATER_REC_SQL = """
             SELECT
             CASE
                WHEN n_sample <= 1000 then null
                WHEN quantile_99 >= (avg_value + 3 * std_value) then quantile_99
                WHEN quantile_9999 >= (avg_value + 3 * std_value) then (avg_value + 3 * std_value)
                ELSE quantile_995
             END as recommend_value
             FROM
             (
             SELECT
             quantileExact(0.99)(%s) as quantile_99,
             quantileExact(0.995)(%s) as quantile_995,
             quantileExact(0.9999)(%s) as quantile_9999,
             avg(%s) as avg_value,
             stddevPop(%s) as std_value,
             count(1) as n_sample
             FROM %s
             WHERE time > %d AND time < %d %s
             )
            """;

    private static final String FIELD_LESS_REC_SQL = """
              SELECT
              case
                  when n_sample <= 1000 then null
                  when quantile_01 <= (avg_value - 3 * std_value) then quantile_01
                  when quantile_0001 <= (avg_value - 3 * std_value) then (avg_value - 3 * std_value)
                  else quantile_005
              END as recommend_value
              FROM
              (
              SELECT
              quantileExact(0.01)(%s) as quantile_01,
              quantileExact(0.005)(%s) as quantile_005,
              quantileExact(0.0001)(%s) as quantile_0001,
              avg(%s) as avg_value,
              stddevPop(%s) as std_value,
              count(1) as n_sample
              FROM %s
              WHERE time > %d AND time < %d %s
              )
            """;

    private static final String RECOMMEND_VALUE = "recommend_value";

    @Override
    public ResponseObject<AlarmConditionThresholdRecModel> queryFieldRecThreshold(AlarmConditionThresholdRecModel alarmConditionThresholdRecModel) {

        authService.checkAuth(ThreadLocalStore.getUserInfoLocal(), ThreadLocalStore.getTenantInfoLocal(), ThreadLocalStore.getApiPath());
        try {
            //Add switch
            if (!alarmParaService.getParamBooleanValue(NEED_REC_FIELD_THRESHOLD, true)) {
                return ResponseObject.success(alarmConditionThresholdRecModel);
            }
            //check input is valid.
            if (Objects.isNull(alarmConditionThresholdRecModel) || !alarmConditionThresholdRecModel.isFieldRecValid()) {
                return ResponseObject.success(alarmConditionThresholdRecModel);
            }
            String tenantName = Optional.of(tenantHandler.getTenantFromCacheFirst(alarmConditionThresholdRecModel.getTenantId())).map(TenantDO::getName).orElse(null);
            String metricName = Optional.of(metricsHandler.getMetricsById(alarmConditionThresholdRecModel.getMetricId())).map(MetricsDO::getMetricsName).orElse(null);
            if (StringUtils.isBlank(tenantName) || StringUtils.isBlank(metricName)) {
                return ResponseObject.success(alarmConditionThresholdRecModel);
            }
            String tableName = String.format("%s.%s", ClickhouseSqlUtil.encodeClickhouseName(tenantName), metricName);
            String operator = alarmConditionThresholdRecModel.getRecCondition().getOperator();
            String fieldName = alarmConditionThresholdRecModel.getRecCondition().getName();

            long hourTimestamp = TimeUtils.getCurrentHourlyTimestamp();
            long end = hourTimestamp / 1000;
            long begin = DateUtils.addDay(new Date(hourTimestamp), -7) / 1000;
            String otherConditions = generateOtherRecConditions(alarmConditionThresholdRecModel.getOtherConditions());

            String querySql;
            if (IS_GREATER.equals(operator) || IS_GREATER_EQUAL.equals(operator)) {
                querySql = String.format(FIELD_GREATER_REC_SQL, fieldName, fieldName, fieldName, fieldName, fieldName, tableName, begin, end, otherConditions);
            } else {
                querySql = String.format(FIELD_LESS_REC_SQL, fieldName, fieldName, fieldName, fieldName, fieldName, tableName, begin, end, otherConditions);
            }

            List<Map<String, Object>> queryResult = clickhouseHandlerFactory.get().query(tenantName, querySql, subEnvironmentHandler.getDataQueryEnv(tenantName));
            if (CollectionUtils.isEmpty(queryResult)) {
                return ResponseObject.success(alarmConditionThresholdRecModel);
            }

            Double recValue = Optional.ofNullable(queryResult.get(0)).map(r -> r.get(RECOMMEND_VALUE)).map(Object::toString).map(Double::valueOf).orElse(null);

            if (recValue == null) {
                return ResponseObject.success(alarmConditionThresholdRecModel);
            }
            alarmConditionThresholdRecModel.getRecCondition().setThreshold(String.format("%.2f", recValue));

        } catch (Exception e) {
            log.error("queryFieldRecThreshold error!, alarmConditionThresholdRecModel:{}", JsonUtils.toJsonStringIgnoreExp(alarmConditionThresholdRecModel), e);
        }

        return ResponseObject.success(alarmConditionThresholdRecModel);
    }

    private String generateOtherRecConditions(List<AlarmConditionThresholdRecModel.RuleCondition> conditions) {
        if (conditions == null || conditions.isEmpty()) {
            return "";
        }

        StringBuilder sb = new StringBuilder();

        for (AlarmConditionThresholdRecModel.RuleCondition condition : conditions) {
            if (!ConditionType.TAG.equals(condition.getConditionType())) {
                continue;
            }

            String name = condition.getName();
            String operator = condition.getOperator();
            String threshold = condition.getThreshold();

            if (name == null || operator == null || threshold == null) {
                continue;
            }

            // Construct SQL conditions based on different operators
            String conditionSql = buildTagCondition(name, operator, threshold);
            if (StringUtils.isNotBlank(conditionSql)) {
                sb.append(" AND ");
                sb.append(conditionSql);
            }
        }

        return sb.toString();
    }

    private String buildTagCondition(String name, String operator, String threshold) {

        String[] values = threshold.split(",");
        String formattedValues = Arrays.stream(values)
                .map(String::trim)
                .map(v -> "'" + v + "'")
                .collect(Collectors.joining(", "));

        switch (operator.toLowerCase()) {
            case "in":
                return String.format("%s IN (%s)", name, formattedValues);
            case "not-in":
                return String.format("%s NOT IN (%s)", name, formattedValues);
            default:
                return null;
        }
    }

    public void supplyAlarmDefinitionOutputInfo(Optional<AlarmDefinition> alarmDefinition, boolean forExport) {
        if (alarmDefinition.isEmpty()) {
            return;
        }
        supplyAlarmDefinitionOutputInfo(alarmDefinition.get(), forExport);
    }

    public void supplyAlarmDefinitionOutputInfo(AlarmDefinition alarmDefinition, boolean forExport) {
        try {
            //add label info
            List<TagInputDo> tagInputDos = resourceTagService.listResourceTagOutDo(ThreadLocalStore.getTenantInfoLocal(), alarmDefinition.getId(),
                    ResourceTypeConstant.RESOURCE_AlARM_TYPE);
            alarmDefinition.setLabelInfoList(tagInputDos);

            // fill in derivedMetric info
            if (alarmDefinition.getSourceType() != null &&
                    alarmDefinition.getSourceType().equals(AlarmSourceTypeEnum.SQL) &&
                    alarmDefinition.getMetricId() != null) {
                DerivedMetric derivedMetric = derivedMetricHandler.getByMetricId(alarmDefinition.getMetricId());
                if (derivedMetric != null) {
                    alarmDefinition.setDerivedMetric(derivedMetric);
                }
            }

            //get metrics tagOrField type
            String metricsId = alarmDefinition.getMetricId();
            if (StringUtils.isBlank(metricsId)) {
                return;
            }
            List<FieldOrTag> tagAndFields = metricsHandler.getTagAndField(metricsId);
            //fieldName -> fieldValueType(number/string)
            Map<String, String> fieldOrTagNameToValueTypeMap = tagAndFields.stream().collect(Collectors.toMap(FieldOrTag::getFieldOrTagName, FieldOrTag::getValueType, (a, b) -> a));

            Integer period = metricsService.getPeriod(metricsId) == null ? DEFAULT_PERIOD_UNIT : metricsService.getPeriod(metricsId);

            //fill null value of hitCount & timeWindow in alarm rules
            List<AlarmRule> rules = alarmDefinition.getRules();
            for (AlarmRule alarmRule : rules) {
                //for AI model User-Defined Threshold condition.
                RuleCondition newCondition = null;
                //fill valueType of ruleCondition field
                for (RuleCondition ruleCondition : alarmRule.getConditions()) {
                    String valueType = fieldOrTagNameToValueTypeMap.get(ruleCondition.getName());
                    ruleCondition.setValueType(valueType);

                    if (ConditionType.AI.equals(ruleCondition.getConditionType())) {
                        String adId = ruleCondition.getExpression();
                        if (StringUtils.isEmpty(adId)) {
                            continue;
                        }
                        List<AdCfgDataOut> adCfgs = adCfgHandler.getAdCfgById(adId);
                        if (CollectionUtils.isEmpty(adCfgs)) {
                            continue;
                        }
                        AdCfgDataOut adCfg = adCfgs.get(0);
                        if(forExport) {
                            //to cover the id by name in the expression
                            ruleCondition.setExpression(adCfg.getAdName());
                        }

                        //Old config, need to complete the info from AD Model
                        if (StringUtils.isEmpty(ruleCondition.getExtension())) {
                            //add ai extension info.
                            AlarmRuleSingleContent.AlarmRuleConditionExtension extension = new AlarmRuleSingleContent.AlarmRuleConditionExtension();
                            AlarmRuleSingleContent.DetectModeEnum detectModeEnum = AlarmRuleSingleContent.fromAnomalyDirectionType(adCfg.getAnomalyDirectionType());
                            extension.setDetectMode(detectModeEnum);
                            ruleCondition.setExtension(JsonUtils.toJsonString(extension));

                            //update hitCount, timeWindow and isContinuous.
                            Optional.ofNullable(MathUtils.safeParseInt(adCfg.getRollingAlertSensitivityNum())).ifPresent(alarmRule::setHitCount);
                            Optional.ofNullable(MathUtils.safeParseInt(adCfg.getRollingAlertSensitivityAll())).ifPresent(alarmRule::setNeedHits);
                            alarmRule.setIsContinuous(false);

                            if (StringUtils.isNoneBlank(adCfg.getThresValueFilterUp()) || StringUtils.isNoneBlank(adCfg.getThresValueFilterDown())) {
                                //add new condition
                                newCondition = new RuleCondition();
                                newCondition.setConditionType(ConditionType.EXPRESSION);
                                String expression = AlarmRuleSingleContent.buildUserDefinedThresholdExpression(adCfg.getFieldName(), adCfg.getThresValueFilterUp(), adCfg.getThresValueFilterDown());
                                newCondition.setExpression(expression);
                            }
                            alarmRule.setTimeWindow(period * alarmRule.getNeedHits());
                        }
                    }
                }
                if (Objects.nonNull(newCondition)) {
                    alarmRule.getConditions().add(newCondition);
                }
                if (alarmRule.getHitCount() == null || alarmRule.getHitCount() <= 0) {
                    alarmRule.setHitCount(alarmRule.getNeedHits());
                }
                if (alarmRule.getTimeWindow() == null || alarmRule.getTimeWindow() <= 0) {
                    alarmRule.setTimeWindow(period * alarmRule.getNeedHits());
                }
            }
        } catch (Exception e) {
            log.error("initAlarmDefinition error, alarmDefinition:{}", JsonUtils.toJsonStringIgnoreExp(alarmDefinition), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseObject<AlarmDefinition> updateApproveStatus(AlarmApproveStatusInput input) {
        // Validate input
        input.validate();
        
        // Check authorization
        authService.checkAuth(ThreadLocalStore.getUserInfoLocal(), ThreadLocalStore.getTenantInfoLocal(), ThreadLocalStore.getApiPath());
        
        // Find the alarm definition
        Optional<AlarmDefinition> optionalDefinition = alarmDefinitionDao.findById(input.getId());
        
        if (optionalDefinition.isEmpty()) {
            return ResponseObject.fail("Alarm definition not found with id: " + input.getId());
        }
        
        AlarmDefinition definition = optionalDefinition.get();
        
        // Map input status to enum value using fromValue method
        AlarmConfigStatusEnum status = AlarmConfigStatusEnum.fromValue(input.getStatus());
        if (status == null) {
            return ResponseObject.fail("Invalid status value: " + input.getStatus());
        }
        
        // Update the status
        definition.setStatus(status);
        definition.setModifyTime(new Date());
        String userName = userHandler.getNameById(input.getUserId());
        definition.setEditor(userName);
        
        // Save the updated definition
        alarmDefinitionDao.save(definition);
        
        return ResponseObject.success(definition);
    }


    /**
     * Build sorting rules
     * @param field Sort field
     * @param order Sort direction
     * @return Sort object
     */
    @Override
    public Sort buildSort(String field, String order) {
        // If no sorting parameters provided, use default sorting
        if (field == null || field.trim().isEmpty() || order == null || order.trim().isEmpty()) {
            return Sort.by(Sort.Direction.DESC, "modifyTime", "id");
        }

        // Validate sort direction
        Sort.Direction direction;
        try {
            direction = Sort.Direction.fromString(order.toUpperCase());
        } catch (IllegalArgumentException e) {
            // If sort direction is invalid, use default descending order
            direction = Sort.Direction.DESC;
        }

        // Validate sort field and map to database column name
        String mappedField = mapFieldToDatabaseColumn(field);

        return Sort.by(direction, mappedField);
    }

    @Override
    public List<FieldOrTag> getMetricTagAndFieldForAlarm(String metricId) {
        List<FieldOrTag> fieldOrTagList = new ArrayList<>();
        if(StringUtils.isNotBlank(metricId)) {
            fieldOrTagList.addAll(metricsService.getFieldAndTagByMetricsIdV2(metricId));
        }
        extraFieldOrTag(fieldOrTagList, true);
        return fieldOrTagList;
    }

    private void initAlarmMatchMode(AlarmDefinition alarmDefinition) {
        if (null == alarmDefinition.getAlarmMatchMode()) {
            alarmDefinition.setAlarmMatchMode(AlarmMatchModeEnum.traversal);
        }
    }


    public void extraFieldOrTag(List<FieldOrTag> fieldOrTags, boolean needPre) {
        //add previous variables from the original variables
        if (needPre) {
            fieldOrTags.addAll(getPreVar(fieldOrTags.stream().filter(columnObj -> FieldOrTag.fieldMetaType.equals(columnObj.getMetaType())).collect(Collectors.toList())));
        }
        //add system variables
        for(FieldOrTag extraVar : EXTRA_FIELD) {
            if (!fieldOrTags.contains(extraVar)) {
                fieldOrTags.add(extraVar);
            }
        }
    }

    private List<FieldOrTag> getPreVar(List<FieldOrTag> fieldOrTags) {
        String preVarPrefix = alarmParaService.getParamValue(AlarmParaService.PRE_VAR_PREFIX);
        List<FieldOrTag> cpvList = new ArrayList<>();
        for (FieldOrTag fieldOrTag : fieldOrTags) {
            FieldOrTag newItem = new FieldOrTag(fieldOrTag);
            newItem.setCategory(CategoryType.DERIVED_VAR);
            newItem.setFieldOrTagName(preVarPrefix + fieldOrTag.getFieldOrTagName());
            cpvList.add(newItem);
        }
        return cpvList;
    }

    /**
     * Map frontend field names to database column names
     * @param field Frontend field name
     * @return Database column name
     */
    private String mapFieldToDatabaseColumn(String field) {
        if (field == null) {
            return "modifyTime";
        }

        // Field name mapping table
        switch (field.toLowerCase()) {
            case "alarmname":
            case "name":
                return "name";
            case "metricname":
            case "metric_name":
            case "metricId":
                // Note: This may need adjustment based on actual business logic
                return "metricId";
            case "createtime":
            case "create_time":
                return "createTime";
            case "modifytime":
            case "modify_time":
                return "modifyTime";
            case "enabled":
                return "enabled";
            case "status":
                return "status";
            case "creator":
                return "creator";
            case "editor":
                return "editor";
            default:
                // If field name doesn't match, return default field
                return "modifyTime";
        }
    }
    
    /**
     * Create AdCfg for AI conditions that don't exist
     */
    private void createAdCfgForAIConditions(AlarmDefinition definition) {
        if (CollectionUtils.isEmpty(definition.getRules())) {
            return;
        }
        
        // Map to record the mapping relationship between created adName and aiModelId
        Map<String, String> adNameToAiModelIdMap = new HashMap<>();
        
        for (AlarmRule alarmRule : definition.getRules()) {
            if (CollectionUtils.isEmpty(alarmRule.getConditions())) {
                continue;
            }
            
            for (RuleCondition ruleCondition : alarmRule.getConditions()) {
                if (ruleCondition.getConditionType() == ConditionType.AI) {
                    createAdCfgIfNotExists(ruleCondition, definition, adNameToAiModelIdMap);
                }
            }
        }
    }
    
    /**
     * Create new AdCfg when adName doesn't exist
     */
    private void createAdCfgIfNotExists(RuleCondition ruleCondition, AlarmDefinition definition, Map<String, String> adNameToAiModelIdMap) {
        if (StringUtils.isBlank(ruleCondition.getExpression())) {
            AlarmRuleSingleContent.AlarmRuleConditionExtension extension = JsonUtils.toObject(ruleCondition.getExtension(), AlarmRuleSingleContent.AlarmRuleConditionExtension.class);
            String adName = extension.getAdName();

            boolean isNameExist = !adNameToAiModelIdMap.containsKey(adName) && adCfgHandler.hasSameAdName(adName, List.of(definition.getTenantId()));
            if (StringUtils.isBlank(extension.getAdName()) || isNameExist) {
                throw new IllegalArgumentException("AI condition type must have expression or adName.");
            }

            // Check if AI Model with the same adName has already been created
            if (adNameToAiModelIdMap.containsKey(adName)) {
                // If already created, directly use the existing ID
                String existingAiModelId = adNameToAiModelIdMap.get(adName);
                ruleCondition.setExpression(existingAiModelId);
            } else {
                // If not created yet, create new AI Model and record the mapping relationship
                String newAiModelId = createNewAdCfg(ruleCondition, definition, adName);
                adNameToAiModelIdMap.put(adName, newAiModelId);
                log.info("Created new AI Model with adName: {}, aiModelId: {}", adName, newAiModelId);
            }
        }
    }
    
    /**
     * Create new AdCfg with complete information
     */
    private String createNewAdCfg(RuleCondition ruleCondition, AlarmDefinition definition, String adName) {
        try {
            AdCfgData adCfgData = new AdCfgData();
            
            // Set basic information from definition
            adCfgData.setAdName(adName);
            adCfgData.setMetricsId(definition.getMetricId());
            adCfgData.setMetricsName(definition.getDerivedMetric().getName());
            // Set aggPeriod from derivedMetric interval if available
            if (definition.getDerivedMetric() != null && definition.getDerivedMetric().getInterval() != null) {
                adCfgData.setAggPeriod(definition.getDerivedMetric().getInterval());
            } else {
                adCfgData.setAggPeriod(60);
            }
            adCfgData.setMetricsType(2);
            adCfgData.setFieldName(ruleCondition.getName());
            adCfgData.setAdStatus(true);
            adCfgData.setTenantId(definition.getTenantId());
            adCfgData.setTenantName(tenantHandler.getTenantNameById(definition.getTenantId()));
            adCfgData.setAnomalyDirectionType("3");
            adCfgData.setFillEmptyAsZero(false);
            adCfgData.setDataSourceType("SQL");
            // Set adTagCfgDataList from derivedMetric tags
            if (definition.getDerivedMetric() != null && StringUtils.isNotBlank(definition.getDerivedMetric().getTags())) {
                List<AdTagCfgData> adTagCfgDataList = new ArrayList<>();
                String[] tags = definition.getDerivedMetric().getTags().split(",");
                
                for (String tag : tags) {
                    if (StringUtils.isNotBlank(tag.trim())) {
                        AdTagCfgData adTagCfgData = new AdTagCfgData();
                        adTagCfgData.setTagName(tag.trim());
                        adTagCfgData.setAdTagType(2);
                        adTagCfgData.setTagValue(null);
                        adTagCfgDataList.add(adTagCfgData);
                    }
                }
                
                adCfgData.setAdTagCfgDataList(adTagCfgDataList);
            }
            // Call AiCfgService to add the new AdCfg
            ResponseObject responseObject = aiCfgService.addAdCfg(adCfgData);
            
            // Get the created AI Model ID from the response
            String aiModelId = (String) responseObject.getData();
            
            // Update ruleCondition's expression with the created AI Model ID
            ruleCondition.setExpression(aiModelId);
            
            log.info("Successfully created new AdCfg with adName: {}, fieldName: {}, metricsId: {}, aiModelId: {}", 
                    adName, ruleCondition.getName(), definition.getMetricId(), aiModelId);
            
            return aiModelId;
                    
        } catch (Exception e) {
            log.error("Failed to create new AdCfg for adName: {}, error: {}", adName, e.getMessage(), e);
            throw new IllegalArgumentException("Failed to create new AdCfg: " + e.getMessage());
        }
    }
}
