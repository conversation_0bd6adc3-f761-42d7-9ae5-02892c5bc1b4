package us.zoom.cube.site.core.alarm.insight;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.zoom.op.monitor.domain.alarm.AlarmRule;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.site.biz.AuthService;
import us.zoom.cube.site.biz.syspara.AlarmParaService;
import us.zoom.cube.site.core.SubEnvironmentHandler;
import us.zoom.cube.site.core.TenantHandler;
import us.zoom.cube.site.core.config.ClickhouseHandlerFactory;
import us.zoom.cube.site.lib.input.alarm.insight.AlarmRecordsInput;
import us.zoom.cube.site.lib.output.alarm.insight.AlarmDetailOutput;
import us.zoom.cube.site.lib.output.alarm.insight.AlarmNotificationOutput;
import us.zoom.cube.site.lib.output.alarm.insight.AlarmRecordsOutput;
import us.zoom.cube.site.lib.output.alarm.insight.AlarmRecordsOutputEntry;
import us.zoom.cube.site.lib.output.config.metrics.MetricsTags;
import us.zoom.infra.clickhouse.ClickhouseConst;
import us.zoom.infra.clickhouse.ClickhouseSqlUtil;
import us.zoom.infra.dao.model.CollectorFieldDO;
import us.zoom.infra.dao.model.SubAlarmDO;
import us.zoom.infra.dao.model.TenantDO;
import us.zoom.infra.dao.service.AlarmDAO;
import us.zoom.infra.dao.service.CollectorFieldDAO;
import us.zoom.infra.enums.AlarmRecordStatusEnum;
import us.zoom.infra.enums.AlarmSourceTypeEnum;
import us.zoom.infra.influx.model.alarm.AlarmMatchRecord;
import us.zoom.infra.influx.model.alarm.AlarmRuleHittedValue;
import us.zoom.infra.model.alarm.AlarmLevel;
import us.zoom.infra.utils.CommonSplitConstants;

import java.io.IOException;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: Starls Ding
 * @date: 2023/6/30 20:58
 * @desc:
 */
@Slf4j
@Component
public class AlarmInsightRecordsHandler {

    @Autowired
    private AuthService authService;

    @Autowired
    private SubEnvironmentHandler subEnvironmentHandler;

    @Autowired
    private ClickhouseHandlerFactory clickhouseHandlerFactory;

    @Autowired
    private AlarmDAO alarmDAO;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private AlarmRecordEventHandler alarmRecordEventHandler;

    @Autowired
    private AlarmParaService alarmParaService;

    @Autowired
    private CollectorFieldDAO collectorFieldDAO;

    @Autowired
    private TenantHandler tenantHandler;

    private static final String OPEN = "open";
    private static final String TRIGGERED = "triggered";
    private static final String ACKNOWLEDGED = "acknowledged";
    private static final String RESOLVED = "resolved";
    private static final String ALL_STATUS = "allStatus";

    private static final String COLUMN_COUNT = "count";

    public static final String SILENCED = "silenced";

    public static final String SUPPRESSED = "suppressed";

    private static final String BASE_FETCH_RECORDS_SQL_TEMPLATE =
            "    (select x.* from %s.%s  as x join ( select id, MAX(gmtCreate) " +
                    "AS maxCreate  from %s.%s  where 1=1  %s and alarmName='%s' and time>=%d and time<=%d %s group by \"id\") " +
                    " as y on x.id = y.id and  x.gmtCreate = y.maxCreate where 1=1  %s and x.alarmName='%s' and x.time>=%d and x.time<=%d " +
                    " %s limit 1 by \"id\",\"alarmStatus\" ) as ta " +
                    "    global join " +
                    "    (select * from %s.%s where 1=1 %s %s and alarmName='%s' and time>=%d and time<=%d) as tb " +
                    "    on ta.id=tb.id ";

    private static final String FETCH_TOTAL_SQL_TEMPLATE = "select count(1) as \"count\" from " + BASE_FETCH_RECORDS_SQL_TEMPLATE;

    /**
      SQL Template:
      select * from (
          select * from (                                      //query the latest status of each alarm record
              select * from "Meeting_Web_marketplace"."alarm_cube_match_record_event_table"
              where 1 = 1
              and alarmLevel in ('fatal', 'error', 'warn', 'info')
              and alarmName = 'CommonAD_Marketplace_API_Response_Time_Alert'
              and time >= toDateTime('2024-01-07 00:00:00')
              and time <= toDateTime('2024-01-07 08:00:00')
              and alarmStatus != 'suppressed'                  //if has duplicate data, need filter suppressed status
              order by "gmtCreate" desc
              limit 1 by "id"
          )
          where alarmStatus in ('triggered', 'acknowledged', 'resolved')
      ) as ta global
      join (
          select * from "Meeting_Web_marketplace"."alarm_cube_match_service_record_table"
          where 1 = 1
          and (
              tagMap ['clusterId'] = 'us01'
              or tagMap ['clusterId'] = 'eu02'
          )
          and alarmName = 'CommonAD_Marketplace_API_Response_Time_Alert'
          and time >= toDateTime('2024-01-07 00:00:00')
          and time <= toDateTime('2024-01-07 08:00:00')
      ) as tb on ta.id = tb.id;
     */
    private static final String FETCH_RECORDS_SQL_TEMPLATE = "select * from " + BASE_FETCH_RECORDS_SQL_TEMPLATE + " order by time desc limit %d,%d";

    public AlarmRecordsOutput getAlarmRecords(AlarmRecordsInput alarmRecordsInput, boolean hasPiiPermission) {

        validateInput(alarmRecordsInput);

        String queryStatus = Optional.of(alarmRecordsInput).map(AlarmRecordsInput::getFilterItemValues)
                .map(AlarmRecordsInput.FilterItemValues::getStatus).orElse(null);

        long total = fetchRecordsTotalCount(alarmRecordsInput);

        List<AlarmRecordsOutputEntry> recordsOutputList = fetchRecordsList(alarmRecordsInput);

        //merge record status from db
        if (alarmParaService.getParamBooleanValue(AlarmParaService.ALARM_INSIGHT_MERGE_RESULT, false))  {
            mergeOutput(recordsOutputList, queryStatus, alarmRecordsInput);
        }

        String tenantId = alarmRecordsInput.getTenantId();
        if (StringUtils.isBlank(tenantId)) {
            if (StringUtils.isNotBlank(alarmRecordsInput.getServiceName())) {
                try {
                    TenantDO tenant = tenantHandler.getTenantByName(alarmRecordsInput.getServiceName());
                    if (tenant != null && StringUtils.isNotBlank(tenant.getId())) {
                        tenantId = tenant.getId();
                    }
                } catch (Exception e) {
                    log.error("Error resolving tenantId from serviceName {}. Error: {}", alarmRecordsInput.getServiceName(), e.getMessage());
                    tenantId = null;
                }
            }
        }

        List<CollectorFieldDO> collectorFieldDOS;
        if (StringUtils.isBlank(tenantId)) {
            collectorFieldDOS = new ArrayList<>();
        } else {
            collectorFieldDOS = collectorFieldDAO.listPiiFieldByAlarm(tenantId, alarmRecordsInput.getAlarmName());
        }
        
        List<MetricsTags> tagList;
        for (AlarmRecordsOutputEntry alarmRecordsOutputEntry : recordsOutputList) {
            tagList = new ArrayList<>();
            Map<String, Object> tagMap = alarmRecordsOutputEntry.getMetricsTagMap();
            if (MapUtils.isNotEmpty(tagMap)) {
                for (Map.Entry<String, Object> entry : tagMap.entrySet()) {
                    String tagName = entry.getKey();
                    boolean needMask;
                    
                    if (StringUtils.isBlank(tenantId) || CollectionUtils.isEmpty(collectorFieldDOS)) {
                        needMask = !hasPiiPermission;
                    } else {
                        boolean isPii = collectorFieldDOS.stream().anyMatch(collectorField -> tagName.equals(collectorField.getTargetField()));
                        needMask = isPii && !hasPiiPermission;
                    }
                    tagList.add(new MetricsTags(tagName, needMask ? "*" : entry.getValue(), needMask));
                }
                tagList.sort(Comparator.comparing(tag -> tag.getName()));
                alarmRecordsOutputEntry.setMetricsTags(tagList);
            }
        }

        return new AlarmRecordsOutput(alarmRecordsInput.getPageIndex(), alarmRecordsInput.getPageSize(), total, recordsOutputList);
    }


    /**
     * Merge with the latest record in the database
     */
    private void mergeOutput(List<AlarmRecordsOutputEntry> recordsOutputList, String queryStatus, AlarmRecordsInput alarmRecordsInput) {

        String alarmId = alarmRecordsInput.getAlarmId();

        if (StringUtils.isBlank(alarmId) && !CollectionUtils.isEmpty(recordsOutputList)) {
            alarmId = Optional.of(recordsOutputList).map(l -> l.get(0)).map(AlarmRecordsOutputEntry::getAlarmId).orElse(null);
        }
        if (StringUtils.isBlank(alarmId)) {
            return ;
        }
        List<String> statsList = covertQueryStatusToList(queryStatus);
        Map<String, AlarmRecordsOutputEntry> recentMap = alarmRecordEventHandler.getRecentChangeRecord(alarmId);
        if (CollectionUtils.isEmpty(recentMap)) {
            return ;
        }
        //1. delete record
        Iterator<AlarmRecordsOutputEntry> iterator = recordsOutputList.iterator();
        while (iterator.hasNext()) {
            AlarmRecordsOutputEntry curr = iterator.next();
            String id = curr.getId();
            AlarmRecordsOutputEntry outputEntry = recentMap.get(id);
            if (Objects.nonNull(outputEntry)) {
                iterator.remove();
            }
        }
        //2. add record
        for (String changedId : recentMap.keySet()) {
            AlarmRecordsOutputEntry outputEntry = recentMap.get(changedId);
            //new status in query status list
            if (statsList.contains(outputEntry.getStatus())) {
                recordsOutputList.add(outputEntry);
            }
        }
        recordsOutputList.sort((o1, o2) -> (int)(o2.getTime() - o1.getTime()));
    }

    private List<String> covertQueryStatusToList(String alarmStatus) {
        switch (alarmStatus) {
            case OPEN:
                return Arrays.asList(TRIGGERED, ACKNOWLEDGED);
            case TRIGGERED:
                return Collections.singletonList(TRIGGERED);
            case ACKNOWLEDGED:
                return Collections.singletonList(ACKNOWLEDGED);
            case RESOLVED:
                return Collections.singletonList(RESOLVED);
            case ALL_STATUS:
                return Arrays.asList(TRIGGERED, ACKNOWLEDGED, RESOLVED, SILENCED);
            case SILENCED:
                return Collections.singletonList(SILENCED);
            case SUPPRESSED:
                return Collections.singletonList(SUPPRESSED);
            default:
                return new ArrayList<>();
        }
    }


    public AlarmRecordsOutput getSuppressedAlarmRecords(AlarmRecordsInput alarmRecordsInput) {

        Assert.isTrue(StringUtils.isNotBlank(alarmRecordsInput.getServiceName()), "Service can't be empty!");
        Assert.isTrue(StringUtils.isNotBlank(alarmRecordsInput.getPreRecordId()), "preRecordId can't be empty!");

        long total = fetchSuppressedRecordsTotalCount(alarmRecordsInput);

        List<AlarmRecordsOutputEntry> recordsOutputList = fetchSuppressedRecordsList(alarmRecordsInput);

        return new AlarmRecordsOutput(alarmRecordsInput.getPageIndex(), alarmRecordsInput.getPageSize(), total, recordsOutputList);
    }

    private static final String RECORD_SQL_TEMPLATE = "select amsr.*, acmre.alarmStatus, acmre.ackExpireTime, acmre.gmtCreate as eventGmtCreate from ( select * from %s.%s where id = '%s') amsr " +
            "global join " +
            "(select id, alarmStatus, ackExpireTime, gmtCreate from (select id, alarmStatus, ackExpireTime, gmtCreate, row_number() over(partition by id order by gmtCreate desc) r from %s.%s where id = '%s') where r = 1) acmre " +
            " on amsr.id = acmre.id";

    public AlarmMatchRecord getAlarmRecordById(String serviceName, String alarmRecordId) {
        final String dbName = ClickhouseSqlUtil.encodeClickhouseName(serviceName);
        String sql = String.format(RECORD_SQL_TEMPLATE, dbName, ClickhouseConst.ALARM_CUBE_MATCH_SERVICE_RECORD_TABLE, alarmRecordId
                , dbName, ClickhouseConst.ALARM_CUBE_MATCH_RECORD_EVENT_TABLE, alarmRecordId);
        List<Map<String, Object>> queryResult = clickhouseHandlerFactory.get().query(serviceName, sql, subEnvironmentHandler.getAlarmInsightEnv(serviceName));
        Assert.notEmpty(queryResult, "Can't find the alarm record according to the id:" + alarmRecordId);
        return convertChResultToRecord(queryResult.get(0));
    }

    /**
     * Get alarm record by ID with time range filter
     * 
     * @param serviceName the service name
     * @param alarmRecordId the alarm record ID
     * @param beginTime begin time in milliseconds (optional)
     * @param endTime end time in milliseconds (optional)
     * @return alarm match record
     */
    public AlarmMatchRecord getAlarmRecordById(String serviceName, String alarmRecordId, Long beginTime, Long endTime) {
        final String dbName = ClickhouseSqlUtil.encodeClickhouseName(serviceName);
        
        // Build time range condition if provided
        String timeCondition = "";
        if (beginTime != null && endTime != null) {
            long beginSeconds = beginTime / 1000;
            long endSeconds = endTime / 1000;
            timeCondition = String.format(" AND time >= %d AND time <= %d", beginSeconds, endSeconds);
        }
        
        String sql = String.format(RECORD_SQL_TEMPLATE, dbName, ClickhouseConst.ALARM_CUBE_MATCH_SERVICE_RECORD_TABLE, alarmRecordId + timeCondition
                , dbName, ClickhouseConst.ALARM_CUBE_MATCH_RECORD_EVENT_TABLE, alarmRecordId + timeCondition);
        List<Map<String, Object>> queryResult = clickhouseHandlerFactory.get().query(serviceName, sql, subEnvironmentHandler.getAlarmInsightEnv(serviceName));
        Assert.notEmpty(queryResult, "Can't find the alarm record according to the id:" + alarmRecordId);
        return convertChResultToRecord(queryResult.get(0));
    }


    private static final String RECORD_NOTIFICATION_SQL_TEMPLATE = "select * from %s.%s where alarmMatchRecordId = '%s' order by channelPriority";

    public List<AlarmNotificationOutput> getAlarmRecordNotifications(String serviceName, String alarmRecordId, boolean auth) {
        final String dbName = ClickhouseSqlUtil.encodeClickhouseName(serviceName);
        String sql = String.format(RECORD_NOTIFICATION_SQL_TEMPLATE, dbName, ClickhouseConst.ALARM_MATCH_RECORD_NOTIFICATION_TABLE_NAME, alarmRecordId);
        List<Map<String, Object>> queryResult = clickhouseHandlerFactory.get().query(serviceName, sql, subEnvironmentHandler.getAlarmInsightEnv(serviceName));
        return convertToNotificationsOutput(queryResult, auth);
    }

    private List<AlarmNotificationOutput> convertToNotificationsOutput(List<Map<String, Object>> queryFields, boolean auth) {
        List<AlarmNotificationOutput> outputs = new ArrayList<>();
        for (Map<String, Object> queryField : queryFields) {
            AlarmNotificationOutput notification = new AlarmNotificationOutput();
            notification.setId(Optional.ofNullable(queryField.get("id")).map(Object::toString).orElse(null));
            notification.setNotifyTitle(Optional.ofNullable(queryField.get("title")).map(Object::toString).map(value -> !auth && value != null ? "*" : value).orElse(null));
            notification.setNotifyContent(Optional.ofNullable(queryField.get("content")).map(Object::toString).map(value -> !auth && value != null ? "*" : value).orElse(null));
            notification.setNoticedChannel(Optional.ofNullable(queryField.get("channelName")).map(Object::toString).orElse(null));
            notification.setNoticedChannelType(Optional.ofNullable(queryField.get("channelType")).map(Object::toString).orElse(null));
            outputs.add(notification);
        }
        return outputs;
    }

    public AlarmDetailOutput convertToAlarmDetailOutput(AlarmMatchRecord alarmMatchRecord, Map<String, SubAlarmDO> subAlarmMap) {
        AlarmDetailOutput alarmDetailOutput = new AlarmDetailOutput();
        alarmDetailOutput.setId(alarmMatchRecord.getId());
        alarmDetailOutput.setAlarmLevel(alarmMatchRecord.getAlarmLevel().getLevel());
        alarmDetailOutput.setAlarmId(alarmMatchRecord.getAlarmId());
        alarmDetailOutput.setAlarmRuleId(alarmMatchRecord.getAlarmRuleId());
        alarmDetailOutput.setStatus(alarmMatchRecord.getStatus().name());
        String alarmName = alarmMatchRecord.getAlarmName();
        alarmDetailOutput.setAlarmName(alarmName);
        if (null != subAlarmMap && subAlarmMap.containsKey(alarmName)) {
            alarmDetailOutput.setHasSubAlarm(true);
            alarmDetailOutput.setSubAlarmId(subAlarmMap.get(alarmName).getSubAlarmId());
            alarmDetailOutput.setSubAlarmName(subAlarmMap.get(alarmName).getSubAlarmName());
        }
        alarmDetailOutput.setMetricsTags(alarmMatchRecord.getMetricsTags());
        alarmDetailOutput.setServiceName(alarmMatchRecord.getTenantName());
        alarmDetailOutput.setMetricsName(alarmMatchRecord.getMetricsName());
        alarmDetailOutput.setTs(alarmMatchRecord.getTime());
        alarmDetailOutput.setHitRule(Optional.ofNullable(alarmMatchRecord.getHittedRule()).map(AlarmRule::getConditions).orElse(null));

        //wait confirm if can delete
        alarmDetailOutput.setCanMarkResolved(false);
        return alarmDetailOutput;
    }

    public void addAlarmRecordEvent(AlarmMatchRecord alarmMatchRecord){
        String tenantName = alarmMatchRecord.getTenantName();
        try {
            Map<String, Object> columns =new HashMap<>();
            columns.put("id", (alarmMatchRecord.getId()));
            columns.put("gmtCreate", alarmMatchRecord.getEventGmtCreate());
            columns.put("alarmStatus", alarmMatchRecord.getStatus().getWithNoPrefix());
            columns.put("ackExpireTime", Optional.ofNullable(alarmMatchRecord.getAckExpireTime()).map(Timestamp::new).orElse(null));
            columns.put("alarmId", alarmMatchRecord.getAlarmId());
            columns.put("alarmName", alarmMatchRecord.getAlarmName());
            columns.put("alarmLevel", alarmMatchRecord.getAlarmLevel().getLevel());
            columns.put("metricsId", alarmMatchRecord.getMetricsId());
            columns.put("metricsName", alarmMatchRecord.getMetricsName());
            columns.put("event", alarmMatchRecord.getAlarmEventType().name());
            columns.put("tagKey", alarmMatchRecord.getTagKey());
            Timestamp metricTs = new Timestamp(alarmMatchRecord.getTime() == 0 ? System.currentTimeMillis() : alarmMatchRecord.getTime());
            long recordMemorySize = ClickhouseSqlUtil.approximateSize(columns);
            String dbName = ClickhouseSqlUtil.toClickhouseName(tenantName);
            clickhouseHandlerFactory.getClickhouseWriter().write(dbName, ClickhouseConst.ALARM_CUBE_MATCH_RECORD_EVENT_TABLE, columns, metricTs, recordMemorySize, false, true);
        }catch (Exception e){
            log.error("Error when insert alarm record to clickhouse",e);
        }
    }

    public Map<String, SubAlarmDO> getConfigSubAlarmsByServiceId(String serviceId) {
        List<SubAlarmDO> subAlarms = alarmDAO.getSubAlarmsByServiceId(serviceId);
        return subAlarms.stream().collect(Collectors.toMap(SubAlarmDO::getAlarmName, o -> o));
    }


    private AlarmMatchRecord convertChResultToRecord(Map<String, Object> queryFields) {

        AlarmMatchRecord alarmMatchRecord = new AlarmMatchRecord();
        String alarmRecordId = queryFields.get("id").toString();
        alarmMatchRecord.setId(alarmRecordId);
        alarmMatchRecord.setAckExpireTime((Optional.of(((Timestamp)(queryFields.get("ackExpireTime"))).getTime()).orElse(null)));
        alarmMatchRecord.setAlarmId(queryFields.get("alarmId").toString());
        alarmMatchRecord.setAlarmName(queryFields.get("alarmName").toString());
        alarmMatchRecord.setAlarmLevel(AlarmLevel.fromLevel(queryFields.get("alarmLevel").toString()));
        alarmMatchRecord.setAlarmRuleId(queryFields.get("alarmRuleId").toString());
        alarmMatchRecord.setAlarmSourceType(AlarmSourceTypeEnum.fromValue(Integer.parseInt(queryFields.get("alarmSourceType").toString())));
        alarmMatchRecord.setMetricsId(queryFields.get("metricsId").toString());
        alarmMatchRecord.setMetricsName(queryFields.get("metricsName").toString());
        alarmMatchRecord.setTenantName(queryFields.get("serviceName").toString());
        alarmMatchRecord.setTime(((Timestamp) queryFields.get("time")).getTime());
        alarmMatchRecord.setTagKey(queryFields.get("tagKey").toString());
        alarmMatchRecord.setStatus(AlarmRecordStatusEnum.getFromNoPrefixName(queryFields.get("alarmStatus").toString()));
        //hitted rule
        String hittedRule = queryFields.get("hittedRule").toString();
        if (StringUtils.isNotBlank(hittedRule)) {
            try {
                alarmMatchRecord.setHittedRule(objectMapper.readValue(hittedRule, AlarmRule.class));
            } catch (IOException e) {
                log.error(String.format("convert alarm rule error! alarmRecordId:%s", alarmRecordId), e);
            }
        }

        //hitted value
        String hittedValue = queryFields.get("hittedValue").toString();
        if (StringUtils.isNotBlank(hittedValue)) {
            try {
                alarmMatchRecord.setHittedValue(objectMapper.readValue(hittedValue, AlarmRuleHittedValue.class));
            } catch (IOException e) {
                log.error(String.format("convert alarm hitted value error! alarmRecordId:%s", alarmRecordId), e);
            }
        }

        alarmMatchRecord.setPreRecordId(queryFields.get("preRecordId").toString());
        alarmMatchRecord.setEventGmtCreate(((Timestamp) queryFields.get("eventGmtCreate")));
        alarmMatchRecord.setRecordGmtCreate(((Timestamp) queryFields.get("gmtCreate")));
        if (Objects.nonNull(queryFields.get("tagMap"))) {
            alarmMatchRecord.setMetricsTags((Map)queryFields.get("tagMap"));
        }

        //extend info
        Optional.ofNullable(queryFields.get("extendInfo"))
                .map(Object::toString)
                .filter(StringUtils::isNotBlank)
                .ifPresent(extendInfo -> {
                    try {
                        AlarmMatchRecord.RecordExtendInfo recordExtendInfo = objectMapper.readValue(extendInfo, AlarmMatchRecord.RecordExtendInfo.class);
                        alarmMatchRecord.setGroupTagKey(recordExtendInfo.getGroupTagKey());
                        alarmMatchRecord.setNotifyGroupKey(recordExtendInfo.getNotifyGroupKey());
                    } catch (Exception ignored) {
                    }
                });
        return alarmMatchRecord;
    }

    private void validateInput(AlarmRecordsInput alarmRecordsInput) {
        Assert.isTrue(StringUtils.isNotBlank(alarmRecordsInput.getServiceName()), "Service can't be empty!");
        Assert.isTrue(StringUtils.isNotBlank(alarmRecordsInput.getAlarmName()), "Alarm name can't be empty!");
        Assert.notNull(alarmRecordsInput.getFilterItemValues(), "Filter items can't be null!");
        Assert.isTrue(StringUtils.isNotBlank(alarmRecordsInput.getFilterItemValues().getStatus()), "Status chose by user can't be empty!");
        Assert.isTrue(alarmRecordsInput.getPageIndex() > 0, "page index should be positive number!");
        Assert.isTrue(alarmRecordsInput.getPageSize() > 0, "page size should be positive number!");
    }

    private long fetchRecordsTotalCount(AlarmRecordsInput alarmRecordsInput) {
        final long beginSeconds = alarmRecordsInput.getBegin() / 1000;
        final long endSeconds = alarmRecordsInput.getEnd() / 1000;
        final String dbName = ClickhouseSqlUtil.encodeClickhouseName(alarmRecordsInput.getServiceName());
        final String alarmName = alarmRecordsInput.getAlarmName();

        String fetchTotalCountSql = String.format(FETCH_TOTAL_SQL_TEMPLATE,
                dbName, ClickhouseConst.ALARM_CUBE_MATCH_RECORD_EVENT_TABLE, dbName, ClickhouseConst.ALARM_CUBE_MATCH_RECORD_EVENT_TABLE,
                buildFilterCondition(alarmRecordsInput.getFilterItemValues()), alarmName, beginSeconds, endSeconds,
                buildAlarmRecordIdCondition(alarmRecordsInput.getAlarmRecordId()),
                buildFilterConditionAsAlias(alarmRecordsInput.getFilterItemValues()),
                alarmName,beginSeconds, endSeconds,
                buildStatusCondition(alarmRecordsInput.getFilterItemValues().getStatus()),
                dbName, ClickhouseConst.ALARM_CUBE_MATCH_SERVICE_RECORD_TABLE,
                buildTagMapCondition(alarmRecordsInput.getFilterTagValues()),
                buildInitialStatusCondition(alarmRecordsInput.getFilterItemValues().getStatus()),
                alarmName, beginSeconds, endSeconds);
        List<Map<String, Object>> queryResult = clickhouseHandlerFactory.get().query(alarmRecordsInput.getServiceName(), fetchTotalCountSql,
                subEnvironmentHandler.getAlarmInsightEnv(alarmRecordsInput.getServiceName()));
        if (CollectionUtils.isEmpty(queryResult)) {
            return 0L;
        } else {
            return Long.valueOf(queryResult.get(0).get(COLUMN_COUNT).toString());
        }
    }

    private List<AlarmRecordsOutputEntry> fetchRecordsList(AlarmRecordsInput alarmRecordsInput) {
        final long beginSeconds = alarmRecordsInput.getBegin() / 1000;
        final long endSeconds = alarmRecordsInput.getEnd() / 1000;
        final String dbName = ClickhouseSqlUtil.encodeClickhouseName(alarmRecordsInput.getServiceName());
        final String alarmName = alarmRecordsInput.getAlarmName();
        final int pageIndex = alarmRecordsInput.getPageIndex();
        final int pageSize = alarmRecordsInput.getPageSize();

        String fetchRecordsSql = String.format(FETCH_RECORDS_SQL_TEMPLATE,
                dbName, ClickhouseConst.ALARM_CUBE_MATCH_RECORD_EVENT_TABLE, dbName, ClickhouseConst.ALARM_CUBE_MATCH_RECORD_EVENT_TABLE,
                buildFilterCondition(alarmRecordsInput.getFilterItemValues()), alarmName, beginSeconds, endSeconds, 
                buildAlarmRecordIdCondition(alarmRecordsInput.getAlarmRecordId()),
                buildFilterConditionAsAlias(alarmRecordsInput.getFilterItemValues()),
                alarmName,beginSeconds, endSeconds, buildStatusCondition(alarmRecordsInput.getFilterItemValues().getStatus()),
                dbName, ClickhouseConst.ALARM_CUBE_MATCH_SERVICE_RECORD_TABLE,
                buildTagMapCondition(alarmRecordsInput.getFilterTagValues()),
                buildInitialStatusCondition(alarmRecordsInput.getFilterItemValues().getStatus()),
                alarmName, beginSeconds, endSeconds, (pageIndex - 1) * pageSize, pageSize
        );

        List<Map<String, Object>> queryResult = clickhouseHandlerFactory.get().query(alarmRecordsInput.getServiceName(), fetchRecordsSql,
                subEnvironmentHandler.getAlarmInsightEnv(alarmRecordsInput.getServiceName()));
        List<AlarmRecordsOutputEntry> recordsOutputList = mappingResultToOutput(queryResult);
        return recordsOutputList;
    }


    private static final String FETCH_SUPPRESSED_TOTAL_SQL_TEMPLATE = "select count(1) as \"count\" from %s.%s where preRecordId = '%s' and initialStatus = 'suppressed'";

    private long fetchSuppressedRecordsTotalCount(AlarmRecordsInput alarmRecordsInput) {
        final String dbName = ClickhouseSqlUtil.encodeClickhouseName(alarmRecordsInput.getServiceName());
        final String preRecordId = alarmRecordsInput.getPreRecordId();

        String fetchSuppressedTotalCountSql = String.format(FETCH_SUPPRESSED_TOTAL_SQL_TEMPLATE, dbName, ClickhouseConst.ALARM_CUBE_MATCH_SERVICE_RECORD_TABLE,preRecordId);
        List<Map<String, Object>> queryResult = clickhouseHandlerFactory.get().query(alarmRecordsInput.getServiceName(), fetchSuppressedTotalCountSql,
                subEnvironmentHandler.getAlarmInsightEnv(alarmRecordsInput.getServiceName()));
        if (CollectionUtils.isEmpty(queryResult)) {
            return 0L;
        } else {
            return Long.parseLong(queryResult.get(0).get(COLUMN_COUNT).toString());
        }
    }

    private static final String FETCH_SUPPRESSED_RECORDS_SQL_TEMPLATE = "select * from %s.%s where preRecordId = '%s' and initialStatus = 'suppressed' order by gmtCreate desc limit %d,%d";

    private List<AlarmRecordsOutputEntry> fetchSuppressedRecordsList(AlarmRecordsInput alarmRecordsInput) {
        final String dbName = ClickhouseSqlUtil.encodeClickhouseName(alarmRecordsInput.getServiceName());
        final String preRecordId = alarmRecordsInput.getPreRecordId();

        final int pageIndex = alarmRecordsInput.getPageIndex();
        final int pageSize = alarmRecordsInput.getPageSize();


        String fetchRecordsSql = String.format(FETCH_SUPPRESSED_RECORDS_SQL_TEMPLATE, dbName,
                ClickhouseConst.ALARM_CUBE_MATCH_SERVICE_RECORD_TABLE, preRecordId, (pageIndex - 1) * pageSize, pageSize);

        List<Map<String, Object>> queryResult = clickhouseHandlerFactory.get().query(alarmRecordsInput.getServiceName(), fetchRecordsSql,
                subEnvironmentHandler.getAlarmInsightEnv(alarmRecordsInput.getServiceName()));
        if (CollectionUtils.isEmpty(queryResult)) {
            return new ArrayList<>();
        }

        String tenantId = alarmRecordsInput.getTenantId();
        if (StringUtils.isBlank(tenantId)) {
            if (StringUtils.isNotBlank(alarmRecordsInput.getServiceName())) {
                try {
                    TenantDO tenant = tenantHandler.getTenantByName(alarmRecordsInput.getServiceName());
                    if (tenant != null && StringUtils.isNotBlank(tenant.getId())) {
                        tenantId = tenant.getId();
                    }
                } catch (Exception e) {
                    log.error("Error resolving tenantId from serviceName {} for suppressed records. Error: {}", alarmRecordsInput.getServiceName(), e.getMessage());
                }
            }
        }

        List<CollectorFieldDO> collectorFieldDOS;
        if (StringUtils.isBlank(tenantId)) {
            collectorFieldDOS = new ArrayList<>();
        } else {
            collectorFieldDOS = collectorFieldDAO.listPiiFieldByAlarm(tenantId, alarmRecordsInput.getAlarmName());
        }
        
        return mappingSuppressedRecordResultToOutput(queryResult, collectorFieldDOS, alarmRecordsInput.isPiiPermission());
    }


    private String buildTagMapCondition(List<AlarmRecordsInput.FilterTagValues> filterTagValueList) {
        StringBuilder tagMapCond = new StringBuilder();
        if (!CollectionUtils.isEmpty(filterTagValueList)) {
            for (AlarmRecordsInput.FilterTagValues filterTagValues : filterTagValueList) {
                String tagName = filterTagValues.getName();
                if (!CollectionUtils.isEmpty(filterTagValues.getValues())) {
                    tagMapCond.append(" and (");
                    //query format -> mapColumn['name']='value'，and we will use string as all the value object
                    tagMapCond.append(String.join(" or ", filterTagValues.getValues().stream().map(value -> "tagMap['" + tagName + "']='" + value + "'").collect(Collectors.toList())));
                    tagMapCond.append(" ) ");
                }
            }
        }
        return tagMapCond.toString();
    }

    private String buildFilterCondition(AlarmRecordsInput.FilterItemValues filterItemValues) {
        StringBuilder condition = new StringBuilder();
        if (null != filterItemValues) {
            //level
            List<String> levels = filterItemValues.getLevel();
            if (!CollectionUtils.isEmpty(levels)) {
                condition.append(String.format("and alarmLevel in ( %s ) ",
                        String.join(CommonSplitConstants.COMMA_SPLIT,
                                levels.stream().map(peril -> "'" + AlarmLevel.getLevel(peril).getLevel() + "'").collect(Collectors.toList()))));
            }
        }
        return condition.toString();
    }

    private String buildFilterConditionAsAlias(AlarmRecordsInput.FilterItemValues filterItemValues) {
        StringBuilder condition = new StringBuilder();
        if (null != filterItemValues) {
            //level
            List<String> levels = filterItemValues.getLevel();
            if (!CollectionUtils.isEmpty(levels)) {
                condition.append(String.format("and x.alarmLevel in ( %s ) ",
                        String.join(CommonSplitConstants.COMMA_SPLIT,
                                levels.stream().map(peril -> "'" + AlarmLevel.getLevel(peril).getLevel() + "'").collect(Collectors.toList()))));
            }
        }
        return condition.toString();
    }

    private String buildStatusCondition(String alarmStatus) {
        List<String> statusCodes = new ArrayList<>();
        if (OPEN.equals(alarmStatus)) {
            statusCodes = Arrays.asList(TRIGGERED, ACKNOWLEDGED);
        } else if (TRIGGERED.equals(alarmStatus)) {
            statusCodes = List.of(TRIGGERED);
        } else if (ACKNOWLEDGED.equals(alarmStatus)) {
            statusCodes = List.of(ACKNOWLEDGED);
        } else if (RESOLVED.equals(alarmStatus)) {
            statusCodes = List.of(RESOLVED);
        } else if (ALL_STATUS.equals(alarmStatus)) {
            statusCodes = Arrays.asList(TRIGGERED, ACKNOWLEDGED, RESOLVED,  SILENCED);
        } else if(SILENCED.equals(alarmStatus)) {
            statusCodes = List.of(SILENCED);
        } else if(SUPPRESSED.equals(alarmStatus)) {
            statusCodes = List.of(SUPPRESSED);
        } else {
            return StringUtils.EMPTY;
        }
        return String.format(" and x.alarmStatus in ( %s ) ",
                String.join(CommonSplitConstants.COMMA_SPLIT, statusCodes.stream().map(s -> "'" + s + "'").collect(Collectors.toList())));
    }

    private String buildInitialStatusCondition(String alarmStatus) {
        List<String> statusCodes = new ArrayList<>();
        if (OPEN.equals(alarmStatus)) {
            statusCodes = Arrays.asList(TRIGGERED, ACKNOWLEDGED);
        } else if (TRIGGERED.equals(alarmStatus)) {
            statusCodes = List.of(TRIGGERED);
        } else if (ACKNOWLEDGED.equals(alarmStatus)) {
            statusCodes = Arrays.asList(ACKNOWLEDGED, TRIGGERED);
        } else if (RESOLVED.equals(alarmStatus)) {
            statusCodes =Arrays.asList(RESOLVED, TRIGGERED);
        } else if (ALL_STATUS.equals(alarmStatus)) {
            statusCodes = Arrays.asList(TRIGGERED, ACKNOWLEDGED, RESOLVED, SILENCED);
        } else if(SILENCED.equals(alarmStatus)) {
            statusCodes = List.of(SILENCED);
        } else if(SUPPRESSED.equals(alarmStatus)) {
            statusCodes = List.of(SUPPRESSED);
        } else {
            return StringUtils.EMPTY;
        }
        return String.format(" and initialStatus in ( %s ) ",
                String.join(CommonSplitConstants.COMMA_SPLIT, statusCodes.stream().map(s -> "'" + s + "'").collect(Collectors.toList())));
    }

    private String buildAlarmRecordIdCondition(String alarmRecordId) {
        return StringUtils.isNotBlank(alarmRecordId) ? "and id = '" + alarmRecordId + "'" : "";
    }

    /**
     * Convert single query result to AlarmRecordsOutputEntry (without PII processing)
     * This method ensures consistent format for hittedRule field
     * 
     * @param resultEntry the query result entry
     * @return alarm records output entry
     */
    public AlarmRecordsOutputEntry convertQueryResultToOutputEntry(Map<String, Object> resultEntry) {
        AlarmRecordsOutputEntry alarmRecordsOutputEntry = new AlarmRecordsOutputEntry();
        alarmRecordsOutputEntry.setId(Optional.ofNullable(resultEntry.get("id")).map(Object::toString).orElse(null));
        alarmRecordsOutputEntry.setAlarmLevel(Optional.ofNullable(resultEntry.get("alarmLevel")).map(Object::toString).orElse(null));
        alarmRecordsOutputEntry.setAlarmId(Optional.ofNullable(resultEntry.get("alarmId")).map(Object::toString).orElse(null));
        alarmRecordsOutputEntry.setAlarmName(Optional.ofNullable(resultEntry.get("alarmName")).map(Object::toString).orElse(null));
        alarmRecordsOutputEntry.setAlarmRuleId(Optional.ofNullable(resultEntry.get("alarmRuleId")).map(Object::toString).orElse(null));
        alarmRecordsOutputEntry.setHittedRule(Optional.ofNullable(resultEntry.get("hittedRule")).map(Object::toString).orElse(null));
        alarmRecordsOutputEntry.setHittedValue(Optional.ofNullable(resultEntry.get("hittedValue")).map(Object::toString).orElse(null));
        alarmRecordsOutputEntry.setStatus(Optional.ofNullable(resultEntry.get("alarmStatus")).map(Object::toString).orElse(null));
        alarmRecordsOutputEntry.setMetricsId(Optional.ofNullable(resultEntry.get("metricsId")).map(Object::toString).orElse(null));
        alarmRecordsOutputEntry.setMetricsName(Optional.ofNullable(resultEntry.get("metricsName")).map(Object::toString).orElse(null));
        alarmRecordsOutputEntry.setMetricsTagMap((Map)resultEntry.get("tagMap"));
        alarmRecordsOutputEntry.setAckExpireTime(Optional.ofNullable(resultEntry.get("ackExpireTime"))
                .map(obj -> ((Timestamp) obj).getTime())
                .orElse(0L));
        alarmRecordsOutputEntry.setTime(Optional.ofNullable(resultEntry.get("time"))
                .map(obj -> ((Timestamp) obj).getTime())
                .orElse(0L));
        alarmRecordsOutputEntry.setEventGmtCreate(Optional.ofNullable(resultEntry.get("eventGmtCreate"))
                .map(obj -> ((Timestamp) obj).getTime())
                .orElse(0L));
        alarmRecordsOutputEntry.setAlarmSilenceId(Optional.ofNullable(resultEntry.get("alarmSilenceId")).map(Object::toString).orElse(null));
        alarmRecordsOutputEntry.setAlarmSilenceName(Optional.ofNullable(resultEntry.get("alarmSilenceName")).map(Object::toString).orElse(null));
        alarmRecordsOutputEntry.setRecordGmtCreate(Optional.ofNullable(resultEntry.get("tb.gmtCreate"))
                .map(obj -> ((Timestamp) obj).getTime())
                .orElseGet(() -> Optional.ofNullable(resultEntry.get("eventGmtCreate"))
                        .map(obj -> ((Timestamp) obj).getTime())
                        .orElse(0L)));
        alarmRecordsOutputEntry.setAlarmSourceType(Optional.ofNullable(resultEntry.get("alarmSourceType")).map(Object::toString)
                .map(Integer::parseInt).map(AlarmSourceTypeEnum::fromValue).orElse(null));
        try {
            if (resultEntry.get("varMap") != null) {
                alarmRecordsOutputEntry.setVarMap(objectMapper.readValue(resultEntry.get("varMap").toString(), Map.class));
            }
        } catch (Exception ignored) {
        }


        
        return alarmRecordsOutputEntry;
    }

    /**
     * Convert single query result to AlarmRecordsOutputEntry with PII processing
     * This method ensures consistent format for hittedRule field and handles PII permission
     * 
     * @param resultEntry the query result entry
     * @param hasPiiPermission whether user has PII permission
     * @return alarm records output entry
     */
    public AlarmRecordsOutputEntry convertQueryResultToOutputEntryWithPII(Map<String, Object> resultEntry, boolean hasPiiPermission) {
        AlarmRecordsOutputEntry alarmRecordsOutputEntry = convertQueryResultToOutputEntry(resultEntry);
        
        // Handle metrics tags with PII permission
        Map<String, String> tagMap = alarmRecordsOutputEntry.getMetricsTagMap();
        if (tagMap != null && !hasPiiPermission) {
            // Mask sensitive data if has no PII permission
            Map<String, String> maskedTagMap = new HashMap<>();
            for (Map.Entry<String, String> entry : tagMap.entrySet()) {
                String key = entry.getKey();
                String value = entry.getValue();
                maskedTagMap.put(key, value != null ? "*" : null);
            }
            alarmRecordsOutputEntry.setMetricsTagMap((Map) maskedTagMap);
        }
        
        // Convert metrics tags to list format if needed
        if (tagMap != null && !tagMap.isEmpty()) {
            List<MetricsTags> metricsTagsList = new ArrayList<>();
            for (Map.Entry<String, String> entry : tagMap.entrySet()) {
                MetricsTags metricsTags = new MetricsTags();
                metricsTags.setName(entry.getKey());
                metricsTags.setValue(entry.getValue());
                metricsTagsList.add(metricsTags);
            }
            alarmRecordsOutputEntry.setMetricsTags(metricsTagsList);
        }
        
        return alarmRecordsOutputEntry;
    }

    private List<AlarmRecordsOutputEntry> mappingResultToOutput(List<Map<String, Object>> queryResult) {
        List<AlarmRecordsOutputEntry> alarmRecordsOutputList = new ArrayList<>();
        for (Map<String, Object> resultEntry : queryResult) {
            // Use the shared method for consistent behavior (without PII processing)
            alarmRecordsOutputList.add(convertQueryResultToOutputEntry(resultEntry));
        }
        return alarmRecordsOutputList;
    }

    private List<AlarmRecordsOutputEntry> mappingSuppressedRecordResultToOutput(List<Map<String, Object>> queryResult, List<CollectorFieldDO> collectorFieldDOS, boolean hasPiiPermission) {
        List<AlarmRecordsOutputEntry> alarmRecordsOutputList = new ArrayList<>();
        for (Map<String, Object> resultEntry : queryResult) {
            AlarmRecordsOutputEntry alarmRecordsOutputEntry = new AlarmRecordsOutputEntry();
            alarmRecordsOutputEntry.setId(Optional.ofNullable(resultEntry.get("id")).map(Object::toString).orElse(null));
            alarmRecordsOutputEntry.setAlarmLevel(Optional.ofNullable(resultEntry.get("alarmLevel")).map(Object::toString).orElse(null));
            alarmRecordsOutputEntry.setAlarmId(Optional.ofNullable(resultEntry.get("alarmId")).map(Object::toString).orElse(null));
            alarmRecordsOutputEntry.setAlarmName(Optional.ofNullable(resultEntry.get("alarmName")).map(Object::toString).orElse(null));
            alarmRecordsOutputEntry.setAlarmRuleId(Optional.ofNullable(resultEntry.get("alarmRuleId")).map(Object::toString).orElse(null));
            alarmRecordsOutputEntry.setHittedRule(Optional.ofNullable(resultEntry.get("hittedRule")).map(Object::toString).orElse(null));
            alarmRecordsOutputEntry.setStatus("suppressed");
            alarmRecordsOutputEntry.setMetricsId(Optional.ofNullable(resultEntry.get("metricsId")).map(Object::toString).orElse(null));
            alarmRecordsOutputEntry.setMetricsName(Optional.ofNullable(resultEntry.get("metricsName")).map(Object::toString).orElse(null));
            alarmRecordsOutputEntry.setMetricsTags(extractMetricsTags(collectorFieldDOS, resultEntry, hasPiiPermission));
            alarmRecordsOutputEntry.setTime(Optional.ofNullable(((Timestamp)(resultEntry.get("time")))).map(Timestamp::getTime).orElse(0L));
            alarmRecordsOutputEntry.setRecordGmtCreate(Optional.ofNullable(((Timestamp)(resultEntry.get("gmtCreate")))).map(Timestamp::getTime).orElse(0L));
            alarmRecordsOutputList.add(alarmRecordsOutputEntry);
        }
        return alarmRecordsOutputList;
    }

    private List<MetricsTags> extractMetricsTags(List<CollectorFieldDO> collectorFieldDOS, Map<String, Object> resultEntry, boolean hasPiiPermission) {
        List<MetricsTags> tagList = new ArrayList<>();
        Map<String, Object> tagMap = (Map) resultEntry.get("tagMap");
        if (MapUtils.isNotEmpty(tagMap)) {
            for (Map.Entry<String, Object> entry : tagMap.entrySet()) {
                String tagName = entry.getKey();
                boolean needMask;
                
                if (CollectionUtils.isEmpty(collectorFieldDOS)) {
                    needMask = !hasPiiPermission;
                } else {
                    boolean isPii = collectorFieldDOS.stream().anyMatch(collectorField -> tagName.equals(collectorField.getTargetField()));
                    needMask = isPii && !hasPiiPermission;
                }
                
                tagList.add(new MetricsTags(tagName, needMask ? "*" : entry.getValue(), needMask));
            }
        }
        return tagList;
    }


}
