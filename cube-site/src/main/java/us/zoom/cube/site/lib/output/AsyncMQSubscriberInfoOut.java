package us.zoom.cube.site.lib.output;

import lombok.Data;
import us.zoom.async.mq.openapi.model.result.consumer.ConsumerGroupDescriptionResult;
import us.zoom.mq.common.entity.TopicGroupDescribe;

@Data
public class AsyncMQSubscriberInfoOut {
    private String unitTag;
    private String topic;
    private String group;
    private String sourceName;
    private String sourceType;
    private String purposeType;
    private ConsumerGroupDescriptionResult topicGroupDescribe;
}
