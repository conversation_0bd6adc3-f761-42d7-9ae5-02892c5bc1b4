package us.zoom.cube.site.infra.utils;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Template utility class
 * Supports high-performance template placeholder replacement, similar to template engine functionality
 * 
 * <AUTHOR>
 */
public class TemplateUtils {
    
    // Cache compiled patterns to improve performance
    private static final Map<String, Pattern> PATTERN_CACHE = new ConcurrentHashMap<>();
    
    // Default placeholder start and end markers
    private static final String DEFAULT_START = "${";
    private static final String DEFAULT_END = "}";
    
    /**
     * Replace placeholders using default format ${key}
     */
    public static String process(String template, Map<String, Object> context) {
        return process(template, context, DEFAULT_START, DEFAULT_END);
    }
    
    /**
     * Replace placeholders using custom delimiter format
     * @param template Template string
     * @param context Context parameters
     * @param startDelimiter Start delimiter
     * @param endDelimiter End delimiter
     */
    public static String process(String template, Map<String, Object> context, 
                               String startDelimiter, String endDelimiter) {
        if (template == null || template.isEmpty() || context == null || context.isEmpty()) {
            return template;
        }
        
        // Escape regex special characters
        String escapedStart = Pattern.quote(startDelimiter);
        String escapedEnd = Pattern.quote(endDelimiter);
        String patternStr = escapedStart + "([^" + escapedEnd + "]+)" + escapedEnd;
        
        Pattern pattern = PATTERN_CACHE.computeIfAbsent(patternStr, Pattern::compile);
        
        Matcher matcher = pattern.matcher(template);
        if (!matcher.find()) {
            return template;
        }
        
        StringBuilder result = new StringBuilder(template.length() + 100);
        int lastEnd = 0;
        
        do {
            String key = matcher.group(1).trim();
            Object value = context.get(key);
            String replacement = value != null ? value.toString() : "";
            
            result.append(template, lastEnd, matcher.start());
            result.append(replacement);
            lastEnd = matcher.end();
        } while (matcher.find());
        
        result.append(template, lastEnd, template.length());
        return result.toString();
    }
    
    /**
     * Replace placeholders using colon format :key:
     */
    public static String processWithColon(String template, Map<String, Object> context) {
        return process(template, context, ":", ":");
    }
    
    /**
     * Replace placeholders using brace format {key}
     */
    public static String processWithBrace(String template, Map<String, Object> context) {
        return process(template, context, "{", "}");
    }
    
    /**
     * Check if template contains placeholders
     */
    public static boolean hasPlaceholders(String template, String startDelimiter, String endDelimiter) {
        if (template == null || template.isEmpty()) {
            return false;
        }
        
        String escapedStart = Pattern.quote(startDelimiter);
        String escapedEnd = Pattern.quote(endDelimiter);
        String patternStr = escapedStart + "([^" + escapedEnd + "]+)" + escapedEnd;
        
        Pattern pattern = PATTERN_CACHE.computeIfAbsent(patternStr, Pattern::compile);
        return pattern.matcher(template).find();
    }
    
    /**
     * Extract all placeholder keys from template
     */
    public static java.util.Set<String> extractKeys(String template, String startDelimiter, String endDelimiter) {
        java.util.Set<String> keys = new java.util.HashSet<>();
        if (template == null || template.isEmpty()) {
            return keys;
        }
        
        String escapedStart = Pattern.quote(startDelimiter);
        String escapedEnd = Pattern.quote(endDelimiter);
        String patternStr = escapedStart + "([^" + escapedEnd + "]+)" + escapedEnd;
        
        Pattern pattern = PATTERN_CACHE.computeIfAbsent(patternStr, Pattern::compile);
        Matcher matcher = pattern.matcher(template);
        
        while (matcher.find()) {
            keys.add(matcher.group(1).trim());
        }
        
        return keys;
    }
    
    /**
     * Validate that all placeholders in template have corresponding values
     */
    public static boolean validateTemplate(String template, Map<String, Object> context, 
                                         String startDelimiter, String endDelimiter) {
        java.util.Set<String> requiredKeys = extractKeys(template, startDelimiter, endDelimiter);
        return context != null && context.keySet().containsAll(requiredKeys);
    }
    
    /**
     * Clear pattern cache (use when memory is tight)
     */
    public static void clearCache() {
        PATTERN_CACHE.clear();
    }
} 