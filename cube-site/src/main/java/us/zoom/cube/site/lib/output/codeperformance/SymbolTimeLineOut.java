package us.zoom.cube.site.lib.output.codeperformance;

import lombok.Data;

/**
 * @author: tobey.zhu
 * @date: /2023/07/20
 * @description:
 */
@Data
public class SymbolTimeLineOut {
    private Long ts;
    private Double cpu;
    private String sampleId;

    public SymbolTimeLineOut(String sampleId, Long ts, Double cpu) {
        this.sampleId = sampleId;
        this.ts = ts;
        this.cpu = cpu;
    }
}
