package us.zoom.cube.site.core.monitor;


import com.zoom.op.monitor.domain.alarm.Channel;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import us.zoom.cloud.secrets.spring.annotation.SecretValue;
import us.zoom.cube.site.biz.alarm.ChannelService;
import us.zoom.cube.site.core.InstanceMessagingHandler;
import us.zoom.cube.site.core.RsaService;
import us.zoom.cube.site.core.SysParaHandler;
import us.zoom.cube.site.core.TenantHandler;
import us.zoom.cube.site.core.config.ClickhouseHandlerFactory;
import us.zoom.cube.site.lib.common.MonitorTypeEnum;
import us.zoom.cube.site.lib.input.SysParaInput;
import us.zoom.cube.site.lib.query.SysParaQuery;
import us.zoom.infra.clickhouse.ClickhouseSqlUtil;
import us.zoom.infra.dao.model.DistributedLockDO;
import us.zoom.infra.dao.model.SysParaDO;
import us.zoom.infra.dao.service.DistributedLockDAO;
import us.zoom.infra.dao.service.InstanceMessagingChannelDAO;
import us.zoom.infra.influx.service.InfluxService;
import us.zoom.infra.model.alarm.AlarmLevel;
import us.zoom.infra.notification.channel.zoomchat.IMChannelEngine;
import us.zoom.infra.thread.NamedThreadFactory;
import us.zoom.infra.utils.DateUtils;
import us.zoom.infra.utils.IpUtils;
import us.zoom.cube.lib.utils.JsonUtils;

import jakarta.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
public class MetricsDetectMonitorService {



    @Value("${self.monitor.im.url}")
    private String url;

    @SecretValue("self.monitor.im.authorization")
    private String authorization;
    private static ScheduledExecutorService scheduler;
    private Integer scheduleSeconds= 120;
    private int lockTimeOut=(scheduleSeconds/60);



    @Autowired
    private SysParaHandler sysParaHandler;
    @Autowired
    private DistributedLockDAO distributedLockDAO;

    @Autowired
    private ChannelService channelService;

    @Autowired
    private InstanceMessagingChannelDAO instanceMessagingChannelDAO;

    @Autowired
    private InstanceMessagingHandler instanceMessagingHandler;

    @Autowired
    private TenantHandler tenantHandler;


    @Autowired
    private  SelfMonitorHandler selfMonitorHandler;
    private static final Logger LOG= LoggerFactory.getLogger(MetricsDetectMonitorService.class.getName());

    @Autowired
    private RsaService rsaService;

    @Autowired
    private InfluxService influxService;



    @Autowired
    private ClickhouseHandlerFactory clickhouseHandlerFactory;

    private Logger nonitorLog = LoggerFactory.getLogger("Monitor");



    public static final  String METRICS_DETECT_ASSIGNER="metrics_detect_assigner";

    public static final String __ALL__ = "__ALL__";

    @PostConstruct
    public void init(){
        scheduler= Executors.newScheduledThreadPool(1,new NamedThreadFactory("metrics detect threadpool "));
        scheduler.scheduleWithFixedDelay(new Runnable() {
            @Override
            public void run() {
                try{
                    detect();
                }catch (Exception e){
                    LOG.error("monitor error",e);
                }
            }
        },60,scheduleSeconds, TimeUnit.SECONDS);
    }



    private static final String metricsDetectSysType="metricsDetect";
    private void detect() {
        LOG.info("begin detect!");
        if(!lock()){
            LOG.info("do not get the detect lock");
            return;
        }
        SysParaQuery paraQuery=new SysParaQuery();
        paraQuery.setType(metricsDetectSysType);
        List<SysParaDO> sysParaDOS= sysParaHandler.listByType(paraQuery);
        if(CollectionUtils.isEmpty(sysParaDOS)){
            LOG.info("there is no metrics to detect");
            return;
        }


        //Map<tenantName,InstanceMessagingChannelDO>
//        Map<String,InstanceMessagingChannelDO> tenantImMap=getImMap();

        Map<String,List<Channel>> tenantNameChannelListMap=channelService.getTenantNameImChannelListWithDecrypt().getData();

        for(SysParaDO sysParaDO:sysParaDOS){
            try{
                long begin=System.currentTimeMillis();
                LOG.info("begin detect metrics for {}",sysParaDO.getValue());
                String condition=sysParaDO.getValue();
                if(StringUtils.isBlank(condition)){
                    LOG.error("metrics detect condition is error, id is {}",sysParaDO.getId());
                    continue;
                }

                MetricsDetectCondition metricsDetectCondition= JsonUtils.toObject(condition,MetricsDetectCondition.class);
                java.util.Date now=new java.util.Date();

                boolean shouldSend=shouldSendMsg(metricsDetectCondition,now);
                if(! shouldSend){
                    LOG.info("It is notified for  metrics {} ",sysParaDO.getValue());
                    continue;
                }

                List<String> alarmContents=new ArrayList<>();

                if(CollectionUtils.isEmpty(metricsDetectCondition.getConditions())){
                    Date queryDate=new Date(DateUtils.addSeconds(now,-metricsDetectCondition.getDetectPeriods()));
                    String query = createQuery(metricsDetectCondition, Collections.EMPTY_LIST,queryDate);
                    LOG.info("metrics detect query sql {}",query);
//                    clickhouseHandlerFactory.get().getHandlerByEnv().query(db,sql);
                    //QueryResult queryResult= InfluxDBUtil.query(metricsDetectCondition.getService().toLowerCase(),query);
                    checkByQueryData(metricsDetectCondition, alarmContents, queryDate, query,"");
                }else{
                    for(List<MetricsDetectConditionDetail> conditionRecordDetail:metricsDetectCondition.getConditions()){
                        Date queryDate=new Date(DateUtils.addSeconds(now,-metricsDetectCondition.getDetectPeriods()));
                        String query = createQuery(metricsDetectCondition, conditionRecordDetail,queryDate);
                        LOG.info("metrics detect query sql {}",query);
                        //QueryResult queryResult= InfluxDBUtil.query(metricsDetectCondition.getService().toLowerCase(),query);
                        checkByQueryData(metricsDetectCondition, alarmContents, queryDate, query,JsonUtils.toJsonStringIgnoreExp(conditionRecordDetail));
                    }
                }

                if(CollectionUtils.isEmpty(alarmContents)){
                    LOG.info("It is ok  for metrics {} ",sysParaDO.getValue());
                    continue;
                }
                AtomicBoolean matchCustomChannel = new AtomicBoolean(false);
                if(tenantNameChannelListMap != null && tenantNameChannelListMap.get(metricsDetectCondition.getService()) != null){
                    List<Channel> channelList = tenantNameChannelListMap.get(metricsDetectCondition.getService());
                    channelList.forEach(channel -> {
                        if(!CollectionUtils.isEmpty(metricsDetectCondition.getChannelNameList())
                                && (metricsDetectCondition.getChannelNameList().contains(channel.getName()) || metricsDetectCondition.getChannelNameList().contains(__ALL__))){
                            matchCustomChannel.set(true);
                            long being = System.currentTimeMillis();
                            String endpoint=channel.getParameters().stream().filter(para->para.getName().equals(IMChannelEngine.PARAM_NAME_ENDPOINT)).map(para->para.getValue()).collect(Collectors.toList()).get(0);
                            String token=channel.getParameters().stream().filter(para->para.getName().equals(IMChannelEngine.PARAM_NAME_VERIFICATION_TOKEN)).map(para->para.getValue()).collect(Collectors.toList()).get(0);
                            boolean success  = selfMonitorHandler.send(null,alarmContents,metricsDetectCondition.getMetrics()+" metrics stop",null, AlarmLevel.ERROR,endpoint,token, false, metricsDetectCondition.getService(), channel.getName());
                            Map<String,Object> monitorMap = new HashMap<>();
                            monitorMap.put("phase","sendIm");
                            monitorMap.put("cost",System.currentTimeMillis() - begin);
                            monitorMap.put("status",success);
                            pringMonitor(monitorMap);
                        }
                    });
                }
                if(!matchCustomChannel.get()){
                    LOG.info("send the metrics detect alarm [{}] to default channel url[{}]",alarmContents, url);
                    selfMonitorHandler.send(null,alarmContents,metricsDetectCondition.getMetrics()+" metrics stop",null, AlarmLevel.ERROR,url,authorization, false, metricsDetectCondition.getService(), "");
                }
                sysParaDO.setValue(JsonUtils.toJsonString(metricsDetectCondition));
                SysParaInput sysParaInput=new SysParaInput();
                BeanUtils.copyProperties(sysParaDO,sysParaInput);
                sysParaHandler.edit(sysParaInput);
                LOG.info("send msg over and end detect metrics for {} ,cost is {}",sysParaDO.getValue(),(System.currentTimeMillis()-begin));
            }catch (Exception e){
                LOG.error("some error happend for  detect , id is {}",sysParaDO.getId(),e);
            }
        }
    }


    private void pringMonitor(Map<String,Object> logMap){
        logMap.put("type", MonitorTypeEnum.metricsDetect.name());
        logMap.put("ts",System.currentTimeMillis());
        logMap.put("timeShow", DateUtils.format(new java.util.Date(),DateUtils.FORMART1));
        nonitorLog.info(JsonUtils.toJsonStringIgnoreExp(logMap));
    }

    private void checkByQueryData(MetricsDetectCondition metricsDetectCondition, List<String> alarmContents, Date queryDate, String query,String prefix) {
//        QueryResult influxQueryResult = influxService.query(metricsDetectCondition.getService(), query);
//
//        if(influxQueryResult.getResults() == null  || CollectionUtils.isEmpty(influxQueryResult.getResults().get(0).getSeries())){
//            alarmContents.add(prefix+" metrics has stop after "+queryDate +" service="+metricsDetectCondition.getService());
//        }
        long begin = System.currentTimeMillis();
        List<Map<String,Object>> results =  clickhouseHandlerFactory.get().getHandlerByQueryEnv().query(metricsDetectCondition.getService(),query);
        if(org.apache.commons.collections.CollectionUtils.isEmpty(results)){
            alarmContents.add(prefix+" metrics has stop after "+queryDate +" service="+metricsDetectCondition.getService());
        }
        Map<String,Object> monitorMap = new HashMap<>();
        monitorMap.put("sql",query);
        monitorMap.put("phase","query");
        monitorMap.put("resultSize",results == null ? 0 : results.size());
        monitorMap.put("cost",System.currentTimeMillis() - begin);
        pringMonitor(monitorMap);
    }


    private  String createQuery(MetricsDetectCondition metricsDetectCondition,List<MetricsDetectConditionDetail> metricsDetectConditionDetails, Date queryDate) {
        String query="select \""+metricsDetectCondition.getField()+"\"  from \""+ ClickhouseSqlUtil.toClickhouseName(metricsDetectCondition.getService())+"\".\""+ClickhouseSqlUtil.toClickhouseName(metricsDetectCondition.getMetrics())+"\" where time > '"+DateUtils.format2UTC(queryDate,DateUtils.FORMART1)+"'";
        String condition=metricsDetectConditionDetails.stream().map(item->
                item.getValues().stream().map(value -> "\""+item.getFieldName()+"\" = "+ ( (item.isNumber() ? "": "'")+value+(item.isNumber() ? "": "'") )).collect(Collectors.joining(" or "))
        ).collect(Collectors.joining(") and ("));

        if(!CollectionUtils.isEmpty(metricsDetectConditionDetails) ){
            condition=" and (" +condition+ ")";
        }
        return query+condition+" limit 1";
    }

    private static final Long sendIntevalInMinutes=60*60*1000L;
    private static final  Integer maxSendCount=2;
    private boolean shouldSendMsg(MetricsDetectCondition metricsDetectCondition, Date now) {
        if( null == metricsDetectCondition.getLastAlarmTime()){
            metricsDetectCondition.setLastAlarmTime(now.getTime());
            metricsDetectCondition.setSendCount(1);
            return true;
        }

        Long lastSendInteval=(now.getTime() - metricsDetectCondition.getLastAlarmTime());
        if(lastSendInteval > sendIntevalInMinutes){
            metricsDetectCondition.setLastAlarmTime(now.getTime());
            metricsDetectCondition.setSendCount(1);
            return true;
        }

        if(metricsDetectCondition.getSendCount() < maxSendCount){
            metricsDetectCondition.setLastAlarmTime(now.getTime());
            metricsDetectCondition.setSendCount(metricsDetectCondition.getSendCount()+1);
            return true;
        }

        return false;
    }


    public boolean lock() {
        List<DistributedLockDO> distributedLocks=distributedLockDAO.listByType(METRICS_DETECT_ASSIGNER);
        if(CollectionUtils.isEmpty(distributedLocks)){
            return false;
        }
        return  distributedLockDAO.lockWithType(METRICS_DETECT_ASSIGNER,distributedLocks.get(0).getHandler(), IpUtils.getLocalIP(),lockTimeOut)>0;
    }
}
