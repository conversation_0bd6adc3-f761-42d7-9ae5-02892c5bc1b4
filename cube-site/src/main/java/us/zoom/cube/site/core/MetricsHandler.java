package us.zoom.cube.site.core;

import com.google.common.collect.Lists;
import com.zoom.op.monitor.domain.AggregationRuleType;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.lib.integrations.MetricsFieldTypeEnum;
import us.zoom.cube.lib.utils.JsonUtils;
import us.zoom.cube.site.infra.utils.ExceptionUtils;
import us.zoom.cube.site.lib.SiteException;
import us.zoom.cube.site.lib.output.config.metrics.FieldOrTag;
import us.zoom.infra.dao.model.*;
import us.zoom.infra.dao.service.CollectorMetricsDAO;
import us.zoom.infra.dao.service.MetricsDAO;
import us.zoom.infra.enums.MetricsTypeEnum;
import us.zoom.infra.thread.NamedThreadFactory;
import us.zoom.infra.utils.Instance;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

import static us.zoom.cube.site.infra.enums.WebCodeEnum.InnerError;

/**
 * <AUTHOR> Junjian
 * @create 2020/7/16 4:47 PM
 */
@Service
@Slf4j
public class MetricsHandler {

    @Autowired
    private MetricsDAO metricsDAO;

    @Autowired
    private CollectorMetricsDAO collectorMetricsDAO;

    @Autowired
    private MetricsFieldHandler metricsFieldHandler;

    @Autowired
    private MetricsAggregationHandler metricsAggregationHandler;

    @Autowired
    private MetricsAggregationRuleHandler metricsAggregationRuleHandler;

    @Autowired
    private AggregationFunctionItemHandler aggregationFunctionItemHandler;

    @Autowired
    private AggregationHistogramRangeItemHandler aggregationHistogramRangeItemHandler;

    @Autowired
    private AggregationPercentileItemHandler aggregationPercentileItemHandler;

    @Autowired
    private AggregationCustomFieldRuleHandler aggregationCustomFieldRuleHandler;

    private static ThreadPoolExecutor executor;

    @PostConstruct
    public void init() {
        int cores = Runtime.getRuntime().availableProcessors();
        executor=new ThreadPoolExecutor(cores, cores * 2 - 1, 1, TimeUnit.HOURS,
                new LinkedBlockingQueue(50), new NamedThreadFactory("metrics handle thread pool"));
    }

    public List<MetricsDO> findByMetricsNameLike(String metricsName, List<String> tenantIds, int pageIndex, int pageSize) {
        return metricsDAO.findByMetricsNameLike(metricsName, tenantIds, (pageIndex - 1) * pageSize, pageSize);
    }

    public List<MetricsDO> findAllByMetricsNameLike(String metricsName, String tenantId) {
        return metricsDAO.findAllByMetricsNameLike(metricsName, tenantId);
    }

    public List<MetricsDO> findByMetricsNameLikeAndType(String metricsName, Integer type, List<String> tenantIds, int pageIndex, int pageSize) {
        return metricsDAO.findByMetricsNameLikeAndType(metricsName, type, tenantIds, (pageIndex - 1) * pageSize, pageSize);
    }

    public List<MetricsDO> findByMetricsNameLikeWithTypeAndStatus(String metricsName, Integer type, Integer enabled, String collectorName, String dataParserName, List<String> tenantIds, String creator, List<String> labelIds, Integer labelSize,  String labelMatchType, int pageIndex, int pageSize) {
        return metricsDAO.findByMetricsNameLikeWithTypeAndStatus(metricsName, type, enabled, collectorName, dataParserName, tenantIds, creator, labelIds, labelSize, labelMatchType, (pageIndex - 1) * pageSize, pageSize);
    }

    public int getCountByMetricsNameLike(String metricsName, List<String> tenantIds) {
        return metricsDAO.getCountByMetricsNameLike(metricsName, tenantIds);
    }

    public List<MetricsDO> queryExistTenantsByMetricsNameAndTenantIds(MetricsQueryDO metricsQueryDO){
        return metricsDAO.queryExistTenantsByMetricsNameAndTenantIds(metricsQueryDO);
    }

    public int getCountByMetricsNameLikeAndType(String metricsName, Integer type, List<String> tenantIds) {
        return metricsDAO.getCountByMetricsNameLikeAndType(metricsName, type, tenantIds);
    }

    public int getCountByMetricsNameLikeAndType(String metricsName, Integer type, Integer enabled, String collectorName, String dataParserName, List<String> tenantIds, String creator, List<String> labelIds, Integer labelsize,  String labelMatchType) {
        return metricsDAO.getCountByMetricsNameLikeAndTypeNew(metricsName, type, enabled, collectorName, dataParserName, tenantIds, creator, labelIds, labelsize, labelMatchType);
    }

    public boolean hasSameMetricsName(String metricsName, List<String> tenantIds) {
        return metricsDAO.getCountByMetricsName(metricsName, tenantIds) > 0;
    }

    @Transactional(rollbackFor = Exception.class)
    public void addOriginalMetrics(MetricsDO metricsDO, CollectorMetricsDO collectorMetricsDO, List<MetricsFieldDO> metricsFieldDOList) {
        log.info("[addOriginalMetrics] metricsDO = {}, collectorMetricsDO = {}, metricsFieldDOList = {}", JsonUtils.toJsonStringIgnoreExp(metricsDO), JsonUtils.toJsonStringIgnoreExp(collectorMetricsDO), JsonUtils.toJsonStringIgnoreExp(metricsFieldDOList));
        metricsDAO.insertMetrics(metricsDO);
        if (collectorMetricsDO.getCollectorId() != null) {
            collectorMetricsDAO.insertCollectorMetrics(collectorMetricsDO);
        }
        metricsFieldHandler.batchInsertMetricsField(metricsFieldDOList);
    }

    @Transactional(rollbackFor = Exception.class)
    public void addOriginalMetrics(MetricsSynDO metricsSynDO) {
        metricsDAO.batchInsertMetrics(Arrays.asList(metricsSynDO.getMetricsSynAddDO().getMetricsDO()));
        collectorMetricsDAO.batchInsertCollectorMetrics(Arrays.asList(metricsSynDO.getMetricsSynAddDO().getCollectorMetricsDO()));
        metricsFieldHandler.batchInsertMetricsField(metricsSynDO.getMetricsSynAddDO().getMetricsFieldDOS());
    }

    @Transactional(rollbackFor = Exception.class)
    public void editOriginalMetrics(MetricsDO metricsDO, List<String> needDeleteMetricsFieldIds, List<MetricsFieldDO> needAddMetricsFieldList) {
        log.info("[editOriginalMetrics] metricsDO = {}， needDeleteMetricsFieldIds = {}, needAddMetricsFieldList = {}", JsonUtils.toJsonStringIgnoreExp(metricsDO), JsonUtils.toJsonStringIgnoreExp(needDeleteMetricsFieldIds), JsonUtils.toJsonStringIgnoreExp(needAddMetricsFieldList));
        metricsDAO.updateMetrics(metricsDO);
        metricsFieldHandler.deleteByFieldIds(needDeleteMetricsFieldIds);
        metricsFieldHandler.batchInsertMetricsField(needAddMetricsFieldList);
    }

    @Transactional(rollbackFor = Exception.class)
    public void editAggregatedMetrics(MetricsDO metricsDO, MetricsAggregationDO metricsAggregationDO,
                                      List<MetricsAggregationRuleDO> addMetricsAggregationRuleDOList,
                                      List<MetricsAggregationRuleDO> editMetricsAggregationRuleDOList,
                                      List<String> deleteMetricsAggregationRuleIdList,
                                      List<AggregationFunctionItemDO> addAggregationFunctionItemDOList,
                                      List<AggregationFunctionItemDO> editAggregationFunctionItemDOList,
                                      List<String> deleteAggregationFunctionItemIdList,
                                      List<AggregationHistogramRangeItemDO> addAggregationHistogramRangeItemDOList,
                                      List<AggregationHistogramRangeItemDO> editAggregationHistogramRangeItemDOList,
                                      List<String> deleteAggregationHistogramRangeItemIdList,
                                      List<AggregationPercentileItemDO> addAggregationPercentileItemDOList,
                                      List<AggregationPercentileItemDO> editAggregationPercentileItemDOList,
                                      List<String> deleteAggregationPercentileItemIdList,
                                      List<AggregationCustomFieldRuleDO> addAggregationCustomFieldRuleDOList,
                                      List<AggregationCustomFieldRuleDO> editAggregationCustomFieldRuleDOList,
                                      List<String> deleteAggregationCustomFieldRuleDOList,
                                      List<MetricsFieldDO> addMetricsFieldDOList,
                                      List<MetricsFieldDO> editMetricsFieldDOList,
                                      List<String> deleteMetricsFieldIdList
                                      ) {
        LocalDateTime startTime = LocalDateTime.now();
        try {
            log.info("[editAggregatedMetrics] metricsDO = {}, metricsAggregationDO = {}, addMetricsAggregationRuleDOList = {}, editMetricsAggregationRuleDOList = {}", JsonUtils.toJsonStringIgnoreExp(metricsDO), JsonUtils.toJsonStringIgnoreExp(metricsAggregationDO), JsonUtils.toJsonStringIgnoreExp(addMetricsAggregationRuleDOList), JsonUtils.toJsonStringIgnoreExp(editMetricsAggregationRuleDOList));
            log.info("[editAggregatedMetrics] addAggregationFunctionItemDOList = {}, editAggregationFunctionItemDOList = {}, deleteAggregationFunctionItemIdList = {}", JsonUtils.toJsonStringIgnoreExp(addAggregationFunctionItemDOList), JsonUtils.toJsonStringIgnoreExp(editAggregationFunctionItemDOList), JsonUtils.toJsonStringIgnoreExp(deleteAggregationFunctionItemIdList));
            log.info("[editAggregatedMetrics] addAggregationHistogramRangeItemDOList = {}, editAggregationHistogramRangeItemDOList = {}, deleteAggregationHistogramRangeItemIdList = {}", JsonUtils.toJsonStringIgnoreExp(addAggregationHistogramRangeItemDOList), JsonUtils.toJsonStringIgnoreExp(editAggregationHistogramRangeItemDOList), JsonUtils.toJsonStringIgnoreExp(deleteAggregationHistogramRangeItemIdList));
            log.info("[editAggregatedMetrics] addAggregationPercentileItemDOList = {}, editAggregationPercentileItemDOList = {}, deleteAggregationPercentileItemIdList = {}", JsonUtils.toJsonStringIgnoreExp(addAggregationPercentileItemDOList), JsonUtils.toJsonStringIgnoreExp(editAggregationPercentileItemDOList), JsonUtils.toJsonStringIgnoreExp(deleteAggregationPercentileItemIdList));
            log.info("[editAggregatedMetrics] addAggregationCustomFieldRuleDOList = {}, editAggregationCustomFieldRuleDOList = {}, deleteAggregationCustomFieldRuleDOList = {}", JsonUtils.toJsonStringIgnoreExp(addAggregationCustomFieldRuleDOList), JsonUtils.toJsonStringIgnoreExp(editAggregationCustomFieldRuleDOList), JsonUtils.toJsonStringIgnoreExp(deleteAggregationCustomFieldRuleDOList));
            log.info("[editAggregatedMetrics] addMetricsFieldDOList = {}, editMetricsFieldDOList = {}, deleteMetricsFieldIdList = {}", JsonUtils.toJsonStringIgnoreExp(addMetricsFieldDOList), JsonUtils.toJsonStringIgnoreExp(editMetricsFieldDOList), JsonUtils.toJsonStringIgnoreExp(deleteMetricsFieldIdList));

            updateMetrics(metricsDO);
            metricsAggregationHandler.updateMetricsAggregation(metricsAggregationDO);
            metricsAggregationRuleHandler.batchInsertMetricsAggregationRule(addMetricsAggregationRuleDOList);
            metricsAggregationRuleHandler.batchUpdateMetricsAggregationRule(editMetricsAggregationRuleDOList);
            metricsAggregationRuleHandler.batchDeleteByIdList(deleteMetricsAggregationRuleIdList);
            aggregationFunctionItemHandler.batchDeleteByIdList(deleteAggregationFunctionItemIdList);
            aggregationFunctionItemHandler.batchInsertAggregationFunction(addAggregationFunctionItemDOList);
            aggregationFunctionItemHandler.batchUpdateAggregationFunction(editAggregationFunctionItemDOList);
            aggregationHistogramRangeItemHandler.batchDeleteByIdList(deleteAggregationHistogramRangeItemIdList);
            aggregationHistogramRangeItemHandler.batchInsertAggregationHistogramRange(addAggregationHistogramRangeItemDOList);
            aggregationHistogramRangeItemHandler.batchUpdateAggregationHistogramRange(editAggregationHistogramRangeItemDOList);
            aggregationPercentileItemHandler.batchDeleteByIdList(deleteAggregationPercentileItemIdList);
            aggregationPercentileItemHandler.batchInsertAggregationPercentile(addAggregationPercentileItemDOList);
            aggregationPercentileItemHandler.batchUpdateAggregationPercentile(editAggregationPercentileItemDOList);
            aggregationCustomFieldRuleHandler.batchDeleteByIdList(deleteAggregationCustomFieldRuleDOList);
            aggregationCustomFieldRuleHandler.batchInsertCustomFieldRule(addAggregationCustomFieldRuleDOList);
            aggregationCustomFieldRuleHandler.batchUpdateCustomFieldRule(editAggregationCustomFieldRuleDOList);
            metricsFieldHandler.deleteByFieldIds(deleteMetricsFieldIdList);
            metricsFieldHandler.batchInsertMetricsField(addMetricsFieldDOList);
            metricsFieldHandler.batchUpdateMetricsField(editMetricsFieldDOList);

        }  catch (Exception e) {
            log.error(ExceptionUtils.getTrace(e));
            throw new SiteException(InnerError.getCode(), InnerError.getErrMsg());
        } finally {
            log.info("[editAggregatedMetrics] cost = {} ms", Duration.between(startTime, LocalDateTime.now()).toMillis());
        }
    }


    // todo rollback will not happen, need investigate later
    @Transactional(rollbackFor = Exception.class)
    public void addAggregatedMetrics(MetricsDO metricsDO, CollectorMetricsDO collectorMetricsDO, List<MetricsFieldDO> metricsFieldDOList,
                                     MetricsAggregationDO metricsAggregationDO, List<MetricsAggregationRuleDO> metricsAggregationRuleDOList,
                                     List<AggregationFunctionItemDO> aggregationFunctionItemDOList, List<AggregationHistogramRangeItemDO> aggregationHistogramRangeItemDOList,
                                     List<AggregationPercentileItemDO> aggregationPercentileItemDOList, List<AggregationCustomFieldRuleDO> aggregationCustomFieldRuleDOList) {
        try {
            long startTime = System.currentTimeMillis();
            log.info("[addAggregatedMetrics] metricsDO = {}, collectorMetricsDO = {}, metricsFieldDOList = {}, metricsAggregationDO = {}," +
                    "metricsAggregationRuleDOList = {}, aggregationFunctionItemDOList = {}, aggregationHistogramRangeItemDOList = {}, " +
                            "aggregationPercentileItemDOList = {}, aggregationCustomFieldRuleDOList = {}",
                    JsonUtils.toJsonStringIgnoreExp(metricsDO), JsonUtils.toJsonStringIgnoreExp(collectorMetricsDO), JsonUtils.toJsonStringIgnoreExp(metricsFieldDOList), JsonUtils.toJsonStringIgnoreExp(metricsAggregationDO),
                    JsonUtils.toJsonStringIgnoreExp(metricsAggregationRuleDOList), JsonUtils.toJsonStringIgnoreExp(aggregationFunctionItemDOList), JsonUtils.toJsonStringIgnoreExp(aggregationHistogramRangeItemDOList),
                    JsonUtils.toJsonStringIgnoreExp(aggregationPercentileItemDOList), JsonUtils.toJsonStringIgnoreExp(aggregationCustomFieldRuleDOList));

            List<Callable<Boolean>> callableList = new ArrayList<>();
            callableList.add(() -> {
                metricsDAO.insertMetrics(metricsDO);
                return true;
            });

            callableList.add(() -> {
                if (collectorMetricsDO.getCollectorId() != null) {
                    collectorMetricsDAO.insertCollectorMetrics(collectorMetricsDO);
                }
                return true;
            });

            callableList.add(() -> {
                metricsFieldHandler.batchInsertMetricsField(metricsFieldDOList);
                return true;
            });

            callableList.add(() -> {
                metricsAggregationHandler.insertMetricsAggregation(metricsAggregationDO);
                return true;
            });


            callableList.add(() -> {
                metricsAggregationRuleHandler.batchInsertMetricsAggregationRule(metricsAggregationRuleDOList);
                return true;
            });
            callableList.add(() -> {
                aggregationFunctionItemHandler.batchInsertAggregationFunction(aggregationFunctionItemDOList);
                return true;
            });
            callableList.add(() -> {
                aggregationHistogramRangeItemHandler.batchInsertAggregationHistogramRange(aggregationHistogramRangeItemDOList);
                return true;
            });

            callableList.add(() -> {
                aggregationPercentileItemHandler.batchInsertAggregationPercentile(aggregationPercentileItemDOList);
                return true;
            });

            callableList.add(() -> {
                aggregationCustomFieldRuleHandler.batchInsertCustomFieldRule(aggregationCustomFieldRuleDOList);
                return true;
            });
            List<Future<Boolean>> futureList = executor.invokeAll(callableList);
            for(Future<Boolean> future:futureList){
                future.get();
            }
            long endTime = System.currentTimeMillis();
            log.info("[addAggregatedMetrics] cost = {} ms",endTime-startTime);

        }  catch (InterruptedException | ExecutionException e) {
            log.error(ExceptionUtils.getTrace(e));
            throw new SiteException(InnerError.getCode(), InnerError.getErrMsg());
        }
    }

    public void addAggregatedMetricsInParallel(MetricsDO metricsDO, CollectorMetricsDO collectorMetricsDO, List<MetricsFieldDO> metricsFieldDOList,
                                               MetricsAggregationDO metricsAggregationDO, List<MetricsAggregationRuleDO> metricsAggregationRuleDOList,
                                               List<AggregationFunctionItemDO> aggregationFunctionItemDOList, List<AggregationHistogramRangeItemDO> aggregationHistogramRangeItemDOList,
                                               List<AggregationPercentileItemDO> aggregationPercentileItemDOList, List<AggregationCustomFieldRuleDO> aggregationCustomFieldRuleDOList) {
        try {
            List<CompletableFuture> futureList = new ArrayList<>();
            futureList.add(CompletableFuture.runAsync(() -> metricsDAO.insertMetrics(metricsDO), executor));
            if (collectorMetricsDO.getCollectorId() != null) {
                futureList.add(CompletableFuture.runAsync(() -> collectorMetricsDAO.insertCollectorMetrics(collectorMetricsDO), executor));
            }
            futureList.add(CompletableFuture.runAsync(() -> metricsFieldHandler.batchInsertMetricsField(metricsFieldDOList), executor));
            futureList.add(CompletableFuture.runAsync(() -> metricsAggregationHandler.insertMetricsAggregation(metricsAggregationDO), executor));
            futureList.add(CompletableFuture.runAsync(() -> metricsAggregationRuleHandler.batchInsertMetricsAggregationRule(metricsAggregationRuleDOList), executor));
            futureList.add(CompletableFuture.runAsync(() -> aggregationFunctionItemHandler.batchInsertAggregationFunction(aggregationFunctionItemDOList), executor));
            futureList.add(CompletableFuture.runAsync(() -> aggregationHistogramRangeItemHandler.batchInsertAggregationHistogramRange(aggregationHistogramRangeItemDOList), executor));
            futureList.add(CompletableFuture.runAsync(() -> aggregationPercentileItemHandler.batchInsertAggregationPercentile(aggregationPercentileItemDOList), executor));
            futureList.add(CompletableFuture.runAsync(() -> aggregationCustomFieldRuleHandler.batchInsertCustomFieldRule(aggregationCustomFieldRuleDOList), executor));
            CompletableFuture.allOf(futureList.toArray(new CompletableFuture[]{})).join();
        } catch (Exception e) {
            log.error(ExceptionUtils.getTrace(e));
            throw new SiteException(InnerError.getCode(), InnerError.getErrMsg());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void addAggregatedMetricsInSerial(MetricsDO metricsDO, CollectorMetricsDO collectorMetricsDO, List<MetricsFieldDO> metricsFieldDOList,
                                               MetricsAggregationDO metricsAggregationDO, List<MetricsAggregationRuleDO> metricsAggregationRuleDOList,
                                               List<AggregationFunctionItemDO> aggregationFunctionItemDOList, List<AggregationHistogramRangeItemDO> aggregationHistogramRangeItemDOList,
                                               List<AggregationPercentileItemDO> aggregationPercentileItemDOList, List<AggregationCustomFieldRuleDO> aggregationCustomFieldRuleDOList) {
        metricsDAO.insertMetrics(metricsDO);
        if (collectorMetricsDO.getCollectorId() != null) {
            collectorMetricsDAO.insertCollectorMetrics(collectorMetricsDO);
        }
        metricsFieldHandler.batchInsertMetricsField(metricsFieldDOList);
        metricsAggregationHandler.insertMetricsAggregation(metricsAggregationDO);
        metricsAggregationRuleHandler.batchInsertMetricsAggregationRule(metricsAggregationRuleDOList);
        aggregationFunctionItemHandler.batchInsertAggregationFunction(aggregationFunctionItemDOList);
        aggregationHistogramRangeItemHandler.batchInsertAggregationHistogramRange(aggregationHistogramRangeItemDOList);
        aggregationPercentileItemHandler.batchInsertAggregationPercentile(aggregationPercentileItemDOList);
        aggregationCustomFieldRuleHandler.batchInsertCustomFieldRule(aggregationCustomFieldRuleDOList);
    }


    public MetricsDO getMetricsById(String metricsId) {
        return metricsDAO.getMetricsById(metricsId);
    }

    /**
     * Deprecated because the misMatch of method name and parameter order,
     * please use us.zoom.cube.site.core.MetricsHandler#findMetricsByNameAndTenantId(java.lang.String, java.lang.String) instead
     * @param tenantId
     * @param metricsName
     * @return
     */
    @Deprecated
    public MetricsDO getMetricsByNameOfTenant(String tenantId,String metricsName) {
        return metricsDAO.getMetricsByNameOfTenant(tenantId,metricsName);
    }

    public MetricsDO findMetricsByNameAndTenantId(String metricsName, String tenantId) {
        return metricsDAO.findMetricsByNameAndTenantId(metricsName,tenantId);
    }


    public String getCollectorIdByMetricsId(String metricsId) {
        return collectorMetricsDAO.getCollectorIdByMetricsId(metricsId);
    }

    public List<MetricsDO> getMetricsByTenant(String tenantId) {
        return metricsDAO.getMetricsByTenant(tenantId);
    }


    public List<MetricsFieldDO> listFieldsByMetricsIds(List<String> metricsId){
        return metricsFieldHandler.listFieldByMetricsIds(Lists.newArrayList(metricsId));
    }


    public List<FieldOrTag> getTagAndField(String metricsId) {
        List<FieldOrTag> fieldOrTags = Lists.newArrayList();
        if (StringUtils.isBlank(metricsId)) {
            return fieldOrTags;
        }
        MetricsDO metricsDO = metricsDAO.getMetricsById(metricsId);
        if (metricsDO == null) {
            return fieldOrTags;
        }
        String tagNames = metricsDO.getTagNames();
        if (!StringUtils.isEmpty(tagNames)) {
            String[] tagNameArr = tagNames.split(",");
            for (String tagName : tagNameArr) {
                FieldOrTag tag = FieldOrTag.newTag();
                tag.setFieldOrTagName(tagName);
                tag.setValueType(MetricsFieldTypeEnum.string.name());
                fieldOrTags.add(tag);
            }
        }
        List<MetricsFieldDO> metricsFieldDOList = metricsFieldHandler.listFieldByMetricsIds(Lists.newArrayList(metricsId));
        Instance.ofNullable(metricsFieldDOList).forEach(metricsFieldDO -> {
            FieldOrTag field = FieldOrTag.newField();
            field.setFieldOrTagName(metricsFieldDO.getFieldName());
            MetricsFieldTypeEnum fieldTypeEnum = MetricsFieldTypeEnum.fromValue(metricsFieldDO.getFieldType());
            field.setValueType(fieldTypeEnum == null ? null : fieldTypeEnum.name());
            fieldOrTags.add(field);
        });
        return fieldOrTags;
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteMetrics(String metricsId) {
        /*
          1. delete metrics
          2. delete metrics fields
          3. delete collectorMetrics
          4. delete agg
          5. delete rule
          6. delete function, histogram, percentile
          7. delete customFieldRule
         */

        try {
            long startTime = System.currentTimeMillis();
            MetricsDO metrics = metricsDAO.getMetricsById(metricsId);
            if (null == metrics) {
                return;
            }

            int type = metrics.getType();
            metricsDAO.deleteMetricsById(metricsId);
            metricsFieldHandler.deleteByMetricsId(metricsId);
            collectorMetricsDAO.deleteByMetricsId(metricsId);

            if (type == MetricsTypeEnum.AGGREGATION.getValue()) {
                MetricsAggregationDO metricsAggregationDO = metricsAggregationHandler.getByMetricsId(metricsId);
                String aggId = metricsAggregationDO.getId();
                deteleAggregationAndRules(Lists.newArrayList(aggId));
            }

            long endTime = System.currentTimeMillis();
            log.info("deleteMetrics - cost = {} ms",endTime-startTime);
        }  catch (InterruptedException | ExecutionException e) {
            log.error(ExceptionUtils.getTrace(e));
            throw new SiteException(InnerError.getCode(), InnerError.getErrMsg());
        }

    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteMetricsList(List<String> metricsIds) {
        /**
         * 1. delete metrics
         * 2. delete metrics fields
         * 3. delete collectorMetrics
         * 4. delete agg
         * 5. delete rule
         * 6. delete function, histogram, percentile
         * 7. delete customFieldRule
         */

        try {
            long startTime = System.currentTimeMillis();

            List<MetricsDO> metricsList = metricsDAO.listMetricsByMetricsIds(metricsIds);

            metricsDAO.deleteMetricsByIds(metricsIds);
            metricsFieldHandler.deleteByMetricsIds(metricsIds);
            collectorMetricsDAO.deleteByMetricsIds(metricsIds);

            List<String> aggregationMetricsList = metricsList.stream().
                    filter(metrics -> metrics.getType() == MetricsTypeEnum.AGGREGATION.getValue()).
                    map(metrics -> metrics.getId()).collect(Collectors.toList());
            if (!aggregationMetricsList.isEmpty()) {
                List<MetricsAggregationDO> metricsAggregationDOList = metricsAggregationHandler.getByMetricsIds(metricsIds);
                List<String> aggIds = metricsAggregationDOList.stream().map(ele -> ele.getId()).collect(Collectors.toList());
                deteleAggregationAndRules(aggIds);
            }

            long endTime = System.currentTimeMillis();
            log.info("batch delete Metrics - cost = {} ms",endTime-startTime);
        }  catch (InterruptedException | ExecutionException e) {
            log.error(ExceptionUtils.getTrace(e));
            throw new SiteException(InnerError.getCode(), InnerError.getErrMsg());
        }

    }

    private void deteleAggregationAndRules(List<String> aggIds) throws InterruptedException, ExecutionException {
        List<MetricsAggregationRuleDO> metricsAggregationRuleDOList = metricsAggregationRuleHandler.listRulesByAggIds(aggIds);
        metricsAggregationHandler.deleteByIds(aggIds);
        metricsAggregationRuleHandler.deleteByAggIds(aggIds);

        // Map<aggRuleType, List<ruleId>>
        Map<Integer,List<String>> ruleMap = new HashMap<>();
        metricsAggregationRuleDOList.stream().forEach(rule -> {
            String ruleId = rule.getId();
            Integer ruleType = rule.getAggRuleType();
            ruleMap.computeIfAbsent(ruleType, t -> Lists.newArrayList()).add(ruleId);
        });

        ruleMap.entrySet().forEach(e -> {
            if(e.getKey().equals(AggregationRuleType.function.value())){
                aggregationFunctionItemHandler.deleteByRuleIdList(e.getValue());
            }else if(e.getKey().equals(AggregationRuleType.histogram.value())){
                aggregationHistogramRangeItemHandler.deleteByRuleIdList(e.getValue());
            }else if(e.getKey().equals(AggregationRuleType.percentile.value())){
                aggregationPercentileItemHandler.deleteByRuleIdList(e.getValue());

            }
        });

        aggregationCustomFieldRuleHandler.deleteByAggIds(aggIds);

    }

    public List<MetricsAggregationRuleDO> listRulesByMetricsAggIds(List<String> metricsAggregationIds) {
        if(CollectionUtils.isEmpty(metricsAggregationIds)){
            return new ArrayList<>();
        }
        return metricsAggregationRuleHandler.listRulesByAggIds(metricsAggregationIds);
    }

    public List<MetricsAggregationDO> listMetricsAggByMetricsIds(List<String> metricsIds) {
        if(CollectionUtils.isEmpty(metricsIds)){
            return new ArrayList<>();
        }
        return metricsAggregationHandler.listMetricsAggByMetricsIds(metricsIds);
    }

    public boolean hasSameId(String id) {
        return metricsDAO.getCountById(id) > 0;
    }

    public void updateMetrics(MetricsDO metricsDO){
        metricsDAO.updateMetrics(metricsDO);
    }


    public void editMetricsTags(MetricsDO targetMetricsDO) {
        metricsDAO.editMetricsTags(targetMetricsDO);
    }

    public void addMetricsField(MetricsFieldDO metricsFieldDO) {
        metricsFieldHandler.insertMetricsField(metricsFieldDO);
    }

    public void editMetricsStatus(MetricsDO metricsDO) {
        metricsDAO.editMetricsStatus(metricsDO);
    }

    public void batchUpdateMetricDocumentLink(List<MetricsDO> metricsLinkList, List<String> tenantIds) {
        metricsDAO.editMetricsDocumentLink( metricsLinkList, tenantIds);
    }
    public List<String> listAllMetricsName(String tenantId) {
        return metricsDAO.listAllMetricsName(tenantId);
    }

    public List<MetricsDO> listMetricsByIds(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }
        return metricsDAO.listMetricsByMetricsIds(ids);
    }

    public int batchUpdateMetrics(List<MetricsDO> metricsDOList) {
        return metricsDAO.batchUpdateMetrics(metricsDOList);
    }

    public List<MetricsDO> listMetricsByCollectorId(String collectorId){
        return metricsDAO.listMetricsByCollectorId(collectorId);
    }

    public List<MetricsDO> findMetricsByNames(String tenantId, List<String> metricsNames){
        return metricsDAO.findMetricsByNames(tenantId, metricsNames);
    }

    public List<String> getAggRuleFieldsByMetricsId(String metricsId) {
        return metricsAggregationRuleHandler.getAggRuleFieldsByMetricsId(metricsId);
    }

    public MetricsDO queryMetricsByName(String name, String tenantId) {
        if (StringUtils.isBlank(name) || StringUtils.isBlank(tenantId)) {
            return new MetricsDO();
        }
        return metricsDAO.getMetricsByNameOfTenant(tenantId, name);
    }

    public void updateDocumentLink(MetricsDO metricsDO) {
        metricsDAO.newUpdateMetrics(metricsDO);
    }


    public void updateMetricsTags(MetricsDO metricsDO) {
        Assert.isTrue(StringUtils.isNotBlank(metricsDO.getId()), "metrics id can not be null");
        Assert.isTrue(StringUtils.isNotBlank(metricsDO.getTagNames()), "metrics tags can not be null");
        metricsDAO.updateMetricsTags(metricsDO);
    }

}
