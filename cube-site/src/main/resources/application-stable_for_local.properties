server.ssl.enabled=false
#lock config
resource.lock.inteval=20
#log Config
log.sampling.count.info=10000
log.sampling.count.warn=1000
log.sampling.count.error=1000
#mysql
dataSource.url=****************************************************************************************************************************************************************************************************************************
# To set in CSMS
#cube.dataSource.username=
# To set in CSMS
#cube.dataSource.password=
dataSource.csms_rotate_username_key= cube.dataSource.username
dataSource.csms_rotate_password_key= cube.dataSource.password
dataSourceStandby.csms_rotate_username_key= cube.dataSourceStandby.username
dataSourceStandby.csms_rotate_password_key= cube.dataSourceStandby.password
dataSourceStandby.url=****************************************************************************************************************************************************************************************************************************
# To set in CSMS
#cube.dataSourceStandby.username=
# To set in CSMS
#cube.dataSourceStandby.password=

#front-source path
spring.web.resources.static-locations=file:/home/<USER>/cube-front/static

test.kafka.connectivity.timeout=6

#HttpClient
httpClient.connection.pool.maxTotal=200
httpClient.connection.pool.maxPerRoute=20
httpClient.connection.request.timeout=2000
httpClient.connect.timeout=2000
httpClient.socket.timeout=10000

#IM alarm
self.monitor.im.url=https://inbots.zoom.us/incoming/hook/B2ZHKjYJu6XhveD2LpdA_R4i?format=fields
# To set in CSMS
#self.monitor.im.authorization=

#OKTA authentication, personal developer account
okta.oauth2.issuer=https://dev-448353.oktapreview.com/oauth2/default
# To set in CSMS
okta.oauth2.client-id=
# To set in CSMS
okta.oauth2.client-secret=
okta.oauth2.redirect-uri=/authorization-code/callback

logout.url=https://dev-448353.oktapreview.com/app/UserHome
#change to/** release all
site.oauth2.filter-url=/cfg/**
site.oauth2.filter-url2=/selfmonitor/**

log.filePath=cube-site/logs
apm.log.home=/data/logs/zoom_middleware/apm/Infra_Monitor_Cube-Site
monitor.executorsReplace.enabled=false

spring.jpa.show-sql= true
spring.jpa.hibernate.ddl-auto= none
spring.jpa.hibernate.naming.implicit-strategy=org.springframework.boot.orm.jpa.hibernate.SpringImplicitNamingStrategy
spring.jpa.hibernate.naming.physical-strategy=org.hibernate.boot.model.naming.CamelCaseToUnderscoresNamingStrategy
spring.jpa.database= MYSQL
spring.jpa.properties.hibernate.enable_lazy_load_no_trans= true


#spring.session
#spring.session REDIS or JDBC
spring.session.store-type=JDBC


# auth config
auth.issuer.name=cube
auth.audience.name=hub
# To set in CSMS
#auth.secret.key=

auth.common.audience.name=cube

auth.nws2cube.issuer.name=nws
# To set in CSMS
#auth.nws2cube.secret.key=
auth.nws2cube.secret.key=dummy

auth.asyncmq2cube.issuer.name=asyncmq
# To set in CSMS
#auth.asyncmq2cube.secret.key=
auth.asyncmq2cube.secret.key=dummy

auth.otter2cube.issuer.name=otter
# To set in CSMS
#auth.otter2cube.secret.key=
auth.otter2cube.secret.key=dummy

auth.fst2cube.issuer.name=fst
# To set in CSMS
#auth.fst2cube.secret.key=
auth.fst2cube.secret.key=dummy

auth.pbx2cube.issuer.name=pbxweb
# To set in CSMS
#auth.pbx2cube.secret.key=
auth.pbx2cube.secret.key=dummy

auth.riskDetect2cube.issuer.name=riskDetect
# To set in CSMS
#auth.riskDetect2cube.secret.key=
auth.riskDetect2cube.secret.key=dummy

auth.preplan2cube.issuer.name=preplan

auth.preplan2cube.secret.key=dummy

auth.zcp2cube.audience.name=infra-devops-cube
auth.zcp2cube.issuer.name=zcp-iam



# CSMS configs only used for testing environments: dev, hz, local, stable, etc.
# For prod environment, configs will be set in csms.properties instead of application-{active_profile}.properties.

#true enable, false disable
csms.enable=true
#csms domain
csms.endpoints=https://csmsdev.zoomdev.us
#config in csms console path
csms.app.path=dev/cube
# non-ec2 bind credential file
# For local debug, use the credential file directory. Apply for the access key from the CSMS maintainer!
# Do not upload the credential to Git repository!!!
csms.app.credential.file=/usr/local/credentials

#CORS configs
cors.allowed.url.okta=dev-448353.oktapreview.com

dataparser.must.admin=yes


offline.send.service=false

site.okta.redirect-uri=https://cube-site.zoomdev.us:8443/oauth2/authorization/okta

cube.dataSource.clickhouse.servers=18.235.112.135
cube.dataSource.clickhouse.username=zoom_cube
cube.clickhouse.flush.interval.millsecond=5000
cube.clickhouse.flush.max.batch.size=100000
cube.clickhouse.thread.pool.size=20

cube.incident.url.alert=https://cube-debug.zoomdev.us:8443/

auth.globaltracing2cube.issuer.name=global-tracing
#To set in CSMS
auth.globaltracing2cube.secret.key=dummy

#only used in ZCP_CUBE
#cube.authorization.enableJwt=true


cmdb.login.url = https://devops-agw-sz.zoomdev.us:6443/gateway/api/v1/user/login
cmdb.server.query.url = https://devops-agw-sz.zoomdev.us:6443/cmdb/api/v2/server/batch
cmdb.device.query.url = https://devops-agw-sz.zoomdev.us:6443/cmdb/api/v1/netdeviceinfo
cmdb.tor.topo.query.url = https://devops-agw-sz.zoomdev.us:6443/cmdb/api/v1/netdeviceinfo/tortopo
cmdb.ca.file.path = /home/<USER>/cube-site/ca.crt
cmdb.server.topic = devops_watch_cmdb_server_v1
cmdb.device.topic = devops_watch_cmdb_netdeviceinfosystemchassis

auth.mrt2cube.issuer.name=mrt
auth.mrt2cube.secret.key=dummy

auth.csms2cube.issuer.name=csms
auth.csms2cube.secret.key=dummy

auth.esms2cube.issuer.name=esms
auth.esms2cube.secret.key=dummy

auth.bigdata2cube.issuer.name=bigdata
auth.bigdata2cube.secret.key=dummy

server.servlet.session.timeout=24h



capacity.distributedLock.type=capacity_assigner

async.mq.admin.endpoint=https://asyncmq.zoomdev.us
async.mq.admin.username=app_cube_admin

auth.znm2cube.issuer.name=znm
auth.znm2cube.secret.key=dummy

# probe(E2E)
cube.scheduler.url=https://cube-schedule-perf.zoomdev.us
scheduler.job.group.name=cube_probe
scheduler.job.topic.name=cube_probe_scheduler_job

probe.metrics.service.name=cube_probe
probe.metrics.table.name=cube_probe.probe_task_metrics

probe.task.file.path=/tmp/probe


alarm.async.mq.username=app_cube

auth.asyncfile2cube.issuer.name=asyncfileconsole
auth.asyncfile2cube.secret.key=dummy

auth.openapi2cube.issuer.name=openapi
auth.openapi2cube.secret.key=dummy

cube.template.endpoint.url=https://inbots.zoom.us/incoming/hook/l3mLXxaohl6DMdoq0dvOm_Ca?format=fields

alarm.pii-redirect-url.prefix=/alarm-redirect/dashboard

#cube.authorization.enableJwt=true

# agent heart beat
agent.heart.beat.topic = us_agent_heart_beat_forward_cube
agent.heart.beat.consumer.group = cube-group-dev
agent.heart.check.topic = us_agent_heart_check
hub.heart.check.topic = us_hub_heart_check
heart.check.consumer.group = cube-group-dev

async.file.url=https://file.zoomdev.us
# To set in CSMS
#async.file.secret=

host.detail.info.database=Infra_Monitor_Cube_Site
webop.api.url={"integration":"https://devop-integration.zoomdev.us:8443","ds01":"https://ds01op.zoomdev.us:8443","dev":"https://devop.zoomdev.us:8443"}

asyncmq.csms.key.listen.list=async.mq.admin.password,async.mq.password,alarm.async.mq.password

cube.env=us01
s3.open.env=us01
agent.consumer.env=us01

webhooks.api.endpoint=https://appsnws.zoomdev.us
xms.api.endpoint=https://bxmppapi.zoomdev.us
webop.api.endpoint=https://devop.zoomdev.us:8443