package us.zoom.cube.site.lib.output.influxdb;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import org.springframework.format.annotation.DateTimeFormat;
import us.zoom.cube.site.lib.output.tenant.TenantOut;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/8/12 10:18 AM
 */

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class InfluxdbClusterOut {

    String id;

    String name;

    String type;

    String serverUrl;

    String clusterId;

    String regionId;

    Boolean isDefault;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    Date createTime = new Date();

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    Date modifyTime = new Date();

    List<TenantOut> relatedTenant;
}
