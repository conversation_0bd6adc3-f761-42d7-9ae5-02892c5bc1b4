package com.zoom.op.monitor.config;

import com.zaxxer.hikari.HikariDataSource;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.orm.jpa.HibernateProperties;
import org.springframework.boot.autoconfigure.orm.jpa.HibernateSettings;
import org.springframework.boot.autoconfigure.orm.jpa.JpaProperties;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.EnvironmentAware;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.env.Environment;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.orm.jpa.support.PersistenceAnnotationBeanPostProcessor;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import jakarta.persistence.EntityManager;
import jakarta.persistence.EntityManagerFactory;
import javax.sql.DataSource;
import java.util.Map;
import java.util.Objects;

@Configuration
@EnableJpaRepositories(entityManagerFactoryRef = "metricManagerFactory",
        transactionManagerRef = "metricTransactionManager",
        basePackages = "com.zoom.op.monitor.dao")
@EnableTransactionManagement
@ConditionalOnProperty(name = "mysql.datasource.enabled", havingValue = "true", matchIfMissing = true)
public class MetricDefinitionDataSourceConfig implements EnvironmentAware {

    private Environment environment;

    @Override
    public void setEnvironment(Environment environment) {
        this.environment = environment;
    }

    @Bean(name = "metricDataSource")
    @Qualifier("metricDataSource")
    @Primary
    public DataSource primaryDataSource() {
        HikariDataSource ds = new HikariDataSource();
        ds.setJdbcUrl(environment.getProperty("datasource.url"));
        ds.setUsername(environment.getProperty("cube.dataSource.username"));
        ds.setPassword(environment.getProperty("cube.dataSource.password"));
        ds.setMaximumPoolSize(environment.getProperty("dataSource.maxActive", Integer.class));
        ds.setMinimumIdle(environment.getProperty("dataSource.minIdle", Integer.class));
        ds.setCsmsRotateUsernameKey(environment.getProperty("dataSource.csms_rotate_username_key"));
        ds.setCsmsRotatePasswordKey(environment.getProperty("dataSource.csms_rotate_password_key"));
        ds.setPoolName("CubeMainDS");

        return ds;
    }

    @Bean(name = "metricDataSourceStandby")
    @Qualifier("metricDataSourceStandby")
    public DataSource standbyDataSource() {
        HikariDataSource ds = new HikariDataSource();
        ds.setJdbcUrl(environment.getProperty("dataSourceStandby.url"));
        ds.setUsername(environment.getProperty("cube.dataSourceStandby.username"));
        ds.setPassword(environment.getProperty("cube.dataSourceStandby.password"));
        ds.setMaximumPoolSize(environment.getProperty("dataSource.maxActive", Integer.class));
        ds.setMinimumIdle(environment.getProperty("dataSource.minIdle", Integer.class));
        ds.setCsmsRotateUsernameKey(environment.getProperty("dataSourceStandby.csms_rotate_username_key"));
        ds.setCsmsRotatePasswordKey(environment.getProperty("dataSourceStandby.csms_rotate_password_key"));
        ds.setPoolName("CubeStandbyDS");

        return ds;
    }

    @Bean(name = "metricManagerFactory")
    @Primary
    public LocalContainerEntityManagerFactoryBean entityManagerFactory(
            @Qualifier(value = "dynamicDataSource") DataSource metricDataSource,
            JpaProperties jpaProperties,
            HibernateProperties hibernateProperties,
            EntityManagerFactoryBuilder builder) {

        Map<String, Object> finalHibernateProperties =
                hibernateProperties.determineHibernateProperties(

                        jpaProperties.getProperties(), new HibernateSettings());

        return builder.dataSource(metricDataSource)

                .properties(finalHibernateProperties)

                .packages("com.zoom.op.monitor.domain")

                .build();
    }

    @Bean(name = "metricManager")
    @Primary
    public EntityManager entityManager(@Qualifier(value = "metricManagerFactory") LocalContainerEntityManagerFactoryBean entityManagerFactory) {

        return Objects.requireNonNull(entityManagerFactory.getObject()).createEntityManager();
    }

    @Bean(name = "metricTransactionManager")
    @Primary
    public PlatformTransactionManager transactionManager(EntityManagerFactory entityManagerFactory) {

        JpaTransactionManager manager = new JpaTransactionManager();

        manager.setEntityManagerFactory(entityManagerFactory);

        return manager;
    }

    @Bean
    public BeanPostProcessor persistenceTranslation() {

        return new PersistenceAnnotationBeanPostProcessor();
    }
}

