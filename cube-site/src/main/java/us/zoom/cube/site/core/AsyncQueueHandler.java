package us.zoom.cube.site.core;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.lib.common.AuthEncrTypeEnum;
import us.zoom.cube.lib.common.SaslMechanismEnum;
import us.zoom.cube.site.lib.input.AqInput;
import us.zoom.infra.dao.model.AsyncQueueDO;
import us.zoom.infra.dao.model.OutputAqDO;
import us.zoom.infra.dao.service.AsyncQueueDAO;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@Component
public class AsyncQueueHandler {
    @Autowired
    private AsyncQueueDAO asyncQueueDAO;

    @Autowired
    private RsaService rsaService;

    public List<AsyncQueueDO> listByAqGroupId(String aqGroupId)  throws Exception{
        List<AsyncQueueDO> asyncQueueDOList= asyncQueueDAO.listByAqGroupId(aqGroupId);
        return getOutputAqForDecrypt(asyncQueueDOList);
    }

    public List<AsyncQueueDO> listRawByAqGroupId(String aqGroupId){
        List<AsyncQueueDO> asyncQueueDOList= asyncQueueDAO.listByAqGroupId(aqGroupId);
        return asyncQueueDOList;
    }

    public List<AsyncQueueDO> listAll() throws Exception {
        List<AsyncQueueDO> asyncQueueDOS=   asyncQueueDAO.listAll();
        return  getOutputAqForDecrypt(asyncQueueDOS);

    }

    public List<AsyncQueueDO> listAllNotDecrypt() {
        return asyncQueueDAO.listAll();
    }

    public List<AsyncQueueDO> findByNameLike(String asyncQueueGroupId, String name, int pageIndex, int pageSize) throws Exception {
        List<AsyncQueueDO> asyncQueueDOS= asyncQueueDAO.findByNameLike(asyncQueueGroupId, name,pageSize*(pageIndex-1), pageSize);
        return  getOutputAqForDecrypt(asyncQueueDOS);

    }

    /**
     * Deprecated because of the mismatch of method name and parameter order
     * @param asyncQueueGroupId
     * @param name
     * @return
     */
    @Deprecated
    public List<AsyncQueueDO> getByNameAndGroupId(String asyncQueueGroupId, String name) {
        List<AsyncQueueDO> asyncQueueDOList= asyncQueueDAO.getByNameAndGroupId(name, asyncQueueGroupId);
        return asyncQueueDOList;
    }

    public List<AsyncQueueDO> findByNameAndGroupId(String name, String asyncQueueGroupId) {
        List<AsyncQueueDO> asyncQueueDOList= asyncQueueDAO.getByNameAndGroupId(name, asyncQueueGroupId);
        return asyncQueueDOList;
    }

    public int getCountByNameLike(String asyncQueueGroupId, String name) {
        return  asyncQueueDAO.getCountByNameLike(asyncQueueGroupId, name);
    }

    public void addAq(AsyncQueueDO asyncQueueDO) throws Exception {
        adjustData(asyncQueueDO);
        encrypt(asyncQueueDO);
        asyncQueueDAO.addAq(asyncQueueDO);
    }


    private List<AsyncQueueDO> getOutputAqForDecrypt(List<AsyncQueueDO> asyncQueueDOS) throws Exception {
        if (CollectionUtils.isEmpty(asyncQueueDOS)) {
            return Collections.emptyList();
        }
        for (AsyncQueueDO asyncQueueDO : asyncQueueDOS) {
            decrypt(asyncQueueDO);
        }
        return asyncQueueDOS;
    }

    public void editAq(AsyncQueueDO asyncQueueDO) throws Exception {
        adjustData(asyncQueueDO);
        encrypt(asyncQueueDO);
        asyncQueueDAO.editAq(asyncQueueDO);
    }

    public AsyncQueueDO getAqById(String id) throws Exception {
        AsyncQueueDO asyncQueueDO=   asyncQueueDAO.getAqById(id);
        decrypt(asyncQueueDO);
        return asyncQueueDO;
    }

    public List<AsyncQueueDO> listUnUsed() throws Exception {
        List<AsyncQueueDO> asyncQueueDOS=  asyncQueueDAO.listUnUsed();
        return getOutputAqForDecrypt(asyncQueueDOS);
    }

    public void deleteById(String id) {
        asyncQueueDAO.deleteById(id);
    }

    public boolean hasSameName(String name, String asyncQueueGroupId) {
        return asyncQueueDAO.getCountByName(name, asyncQueueGroupId) > 0;
    }

    public List<AsyncQueueDO>  getCountByAsyncQueueGroupId(String asyncQueueGroupId) {
        return asyncQueueDAO.getCountByAsyncQueueGroupId(asyncQueueGroupId);
    }



    private void decrypt(AsyncQueueDO asyncQueueDO) throws Exception {
        if(StringUtils.isNotBlank(asyncQueueDO.getKeyPwd())){
            asyncQueueDO.setKeyPwd(rsaService.decrypt(asyncQueueDO.getKeyPwd()));
        }

        if(StringUtils.isNotBlank(asyncQueueDO.getKeystorePwd())){
            asyncQueueDO.setKeystorePwd(rsaService.decrypt(asyncQueueDO.getKeystorePwd()));
        }

        if(StringUtils.isNotBlank(asyncQueueDO.getTruststorePwd())){
            asyncQueueDO.setTruststorePwd(rsaService.decrypt(asyncQueueDO.getTruststorePwd()));
        }

        if(StringUtils.isNotBlank(asyncQueueDO.getJaasConfig())){
            asyncQueueDO.setJaasConfig(rsaService.decrypt(asyncQueueDO.getJaasConfig()));
        }
    }


    private void encrypt(AsyncQueueDO asyncQueueDO) throws Exception {
        if(StringUtils.isNotBlank(asyncQueueDO.getKeyPwd())){
            asyncQueueDO.setKeyPwd(rsaService.encrypt(asyncQueueDO.getKeyPwd()));
        }

        if(StringUtils.isNotBlank(asyncQueueDO.getKeystorePwd())){
            asyncQueueDO.setKeystorePwd(rsaService.encrypt(asyncQueueDO.getKeystorePwd()));
        }


        if(StringUtils.isNotBlank(asyncQueueDO.getTruststorePwd())){
            asyncQueueDO.setTruststorePwd(rsaService.encrypt(asyncQueueDO.getTruststorePwd()));
        }


        if(StringUtils.isNotBlank(asyncQueueDO.getJaasConfig())){
            asyncQueueDO.setJaasConfig(rsaService.encrypt(asyncQueueDO.getJaasConfig()));
        }
    }



    private void adjustData(AsyncQueueDO asyncQueueDO) {



        if(StringUtils.isBlank(asyncQueueDO.getKeystorePath())){
            asyncQueueDO.setKeystorePath("");
        }

        if(StringUtils.isBlank(asyncQueueDO.getKeystorePwd())){
            asyncQueueDO.setKeystorePwd("");
        }

        if(StringUtils.isBlank(asyncQueueDO.getKeyPwd())){
            asyncQueueDO.setKeyPwd("");
        }
        if(StringUtils.isBlank( asyncQueueDO.getIdenAlgorithm())){
            asyncQueueDO.setIdenAlgorithm("");
        }

        if(StringUtils.isBlank(asyncQueueDO.getKeyPwd())){
            asyncQueueDO.setKeyPwd("");
        }

        if(null == asyncQueueDO.getSaslMechanism()){
            asyncQueueDO.setSaslMechanism(SaslMechanismEnum.PLAIN.getCode());
        }

        if(asyncQueueDO.getAuthEncrType() != null && AuthEncrTypeEnum.no.getValue()==asyncQueueDO.getAuthEncrType()){
            asyncQueueDO.setIdenAlgorithm("");
            asyncQueueDO.setKeyPwd("");
            asyncQueueDO.setKeystorePath("");
            asyncQueueDO.setKeystorePwd("");
            asyncQueueDO.setTruststorePath("");
            asyncQueueDO.setTruststorePwd("");
            asyncQueueDO.setJaasConfig("");
            return;
        }

        if(asyncQueueDO.getAuthEncrType() != null && AuthEncrTypeEnum.ssl.getValue()==asyncQueueDO.getAuthEncrType()){
            asyncQueueDO.setJaasConfig("");
            return;
        }

    }

    public void deleteByAsyncQueueGroupId(String groupId) {
        asyncQueueDAO.deleteByAsyncQueueGroupId(groupId);
    }

    public void editAqBasic(AsyncQueueDO asyncQueueDO) {
        asyncQueueDAO.editAqBasic(asyncQueueDO);
    }
}
