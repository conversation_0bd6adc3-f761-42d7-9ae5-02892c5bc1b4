{"dataParser": {"name": "ZC", "rawDataType": 1, "rawDataParseType": "groovy", "invokeFunction": "parseMessage", "rawDataParseRule": "import groovy.json.JsonSlurper\nimport java.text.SimpleDateFormat;\n\n    def parseMessage(String message) {\n        def jsonSlurper = new JsonSlurper()\n        List<Map<String, Object>> messageJson = jsonSlurper.parseText(message)\n        List<Map<String, Object>> result = new ArrayList(messageJson.size())\n        SimpleDateFormat format = new SimpleDateFormat(\"MM/dd/yyyy HH:mm:ss:SSS\")\n\n        for (Map<String, Object> map : messageJson) {\n            String log = map.message\n            String[] logArray = log.split(\",\")\n            Long collectTime = format.parse(logArray[1]).time\n\n            Map<String, Object> eleMap = new HashMap()\n\n            eleMap.put(\"region\", map.region)\n            eleMap.put(\"hostname\", map.hostname)\n            eleMap.put(\"regionId\", parseZoneAndRegion(map.regionId))\n            eleMap.put(\"zoneName\", parseZoneAndRegion(map.zoneName))\n            eleMap.put(\"mmc\", map.mmc)\n            eleMap.put(\"capacity\", map.capacity)\n            eleMap.put(\"kafkaRegion\", map.kafka_region)\n\n            String instance = getInstance(map.source)\n            eleMap.put(\"instance\", instance)\n            String publicIp = getPublicIpFromInstanceId(map.instance_id)\n            eleMap.put(\"publicIp\", publicIp)\n            String instanceId = publicIp + \"#\" + instance\n            eleMap.put(\"instanceId\", instanceId)\n            String cluster = getCluster(map.cluster_env)\n            eleMap.put(\"cluster\", cluster)\n\n            eleMap.put(\"clusterEnv\", map.cluster_env)\n\n\n            eleMap.put(\"ts\", collectTime)\n            eleMap.put(\"meetingID\", getMeetingID(logArray[2]))\n            eleMap.put(\"dataType\", logArray[3])\n\n            // Added 04-20,  missing ENV and Zone\n            eleMap.put(\"Region\", map.region)\n            eleMap.put(\"Hostname\", map.hostname)\n            eleMap.put(\"Instance\", getInstance(map.source))\n            eleMap.put(\"InstanceId\", instanceId)\n            eleMap.put(\"MMC\", map.mmc)\n            eleMap.put(\"Cluster\", cluster)\n            eleMap.put(\"PublicIp\", publicIp)\n            eleMap.put(\"MeetingId\", getMeetingID(logArray[2]))\n            eleMap.put(\"Capacity\", map.capacity)\n\n            eleMap.put(\"message\", log.substring(log.indexOf(logArray[3]) + logArray[3].length() + 1))\n\n            result.add(eleMap)\n        }\n        return result\n    }\n\n    public String getInstance(String source) {\n        if (source==null || source.isBlank()) {\n            return source;\n        }\n        int endIndex = source.length();\n        while (endIndex > 0 && source.charAt(endIndex - 1) == '/') {\n            endIndex--;\n        }\n        int startIndex = endIndex - 1\n        while (startIndex > 0 && source.charAt(startIndex - 1) != '/') {\n            startIndex--\n        }\n        int first = source.indexOf(\"_\", startIndex + 1);\n        if (first <= 0) {\n            return null\n        }\n        int second = source.indexOf(\"_\", first + 1)\n//        if (StringUtils.containsAny(source, \"monitor_lttgw_srvr\", \"e2e_server\")) {\n        if (source.contains(\"monitor_lttgw_srvr\") || source.contains(\"e2e_server\")) {\n            second = source.indexOf(\"_\", second + 1)\n        }\n        if (second <= 0) {\n            return null\n        }\n        return source.substring(first + 1, second)\n    }\n\n    public String getPublicIpFromInstanceId(String instance) {\n        if (instance==null || instance.isBlank()) {\n            return instance\n    }\n        int index = instance.indexOf(\".\")\n        if (index <= 0) {\n            return instance\n    }\n        index = instance.indexOf(\".\", index + 1)\n        if (index <= 0) {\n            return instance\n    }\n        index = instance.indexOf(\".\", index + 1)\n        if (index <= 0) {\n            return instance\n    }\n        char ch = 0\n        for (index++; index < instance.length(); index++) {\n            ch = instance.charAt(index)\n            if (ch < '0' || ch > '9') {\n                return instance.substring(0, index);\n        }\n    }\n        return instance\n}\n\n    public String getCluster(String env) {\n        if (env != null && env.contains(\",\")) {\n            String[] array = env.split(\",\")\n            return array[0]\n        }\n        return env\n    }\n\n    public String getMeetingID(String meetingIdStr) {\n        if (meetingIdStr==null || meetingIdStr.isBlank()) {\n            return \"\"\n        }\n        int i = meetingIdStr.indexOf(\" \")\n        if (i != -1) {\n            return meetingIdStr.substring(0, i)\n        }\n        return meetingIdStr\n    }\n\n    public String parseZoneAndRegion(String input) {\n        if (input==null || input.isBlank()) {\n            return input;\n        }\n        return substringBefore(substringBefore(input, \";\"), \"#\").trim();\n    }\n\n    public String substringBefore(final String str, final String separator) {\n        final int pos = str.indexOf(separator);\n        return pos == -1 ? str : str.substring(0, pos);\n    }\n\n    public String parseClusterEnv(String env) {\n        if (env==null || env.isBlank()) {\n            return env\n        }\n        return env.replace(\",\", \"|\")\n    }", "useStatus": 1}, "pipeline": [{"name": "ZC_CC_LOST", "filterRule": "dataType=='CC-LOST'", "useStatus": 1, "order": 0, "fields": [{"fieldName": "cluster", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "mmc", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "instance", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "env", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "cc_adddress", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "lost_reason", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "cc_address", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "hostname", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "instanceid", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "zone", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "connect_cc_fail_count", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "region", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "cc_lost_region_failure_count", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}], "processores": [{"name": "split", "order": 0, "type": "split", "sourceField": "message", "schema": {"separator": ",", "entries": [{"index": 0, "name": "address", "type": "string"}, {"index": 1, "name": "reason", "type": "number"}]}}, {"name": "expression", "order": 1, "type": "expression", "schema": {"entries": [{"name": "instanceid", "expression": "instanceId", "type": "string"}, {"name": "connect_cc_fail_count", "expression": "reason", "type": "number"}, {"name": "cc_adddress", "expression": "address", "type": "string"}, {"name": "lost_reason", "expression": "reason", "type": "number"}, {"name": "instance", "expression": "hostname+':'+instance", "type": "string"}, {"name": "cc_lost_region_failure_count", "expression": "reason", "type": "number"}, {"name": "zone", "expression": "zoneName", "type": "string"}, {"name": "hostname", "expression": "hostname", "type": "string"}, {"name": "cluster", "expression": "cluster", "type": "string"}, {"name": "region", "expression": "region", "type": "string"}, {"name": "cc_address", "expression": "address", "type": "string"}, {"name": "env", "expression": "env", "type": "string"}, {"name": "mmc", "expression": "mmc", "type": "string"}]}}]}, {"name": "ZC_WEB_LOST", "filterRule": "dataType=='WEB-LOST'", "useStatus": 1, "order": 1, "fields": [{"fieldName": "result", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "cluster", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "hostname", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "dns_time", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "mmc", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "instance", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "instanceid", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "zone", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "connect_web_lost_count", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "error", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "region", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "url", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}], "processores": [{"name": "split", "order": 0, "type": "split", "sourceField": "message", "schema": {"separator": ",", "entries": [{"index": 0, "name": "url", "type": "string"}, {"index": 1, "name": "thead_index", "type": "number"}, {"index": 2, "name": "cluster_id", "type": "string"}, {"index": 3, "name": "web_ip", "type": "string"}, {"index": 4, "name": "result", "type": "number"}, {"index": 5, "name": "dns_time", "type": "string"}, {"index": 6, "name": "tracking_id", "type": "string"}, {"index": 7, "name": "error_buf", "type": "string"}]}}, {"name": "expression", "order": 1, "type": "expression", "schema": {"entries": [{"name": "instanceid", "expression": "instanceId", "type": "string"}, {"name": "connect_web_lost_count", "expression": "1", "type": "number"}, {"name": "dns_time", "expression": "dns_time", "type": "string"}, {"name": "instance", "expression": "instance", "type": "string"}, {"name": "result", "expression": "result", "type": "number"}, {"name": "zone", "expression": "zoneName", "type": "string"}, {"name": "hostname", "expression": "hostname", "type": "string"}, {"name": "url", "expression": "url", "type": "string"}, {"name": "mmc", "expression": "mmc", "type": "string"}, {"name": "cluster", "expression": "cluster", "type": "string"}, {"name": "error", "expression": "error_buf", "type": "string"}, {"name": "region", "expression": "region", "type": "string"}]}}]}, {"name": "ZC_ZONE_LOAD", "filterRule": "dataType=='ZONE-LOAD'", "useStatus": 1, "order": 2, "fields": [{"fieldName": "zone_load_real_meeting_count", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "cluster", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "mmc", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "instance", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "demo_meeting_count", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "demo_attendee_count", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "hostname", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "zone_load_p2p_client_count", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "zone", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "zone_load_panelist", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "client", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "zone_load_suspend_mmr_count", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "zone_load_telephony_count", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "zone_load_meeting_count", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "test_attendee_count", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "test_meeting_count", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "zone_load_client_count", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "zone_load_non_p2p_client_count", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "panelist", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "service_type", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "zone_load_viewonly_count", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "instanceid", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "zone_load_ping_count", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "region", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "zone_load_total_mmr_count", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}], "processores": [{"name": "split", "order": 0, "type": "split", "sourceField": "message", "schema": {"separator": ",", "entries": [{"index": 0, "name": "meetingCount", "type": "number"}, {"index": 1, "name": "clientCount", "type": "number"}, {"index": 2, "name": "telephonyCount", "type": "number"}, {"index": 3, "name": "pingCount", "type": "number"}, {"index": 4, "name": "nonP2pClientCount", "type": "number"}, {"index": 5, "name": "p2pClientCount", "type": "number"}, {"index": 6, "name": "viewonlyCount", "type": "number"}, {"index": 7, "name": "panelist<PERSON>ount", "type": "number"}, {"index": 8, "name": "suspendMMRCount", "type": "number"}, {"index": 9, "name": "totalMMRCount", "type": "number"}, {"index": 10, "name": "realMeetingCount", "type": "number"}]}}, {"name": "expression", "order": 1, "type": "expression", "schema": {"entries": [{"name": "zone_load_non_p2p_client_count", "expression": "nonP2pClientCount", "type": "number"}, {"name": "instance", "expression": "hostname+':'+instance", "type": "string"}, {"name": "zone_load_p2p_client_count", "expression": "p2pClientCount", "type": "number"}, {"name": "zone_load_real_meeting_count", "expression": "realMeetingCount", "type": "number"}, {"name": "test_attendee_count", "expression": "clientCount", "type": "number"}, {"name": "region", "expression": "region", "type": "string"}, {"name": "zone_load_panelist", "expression": "panelist<PERSON>ount", "type": "number"}, {"name": "zone_load_telephony_count", "expression": "telephonyCount", "type": "number"}, {"name": "zone_load_ping_count", "expression": "pingCount", "type": "number"}, {"name": "mmc", "expression": "mmc", "type": "string"}, {"name": "zone_load_client_count", "expression": "clientCount", "type": "number"}, {"name": "zone_load_viewonly_count", "expression": "viewonlyCount", "type": "number"}, {"name": "zone_load_meeting_count", "expression": "meetingCount", "type": "number"}, {"name": "instanceid", "expression": "instanceId", "type": "string"}, {"name": "demo_attendee_count", "expression": "clientCount", "type": "number"}, {"name": "client", "expression": "clientCount", "type": "number"}, {"name": "hostname", "expression": "hostname", "type": "string"}, {"name": "zone", "expression": "zoneName", "type": "string"}, {"name": "zone_load_suspend_mmr_count", "expression": "suspendMMRCount", "type": "number"}, {"name": "cluster", "expression": "cluster", "type": "string"}, {"name": "panelist", "expression": "panelist<PERSON>ount", "type": "number"}, {"name": "service_type", "expression": "type", "type": "string"}, {"name": "zone_load_total_mmr_count", "expression": "totalMMRCount", "type": "number"}, {"name": "demo_meeting_count", "expression": " meetingCount", "type": "number"}, {"name": "test_meeting_count", "expression": "meetingCount", "type": "number"}]}}]}, {"name": "ZC_THREAD_BLOCK", "filterRule": "dataType=='THREAD-BLOCK'", "useStatus": 1, "order": 3, "fields": [{"fieldName": "mmc", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "block_thread_count", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "instanceid", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "instance", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "region", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "zonename", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "block_thread_timing", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}], "processores": [{"name": "split", "order": 0, "type": "split", "sourceField": "message", "schema": {"separator": ",", "entries": [{"index": 0, "name": "escapeTime", "type": "number"}]}}, {"name": "expression", "order": 1, "type": "expression", "schema": {"entries": [{"name": "instanceid", "expression": "instanceId", "type": "string"}, {"name": "instance", "expression": "hostname+':'+instance", "type": "string"}, {"name": "block_thread_timing", "expression": "escapeTime", "type": "number"}, {"name": "block_thread_count", "expression": "escapeTime", "type": "number"}, {"name": "zonename", "expression": "zoneName", "type": "string"}, {"name": "mmc", "expression": "mmc", "type": "string"}, {"name": "region", "expression": "region", "type": "string"}]}}]}, {"name": "ZC_CC_LAZY", "filterRule": "dataType=='CC-LAZY'", "useStatus": 1, "order": 4, "fields": [{"fieldName": "cluster", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "mmc", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "max_rtt", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "instance", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "cc_lazy_count", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "lazy_count", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "min_rtt", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "truck_note", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "req_note", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "cc_addr", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "hostname", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "instanceid", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "cc_dest_note", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "zone", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "wma_rtt", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "current_rtt", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "elapsed_time", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "cc_request_id", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "index_note", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "user_data_request_id", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "region", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "addr", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "index_id", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}], "processores": [{"name": "split", "order": 0, "type": "split", "sourceField": "message", "schema": {"separator": ",", "entries": [{"index": 0, "name": "elapsed_time", "type": "number"}, {"index": 1, "name": "truck_note", "type": "string"}, {"index": 2, "name": "user_data_request_id", "type": "number"}, {"index": 3, "name": "req_note", "type": "string"}, {"index": 4, "name": "cc_request_id", "type": "number"}, {"index": 6, "name": "index", "type": "number"}, {"index": 6, "name": "index", "type": "number"}, {"index": 7, "name": "cc_dest_note", "type": "string"}, {"index": 8, "name": "addr", "type": "string"}, {"index": 9, "name": "current_rtt", "type": "string"}, {"index": 10, "name": "min_rtt", "type": "string"}, {"index": 11, "name": "max_rtt", "type": "string"}, {"index": 12, "name": "wma_rtt", "type": "string"}]}}, {"name": "expression", "order": 1, "type": "expression", "schema": {"entries": [{"name": "cc_addr", "expression": "addr", "type": "string"}, {"name": "instanceid", "expression": "instanceId", "type": "string"}, {"name": "min_rtt", "expression": "min_rtt", "type": "string"}, {"name": "instance", "expression": "instance", "type": "string"}, {"name": "wma_rtt", "expression": "wma_rtt", "type": "string"}, {"name": "lazy_count", "expression": "index.index_id", "type": "number"}, {"name": "zone", "expression": "zoneName", "type": "string"}, {"name": "hostname", "expression": "hostname", "type": "string"}, {"name": "addr", "expression": "addr", "type": "string"}, {"name": "truck_note", "expression": "truck_note", "type": "string"}, {"name": "region", "expression": "region", "type": "string"}, {"name": "cluster", "expression": "cluster", "type": "string"}, {"name": "cc_dest_note", "expression": "cc_dest_note", "type": "string"}, {"name": "user_data_request_id", "expression": "user_data_request_id", "type": "number"}, {"name": "cc_lazy_count", "expression": "1", "type": "number"}, {"name": "elapsed_time", "expression": "elapsed_time", "type": "number"}, {"name": "current_rtt", "expression": "current_rtt", "type": "string"}, {"name": "index_note", "expression": "index.index_note", "type": "string"}, {"name": "index_id", "expression": "index.index_id", "type": "number"}, {"name": "max_rtt", "expression": "max_rtt", "type": "string"}, {"name": "req_note", "expression": "req_note", "type": "string"}, {"name": "mmc", "expression": "mmc", "type": "string"}, {"name": "cc_request_id", "expression": "cc_request_id", "type": "number"}]}}]}, {"name": "ZC_ZC_STAT", "filterRule": "dataType=='ZC-STAT'", "useStatus": 1, "order": 5, "fields": [{"fieldName": "minstat4", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "cluster", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "stat", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "mmc", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "minstat2", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "instance", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "minstat3", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "minstat0", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "minstat1", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "stat4", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "env", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "hostname", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "instanceid", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "stat2", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "zone", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "stat3", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "stat0", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "stat1", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "region", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}], "processores": [{"name": "split", "order": 0, "type": "split", "sourceField": "message", "schema": {"separator": ",", "entries": [{"index": 0, "name": "stat0", "type": "number"}, {"index": 1, "name": "stat1", "type": "number"}, {"index": 2, "name": "stat2", "type": "number"}, {"index": 3, "name": "stat3", "type": "number"}, {"index": 4, "name": "stat4", "type": "number"}]}}, {"name": "expression", "order": 1, "type": "expression", "schema": {"entries": [{"name": "minstat1", "expression": "stat1", "type": "number"}, {"name": "instanceid", "expression": "instanceId", "type": "string"}, {"name": "minstat2", "expression": "stat2", "type": "number"}, {"name": "minstat0", "expression": "stat0", "type": "number"}, {"name": "instance", "expression": "instance", "type": "string"}, {"name": "hostname", "expression": "hostname", "type": "string"}, {"name": "zone", "expression": "zoneName", "type": "string"}, {"name": "cluster", "expression": "cluster", "type": "string"}, {"name": "region", "expression": "region", "type": "string"}, {"name": "stat", "expression": "stat0", "type": "number"}, {"name": "stat4", "expression": "stat4", "type": "number"}, {"name": "minstat4", "expression": "stat4", "type": "number"}, {"name": "minstat3", "expression": "stat3", "type": "number"}, {"name": "env", "expression": "env", "type": "string"}, {"name": "stat2", "expression": "stat2", "type": "number"}, {"name": "mmc", "expression": "mmc", "type": "string"}, {"name": "stat3", "expression": "stat3", "type": "number"}, {"name": "stat1", "expression": "stat1", "type": "number"}, {"name": "stat0", "expression": "stat0", "type": "number"}]}}]}, {"name": "ZC_SUSPEND_MMR", "filterRule": "dataType=='SUSPEND-MMR'", "useStatus": 1, "order": 6, "fields": [{"fieldName": "ssl_address", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "process", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "mmc", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "hostname", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "stat", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "mmz", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "region", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "eip", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}], "processores": [{"name": "split", "order": 0, "type": "split", "sourceField": "message", "schema": {"separator": ",", "entries": [{"index": 0, "name": "mmr_address", "type": "string"}, {"index": 1, "name": "mmr_instanceid", "type": "string"}]}}, {"name": "expression", "order": 1, "type": "expression", "schema": {"entries": [{"name": "process", "expression": "process", "type": "string"}, {"name": "stat", "expression": "1", "type": "number"}, {"name": "eip", "expression": "instanceId", "type": "string"}, {"name": "hostname", "expression": "hostname", "type": "string"}, {"name": "ssl_address", "expression": "sslAddress", "type": "string"}, {"name": "mmc", "expression": "mmc", "type": "string"}, {"name": "mmz", "expression": "zoneName", "type": "string"}, {"name": "region", "expression": "region", "type": "string"}]}}]}, {"name": "ZC_MMR_LOST", "filterRule": "dataType=='MMR-LOST'", "useStatus": 1, "order": 7, "fields": [{"fieldName": "hostname", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "mmc", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "instanceid", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "zone", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "conf_count", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "region", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "client_count", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}], "processores": [{"name": "split", "order": 0, "type": "split", "sourceField": "message", "schema": {"separator": ",", "entries": [{"index": 0, "name": "reason", "type": "string"}, {"index": 1, "name": "mmr_address", "type": "string"}, {"index": 2, "name": "conf_count", "type": "number"}, {"index": 3, "name": "client_count", "type": "number"}]}}, {"name": "expression", "order": 1, "type": "expression", "schema": {"entries": [{"name": "instanceid", "expression": "instanceId", "type": "string"}, {"name": "hostname", "expression": "hostname", "type": "string"}, {"name": "zone", "expression": "zoneName", "type": "string"}, {"name": "mmc", "expression": "mmc", "type": "string"}, {"name": "conf_count", "expression": "conf_count", "type": "number"}, {"name": "region", "expression": "region", "type": "string"}, {"name": "client_count", "expression": "client_count", "type": "number"}]}}]}, {"name": "ZC_CONF_NOT_EXIST", "filterRule": "dataType=='CONF-NOT-EXIST'", "useStatus": 1, "order": 8, "fields": [{"fieldName": "zone", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "conf_not_exist_count", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}], "processores": [{"name": "split", "order": 0, "type": "split", "sourceField": "message", "schema": {"separator": ",", "entries": [{"index": 0, "name": "conf_not_exist_count", "type": "number"}]}}, {"name": "expression", "order": 1, "type": "expression", "schema": {"entries": [{"name": "conf_not_exist_count", "expression": "conf_not_exist_count", "type": "number"}, {"name": "zone", "expression": "zoneName", "type": "string"}]}}]}, {"name": "ZC_ACQUIRE_LACK_VFY", "filterRule": "dataType=='ACQUIRE-LACK-VFY'", "useStatus": 1, "order": 9, "fields": [{"fieldName": "virtual_account", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "region", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}], "processores": [{"name": "split", "order": 0, "type": "split", "sourceField": "message", "schema": {"separator": ",", "entries": [{"index": 0, "name": "virtual data", "type": "number"}]}}, {"name": "expression", "order": 1, "type": "expression", "schema": {"entries": [{"name": "virtual_account", "expression": "1", "type": "number"}, {"name": "region", "expression": "region", "type": "string"}]}}]}, {"name": "ZC_diff_region_account", "filterRule": "dataType=='diff-region-account'", "useStatus": 1, "order": 10, "fields": [{"fieldName": "zone", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "acquire_count", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}], "processores": [{"name": "split", "order": 0, "type": "split", "sourceField": "message", "schema": {"separator": ",", "entries": [{"index": 0, "name": "account-id-token", "type": "string"}, {"index": 1, "name": "account-id-mmr", "type": "string"}]}}, {"name": "expression", "order": 1, "type": "expression", "schema": {"entries": [{"name": "acquire_count", "expression": "1", "type": "number"}, {"name": "zone", "expression": "zoneName", "type": "string"}]}}]}, {"name": "ZC_diff_region_account_create", "filterRule": "dataType=='diff-region-account-create'", "useStatus": 1, "order": 11, "fields": [{"fieldName": "zone", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "diff_account_create", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}], "processores": [{"name": "split", "order": 0, "type": "split", "sourceField": "message", "schema": {"separator": ",", "entries": [{"index": 0, "name": "account-id-token", "type": "string"}, {"index": 1, "name": "account-id-mmr", "type": "string"}]}}, {"name": "expression", "order": 1, "type": "expression", "schema": {"entries": [{"name": "diff_account_create", "expression": "1", "type": "number"}, {"name": "zone", "expression": "zoneName", "type": "string"}]}}]}, {"name": "ZC_DISABLE_HYBRID", "filterRule": "dataType=='DISABLE-HYBRID'", "useStatus": 1, "order": 12, "fields": [{"fieldName": "zone", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "accnt_id_cnt", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}], "processores": [{"name": "split", "order": 0, "type": "split", "sourceField": "message", "schema": {"separator": ",", "entries": [{"index": 0, "name": "accnt_id", "type": "string"}]}}, {"name": "expression", "order": 1, "type": "expression", "schema": {"entries": [{"name": "accnt_id_cnt", "expression": "1", "type": "number"}, {"name": "zone", "expression": "zoneName", "type": "string"}]}}]}, {"name": "ZC_TP_SSL_FAILURE", "filterRule": "dataType=='TP-SSL-FAILURE'", "useStatus": 1, "order": 13, "fields": [{"fieldName": "mmc", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "total", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "totalfail", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "tls1_3conn", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "tls1_2conn", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}], "processores": [{"name": "split", "order": 0, "type": "split", "sourceField": "message", "schema": {"separator": ",", "entries": [{"index": 0, "name": "total", "type": "number"}, {"index": 1, "name": "total_failure", "type": "number"}, {"index": 2, "name": "tls1_2_conn", "type": "number"}, {"index": 3, "name": "tls1_3_conn ", "type": "number"}]}}, {"name": "expression", "order": 1, "type": "expression", "schema": {"entries": [{"name": "totalfail", "expression": "total_failure", "type": "number"}, {"name": "total", "expression": "total", "type": "number"}, {"name": "tls1_3conn", "expression": "tls1_3_conn ", "type": "number"}, {"name": "tls1_2conn", "expression": "tls1_2_conn", "type": "number"}, {"name": "mmc", "expression": "mmc", "type": "string"}]}}]}, {"name": "ZC_PK_CONN_FAILURE", "filterRule": "dataType=='PK-CONN-FAILURE'", "useStatus": 1, "order": 14, "fields": [{"fieldName": "instance", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "zone", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "thresold", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "zone_pk_conn_failure", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}], "processores": [{"name": "split", "order": 0, "type": "split", "sourceField": "message", "schema": {"separator": ",", "entries": [{"index": 0, "name": "connName", "type": "string"}]}}, {"name": "expression", "order": 1, "type": "expression", "schema": {"entries": [{"name": "thresold", "expression": "1", "type": "number"}, {"name": "instance", "expression": "hostname+':'+instance", "type": "string"}, {"name": "zone_pk_conn_failure", "expression": "connName", "type": "string"}, {"name": "zone", "expression": "zoneName", "type": "string"}]}}]}, {"name": "ZC_WEB_LAZY", "filterRule": "dataType=='WEB-LAZY'", "useStatus": 1, "order": 15, "fields": [{"fieldName": "hostname", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "mmc", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "instanceid", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "instance", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "dns_tme", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "web_url", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "zone", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "zc_web_lazy", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "used_time", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "region", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "process_time", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "tracking_id", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}], "processores": [{"name": "split", "order": 0, "type": "split", "sourceField": "message", "schema": {"separator": ",", "entries": [{"index": 0, "name": "web_url", "type": "string"}, {"index": 1, "name": "report_cnt", "type": "number"}, {"index": 2, "name": "used_time", "type": "number"}, {"index": 3, "name": "thd_idx", "type": "number"}, {"index": 4, "name": "cluster_id", "type": "string"}, {"index": 5, "name": "web_ip", "type": "string"}, {"index": 6, "name": "result", "type": "number"}, {"index": 7, "name": "dns_time", "type": "number"}, {"index": 8, "name": "process_time", "type": "number"}, {"index": 9, "name": "tracking_id", "type": "string"}, {"index": 10, "name": "error_buf", "type": "string"}]}}, {"name": "expression", "order": 1, "type": "expression", "schema": {"entries": [{"name": "instanceid", "expression": "instanceId", "type": "string"}, {"name": "web_url", "expression": "web_url", "type": "string"}, {"name": "instance", "expression": "instance", "type": "string"}, {"name": "process_time", "expression": "process_time", "type": "number"}, {"name": "dns_tme", "expression": "dns_time", "type": "number"}, {"name": "zone", "expression": "zoneName", "type": "string"}, {"name": "hostname", "expression": "hostname", "type": "string"}, {"name": "mmc", "expression": "mmc", "type": "string"}, {"name": "zc_web_lazy", "expression": "1", "type": "number"}, {"name": "tracking_id", "expression": "tracking_id", "type": "string"}, {"name": "used_time", "expression": "used_time", "type": "number"}, {"name": "region", "expression": "region", "type": "string"}]}}]}, {"name": "ZC_zcp_zc_time", "filterRule": "dataType=='zcp-zc-time'", "useStatus": 1, "order": 16, "fields": [{"fieldName": "cluster", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "mmc", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "instanceid", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "zone", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "region", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "zproxy_zc_rtt", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}], "processores": [{"name": "split", "order": 0, "type": "split", "sourceField": "message", "schema": {"separator": ",", "entries": [{"index": 0, "name": "region", "type": "string"}, {"index": 1, "name": "name", "type": "string"}, {"index": 2, "name": "rtt", "type": "number"}]}}, {"name": "expression", "order": 1, "type": "expression", "schema": {"entries": [{"name": "instanceid", "expression": "instanceId", "type": "string"}, {"name": "zproxy_zc_rtt", "expression": "rtt", "type": "number"}, {"name": "zone", "expression": "zoneName", "type": "string"}, {"name": "mmc", "expression": "mmc", "type": "string"}, {"name": "region", "expression": "region", "type": "string"}, {"name": "cluster", "expression": "cluster", "type": "string"}]}}]}, {"name": "ZC_CSMS_RUN_FAILURE", "filterRule": "dataType=='CSMS-RUN-FAILURE'", "useStatus": 1, "order": 17, "fields": [{"fieldName": "mmc", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "hostname", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "instanceid", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "csms_conn_failure", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "zone", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "region", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}], "processores": [{"name": "split", "order": 0, "type": "split", "sourceField": "message", "schema": {"separator": ",", "entries": [{"index": 0, "name": "result", "type": "number"}]}}, {"name": "expression", "order": 1, "type": "expression", "schema": {"entries": [{"name": "instanceid", "expression": "instanceId", "type": "string"}, {"name": "zone", "expression": "zoneName", "type": "string"}, {"name": "hostname", "expression": "hostname", "type": "string"}, {"name": "mmc", "expression": "mmc", "type": "string"}, {"name": "region", "expression": "region", "type": "string"}, {"name": "csms_conn_failure", "expression": "result", "type": "number"}]}}]}, {"name": "ZC_GW_STATUS", "filterRule": "dataType=='GW-STATUS'", "useStatus": 1, "order": 18, "fields": [{"fieldName": "cluster", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "hostname", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "service_type", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "instanceid", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "active_conf_percent", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "zone", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "total_server_count", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "active_conf_count", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "total_conf_count", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "region", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "active_server_percent", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "active_server_count", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}], "processores": [{"name": "split", "order": 0, "type": "split", "sourceField": "message", "schema": {"separator": ",", "entries": [{"index": 0, "name": "service_type", "type": "number"}, {"index": 1, "name": "total_server_count", "type": "number"}, {"index": 2, "name": "active_server_count", "type": "number"}, {"index": 3, "name": "total_conf_count", "type": "number"}, {"index": 4, "name": "active_conf_count", "type": "number"}]}}, {"name": "expression", "order": 1, "type": "expression", "schema": {"entries": [{"name": "instanceid", "expression": "instanceId", "type": "string"}, {"name": "service_type", "expression": "service_type", "type": "number"}, {"name": "total_server_count", "expression": "total_server_count", "type": "number"}, {"name": "active_conf_percent", "expression": "active_server_count<1?100:active_conf_count*100/(active_server_count*30)", "type": "number"}, {"name": "hostname", "expression": "hostname", "type": "string"}, {"name": "zone", "expression": "zoneName", "type": "string"}, {"name": "active_conf_count", "expression": "active_conf_count", "type": "number"}, {"name": "active_server_count", "expression": "active_server_count", "type": "number"}, {"name": "active_server_percent", "expression": "total_server_count<1?1:active_server_count*100/total_server_count", "type": "number"}, {"name": "total_conf_count", "expression": "total_conf_count", "type": "number"}, {"name": "cluster", "expression": "cluster", "type": "string"}, {"name": "region", "expression": "region", "type": "string"}]}}]}, {"name": "ZC_GW_UNAVAILABLE", "filterRule": "dataType=='GW-UNAVAILABLE'", "useStatus": 1, "order": 19, "fields": [{"fieldName": "cluster", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "reason", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "hostname", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "service_type", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "instanceid", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "instance", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "zone", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "meeting_uuid", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "count", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "region", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "service_type_tag", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "uuid", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}], "processores": [{"name": "split", "order": 0, "type": "split", "sourceField": "message", "schema": {"separator": ",", "entries": [{"index": 0, "name": "reason", "type": "number"}, {"index": 1, "name": "service_type", "type": "number"}, {"index": 2, "name": "uuid", "type": "string"}]}}, {"name": "expression", "order": 1, "type": "expression", "schema": {"entries": [{"name": "instanceid", "expression": "instanceId", "type": "string"}, {"name": "service_type", "expression": "service_type", "type": "number"}, {"name": "uuid", "expression": "uuid", "type": "string"}, {"name": "meeting_uuid", "expression": "uuid", "type": "string"}, {"name": "instance", "expression": "instance", "type": "string"}, {"name": "zone", "expression": "zoneName", "type": "string"}, {"name": "hostname", "expression": "hostname", "type": "string"}, {"name": "service_type_tag", "expression": "service_type", "type": "string"}, {"name": "count", "expression": "1", "type": "number"}, {"name": "reason", "expression": "reason", "type": "number"}, {"name": "cluster", "expression": "cluster", "type": "string"}, {"name": "region", "expression": "region", "type": "string"}]}}]}, {"name": "ZC_RESP_CLI_ERR", "filterRule": "dataType=='RESP-CLI-ERR'", "useStatus": 1, "order": 20, "fields": [{"fieldName": "cluster", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "hostname", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "mmc", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "instanceid", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "zone", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "conf_id", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "cnt", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "error_code", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "meetingid", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "join_number", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "region", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "error_code_cnt", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}], "processores": [{"name": "split", "order": 0, "type": "split", "sourceField": "message", "schema": {"separator": ",", "entries": [{"index": 0, "name": "error_code", "type": "string"}, {"index": 1, "name": "conf_id", "type": "string"}]}}, {"name": "expression", "order": 1, "type": "expression", "schema": {"entries": [{"name": "instanceid", "expression": "instanceId", "type": "string"}, {"name": "error_code", "expression": "error_code", "type": "number"}, {"name": "join_number", "expression": "1", "type": "number"}, {"name": "cnt", "expression": "1", "type": "number"}, {"name": "hostname", "expression": "hostname", "type": "string"}, {"name": "zone", "expression": "zoneName", "type": "string"}, {"name": "error_code_cnt", "expression": "error_code", "type": "number"}, {"name": "mmc", "expression": "mmc", "type": "string"}, {"name": "conf_id", "expression": "conf_id", "type": "string"}, {"name": "meetingid", "expression": "meetingId", "type": "string"}, {"name": "region", "expression": "region", "type": "string"}, {"name": "cluster", "expression": "cluster", "type": "string"}]}}]}, {"name": "ZC_OCSP_CURL_MAX_RETRY_FAIL", "filterRule": "dataType=='OCSP_CURL_MAX_RETRY_FAIL'", "useStatus": 1, "order": 22, "fields": [{"fieldName": "cluster", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "instanceid", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "zone", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "fingerprint", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "region", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}], "processores": [{"name": "split", "order": 0, "type": "split", "sourceField": "message", "schema": {"separator": ",", "entries": [{"index": 0, "name": "fingerprint", "type": "string"}]}}, {"name": "expression", "order": 1, "type": "expression", "schema": {"entries": [{"name": "instanceid", "expression": "instanceId", "type": "string"}, {"name": "fingerprint", "expression": "fingerprint", "type": "string"}, {"name": "zone", "expression": "zoneName", "type": "string"}, {"name": "region", "expression": "region", "type": "string"}, {"name": "cluster", "expression": "cluster", "type": "string"}]}}]}, {"name": "ZC_REG_WCC_FAIL", "filterRule": "dataType=='REG-WCC-FAIL'", "useStatus": 1, "order": 23, "fields": [{"fieldName": "instanceid", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "instance", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "zone", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "response", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "zone_urn", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "region", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "register_fail_count", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}], "processores": [{"name": "split", "order": 0, "type": "split", "sourceField": "message", "schema": {"separator": ",", "entries": [{"index": 0, "name": "zone_urn", "type": "string"}, {"index": 1, "name": "response", "type": "string"}]}}, {"name": "expression", "order": 1, "type": "expression", "schema": {"entries": [{"name": "instanceid", "expression": "instanceId", "type": "string"}, {"name": "zone_urn", "expression": "zone_urn", "type": "string"}, {"name": "response", "expression": "response", "type": "string"}, {"name": "instance", "expression": "hostname+':'+instance", "type": "string"}, {"name": "zone", "expression": "zoneName", "type": "string"}, {"name": "register_fail_count", "expression": "1", "type": "number"}, {"name": "region", "expression": "region", "type": "string"}]}}]}, {"name": "ZC_ZC_FULL", "filterRule": "dataType=='ZC-FULL'", "useStatus": 1, "order": 24, "fields": [{"fieldName": "push_capacity", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "pull_capacity", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "push_count", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "hostname", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "instanceid", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "instance", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "zone", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "count", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "env", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "region", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "pull_count", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}], "processores": [{"name": "split", "order": 0, "type": "split", "sourceField": "message", "schema": {"separator": ",", "entries": [{"index": 0, "name": "pull_count", "type": "number"}, {"index": 1, "name": "push_count", "type": "number"}, {"index": 2, "name": "pull_capacity", "type": "number"}, {"index": 3, "name": "push_capacity", "type": "number"}]}}, {"name": "expression", "order": 1, "type": "expression", "schema": {"entries": [{"name": "instanceid", "expression": "instanceId", "type": "string"}, {"name": "pull_capacity", "expression": "pull_capacity", "type": "number"}, {"name": "instance", "expression": "instance", "type": "string"}, {"name": "push_count", "expression": "push_count", "type": "number"}, {"name": "zone", "expression": "zoneName", "type": "string"}, {"name": "hostname", "expression": "hostname", "type": "string"}, {"name": "pull_count", "expression": "pull_count", "type": "number"}, {"name": "env", "expression": "env", "type": "string"}, {"name": "count", "expression": "1", "type": "number"}, {"name": "region", "expression": "region", "type": "string"}, {"name": "push_capacity", "expression": "push_capacity", "type": "number"}]}}]}, {"name": "ZC_STREAM_DAY_MONITOR", "filterRule": "dataType=='STREAM DAY MONITOR'", "useStatus": 1, "order": 25, "fields": [{"fieldName": "stream_create_count", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "hostname", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "instanceid", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "instance", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "max_pull_count", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "zone", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "max_push_count", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "env", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "region", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "viewer_ping_req", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}], "processores": [{"name": "split", "order": 0, "type": "split", "sourceField": "message", "schema": {"separator": ",", "entries": [{"index": 0, "name": "viewer_ping_req", "type": "number"}, {"index": 1, "name": "stream_create_count", "type": "number"}, {"index": 2, "name": "max_pull_count", "type": "number"}, {"index": 3, "name": "max_push_count", "type": "number"}]}}, {"name": "expression", "order": 1, "type": "expression", "schema": {"entries": [{"name": "stream_create_count", "expression": "stream_create_count", "type": "number"}, {"name": "instanceid", "expression": "instanceId", "type": "string"}, {"name": "instance", "expression": "instance", "type": "string"}, {"name": "viewer_ping_req", "expression": "viewer_ping_req", "type": "number"}, {"name": "zone", "expression": "zoneName", "type": "string"}, {"name": "hostname", "expression": "hostname", "type": "string"}, {"name": "env", "expression": "env", "type": "string"}, {"name": "max_pull_count", "expression": "max_pull_count", "type": "number"}, {"name": "max_push_count", "expression": "max_push_count", "type": "number"}, {"name": "region", "expression": "region", "type": "string"}]}}]}, {"name": "ZC_COMMAND_DROP", "filterRule": "dataType=='COMMAND-DROP'", "useStatus": 1, "order": 27, "fields": [{"fieldName": "hostname", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "instanceid", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "zone", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "conf_id", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "msg_type", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "region", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}], "processores": [{"name": "split", "order": 0, "type": "split", "sourceField": "message", "schema": {"separator": ",", "entries": [{"index": 0, "name": "msg_type", "type": "number"}]}}, {"name": "expression", "order": 1, "type": "expression", "schema": {"entries": [{"name": "instanceid", "expression": "instanceId", "type": "string"}, {"name": "msg_type", "expression": "msg_type", "type": "number"}, {"name": "zone", "expression": "zoneName", "type": "string"}, {"name": "hostname", "expression": "hostname", "type": "string"}, {"name": "conf_id", "expression": "meetingId", "type": "string"}, {"name": "region", "expression": "region", "type": "string"}]}}]}, {"name": "ZC_ASYNCMQ_DELAY", "filterRule": "dataType=='ASYNCMQ-DELAY'", "useStatus": 1, "order": 28, "fields": [{"fieldName": "handle_time", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "hostname", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "generate_time", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "instanceid", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "zone", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "receive_time", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "task_id", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "region", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "produce_time", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "arrive_time", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}], "processores": [{"name": "split", "order": 0, "type": "split", "sourceField": "message", "schema": {"separator": ",", "entries": [{"index": 0, "name": "task_id", "type": "string"}, {"index": 1, "name": "produce_time", "type": "string"}, {"index": 2, "name": "generate_time", "type": "string"}, {"index": 3, "name": "arrive_time", "type": "string"}, {"index": 4, "name": "receive_time", "type": "string"}, {"index": 5, "name": "handle_time", "type": "string"}]}}, {"name": "expression", "order": 1, "type": "expression", "schema": {"entries": [{"name": "instanceid", "expression": "instanceId", "type": "string"}, {"name": "generate_time", "expression": "generate_time", "type": "string"}, {"name": "receive_time", "expression": "receive_time", "type": "string"}, {"name": "zone", "expression": "zoneName", "type": "string"}, {"name": "hostname", "expression": "hostname", "type": "string"}, {"name": "handle_time", "expression": "handle_time", "type": "string"}, {"name": "arrive_time", "expression": "arrive_time", "type": "string"}, {"name": "region", "expression": "region", "type": "string"}, {"name": "produce_time", "expression": "produce_time", "type": "string"}, {"name": "task_id", "expression": "task_id", "type": "string"}]}}]}, {"name": "ZC_CONF_ZC_FAILOVER_CNT", "filterRule": "dataType=='CONF-ZC-FAILOVER-CNT'", "useStatus": 1, "order": 29, "fields": [{"fieldName": "cluster", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "mmc", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "hostname", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "instanceid", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "zone", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "confcount", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "conf_cnt", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "region", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}], "processores": [{"name": "split", "order": 0, "type": "split", "sourceField": "message", "schema": {"separator": ",", "entries": [{"index": 0, "name": "confCount", "type": "number"}, {"index": 1, "name": "cluster", "type": "string"}]}}, {"name": "expression", "order": 1, "type": "expression", "schema": {"entries": [{"name": "instanceid", "expression": "instanceId", "type": "string"}, {"name": "hostname", "expression": "hostname", "type": "string"}, {"name": "zone", "expression": "zoneName", "type": "string"}, {"name": "confcount", "expression": "confCount", "type": "number"}, {"name": "mmc", "expression": "mmc", "type": "string"}, {"name": "region", "expression": "region", "type": "string"}, {"name": "conf_cnt", "expression": "confCount", "type": "number"}, {"name": "cluster", "expression": "cluster", "type": "string"}]}}]}, {"name": "ZC_IB_GR_ERR", "filterRule": "dataType=='IB-GR-ERR'", "useStatus": 1, "order": 30, "fields": [{"fieldName": "cluster", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "instanceid", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "instance", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "meeting_id", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "meetingid", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}], "processores": [{"name": "split", "order": 0, "type": "split", "sourceField": "message", "schema": {"separator": ",", "entries": [{"index": 0, "name": "group_size", "type": "number"}]}}, {"name": "expression", "order": 1, "type": "expression", "schema": {"entries": [{"name": "instanceid", "expression": "instanceId", "type": "string"}, {"name": "instance", "expression": "instance", "type": "string"}, {"name": "meeting_id", "expression": "meetingId", "type": "number"}, {"name": "meetingid", "expression": "meetingId", "type": "string"}, {"name": "cluster", "expression": "cluster", "type": "string"}]}}]}, {"name": "ZC_KMS_FATAL", "filterRule": "dataType=='KMS-FATAL'", "useStatus": 1, "order": 31, "fields": [{"fieldName": "hostname", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "instanceid", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "zone", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "conf_id", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "err_code", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "region", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "tracking_id", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}], "processores": [{"name": "split", "order": 0, "type": "split", "sourceField": "message", "schema": {"separator": ",", "entries": [{"index": 0, "name": "err_code", "type": "number"}, {"index": 1, "name": "tracking_id", "type": "string"}]}}, {"name": "expression", "order": 1, "type": "expression", "schema": {"entries": [{"name": "instanceid", "expression": "instanceId", "type": "string"}, {"name": "zone", "expression": "zoneName", "type": "string"}, {"name": "hostname", "expression": "hostname", "type": "string"}, {"name": "err_code", "expression": "err_code", "type": "number"}, {"name": "tracking_id", "expression": "tracking_id", "type": "string"}, {"name": "conf_id", "expression": "meetingId", "type": "string"}, {"name": "region", "expression": "region", "type": "string"}]}}]}, {"name": "ZC_ZC_STATISTICS", "filterRule": "dataType=='ZC-STATISTICS'", "useStatus": 1, "order": 32, "fields": [{"fieldName": "mmc", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "clients", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "instance", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "zone_statistics_throughput", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "total_mmr", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "zone_statistics_participant_count", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "failpingcount", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "zone_statistics_unsuspended_count", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "zone_statistics_capacity_total", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "webreqcount", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "load", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "zone_statistics_capacity_free", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "zone", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "zone_statistics_capacity_availiable", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "throughput", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "err_req_rate", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "total_capacity", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "zone_statistics_failpercent", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "zone_statistics_usedpercent", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "zone_statistics_leftpercent", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "available_capacity", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "ccusedtime", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "instanceid", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "zone_statistics_mmr_count", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "available_mmr", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "used_percent", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "webusedtime", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "region", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "zone_statistics_maxclient_count", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "pingcount", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "webfailcount", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}], "processores": [{"name": "split", "order": 0, "type": "split", "sourceField": "message", "schema": {"separator": ",", "entries": [{"index": 0, "name": "unitCapacity", "type": "number"}, {"index": 1, "name": "mmrCount", "type": "number"}, {"index": 2, "name": "unsuspendedMMRs", "type": "number"}, {"index": 3, "name": "Capacity", "type": "number"}, {"index": 4, "name": "unsuspendedCapacity", "type": "number"}, {"index": 5, "name": "participants", "type": "number"}, {"index": 6, "name": "freeCapacity", "type": "number"}, {"index": 7, "name": "usedPercent", "type": "number"}, {"index": 8, "name": "leftPercent", "type": "number"}, {"index": 9, "name": "failPingPercent", "type": "number"}, {"index": 10, "name": "throughput", "type": "number"}, {"index": 11, "name": "pingCount", "type": "number"}, {"index": 12, "name": "failPingCount", "type": "number"}, {"index": 13, "name": "webReqCount", "type": "number"}, {"index": 14, "name": "webFailCount", "type": "number"}, {"index": 15, "name": "ccReqCount", "type": "number"}, {"index": 16, "name": "ccFailCount", "type": "number"}, {"index": 17, "name": "toWebUsedTime", "type": "number"}, {"index": 18, "name": "toCCUsedTime", "type": "number"}, {"index": 19, "name": "Reserved", "type": "number"}, {"index": 20, "name": "load", "type": "number"}, {"index": 21, "name": "activeClients", "type": "number"}]}}, {"name": "expression", "order": 1, "type": "expression", "schema": {"entries": [{"name": "failpingcount", "expression": "failPingCount", "type": "number"}, {"name": "zone_statistics_failpercent", "expression": "failPingPercent", "type": "number"}, {"name": "total_mmr", "expression": "mmrCount", "type": "number"}, {"name": "instance", "expression": "hostname+':'+instance", "type": "string"}, {"name": "zone_statistics_usedpercent", "expression": "usedPercent", "type": "number"}, {"name": "clients", "expression": "participants", "type": "number"}, {"name": "load", "expression": "load", "type": "number"}, {"name": "available_mmr", "expression": "unsuspendedMMRs", "type": "number"}, {"name": "region", "expression": "region", "type": "string"}, {"name": "zone_statistics_throughput", "expression": "throughput", "type": "number"}, {"name": "zone_statistics_capacity_total", "expression": "Capacity", "type": "number"}, {"name": "zone_statistics_mmr_count", "expression": "mmrCount", "type": "number"}, {"name": "err_req_rate", "expression": "failPingCount * 1.0 / pingCount", "type": "number"}, {"name": "zone_statistics_capacity_free", "expression": "freeCapacity", "type": "number"}, {"name": "webfailcount", "expression": "webFailCount", "type": "number"}, {"name": "zone_statistics_capacity_availiable", "expression": "unsuspendedCapacity", "type": "number"}, {"name": "mmc", "expression": "mmc", "type": "string"}, {"name": "used_percent", "expression": "participants * 1.0 / Capacity", "type": "number"}, {"name": "webreqcount", "expression": "webReqCount", "type": "number"}, {"name": "throughput", "expression": "throughput", "type": "number"}, {"name": "zone_statistics_unsuspended_count", "expression": "unsuspendedMMRs", "type": "number"}, {"name": "instanceid", "expression": "instanceId", "type": "string"}, {"name": "ccusedtime", "expression": "toCCUsedTime", "type": "number"}, {"name": "zone_statistics_participant_count", "expression": "participants", "type": "number"}, {"name": "pingcount", "expression": "pingCount", "type": "number"}, {"name": "zone", "expression": "zoneName", "type": "string"}, {"name": "total_capacity", "expression": "Capacity", "type": "number"}, {"name": "zone_statistics_maxclient_count", "expression": "unitCapacity", "type": "number"}, {"name": "available_capacity", "expression": "freeCapacity", "type": "number"}, {"name": "webusedtime", "expression": "toWebUsedTime", "type": "number"}, {"name": "zone_statistics_leftpercent", "expression": "metileftPercent", "type": "number"}]}}]}, {"name": "ZC_CMR_UNAVAILABLE", "filterRule": "dataType=='CMR-UNAVAILABLE'", "useStatus": 1, "order": 33, "fields": [{"fieldName": "hostname", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "instanceid", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "zone", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "meeting_id", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}], "processores": [{"name": "split", "order": 0, "type": "split", "sourceField": "message", "schema": {"separator": ",", "entries": [{"index": 0, "name": "meetingID", "type": "string"}]}}, {"name": "expression", "order": 1, "type": "expression", "schema": {"entries": [{"name": "instanceid", "expression": "instanceId", "type": "string"}, {"name": "hostname", "expression": "hostname", "type": "string"}, {"name": "zone", "expression": "zoneName", "type": "string"}, {"name": "meeting_id", "expression": "meetingID", "type": "string"}]}}]}, {"name": "ZC_LOAD_KEY", "filterRule": "dataType=='LOAD-KEY'", "useStatus": 1, "order": 34, "fields": [{"fieldName": "cluster", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "aliasname", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "instanceid", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "size", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "stage", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "zone", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "version", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}], "processores": [{"name": "split", "order": 0, "type": "split", "sourceField": "message", "schema": {"separator": ",", "entries": [{"index": 0, "name": "alias_name", "type": "string"}, {"index": 1, "name": "version", "type": "string"}, {"index": 2, "name": "stage", "type": "string"}, {"index": 3, "name": "size", "type": "number"}]}}, {"name": "expression", "order": 1, "type": "expression", "schema": {"entries": [{"name": "instanceid", "expression": "instanceId", "type": "string"}, {"name": "stage", "expression": "stage", "type": "string"}, {"name": "size", "expression": "size", "type": "number"}, {"name": "zone", "expression": "zoneName", "type": "string"}, {"name": "aliasname", "expression": "alias_name", "type": "string"}, {"name": "version", "expression": "version", "type": "string"}, {"name": "cluster", "expression": "cluster", "type": "string"}]}}]}, {"name": "ZC_LONG_GW_CONN_ERR", "filterRule": "dataType=='LONG-GW-CONN-ERR'", "useStatus": 1, "order": 35, "fields": [{"fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "instanceid", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "zone", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "meetingid", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "dstaddr", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}], "processores": [{"name": "split", "order": 0, "type": "split", "sourceField": "message", "schema": {"separator": ",", "entries": [{"index": 0, "name": "failure", "type": "string"}, {"index": 1, "name": "reason", "type": "number"}, {"index": 2, "name": "dest", "type": "string"}]}}, {"name": "expression", "order": 1, "type": "expression", "schema": {"entries": [{"name": "instanceid", "expression": "instanceId", "type": "string"}, {"name": "dstaddr", "expression": "dest", "type": "string"}, {"name": "<PERSON><PERSON><PERSON>", "expression": "reason", "type": "number"}, {"name": "zone", "expression": "zoneName", "type": "string"}, {"name": "meetingid", "expression": "meetingId", "type": "string"}]}}]}, {"name": "ZC_CSMS_UPDATE_KEY", "filterRule": "dataType=='CSMS-UPDATE-KEY'", "useStatus": 1, "order": 36, "fields": [{"fieldName": "cluster", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "aliasname", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "instanceid", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "zone", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "version", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "status", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}], "processores": [{"name": "split", "order": 0, "type": "split", "sourceField": "message", "schema": {"separator": ",", "entries": [{"index": 0, "name": "<PERSON><PERSON><PERSON>", "type": "string"}, {"index": 1, "name": "version", "type": "string"}, {"index": 2, "name": "status", "type": "string"}]}}, {"name": "expression", "order": 1, "type": "expression", "schema": {"entries": [{"name": "instanceid", "expression": "instanceId", "type": "string"}, {"name": "status", "expression": "status", "type": "string"}, {"name": "zone", "expression": "zoneName", "type": "string"}, {"name": "aliasname", "expression": "<PERSON><PERSON><PERSON>", "type": "string"}, {"name": "version", "expression": "version", "type": "string"}, {"name": "cluster", "expression": "cluster", "type": "string"}]}}]}, {"name": "ZC_SVR_REGISTER_FAIL", "filterRule": "dataType=='SVR-REGISTER-FAIL'", "useStatus": 1, "order": 37, "fields": [{"fieldName": "instanceid", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "zone", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "addr", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}], "processores": [{"name": "split", "order": 0, "type": "split", "sourceField": "message", "schema": {"separator": ",", "entries": [{"index": 0, "name": "failure", "type": "string"}, {"index": 1, "name": "addr", "type": "string"}]}}, {"name": "expression", "order": 1, "type": "expression", "schema": {"entries": [{"name": "instanceid", "expression": "instanceId", "type": "string"}, {"name": "zone", "expression": "zoneName", "type": "string"}, {"name": "addr", "expression": "addr", "type": "string"}]}}]}, {"name": "ZC__svr_token_failure", "filterRule": "dataType==' svr-token-failure'", "useStatus": 1, "order": 38, "fields": [{"fieldName": "addr", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}], "processores": [{"name": "split", "order": 0, "type": "split", "sourceField": "message", "schema": {"separator": ",", "entries": [{"index": 0, "name": "addr", "type": "string"}]}}, {"name": "expression", "order": 1, "type": "expression", "schema": {"entries": [{"name": "addr", "expression": "addr", "type": "string"}]}}]}, {"name": "ZC_LSSZC_STATISTICS", "filterRule": "dataType=='LSSZC-STATISTICS'", "useStatus": 1, "order": 39, "fields": [{"fieldName": "pull_capacity", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "cluster", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "push_usage_percent", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "active_lss_percent", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "push_capacity", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "player_count", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "push_count", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "hostname", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "instanceid", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "active_lss_count", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "zone", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "pull_usage_percent", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "total_lss_count", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "region", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "pull_count", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}], "processores": [{"name": "split", "order": 0, "type": "split", "sourceField": "message", "schema": {"separator": ",", "entries": [{"index": 0, "name": "total_lss_count", "type": "number"}, {"index": 1, "name": "active_lss_count", "type": "number"}, {"index": 2, "name": "pull_count", "type": "number"}, {"index": 3, "name": "push_count", "type": "number"}, {"index": 4, "name": "player_count", "type": "number"}, {"index": 5, "name": "pull_capacity", "type": "number"}, {"index": 6, "name": "push_capacity", "type": "number"}]}}, {"name": "expression", "order": 1, "type": "expression", "schema": {"entries": [{"name": "instanceid", "expression": "instanceId", "type": "string"}, {"name": "pull_capacity", "expression": "pull_capacity", "type": "number"}, {"name": "total_lss_count", "expression": "total_lss_count", "type": "number"}, {"name": "zone", "expression": "zoneName", "type": "string"}, {"name": "hostname", "expression": "hostname", "type": "string"}, {"name": "active_lss_percent", "expression": "total_lss_count<1?1:active_lss_count*100/total_lss_count", "type": "number"}, {"name": "region", "expression": "region", "type": "string"}, {"name": "cluster", "expression": "cluster", "type": "string"}, {"name": "pull_usage_percent", "expression": "active_lss_count<1?100:pull_count*100/(active_lss_count*2000)", "type": "number"}, {"name": "player_count", "expression": "player_count", "type": "number"}, {"name": "push_count", "expression": "push_count", "type": "number"}, {"name": "active_lss_count", "expression": "active_lss_count", "type": "number"}, {"name": "push_usage_percent", "expression": "active_lss_count<1?100:push_count*100/(active_lss_count*20)", "type": "number"}, {"name": "pull_count", "expression": "pull_count", "type": "number"}, {"name": "push_capacity", "expression": "push_capacity", "type": "number"}]}}]}, {"name": "ZC_STREAM_ERR", "filterRule": "dataType=='STREAM-ERR'", "useStatus": 1, "order": 40, "fields": [{"fieldName": "cluster", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "hostname", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "instanceid", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "zone", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "stream_id", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "count", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "err_code", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "region", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}], "processores": [{"name": "split", "order": 0, "type": "split", "sourceField": "message", "schema": {"separator": ",", "entries": [{"index": 0, "name": "err_code", "type": "number"}, {"index": 1, "name": "stream_id", "type": "string"}]}}, {"name": "expression", "order": 1, "type": "expression", "schema": {"entries": [{"name": "instanceid", "expression": "instanceId", "type": "string"}, {"name": "stream_id", "expression": "stream_id", "type": "string"}, {"name": "zone", "expression": "zoneName", "type": "string"}, {"name": "hostname", "expression": "hostname", "type": "string"}, {"name": "count", "expression": "1", "type": "number"}, {"name": "err_code", "expression": "err_code", "type": "number"}, {"name": "region", "expression": "region", "type": "string"}, {"name": "cluster", "expression": "cluster", "type": "string"}]}}]}, {"name": "ZC_VIEWER_ERR", "filterRule": "dataType=='VIEWER-ERR'", "useStatus": 1, "order": 41, "fields": [{"fieldName": "cluster", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "hostname", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "instance", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "zone", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "stream_id", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "count", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "error_code", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "region", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}], "processores": [{"name": "split", "order": 0, "type": "split", "sourceField": "message", "schema": {"separator": ",", "entries": [{"index": 0, "name": "error_code", "type": "number"}, {"index": 1, "name": "stream_id", "type": "string"}]}}, {"name": "expression", "order": 1, "type": "expression", "schema": {"entries": [{"name": "error_code", "expression": "error_code", "type": "number"}, {"name": "stream_id", "expression": "stream_id", "type": "string"}, {"name": "instance", "expression": "instance", "type": "string"}, {"name": "zone", "expression": "zoneName", "type": "string"}, {"name": "hostname", "expression": "hostname", "type": "string"}, {"name": "count", "expression": "1", "type": "number"}, {"name": "region", "expression": "region", "type": "string"}, {"name": "cluster", "expression": "cluster", "type": "string"}]}}]}, {"name": "ZC_SPLIT_MEETING", "filterRule": "dataType=='SPLIT-MEETING'", "useStatus": 1, "order": 42, "fields": [{"fieldName": "mmc", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "instanceid", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "zone", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "meetingid", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "merge_times", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}], "processores": [{"name": "split", "order": 0, "type": "split", "sourceField": "message", "schema": {"separator": ",", "entries": [{"index": 0, "name": "merge_times", "type": "number"}]}}, {"name": "expression", "order": 1, "type": "expression", "schema": {"entries": [{"name": "instanceid", "expression": "instanceId", "type": "string"}, {"name": "merge_times", "expression": "merge_times", "type": "number"}, {"name": "zone", "expression": "zoneName", "type": "string"}, {"name": "mmc", "expression": "mmc", "type": "string"}, {"name": "meetingid", "expression": "meetingId", "type": "string"}]}}]}, {"name": "ZC_CONF_DIST", "filterRule": "dataType=='CONF-DIST'", "useStatus": 1, "order": 43, "fields": [{"fieldName": "cluster", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "instanceid", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "zone", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "meeting_type", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "meetingid", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "attendee_cnts", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "mmr_cnts", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}], "processores": [{"name": "split", "order": 0, "type": "split", "sourceField": "message", "schema": {"separator": ",", "entries": [{"index": 0, "name": "mmr_count", "type": "number"}, {"index": 1, "name": "attendee_count", "type": "number"}, {"index": 2, "name": "meeting_type", "type": "number"}]}}, {"name": "expression", "order": 1, "type": "expression", "schema": {"entries": [{"name": "instanceid", "expression": "instanceId", "type": "string"}, {"name": "meeting_type", "expression": "meeting_type", "type": "number"}, {"name": "mmr_cnts", "expression": "mmr_count", "type": "number"}, {"name": "zone", "expression": "zoneName", "type": "string"}, {"name": "meetingid", "expression": "meetingId", "type": "string"}, {"name": "attendee_cnts", "expression": "attendee_count", "type": "number"}, {"name": "cluster", "expression": "cluster", "type": "string"}]}}]}, {"name": "ZC_MQ_AGENT_CONN_FAIL", "filterRule": "dataType=='MQ-AGENT-CONN-FAIL'", "useStatus": 1, "order": 44, "fields": [{"fieldName": "reason", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "hostname", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "instanceid", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "zone", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "region", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}], "processores": [{"name": "split", "order": 0, "type": "split", "sourceField": "message", "schema": {"separator": ",", "entries": [{"index": 0, "name": "reason", "type": "number"}]}}, {"name": "expression", "order": 1, "type": "expression", "schema": {"entries": [{"name": "instanceid", "expression": "instanceId", "type": "string"}, {"name": "zone", "expression": "zoneName", "type": "string"}, {"name": "hostname", "expression": "hostname", "type": "string"}, {"name": "reason", "expression": "reason", "type": "number"}, {"name": "region", "expression": "region", "type": "string"}]}}]}, {"name": "ZC_MQ_AGENT_INIT_FAIL", "filterRule": "dataType=='MQ-AGENT-INIT-FAIL'", "useStatus": 1, "order": 45, "fields": [{"fieldName": "hostname", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "instanceid", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "instance", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "zone", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "mmz", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "region", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}], "processores": [{"name": "split", "order": 0, "type": "split", "sourceField": "message", "schema": {"separator": ",", "entries": [{"index": 0, "name": "MMZ", "type": "string"}, {"index": 1, "name": "instance", "type": "string"}]}}, {"name": "expression", "order": 1, "type": "expression", "schema": {"entries": [{"name": "instanceid", "expression": "instanceId", "type": "string"}, {"name": "mmz", "expression": "MMZ", "type": "number"}, {"name": "zone", "expression": "zoneName", "type": "string"}, {"name": "hostname", "expression": "hostname", "type": "string"}, {"name": "instance", "expression": "instance", "type": "number"}, {"name": "region", "expression": "region", "type": "string"}]}}]}, {"name": "ZC_CURL_FAIL", "filterRule": "dataType=='CURL-FAIL'", "useStatus": 1, "order": 46, "fields": [{"fieldName": "result", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "hostname", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "instanceid", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "zone", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "region", "fieldType": "string", "hasPii": 0, "fieldSchema": ""}, {"fieldName": "url", "fieldType": "number", "hasPii": 0, "fieldSchema": ""}], "processores": [{"name": "split", "order": 0, "type": "split", "sourceField": "message", "schema": {"separator": ",", "entries": [{"index": 0, "name": "result", "type": "number"}, {"index": 1, "name": "url", "type": "string"}]}}, {"name": "expression", "order": 1, "type": "expression", "schema": {"entries": [{"name": "instanceid", "expression": "instanceId", "type": "string"}, {"name": "url", "expression": "url", "type": "number"}, {"name": "result", "expression": "result", "type": "number"}, {"name": "zone", "expression": "zoneName", "type": "string"}, {"name": "hostname", "expression": "hostname", "type": "string"}, {"name": "region", "expression": "region", "type": "string"}]}}]}], "metrics": [{"metricsName": "ZC_WEB_LOST", "oldMetricsName": "ZC-WEB-LOST", "tagNames": ["mmc", "region"], "tagDetails": [{"tagName": "mmc", "oldTagName": "MMC"}, {"tagName": "region", "oldTagName": "Region"}], "type": 1, "enabled": true, "metricsFieldList": [{"fieldName": "connect_web_lost_count.sum", "oldFieldName": "connect.web.lost.count", "fieldType": 1}], "metricsAggregation": {"aggPeriod": 60, "waitTime": 0, "filterCondition": "", "aggregationResultFilter": "", "metricsAggregationRuleCompose": {"common": [{"aggField": "connect_web_lost_count", "fieldType": "number", "aggTypes": [{"value": "sum", "condition": "", "fieldName": "connect_web_lost_count.sum"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "connect_web_lost_count"}], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "ZC_WEB_LOST"}, {"metricsName": "ZC_ZONE_LOAD", "oldMetricsName": "ZC_ZONE-LOAD", "tagNames": ["instance", "mmc", "region", "instanceid", "zone"], "tagDetails": [{"tagName": "instance", "oldTagName": "Instance"}, {"tagName": "mmc", "oldTagName": "MMC"}, {"tagName": "region", "oldTagName": "Region"}, {"tagName": "instanceid", "oldTagName": "InstanceId"}, {"tagName": "zone", "oldTagName": "Zone"}], "type": 2, "enabled": true, "metricsFieldList": [{"fieldName": "zone_load_client_count", "oldFieldName": "zone.load.client.count", "fieldType": 1}, {"fieldName": "zone_load_meeting_count", "oldFieldName": "zone.load.meeting.count", "fieldType": 1}, {"fieldName": "zone_load_non_p2p_client_count", "oldFieldName": "zone.load.non.p2p.client.count", "fieldType": 1}, {"fieldName": "zone_load_p2p_client_count", "oldFieldName": "zone.load.p2p.client.count", "fieldType": 1}, {"fieldName": "zone_load_ping_count", "oldFieldName": "zone.load.ping.count", "fieldType": 1}, {"fieldName": "zone_load_telephony_count", "oldFieldName": "zone.load.telephony.count", "fieldType": 1}, {"fieldName": "zone_load_viewonly_count", "oldFieldName": "zone.load.viewonly.count", "fieldType": 1}, {"fieldName": "zone_load_panelist", "oldFieldName": "zone.load.panelist", "fieldType": 1}, {"fieldName": "zone_load_suspend_mmr_count", "oldFieldName": "zone.load.suspend.mmr.count", "fieldType": 1}, {"fieldName": "zone_load_total_mmr_count", "oldFieldName": "zone.load.total.mmr.Count", "fieldType": 1}, {"fieldName": "zone_load_real_meeting_count", "oldFieldName": "zone.load.real.meeting.count", "fieldType": 1}], "metricsAggregation": {"metricsAggregationRuleCompose": {"common": [], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "ZC_ZONE_LOAD"}, {"metricsName": "ZC_THREAD_BLOCK", "oldMetricsName": "ZC_THREAD-BLOCK", "tagNames": ["instance", "mmc", "region", "instanceid", "zonename"], "tagDetails": [{"tagName": "instance", "oldTagName": "Instance"}, {"tagName": "mmc", "oldTagName": "MMC"}, {"tagName": "region", "oldTagName": "Region"}, {"tagName": "instanceid", "oldTagName": "InstanceId"}, {"tagName": "zonename", "oldTagName": "ZoneName"}], "type": 1, "enabled": true, "metricsFieldList": [{"fieldName": "block_thread_count.count", "oldFieldName": "block.thread.count", "fieldType": 1}, {"fieldName": "block_thread_timing.max", "oldFieldName": "block.thread.timing", "fieldType": 1}], "metricsAggregation": {"aggPeriod": 60, "waitTime": 0, "filterCondition": "", "aggregationResultFilter": "", "metricsAggregationRuleCompose": {"common": [{"aggField": "block_thread_count", "fieldType": "number", "aggTypes": [{"value": "count", "condition": "", "fieldName": "block_thread_count.count"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "block_thread_count"}, {"aggField": "block_thread_timing", "fieldType": "number", "aggTypes": [{"value": "max", "condition": "", "fieldName": "block_thread_timing.max"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "block_thread_timing"}], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "ZC_THREAD_BLOCK"}, {"metricsName": "ZC_CC_LOST", "oldMetricsName": "ZC-CC-LOST", "tagNames": ["instance", "mmc", "region", "instanceid", "hostname", "zone"], "tagDetails": [{"tagName": "instance", "oldTagName": "Instance"}, {"tagName": "mmc", "oldTagName": "MMC"}, {"tagName": "region", "oldTagName": "Region"}, {"tagName": "instanceid", "oldTagName": "InstanceId"}, {"tagName": "hostname", "oldTagName": "Hostname"}, {"tagName": "zone", "oldTagName": "Zone"}], "type": 1, "enabled": true, "metricsFieldList": [{"fieldName": "connect_cc_fail_count.count", "oldFieldName": "connect.cc.fail.count", "fieldType": 1}], "metricsAggregation": {"aggPeriod": 60, "waitTime": 0, "filterCondition": "", "aggregationResultFilter": "", "metricsAggregationRuleCompose": {"common": [{"aggField": "connect_cc_fail_count", "fieldType": "number", "aggTypes": [{"value": "count", "condition": "", "fieldName": "connect_cc_fail_count.count"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "connect_cc_fail_count"}], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "ZC_CC_LOST"}, {"metricsName": "ZC_CC_LOST_RAW", "oldMetricsName": "ZC-CC-LOST-RAW", "tagNames": ["instance", "mmc", "region", "zone", "instanceid", "cluster"], "tagDetails": [{"tagName": "instance", "oldTagName": "Instance"}, {"tagName": "mmc", "oldTagName": "MMC"}, {"tagName": "region", "oldTagName": "Region"}, {"tagName": "zone", "oldTagName": "Zone"}, {"tagName": "instanceid", "oldTagName": "InstanceId"}, {"tagName": "cluster", "oldTagName": "Cluster"}], "type": 2, "enabled": true, "metricsFieldList": [{"fieldName": "cc_adddress", "oldFieldName": "cc.adddress", "fieldType": 2}, {"fieldName": "lost_reason", "oldFieldName": "lost.reason", "fieldType": 1}], "metricsAggregation": {"metricsAggregationRuleCompose": {"common": [], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "ZC_CC_LOST"}, {"metricsName": "ZC_ZC_STAT", "oldMetricsName": "ZC_ZC-STAT", "tagNames": ["zone", "region", "mmc"], "tagDetails": [{"tagName": "zone", "oldTagName": "Zone"}, {"tagName": "region", "oldTagName": "Region"}, {"tagName": "mmc", "oldTagName": "MMC"}], "type": 1, "enabled": true, "metricsFieldList": [{"fieldName": "stat0.max", "fieldType": 1}, {"fieldName": "stat1.max", "fieldType": 1}, {"fieldName": "stat2.max", "fieldType": 1}, {"fieldName": "stat3.max", "fieldType": 1}, {"fieldName": "stat4.max", "fieldType": 1}, {"fieldName": "minstat0.min", "oldFieldName": "MinStat0", "fieldType": 1}, {"fieldName": "minstat1.min", "oldFieldName": "MinStat1", "fieldType": 1}, {"fieldName": "minstat2.min", "oldFieldName": "MinStat2", "fieldType": 1}, {"fieldName": "minstat3.min", "oldFieldName": "MinStat3", "fieldType": 1}, {"fieldName": "minstat4.min", "oldFieldName": "MinStat4", "fieldType": 1}], "metricsAggregation": {"aggPeriod": 60, "waitTime": 0, "filterCondition": "", "aggregationResultFilter": "", "metricsAggregationRuleCompose": {"common": [{"aggField": "stat0", "fieldType": "number", "aggTypes": [{"value": "max", "condition": "", "fieldName": "stat0.max"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "stat0"}, {"aggField": "stat1", "fieldType": "number", "aggTypes": [{"value": "max", "condition": "", "fieldName": "stat1.max"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "stat1"}, {"aggField": "stat2", "fieldType": "number", "aggTypes": [{"value": "max", "condition": "", "fieldName": "stat2.max"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "stat2"}, {"aggField": "stat3", "fieldType": "number", "aggTypes": [{"value": "max", "condition": "", "fieldName": "stat3.max"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "stat3"}, {"aggField": "stat4", "fieldType": "number", "aggTypes": [{"value": "max", "condition": "", "fieldName": "stat4.max"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "stat4"}, {"aggField": "minstat0", "fieldType": "number", "aggTypes": [{"value": "min", "condition": "", "fieldName": "minstat0.min"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "minstat0"}, {"aggField": "minstat1", "fieldType": "number", "aggTypes": [{"value": "min", "condition": "", "fieldName": "minstat1.min"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "minstat1"}, {"aggField": "minstat2", "fieldType": "number", "aggTypes": [{"value": "min", "condition": "", "fieldName": "minstat2.min"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "minstat2"}, {"aggField": "minstat3", "fieldType": "number", "aggTypes": [{"value": "min", "condition": "", "fieldName": "minstat3.min"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "minstat3"}, {"aggField": "minstat4", "fieldType": "number", "aggTypes": [{"value": "min", "condition": "", "fieldName": "minstat4.min"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "minstat4"}], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "ZC_ZC_STAT"}, {"metricsName": "ZC_SUSPEND_MMR", "oldMetricsName": "ZC_SUSPEND-MMR", "tagNames": ["ssl_address", "region", "hostname", "eip", "mmz", "mmc", "process", "mmr_address", "mmr_instanceid"], "tagDetails": [{"tagName": "ssl_address"}, {"tagName": "region", "oldTagName": "Region"}, {"tagName": "hostname", "oldTagName": "Hostname"}, {"tagName": "eip", "oldTagName": "EIP"}, {"tagName": "mmz", "oldTagName": "MMZ"}, {"tagName": "mmc", "oldTagName": "MMC"}, {"tagName": "process"}, {"tagName": "mmr_address"}, {"tagName": "mmr_instanceid"}], "type": 2, "enabled": true, "metricsFieldList": [{"fieldName": "stat", "fieldType": 1}], "metricsAggregation": {"metricsAggregationRuleCompose": {"common": [], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "ZC_SUSPEND_MMR"}, {"metricsName": "MMR_LOST", "oldMetricsName": "MMR-LOST", "tagNames": ["reason", "instanceid", "region", "zone", "mmc", "hostname", "mmr_address"], "tagDetails": [{"tagName": "reason"}, {"tagName": "instanceid", "oldTagName": "InstanceId"}, {"tagName": "region", "oldTagName": "Region"}, {"tagName": "zone", "oldTagName": "Zone"}, {"tagName": "mmc", "oldTagName": "MMC"}, {"tagName": "hostname", "oldTagName": "Hostname"}, {"tagName": "mmr_address"}], "type": 2, "enabled": true, "metricsFieldList": [{"fieldName": "conf_count", "fieldType": 1}, {"fieldName": "client_count", "fieldType": 1}], "metricsAggregation": {"metricsAggregationRuleCompose": {"common": [], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "ZC_MMR_LOST"}, {"metricsName": "ZC_MMR_LOST", "oldMetricsName": "ZC_MMR-LOST", "tagNames": ["zone", "region", "hostname"], "tagDetails": [{"tagName": "zone", "oldTagName": "Zone"}, {"tagName": "region", "oldTagName": "Region"}, {"tagName": "hostname", "oldTagName": "Hostname"}], "type": 1, "enabled": true, "metricsFieldList": [{"fieldName": "conf_count.sum", "fieldType": 1}, {"fieldName": "client_count.sum", "fieldType": 1}], "metricsAggregation": {"aggPeriod": 60, "waitTime": 0, "filterCondition": "", "aggregationResultFilter": "", "metricsAggregationRuleCompose": {"common": [{"aggField": "conf_count", "fieldType": "number", "aggTypes": [{"value": "sum", "condition": "", "fieldName": "conf_count.sum"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "conf_count"}, {"aggField": "client_count", "fieldType": "number", "aggTypes": [{"value": "sum", "condition": "", "fieldName": "client_count.sum"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "client_count"}], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "ZC_MMR_LOST"}, {"metricsName": "ZC_ZONE_LOAD_DEMO", "tagNames": ["instanceid", "mmc", "region"], "tagDetails": [{"tagName": "instanceid", "oldTagName": "InstanceId"}, {"tagName": "mmc", "oldTagName": "MMC"}, {"tagName": "region", "oldTagName": "Region"}], "type": 2, "enabled": true, "metricsFieldList": [{"fieldName": "test_meeting_count", "oldFieldName": "test.meeting.count", "fieldType": 1}, {"fieldName": "test_attendee_count", "oldFieldName": "test.attendee.count", "fieldType": 1}], "metricsAggregation": {"metricsAggregationRuleCompose": {"common": [], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "ZC_ZONE_LOAD"}, {"metricsName": "CONF_NOT_EXIST_EACH_SEC", "oldMetricsName": "CONF-NOT-EXIST-EACH-SEC", "tagNames": ["zone"], "tagDetails": [{"tagName": "zone", "oldTagName": "Zone"}], "type": 2, "enabled": true, "metricsFieldList": [{"fieldName": "conf_not_exist_count", "oldFieldName": "conf.not.exist.count", "fieldType": 1}], "metricsAggregation": {"metricsAggregationRuleCompose": {"common": [], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "ZC_CONF_NOT_EXIST"}, {"metricsName": "ZC_ZONE_LOAD_TEST", "oldMetricsName": "ZC_ZONE-LOAD_TEST", "tagNames": ["instance", "mmc", "region", "service_type", "zone", "instanceid", "cluster", "hostname"], "tagDetails": [{"tagName": "instance", "oldTagName": "Instance"}, {"tagName": "mmc", "oldTagName": "MMC"}, {"tagName": "region", "oldTagName": "Region"}, {"tagName": "service_type", "oldTagName": "Service_type"}, {"tagName": "zone", "oldTagName": "Zone"}, {"tagName": "instanceid", "oldTagName": "InstanceId"}, {"tagName": "cluster", "oldTagName": "Cluster"}, {"tagName": "hostname", "oldTagName": "Hostname"}], "type": 2, "enabled": true, "metricsFieldList": [{"fieldName": "demo_meeting_count", "oldFieldName": "demo.meeting.count", "fieldType": 1}, {"fieldName": "demo_attendee_count", "oldFieldName": "demo.attendee.count", "fieldType": 1}], "metricsAggregation": {"metricsAggregationRuleCompose": {"common": [], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "ZC_ZONE_LOAD"}, {"metricsName": "ZC_ACQUIRE_LACK_VFY", "oldMetricsName": "ZC_ACQUIRE-LACK-VFY", "tagNames": ["region"], "tagDetails": [{"tagName": "region", "oldTagName": "Region"}], "type": 2, "enabled": true, "metricsFieldList": [{"fieldName": "virtual_account", "oldFieldName": "virtual account", "fieldType": 1}], "metricsAggregation": {"metricsAggregationRuleCompose": {"common": [], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "ZC_ACQUIRE_LACK_VFY"}, {"metricsName": "ZC_diff_region_account", "oldMetricsName": "ZC_diff-region-account", "tagNames": ["zone"], "tagDetails": [{"tagName": "zone", "oldTagName": "Zone"}], "type": 2, "enabled": true, "metricsFieldList": [{"fieldName": "acquire_count", "fieldType": 1}], "metricsAggregation": {"metricsAggregationRuleCompose": {"common": [], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "ZC_diff_region_account"}, {"metricsName": "ZC_diff_region_account_create", "oldMetricsName": "ZC_diff-region-account-create", "tagNames": ["zone"], "tagDetails": [{"tagName": "zone", "oldTagName": "Zone"}], "type": 2, "enabled": true, "metricsFieldList": [{"fieldName": "diff_account_create", "oldFieldName": "diff-account-create", "fieldType": 1}], "metricsAggregation": {"metricsAggregationRuleCompose": {"common": [], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "ZC_diff_region_account_create"}, {"metricsName": "ZC_DISABLE_HYBRID", "oldMetricsName": "ZC-DISABLE-HYBRID", "tagNames": ["zone"], "tagDetails": [{"tagName": "zone", "oldTagName": "Zone"}], "type": 2, "enabled": true, "metricsFieldList": [{"fieldName": "accnt_id_cnt", "fieldType": 1}], "metricsAggregation": {"metricsAggregationRuleCompose": {"common": [], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "ZC_DISABLE_HYBRID"}, {"metricsName": "ZC_TP_SSL_FAILURE", "oldMetricsName": "ZC-TP-SSL-FAILURE", "tagNames": ["mmc"], "tagDetails": [{"tagName": "mmc", "oldTagName": "MMC"}], "type": 1, "enabled": true, "metricsFieldList": [{"fieldName": "total.sum", "fieldType": 1}, {"fieldName": "totalfail.sum", "oldFieldName": "totalFail", "fieldType": 1}, {"fieldName": "tls1_2conn.sum", "fieldType": 1}, {"fieldName": "tls1_3conn.sum", "fieldType": 1}], "metricsAggregation": {"aggPeriod": 60, "waitTime": 0, "filterCondition": "", "aggregationResultFilter": "", "metricsAggregationRuleCompose": {"common": [{"aggField": "total", "fieldType": "number", "aggTypes": [{"value": "sum", "condition": "", "fieldName": "total.sum"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "total"}, {"aggField": "totalfail", "fieldType": "number", "aggTypes": [{"value": "sum", "condition": "", "fieldName": "totalfail.sum"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "totalfail"}, {"aggField": "tls1_2conn", "fieldType": "number", "aggTypes": [{"value": "sum", "condition": "", "fieldName": "tls1_2conn.sum"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "tls1_2conn"}, {"aggField": "tls1_3conn", "fieldType": "number", "aggTypes": [{"value": "sum", "condition": "", "fieldName": "tls1_3conn.sum"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "tls1_3conn"}], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "ZC_TP_SSL_FAILURE"}, {"metricsName": "ZC_CC_LAZY_DETAIL", "oldMetricsName": "ZC-CC-LAZY-DETAIL", "tagNames": ["cluster", "hostname", "instance", "instanceid", "mmc", "zone", "region"], "tagDetails": [{"tagName": "cluster", "oldTagName": "Cluster"}, {"tagName": "hostname", "oldTagName": "Hostname"}, {"tagName": "instance", "oldTagName": "Instance"}, {"tagName": "instanceid", "oldTagName": "InstanceId"}, {"tagName": "mmc", "oldTagName": "MMC"}, {"tagName": "zone", "oldTagName": "Zone"}, {"tagName": "region", "oldTagName": "Region"}], "type": 2, "enabled": true, "metricsFieldList": [{"fieldName": "elapsed_time", "fieldType": 1}, {"fieldName": "truck_note", "fieldType": 2}, {"fieldName": "user_data_request_id", "fieldType": 1}, {"fieldName": "req_note", "fieldType": 2}, {"fieldName": "cc_request_id", "fieldType": 1}, {"fieldName": "index_note", "fieldType": 2}, {"fieldName": "index_id", "fieldType": 1}, {"fieldName": "cc_dest_note", "fieldType": 2}, {"fieldName": "addr", "fieldType": 2}, {"fieldName": "current_rtt", "fieldType": 2}, {"fieldName": "min_rtt", "fieldType": 2}, {"fieldName": "max_rtt", "fieldType": 2}, {"fieldName": "wma_rtt", "fieldType": 2}], "metricsAggregation": {"metricsAggregationRuleCompose": {"common": [], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "ZC_CC_LAZY"}, {"metricsName": "ZC_ZC_STAT_TEST", "oldMetricsName": "ZC_ZC-STAT-TEST", "tagNames": ["instance", "instanceid", "hostname", "region", "zone", "mmc", "cluster", "env"], "tagDetails": [{"tagName": "instance", "oldTagName": "Instance"}, {"tagName": "instanceid", "oldTagName": "InstanceId"}, {"tagName": "hostname", "oldTagName": "Hostname"}, {"tagName": "region", "oldTagName": "Region"}, {"tagName": "zone", "oldTagName": "Zone"}, {"tagName": "mmc", "oldTagName": "MMC"}, {"tagName": "cluster", "oldTagName": "Cluster"}, {"tagName": "env", "oldTagName": "ENV"}], "type": 2, "enabled": true, "metricsFieldList": [{"fieldName": "stat", "fieldType": 1}], "metricsAggregation": {"metricsAggregationRuleCompose": {"common": [], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "ZC_ZC_STAT"}, {"metricsName": "ZC_PK_CONN_FAILURE", "oldMetricsName": "ZC_PK-CONN-FAILURE", "tagNames": ["instance", "zone"], "tagDetails": [{"tagName": "instance"}, {"tagName": "zone", "oldTagName": "Zone"}], "type": 2, "enabled": true, "metricsFieldList": [{"fieldName": "zone_pk_conn_failure", "oldFieldName": "zone.pk.conn.failure", "fieldType": 2}, {"fieldName": "thresold", "fieldType": 1}], "metricsAggregation": {"metricsAggregationRuleCompose": {"common": [], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "ZC_PK_CONN_FAILURE"}, {"metricsName": "ZC_MMR_LOST", "oldMetricsName": "ZC-MMR-LOST", "tagNames": ["reason", "mmr_address", "instanceid", "hostname", "region", "zone", "mmc"], "tagDetails": [{"tagName": "reason"}, {"tagName": "mmr_address"}, {"tagName": "instanceid", "oldTagName": "InstanceId"}, {"tagName": "hostname", "oldTagName": "Hostname"}, {"tagName": "region", "oldTagName": "Region"}, {"tagName": "zone", "oldTagName": "Zone"}, {"tagName": "mmc", "oldTagName": "MMC"}], "type": 2, "enabled": true, "metricsFieldList": [{"fieldName": "conf_count", "fieldType": 1}, {"fieldName": "client_count", "fieldType": 1}], "metricsAggregation": {"metricsAggregationRuleCompose": {"common": [], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "ZC_MMR_LOST"}, {"metricsName": "ZC_WEB_LAZY", "oldMetricsName": "ZC-WEB-LAZY", "tagNames": ["instance", "instanceid", "mmc", "hostname", "region", "zone"], "tagDetails": [{"tagName": "instance", "oldTagName": "Instance"}, {"tagName": "instanceid", "oldTagName": "InstanceId"}, {"tagName": "mmc", "oldTagName": "MMC"}, {"tagName": "hostname", "oldTagName": "Hostname"}, {"tagName": "region", "oldTagName": "Region"}, {"tagName": "zone", "oldTagName": "Zone"}], "type": 2, "enabled": true, "metricsFieldList": [{"fieldName": "web_url", "fieldType": 2}, {"fieldName": "used_time", "fieldType": 1}, {"fieldName": "dns_tme", "fieldType": 1}, {"fieldName": "process_time", "fieldType": 1}, {"fieldName": "tracking_id", "fieldType": 2}], "metricsAggregation": {"metricsAggregationRuleCompose": {"common": [], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "ZC_WEB_LAZY"}, {"metricsName": "ZC_WEB_LAZY_REGION", "oldMetricsName": "ZC-WEB-LAZY-REGION", "tagNames": ["region", "mmc"], "tagDetails": [{"tagName": "region", "oldTagName": "Region"}, {"tagName": "mmc", "oldTagName": "MMC"}], "type": 1, "enabled": true, "metricsFieldList": [{"fieldName": "zc_web_lazy.count", "oldFieldName": "ZC_WEB_LAZY", "fieldType": 1}], "metricsAggregation": {"aggPeriod": 60, "waitTime": 0, "filterCondition": "", "aggregationResultFilter": "", "metricsAggregationRuleCompose": {"common": [{"aggField": "zc_web_lazy", "fieldType": "number", "aggTypes": [{"value": "count", "condition": "", "fieldName": "zc_web_lazy.count"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "zc_web_lazy"}], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "ZC_WEB_LAZY"}, {"metricsName": "ZC_WEB_LOST_INFO", "oldMetricsName": "ZC-WEB-LOST-INFO", "tagNames": ["hostname", "mmc", "zone", "region", "instanceid"], "tagDetails": [{"tagName": "hostname", "oldTagName": "Hostname"}, {"tagName": "mmc", "oldTagName": "MMC"}, {"tagName": "zone", "oldTagName": "Zone"}, {"tagName": "region", "oldTagName": "Region"}, {"tagName": "instanceid", "oldTagName": "InstanceId"}], "type": 2, "enabled": true, "metricsFieldList": [{"fieldName": "url", "fieldType": 2}, {"fieldName": "dns_time", "fieldType": 2}, {"fieldName": "result", "fieldType": 1}, {"fieldName": "error", "fieldType": 2}], "metricsAggregation": {"metricsAggregationRuleCompose": {"common": [], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "ZC_WEB_LOST"}, {"metricsName": "ZC_CC_LAZY_CC_INFO", "oldMetricsName": "ZC-CC-LAZY-CC-INFO", "tagNames": ["cc_addr"], "tagDetails": [{"tagName": "cc_addr"}], "type": 1, "enabled": true, "metricsFieldList": [{"fieldName": "cc_lazy_count.count", "oldFieldName": "CC_LAZY_COUNT", "fieldType": 1}], "metricsAggregation": {"aggPeriod": 60, "waitTime": 0, "filterCondition": "", "aggregationResultFilter": "", "metricsAggregationRuleCompose": {"common": [{"aggField": "cc_lazy_count", "fieldType": "number", "aggTypes": [{"value": "count", "condition": "", "fieldName": "cc_lazy_count.count"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "cc_lazy_count"}], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "ZC_CC_LAZY"}, {"metricsName": "ZC_CC_LOST_REGION", "oldMetricsName": "ZC-CC-LOST-REGION", "tagNames": ["mmc", "region"], "tagDetails": [{"tagName": "mmc", "oldTagName": "MMC"}, {"tagName": "region", "oldTagName": "Region"}], "type": 1, "enabled": true, "metricsFieldList": [{"fieldName": "cc_lost_region_failure_count.count", "fieldType": 1}], "metricsAggregation": {"aggPeriod": 60, "waitTime": 0, "filterCondition": "", "aggregationResultFilter": "", "metricsAggregationRuleCompose": {"common": [{"aggField": "cc_lost_region_failure_count", "fieldType": "number", "aggTypes": [{"value": "count", "condition": "", "fieldName": "cc_lost_region_failure_count.count"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "cc_lost_region_failure_count"}], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "ZC_CC_LOST"}, {"metricsName": "ZC_LOAD_DC", "oldMetricsName": "ZC-LOAD-DC", "tagNames": ["cluster", "mmc", "region"], "tagDetails": [{"tagName": "cluster", "oldTagName": "Cluster"}, {"tagName": "mmc", "oldTagName": "MMC"}, {"tagName": "region", "oldTagName": "Region"}], "type": 1, "enabled": true, "metricsFieldList": [{"fieldName": "panelist.avg", "fieldType": 1}, {"fieldName": "client.avg", "fieldType": 1}], "metricsAggregation": {"aggPeriod": 60, "waitTime": 0, "filterCondition": "", "aggregationResultFilter": "", "metricsAggregationRuleCompose": {"common": [{"aggField": "panelist", "fieldType": "number", "aggTypes": [{"value": "avg", "condition": "", "fieldName": "panelist.avg"}, {"value": "sum", "condition": "", "fieldName": "panelist.sum"}, {"value": "count", "condition": "", "fieldName": "panelist.count"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "panelist"}, {"aggField": "client", "fieldType": "number", "aggTypes": [{"value": "avg", "condition": "", "fieldName": "client.avg"}, {"value": "sum", "condition": "", "fieldName": "client.sum"}, {"value": "count", "condition": "", "fieldName": "client.count"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "client"}], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "ZC_ZONE_LOAD"}, {"metricsName": "ZC_CSMS_CONN_FAILURE", "tagNames": ["hostname", "instanceid", "region", "zone", "mmc"], "tagDetails": [{"tagName": "hostname", "oldTagName": "Hostname"}, {"tagName": "instanceid", "oldTagName": "InstanceId"}, {"tagName": "region", "oldTagName": "Region"}, {"tagName": "zone", "oldTagName": "Zone"}, {"tagName": "mmc", "oldTagName": "MMC"}], "type": 2, "enabled": true, "metricsFieldList": [{"fieldName": "csms_conn_failure", "fieldType": 1}], "metricsAggregation": {"metricsAggregationRuleCompose": {"common": [], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "ZC_CSMS_RUN_FAILURE"}, {"metricsName": "CMA_ZONE_LEVEL_GW_STATUS", "oldMetricsName": "CMA_ZONE_LEVEL_GW-STATUS", "tagNames": ["region", "zone", "cluster"], "tagDetails": [{"tagName": "region", "oldTagName": "Region"}, {"tagName": "zone", "oldTagName": "Zone"}, {"tagName": "cluster", "oldTagName": "Cluster"}], "type": 2, "enabled": true, "metricsFieldList": [{"fieldName": "service_type", "fieldType": 1}, {"fieldName": "total_server_count", "fieldType": 1}, {"fieldName": "active_server_count", "fieldType": 1}, {"fieldName": "total_conf_count", "fieldType": 1}, {"fieldName": "active_conf_count", "fieldType": 1}, {"fieldName": "active_server_percent", "fieldType": 1}, {"fieldName": "active_conf_percent", "fieldType": 1}], "metricsAggregation": {"metricsAggregationRuleCompose": {"common": [], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "ZC_GW_STATUS"}, {"metricsName": "ZC_GW_UNAVAILABLE", "oldMetricsName": "ZC_GW-UNAVAILABLE", "tagNames": ["instance", "instanceid", "hostname", "region", "zone", "cluster", "meeting_uuid", "service_type_tag"], "tagDetails": [{"tagName": "instance", "oldTagName": "Instance"}, {"tagName": "instanceid", "oldTagName": "InstanceId"}, {"tagName": "hostname", "oldTagName": "Hostname"}, {"tagName": "region", "oldTagName": "Region"}, {"tagName": "zone", "oldTagName": "Zone"}, {"tagName": "cluster", "oldTagName": "Cluster"}, {"tagName": "meeting_uuid"}, {"tagName": "service_type_tag"}], "type": 2, "enabled": true, "metricsFieldList": [{"fieldName": "reason", "fieldType": 1}, {"fieldName": "uuid", "fieldType": 2}, {"fieldName": "service_type", "fieldType": 1}, {"fieldName": "count", "fieldType": 1}], "metricsAggregation": {"metricsAggregationRuleCompose": {"common": [], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "ZC_GW_UNAVAILABLE"}, {"metricsName": "CMR_ZONE_LEVEL_GW_STATUS", "oldMetricsName": "CMR_ZONE_LEVEL_GW-STATUS", "tagNames": ["region", "zone", "cluster", "instanceid", "hostname"], "tagDetails": [{"tagName": "region", "oldTagName": "Region"}, {"tagName": "zone", "oldTagName": "Zone"}, {"tagName": "cluster", "oldTagName": "Cluster"}, {"tagName": "instanceid", "oldTagName": "InstanceId"}, {"tagName": "hostname", "oldTagName": "Hostname"}], "type": 2, "enabled": true, "metricsFieldList": [{"fieldName": "service_type", "fieldType": 1}, {"fieldName": "total_server_count", "fieldType": 1}, {"fieldName": "active_server_count", "fieldType": 1}, {"fieldName": "total_conf_count", "fieldType": 1}, {"fieldName": "active_conf_count", "fieldType": 1}, {"fieldName": "active_server_percent", "fieldType": 1}, {"fieldName": "active_conf_percent", "fieldType": 1}], "metricsAggregation": {"metricsAggregationRuleCompose": {"common": [], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "ZC_GW_STATUS"}, {"metricsName": "TXTGW_ZONE_LEVEL_GW_STATUS", "oldMetricsName": "TXTGW_ZONE_LEVEL_GW-STATUS", "tagNames": ["region", "zone", "cluster"], "tagDetails": [{"tagName": "region", "oldTagName": "Region"}, {"tagName": "zone", "oldTagName": "Zone"}, {"tagName": "cluster", "oldTagName": "Cluster"}], "type": 2, "enabled": true, "metricsFieldList": [{"fieldName": "service_type", "fieldType": 1}, {"fieldName": "total_server_count", "fieldType": 1}, {"fieldName": "active_server_count", "fieldType": 1}, {"fieldName": "total_conf_count", "fieldType": 1}, {"fieldName": "active_conf_count", "fieldType": 1}, {"fieldName": "active_server_percent", "fieldType": 1}, {"fieldName": "active_conf_percent", "fieldType": 1}], "metricsAggregation": {"metricsAggregationRuleCompose": {"common": [], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "ZC_GW_STATUS"}, {"metricsName": "TXTGW_TXTA_GW_UNAVAILABLE", "oldMetricsName": "TXTGW_TXTA_GW-UNAVAILABLE", "tagNames": ["cluster", "instance", "instanceid", "hostname", "region", "zone"], "tagDetails": [{"tagName": "cluster", "oldTagName": "Cluster"}, {"tagName": "instance", "oldTagName": "Instance"}, {"tagName": "instanceid", "oldTagName": "InstanceId"}, {"tagName": "hostname", "oldTagName": "Hostname"}, {"tagName": "region", "oldTagName": "Region"}, {"tagName": "zone", "oldTagName": "Zone"}], "type": 2, "enabled": true, "metricsFieldList": [{"fieldName": "reason", "fieldType": 1}, {"fieldName": "meeting_uuid", "fieldType": 2}, {"fieldName": "service_type", "fieldType": 1}], "metricsAggregation": {"metricsAggregationRuleCompose": {"common": [], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "ZC_GW_UNAVAILABLE"}, {"metricsName": "Resp_Client_ERR", "tagNames": ["cluster", "instanceid", "region", "zone", "error_code"], "tagDetails": [{"tagName": "cluster", "oldTagName": "Cluster"}, {"tagName": "instanceid", "oldTagName": "InstanceId"}, {"tagName": "region", "oldTagName": "Region"}, {"tagName": "zone", "oldTagName": "Zone"}, {"tagName": "error_code"}], "type": 1, "enabled": true, "metricsFieldList": [{"fieldName": "error_code_cnt.count", "fieldType": 1}], "metricsAggregation": {"aggPeriod": 60, "waitTime": 0, "filterCondition": "", "aggregationResultFilter": "", "metricsAggregationRuleCompose": {"common": [{"aggField": "error_code_cnt", "fieldType": "number", "aggTypes": [{"value": "count", "condition": "", "fieldName": "error_code_cnt.count"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "error_code_cnt"}], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "ZC_RESP_CLI_ERR"}, {"metricsName": "Resp_cli_err_mmc", "tagNames": ["mmc", "error_code"], "tagDetails": [{"tagName": "mmc", "oldTagName": "MMC"}, {"tagName": "error_code"}], "type": 1, "enabled": true, "metricsFieldList": [{"fieldName": "error_code_cnt.count", "fieldType": 1}], "metricsAggregation": {"aggPeriod": 60, "waitTime": 0, "filterCondition": "", "aggregationResultFilter": "", "metricsAggregationRuleCompose": {"common": [{"aggField": "error_code_cnt", "fieldType": "number", "aggTypes": [{"value": "count", "condition": "", "fieldName": "error_code_cnt.count"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "error_code_cnt"}], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "ZC_RESP_CLI_ERR"}, {"metricsName": "Resp_cli_err_cluster", "tagNames": ["cluster", "error_code"], "tagDetails": [{"tagName": "cluster", "oldTagName": "Cluster"}, {"tagName": "error_code"}], "type": 1, "enabled": true, "metricsFieldList": [{"fieldName": "error_code_cnt.count", "fieldType": 1}], "metricsAggregation": {"aggPeriod": 60, "waitTime": 0, "filterCondition": "", "aggregationResultFilter": "", "metricsAggregationRuleCompose": {"common": [{"aggField": "error_code_cnt", "fieldType": "number", "aggTypes": [{"value": "count", "condition": "", "fieldName": "error_code_cnt.count"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "error_code_cnt"}], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "ZC_RESP_CLI_ERR"}, {"metricsName": "ocsp_curl_retry_failure", "tagNames": ["cluster", "region", "zone", "instanceid"], "tagDetails": [{"tagName": "cluster", "oldTagName": "Cluster"}, {"tagName": "region", "oldTagName": "Region"}, {"tagName": "zone", "oldTagName": "Zone"}, {"tagName": "instanceid", "oldTagName": "InstanceId"}], "type": 2, "enabled": true, "metricsFieldList": [{"fieldName": "fingerprint", "fieldType": 2}], "metricsAggregation": {"metricsAggregationRuleCompose": {"common": [], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "ZC_OCSP_CURL_MAX_RETRY_FAIL"}, {"metricsName": "Resp_client_err_details", "tagNames": ["cluster", "hostname", "mmc", "instanceid", "error_code", "conf_id", "zone"], "tagDetails": [{"tagName": "cluster", "oldTagName": "Cluster"}, {"tagName": "hostname", "oldTagName": "Hostname"}, {"tagName": "mmc", "oldTagName": "MMC"}, {"tagName": "instanceid", "oldTagName": "InstanceId"}, {"tagName": "error_code"}, {"tagName": "conf_id"}, {"tagName": "zone", "oldTagName": "Zone"}], "type": 1, "enabled": true, "metricsFieldList": [{"fieldName": "error_code_cnt.count", "fieldType": 1}], "metricsAggregation": {"aggPeriod": 60, "waitTime": 0, "filterCondition": "", "aggregationResultFilter": "", "metricsAggregationRuleCompose": {"common": [{"aggField": "error_code_cnt", "fieldType": "number", "aggTypes": [{"value": "count", "condition": "", "fieldName": "error_code_cnt.count"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "error_code_cnt"}], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "ZC_RESP_CLI_ERR"}, {"metricsName": "REG_WCC_FAIL", "oldMetricsName": "REG-WCC-FAIL", "tagNames": ["instance", "instanceid", "region", "zone"], "tagDetails": [{"tagName": "instance", "oldTagName": "Instance"}, {"tagName": "instanceid", "oldTagName": "InstanceId"}, {"tagName": "region", "oldTagName": "Region"}, {"tagName": "zone", "oldTagName": "Zone"}], "type": 2, "enabled": true, "metricsFieldList": [{"fieldName": "zone_urn", "fieldType": 2}, {"fieldName": "response", "fieldType": 2}, {"fieldName": "register_fail_count", "oldFieldName": "register.fail.count", "fieldType": 1}], "metricsAggregation": {"metricsAggregationRuleCompose": {"common": [], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "ZC_REG_WCC_FAIL"}, {"metricsName": "LSSZC_FULL", "oldMetricsName": "LSSZC-FULL", "tagNames": ["hostname", "instance", "zone", "instanceid", "region", "env"], "tagDetails": [{"tagName": "hostname", "oldTagName": "Hostname"}, {"tagName": "instance", "oldTagName": "Instance"}, {"tagName": "zone", "oldTagName": "Zone"}, {"tagName": "instanceid", "oldTagName": "InstanceId"}, {"tagName": "region", "oldTagName": "Region"}, {"tagName": "env", "oldTagName": "ENV"}], "type": 2, "enabled": true, "metricsFieldList": [{"fieldName": "pull_count", "fieldType": 1}, {"fieldName": "push_count", "fieldType": 1}, {"fieldName": "pull_capacity", "fieldType": 1}, {"fieldName": "push_capacity", "fieldType": 1}, {"fieldName": "count", "fieldType": 1}], "metricsAggregation": {"metricsAggregationRuleCompose": {"common": [], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "ZC_ZC_FULL"}, {"metricsName": "LSSZC_DAILY_STREAM", "oldMetricsName": "LSSZC DAILY STREAM", "tagNames": ["hostname", "instance", "instanceid", "region", "zone", "env"], "tagDetails": [{"tagName": "hostname", "oldTagName": "Hostname"}, {"tagName": "instance", "oldTagName": "Instance"}, {"tagName": "instanceid", "oldTagName": "InstanceId"}, {"tagName": "region", "oldTagName": "Region"}, {"tagName": "zone", "oldTagName": "Zone"}, {"tagName": "env", "oldTagName": "ENV"}], "type": 2, "enabled": true, "metricsFieldList": [{"fieldName": "viewer_ping_req", "fieldType": 1}, {"fieldName": "stream_create_count", "fieldType": 1}, {"fieldName": "max_pull_count", "fieldType": 1}, {"fieldName": "max_push_count", "fieldType": 1}], "metricsAggregation": {"metricsAggregationRuleCompose": {"common": [], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "ZC_STREAM_DAY_MONITOR"}, {"metricsName": "COMMAND_DROP", "oldMetricsName": "COMMAND-DROP", "tagNames": ["hostname", "instanceid", "region", "zone"], "tagDetails": [{"tagName": "hostname", "oldTagName": "Hostname"}, {"tagName": "instanceid", "oldTagName": "InstanceId"}, {"tagName": "region", "oldTagName": "Region"}, {"tagName": "zone", "oldTagName": "Zone"}], "type": 2, "enabled": true, "metricsFieldList": [{"fieldName": "conf_id", "fieldType": 2}, {"fieldName": "msg_type", "fieldType": 1}], "metricsAggregation": {"metricsAggregationRuleCompose": {"common": [], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "ZC_COMMAND_DROP"}, {"metricsName": "ZC_ASYNCMQ_DELAY", "oldMetricsName": "ZC-ASYNCMQ-DELAY", "tagNames": ["hostname", "instanceid", "region", "zone"], "tagDetails": [{"tagName": "hostname", "oldTagName": "Hostname"}, {"tagName": "instanceid", "oldTagName": "InstanceId"}, {"tagName": "region", "oldTagName": "Region"}, {"tagName": "zone", "oldTagName": "Zone"}], "type": 2, "enabled": true, "metricsFieldList": [{"fieldName": "task_id", "fieldType": 2}, {"fieldName": "produce_time", "fieldType": 2}, {"fieldName": "generate_time", "fieldType": 2}, {"fieldName": "arrive_time", "fieldType": 2}, {"fieldName": "receive_time", "fieldType": 2}, {"fieldName": "handle_time", "fieldType": 2}], "metricsAggregation": {"metricsAggregationRuleCompose": {"common": [], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "ZC_ASYNCMQ_DELAY"}, {"metricsName": "conf_zc_failover_count", "tagNames": ["instanceid", "zone", "cluster", "hostname"], "tagDetails": [{"tagName": "instanceid", "oldTagName": "InstanceId"}, {"tagName": "zone", "oldTagName": "Zone"}, {"tagName": "cluster", "oldTagName": "Cluster"}, {"tagName": "hostname", "oldTagName": "Hostname"}], "type": 2, "enabled": true, "metricsFieldList": [{"fieldName": "confcount", "oldFieldName": "confCount", "fieldType": 1}], "metricsAggregation": {"metricsAggregationRuleCompose": {"common": [], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "ZC_CONF_ZC_FAILOVER_CNT"}, {"metricsName": "IB_Group_Count_Zero", "oldMetricsName": "IB-Group-Count-Zero", "tagNames": ["cluster", "instance", "instanceid", "meetingid"], "tagDetails": [{"tagName": "cluster", "oldTagName": "Cluster"}, {"tagName": "instance", "oldTagName": "Instance"}, {"tagName": "instanceid", "oldTagName": "InstanceId"}, {"tagName": "meetingid", "oldTagName": "MeetingId"}], "type": 2, "enabled": true, "metricsFieldList": [{"fieldName": "meeting_id", "fieldType": 1}], "metricsAggregation": {"metricsAggregationRuleCompose": {"common": [], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "ZC_IB_GR_ERR"}, {"metricsName": "KMS_FATAL", "oldMetricsName": "KMS-FATAL", "tagNames": ["hostname", "instanceid", "region", "zone"], "tagDetails": [{"tagName": "hostname", "oldTagName": "Hostname"}, {"tagName": "instanceid", "oldTagName": "InstanceId"}, {"tagName": "region", "oldTagName": "Region"}, {"tagName": "zone", "oldTagName": "Zone"}], "type": 2, "enabled": true, "metricsFieldList": [{"fieldName": "conf_id", "fieldType": 2}, {"fieldName": "err_code", "fieldType": 1}, {"fieldName": "tracking_id", "fieldType": 2}], "metricsAggregation": {"metricsAggregationRuleCompose": {"common": [], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "ZC_KMS_FATAL"}, {"metricsName": "ZC_STATISTICS", "tagNames": ["instance", "mmc", "region", "instanceid", "zone"], "tagDetails": [{"tagName": "instance", "oldTagName": "Instance"}, {"tagName": "mmc", "oldTagName": "MMC"}, {"tagName": "region", "oldTagName": "Region"}, {"tagName": "instanceid", "oldTagName": "InstanceId"}, {"tagName": "zone", "oldTagName": "Zone"}], "type": 2, "enabled": true, "metricsFieldList": [{"fieldName": "zone_statistics_maxclient_count", "oldFieldName": "zone.statistics.maxclient.count", "fieldType": 1}, {"fieldName": "zone_statistics_mmr_count", "oldFieldName": "zone.statistics.mmr.count", "fieldType": 1}, {"fieldName": "zone_statistics_unsuspended_count", "oldFieldName": "zone.statistics.unsuspended.count", "fieldType": 1}, {"fieldName": "zone_statistics_capacity_total", "oldFieldName": "zone.statistics.capacity.total", "fieldType": 1}, {"fieldName": "zone_statistics_capacity_availiable", "oldFieldName": "zone.statistics.capacity.availiable", "fieldType": 1}, {"fieldName": "zone_statistics_participant_count", "oldFieldName": "zone.statistics.participant.count", "fieldType": 1}, {"fieldName": "zone_statistics_capacity_free", "oldFieldName": "zone.statistics.capacity.free", "fieldType": 1}, {"fieldName": "zone_statistics_failpercent", "oldFieldName": "zone.statistics.failpercent", "fieldType": 1}, {"fieldName": "zone_statistics_throughput", "oldFieldName": "zone.statistics.throughput", "fieldType": 1}, {"fieldName": "zone_statistics_usedpercent", "oldFieldName": "zone.statistics.usedPercent", "fieldType": 1}, {"fieldName": "zone_statistics_leftpercent", "oldFieldName": "zone.statistics.leftPercent", "fieldType": 1}], "metricsAggregation": {"metricsAggregationRuleCompose": {"common": [], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "ZC_ZC_STATISTICS"}, {"metricsName": "ZC_CC_LAZY_REGION_COUNT", "oldMetricsName": "ZC-CC-LAZY-REGION-COUNT", "tagNames": ["mmc", "region"], "tagDetails": [{"tagName": "mmc", "oldTagName": "MMC"}, {"tagName": "region", "oldTagName": "Region"}], "type": 1, "enabled": true, "metricsFieldList": [{"fieldName": "lazy_count.count", "fieldType": 1}], "metricsAggregation": {"aggPeriod": 60, "waitTime": 0, "filterCondition": "", "aggregationResultFilter": "", "metricsAggregationRuleCompose": {"common": [{"aggField": "lazy_count", "fieldType": "number", "aggTypes": [{"value": "count", "condition": "", "fieldName": "lazy_count.count"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "lazy_count"}], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "ZC_CC_LAZY"}, {"metricsName": "Resp_Client_ERR_MEETINGID", "tagNames": ["hostname", "meetingid", "error_code"], "tagDetails": [{"tagName": "hostname", "oldTagName": "Hostname"}, {"tagName": "meetingid", "oldTagName": "MeetingId"}, {"tagName": "error_code"}], "type": 1, "enabled": true, "metricsFieldList": [{"fieldName": "join_number.sum", "fieldType": 1}], "metricsAggregation": {"aggPeriod": 300, "waitTime": 0, "filterCondition": "", "aggregationResultFilter": "", "metricsAggregationRuleCompose": {"common": [{"aggField": "join_number", "fieldType": "number", "aggTypes": [{"value": "sum", "condition": "", "fieldName": "join_number.sum"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "join_number"}], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "ZC_RESP_CLI_ERR"}, {"metricsName": "CMR_ZC_NO_LOAD", "oldMetricsName": "CMR-ZC-NO-LOAD", "tagNames": ["zone", "hostname", "instanceid"], "tagDetails": [{"tagName": "zone", "oldTagName": "Zone"}, {"tagName": "hostname", "oldTagName": "Hostname"}, {"tagName": "instanceid", "oldTagName": "InstanceId"}], "type": 2, "enabled": true, "metricsFieldList": [{"fieldName": "meeting_id", "fieldType": 2}], "metricsAggregation": {"metricsAggregationRuleCompose": {"common": [], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "ZC_CMR_UNAVAILABLE"}, {"metricsName": "ZC_CSMS_VFY_KEY", "tagNames": ["cluster", "zone", "instanceid"], "tagDetails": [{"tagName": "cluster", "oldTagName": "Cluster"}, {"tagName": "zone", "oldTagName": "Zone"}, {"tagName": "instanceid", "oldTagName": "InstanceId"}], "type": 2, "enabled": true, "metricsFieldList": [{"fieldName": "aliasname", "oldFieldName": "<PERSON><PERSON><PERSON>", "fieldType": 2}, {"fieldName": "version", "fieldType": 2}, {"fieldName": "stage", "fieldType": 2}, {"fieldName": "size", "fieldType": 1}], "metricsAggregation": {"metricsAggregationRuleCompose": {"common": [], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "ZC_LOAD_KEY"}, {"metricsName": "ZC_CONN_GW_FAILURE", "tagNames": ["meetingid", "instanceid", "zone"], "tagDetails": [{"tagName": "meetingid", "oldTagName": "MeetingId"}, {"tagName": "instanceid", "oldTagName": "InstanceId"}, {"tagName": "zone", "oldTagName": "Zone"}], "type": 2, "enabled": true, "metricsFieldList": [{"fieldName": "<PERSON><PERSON><PERSON>", "oldFieldName": "errCode", "fieldType": 1}, {"fieldName": "dstaddr", "oldFieldName": "dstAddr", "fieldType": 2}], "metricsAggregation": {"metricsAggregationRuleCompose": {"common": [], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "ZC_LONG_GW_CONN_ERR"}, {"metricsName": "ZC_CSMS_UPDATE_KEY", "tagNames": ["cluster", "zone", "instanceid"], "tagDetails": [{"tagName": "cluster", "oldTagName": "Cluster"}, {"tagName": "zone", "oldTagName": "Zone"}, {"tagName": "instanceid", "oldTagName": "InstanceId"}], "type": 2, "enabled": true, "metricsFieldList": [{"fieldName": "aliasname", "oldFieldName": "<PERSON><PERSON><PERSON>", "fieldType": 2}, {"fieldName": "version", "fieldType": 2}, {"fieldName": "status", "fieldType": 2}], "metricsAggregation": {"metricsAggregationRuleCompose": {"common": [], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "ZC_CSMS_UPDATE_KEY"}, {"metricsName": "SVR_REG_FAILURE", "oldMetricsName": "SVR-REG-FAILURE", "tagNames": ["instanceid", "zone"], "tagDetails": [{"tagName": "instanceid", "oldTagName": "InstanceId"}, {"tagName": "zone", "oldTagName": "Zone"}], "type": 2, "enabled": true, "metricsFieldList": [{"fieldName": "addr", "fieldType": 2}], "metricsAggregation": {"metricsAggregationRuleCompose": {"common": [], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "ZC_SVR_REGISTER_FAIL"}, {"metricsName": "LssRegisterFailure", "tagNames": [], "tagDetails": [], "type": 2, "enabled": true, "metricsFieldList": [{"fieldName": "addr", "fieldType": 2}], "metricsAggregation": {"metricsAggregationRuleCompose": {"common": [], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "ZC__svr_token_failure"}, {"metricsName": "ZONE_STATISTICS", "tagNames": ["mmc", "region", "zone"], "tagDetails": [{"tagName": "mmc", "oldTagName": "MMC"}, {"tagName": "region", "oldTagName": "Region"}, {"tagName": "zone", "oldTagName": "Zone"}], "type": 1, "enabled": true, "metricsFieldList": [{"fieldName": "available_capacity.max", "oldFieldName": "available.capacity", "fieldType": 1}, {"fieldName": "clients.max", "fieldType": 1}, {"fieldName": "total_capacity.max", "oldFieldName": "total.capacity", "fieldType": 1}, {"fieldName": "used_percent.max", "oldFieldName": "used.percent", "fieldType": 1}, {"fieldName": "total_mmr.max", "oldFieldName": "total.mmr", "fieldType": 1}, {"fieldName": "available_mmr.max", "oldFieldName": "available.mmr", "fieldType": 1}, {"fieldName": "throughput.max", "fieldType": 1}, {"fieldName": "load.max", "fieldType": 1}, {"fieldName": "pingcount.max", "oldFieldName": "pingCount", "fieldType": 1}, {"fieldName": "failpingcount.max", "oldFieldName": "failPingCount", "fieldType": 1}, {"fieldName": "webusedtime.max", "oldFieldName": "webUsedTime", "fieldType": 1}, {"fieldName": "ccusedtime.max", "oldFieldName": "ccUsedTime", "fieldType": 1}, {"fieldName": "webfailcount.max", "oldFieldName": "webFailCount", "fieldType": 1}, {"fieldName": "webreqcount.max", "oldFieldName": "webReqCount", "fieldType": 1}, {"fieldName": "err_req_rate.max", "oldFieldName": "err.req.rate", "fieldType": 1}], "metricsAggregation": {"aggPeriod": 60, "waitTime": 0, "filterCondition": "", "aggregationResultFilter": "", "metricsAggregationRuleCompose": {"common": [{"aggField": "available_capacity", "fieldType": "number", "aggTypes": [{"value": "max", "condition": "", "fieldName": "available_capacity.max"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "available_capacity"}, {"aggField": "clients", "fieldType": "number", "aggTypes": [{"value": "max", "condition": "", "fieldName": "clients.max"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "clients"}, {"aggField": "total_capacity", "fieldType": "number", "aggTypes": [{"value": "max", "condition": "", "fieldName": "total_capacity.max"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "total_capacity"}, {"aggField": "used_percent", "fieldType": "number", "aggTypes": [{"value": "max", "condition": "", "fieldName": "used_percent.max"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "used_percent"}, {"aggField": "total_mmr", "fieldType": "number", "aggTypes": [{"value": "max", "condition": "", "fieldName": "total_mmr.max"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "total_mmr"}, {"aggField": "available_mmr", "fieldType": "number", "aggTypes": [{"value": "max", "condition": "", "fieldName": "available_mmr.max"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "available_mmr"}, {"aggField": "throughput", "fieldType": "number", "aggTypes": [{"value": "max", "condition": "", "fieldName": "throughput.max"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "throughput"}, {"aggField": "load", "fieldType": "number", "aggTypes": [{"value": "max", "condition": "", "fieldName": "load.max"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "load"}, {"aggField": "pingcount", "fieldType": "number", "aggTypes": [{"value": "max", "condition": "", "fieldName": "pingcount.max"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "pingcount"}, {"aggField": "failpingcount", "fieldType": "number", "aggTypes": [{"value": "max", "condition": "", "fieldName": "failpingcount.max"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "failpingcount"}, {"aggField": "webusedtime", "fieldType": "number", "aggTypes": [{"value": "max", "condition": "", "fieldName": "webusedtime.max"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "webusedtime"}, {"aggField": "ccusedtime", "fieldType": "number", "aggTypes": [{"value": "max", "condition": "", "fieldName": "ccusedtime.max"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "ccusedtime"}, {"aggField": "webfailcount", "fieldType": "number", "aggTypes": [{"value": "max", "condition": "", "fieldName": "webfailcount.max"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "webfailcount"}, {"aggField": "webreqcount", "fieldType": "number", "aggTypes": [{"value": "max", "condition": "", "fieldName": "webreqcount.max"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "webreqcount"}, {"aggField": "err_req_rate", "fieldType": "number", "aggTypes": [{"value": "max", "condition": "", "fieldName": "err_req_rate.max"}], "filterCondition": "", "isConditioned": 0, "conditionalFieldPrefix": "", "spcCompute": 0, "fieldPrefix": "err_req_rate"}], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "ZC_ZC_STATISTICS"}, {"metricsName": "ZC_ERR_Resp_Cli", "oldMetricsName": "ZC-ERR-Resp-Cli", "tagNames": ["mmc", "region", "instanceid", "zone"], "tagDetails": [{"tagName": "mmc", "oldTagName": "MMC"}, {"tagName": "region", "oldTagName": "Region"}, {"tagName": "instanceid", "oldTagName": "InstanceId"}, {"tagName": "zone", "oldTagName": "Zone"}], "type": 2, "enabled": true, "metricsFieldList": [{"fieldName": "error_code", "fieldType": 1}, {"fieldName": "conf_id", "fieldType": 2}], "metricsAggregation": {"metricsAggregationRuleCompose": {"common": [], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "ZC_RESP_CLI_ERR"}, {"metricsName": "LSSZC_STATISTICS", "oldMetricsName": "LSSZC-STATISTICS", "tagNames": ["cluster", "hostname", "zone", "region", "instanceid"], "tagDetails": [{"tagName": "cluster", "oldTagName": "Cluster"}, {"tagName": "hostname", "oldTagName": "Hostname"}, {"tagName": "zone", "oldTagName": "Zone"}, {"tagName": "region", "oldTagName": "Region"}, {"tagName": "instanceid", "oldTagName": "InstanceId"}], "type": 2, "enabled": true, "metricsFieldList": [{"fieldName": "total_lss_count", "fieldType": 1}, {"fieldName": "active_lss_count", "fieldType": 1}, {"fieldName": "pull_count", "fieldType": 1}, {"fieldName": "push_count", "fieldType": 1}, {"fieldName": "player_count", "fieldType": 1}, {"fieldName": "pull_capacity", "fieldType": 1}, {"fieldName": "push_capacity", "fieldType": 1}, {"fieldName": "active_lss_percent", "fieldType": 1}, {"fieldName": "pull_usage_percent", "fieldType": 1}, {"fieldName": "push_usage_percent", "fieldType": 1}], "metricsAggregation": {"metricsAggregationRuleCompose": {"common": [], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "ZC_LSSZC_STATISTICS"}, {"metricsName": "LSSZC_STREAM_ERR", "oldMetricsName": "LSSZC-STREAM-ERR", "tagNames": ["hostname", "cluster", "zone", "region", "instanceid"], "tagDetails": [{"tagName": "hostname", "oldTagName": "Hostname"}, {"tagName": "cluster", "oldTagName": "Cluster"}, {"tagName": "zone", "oldTagName": "Zone"}, {"tagName": "region", "oldTagName": "Region"}, {"tagName": "instanceid", "oldTagName": "InstanceId"}], "type": 2, "enabled": true, "metricsFieldList": [{"fieldName": "err_code", "fieldType": 1}, {"fieldName": "stream_id", "fieldType": 2}, {"fieldName": "count", "fieldType": 1}], "metricsAggregation": {"metricsAggregationRuleCompose": {"common": [], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "ZC_STREAM_ERR"}, {"metricsName": "LSSZC_VIEWER_ERR", "oldMetricsName": "LSSZC-VIEWER-ERR", "tagNames": ["cluster", "hostname", "instance", "zone", "region"], "tagDetails": [{"tagName": "cluster", "oldTagName": "Cluster"}, {"tagName": "hostname", "oldTagName": "Hostname"}, {"tagName": "instance", "oldTagName": "Instance"}, {"tagName": "zone", "oldTagName": "Zone"}, {"tagName": "region", "oldTagName": "Region"}], "type": 2, "enabled": true, "metricsFieldList": [{"fieldName": "error_code", "fieldType": 1}, {"fieldName": "stream_id", "fieldType": 2}, {"fieldName": "count", "fieldType": 1}], "metricsAggregation": {"metricsAggregationRuleCompose": {"common": [], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "ZC_VIEWER_ERR"}, {"metricsName": "ZC_SPLIT_MEETING_CNT", "oldMetricsName": "ZC-SPLIT-MEETING-CNT", "tagNames": ["mmc", "zone", "instanceid", "meetingid"], "tagDetails": [{"tagName": "mmc", "oldTagName": "MMC"}, {"tagName": "zone", "oldTagName": "Zone"}, {"tagName": "instanceid", "oldTagName": "InstanceId"}, {"tagName": "meetingid", "oldTagName": "MeetingId"}], "type": 2, "enabled": true, "metricsFieldList": [{"fieldName": "merge_times", "fieldType": 1}], "metricsAggregation": {"metricsAggregationRuleCompose": {"common": [], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "ZC_SPLIT_MEETING"}, {"metricsName": "ZC_Conf_Distribution", "tagNames": ["cluster", "instanceid", "zone", "meetingid"], "tagDetails": [{"tagName": "cluster", "oldTagName": "Cluster"}, {"tagName": "instanceid", "oldTagName": "InstanceId"}, {"tagName": "zone", "oldTagName": "Zone"}, {"tagName": "meetingid", "oldTagName": "MeetingId"}], "type": 2, "enabled": true, "metricsFieldList": [{"fieldName": "mmr_cnts", "fieldType": 1}, {"fieldName": "attendee_cnts", "fieldType": 1}, {"fieldName": "meeting_type", "fieldType": 1}], "metricsAggregation": {"metricsAggregationRuleCompose": {"common": [], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "ZC_CONF_DIST"}, {"metricsName": "Z<PERSON>_ZON<PERSON>_LEVEL_GW_STATUS", "oldMetricsName": "ZSS_ZONE_LEVEL_GW-STATUS", "tagNames": ["hostname", "zone", "region", "cluster"], "tagDetails": [{"tagName": "hostname", "oldTagName": "Hostname"}, {"tagName": "zone", "oldTagName": "Zone"}, {"tagName": "region", "oldTagName": "Region"}, {"tagName": "cluster", "oldTagName": "Cluster"}], "type": 2, "enabled": true, "metricsFieldList": [{"fieldName": "service_type", "fieldType": 1}, {"fieldName": "total_server_count", "fieldType": 1}, {"fieldName": "active_server_count", "fieldType": 1}, {"fieldName": "total_conf_count", "fieldType": 1}, {"fieldName": "active_conf_count", "fieldType": 1}, {"fieldName": "active_server_percent", "fieldType": 1}], "metricsAggregation": {"metricsAggregationRuleCompose": {"common": [], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "ZC_GW_STATUS"}, {"metricsName": "ZC_Conf_Failover_Count", "tagNames": ["mmc", "region", "zone", "instanceid"], "tagDetails": [{"tagName": "mmc", "oldTagName": "MMC"}, {"tagName": "region", "oldTagName": "Region"}, {"tagName": "zone", "oldTagName": "Zone"}, {"tagName": "instanceid", "oldTagName": "InstanceId"}], "type": 2, "enabled": true, "metricsFieldList": [{"fieldName": "conf_cnt", "fieldType": 1}, {"fieldName": "cluster", "fieldType": 1}], "metricsAggregation": {"metricsAggregationRuleCompose": {"common": [], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "ZC_CONF_ZC_FAILOVER_CNT"}, {"metricsName": "ZC_MQ_AGENT_CONN_FAIL", "oldMetricsName": "ZC-MQ-AGENT-CONN-FAIL", "tagNames": ["hostname", "region", "zone", "instanceid"], "tagDetails": [{"tagName": "hostname", "oldTagName": "Hostname"}, {"tagName": "region", "oldTagName": "Region"}, {"tagName": "zone", "oldTagName": "Zone"}, {"tagName": "instanceid", "oldTagName": "InstanceId"}], "type": 2, "enabled": true, "metricsFieldList": [{"fieldName": "reason", "fieldType": 1}], "metricsAggregation": {"metricsAggregationRuleCompose": {"common": [], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "ZC_MQ_AGENT_CONN_FAIL"}, {"metricsName": "LTTGW_ZONE_LEVEL_GW_STATUS", "oldMetricsName": "LTTGW_ZONE_LEVEL_GW-STATUS", "tagNames": ["region", "zone", "cluster"], "tagDetails": [{"tagName": "region", "oldTagName": "Region"}, {"tagName": "zone", "oldTagName": "Zone"}, {"tagName": "cluster", "oldTagName": "Cluster"}], "type": 2, "enabled": true, "metricsFieldList": [{"fieldName": "service_type", "fieldType": 1}, {"fieldName": "total_server_count", "fieldType": 1}, {"fieldName": "active_server_count", "fieldType": 1}, {"fieldName": "total_conf_count", "fieldType": 1}, {"fieldName": "active_conf_count", "fieldType": 1}, {"fieldName": "active_server_percent", "fieldType": 1}], "metricsAggregation": {"metricsAggregationRuleCompose": {"common": [], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "ZC_GW_STATUS"}, {"metricsName": "ZC_CURL_FAIL", "oldMetricsName": "ZC-CURL-FAIL", "tagNames": ["hostname", "instanceid", "region", "zone"], "tagDetails": [{"tagName": "hostname", "oldTagName": "Hostname"}, {"tagName": "instanceid", "oldTagName": "InstanceId"}, {"tagName": "region", "oldTagName": "Region"}, {"tagName": "zone", "oldTagName": "Zone"}], "type": 2, "enabled": true, "metricsFieldList": [{"fieldName": "result", "fieldType": 1}, {"fieldName": "url", "fieldType": 1}], "metricsAggregation": {"metricsAggregationRuleCompose": {"common": [], "histogram": [], "percentile": [], "customize": []}, "spcStatisticalPeriod": 1, "spcSamplingWeight": 1}, "metricsOriginalOutput": {"originalCustomFieldRuleOutputs": []}, "labelInfoList": [], "hasBatchAggMetric": false, "pipelineName": "ZC_CURL_FAIL"}], "alarms": [{"name": "SA01-DC-MAX-MEETING-PARTICIPANTS", "rules": [{"level": "WARN", "needHits": 1, "hitCount": 1, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "panelist.avg", "operator": ">=", "threshold": "30"}, {"conditionType": "TAG", "name": "region", "operator": "in", "threshold": "JED_DIST"}]}, {"level": "ERROR", "needHits": 1, "hitCount": 1, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "panelist.avg", "operator": ">=", "threshold": "1000"}, {"conditionType": "TAG", "name": "region", "operator": "in", "threshold": "JED_DIST"}]}], "timesInPeriod": 1, "periodInMinutes": 5, "enabled": false, "notifications": [{"channel": {"name": "RTM - MMR", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "SA01-DC-MAX-MEETING-PARTICIPANTS", "content": "Participant count : ${panelist.avg}\n", "whichLevels": "ERROR", "repeatedSendAsyncmq": false, "isMarkdown": false, "mentions": "[{\"displayName\":\"<PERSON><PERSON><PERSON>\",\"email\":\"<EMAIL>\"},{\"displayName\":\"<PERSON>\",\"email\":\"<EMAIL>\"}]", "pdGroupTags": ""}, {"channel": {"name": "Pagerduty-MMZ-p1", "engineName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isDefault": 0}, "title": "SA01-DC-MAX-MEETING-PARTICIPANTS", "content": "Participant count : ${panelist.avg}", "whichLevels": "ERROR", "repeatedSendAsyncmq": false, "isMarkdown": false, "mentions": "[]", "pdGroupTags": ""}, {"channel": {"name": "ZC alarm", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "SA01-DC-MAX-MEETING-PARTICIPANTS", "content": "Participant count : ${panelist.avg}", "whichLevels": "WARN", "repeatedSendAsyncmq": false, "isMarkdown": false, "mentions": "[]", "pdGroupTags": ""}], "alarmExtensionRelations": [], "description": " concurrent avg meeting participants >=1K", "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "ZC_LOAD_DC"}, {"name": "LSS_ZONE_UTILIZATION", "rules": [{"level": "WARN", "needHits": 2, "hitCount": 2, "timeWindow": 120, "isContinuous": true, "conditions": [{"conditionType": "EXPRESSION", "expression": "(pull_count>0 && pull_count/pull_capacity > 0.7) || (push_count>0 && push_count/push_capacity > 0.7)"}, {"conditionType": "TAG", "name": "zone", "operator": "not-in", "threshold": "AMS_LSS_HYZ,SJC_LSS_HYZ,IAD_LSS_HYZ,GO_SJC_LSS_HYZ,FRA_LSS_HYZ,TJ_LSS_HYZ,RTS_GO_SJC_LSS,GO_SJC_LSS,scaling_zone_prod_us01_uswest2_realtime_verification,Unknown,scaling_zone_prod_go_oh1_web_verification"}]}, {"level": "ERROR", "needHits": 2, "hitCount": 2, "timeWindow": 120, "isContinuous": true, "conditions": [{"conditionType": "EXPRESSION", "expression": "(pull_count>0 && pull_count/pull_capacity > 0.8) || (push_count>0 && push_count/push_capacity > 0.8)"}, {"conditionType": "TAG", "name": "zone", "operator": "not-in", "threshold": "AMS_LSS_HYZ,SJC_LSS_HYZ,IAD_LSS_HYZ,GO_SJC_LSS_HYZ,FRA_LSS_HYZ,TJ_LSS_HYZ,RTS_GO_SJC_LSS,GO_SJC_LSS,scaling_zone_prod_us01_uswest2_realtime_verification,Unknown,scaling_zone_prod_go_oh1_web_verification"}]}, {"level": "FATAL", "needHits": 3, "hitCount": 3, "timeWindow": 180, "isContinuous": true, "conditions": [{"conditionType": "EXPRESSION", "expression": "(pull_count>0 && pull_count/pull_capacity > 0.9) || (push_count>0 && push_count/push_capacity > 0.9)"}, {"conditionType": "TAG", "name": "zone", "operator": "not-in", "threshold": "AMS_LSS_HYZ,SJC_LSS_HYZ,IAD_LSS_HYZ,GO_SJC_LSS_HYZ,FRA_LSS_HYZ,TJ_LSS_HYZ,RTS_GO_SJC_LSS,GO_SJC_LSS,scaling_zone_prod_us01_uswest2_realtime_verification,Unknown,scaling_zone_prod_go_oh1_web_verification"}]}], "timesInPeriod": 1, "periodInMinutes": 30, "enabled": true, "notifications": [{"channel": {"name": "RTM - LSS", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "LSS zone usage is high on ${Zone}", "content": "Time: ${alarmTime}\nZone: ${zone}\nPull Count: ${pull_count}\nPush Count: ${push_count}\nPull Usage Percent: ${pull_usage_percent}\nPush Usage Percent: ${push_usage_percent}", "whichLevels": "WARN,ERROR,FATAL", "repeatedSendAsyncmq": false, "isMarkdown": false, "mentions": "[]", "pdGroupTags": ""}, {"channel": {"name": "Realtime-LSS-p1", "engineName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isDefault": 0}, "title": "${Zone} usage is > 80%", "content": "Time: ${alarmTime}\nZone: ${zone}\nPull Count: ${pull_count}\nPush Count: ${push_count}\nPull Usage Percent: ${pull_usage_percent}\nPush Usage Percent: ${push_usage_percent}", "whichLevels": "ERROR", "repeatedSendAsyncmq": false, "isMarkdown": false, "mentions": "[]", "pdGroupTags": "Zone"}, {"channel": {"name": "Realtime-LSS-p0", "engineName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isDefault": 0}, "title": "${Zone} usage is > 90%", "content": "Time: ${alarmTime}\nZone: ${zone}\nPull Count: ${pull_count}\nPush Count: ${push_count}\nPull Usage Percent: ${pull_usage_percent}\nPush Usage Percent: ${push_usage_percent}", "whichLevels": "FATAL", "repeatedSendAsyncmq": false, "isMarkdown": false, "mentions": "[]", "pdGroupTags": "Zone"}], "alarmExtensionRelations": [], "description": "Alerts if usage percent of an LSS zone exceeds defined thresholds", "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "groupTags": "", "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "LSSZC_STATISTICS"}, {"name": "LSSZC-VIEWER-ERR", "rules": [{"level": "WARN", "needHits": 1, "hitCount": 1, "timeWindow": 60, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "count", "operator": "==", "threshold": "1"}]}], "timesInPeriod": 2, "periodInMinutes": 30, "enabled": true, "notifications": [{"channel": {"name": "LSSZC Alarm", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "LSSZC-VIEWER-ERR", "content": "Viewer ping ZC error\n${alarmTime}\nRegion: ${region}\nZone: ${zone}\nHostname: ${hostname}\nstream_id: ${stream_id}\nerror_code: ${error_code}\ndetails: https://cube.zoom.us/dashboards/ce61df70-49bb-45f7-8ca6-e2693fffa494?service=service_monitor&timeRange=%7B%22type%22%3A%22relative%22%2C%22value%22%3A%2221d%22%7D", "whichLevels": "WARN", "repeatedSendAsyncmq": false, "isMarkdown": true, "mentions": "[]", "pdGroupTags": ""}], "alarmExtensionRelations": [], "description": "Viewer ping ZC error", "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "groupTags": "", "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "LSSZC_VIEWER_ERR"}, {"name": "ZC_CSMS_CONN_FAILURE", "rules": [{"level": "ERROR", "needHits": 1, "hitCount": 3, "timeWindow": 180, "isContinuous": false, "conditions": [{"conditionType": "FIELD", "name": "csms_conn_failure", "operator": "!=", "threshold": "0"}, {"conditionType": "EXPRESSION", "expression": "string.contains(zone,'LTTZ') != true"}]}], "timesInPeriod": 1, "periodInMinutes": 3, "enabled": true, "notifications": [{"channel": {"name": "ZC alarm", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "zc_csms_run_failure", "content": "ZC CSMS RUN FAILURE - CAN'T RESTART THE ZC \nzone: ${zone}\ninstance id: ${instanceid}\nhostname: ${hostname}\ncsms result: ${csms_conn_failure}\n\nhttps://zoomvideo.atlassian.net/wiki/x/MIFPt", "whichLevels": "ERROR", "repeatedSendAsyncmq": false, "isMarkdown": false, "mentions": "[{\"displayName\":\"<PERSON><PERSON> Pal\",\"email\":\"<EMAIL>\"}]", "pdGroupTags": ""}, {"channel": {"name": "RTM - ZC", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "ZC_CSMS_CONN_FAILURE - Failure [check zc csms status]", "content": "ZC CSMS RUN FAILURE - CAN'T RESTART THE ZC \nzone: ${zone}\ninstance id: ${instanceid}\nhostname: ${hostname}\ncsms result: ${csms_conn_failure}\n\nhttps://zoomvideo.atlassian.net/wiki/x/MIFPt", "whichLevels": "ERROR", "repeatedSendAsyncmq": false, "isMarkdown": false, "mentions": "[]", "pdGroupTags": ""}, {"channel": {"name": "Pagerduty-ZC-p1", "engineName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isDefault": 0}, "title": "ZC_CSMS_CONN_FAILURE - Failure [check zc csms status]", "content": "ZC CSMS RUN FAILURE - CAN'T RESTART THE ZC \nzone: ${zone}\ninstance id: ${instanceid}\nhostname: ${hostname}\ncsms result: ${csms_conn_failure}\n\nhttps://zoomvideo.atlassian.net/wiki/x/MIFPt", "whichLevels": "ERROR", "repeatedSendAsyncmq": false, "isMarkdown": false, "mentions": "[]", "pdGroupTags": ""}], "alarmExtensionRelations": [], "description": "csms conn failure, can not start zc", "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "groupTags": "", "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "ZC_CSMS_CONN_FAILURE"}, {"name": "ZC_CSMS_CONN_FAILURE_2", "rules": [{"level": "ERROR", "needHits": 1, "hitCount": 1, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "csms_conn_failure", "operator": "!=", "threshold": "0"}]}], "timesInPeriod": 1, "periodInMinutes": 3, "enabled": false, "notifications": [{"channel": {"name": "ZC alarm", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "zc_csms_run_failure", "content": "zc csms run failure, can not start this zc.\nzone: ${zone}\ninstance id: ${instanceid}\nhostname: ${hostname}\ncsms result: ${csms_conn_failure}", "whichLevels": "ERROR", "repeatedSendAsyncmq": false, "isMarkdown": false, "mentions": "[]", "pdGroupTags": ""}], "alarmExtensionRelations": [], "description": "zc can not connect csms_zc start failure", "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "ZC_CSMS_CONN_FAILURE"}, {"name": "CMR_Recording_Traffic_Overload", "rules": [{"level": "WARN", "needHits": 1, "hitCount": 1, "timeWindow": 60, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "active_conf_percent", "operator": ">", "threshold": "80"}, {"conditionType": "FIELD", "name": "active_conf_percent", "operator": "<", "threshold": "100"}, {"conditionType": "TAG", "name": "zone", "operator": "not-in", "threshold": "US01_HKG_CMZHY01,US01_AMS_CMZHY01,US01_TJ_CMZHY01,US01_SIN_CMZHY01,US01_NRT_CMZHY01,GO_SJC_CMZHY01,US01_FRA_CMZHY01"}]}, {"level": "WARN", "needHits": 1, "hitCount": 1, "timeWindow": 60, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "active_server_percent", "operator": ">", "threshold": "0"}, {"conditionType": "FIELD", "name": "active_server_percent", "operator": "<", "threshold": "20"}, {"conditionType": "TAG", "name": "zone", "operator": "not-in", "threshold": "US01_HKG_CMZHY01,US01_AMS_CMZHY01,US01_TJ_CMZHY01,US01_SIN_CMZHY01,US01_NRT_CMZHY01,GO_SJC_CMZHY01,US01_FRA_CMZHY01"}]}, {"level": "ERROR", "needHits": 30, "hitCount": 1, "timeWindow": 1800, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "active_conf_percent", "operator": ">", "threshold": "95"}, {"conditionType": "FIELD", "name": "active_conf_percent", "operator": "<", "threshold": "100"}, {"conditionType": "TAG", "name": "zone", "operator": "not-in", "threshold": "US01_HKG_CMZHY01,US01_AMS_CMZHY01,US01_TJ_CMZHY01,US01_SIN_CMZHY01,US01_NRT_CMZHY01,GO_SJC_CMZHY01,US01_FRA_CMZHY01"}]}, {"level": "ERROR", "needHits": 30, "hitCount": 1, "timeWindow": 1800, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "active_server_percent", "operator": ">", "threshold": "0"}, {"conditionType": "FIELD", "name": "active_server_percent", "operator": "<", "threshold": "10"}, {"conditionType": "TAG", "name": "zone", "operator": "not-in", "threshold": "US01_HKG_CMZHY01,US01_AMS_CMZHY01,US01_TJ_CMZHY01,US01_SIN_CMZHY01,US01_NRT_CMZHY01,GO_SJC_CMZHY01,US01_FRA_CMZHY01"}]}], "timesInPeriod": 1, "periodInMinutes": 120, "enabled": true, "notifications": [{"channel": {"name": "CMR&CMA&TXTGW&Brahma engineer P0/P1", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "CMR_RECORDING_TRAFFIC_OVERLOAD", "content": "Zone: ${zone}| REGION: ${region} | Cluster:${cluster}\n--------Notes: active_server_percent should over 20%\nTOTAL_SERVER_COUNT:${total_server_count} | ACTIVE_SERVER_COUNT:${active_server_count} |  ACTIVE_SERVER_PERCENT:${active_server_percent}\n--------Notes: active_conf_percent    should less 80%\nACTIVE_CONF_COUNT:${active_conf_count} | ACTIVE_CONF_PERCENT:${active_conf_percent}", "whichLevels": "ERROR,FATAL", "repeatedSendAsyncmq": false, "isMarkdown": false, "mentions": "[]", "pdGroupTags": ""}, {"channel": {"name": "CMR&CMA&TXTGW&Brahma engineer P2/P3", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "CMR_RECORDING_TRAFFIC_OVERLOAD", "content": "Zone: ${zone}| REGION: ${region} | Cluster:${cluster}\n--------Notes: active_server_percent should over 20%\nTOTAL_SERVER_COUNT:${total_server_count} | ACTIVE_SERVER_COUNT:${active_server_count} |  ACTIVE_SERVER_PERCENT:${active_server_percent}\n--------Notes: active_conf_percent    should less 80%\nACTIVE_CONF_COUNT:${active_conf_count} | ACTIVE_CONF_PERCENT:${active_conf_percent}", "whichLevels": "WARN,INFO", "repeatedSendAsyncmq": false, "isMarkdown": false, "mentions": "[]", "pdGroupTags": ""}], "alarmExtensionRelations": [], "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "groupTags": "", "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "CMR_ZONE_LEVEL_GW_STATUS"}, {"name": "CMR_MRA_USAGE_ALARM", "rules": [{"level": "WARN", "needHits": 1, "hitCount": 1, "isContinuous": true, "conditions": [{"conditionType": "EXPRESSION", "expression": "(total_conf_count > 0) &&  active_conf_count/(active_server_count*30) > 0.70"}, {"conditionType": "TAG", "name": "zone", "operator": "not-in", "threshold": "US01_TJ_CMZHY01,US01_SJC_CMZHY01,US01_SIN_CMZHY01,US01_NRT_CMZHY01,US01_IAD_CMZHY01,US01_HKG_CMZHY01,US01_FRA_CMZHY01,US01_AMS_CMZHY01,SG01_SG_CMZHY01,RTS_GO_SJC_CMZHY01,GO_SJC_CMZHY01,EU01_FR_CMZHY01,DG01_AVA_CMZHY01,CA01_TR_CMZHY01,AU01_SY_CMZHY01,SA01_OJED_CMZHY01"}]}, {"level": "ERROR", "needHits": 1, "hitCount": 1, "isContinuous": true, "conditions": [{"conditionType": "EXPRESSION", "expression": "(total_conf_count > 0) &&  active_conf_count/(active_server_count*30) > 0.80"}, {"conditionType": "TAG", "name": "zone", "operator": "not-in", "threshold": "AOH_MRZ02,<PERSON><PERSON>_<PERSON>Z01,A<PERSON>_MRZ03,US01_TJ_CMZHY01,US01_SJC_CMZHY01,US01_SIN_CMZHY01,US01_NRT_CMZHY01,US01_IAD_CMZHY01,US01_HKG_CMZHY01,US01_FRA_CMZHY01,US01_AMS_CMZHY01,SG01_SG_CMZHY01,RTS_GO_SJC_CMZHY01,GO_SJC_CMZHY01,EU01_FR_CMZHY01,DG01_AVA_CMZHY01,CA01_TR_CMZHY01,AU01_SY_CMZHY01,SA01_OJED_CMZHY01"}]}, {"level": "ERROR", "needHits": 1, "hitCount": 1, "isContinuous": true, "conditions": [{"conditionType": "EXPRESSION", "expression": "(total_conf_count > 0) &&  active_conf_count/(active_server_count*30) > 0.85"}, {"conditionType": "TAG", "name": "zone", "operator": "in", "threshold": "AOH_MRZ02,AOH_MRZ01,AOH_MRZ03"}]}, {"level": "FATAL", "needHits": 1, "hitCount": 1, "isContinuous": true, "conditions": [{"conditionType": "EXPRESSION", "expression": "(total_conf_count > 0) &&  active_conf_count/(active_server_count*30) > 0.95"}, {"conditionType": "TAG", "name": "zone", "operator": "not-in", "threshold": "US01_TJ_CMZHY01,US01_SJC_CMZHY01,US01_SIN_CMZHY01,US01_NRT_CMZHY01,US01_IAD_CMZHY01,US01_HKG_CMZHY01,US01_FRA_CMZHY01,US01_AMS_CMZHY01,SG01_SG_CMZHY01,RTS_GO_SJC_CMZHY01,GO_SJC_CMZHY01,EU01_FR_CMZHY01,DG01_AVA_CMZHY01,CA01_TR_CMZHY01,AU01_SY_CMZHY01,SA01_OJED_CMZHY01"}]}], "timesInPeriod": 3, "periodInMinutes": 30, "enabled": true, "notifications": [{"channel": {"name": "RTM-CMR", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "MRA usage is high! ", "content": "Time: ${alarmTime}\nCurrent Active Recordings: ${active_conf_count}\nUsage Percentage: ${math.round((active_conf_count/(active_server_count*30)*100))}\nZone: ${zone}", "whichLevels": "INFO,ERROR,WARN,FATAL", "repeatedSendAsyncmq": false, "isMarkdown": false, "mentions": "[]", "pdGroupTags": ""}, {"channel": {"name": "Pagerduty-CMR-p1", "engineName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isDefault": 0}, "title": "MRA usage is high!", "content": "Time: ${alarmTime}\nCurrent Active Recordings: ${active_conf_count}\nUsage Percentage: ${math.round((active_conf_count/(active_server_count*30)*100))}\nZone: ${zone}", "whichLevels": "ERROR,FATAL", "repeatedSendAsyncmq": false, "isMarkdown": false, "mentions": "[]", "pdGroupTags": ""}], "alarmExtensionRelations": [], "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "CMR_ZONE_LEVEL_GW_STATUS"}, {"name": "REALTIME_CMR_SUSPENDED_CAPACITY", "rules": [{"level": "ERROR", "needHits": 1, "hitCount": 1, "timeWindow": 60, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "active_server_percent", "operator": "<", "threshold": "41"}, {"conditionType": "TAG", "name": "zone", "operator": "not-in", "threshold": "AU01_SY_CMZHY01,CA01_TR_CMZHY01,DG01_AVA_CMZHY01,EU01_FR_CMZHY01,GO_SJC_CMZHY01,RTS_GO_SJC_CMZHY01,SA01_OJED_CMZHY01,SA01_ORUH_CMZHY01,SG01_SG_CMZHY01,US01_AMS_CMZHY01,US01_HKG_CMZHY01,US01_IAD_CMZHY01,US01_NRT_CMZHY01,US01_SIN_CMZHY01,US01_SJC_CMZHY01,US01_TJ_CMZHY01,US01_FRA_CMZHY01"}]}], "timesInPeriod": 2, "periodInMinutes": 30, "enabled": true, "notifications": [{"channel": {"name": "RTM-CMR", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "Zone has abnormally high suspended host count!", "content": "ZONE: ${zone}\nTotal Hosts: ${total_server_count}\nUnsuspended Percentage: ${active_server_percent}\n\nWork to unsuspected immediately and investigate after why this happened. ", "whichLevels": "ERROR,FATAL", "repeatedSendAsyncmq": false, "isMarkdown": false, "mentions": "[]", "pdGroupTags": ""}, {"channel": {"name": "Pagerduty-CMR-p1", "engineName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isDefault": 0}, "title": "Zone has abnormally high suspended host count!", "content": "ZONE: ${zone}\nTotal Hosts: ${total_server_count}\nUnsuspended Percentage: ${active_server_percent}\n\nWork to unsuspected immediately and investigate after why this happened. ", "whichLevels": "ERROR,FATAL", "repeatedSendAsyncmq": false, "isMarkdown": false, "pdGroupTags": ""}], "alarmExtensionRelations": [], "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "groupTags": "", "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "CMR_ZONE_LEVEL_GW_STATUS"}, {"name": "ZC_WEB_LOST_ai", "rules": [{"level": "WARN", "needHits": 1, "hitCount": 1, "timeWindow": 60, "isContinuous": false, "conditions": [{"conditionType": "AI", "name": "connect.web.lost.count", "operator": "AI", "threshold": "3", "expression": "09be46e3-a67e-4f6a-9658-6d54f1cd15c9", "extension": "{\"detectMode\":\"Upper\"}"}, {"conditionType": "FIELD", "name": "connect_web_lost_count.sum", "operator": ">", "threshold": "10"}]}, {"level": "ERROR", "needHits": 1, "hitCount": 2, "timeWindow": 300, "isContinuous": true, "conditions": [{"conditionType": "AI", "name": "connect.web.lost.count", "operator": "AI", "threshold": "5", "expression": "09be46e3-a67e-4f6a-9658-6d54f1cd15c9", "extension": "{\"detectMode\":\"Upper\"}"}, {"conditionType": "FIELD", "name": "connect_web_lost_count.sum", "operator": ">", "threshold": "200"}]}, {"level": "FATAL", "needHits": 5, "hitCount": 6, "timeWindow": 360, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "connect_web_lost_count.sum", "operator": ">", "threshold": "2000"}]}], "timesInPeriod": 1, "periodInMinutes": 5, "enabled": true, "notifications": [{"channel": {"name": "ZC alarm", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "ZC_WEB_LOST", "content": "ZC cannot notify message to web.\nMMC: ${mmc}\nRegion: ${region}\nconnect.web.lost.count: ${connect_web_lost_count.sum}\ndetails: https://cube.zoom.us/dashboards/c1d5ea4f-d16e-4822-9c43-46e8be239330\nrunbook: https://dg01docs.zoom.us/doc/-f64NtOOQK65go9pQg_K2g", "whichLevels": "ERROR", "repeatedSendAsyncmq": false, "isMarkdown": false, "mentions": "[{\"displayName\":\"<PERSON>\",\"email\":\"<EMAIL>\"},{\"displayName\":\"<PERSON>\",\"email\":\"<EMAIL>\"}]", "pdGroupTags": ""}, {"channel": {"name": "RTM - ZC", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "ZC_WEB_LOST", "content": "ZC cannot notify message to web.\nMMC: ${mmc}\nRegion: ${region}\nconnect.web.lost.count: ${connect_web_lost_count.sum}\ndetails: https://cube.zoom.us/dashboards/c1d5ea4f-d16e-4822-9c43-46e8be239330", "whichLevels": "WARN,ERROR", "repeatedSendAsyncmq": false, "isMarkdown": false, "pdGroupTags": "", "routingRule": "", "runBook": "https://dg01docs.zoom.us/doc/-f64NtOOQK65go9pQg_K2g"}, {"channel": {"name": "Pagerduty-ZC-p1", "engineName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isDefault": 0}, "title": "ZC_WEB_LOST", "content": "MMC: ${mmc}\nRegion: ${region}\nconnect.web.lost.count: ${connect_web_lost_count.sum}\ndetails: https://cube.zoom.us/dashboards/c1d5ea4f-d16e-4822-9c43-46e8be239330", "whichLevels": "ERROR", "repeatedSendAsyncmq": false, "isMarkdown": false, "pdGroupTags": "", "routingRule": "connect.web.lost.count > 500", "runBook": "https://dg01docs.zoom.us/doc/-f64NtOOQK65go9pQg_K2g"}, {"channel": {"name": "Pagerduty-ZC-p0", "engineName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isDefault": 0}, "title": "ZC_WEB_LOST", "content": "MMC: ${mmc}\nRegion: ${region}\nconnect.web.lost.count: ${connect_web_lost_count.sum}\ndetails: https://cube.zoom.us/dashboards/c1d5ea4f-d16e-4822-9c43-46e8be239330", "whichLevels": "FATAL", "repeatedSendAsyncmq": false, "isMarkdown": false, "pdGroupTags": "", "routingRule": "connect.web.lost.count > 1000", "runBook": "https://dg01docs.zoom.us/doc/-f64NtOOQK65go9pQg_K2g"}, {"channel": {"name": "Pagerduty-Eng-ZC", "engineName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isDefault": 0}, "title": "ZC_WEB_LOST", "content": "MMC: ${mmc}\nRegion: ${region}\nconnect.web.lost.count: ${connect_web_lost_count.sum}\ndetails: https://cube.zoom.us/dashboards/c1d5ea4f-d16e-4822-9c43-46e8be239330", "whichLevels": "ERROR,FATAL", "repeatedSendAsyncmq": false, "isMarkdown": false, "pdGroupTags": "", "routingRule": "connect.web.lost.count > 800", "runBook": "https://dg01docs.zoom.us/doc/-f64NtOOQK65go9pQg_K2g"}], "alarmExtensionRelations": [], "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "groupTags": "", "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "ZC_WEB_LOST"}, {"name": "REALTIME_ZC_WEB_LOST", "rules": [{"level": "WARN", "needHits": 2, "hitCount": 2, "timeWindow": 120, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "connect_web_lost_count.sum", "operator": ">", "threshold": "100"}, {"conditionType": "TAG", "name": "region", "operator": "not-in", "threshold": "TJ"}]}, {"level": "WARN", "needHits": 2, "hitCount": 2, "timeWindow": 120, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "connect_web_lost_count.sum", "operator": ">", "threshold": "150"}, {"conditionType": "TAG", "name": "region", "operator": "in", "threshold": "TJ"}]}, {"level": "ERROR", "needHits": 10, "hitCount": 10, "timeWindow": 600, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "connect_web_lost_count.sum", "operator": ">", "threshold": "200"}, {"conditionType": "TAG", "name": "region", "operator": "not-in", "threshold": "TJ"}]}, {"level": "ERROR", "needHits": 10, "hitCount": 10, "timeWindow": 600, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "connect_web_lost_count.sum", "operator": ">", "threshold": "300"}, {"conditionType": "TAG", "name": "region", "operator": "not-in", "threshold": "TJ"}]}, {"level": "FATAL", "needHits": 10, "hitCount": 10, "timeWindow": 600, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "connect_web_lost_count.sum", "operator": ">", "threshold": "2000"}]}], "timesInPeriod": 1, "periodInMinutes": 15, "enabled": true, "notifications": [{"channel": {"name": "RTM - ZC", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "REALTIME_ZC_WEB_LOST | Connection Fail", "content": "Team: RealtimeServiceOps\nThe ZC has a failed connection with WEB for 5 minutes! Ping realtime oncall to investigate\nRegion:${region}\nLost count: ${connect_web_lost_count.sum}\ndetail: https://cube.zoom.us/dashboards/c1d5ea4f-d16e-4822-9c43-46e8be239330\nNote: MMZ (and thereby zone/ZC info) can be found on the bottom half of the above Details view. This is helpful for recording zones that share REGION. \n\nhttps://zoomvideo.atlassian.net/wiki/spaces/REAL/pages/2836169532/REALTIME+ZC+WEB+LOST+Connection+Fail\n\nLink for zc and web info : https://cube.zoom.us/dashboards/c1d5ea4f-d16e-4822-9c43-46e8be239330", "whichLevels": "ERROR,WARN", "repeatedSendAsyncmq": false, "isMarkdown": false, "pdGroupTags": ""}, {"channel": {"name": "Pagerduty-ZC-p1", "engineName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isDefault": 0}, "title": "REALTIME_ZC_WEB_LOST | Connection Fail", "content": "Team: RealtimeServiceOps\nThe ZC has a failed connection with WEB for 5 minutes! Ping realtime oncall to investigate\nLost count: ${connect_web_lost_count.sum}\nRegion: ${region}\ndetail: https://cube.zoom.us/dashboards/c1d5ea4f-d16e-4822-9c43-46e8be239330\nNote: MMZ (and thereby zone/ZC info) can be found on the bottom half of the above Details view. This is helpful for recording zones that share REGION. \n\nhttps://zoomvideo.atlassian.net/wiki/spaces/REAL/pages/2836169532/REALTIME+ZC+WEB+LOST+Connection+Fail", "whichLevels": "ERROR", "repeatedSendAsyncmq": false, "isMarkdown": false, "pdGroupTags": ""}, {"channel": {"name": "ZC alarm", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "REALTIME_ZC_WEB_LOST | Connection Fail", "content": "Team: RealtimeServiceOps\nThe ZC has a failed connection with WEB for 5 minutes! Ping realtime oncall to investigate\nLost count: ${connect_web_lost_count.sum}\nRegion: ${region}\n", "whichLevels": "WARN,ERROR", "repeatedSendAsyncmq": false, "isMarkdown": false, "pdGroupTags": ""}], "alarmExtensionRelations": [], "description": "@A<PERSON><PERSON>", "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "groupTags": "", "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "ZC_WEB_LOST"}, {"name": "TXTGW_TXTAA_USAGE_ALARM", "rules": [{"level": "WARN", "needHits": 1, "hitCount": 1, "timeWindow": 60, "isContinuous": true, "conditions": [{"conditionType": "EXPRESSION", "expression": "(total_conf_count > 0) && active_conf_count/(active_server_count*3000) > 0.70"}]}, {"level": "ERROR", "needHits": 1, "hitCount": 1, "timeWindow": 60, "isContinuous": true, "conditions": [{"conditionType": "EXPRESSION", "expression": "(total_conf_count > 0) && active_conf_count/(active_server_count*3000) > 0.80"}]}, {"level": "FATAL", "needHits": 3, "hitCount": 3, "timeWindow": 180, "isContinuous": true, "conditions": [{"conditionType": "EXPRESSION", "expression": "(total_conf_count > 0) && active_conf_count/(active_server_count*3000) > 0.95"}]}], "timesInPeriod": 3, "periodInMinutes": 30, "enabled": true, "notifications": [{"channel": {"name": "RTM-TXTGW", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "TXTA usage high!", "content": "Time: ${alarmTime}\nCurrent Active Recordings: ${active_conf_count}\nUsage Percentage: ${active_conf_count/(active_server_count*3000)*100}\nZone: ${zone}\n\n\nIf real, consider emergency TXTA expansion", "whichLevels": "INFO,WARN,ERROR,FATAL", "repeatedSendAsyncmq": false, "isMarkdown": false, "mentions": "[]", "pdGroupTags": ""}, {"channel": {"name": "Pagerduty-TXTGW-p1", "engineName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isDefault": 0}, "title": "TXTA usage high!", "content": "Time: ${alarmTime}\nCurrent Active Recordings: ${active_conf_count}\nUsage Percentage: ${active_conf_count/(active_server_count*3000)*100}\nZone: ${zone}\n\n\nIf real, consider emergency TXTA expansion", "whichLevels": "ERROR,FATAL", "repeatedSendAsyncmq": false, "isMarkdown": false, "mentions": "[]", "pdGroupTags": ""}], "alarmExtensionRelations": [], "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "groupTags": "", "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "TXTGW_ZONE_LEVEL_GW_STATUS"}, {"name": "REALTIME_TXTGW_SUSPENDED_CAPACITY", "rules": [{"level": "ERROR", "needHits": 1, "hitCount": 1, "timeWindow": 60, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "active_server_percent", "operator": "<", "threshold": "41"}]}], "timesInPeriod": 2, "periodInMinutes": 30, "enabled": true, "notifications": [{"channel": {"name": "RTM-TXTGW", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "Zone has abnormally high suspended host count!", "content": "ZONE: ${zone}\nTotal Hosts: ${total_server_count}\nUnsuspended Percentage: ${active_server_percent}\n\nWork to unsuspected immediately and investigate after why this happened. ", "whichLevels": "ERROR,FATAL", "repeatedSendAsyncmq": false, "isMarkdown": false, "mentions": "[]", "pdGroupTags": ""}, {"channel": {"name": "Pagerduty-TXTGW-p1", "engineName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isDefault": 0}, "title": "Zone has abnormally high suspended host count!", "content": "ZONE: ${zone}\nTotal Hosts: ${total_server_count}\nUnsuspended Percentage: ${active_server_percent}\n\nWork to unsuspected immediately and investigate after why this happened. ", "whichLevels": "ERROR", "repeatedSendAsyncmq": false, "isMarkdown": false, "pdGroupTags": ""}], "alarmExtensionRelations": [], "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "groupTags": "", "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "TXTGW_ZONE_LEVEL_GW_STATUS"}, {"name": "RTM_MMR_DISCONNECT_IMPACT", "rules": [{"level": "WARN", "needHits": 1, "hitCount": 1, "timeWindow": 60, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "client_count", "operator": ">", "threshold": "50"}]}], "timesInPeriod": 1, "periodInMinutes": 5, "enabled": true, "notifications": [{"channel": {"name": "RTM-MMR-SERVICE", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "MMR SHUTDOWN IMPACT - CLIENT COUNT > 50", "content": "reason: ${reason}\nconf_count: ${conf_count}\nclient_count: ${client_count}\nMMR_address: ${mmr_address}\nZC_address: ${hostname}\nZC_ip: ${instanceid}\nRegion: ${region}\nZone: ${zone}\n", "whichLevels": "WARN", "repeatedSendAsyncmq": false, "isMarkdown": false, "mentions": "[]", "pdGroupTags": ""}], "alarmExtensionRelations": [], "description": "Tracks impact when MMR process unexpectedly terminated or restarted due to maintenance events", "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "MMR_LOST"}, {"name": "MMR-LOST", "rules": [{"level": "WARN", "needHits": 1, "hitCount": 3, "timeWindow": 300, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "conf_count", "operator": ">", "threshold": "5"}, {"conditionType": "FIELD", "name": "client_count", "operator": ">", "threshold": "10"}]}, {"level": "ERROR", "needHits": 10, "hitCount": 10, "timeWindow": 300, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "conf_count", "operator": ">", "threshold": "20"}, {"conditionType": "FIELD", "name": "client_count", "operator": ">", "threshold": "40"}]}, {"level": "FATAL", "needHits": 40, "hitCount": 40, "timeWindow": 300, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "client_count", "operator": ">", "threshold": "100"}, {"conditionType": "FIELD", "name": "conf_count", "operator": ">", "threshold": "200"}]}], "timesInPeriod": 1, "periodInMinutes": 5, "enabled": true, "notifications": [{"channel": {"name": "RTM - ZC", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "MMR-LOST - 5min", "content": "reason: ${reason}\nconf_count: ${conf_count}\nclient_count: ${client_count}\nMMR_address: ${mmr_address}\nZC_address: ${hostname}\nZC_ip: ${instanceid}\nRegion: ${region}\nZone: ${zone}\n", "whichLevels": "ERROR,WARN,FATAL", "repeatedSendAsyncmq": false, "isMarkdown": false, "pdGroupTags": "", "runBook": "https://dg01docs.zoom.us/doc/bpYIN6nsSM6RhamJ_Ku5Sg"}, {"channel": {"name": "Pagerduty-ZC-p1", "engineName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isDefault": 0}, "title": "MMR-LOST - 5min", "content": "reason: ${reason}\nconf_count: ${conf_count}\nclient_count: ${client_count}\nMMR_address: ${mmr_address}\nZC_address: ${hostname}\nZC_ip: ${instanceid}\nRegion: ${region}\nZone: ${zone}", "whichLevels": "WARN,ERROR", "repeatedSendAsyncmq": false, "isMarkdown": false, "pdGroupTags": "", "runBook": "https://dg01docs.zoom.us/doc/bpYIN6nsSM6RhamJ_Ku5Sg"}], "alarmExtensionRelations": [], "levelsSendIncident": "", "alarmMatchMode": 0, "groupTags": "", "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "MMR_LOST"}, {"name": "ZC_MMR-LOST", "rules": [{"level": "INFO", "needHits": 10, "hitCount": 10, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "conf_count.sum", "operator": ">", "threshold": "10"}]}], "timesInPeriod": 1, "periodInMinutes": 30, "enabled": true, "notifications": [{"channel": {"name": "RTM - ZC", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "ZC_MMR-LOST - 30mins | 10 periods", "content": "This is a Info Message, not really an alert you act on.\nHostname: ${hostname}\nSUM conf_count: ${conf_count.sum}\nSUM client_count: ${client_count.sum}\nRegion: ${region}\nZone: ${zone}", "whichLevels": "INFO", "repeatedSendAsyncmq": false, "isMarkdown": false}], "alarmExtensionRelations": [], "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "ZC_MMR_LOST"}, {"name": "REALTIME_MMR_SUSPENDED_CAPACITY", "rules": [{"level": "ERROR", "needHits": 1, "hitCount": 1, "timeWindow": 60, "isContinuous": true, "conditions": [{"conditionType": "EXPRESSION", "expression": "zone_statistics_unsuspended_count/zone_statistics_mmr_count < 0.41"}, {"conditionType": "TAG", "name": "zone", "operator": "not-in", "threshold": "GO_RTS_SJC,SIN_HYZ2"}]}], "timesInPeriod": 2, "periodInMinutes": 30, "enabled": true, "notifications": [{"channel": {"name": "RTM - MMR", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "Zone has abnormally high suspended host count!", "content": "ZONE: ${zone}\nTotal Hosts: ${zone_statistics_mmr_count}\nUnsuspended Percentage: ${(zone.statistics.unsuspended.count/zone.statistics.mmr.count)*100}\n\nWork to unsuspected immediately and investigate after why this happened. ", "whichLevels": "ERROR,FATAL", "repeatedSendAsyncmq": false, "isMarkdown": false, "pdGroupTags": ""}, {"channel": {"name": "Pagerduty-MMZ-p1", "engineName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isDefault": 0}, "title": "Zone has abnormally high suspended host count!", "content": "ZONE: ${zone}\nTotal Hosts: ${zone_statistics_mmr_count}\nUnsuspended Percentage: ${(zone.statistics.unsuspended.count/zone.statistics.mmr.count)*100}\n\nWork to unsuspected immediately and investigate after why this happened. ", "whichLevels": "ERROR,FATAL", "repeatedSendAsyncmq": false, "isMarkdown": false, "pdGroupTags": ""}], "alarmExtensionRelations": [], "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "groupTags": "", "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "ZC_STATISTICS"}, {"name": "MMZ_ZONE_UTILIZED_CAPACITY", "rules": [{"level": "ERROR", "needHits": 3, "hitCount": 3, "timeWindow": 180, "isContinuous": true, "conditions": [{"conditionType": "EXPRESSION", "expression": "zone_statistics_participant_count / (zone_statistics_capacity_availiable + 0.01) >= 0.9"}, {"conditionType": "FIELD", "name": "zone_statistics_participant_count", "operator": ">", "threshold": "0"}, {"conditionType": "TAG", "name": "zone", "operator": "not-in", "threshold": "HYZ"}]}, {"level": "WARN", "needHits": 3, "hitCount": 3, "timeWindow": 180, "isContinuous": true, "conditions": [{"conditionType": "EXPRESSION", "expression": "zone_statistics_participant_count / (zone_statistics_capacity_availiable + 0.01) >= 0.8"}, {"conditionType": "FIELD", "name": "zone_statistics_participant_count", "operator": ">", "threshold": "0"}, {"conditionType": "TAG", "name": "zone", "operator": "not-in", "threshold": "HYZ"}]}, {"level": "INFO", "needHits": 3, "hitCount": 3, "timeWindow": 180, "isContinuous": true, "conditions": [{"conditionType": "EXPRESSION", "expression": "zone_statistics_participant_count / (zone_statistics_capacity_availiable + 0.01) >= 0.7"}, {"conditionType": "FIELD", "name": "zone_statistics_participant_count", "operator": ">", "threshold": "0"}]}], "timesInPeriod": 1, "periodInMinutes": 180, "enabled": true, "notifications": [{"channel": {"name": "RTM-MMR-SERVICE", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "MMZ_ZONE_UTILIZED_CAPACITY > 70", "content": "Participants: ${zone_statistics_participant_count}\nAvailable Capacity: ${zone_statistics_capacity_availiable}\nRegion: ${region}\nZone: ${zone}\n\nZone Utilization is at ${zone.statistics.participant.count / zone.statistics.capacity.availiable} of total unsuspended capacity", "whichLevels": "INFO", "repeatedSendAsyncmq": false, "isMarkdown": false, "mentions": "[]", "pdGroupTags": ""}, {"channel": {"name": "RTM-MMR-SERVICE", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "MMZ_ZONE_UTILIZED_CAPACITY > 80", "content": "Participants: ${zone_statistics_participant_count}\nAvailable Capacity: ${zone_statistics_capacity_availiable}\nRegion: ${region}\nZone: ${zone}\n\nZone Utilization is at ${zone.statistics.participant.count / zone.statistics.capacity.availiable} of total unsuspended capacity", "whichLevels": "ERROR,WARN", "repeatedSendAsyncmq": false, "isMarkdown": false, "mentions": "[]", "pdGroupTags": ""}, {"channel": {"name": "RTM-MMR-SERVICE", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "MMZ_ZONE_UTILIZED_CAPACITY > 90", "content": "Participants: ${zone_statistics_participant_count}\nAvailable Capacity: ${zone_statistics_capacity_availiable}\nRegion: ${region}\nZone: ${zone}\n\nZone Utilization is ${zone.statistics.participant.count / zone.statistics.capacity.availiable} of total unsuspended capacity", "whichLevels": "ERROR", "repeatedSendAsyncmq": false, "isMarkdown": false, "mentions": "[]", "pdGroupTags": ""}, {"channel": {"name": "Pagerduty-MMZ-p1", "engineName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isDefault": 0}, "title": "MMZ_ZONE_UTILIZED_CAPACITY > 90", "content": "Participants: ${zone_statistics_participant_count}\nAvailable Capacity: ${zone_statistics_capacity_availiable}\nRegion: ${region}\nZone: ${zone}\n\nZone Utilization is ${zone.statistics.participant.count / zone.statistics.capacity.availiable} of total unsuspended capacity", "whichLevels": "ERROR", "repeatedSendAsyncmq": false, "isMarkdown": false, "mentions": "[]", "pdGroupTags": "Zone"}], "alarmExtensionRelations": [], "description": "utilized unsuspended capacity of a MMZ zone", "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "groupTags": "", "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "ZC_STATISTICS"}, {"name": "CMA_MAA_USAGE_ALARM", "rules": [{"level": "ERROR", "needHits": 1, "hitCount": 1, "timeWindow": 60, "isContinuous": true, "conditions": [{"conditionType": "EXPRESSION", "expression": "(total_conf_count > 0) &&  active_conf_count/(active_server_count*30) > 0.70"}]}, {"level": "ERROR", "needHits": 1, "hitCount": 1, "timeWindow": 60, "isContinuous": true, "conditions": [{"conditionType": "EXPRESSION", "expression": "(total_conf_count > 0) &&  active_conf_count/(active_server_count*30) > 0.80"}]}, {"level": "FATAL", "needHits": 2, "hitCount": 2, "timeWindow": 120, "isContinuous": true, "conditions": [{"conditionType": "EXPRESSION", "expression": "(total_conf_count > 0) &&  active_conf_count/(active_server_count*30) > 0.90"}]}], "timesInPeriod": 3, "periodInMinutes": 30, "enabled": true, "notifications": [{"channel": {"name": "RTM-CMA", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "MAA usage is high!", "content": "Time: ${alarmTime}\nCurrent Active Recordings: ${active_conf_count}\nUsage Percentage: ${math.round(active_conf_count/(active_server_count*30)*100)}\nZone: ${zone}\n\nIf real, consider emergency MAA expansion", "whichLevels": "WARN,ERROR,FATAL", "repeatedSendAsyncmq": false, "isMarkdown": false, "pdGroupTags": ""}, {"channel": {"name": "Pagerduty-CMA-p1", "engineName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isDefault": 0}, "title": "MAA usage is high!", "content": "Time: ${alarmTime}\nCurrent Active Recordings: ${active_conf_count}\nUsage Percentage: ${math.round(active_conf_count/(active_server_count*30)*100)}\nZone: ${zone}\n\n\nIf real, consider emergency MAA expansion", "whichLevels": "ERROR,FATAL", "repeatedSendAsyncmq": false, "isMarkdown": false, "pdGroupTags": ""}], "alarmExtensionRelations": [], "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "groupTags": "", "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "CMA_ZONE_LEVEL_GW_STATUS"}, {"name": "REALTIME_CMA_SUSPENDED_CAPACITY", "rules": [{"level": "ERROR", "needHits": 1, "hitCount": 1, "timeWindow": 60, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "active_server_percent", "operator": "<", "threshold": "41"}]}], "timesInPeriod": 2, "periodInMinutes": 30, "enabled": true, "notifications": [{"channel": {"name": "RTM-CMA", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "Zone has abnormally high suspended host count!", "content": "ZONE: ${zone}\nTotal Hosts: ${total_server_count}\nUnsuspended Percentage: ${active_server_percent}\n\nWork to unsuspected immediately and investigate after why this happened. ", "whichLevels": "ERROR,FATAL", "repeatedSendAsyncmq": false, "isMarkdown": false, "mentions": "[]", "pdGroupTags": ""}, {"channel": {"name": "Pagerduty-CMA-p1", "engineName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isDefault": 0}, "title": "Zone has abnormally high suspended host count!", "content": "ZONE: ${zone}\nTotal Hosts: ${total_server_count}\nUnsuspended Percentage: ${active_server_percent}\n\nWork to unsuspected immediately and investigate after why this happened. ", "whichLevels": "ERROR", "repeatedSendAsyncmq": false, "isMarkdown": false, "pdGroupTags": ""}], "alarmExtensionRelations": [], "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "groupTags": "", "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "CMA_ZONE_LEVEL_GW_STATUS"}, {"name": "CMA_Recording_Traffic_Overload", "rules": [{"level": "WARN", "needHits": 1, "hitCount": 1, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "active_conf_percent", "operator": ">", "threshold": "80"}, {"conditionType": "FIELD", "name": "active_conf_percent", "operator": "<", "threshold": "100"}]}, {"level": "WARN", "needHits": 1, "hitCount": 1, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "active_server_percent", "operator": ">", "threshold": "0"}, {"conditionType": "FIELD", "name": "active_server_percent", "operator": "<", "threshold": "20"}]}, {"level": "ERROR", "needHits": 1, "hitCount": 1, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "active_conf_percent", "operator": ">=", "threshold": "85"}, {"conditionType": "FIELD", "name": "active_conf_percent", "operator": "<", "threshold": "100"}]}, {"level": "ERROR", "needHits": 1, "hitCount": 1, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "active_server_percent", "operator": ">", "threshold": "0"}, {"conditionType": "FIELD", "name": "active_server_percent", "operator": "<", "threshold": "10"}]}], "timesInPeriod": 1, "periodInMinutes": 60, "enabled": true, "notifications": [{"channel": {"name": "CMR&CMA&TXTGW&Brahma engineer P2/P3", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "CMA_Recording_Traffic_Overload", "content": "Zone: ${zone}| REGION: ${region} | Cluster:${cluster}\n\n---------Notes: active_server_percent should over 20%\nactive_server_count:${active_server_count} | total_server_count:${total_server_count} | active_server_percent:${active_server_percent}\n---------Notes: active_conf_percent    should less 80%\nactive_conf_count:${active_conf_count} | active_server_count: ${active_server_count} | active_conf_percent:${active_conf_percent}", "whichLevels": "WARN,ERROR", "repeatedSendAsyncmq": false, "isMarkdown": false, "mentions": "[]", "pdGroupTags": ""}], "alarmExtensionRelations": [], "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "CMA_ZONE_LEVEL_GW_STATUS"}, {"name": "STREAM-ERR", "rules": [{"level": "WARN", "needHits": 1, "hitCount": 1, "timeWindow": 60, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "count", "operator": "==", "threshold": "1"}]}, {"level": "ERROR", "needHits": 3, "hitCount": 3, "timeWindow": 180, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "count", "operator": "==", "threshold": "1"}]}], "timesInPeriod": 1, "periodInMinutes": 5, "enabled": true, "notifications": [{"channel": {"name": "LSSZC Alarm", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "STREAM-ERROR", "content": "LSS report stream error\n${alarmTime}\nRegion: ${region}\nZone: ${zone}\nHostname: ${hostname}\nstream_id: ${stream_id}\nerror_code: ${err_code}", "whichLevels": "WARN", "repeatedSendAsyncmq": false, "isMarkdown": false, "pdGroupTags": ""}], "alarmExtensionRelations": [], "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "groupTags": "", "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "LSSZC_STREAM_ERR"}, {"name": "zc_ocsp_visit_failure", "rules": [{"level": "WARN", "needHits": 1, "hitCount": 1, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "fingerprint", "operator": "not-in", "threshold": "1"}]}, {"level": "ERROR", "needHits": 5, "hitCount": 5, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "fingerprint", "operator": "not-in", "threshold": "1"}]}], "timesInPeriod": 1, "periodInMinutes": 5, "enabled": true, "notifications": [{"channel": {"name": "ZC alarm", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "oscp_call_failure", "content": "fingerprint : ${fingerprint}\nregion : ${region}\nzone : ${zone}\ninstanceid : ${instanceid}", "whichLevels": "WARN", "repeatedSendAsyncmq": false, "isMarkdown": false, "pdGroupTags": ""}, {"channel": {"name": "ZC alarm", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "oscp_call_failure", "content": "fingerprint : ${fingerprint}\nregion : ${region}\nzone : ${zone}\ninstanceid : ${instanceid}", "whichLevels": "ERROR", "repeatedSendAsyncmq": false, "isMarkdown": false, "mentions": "[{\"displayName\":\"<PERSON>\",\"email\":\"<EMAIL>\"}]", "pdGroupTags": ""}], "alarmExtensionRelations": [], "description": "call ocsp failure", "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "ocsp_curl_retry_failure"}, {"name": "LSSZC-FULL", "rules": [{"level": "ERROR", "needHits": 1, "hitCount": 1, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "count", "operator": ">=", "threshold": "1"}, {"conditionType": "FIELD", "name": "pull_capacity", "operator": "!=", "threshold": "0"}, {"conditionType": "FIELD", "name": "push_capacity", "operator": "!=", "threshold": "0"}]}], "timesInPeriod": 3, "periodInMinutes": 30, "enabled": true, "notifications": [{"channel": {"name": "LSSZC Alarm", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "LSSZC  FULL", "content": "region: ${region}\nzone: ${zone}\nhostname: ${hostname}\npull_count: ${pull_count}\npush_count: ${push_count}\npull_capacity: ${pull_capacity}\npush_capacity: ${push_capacity}\n", "whichLevels": "ERROR", "repeatedSendAsyncmq": false, "isMarkdown": false, "mentions": "[]", "pdGroupTags": ""}], "alarmExtensionRelations": [], "description": "LSSZC FULL", "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "LSSZC_FULL"}, {"name": "Register-LSS-RSGW-FAILURE", "rules": [{"level": "ERROR", "needHits": 1, "hitCount": 1, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "addr", "operator": "not-in", "threshold": "\"\""}]}], "timesInPeriod": 1, "periodInMinutes": 5, "enabled": true, "notifications": [{"channel": {"name": "ZC alarm", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "lss-rsgw register-failure", "content": "${alarmTime}\naddr:${addr}\nZone:${Zone}\ninstance:${InstanceId}", "whichLevels": "ERROR", "repeatedSendAsyncmq": false, "isMarkdown": false, "mentions": "[{\"displayName\":\"<PERSON>\",\"email\":\"<EMAIL>\"},{\"displayName\":\"<PERSON>\",\"email\":\"<EMAIL>\"}]", "pdGroupTags": ""}, {"channel": {"name": "Pagerduty-Eng-ZC", "engineName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isDefault": 0}, "title": "lss-rsgw-register-failure", "content": "${alarmTime}\naddr:${addr}\nZone:${Zone}\ninstance:${InstanceId}", "whichLevels": "ERROR", "repeatedSendAsyncmq": false, "isMarkdown": false, "pdGroupTags": ""}], "alarmExtensionRelations": [], "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "LssRegisterFailure"}, {"name": "Cloud-Zone-Load-Full", "rules": [{"level": "WARN", "needHits": 1, "hitCount": 1, "timeWindow": 60, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "error_code_cnt.count", "operator": ">=", "threshold": "1"}, {"conditionType": "EXPRESSION", "expression": "string.contains(zone, \"HYZ\") == false"}, {"conditionType": "TAG", "name": "error_code", "operator": "in", "threshold": "102005,102020"}, {"conditionType": "TAG", "name": "zone", "operator": "not-in", "threshold": "GO_IAD_MMZ1"}]}, {"level": "ERROR", "needHits": 1, "hitCount": 1, "timeWindow": 60, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "error_code_cnt.count", "operator": ">=", "threshold": "10"}, {"conditionType": "EXPRESSION", "expression": "string.contains(zone, \"HYZ\") == false"}, {"conditionType": "TAG", "name": "error_code", "operator": "in", "threshold": "102005,102020"}, {"conditionType": "TAG", "name": "zone", "operator": "not-in", "threshold": "GO_IAD_MMZ1"}]}, {"level": "FATAL", "needHits": 2, "hitCount": 2, "timeWindow": 120, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "error_code_cnt.count", "operator": ">=", "threshold": "2000"}, {"conditionType": "TAG", "name": "error_code", "operator": "in", "threshold": "102005,102020"}, {"conditionType": "EXPRESSION", "expression": "string.contains(zone, \"HYZ\") == false"}, {"conditionType": "EXPRESSION", "expression": "string.contains(zone, \"GO\") == false"}]}], "timesInPeriod": 1, "periodInMinutes": 5, "enabled": true, "notifications": [{"channel": {"name": "RTM - ZC", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "Cloud-Zone-Load-Full", "content": "time: ${alarmTime}\nZone: ${zone}\nerror_cnt: ${error_code_cnt.count}\ndetails: https://cube.zoom.us/dashboards/f1c7f86b-0e5a-4828-857b-d44deed5de37\nrunbook: https://docs.zoom.us/doc/SlwFRmB7TM--mzTmZYDtsg", "whichLevels": "WARN,ERROR,FATAL", "repeatedSendAsyncmq": false, "isMarkdown": false, "mentions": "[]", "pdGroupTags": ""}, {"channel": {"name": "Pagerduty-MMZ-p0", "engineName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isDefault": 0}, "title": "Cloud Zone Load Full", "content": "time: ${alarmTime}\nZone: ${zone}\nerror_cnt: ${error_code_cnt.count}\ndetails: https://cube.zoom.us/dashboards/f1c7f86b-0e5a-4828-857b-d44deed5de37\nrunbook: https://docs.zoom.us/doc/SlwFRmB7TM--mzTmZYDtsg", "whichLevels": "FATAL", "repeatedSendAsyncmq": false, "isMarkdown": false, "mentions": "[]", "pdGroupTags": "Zone"}, {"channel": {"name": "Pagerduty-ZC-p1", "engineName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isDefault": 0}, "title": "Cloud Zone Load Full", "content": "time: ${alarmTime}\nZone: ${zone}\nerror_cnt: ${error_code_cnt.count}\ndetails: https://cube.zoom.us/dashboards/f1c7f86b-0e5a-4828-857b-d44deed5de37\nrunbook: https://docs.zoom.us/doc/SlwFRmB7TM--mzTmZYDtsg", "whichLevels": "ERROR", "repeatedSendAsyncmq": false, "isMarkdown": false, "mentions": "[]", "pdGroupTags": "Zone"}, {"channel": {"name": "Pagerduty-Eng-ZC", "engineName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isDefault": 0}, "title": "Cloud Zone Load Full", "content": "time: ${alarmTime}\nZone: ${zone}\nerror_cnt: ${error_code_cnt.count}\ndetails: https://cube.zoom.us/dashboards/f1c7f86b-0e5a-4828-857b-d44deed5de37\nrunbook: https://docs.zoom.us/doc/SlwFRmB7TM--mzTmZYDtsg", "whichLevels": "FATAL", "repeatedSendAsyncmq": false, "isMarkdown": false, "pdGroupTags": "Zone"}], "alarmExtensionRelations": [], "description": "102005 and 102020 trigger alert", "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "Resp_client_err_details"}, {"name": "HYBRID_LOAD_FULL", "rules": [{"level": "WARN", "needHits": 2, "hitCount": 1, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "error_code_cnt.count", "operator": ">=", "threshold": "1"}, {"conditionType": "TAG", "name": "error_code", "operator": "in", "threshold": "102005,102020"}, {"conditionType": "EXPRESSION", "expression": "string.contains(zone, \"HYZ\") == true"}, {"conditionType": "EXPRESSION", "expression": "string.contains(zone, \"GO\") == false"}, {"conditionType": "EXPRESSION", "expression": "string.contains(zone, \"DG\") == false"}]}, {"level": "ERROR", "needHits": 2, "hitCount": 2, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "error_code_cnt.count", "operator": ">=", "threshold": "10"}, {"conditionType": "TAG", "name": "error_code", "operator": "in", "threshold": "102005,102020"}, {"conditionType": "EXPRESSION", "expression": "string.contains(zone, \"HYZ\") == true"}, {"conditionType": "EXPRESSION", "expression": "string.contains(zone, \"GO\") == false"}, {"conditionType": "EXPRESSION", "expression": "string.contains(zone, \"DG\") == false"}]}], "timesInPeriod": 1, "periodInMinutes": 5, "enabled": true, "notifications": [{"channel": {"name": "Server Error Code Alert for Hybrid", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "Hybrid load full", "content": "time: ${alarmTime}\nZone: ${zone}\nerror_code : ${error_code}", "whichLevels": "ERROR,WARN", "repeatedSendAsyncmq": false, "isMarkdown": false, "mentions": "[{\"displayName\":\"<PERSON>\",\"email\":\"<EMAIL>\"},{\"displayName\":\"<PERSON>\",\"email\":\"<EMAIL>\"}]", "pdGroupTags": ""}], "alarmExtensionRelations": [], "description": "hybrid load full", "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "Resp_client_err_details"}, {"name": "ZC-CURL-FAIL", "rules": [{"level": "WARN", "needHits": 1, "hitCount": 1, "timeWindow": 60, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "result", "operator": "!=", "threshold": "0"}]}], "timesInPeriod": 2, "periodInMinutes": 30, "enabled": true, "notifications": [{"channel": {"name": "LSSZC Alarm", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "LSSZC curl fail", "content": "CURL FAIL\n${alarmTime}\n${alarmName}\n${alarmMetricName}\n${alarmLevel}\ncurl result: ${result}\ncurl url: ${url}\nRegion: ${region}\nZone: ${zone}\nHostname: ${hostname}", "whichLevels": "WARN", "repeatedSendAsyncmq": false, "isMarkdown": false, "pdGroupTags": ""}], "alarmExtensionRelations": [], "description": "zc curl fail", "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "ZC_CURL_FAIL"}, {"name": "ZC_WEB_LAZY_REGION_ai", "rules": [{"level": "WARN", "needHits": 1, "hitCount": 1, "timeWindow": 60, "isContinuous": false, "conditions": [{"conditionType": "AI", "name": "ZC_WEB_LAZY", "operator": "AI", "threshold": "3", "expression": "b5affbfd-1865-4e5f-a8ba-a321f55f1c7a", "extension": "{\"detectMode\":\"Upper\"}"}]}, {"level": "ERROR", "needHits": 1, "hitCount": 2, "timeWindow": 300, "isContinuous": false, "conditions": [{"conditionType": "AI", "name": "ZC_WEB_LAZY", "operator": "AI", "threshold": "5", "expression": "b5affbfd-1865-4e5f-a8ba-a321f55f1c7a", "extension": "{\"detectMode\":\"Upper\"}"}, {"conditionType": "FIELD", "name": "zc_web_lazy.count", "operator": ">=", "threshold": "200"}]}, {"level": "FATAL", "needHits": 5, "hitCount": 5, "timeWindow": 600, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "zc_web_lazy.count", "operator": ">=", "threshold": "2000"}]}], "timesInPeriod": 1, "periodInMinutes": 5, "enabled": true, "notifications": [{"channel": {"name": "ZC alarm", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "ZC_WEB_LAZY", "content": "${mmc}\n${region}\n${zc_web_lazy.count}\ndashboard: https://cube.zoom.us/dashboards/c1d5ea4f-d16e-4822-9c43-46e8be239330\nrunbook: https://cube.zoom.us/dashboards/c1d5ea4f-d16e-4822-9c43-46e8be239330\n", "whichLevels": "WARN,ERROR", "repeatedSendAsyncmq": false, "isMarkdown": false, "pdGroupTags": "", "runBook": "https://cube.zoom.us/dashboards/c1d5ea4f-d16e-4822-9c43-46e8be239330"}, {"channel": {"name": "RTM - ZC", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "ZC_WEB_LAZY", "content": "${mmc}\n${region}\n${zc_web_lazy.count}\ndashboard: https://cube.zoom.us/dashboards/c1d5ea4f-d16e-4822-9c43-46e8be239330\n", "whichLevels": "WARN,ERROR", "repeatedSendAsyncmq": false, "isMarkdown": false, "pdGroupTags": "", "runBook": "https://cube.zoom.us/dashboards/c1d5ea4f-d16e-4822-9c43-46e8be239330"}, {"channel": {"name": "Pagerduty-ZC-p0", "engineName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isDefault": 0}, "title": "ZC_WEB_LAZY", "content": "${mmc}\n${region}\n${zc_web_lazy.count}\ndashboard: https://cube.zoom.us/dashboards/c1d5ea4f-d16e-4822-9c43-46e8be239330\nrunbook: https://cube.zoom.us/dashboards/c1d5ea4f-d16e-4822-9c43-46e8be239330", "whichLevels": "FATAL", "repeatedSendAsyncmq": false, "isMarkdown": false, "pdGroupTags": "", "runBook": "https://cube.zoom.us/dashboards/c1d5ea4f-d16e-4822-9c43-46e8be239330"}], "alarmExtensionRelations": [], "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "groupTags": "", "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "ZC_WEB_LAZY_REGION"}, {"name": "ZC-WEB-LAZY-ALERT", "rules": [{"level": "WARN", "needHits": 1, "hitCount": 1, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "zc_web_lazy.count", "operator": ">=", "threshold": "100"}]}, {"level": "ERROR", "needHits": 3, "hitCount": 3, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "zc_web_lazy.count", "operator": ">=", "threshold": "100"}]}], "timesInPeriod": 1, "periodInMinutes": 5, "enabled": false, "notifications": [{"channel": {"name": "ZC alarm", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "ZC-WEB-LAZY-COUNT-ALERT", "content": "count : ${zc_web_lazy.count}\ndetail : https://cube.zoom.us/dashboards/c1d5ea4f-d16e-4822-9c43-46e8be239330\nRegion: ${region}", "whichLevels": "ERROR,WARN", "repeatedSendAsyncmq": false, "isMarkdown": false, "pdGroupTags": ""}], "alarmExtensionRelations": [], "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "ZC_WEB_LAZY_REGION"}, {"name": "usage-zone-level-alert", "rules": [{"level": "WARN", "needHits": 1, "hitCount": 1, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "used_percent.max", "operator": ">=", "threshold": "0.7"}, {"conditionType": "EXPRESSION", "expression": "string.contains(zone, \"HYZ\") == false"}]}, {"level": "ERROR", "needHits": 1, "hitCount": 1, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "used_percent.max", "operator": ">=", "threshold": "0.85"}, {"conditionType": "EXPRESSION", "expression": "string.contains(zone, \"HYZ\") == false"}]}], "timesInPeriod": 1, "periodInMinutes": 5, "enabled": true, "notifications": [{"channel": {"name": "RTM - ZC", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "ZC usage alert", "content": "zone: ${zone}\nclients: ${clients.max}\nusage: ${used_percent.max}\navailable capcity:  ${available_capacity.max}\ntotal capacity:  ${total_capacity.max}", "whichLevels": "WARN,ERROR", "repeatedSendAsyncmq": false, "isMarkdown": false, "mentions": "[]", "pdGroupTags": ""}], "alarmExtensionRelations": [], "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "ZONE_STATISTICS"}, {"name": "ZC_ERROR_RATE", "rules": [{"level": "WARN", "needHits": 1, "hitCount": 1, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "err_req_rate.max", "operator": ">=", "threshold": "0.2"}, {"conditionType": "FIELD", "name": "pingcount.max", "operator": ">=", "threshold": "30"}]}], "timesInPeriod": 2, "periodInMinutes": 30, "enabled": true, "notifications": [{"channel": {"name": "ZC alarm", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "Zone Error Rate Alert", "content": "failPingCount: ${failpingcount.max}\npingCount: ${pingcount.max}\nerrorRate: ${err_req_rate.max}\nzone: ${zone}\nqueryDashboard: https://cube.zoom.us/dashboards/f1c7f86b-0e5a-4828-857b-d44deed5de37", "whichLevels": "WARN,ERROR", "repeatedSendAsyncmq": false, "isMarkdown": false, "mentions": "[]", "pdGroupTags": ""}], "alarmExtensionRelations": [], "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "ZONE_STATISTICS"}, {"name": "capacity-alert-zone-level", "rules": [{"level": "WARN", "needHits": 1, "hitCount": 1, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "available_capacity.max", "operator": "<=", "threshold": "10000"}, {"conditionType": "EXPRESSION", "expression": "string.contains(zone, \"IAD\") == false && string.contains(zone, \"SJC\")==false "}, {"conditionType": "EXPRESSION", "expression": "string.contains(zone, \"MMZ\") == true"}, {"conditionType": "EXPRESSION", "expression": "string.contains(zone, \"GO\") == false && string.contains(zone, \"HYZ\") == false && string.contains(zone, \"DG01\") ==false"}, {"conditionType": "EXPRESSION", "expression": "string.contains(zone, \"TJ_MMZ6\") == false"}]}, {"level": "WARN", "needHits": 1, "hitCount": 1, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "available_capacity.max", "operator": "<=", "threshold": "30000"}, {"conditionType": "EXPRESSION", "expression": "string.contains(zone, \"IAD\") == true || string.contains(zone, \"SJC\") == true"}, {"conditionType": "EXPRESSION", "expression": "string.contains(zone, \"MMZ\") == true"}, {"conditionType": "EXPRESSION", "expression": "string.contains(zone, \"GO\") == false && string.contains(zone, \"HYZ\") == false && string.contains(zone, \"DG01\") ==false"}]}, {"level": "WARN", "needHits": 1, "hitCount": 1, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "available_capacity.max", "operator": "<=", "threshold": "200"}, {"conditionType": "EXPRESSION", "expression": "string.contains(zone, \"GO\") == true || string.contains(zone, \"DG01\") == true"}, {"conditionType": "EXPRESSION", "expression": "string.contains(zone, \"MMZ\") == true"}]}, {"level": "ERROR", "needHits": 1, "hitCount": 1, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "available_capacity.max", "operator": "<=", "threshold": "3000"}, {"conditionType": "EXPRESSION", "expression": "string.contains(zone, \"IAD\") == false && string.contains(zone, \"SJC\")==false "}, {"conditionType": "EXPRESSION", "expression": "string.contains(zone, \"MMZ\") == true"}, {"conditionType": "EXPRESSION", "expression": "string.contains(zone, \"GO\") == false && string.contains(zone, \"HYZ\") == false && string.contains(zone, \"DG01\") ==false"}]}, {"level": "ERROR", "needHits": 1, "hitCount": 1, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "available_capacity.max", "operator": "<=", "threshold": "10000"}, {"conditionType": "EXPRESSION", "expression": "string.contains(zone, \"IAD\") == true || string.contains(zone, \"SJC\") == true"}, {"conditionType": "EXPRESSION", "expression": "string.contains(zone, \"MMZ\") == true"}, {"conditionType": "EXPRESSION", "expression": "string.contains(zone, \"GO\") == false && string.contains(zone, \"HYZ\") == false && string.contains(zone, \"DG01\") ==false"}]}, {"level": "ERROR", "needHits": 1, "hitCount": 1, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "available_capacity.max", "operator": "<=", "threshold": "50"}, {"conditionType": "EXPRESSION", "expression": "string.contains(zone, \"GO\") == true || string.contains(zone, \"DG01\") == true"}, {"conditionType": "EXPRESSION", "expression": "string.contains(zone, \"MMZ\") == true"}]}], "timesInPeriod": 1, "periodInMinutes": 5, "enabled": true, "notifications": [{"channel": {"name": "ZC alarm", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "Zone capacity alert", "content": "${alarmTime} \nzone: ${zone}\navailable : ${available_capacity.max}\ntotal: ${total_capacity.max}\nusedPercent: ${used_percent.max}\n\n\n", "whichLevels": "WARN,ERROR", "repeatedSendAsyncmq": false, "isMarkdown": false, "mentions": "[]", "pdGroupTags": "", "routingRule": "used.percent > 0.3"}], "alarmExtensionRelations": [], "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "ZONE_STATISTICS"}, {"name": "ZC_Throughput", "rules": [{"level": "WARN", "needHits": 1, "hitCount": 1, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "throughput.max", "operator": ">=", "threshold": "6000"}]}, {"level": "WARN", "needHits": 1, "hitCount": 1, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "throughput.max", "operator": ">=", "threshold": "12000"}]}], "timesInPeriod": 1, "periodInMinutes": 5, "enabled": true, "notifications": [{"channel": {"name": "ZC alarm", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "ZC-Throught-Overflow", "content": "time:${alarmTime}\nthrought: ${throughput.max}\nZone: ${zone}\nusedPercent: ${used_percent.max}", "whichLevels": "WARN,ERROR", "repeatedSendAsyncmq": false, "isMarkdown": false, "pdGroupTags": ""}], "alarmExtensionRelations": [], "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "ZONE_STATISTICS"}, {"name": "Connect-GW-ZC-FAILURE", "rules": [{"level": "ERROR", "needHits": 10, "hitCount": 10, "timeWindow": 600, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "thresold", "operator": ">", "threshold": "20"}, {"conditionType": "EXPRESSION", "expression": "string.contains(zone_pk_conn_failure,\"TC_CONN_PK\") != true"}]}, {"level": "FATAL", "needHits": 10, "hitCount": 10, "timeWindow": 600, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "thresold", "operator": ">", "threshold": "300"}, {"conditionType": "EXPRESSION", "expression": "string.contains(zone_pk_conn_failure,\"TC_CONN_PK\") != true"}]}], "timesInPeriod": 3, "periodInMinutes": 60, "enabled": true, "notifications": [{"channel": {"name": "ZC alarm", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "Conn_GW_ZC_ERROR", "content": "zonePk:${zone_pk_conn_failure}\nzone:${zone}\nalarmTime:${alarmTime}\ninstance:${instance}", "whichLevels": "ERROR", "repeatedSendAsyncmq": false, "isMarkdown": false, "pdGroupTags": ""}, {"channel": {"name": "RTM - ZC", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "GW/Collab Service Invite Fail!", "content": "There appears to be an issue inviting a collab service from a MMR ZC.\n20+ events in the last 10 minutes. Check networking issues and confirm the target collab zone is working. \n\nzonePk:${zone_pk_conn_failure}\nzone:${zone}\nalarmTime:${alarmTime}\ninstance:${instance}", "whichLevels": "ERROR", "repeatedSendAsyncmq": false, "isMarkdown": false, "pdGroupTags": ""}, {"channel": {"name": "Pagerduty-ZC-p1", "engineName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isDefault": 0}, "title": "GW/Collab Service Invite Fail!", "content": "There appears to be an issue inviting a collab service from a MMR ZC.\n20+ events in the last 10 minutes. Check networking issues and confirm the target collab zone is working. \n\nzonePk:${zone_pk_conn_failure}\nzone:${zone}\nalarmTime:${alarmTime}\ninstance:${instance}", "whichLevels": "ERROR", "repeatedSendAsyncmq": false, "isMarkdown": false, "pdGroupTags": "", "runBook": "https://dg01docs.zoom.us/doc/zvoq7diPTGu5E_zO2dPUiQ"}, {"channel": {"name": "Pagerduty-ZC-p0", "engineName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isDefault": 0}, "title": "GW/Collab Service Invite Fail!", "content": "There appears to be an issue inviting a collab service from a MMR ZC.\n20+ events in the last 10 minutes. Check networking issues and confirm the target collab zone is working. \n\nzonePk:${zone_pk_conn_failure}\nzone:${zone}\nalarmTime:${alarmTime}\ninstance:${instance}", "whichLevels": "FATAL", "repeatedSendAsyncmq": false, "isMarkdown": false, "pdGroupTags": "", "runBook": "https://dg01docs.zoom.us/doc/zvoq7diPTGu5E_zO2dPUiQ"}, {"channel": {"name": "Pagerduty-Eng-ZC", "engineName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isDefault": 0}, "title": "GW/Collab Service Invite Fail!", "content": "There appears to be an issue inviting a collab service from a MMR ZC.\n20+ events in the last 10 minutes. Check networking issues and confirm the target collab zone is working. \n\nzonePk:${zone_pk_conn_failure}\nzone:${zone}\nalarmTime:${alarmTime}\ninstance:${instance}", "whichLevels": "ERROR,FATAL", "repeatedSendAsyncmq": false, "isMarkdown": false, "pdGroupTags": ""}], "alarmExtensionRelations": [], "description": "Connect-GW-ZC-FAILURE", "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "groupTags": "", "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "ZC_PK_CONN_FAILURE"}, {"name": "Resp_cli_error_code_cnt_cluster_ai", "rules": [{"level": "WARN", "needHits": 3, "hitCount": 3, "timeWindow": 180, "isContinuous": false, "conditions": [{"conditionType": "AI", "name": "error_code_cnt", "operator": "AI", "threshold": "3", "expression": "5871b6ba-81a2-4601-83dd-9ce5700636f3", "extension": "{\"detectMode\":\"Upper\"}"}]}, {"level": "ERROR", "needHits": 3, "hitCount": 3, "timeWindow": 180, "isContinuous": false, "conditions": [{"conditionType": "AI", "name": "error_code_cnt", "operator": "AI", "threshold": "5", "expression": "5871b6ba-81a2-4601-83dd-9ce5700636f3", "extension": "{\"detectMode\":\"Upper\"}"}]}, {"level": "FATAL", "needHits": 3, "hitCount": 3, "timeWindow": 180, "isContinuous": false, "conditions": [{"conditionType": "FIELD", "name": "error_code_cnt.count", "operator": ">=", "threshold": "5000"}, {"conditionType": "AI", "name": "error_code_cnt", "operator": "AI", "threshold": "5", "expression": "5871b6ba-81a2-4601-83dd-9ce5700636f3", "extension": "{\"detectMode\":\"Upper\"}"}, {"conditionType": "TAG", "name": "error_code", "operator": "in", "threshold": "102006"}]}], "timesInPeriod": 1, "periodInMinutes": 5, "enabled": true, "notifications": [{"channel": {"name": "ZC alarm", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "Resp_cli_err_cluster", "content": "${alarmTime}\n${cluster}\n${error_code}\n${error_code_cnt.count}\ndetail: https://cube.zoom.us/dashboards/f1c7f86b-0e5a-4828-857b-d44deed5de37", "whichLevels": "ERROR,FATAL", "repeatedSendAsyncmq": false, "isMarkdown": false, "mentions": "[{\"displayName\":\"<PERSON>\",\"email\":\"<EMAIL>\"},{\"displayName\":\"<PERSON>\",\"email\":\"<EMAIL>\"},{\"displayName\":\"Kai J\",\"email\":\"<EMAIL>\"}]", "pdGroupTags": "", "runBook": "https://dg01docs.zoom.us/space/swk_oH6MRVa8taWYHixD9Q"}, {"channel": {"name": "Pagerduty-MMZ-p0", "engineName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isDefault": 0}, "title": "ZC-Resp-ERR", "content": "error code:${error_code}\ncount: ${error_code_cnt.count}", "whichLevels": "FATAL", "repeatedSendAsyncmq": false, "isMarkdown": false, "mentions": "[]", "pdGroupTags": "", "runBook": "https://dg01docs.zoom.us/space/swk_oH6MRVa8taWYHixD9Q"}, {"channel": {"name": "Pagerduty-Eng-ZC", "engineName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isDefault": 0}, "title": "ZC-Resp-ERR", "content": "error code:${error_code}\ncount: ${error_code_cnt.count}", "whichLevels": "FATAL", "repeatedSendAsyncmq": false, "isMarkdown": false, "mentions": "[]", "pdGroupTags": "", "runBook": "https://dg01docs.zoom.us/space/swk_oH6MRVa8taWYHixD9Q"}, {"channel": {"name": "ZC alarm", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "Resp_cli_err_cluster", "content": "${alarmTime}\n${cluster}\n${error_code}\n${error_code_cnt.count}\ndetail: https://cube.zoom.us/dashboards/f1c7f86b-0e5a-4828-857b-d44deed5de37", "whichLevels": "WARN", "repeatedSendAsyncmq": false, "isMarkdown": false, "mentions": "[]", "pdGroupTags": "", "runBook": "https://dg01docs.zoom.us/space/swk_oH6MRVa8taWYHixD9Q"}, {"channel": {"name": "Pagerduty-ZC-p1", "engineName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isDefault": 0}, "title": "ZC-Resp-ERR", "content": "${cluster}\nerror code:${error_code}\ncount: ${error_code_cnt.count}\n\nhttps://docs.zoom.us/doc/SlwFRmB7TM--mzTmZYDtsg?from=client", "whichLevels": "ERROR", "repeatedSendAsyncmq": false, "isMarkdown": false, "mentions": "[]", "pdGroupTags": "", "runBook": "https://dg01docs.zoom.us/space/swk_oH6MRVa8taWYHixD9Q"}, {"channel": {"name": "RTM - ZC", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "Resp_cli_err_cluster", "content": "${alarmTime}\n${cluster}\n${error_code}\n${error_code_cnt.count}\ndetail: https://cube.zoom.us/dashboards/f1c7f86b-0e5a-4828-857b-d44deed5de37\n\n", "whichLevels": "WARN,ERROR,FATAL", "repeatedSendAsyncmq": false, "isMarkdown": false, "mentions": "[]", "pdGroupTags": "", "runBook": "https://dg01docs.zoom.us/space/swk_oH6MRVa8taWYHixD9Q"}], "alarmExtensionRelations": [], "description": "102*:Error Code ", "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "groupTags": "", "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "Resp_cli_err_cluster"}, {"name": "ZC_Conf_Failover_Cnt_Alert", "rules": [{"level": "WARN", "needHits": 1, "hitCount": 1, "timeWindow": 60, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "conf_cnt", "operator": ">=", "threshold": "50"}]}, {"level": "ERROR", "needHits": 1, "hitCount": 1, "timeWindow": 60, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "conf_cnt", "operator": ">=", "threshold": "100"}]}, {"level": "FATAL", "needHits": 2, "hitCount": 2, "timeWindow": 300, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "conf_cnt", "operator": ">=", "threshold": "1000"}]}], "timesInPeriod": 1, "periodInMinutes": 5, "enabled": true, "notifications": [{"channel": {"name": "RTM - ZC", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "zc_conf_failover_cnt_alert", "content": "Many conferences failover by zc restart. Please check.\nconf_cnt = ${conf_cnt}\ncluster = ${cluster}\nzone = ${zone}\ninstance id = ${instanceid}", "whichLevels": "WARN,ERROR,FATAL", "repeatedSendAsyncmq": false, "isMarkdown": false, "pdGroupTags": "", "runBook": "https://dg01docs.zoom.us/doc/tmsyJ-nmSn6B4z6gj0k-Gg"}, {"channel": {"name": "Pagerduty-ZC-p1", "engineName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isDefault": 0}, "title": "zc_conf_failover_cnt_alert", "content": "Many conferences failover by zc restart. Please check.\nconf_cnt = ${conf_cnt}\ncluster = ${cluster}\nzone = ${zone}\ninstance id = ${instanceid}", "whichLevels": "ERROR", "repeatedSendAsyncmq": false, "isMarkdown": false, "pdGroupTags": "", "runBook": "https://dg01docs.zoom.us/doc/tmsyJ-nmSn6B4z6gj0k-Gg"}, {"channel": {"name": "Pagerduty-ZC-p0", "engineName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isDefault": 0}, "title": "zc_conf_failover_cnt_alert", "content": "Many conferences failover by zc restart. Please check.\nconf_cnt = ${conf_cnt}\ncluster = ${cluster}\nzone = ${zone}\ninstance id = ${instanceid}", "whichLevels": "FATAL", "repeatedSendAsyncmq": false, "isMarkdown": false, "pdGroupTags": "", "runBook": "https://dg01docs.zoom.us/doc/tmsyJ-nmSn6B4z6gj0k-Gg"}], "alarmExtensionRelations": [], "description": "Restart ZC, cause many conferences failover. Two zones use same MMZ name will cause this issue.", "levelsSendIncident": "", "alarmMatchMode": 0, "groupTags": "", "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "ZC_Conf_Failover_Count"}, {"name": "ZC_STAT_ALERT", "rules": [{"level": "ERROR", "needHits": 1, "hitCount": 1, "timeWindow": 60, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "stat0.max", "operator": "!=", "threshold": "1.0"}, {"conditionType": "EXPRESSION", "expression": "string.contains(zone,\"MMZ\")"}, {"conditionType": "EXPRESSION", "expression": "string.contains(zone, \"TJ_MMZ6\") == false"}, {"conditionType": "EXPRESSION", "expression": "string.contains(zone, \"SG_MMZ9\") == false"}]}, {"level": "ERROR", "needHits": 1, "hitCount": 1, "timeWindow": 60, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "stat1.max", "operator": "!=", "threshold": "1.0"}, {"conditionType": "EXPRESSION", "expression": "string.contains(zone,\"MMZ\")"}, {"conditionType": "EXPRESSION", "expression": "string.contains(zone,\"GO_TJ_MMZ1\") == false"}, {"conditionType": "EXPRESSION", "expression": "string.contains(zone, \"TJ_MMZ6\") == false"}, {"conditionType": "EXPRESSION", "expression": "string.contains(zone, \"SG_MMZ9\") == false"}]}, {"level": "ERROR", "needHits": 1, "hitCount": 1, "timeWindow": 60, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "stat2.max", "operator": "!=", "threshold": "1.0"}, {"conditionType": "EXPRESSION", "expression": "string.contains(zone,\"MMZ\")"}, {"conditionType": "EXPRESSION", "expression": "string.contains(zone, \"TJ_MMZ6\") == false"}, {"conditionType": "EXPRESSION", "expression": "string.contains(zone, \"SG_MMZ9\") == false"}]}, {"level": "ERROR", "needHits": 1, "hitCount": 1, "timeWindow": 60, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "stat3.max", "operator": "!=", "threshold": "1.0"}, {"conditionType": "EXPRESSION", "expression": "string.contains(zone,\"MMZ\")"}, {"conditionType": "EXPRESSION", "expression": "string.contains(zone,\"GO\") == false"}, {"conditionType": "EXPRESSION", "expression": "string.contains(zone,\"US04\") == false"}, {"conditionType": "EXPRESSION", "expression": "string.contains(zone,\"DG01\") == false"}, {"conditionType": "EXPRESSION", "expression": "string.contains(zone, \"TJ_MMZ6\") == false"}, {"conditionType": "EXPRESSION", "expression": "string.contains(zone, \"SG_MMZ9\") == false"}]}, {"level": "ERROR", "needHits": 1, "hitCount": 1, "timeWindow": 60, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "stat4.max", "operator": "!=", "threshold": "1.0"}, {"conditionType": "EXPRESSION", "expression": "string.contains(zone,\"MMZ\")"}, {"conditionType": "EXPRESSION", "expression": "string.contains(zone,\"GO\") == false"}, {"conditionType": "EXPRESSION", "expression": "string.contains(zone,\"US04\") == false"}, {"conditionType": "EXPRESSION", "expression": "string.contains(zone,\"DG01\") == false"}, {"conditionType": "EXPRESSION", "expression": "string.contains(zone, \"TJ_MMZ6\") == false"}, {"conditionType": "EXPRESSION", "expression": "string.contains(zone, \"SG_MMZ9\") == false"}]}, {"level": "ERROR", "needHits": 1, "hitCount": 1, "timeWindow": 60, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "minstat0.min", "operator": "!=", "threshold": "0"}, {"conditionType": "EXPRESSION", "expression": "string.contains(zone,\"MMZ\")"}, {"conditionType": "EXPRESSION", "expression": "string.contains(zone,\"GO_TJ_MMZ1\") == false"}, {"conditionType": "EXPRESSION", "expression": "string.contains(zone,\"DG01_TJ_MMZ1\") == false"}, {"conditionType": "EXPRESSION", "expression": "string.contains(zone, \"SG_MMZ9\") == false"}]}, {"level": "ERROR", "needHits": 1, "hitCount": 1, "timeWindow": 60, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "minstat1.min", "operator": "!=", "threshold": "0.0"}, {"conditionType": "EXPRESSION", "expression": "string.contains(zone,\"MMZ\")"}, {"conditionType": "EXPRESSION", "expression": "string.contains(zone,\"GO_TJ_MMZ1\") == false"}, {"conditionType": "EXPRESSION", "expression": "string.contains(zone,\"DG01_TJ_MMZ1\") == false"}, {"conditionType": "EXPRESSION", "expression": "string.contains(zone, \"SG_MMZ9\") == false"}]}, {"level": "ERROR", "needHits": 1, "hitCount": 1, "timeWindow": 60, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "minstat2.min", "operator": "!=", "threshold": "0.0"}, {"conditionType": "EXPRESSION", "expression": "string.contains(zone,\"MMZ\")"}, {"conditionType": "EXPRESSION", "expression": "string.contains(zone,\"GO_TJ_MMZ1\") == false"}, {"conditionType": "EXPRESSION", "expression": "string.contains(zone,\"DG01_TJ_MMZ1\") == false"}, {"conditionType": "EXPRESSION", "expression": "string.contains(zone, \"SG_MMZ9\") == false"}]}, {"level": "ERROR", "needHits": 1, "hitCount": 1, "timeWindow": 60, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "minstat3.min", "operator": "!=", "threshold": "0.0"}, {"conditionType": "EXPRESSION", "expression": "string.contains(zone,\"MMZ\")"}, {"conditionType": "EXPRESSION", "expression": "string.contains(zone,\"GO\") == false"}, {"conditionType": "EXPRESSION", "expression": "string.contains(zone,\"US04\") == false"}, {"conditionType": "EXPRESSION", "expression": "string.contains(zone,\"DG01\") == false"}, {"conditionType": "EXPRESSION", "expression": "string.contains(zone, \"SG_MMZ9\") == false"}]}, {"level": "ERROR", "needHits": 1, "hitCount": 1, "timeWindow": 60, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "minstat4.min", "operator": "!=", "threshold": "0.0"}, {"conditionType": "EXPRESSION", "expression": "string.contains(zone,\"MMZ\")"}, {"conditionType": "EXPRESSION", "expression": "string.contains(zone,\"GO\") == false"}, {"conditionType": "EXPRESSION", "expression": "string.contains(zone,\"US04\") == false"}, {"conditionType": "EXPRESSION", "expression": "string.contains(zone,\"DG01\") == false"}, {"conditionType": "EXPRESSION", "expression": "string.contains(zone, \"SG_MMZ9\") == false"}]}], "timesInPeriod": 1, "periodInMinutes": 1, "enabled": true, "notifications": [{"channel": {"name": "ZC alarm", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "ZC-STAT-ERROR", "content": "ALL ZC in this zone: ${zone}  is backup.\nstat0: ${stat0.max}    stat1: ${stat1.max}  stat2: ${stat2.max}  stat3:  ${stat3.max}  stat4:  ${stat4.max}\nMinStat0: ${minstat0.min}  MinStat1:${minstat1.min}  MinStat2::${minstat2.min}  MinStat3: ${minstat3.min} MinStat4:${minstat4.min}\nRegion:${region}\nMMC:${mmc}\n", "whichLevels": "ERROR", "repeatedSendAsyncmq": false, "isMarkdown": false, "pdGroupTags": ""}], "alarmExtensionRelations": [], "description": "zc stat alert,all backup", "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "groupTags": "", "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "ZC_ZC_STAT"}, {"name": "ZC-STAT-ALERT-T", "rules": [{"level": "ERROR", "needHits": 1, "hitCount": 1, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "stat0.max", "operator": "!=", "threshold": "1"}, {"conditionType": "EXPRESSION", "expression": "string.contains(zone,\"MMZ\")"}]}], "timesInPeriod": 1, "periodInMinutes": 1, "enabled": false, "notifications": [{"channel": {"name": "ZC alarm", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "ZC-STAT-ERROR", "content": "stat0: ${stat0.max}    stat1: ${stat1.max}  stat2: ${stat2.max}  stat3:  ${stat3.max}  stat4:  ${stat4.max}\nMinStat0: ${minstat0.min}  MinStat1:${minstat1.min}  MinStat2::${minstat2.min}  MinStat3: ${minstat3.min} MinStat4:${minstat4.min}\nZone: ${zone}", "whichLevels": "ERROR", "repeatedSendAsyncmq": false, "isMarkdown": false, "mentions": "[]", "pdGroupTags": ""}], "alarmExtensionRelations": [], "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "ZC_ZC_STAT"}, {"name": "ZC-CC-LAZY-CC-ALARM", "rules": [{"level": "WARN", "needHits": 1, "hitCount": 1, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "cc_lazy_count.count", "operator": ">", "threshold": "60"}]}, {"level": "ERROR", "needHits": 1, "hitCount": 1, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "cc_lazy_count.count", "operator": ">=", "threshold": "100"}]}], "timesInPeriod": 1, "periodInMinutes": 5, "enabled": true, "notifications": [{"channel": {"name": "ZC alarm", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "CC-LAZY-ALARM", "content": "CC lazy count alarm\ncc lazy count: ${cc_lazy_count.count}\ncc_addr : ${cc_addr}\ndetail: https://cube.zoom.us/dashboards/783ff111-b9e9-4e75-94a6-b03df1d4f74d\n\n", "whichLevels": "WARN,ERROR", "repeatedSendAsyncmq": false, "isMarkdown": false, "pdGroupTags": ""}], "alarmExtensionRelations": [], "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "ZC_CC_LAZY_CC_INFO"}, {"name": "ZC-CC-LAZY-CC-INFO_ai", "rules": [{"level": "WARN", "needHits": 1, "hitCount": 1, "isContinuous": true, "conditions": [{"conditionType": "AI", "name": "CC_LAZY_COUNT", "operator": "AI", "threshold": "3", "expression": "a6a55962-5dc3-47de-ac23-1f0ea46d4a7f"}]}, {"level": "ERROR", "needHits": 1, "hitCount": 1, "isContinuous": true, "conditions": [{"conditionType": "AI", "name": "CC_LAZY_COUNT", "operator": "AI", "threshold": "5", "expression": "a6a55962-5dc3-47de-ac23-1f0ea46d4a7f"}]}], "timesInPeriod": 1, "periodInMinutes": 5, "enabled": true, "notifications": [{"channel": {"name": "ZC alarm", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "ZC-CC-LAZY-CC-INFO", "content": "cc_addr: ${cc_addr}\nCC_LAZY_COUNT: ${cc_lazy_count.count}\ndetail: https://cube.zoom.us/dashboards/783ff111-b9e9-4e75-94a6-b03df1d4f74d", "whichLevels": "ERROR", "repeatedSendAsyncmq": false, "isMarkdown": false, "mentions": "[{\"displayName\":\"<PERSON>\",\"email\":\"<EMAIL>\"},{\"displayName\":\"<PERSON>\",\"email\":\"<EMAIL>\"},{\"displayName\":\"Kai J\",\"email\":\"<EMAIL>\"}]", "pdGroupTags": ""}], "alarmExtensionRelations": [], "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "ZC_CC_LAZY_CC_INFO"}, {"name": "ZC-GW-UNAVAILABLE", "rules": [{"level": "WARN", "needHits": 1, "hitCount": 1, "timeWindow": 60, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "count", "operator": "!=", "threshold": "0"}, {"conditionType": "TAG", "name": "zone", "operator": "not-in", "threshold": "GLOBALDEV_IAD_LTTZ1"}]}, {"level": "ERROR", "needHits": 3, "hitCount": 3, "timeWindow": 180, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "count", "operator": "!=", "threshold": "0"}, {"conditionType": "TAG", "name": "zone", "operator": "not-in", "threshold": "GLOBALDEV_IAD_LTTZ1"}]}], "timesInPeriod": 1, "periodInMinutes": 5, "enabled": true, "notifications": [{"channel": {"name": "ZC alarm", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "GW-ZC-NO-LOAD", "content": "Gateway ZC do not have enough load, please help check:\nZone: ${zone}\nHostname: ${hostname}\nMeeting id: ${uuid}\ngateway type: ${service_type}\ndetails: https://cube.zoom.us/dashboards/76175726-5e7a-4150-b831-f00c269f9164?service=service_monitor&timeRange=%7B%22type%22%3A%22relative%22%2C%22value%22%3A%2230m%22%7D\n", "whichLevels": "WARN", "repeatedSendAsyncmq": false, "isMarkdown": false, "mentions": "[{\"displayName\":\"<PERSON>\",\"email\":\"<EMAIL>\"},{\"displayName\":\"<PERSON>\",\"email\":\"<EMAIL>\"},{\"displayName\":\"Kai J\",\"email\":\"<EMAIL>\"}]", "pdGroupTags": ""}, {"channel": {"name": "RTM - ZC", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "GW-ZC-NO-LOAD", "content": "Gateway ZC do not have enough load, please help check:\nZone: ${zone}\nHostname: ${hostname}\nMeeting id: ${uuid}\ngateway type: ${service_type}\ndetails: https://cube.zoom.us/dashboards/76175726-5e7a-4150-b831-f00c269f9164?service=service_monitor&timeRange=%7B%22type%22%3A%22relative%22%2C%22value%22%3A%2230m%22%7D", "whichLevels": "ERROR", "repeatedSendAsyncmq": false, "isMarkdown": false, "mentions": "[{\"displayName\":\"<PERSON>\",\"email\":\"<EMAIL>\"},{\"displayName\":\"<PERSON>\",\"email\":\"<EMAIL>\"},{\"displayName\":\"Kai J\",\"email\":\"<EMAIL>\"}]", "pdGroupTags": ""}], "alarmExtensionRelations": [], "description": "Find available gateway failure", "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "ZC_GW_UNAVAILABLE"}, {"name": "REALTIME_ZC_40_UNSUSPENDED", "rules": [{"level": "ERROR", "needHits": 1, "hitCount": 1, "timeWindow": 60, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "zone_load_suspend_mmr_count", "operator": ">", "threshold": "0"}, {"conditionType": "EXPRESSION", "expression": "zone_load_suspend_mmr_count / zone_load_total_mmr_count > 0.6"}, {"conditionType": "TAG", "name": "zone", "operator": "not-in", "threshold": "DG01_IAD_HYZ1,EU01_AMS_HYZ1,EU01_FRA_HYZ1,FRA_LSS_HYZ,EU02_LEJ_HYZ1,GO_NY_HYZ1,GO_SC_HYZ1,GO_RTS_SJC_HYZ1,GO_<PERSON>JC_LSS_HYZ,AM_HYZ1,FR_HYZ1,HK_HYZ1,NY_HYZ1,SC_HYZ1,SG_HYZ1,SIN_HYZ1,SJC_HYZ1,SYD_HYZ1,TJ_HYZ1,TY_HYZ1,YYZ_HYZ1,AMS_LSS_HYZ,IAD_LSS_HYZ,SJC_LSS_HYZ,TJ_LSS_HYZ,BOM_HYZ1,OCI_JED_HYZ1,OOH_MMZ9,IAD_HYZ1,HKG_HYZ1,AMS_HYZ1"}, {"conditionType": "EXPRESSION", "expression": "string.contains(zone, \"HYZ1\") == false"}]}], "timesInPeriod": 1, "periodInMinutes": 30, "enabled": true, "notifications": [{"channel": {"name": "RTM - MMR", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "MMR zone under 40% available  - ${Zone}", "content": "REGION: ${region}\nZONE: ${zone}\n\nZone has ${zone_load_suspend_mmr_count} / ${zone_load_total_mmr_count} suspended which exceeds 60% of total capacity. ", "whichLevels": "ERROR", "repeatedSendAsyncmq": false, "isMarkdown": true, "mentions": "[]", "pdGroupTags": ""}, {"channel": {"name": "Pagerduty-MMZ-p1", "engineName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isDefault": 0}, "title": "MMR zone under 40% available - ${Zone}", "content": "REGION: ${region}\nZONE: ${zone}\n\nZone has ${zone_load_suspend_mmr_count} / ${zone_load_total_mmr_count} suspended which exceeds 60% of total capacity. ", "whichLevels": "ERROR", "repeatedSendAsyncmq": false, "isMarkdown": false, "mentions": "[]", "pdGroupTags": "Zone"}], "alarmExtensionRelations": [], "description": "@james.powis alarms if more than 60% of a zone is suspended", "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "groupTags": "", "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "ZC_ZONE_LOAD"}, {"name": "JustAVerifyAlarm", "rules": [{"level": "FATAL", "needHits": 1, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "zone_load_client_count", "operator": ">=", "threshold": "1000"}]}], "timesInPeriod": 1, "periodInMinutes": 4, "enabled": false, "notifications": [{"channel": {"name": "VerifyEmail33", "engineName": "Email", "isDefault": 0}, "title": "Just a verify email", "content": "Just a verify email", "whichLevels": "WARN,ERROR,FATAL", "repeatedSendAsyncmq": false, "isMarkdown": false}], "alarmExtensionRelations": [], "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "ZC_ZONE_LOAD"}, {"name": "testAlarm", "rules": [{"level": "WARN", "needHits": 1, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "zone_load_client_count", "operator": ">=", "threshold": "16000"}, {"conditionType": "FIELD", "name": "zone_load_meeting_count", "operator": ">=", "threshold": "1000"}]}], "timesInPeriod": 3, "periodInMinutes": 30, "enabled": false, "notifications": [{"channel": {"name": "TestChannel", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "ZC Overload", "content": "mmc: ${mmc}\nregion: ${region}\ninstance: ${instance}\nclient count: ${zone_load_client_count}", "whichLevels": "WARN", "repeatedSendAsyncmq": false, "isMarkdown": false}, {"channel": {"name": "VerifyEmail", "engineName": "Email", "isDefault": 0}, "title": "ZC Overload", "content": "mmc: ${mmc}\nregion: ${region}\ninstance: ${instance}\nclient count: ${zone_load_client_count}", "whichLevels": "WARN,ERROR,FATAL", "repeatedSendAsyncmq": false, "isMarkdown": false}], "alarmExtensionRelations": [], "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "ZC_ZONE_LOAD"}, {"name": "ZC_test", "rules": [{"level": "WARN", "needHits": 1, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "zone_load_client_count", "operator": ">=", "threshold": "0"}]}], "timesInPeriod": 3, "periodInMinutes": 30, "enabled": false, "notifications": [{"channel": {"name": "VerifyEmail", "engineName": "Email", "isDefault": 0}, "title": "test", "content": "zc", "whichLevels": "WARN", "repeatedSendAsyncmq": false, "isMarkdown": false}], "alarmExtensionRelations": [], "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "ZC_ZONE_LOAD"}, {"name": "SA01_ZONE_1000_COUNT", "rules": [{"level": "ERROR", "needHits": 1, "hitCount": 1, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "zone_load_panelist", "operator": ">=", "threshold": "1000"}, {"conditionType": "TAG", "name": "zone", "operator": "in", "threshold": "OCI_JED_MMZ1,OCI_JED_MMZ2"}]}], "timesInPeriod": 1, "periodInMinutes": 5, "enabled": false, "notifications": [{"channel": {"name": "RTM - MMR", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "SA01-ZONE-MAX-MEETING-PARTICIPANTS", "content": "Participant count : ${zone_load_panelist}\nzone: ${zone}", "whichLevels": "ERROR", "repeatedSendAsyncmq": false, "isMarkdown": false, "mentions": "[{\"displayName\":\"<PERSON><PERSON><PERSON>\",\"email\":\"<EMAIL>\"}]", "pdGroupTags": ""}, {"channel": {"name": "Pagerduty-MMZ-p1", "engineName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isDefault": 0}, "title": "SA01-ZONE-MAX-MEETING-PARTICIPANTS", "content": "Participant count : ${zone_load_panelist}\nzone: ${zone}", "whichLevels": "ERROR", "repeatedSendAsyncmq": false, "isMarkdown": false, "pdGroupTags": ""}], "alarmExtensionRelations": [], "description": "", "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "ZC_ZONE_LOAD"}, {"name": "ZC_Zone_Load_alert", "rules": [{"level": "WARN", "needHits": 1, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "zone_load_non_p2p_client_count", "operator": ">", "threshold": "30000"}, {"conditionType": "TAG", "name": "region", "operator": "in", "threshold": "CA"}]}, {"level": "WARN", "needHits": 1, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "zone_load_non_p2p_client_count", "operator": ">", "threshold": "26000"}, {"conditionType": "TAG", "name": "region", "operator": "in", "threshold": "VA"}]}, {"level": "WARN", "needHits": 1, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "zone_load_non_p2p_client_count", "operator": ">", "threshold": "5600"}]}], "timesInPeriod": 3, "periodInMinutes": 30, "enabled": false, "notifications": [{"channel": {"name": "Test Alarm Channel", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "Capacity is greater than 80%", "content": "${alarmName}\n${region}\n${zone}\n${instanceid}", "whichLevels": "WARN,ERROR,INFO", "repeatedSendAsyncmq": false, "isMarkdown": false}], "alarmExtensionRelations": [], "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "ZC_ZONE_LOAD"}, {"name": "Regster-Failure", "rules": [{"level": "ERROR", "needHits": 1, "hitCount": 1, "timeWindow": 60, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "addr", "operator": "not-in", "threshold": "\"\""}, {"conditionType": "EXPRESSION", "expression": "string.contains(zone, \"HYZ\") == false"}, {"conditionType": "FIELD", "name": "addr", "operator": "not-in", "threshold": "GO_RTS_SJC"}, {"conditionType": "EXPRESSION", "expression": "string.contains(zone, \"KSA_LSS01\") == false"}]}], "timesInPeriod": 1, "periodInMinutes": 5, "enabled": true, "notifications": [{"channel": {"name": "ZC alarm", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "Register-ZC-Failure", "content": "${alarmTime}\naddr:${addr}\nZone:${zone}\ninstance:${instanceid}\n\nThis is normally a sign of a misconfiguration on a host. It cannot properly connect to its ZC. It could also be related to CSMS secrets missing or having rotated improperly.", "whichLevels": "ERROR", "repeatedSendAsyncmq": false, "isMarkdown": false, "mentions": "[{\"displayName\":\"<PERSON>\",\"email\":\"<EMAIL>\"},{\"displayName\":\"<PERSON>\",\"email\":\"<EMAIL>\"}]", "pdGroupTags": ""}, {"channel": {"name": "Pagerduty-ZC-p1", "engineName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isDefault": 0}, "title": "Register-ZC-Failure", "content": "${alarmTime}\naddr:${addr}\nZone:${zone}\ninstance:${instanceid}\n\nThis is normally a sign of a misconfiguration on a host. It cannot properly connect to its ZC. It could also be related to CSMS secrets missing or having rotated improperly.", "whichLevels": "ERROR", "repeatedSendAsyncmq": false, "isMarkdown": false, "mentions": "[]", "pdGroupTags": ""}], "alarmExtensionRelations": [], "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "groupTags": "", "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "SVR_REG_FAILURE"}, {"name": "ZC-CC-LOST-ALARM", "rules": [{"level": "WARN", "needHits": 1, "hitCount": 1, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "connect_cc_fail_count.count", "operator": ">=", "threshold": "1"}]}, {"level": "ERROR", "needHits": 2, "hitCount": 2, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "connect_cc_fail_count.count", "operator": ">=", "threshold": "2"}]}], "timesInPeriod": 1, "periodInMinutes": 5, "enabled": true, "notifications": [{"channel": {"name": "ZC alarm", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "CC-LOST", "content": "ZC disconnect from CC.\nregion: ${region}\nMMC: ${mmc}\nzone: ${zone}\ninstance: ${instance}", "whichLevels": "WARN,ERROR", "repeatedSendAsyncmq": false, "isMarkdown": false, "pdGroupTags": ""}], "alarmExtensionRelations": [], "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "ZC_CC_LOST"}, {"name": "REALTIME_ZC_CC_LOST", "rules": [{"level": "ERROR", "needHits": 5, "hitCount": 5, "timeWindow": 300, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "connect_cc_fail_count.count", "operator": ">=", "threshold": "5"}]}, {"level": "FATAL", "needHits": 10, "hitCount": 9, "timeWindow": 540, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "connect_cc_fail_count.count", "operator": ">=", "threshold": "50"}]}, {"level": "WARN", "needHits": 2, "hitCount": 2, "timeWindow": 300, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "connect_cc_fail_count.count", "operator": ">=", "threshold": "3"}]}], "timesInPeriod": 2, "periodInMinutes": 30, "enabled": true, "notifications": [{"channel": {"name": "RTM - ZC", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "RTM_ZC_CC_LOST  :  ZC lost connection with CC!", "content": "The ZC Lost Connections with CC for several minutes!\nThis could signal a major datacenter network issue or CCTRL issue, please check runbook. \nCurrent Count: ${connect_cc_fail_count.count}\nZone: ${zone} | ${region}\nPublic: ${instance}\nHostName: ${hostname}\nMMC: ${mmc}\nInstanceID: ${instanceid}", "whichLevels": "ERROR,FATAL,WARN", "repeatedSendAsyncmq": false, "isMarkdown": false, "pdGroupTags": "", "runBook": "https://zoomvideo.atlassian.net/wiki/spaces/REAL/pages/2990145620/CC+Lost+Runbook"}, {"channel": {"name": "Pagerduty-ZC-p1", "engineName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isDefault": 0}, "title": "RTM_ZC_CC_LOST  : ZC lost connection with CC!", "content": "The ZC Lost Connections with CC for 5 minutes!\nThis could signal a major datacenter network issue or CCTRL issue, please check runbook. \nCurrent Count: ${connect_cc_fail_count.count}\nZone: ${zone} | ${region}\nPublic: ${instance}\nHostName: ${hostname}\nMMC: ${mmc}\nInstanceID: ${instanceid}", "whichLevels": "ERROR", "repeatedSendAsyncmq": false, "isMarkdown": false, "pdGroupTags": "", "runBook": "https://zoomvideo.atlassian.net/wiki/spaces/REAL/pages/2990145620/CC+Lost+Runbook"}, {"channel": {"name": "Pagerduty-ZC-p0", "engineName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isDefault": 0}, "title": "RTM_ZC_CC_LOST  : ZC lost connection with CC!", "content": "The ZC Lost Connections with CC for 10 minutes!\nThis could signal a major datacenter network issue or CCTRL issue, please check runbook. \nCurrent Count: ${connect_cc_fail_count.count}\nZone: ${zone} | ${region}\nPublic: ${instance}\nHostName: ${hostname}\nMMC: ${mmc}\nInstanceID: ${instanceid}\n", "whichLevels": "FATAL", "repeatedSendAsyncmq": false, "isMarkdown": false, "pdGroupTags": "", "runBook": "https://zoomvideo.atlassian.net/wiki/spaces/REAL/pages/2990145620/CC+Lost+Runbook"}], "alarmExtensionRelations": [], "description": "@A<PERSON><PERSON>", "levelsSendIncident": "FATAL,ERROR", "alarmMatchMode": 0, "groupTags": "", "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "ZC_CC_LOST"}, {"name": "ZCtrl_connect_cc_fail_count", "rules": [{"level": "ERROR", "needHits": 3, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "connect_cc_fail_count.count", "operator": ">", "threshold": "0"}]}, {"level": "WARN", "needHits": 2, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "connect_cc_fail_count.count", "operator": ">", "threshold": "0"}]}], "timesInPeriod": 1, "periodInMinutes": 30, "enabled": false, "notifications": [{"channel": {"name": "Test Alarm Channel", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "ZCtrl connect.cc.fail.count", "content": "MMC: ${mmc}\nREGION: ${region}\nZone: ${zone}\nHostname: ${hostname}\nInstance: ${instanceid}\nServiceName: ${serviceName}\nConnect CC fail count(${connect_cc_fail_count.count}) over 0.", "whichLevels": "WARN,ERROR,FATAL", "repeatedSendAsyncmq": false, "isMarkdown": false}, {"channel": {"name": "Incident channel", "engineName": "Incident", "isDefault": 0}, "title": "ZC connect.cc.fail.count", "content": "MMC: ${mmc}\nREGION: ${region}\nZone: ${zone}\nHostname: ${hostname}\nInstance: ${instanceid}\nServiceName: ${serviceName}\nConnect CC fail count(${connect_cc_fail_count.count}) over 0 3 times.", "whichLevels": "ERROR", "repeatedSendAsyncmq": false, "isMarkdown": false}], "alarmExtensionRelations": [], "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "ZC_CC_LOST"}, {"name": "REG-WCC-FAIL", "rules": [{"level": "WARN", "needHits": 1, "hitCount": 1, "timeWindow": 60, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "register_fail_count", "operator": ">=", "threshold": "1"}, {"conditionType": "TAG", "name": "zone", "operator": "not-in", "threshold": "RTS_GO_SJC_LSS,RTS_GO_SJC_LSS02,DG01_SJC_LSS01,KSA_LSS01"}]}], "timesInPeriod": 3, "periodInMinutes": 30, "enabled": true, "notifications": [{"channel": {"name": "LSSZC Alarm", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "register wcc fail", "content": "register wcc fail\nregion: ${region}\nzone: ${zone}", "whichLevels": "WARN", "repeatedSendAsyncmq": false, "isMarkdown": false, "mentions": "[]", "pdGroupTags": ""}], "alarmExtensionRelations": [], "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "groupTags": "", "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "REG_WCC_FAIL"}, {"name": "REALTIME_ZSS_SUSPENDED_CAPACITY", "rules": [{"level": "ERROR", "needHits": 1, "hitCount": 1, "timeWindow": 60, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "active_server_percent", "operator": "<", "threshold": "41"}]}], "timesInPeriod": 2, "periodInMinutes": 30, "enabled": true, "notifications": [{"channel": {"name": "RTM - LSS", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "Zone has abnormally high suspended host count! (Z<PERSON>, NEEDS A NEW CHANNEL!)", "content": "ZONE: ${zone}\nTotal Hosts: ${total_server_count}\nUnsuspended Percentage: ${active_server_percent}\n\nWork to unsuspected immediately and investigate after why this happened. ", "whichLevels": "ERROR,FATAL", "repeatedSendAsyncmq": false, "isMarkdown": false, "mentions": "[]", "pdGroupTags": ""}, {"channel": {"name": "Realtime-LSS-p1", "engineName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isDefault": 0}, "title": "Zone has abnormally high suspended host count! (Z<PERSON>, NEEDS A NEW CHANNEL!)", "content": "ZONE: ${zone}\nTotal Hosts: ${total_server_count}\nUnsuspended Percentage: ${active_server_percent}\n\nWork to unsuspected immediately and investigate after why this happened. ", "whichLevels": "ERROR,FATAL", "repeatedSendAsyncmq": false, "isMarkdown": false, "pdGroupTags": ""}], "alarmExtensionRelations": [], "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "groupTags": "", "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "Z<PERSON>_ZON<PERSON>_LEVEL_GW_STATUS"}, {"name": "Resp_cli_error_code_cnt_mmc_ai", "rules": [{"level": "WARN", "needHits": 1, "hitCount": 1, "isContinuous": true, "conditions": [{"conditionType": "AI", "name": "error_code_cnt", "operator": "AI", "threshold": "3", "expression": "98995742-8cc4-43e7-a65c-ab2e9f55ec22"}]}, {"level": "ERROR", "needHits": 1, "hitCount": 1, "isContinuous": true, "conditions": [{"conditionType": "AI", "name": "error_code_cnt", "operator": "AI", "threshold": "5", "expression": "98995742-8cc4-43e7-a65c-ab2e9f55ec22"}]}], "timesInPeriod": 1, "periodInMinutes": 5, "enabled": true, "notifications": [{"channel": {"name": "ZC alarm", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "Resp_cli_err_mmc", "content": "${alarmTime}\n${mmc}\n${error_code}\n${error_code_cnt.count}\ndetail: https://cube.zoom.us/dashboards/f1c7f86b-0e5a-4828-857b-d44deed5de37", "whichLevels": "WARN,ERROR", "repeatedSendAsyncmq": false, "isMarkdown": false, "mentions": "[]", "pdGroupTags": ""}], "alarmExtensionRelations": [], "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "Resp_cli_err_mmc"}, {"name": "Cloud_MMR_Suspend_Alert", "rules": [{"level": "ERROR", "needHits": 1, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "stat", "operator": ">", "threshold": "0"}, {"conditionType": "TAG", "name": "eip", "operator": "not-in", "threshold": "**************,**************"}]}], "timesInPeriod": 10, "periodInMinutes": 1, "enabled": true, "notifications": [{"channel": {"name": "Cloud MMR Suspend <PERSON><PERSON>", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "Cloud MMR Suspend <PERSON><PERSON>", "content": "{\n    \"host_name\": ${hostname}_${process},\n    \"log_time\": ${alarmTime},\n    \"tcp_mmr_address\": ${mmr_address},\n    \"ssl_mmr_address\": ${mmr_instanceid},\n    \"ZC_EIP\": ${eip},\n    \"MMZ\": ${mmz},\n    \"Region\": ${region},\n    \"MMC\": ${mmc}\n}", "whichLevels": "ERROR", "repeatedSendAsyncmq": false, "isMarkdown": false}], "alarmExtensionRelations": [], "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "ZC_SUSPEND_MMR"}, {"name": "ZC-CMR-NO_LOAD", "rules": [{"level": "WARN", "needHits": 1, "hitCount": 1, "timeWindow": 60, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "meeting_id", "operator": "not-in", "threshold": "\" \""}, {"conditionType": "TAG", "name": "zone", "operator": "not-in", "threshold": "AVA_MRZ01"}]}, {"level": "ERROR", "needHits": 2, "hitCount": 30, "timeWindow": 300, "isContinuous": true, "conditions": [{"conditionType": "FIELD", "name": "meeting_id", "operator": "not-in", "threshold": "\" \""}, {"conditionType": "TAG", "name": "zone", "operator": "not-in", "threshold": "AVA_MRZ01"}]}], "timesInPeriod": 1, "periodInMinutes": 5, "enabled": true, "notifications": [{"channel": {"name": "ZC alarm", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "CMR-Zone-No-Load", "content": "meetingid: ${meeting_id}\nzone:${zone}\ninstance id:${instanceid}", "whichLevels": "WARN", "repeatedSendAsyncmq": false, "isMarkdown": false, "pdGroupTags": ""}, {"channel": {"name": "RTM - ZC", "engineName": "<PERSON><PERSON>", "isDefault": 0}, "title": "CMR-Zone-No-Load", "content": "meetingid: ${meeting_id}\nzone:${zone}\ninstance id:${instanceid}", "whichLevels": "ERROR", "repeatedSendAsyncmq": false, "isMarkdown": false, "mentions": "[{\"displayName\":\"<PERSON>\",\"email\":\"<EMAIL>\"},{\"displayName\":\"<PERSON>\",\"email\":\"<EMAIL>\"},{\"displayName\":\"Kai J\",\"email\":\"<EMAIL>\"}]", "pdGroupTags": ""}], "alarmExtensionRelations": [], "description": "CMR zone do not have enough load", "levelsSendIncident": "FATAL", "alarmMatchMode": 0, "groupTags": "", "status": "APPROVED", "source": "METRIC", "sourceType": "DEFAULT", "metricsName": "CMR_ZC_NO_LOAD"}]}