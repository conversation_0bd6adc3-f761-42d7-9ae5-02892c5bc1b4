package us.zoom.cube.site.core.config;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import us.zoom.cube.site.core.RsaService;
import us.zoom.infra.dao.model.InfluxdbClusterDO;
import us.zoom.infra.dao.model.InfluxdbTenantRelationDO;
import us.zoom.infra.dao.model.TenantDO;
import us.zoom.infra.dao.service.InfluxdbClusterDAO;
import us.zoom.infra.dao.service.InfluxdbTenantRelationDAO;
import us.zoom.infra.dao.service.TenantDAO;
import us.zoom.infra.influx.config.InfluxV2HandlerFactory;
import us.zoom.infra.influx.model.cluster.ClusterType;
import us.zoom.infra.influx.service.InfluxV2Handler;
import us.zoom.infra.utils.Instance;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/9/22 3:04 PM
 */

@Component
@Slf4j
public class InfluxdbCloudCacheLoader implements CacheLoader {

    @Autowired
    private InfluxV2HandlerFactory influxV2HandlerFactory;

    @Autowired
    private InfluxdbClusterDAO influxdbClusterDAO;

    @Autowired
    private InfluxdbTenantRelationDAO influxdbTenantRelationDAO;

    @Autowired
    private TenantDAO tenantDAO;

    @Autowired
    private RsaService rsaService;

    @Override
    public void load() {
        // 1.Prepare data/configurations from/to LocalCache/MySQL.
        List<InfluxdbClusterDO> cloudInfluxdbClusterDOList = Instance.ofNullable(influxdbClusterDAO.listAllCloudCluster());
        List<String> cloudInfluxdbClusterIdList = cloudInfluxdbClusterDOList.stream().map(InfluxdbClusterDO::getId).collect(Collectors.toList());
        List<InfluxdbTenantRelationDO> influxdbTenantRelationDOList = influxdbTenantRelationDAO.listAll();
        List<InfluxdbTenantRelationDO> cloudInfluxdbTenantRelationDOList = Instance.ofNullable(influxdbTenantRelationDOList).stream()
                .filter(e -> cloudInfluxdbClusterIdList.contains(e.getInfluxdbClusterId())).collect(Collectors.toList());
        List<TenantDO> tenantDOList = tenantDAO.listAll();
        // Build newInfluxdbClusterDOMap: influxDBClusterId -> InfluxdbClusterDO
        Map<String, InfluxdbClusterDO> newInfluxdbClusterDOMap = buildInfluxdbClusterDOMap(cloudInfluxdbClusterDOList);
        Map<String, InfluxdbClusterDO> oldInfluxdbClusterDOMap = influxV2HandlerFactory.getInfluxdbClusterDOMap();
        // Map: influxdbClusterId -> InfluxV2Handler
        Map<String, InfluxV2Handler> influxV2HandlerMapById = influxV2HandlerFactory.getInfluxV2HandlerMapOfId();

        // 2.Build new InfluxV2Handler if configurations are changed
        List<String> changedInfluxClusterIdList = Lists.newArrayList();
        List<InfluxV2Handler> changedInfluxV2HandlerList = Lists.newArrayList();
        newInfluxdbClusterDOMap.forEach((influxdbClusterId, newInfluxdbClusterDO) -> {
            InfluxdbClusterDO oldInfluxdbClusterDO = oldInfluxdbClusterDOMap.get(influxdbClusterId);
            boolean isConfigChanged = isInfluxClusterChanged(oldInfluxdbClusterDO, newInfluxdbClusterDO);
            if (!isConfigChanged) {
                return;
            }
            // Influx Cluster configurations are changed.
            InfluxV2Handler oldInfluxV2Handler = influxV2HandlerMapById.get(influxdbClusterId);
            if (oldInfluxV2Handler != null) {
                // Collect changed InfluxHandler old links. They will be closed later on.
                changedInfluxV2HandlerList.add(oldInfluxV2Handler);
                // Collect changed influxdbClusterIds. They will be used to update tenant relations.
                changedInfluxClusterIdList.add(influxdbClusterId);
            }
            // Build new InfluxHandler and update influxHandlerMapById.
            InfluxV2Handler newInfluxHandler = buildInfluxV2Handler(newInfluxdbClusterDO);
            if (newInfluxHandler != null) {
                influxV2HandlerMapById.put(influxdbClusterId, newInfluxHandler);
            }
        });
        influxV2HandlerFactory.setInfluxdbClusterDOMap(newInfluxdbClusterDOMap);
        // Get default InfluxdbClusterDO
        InfluxdbClusterDO defaultInfluxdbClusterDO = getDefaultInfluxdbCluster(cloudInfluxdbClusterDOList);
        final String defaultInfluxdbClusterId = (defaultInfluxdbClusterDO != null) ? defaultInfluxdbClusterDO.getId() : null;

        // 3.Update tenant relation of changed InfluxDB cluster
        // Map: tenantName -> InfluxV2Handler
        Map<String, InfluxV2Handler> influxV2HandlerMapOfTenant = influxV2HandlerFactory.getInfluxV2HandlerMapOfTenant();
        // Map: tenantId -> tenantName
        Map<String, String> tenantIdToNameMap = Instance.ofNullable(tenantDOList).stream()
                .collect(Collectors.toMap(TenantDO::getId, TenantDO::getName));
        // Map: tenantName -> influxdbClusterId
        Map<String, String> influxdbTenantRelationMap = Instance.ofNullable(cloudInfluxdbTenantRelationDOList)
                .stream().filter(e -> tenantIdToNameMap.get(e.getRelatedTenantId()) != null)
                .collect(Collectors.toMap(e -> tenantIdToNameMap.get(e.getRelatedTenantId()), InfluxdbTenantRelationDO::getInfluxdbClusterId));
        // Add tenant relation for default InfluxDB cluster
        if (defaultInfluxdbClusterId != null) {
            List<String> relatedTenantIdList = Instance.ofNullable(influxdbTenantRelationDOList).stream().map(InfluxdbTenantRelationDO::getRelatedTenantId).collect(Collectors.toList());
            List<TenantDO> unrelatedTenantDOList = Instance.ofNullable(tenantDOList).stream().filter(tenantDO -> !relatedTenantIdList.contains(tenantDO.getId())).collect(Collectors.toList());
            unrelatedTenantDOList.forEach(tenantDO -> {
                String tenantName = tenantDO.getName();
                influxdbTenantRelationMap.putIfAbsent(tenantName, defaultInfluxdbClusterId);
            });
        }
        // Update tenant relations if needed
        influxdbTenantRelationMap.forEach((tenantName, newInfluxdbClusterId) -> {
            InfluxV2Handler oldInfluxV2Handler = influxV2HandlerFactory.getV2HandlerByTenantName(tenantName);
            InfluxV2Handler newInfluxV2Handler = influxV2HandlerMapById.get(newInfluxdbClusterId);
            // For new added tenant relation, the old InfluxHandler does not exist.
            if (oldInfluxV2Handler == null) {
                influxV2HandlerMapOfTenant.put(tenantName, newInfluxV2Handler);
                return;
            }
            // The tenant relation is bound to another InfluxDB cluster.
            String oldInfluxdbClusterId = oldInfluxV2Handler.getInfluxdbClusterId();
            if (!oldInfluxdbClusterId.equals(newInfluxdbClusterId)) {
                influxV2HandlerMapOfTenant.put(tenantName, newInfluxV2Handler);
                return;
            }
            // The tenant relation binding is not changed. But the InfluxDB cluster configuration is changed.
            if (changedInfluxClusterIdList.contains(newInfluxdbClusterId)) {
                influxV2HandlerMapOfTenant.put(tenantName, newInfluxV2Handler);
            }
        });

        // 4.Close old InfluxHandler connections
        changedInfluxV2HandlerList.forEach(InfluxV2Handler::close);
    }

    private InfluxdbClusterDO getDefaultInfluxdbCluster(List<InfluxdbClusterDO> influxdbClusterDOList) {
        Optional<InfluxdbClusterDO> optional = Instance.ofNullable(influxdbClusterDOList)
                .stream().filter(InfluxdbClusterDO::getIsDefault).findFirst();
        return optional.orElse(null);
    }

    private Map<String, InfluxdbClusterDO> buildInfluxdbClusterDOMap(List<InfluxdbClusterDO> influxdbClusterDOList) {
        return Instance.ofNullable(influxdbClusterDOList)
                .stream().filter(e -> ClusterType.CLOUD.getTypeCode().equals(e.getType()))
                .collect(Collectors.toMap(InfluxdbClusterDO::getId, influxdbClusterDO -> {
                    try {
                        influxdbClusterDO.setUsername(rsaService.decrypt(influxdbClusterDO.getUsername()));
                        influxdbClusterDO.setPassword(rsaService.decrypt(influxdbClusterDO.getPassword()));
                    } catch (Exception exception) {
                        log.error("Decrypting InfluxdbClusterDO error! Cluster name: {}. ", influxdbClusterDO.getName(), exception);
                    }
                    return influxdbClusterDO;
                }));
    }

    private InfluxV2Handler buildInfluxV2Handler(InfluxdbClusterDO influxdbClusterDO) {
        InfluxV2Handler influxV2Handler = null;
        try {
            influxV2Handler = new InfluxV2Handler(influxdbClusterDO.getId(), influxdbClusterDO.getOrgID(),
                    influxdbClusterDO.getServerUrl(), influxdbClusterDO.getPassword());
        } catch (Exception e) {
            log.error("buildInfluxV2Handler error! ", e);
        }

        return influxV2Handler;
    }

    private boolean isInfluxClusterChanged(InfluxdbClusterDO oldInfluxdbClusterDO, InfluxdbClusterDO newInfluxdbClusterDO) {
        if (oldInfluxdbClusterDO == null) {
            return true;
        }
        if (!oldInfluxdbClusterDO.getOrgID().equals(newInfluxdbClusterDO.getOrgID())) {
            return true;
        }
        if (!oldInfluxdbClusterDO.getServerUrl().equals(newInfluxdbClusterDO.getServerUrl())) {
            return true;
        }
        if (!oldInfluxdbClusterDO.getUsername().equals(newInfluxdbClusterDO.getUsername())) {
            return true;
        }
        return !oldInfluxdbClusterDO.getPassword().equals(newInfluxdbClusterDO.getPassword());
    }
}
