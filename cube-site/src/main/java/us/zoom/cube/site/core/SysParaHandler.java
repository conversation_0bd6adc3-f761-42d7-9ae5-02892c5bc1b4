package us.zoom.cube.site.core;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import us.zoom.cube.lib.common.SysParaKeyEnum;
import us.zoom.cube.lib.common.SysParaTypeEnum;
import us.zoom.cube.site.infra.utils.IdUtils;
import us.zoom.cube.site.lib.input.SysParaInput;
import us.zoom.cube.site.lib.query.PageQuery;
import us.zoom.cube.site.lib.query.SysParaQuery;
import us.zoom.infra.dao.model.SysParaDO;
import us.zoom.infra.dao.service.SysParaDAO;

import java.util.Collections;
import java.util.List;

@Component
public class SysParaHandler {

    @Autowired
    private SysParaDAO sysParaDAO;

    @Autowired
    private UserHandler userHandler;

   public List<SysParaDO> listAll(){
       return sysParaDAO.listAll();
    }



    public List<SysParaDO> listByTypeAndKey(SysParaTypeEnum typeEnum, SysParaKeyEnum keyEnum){
        return sysParaDAO.listByTypeAndKey(typeEnum.name(),keyEnum.name());
    }

    public List<SysParaDO> searchSysPara(PageQuery<SysParaQuery> pageQuery) {
        return sysParaDAO.searchSysPara(pageQuery.getQueryPara().getType()
                , pageQuery.getQueryPara().getParaKey()
                , pageQuery.getQueryPara().getValue()
                , pageQuery.getPageSize() * (pageQuery.getPageIndex() - 1)
                , pageQuery.getPageSize());
    }

    public Integer getSysParaCount(PageQuery<SysParaQuery> pageQuery) {
        return sysParaDAO.getSysParaCount(pageQuery.getQueryPara().getType()
                , pageQuery.getQueryPara().getParaKey(), pageQuery.getQueryPara().getValue());
    }

    public List<SysParaDO> listByType(SysParaQuery sysParaQuery) {
        //Assert.assertNotNull("query type is null",sysParaQuery.getType());
        Assert.notNull(sysParaQuery.getType(), "query type is null");
        return sysParaDAO.listByType(sysParaQuery.getType());
    }

    public List<SysParaDO> listByTypeAndParaKey(String type, String paraKey) {
        return sysParaDAO.listByTypeAndKey(type,paraKey);
    }

    public List<SysParaDO> listByTypeAndParaKeyList(String type, List<String> paraKey) {
        return sysParaDAO.listByTypeAndKeyList(type, paraKey);
    }


    public List<SysParaDO> listByTypes(List<String> types) {
       if(CollectionUtils.isEmpty(types)){
           return Collections.emptyList();
       }
        return sysParaDAO.listByTypes(types);
    }

    public void add(SysParaInput sysParaInput) {
        check(sysParaInput,false);
        SysParaDO sysParaDO=new SysParaDO();
        BeanUtils.copyProperties(sysParaInput,sysParaDO);
        sysParaDO.setId(IdUtils.generateId());
        String userName = userHandler.getNameById(sysParaInput.getUserId());
        sysParaDO.setCreator(userName);
        sysParaDO.setEditor(userName);
        sysParaDAO.add(sysParaDO);
    }

    private void check(SysParaInput sysParaInput,boolean checkId) {
        Assert.notNull(sysParaInput, "sys para input is null");
        Assert.isTrue(!StringUtils.isBlank(sysParaInput.getType()), "sys type is blank");
        Assert.isTrue(!StringUtils.isBlank(sysParaInput.getParaKey()), "sys para key is blank");
        Assert.isTrue(!StringUtils.isBlank(sysParaInput.getValue()), "sys para value is blank");
        if(checkId){
            Assert.isTrue(!StringUtils.isBlank(sysParaInput.getId()), "id is blank");
        }
    }

    public void edit(SysParaInput sysParaInput) {
        check(sysParaInput,true);
        SysParaDO sysParaDO=new SysParaDO();
        BeanUtils.copyProperties(sysParaInput,sysParaDO);
        try {
            String userName = userHandler.getNameById(sysParaInput.getUserId());
            sysParaDO.setEditor(userName);
        } catch (Exception ignored){}
        sysParaDAO.edit(sysParaDO);
    }

    public void delete(String id) {
        Assert.isTrue(!StringUtils.isBlank(id), "id is blank");
        sysParaDAO.delete(id);
    }

    public SysParaDO getById(String id) {
        Assert.isTrue(!StringUtils.isBlank(id), "id is blank");
        return sysParaDAO.getById(id);
    }
}
