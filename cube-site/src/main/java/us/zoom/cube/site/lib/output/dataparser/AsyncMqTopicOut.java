package us.zoom.cube.site.lib.output.dataparser;

/**
 * @Author: luis.zheng
 * @Date: 2021/9/23 10:03 
 */
public class AsyncMqTopicOut {
    private String name;
    private String dcName;
    private String clusterName;

    private int partition;

    private String topicId;

    public AsyncMqTopicOut(String name, String dcName, String clusterName, int partition, String topicId) {
        this.name = name;
        this.dcName = dcName;
        this.clusterName = clusterName;
        this.partition = partition;
        this.topicId = topicId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDcName() {
        return dcName;
    }

    public void setDcName(String dcName) {
        this.dcName = dcName;
    }

    public String getClusterName() {
        return clusterName;
    }

    public void setClusterName(String clusterName) {
        this.clusterName = clusterName;
    }

    public int getPartition() {
        return partition;
    }

    public void setPartition(int partition) {
        this.partition = partition;
    }

    public String getTopicId() {
        return topicId;
    }

    public void setTopicId(String topicId) {
        this.topicId = topicId;
    }
}
