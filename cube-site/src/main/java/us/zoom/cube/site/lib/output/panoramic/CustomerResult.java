package us.zoom.cube.site.lib.output.panoramic;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
public class CustomerResult implements Serializable {
    @Serial
    private static final long serialVersionUID = 6498556211112826121L;
    @JsonIgnore
    private String accountId;
    private String companyName;
    private String companyLogo;
    private String sla;
}
