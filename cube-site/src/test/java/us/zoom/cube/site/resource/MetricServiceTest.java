package us.zoom.cube.site.resource;


import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import us.zoom.cube.site.CubeSiteApplication;
import us.zoom.cube.site.lib.query.MetricQuery;

import java.io.IOException;


@SpringBootTest(classes = CubeSiteApplication.class,webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)//springboot
public class MetricServiceTest {
    @Autowired
    us.zoom.cube.site.biz.MetricsService metricsService;


        public  void  testGetMetricDetail() throws IOException {
            MetricQuery metricQuery=new MetricQuery();
            metricQuery.setMetricId("da21f818-b07b-4c32-bb72-e0ac6b8ba5fb");
            metricsService.getMetricDetail(metricQuery);
        }

        @Test
        public void emptyTest(){

        }
    }
