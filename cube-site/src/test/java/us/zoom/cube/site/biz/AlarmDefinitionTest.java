package us.zoom.cube.site.biz;

import com.zoom.op.monitor.domain.alarm.AlarmDefinition;
import org.apache.commons.collections.CollectionUtils;
import org.junit.jupiter.api.Test;
import us.zoom.cube.lib.utils.JsonUtils;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.Valid;
import jakarta.validation.Validation;
import jakarta.validation.Validator;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

public class AlarmDefinitionTest {
    private static Validator validator = Validation.buildDefaultValidatorFactory()
            .getValidator();

    public static void beanValidate(Object obj) throws Exception {
        Map<String, String> validatedMsg = new HashMap<>();
        Set<ConstraintViolation<Object>> constraintViolations = validator.validate(obj);
        for (ConstraintViolation<Object> c : constraintViolations) {
            validatedMsg.put(c.getPropertyPath().toString(), c.getMessage());
        }
        if (CollectionUtils.isNotEmpty(constraintViolations)) {
            throw new Exception(JsonUtils.toJsonString(validatedMsg));
        }
    }

    @Test
    public void test(){

        AlarmDefinition definition=new AlarmDefinition();
        definition.setName("abcabcabcabc");
        try {
            beanValidate(definition);
        } catch (Exception e) {
            e.printStackTrace();
        }
        System.out.println("pk");
    }

    public void validate(@Valid AlarmDefinition definition){
        System.out.println(definition);
    }
}
