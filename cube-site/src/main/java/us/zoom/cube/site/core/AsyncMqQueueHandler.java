package us.zoom.cube.site.core;

import com.google.common.collect.Sets;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import us.zoom.async.mq.openapi.model.result.topic.TopicResult;
import us.zoom.cube.lib.common.AsyncMqQueueSourceTypeEnum;
import us.zoom.cube.lib.common.AsyncMqQueueStatusEnum;
import us.zoom.cube.lib.common.AsyncMqQueueTopicStatusEnum;
import us.zoom.cube.lib.common.CubeConstants;
import us.zoom.cube.lib.common.DataParserQueueType;
import us.zoom.cube.site.infra.utils.IdUtils;
import us.zoom.cube.site.lib.input.AsyncMqBatchUpdateInput;
import us.zoom.cube.site.lib.query.AsyncMqQueueQuery;
import us.zoom.infra.dao.model.AsyncMqQueueDO;
import us.zoom.infra.dao.service.AsyncMqQueueDAO;
import us.zoom.infra.thread.ThreadLocalStore;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Set;

/**
 * @Author: luis.zheng
 */
@Component
public class AsyncMqQueueHandler {
    @Autowired
    private AsyncMqQueueDAO asyncMqQueueDAO;

    public List<AsyncMqQueueDO> listAll(String clusterId) {
        List<AsyncMqQueueDO> asyncMqQueueDOList = new ArrayList<>();
        long counts = asyncMqQueueDAO.queryCounts();
        int pageSize = CubeConstants.CUBE_SQL_BATCH_QUERY_SIZE;
        long pageNum = counts / pageSize + (counts % pageSize == 0 ? 0 : 1);
        for (int i = 1; i <= pageNum; i++) {
            asyncMqQueueDOList.addAll(asyncMqQueueDAO.listBatch(pageSize * (i - 1), pageSize, clusterId));
        }
        return asyncMqQueueDOList;
    }

    public List<AsyncMqQueueDO> listAll() {
        return listAll(null);
    }

    public List<AsyncMqQueueDO> findByParam(AsyncMqQueueQuery query, int pageIndex, int pageSize) {
        if (query == null) {
            return new ArrayList<>();
        }
        return asyncMqQueueDAO.findByParam(query.getAsyncmqClusterId(),
                query.getServiceId(),
                query.getServiceName(),
                query.getSourceId(),
                query.getSourceName(),
                query.getUnitTagId(),
                query.getTopic(),
                query.getGroupId(),
                query.getThreadCount(),
                query.getFlinkQueue(),
                query.getAlarmQueue(),
                query.getEditor(),
                query.getCreator(),
                query.getSourceType(),
                query.getStatus(),
                query.getDcName(),
                query.getPartition(),
                query.getKafkaClusterName(),
                query.getTopicStatus(),
                pageSize * (pageIndex - 1), pageSize);
    }

    public Integer getCountByParam(AsyncMqQueueQuery query) {
        if (query == null) {
            return 0;
        }
        return asyncMqQueueDAO.getCountByParam(query.getAsyncmqClusterId(),
                query.getServiceId(),
                query.getServiceName(),
                query.getSourceId(),
                query.getSourceName(),
                query.getUnitTagId(),
                query.getTopic(),
                query.getGroupId(),
                query.getThreadCount(),
                query.getFlinkQueue(),
                query.getAlarmQueue(),
                query.getEditor(),
                query.getCreator(),
                query.getSourceType(),
                query.getStatus(),
                query.getDcName(),
                query.getPartition(),
                query.getKafkaClusterName(),
                query.getTopicStatus());
    }

    public List<AsyncMqQueueDO> getBySourceIds(Set<String> sourceIds) {
        if (CollectionUtils.isEmpty(sourceIds)) {
            return new ArrayList<>();
        }
        return asyncMqQueueDAO.getBySourceIds(sourceIds);
    }

    public Set<String> getNamesBySourceIdAndAsyncMqClusterId(String sourceId, String asyncMqClusterId) {
        if (!StringUtils.hasText(sourceId) && !StringUtils.hasText(asyncMqClusterId)) {
            return Sets.newHashSet();
        }
        return asyncMqQueueDAO.getNamesBySourceIdAndAsyncMqClusterId(sourceId, asyncMqClusterId);
    }

    public void deleteBySourceIdAndTopicNameLike(String sourceId, String topicName) {
        if (sourceId == null && !StringUtils.hasText(topicName)) {
            return;
        }
        Set<String> ids = asyncMqQueueDAO.getIdsBySourceIdAndTopicName(sourceId, topicName);
        if (!CollectionUtils.isEmpty(ids)) {
            asyncMqQueueDAO.deleteByIds(ids);
        }
    }

    public List<AsyncMqQueueDO> getByUnitTagId(String unitTagId) {
        return getByUnitTagIdAndSourceType(unitTagId, null);
    }

    public List<AsyncMqQueueDO> getByUnitTagIdAndSourceType(String unitTagId, Integer sourceType) {
        return asyncMqQueueDAO.getByUnitTagIdAndSourceType(unitTagId, sourceType);
    }

    public int countByOuterId(String dataParserId) {
        return asyncMqQueueDAO.countByOuterId(dataParserId);
    }

    public void batchAdd(List<AsyncMqQueueDO> asyncMqQueueDOS) {
        if (CollectionUtils.isEmpty(asyncMqQueueDOS)) {
            return;
        }
        asyncMqQueueDAO.batchAdd(asyncMqQueueDOS);
    }

    public void batchUpdate(List<AsyncMqQueueDO> asyncMqQueueDOS) {
        if (CollectionUtils.isEmpty(asyncMqQueueDOS)) {
            return;
        }
        asyncMqQueueDAO.batchUpdate(asyncMqQueueDOS);
    }

    public void batchUpdateConfig(List<AsyncMqQueueDO> asyncMqQueueDOS) {
        if (CollectionUtils.isEmpty(asyncMqQueueDOS)) {
            return;
        }
        asyncMqQueueDAO.batchUpdateConfig(asyncMqQueueDOS);
    }

    public void batchUpdateByIds(AsyncMqBatchUpdateInput input) {
        asyncMqQueueDAO.batchUpdateByIds(input.getIds(), input.getAlarmQueueId(), input.getFlinkQueueId(), input.getStatus(),
                input.getUnitTagId(), input.getThreadCount(), input.getUserName(), input.getDcName(), input.getPartition(),
                input.getKafkaClusterName(), input.getTopicStatus(), input.getReason());
    }

    public void deleteBySourceId(String sourceId) {
        asyncMqQueueDAO.deleteBySourceId(sourceId);
    }

    public int countByUnitTagId(String unitTagId) {
        return asyncMqQueueDAO.countByUnitTagId(unitTagId);
    }

    public void deleteByIds(Set<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        asyncMqQueueDAO.deleteByIds(ids);
    }

    public Set<String> findSourceIdsBySourceTypeTopicTenantId(Integer sourceType, String topic, String tenantId) {
        return asyncMqQueueDAO.findSourceIdsByTopicTenantId(sourceType, topic, tenantId);
    }

    public List<AsyncMqQueueDO> getByInputTopics(List<String> inputTopics) {
        return asyncMqQueueDAO.getByInputTopics(inputTopics);
    }

    public List<AsyncMqQueueDO> searchByParams(String hubUnitTagId, String topic, String group, int pageIndex, int pageSize) {
        return asyncMqQueueDAO.searchByParams(hubUnitTagId, topic, group, (pageIndex - 1) * pageSize, pageSize);
    }

    public int countByParams(String hubUnitTagId, String topic, String group) {
        return asyncMqQueueDAO.countByParams(hubUnitTagId, topic, group);
    }

    public List<String> searchTopicByUnitTag(String unitTagId, String topic, int pageIndex, int pageSize) {
        return asyncMqQueueDAO.searchTopicByUnitTag(unitTagId, topic, pageIndex, pageSize);
    }

    public int countTopicByUnitTag(String unitTagId, String topic) {
        return asyncMqQueueDAO.countTopicByUnitTag(unitTagId, topic);
    }

    public int countByAsyncmqClusterId(String asyncmqClusterId) {
        return asyncMqQueueDAO.countByAsyncmqClusterId(asyncmqClusterId);
    }

    public List<AsyncMqQueueDO> searchTopicNames(String dataparserId) {
        if (StringUtils.isEmpty(dataparserId)) {
            return new ArrayList<>();
        }
        return asyncMqQueueDAO.searchTopicName(dataparserId);
    }

    public void edit(String flinkQueueId, String alarmQueueId, String tenantId, String asyncmqClusterId, Integer status, Integer sourceType, String id, String editor) {
        asyncMqQueueDAO.edit(flinkQueueId, alarmQueueId, tenantId, asyncmqClusterId, status, sourceType, id, editor);
    }

    public AsyncMqQueueDO getOne(Integer sourceType, String sourceId) {
        return asyncMqQueueDAO.getByOne(sourceType, sourceId, null);
    }

    public AsyncMqQueueDO getOneByStartWith(Integer sourceType, String sourceId, String topicStartWith) {
        return asyncMqQueueDAO.getByOne(sourceType, sourceId, topicStartWith);
    }

    public AsyncMqQueueDO buildNewAsyncMqQueueDO(String asyncMqClusterId, String tenantId,
                                                 Integer sourceType, String sourceId, String topic, TopicResult topicResult) {
        AsyncMqQueueDO asyncMqQueueDO = new AsyncMqQueueDO();
        asyncMqQueueDO.setId(IdUtils.generateId());
        asyncMqQueueDO.setAsyncmqClusterId(asyncMqClusterId);
        if (AsyncMqQueueSourceTypeEnum.DataFlow.getType().equals(sourceType)) {
            asyncMqQueueDO.setTenantId(org.apache.commons.lang3.StringUtils.EMPTY);
            asyncMqQueueDO.setStatus(AsyncMqQueueStatusEnum.Approved.getStatus());
        } else {
            asyncMqQueueDO.setTenantId(tenantId);
        }
        asyncMqQueueDO.setSourceType(sourceType);
        asyncMqQueueDO.setSourceId(sourceId);
        asyncMqQueueDO.setType(DataParserQueueType.input.name());
        asyncMqQueueDO.setTopic(topic);
        asyncMqQueueDO.setIsBroadcast("no");
        asyncMqQueueDO.setThreadCount(1);
        asyncMqQueueDO.setOuterId("");
        asyncMqQueueDO.setOutputQueueCfg("");
        asyncMqQueueDO.setOutputQueueCfg("");
        asyncMqQueueDO.setOrder(0);
        asyncMqQueueDO.setTopicStatus(AsyncMqQueueTopicStatusEnum.enabled.getStatus());
        asyncMqQueueDO.setEditor(ThreadLocalStore.getUserNameLocal());
        asyncMqQueueDO.setCreator(ThreadLocalStore.getUserNameLocal());
        if (topicResult != null) {
            asyncMqQueueDO.setDcName(topicResult.getDcName());
            asyncMqQueueDO.setTopicId(topicResult.getId());
            asyncMqQueueDO.setKafkaClusterName(topicResult.getClusterName());
            asyncMqQueueDO.setPartition(topicResult.getPartitions());
            asyncMqQueueDO.setTopicStatus(AsyncMqQueueTopicStatusEnum.fromEnabledStatus(topicResult.getEnabled()).getStatus());
        } else {
            asyncMqQueueDO.setTopicStatus(AsyncMqQueueTopicStatusEnum.notExist.getStatus());
        }
        return asyncMqQueueDO;
    }
}
