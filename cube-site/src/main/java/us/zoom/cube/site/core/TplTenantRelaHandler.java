package us.zoom.cube.site.core;

import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import us.zoom.infra.dao.model.TplQueryDO;
import us.zoom.infra.dao.model.TplTenantRelaDO;
import us.zoom.infra.dao.service.TplTenantRelaDao;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.Set;

@Service
@Slf4j
public class TplTenantRelaHandler {

    @Resource
    @Setter
    private TplTenantRelaDao tplTenantRelaDao;

    public int insertTplTenantRela(TplTenantRelaDO tplTenantRelaDO) {
        return tplTenantRelaDao.insertTplTenantRela(tplTenantRelaDO);
    }

    public Set<String> listAllTenantId(List<String> dataparserId) {
        return tplTenantRelaDao.listAllTenantIdByDataparserId(dataparserId);
    }

    public TplTenantRelaDO getById(String id) {
        return tplTenantRelaDao.getById(id);
    }

    public void deleteById(String id) {
        tplTenantRelaDao.deleteById(id);
    }


    public List<TplTenantRelaDO> listAll() {
        return tplTenantRelaDao.listAll();
    }


    public int updateStatus(TplTenantRelaDO tplTenantRelaDO) {
        return tplTenantRelaDao.updateStatus(tplTenantRelaDO);
    }


    public List<TplTenantRelaDO> queryByTplId(TplQueryDO tplQueryDO) {
        return tplTenantRelaDao.queryByTplId(tplQueryDO);
    }

    public TplTenantRelaDO queryByTplIdAndTenantId(String tplId, String tenantId){
        return tplTenantRelaDao.queryByTplIdAndTenantId(tplId, tenantId);
    }

    public Boolean exist(String tplId, String tenantId){
        return queryByTplIdAndTenantId(tplId, tenantId) != null;
    }

}
