package us.zoom.cube.site.lib.output.alarm;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class FieldAlarmRelationOut {
    String metricsId;

    String tenantId;
    List<Relation> relations = new ArrayList<>();

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class Relation{

        String fieldName;

        List<SimpleAlarm> alarms = new ArrayList<>();
    }


}



