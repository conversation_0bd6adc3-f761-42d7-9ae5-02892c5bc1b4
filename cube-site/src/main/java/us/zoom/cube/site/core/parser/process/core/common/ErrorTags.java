package us.zoom.cube.site.core.parser.process.core.common;

import lombok.Builder;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
@Builder
public class ErrorTags {
    private String service;
    private String aaEnv;
    private String input;
    private String gId;
    private String name;
    private String id;
    private String unit;
    // data flow, data parser, new data parser
    private String sourceType;
    private String alarm;
    private String calc;

    private Map<String, Object> cacheMap;

    public ErrorTags service(String service) {
        this.service = service;
        return this;
    }

    public ErrorTags aaEnv(String aaEnv) {
        this.aaEnv = aaEnv;
        return this;
    }

    public ErrorTags input(String input) {
        this.input = input;
        return this;
    }

    public ErrorTags name(String name) {
        this.name = name;
        return this;
    }

    public ErrorTags gId(String gId) {
        this.gId = gId;
        return this;
    }

    public ErrorTags id(String id) {
        this.id = id;
        return this;
    }

    public ErrorTags unit(String unit) {
        this.unit = unit;
        return this;
    }

    public ErrorTags sourceType(String sourceType) {
        this.sourceType = sourceType;
        return this;
    }

    public ErrorTags alarm(String alarm) {
        this.alarm = alarm;
        return this;
    }

    public ErrorTags calc(String calc) {
        this.calc = calc;
        return this;
    }

    public Map<String, Object> toMap() {
        if (cacheMap == null) {
            cacheMap = new HashMap<>(10);
            cacheMap.put("service", service);
            cacheMap.put("aaEnv", aaEnv);
            cacheMap.put("input", input);
            cacheMap.put("gId", gId);
            cacheMap.put("name", name);
            cacheMap.put("id", id);
            cacheMap.put("unit", unit);
            cacheMap.put("sourceType", sourceType);
            cacheMap.put("alarm", alarm);
            cacheMap.put("calc", calc);
        }
        return cacheMap;
    }
}