package us.zoom.cube.site.core.preprocess;

import com.google.common.collect.Sets;
import us.zoom.cube.lib.utils.JsonUtils;
import us.zoom.cube.site.core.parser.process.core.monitoring.Measure;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * @Author: <EMAIL>
 * @ModuleOwner: <EMAIL>
 * @Date: 12/27/2022 10:35
 * @Description:
 */
public class BaseTest {


    public Measure getMeasure(String measure) {
        Map<String, Object> tag = new HashMap<>();
        tag.put("unit", "unitValue");
        tag.put("zoom", "zoomValue");
        tag.put("dataParser", "dataParserValue");
        Map<String, Object> field = new HashMap<>();
        field.put("cost", 32);
        field.put("size", 123);
        field.put("maxCost", 343);
        Set<String> pii = Sets.newHashSet("zoom", "unit");
        return new Measure(measure, tag, field, pii, System.currentTimeMillis());
    }

    public Measure getErrorMeasure(String measure) {
        Map<String, Object> tag = new HashMap<>();
        tag.put("unit", "unitValue");
        tag.put("zoom", "zoomValue");
        tag.put("dataParser", "dataParserValue");
        Set<String> pii = Sets.newHashSet("zoom", "unit");
        return new Measure(measure, tag, null, pii, System.currentTimeMillis());
    }

    public Map<String, Object> getSingleAgentMsgSingleMeasure(Map<String, Object> labelList, Measure... measures) {
        Map<String, Object> tag = new HashMap<>();
        tag.put("timestamp", System.currentTimeMillis());
        tag.put("source", "file:/data/logs/meeting/kiwi/meeting_kiwi-devep-66567cdd46-clktx/zoom_middleware/apm/kiwi/metrics.log|excludeFields|includeFields");
        tag.put("lambdaTimestamp", System.currentTimeMillis());
        tag.put("loggroup", "");
        tag.put("appcluster", "devep");
        tag.put("logstream", "_i-666666666a775f555_ip-10-10-10-110.ec2.internal_");
        tag.put("message", JsonUtils.toJsonStringIgnoreExp(measures));
        tag.put("id", "666666666666666666666666666666666");
        tag.put("unique_id", "ip-10-10-10-110.ec2.internal_file:/data/logs/meeting/kiwi/meeting_kiwi-devep-66567cdd46-clktx/zoom_middleware/apm/kiwi/metrics.log|excludeFields|includeFields_27309");
        tag.put("path_template", "/data/logs/*/*/*/zoom_middleware/*/*/metrics.log");
        tag.put("appName", "nws");
        tag.put("row_num", "4444");
        tag.put("topType", "file");
        tag.put("business", "");
        tag.put("mwName", "");
        tag.put("labelList", labelList);
        return tag;
    }

    public Map<String, Object> getSingleAgentMsgSingleMeasure(Map<String, Object> labelList, Measure measure) {
        Map<String, Object> tag = new HashMap<>();
        tag.put("timestamp", System.currentTimeMillis());
        tag.put("source", "file:/data/logs/meeting/kiwi/meeting_kiwi-devep-66567cdd46-clktx/zoom_middleware/apm/kiwi/metrics.log|excludeFields|includeFields");
        tag.put("lambdaTimestamp", System.currentTimeMillis());
        tag.put("loggroup", "");
        tag.put("appcluster", "devep");
        tag.put("logstream", "_i-666666666a775f555_ip-10-10-10-110.ec2.internal_");
        tag.put("message", JsonUtils.toJsonStringIgnoreExp(measure));
        tag.put("id", "666666666666666666666666666666666");
        tag.put("unique_id", "ip-10-10-10-110.ec2.internal_file:/data/logs/meeting/kiwi/meeting_kiwi-devep-66567cdd46-clktx/zoom_middleware/apm/kiwi/metrics.log|excludeFields|includeFields_27309");
        tag.put("path_template", "/data/logs/*/*/*/zoom_middleware/*/*/metrics.log");
        tag.put("appName", "nws");
        tag.put("row_num", "4444");
        tag.put("topType", "file");
        tag.put("business", "");
        tag.put("mwName", "");
        tag.put("labelList", labelList);
        return tag;
    }

    public Map<String, Object> getSingleAgentMsgSingleMessage(Map<String, Object> labelList, String message) {
        Map<String, Object> tag = new HashMap<>();
        tag.put("timestamp", System.currentTimeMillis());
        tag.put("source", "file:/data/logs/meeting/kiwi/meeting_kiwi-devep-66567cdd46-clktx/zoom_middleware/apm/kiwi/metrics.log|excludeFields|includeFields");
        tag.put("lambdaTimestamp", System.currentTimeMillis());
        tag.put("loggroup", "");
        tag.put("appcluster", "devep");
        tag.put("logstream", "_i-666666666a775f555_ip-10-10-10-110.ec2.internal_");
        tag.put("message", JsonUtils.toJsonStringIgnoreExp(message));
        tag.put("id", "666666666666666666666666666666666");
        tag.put("unique_id", "ip-10-10-10-110.ec2.internal_file:/data/logs/meeting/kiwi/meeting_kiwi-devep-66567cdd46-clktx/zoom_middleware/apm/kiwi/metrics.log|excludeFields|includeFields_27309");
        tag.put("path_template", "/data/logs/*/*/*/zoom_middleware/*/*/metrics.log");
        tag.put("appName", "nws");
        tag.put("row_num", "4444");
        tag.put("topType", "file");
        tag.put("business", "");
        tag.put("mwName", "");
        tag.put("labelList", labelList);
        return tag;
    }

    public Map<String, Object> labelList() {
        Map<String, Object> labelList = new HashMap<>();
        labelList.put("color", "red");
        labelList.put("appName", "kiki");
        return labelList;
    }
}
