package us.zoom.cube.site.lib.output.config.metrics;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import org.springframework.format.annotation.DateTimeFormat;
import us.zoom.cube.site.lib.output.agg.MetricsAggregationRuleComposeOutput;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2020/7/19 10:11 AM
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class MetricsAggregationOut {

    String id;

    String metricsId;

    Integer aggPeriod;

    Integer subPeriod;

    String aggTimeZone;

    Integer waitTime;

    String filterCondition;

    String aggregationResultFilter;

    MetricsAggregationRuleComposeOutput metricsAggregationRuleCompose = new MetricsAggregationRuleComposeOutput();


    int spcStatisticalPeriod = 1;

    int spcSamplingWeight = 1;

}
