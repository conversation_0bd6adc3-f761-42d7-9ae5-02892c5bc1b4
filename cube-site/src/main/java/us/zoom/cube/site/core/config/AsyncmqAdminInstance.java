package us.zoom.cube.site.core.config;

import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import us.zoom.cloud.secrets.spring.annotation.SecretValue;
import us.zoom.cube.site.biz.AsyncmqLoginTokenService;
import us.zoom.cube.site.biz.syspara.ZoomNodeSwitchParaService;
import us.zoom.cube.site.lib.dto.AsyncmqUserParamDTO;
import us.zoom.infra.asyncmq.AsyncMQRotateHandler;
import us.zoom.mq.client.AsyncMQ;
import us.zoom.mq.client.DefaultAsyncMQ;
import us.zoom.mq.common.Result;
import us.zoom.mq.common.enums.PrivilegesEditType;
import us.zoom.mq.common.param.LoginParam;
import us.zoom.mq.common.param.TopicParam;
import us.zoom.mq.common.param.TopicPrivilegesParam;
import us.zoom.mq.common.param.UserParam;
import us.zoom.mq.common.response.*;
import us.zoom.mq.common.spec.Role;

import java.util.*;
import java.util.stream.Collectors;

import static us.zoom.cube.site.biz.AsyncmqLoginTokenService.ERROR_CODE;

@Component
@Slf4j
public class AsyncmqAdminInstance {

    @Value("${async.mq.admin.endpoint}")
    private String asyncMqAdminEndpoint;

    @SecretValue("async.mq.admin.username")
    private String asyncMqAdminUsername;

    @SecretValue("async.mq.admin.password")
    private String asyncMqAdminPassword;


    @SecretValue("async.mq.endpoint")
    private String asyncMqEndpoint;

    @Value("${alarm.async.mq.username}")
    private String alarmAsyncMqUsername;

    @SecretValue("alarm.async.mq.password")
    private String alarmAsyncMqPassword;

    private volatile static AsyncMQ asyncMQ;

    private volatile static AsyncMQ asyncMQAlarm;

    private volatile static AsyncmqAdminInstance instance;

    public static final String EXIST_TOPIC = "(exist topic)";

    public static final String NEW_TOPIC = "(new topic)";

    public AsyncmqAdminInstance() {
    }

    public static AsyncmqAdminInstance getInstance() {
        return instance;
    }

    @PostConstruct
    public void initAsyncMQ() {
        try {
            initAdminAsyncMQ();
        } catch (Exception e) {
            log.error("initAsyncMQ error", e);
        }
    }

    public void initAdminAsyncMQ() {
        try {
            if (Objects.isNull(instance) || Objects.isNull(asyncMQ)) {
                asyncMQ = new DefaultAsyncMQ(asyncMqAdminEndpoint, asyncMqAdminUsername, asyncMqAdminPassword);
                instance = new AsyncmqAdminInstance();

                AsyncMQRotateHandler.addMQListener(asyncMQ, "async.mq.admin.password", asyncMqAdminUsername);
            }

            if (Objects.isNull(asyncMQAlarm)) {
                asyncMQAlarm = new DefaultAsyncMQ(asyncMqEndpoint, alarmAsyncMqUsername, alarmAsyncMqPassword);
                AsyncMQRotateHandler.addMQListener(asyncMQAlarm, "alarm.async.mq.password", alarmAsyncMqUsername);
            }
        } catch (Exception e) {
            log.error("initAdminAsyncMQ error", e);
        }

    }

    public static AsyncMQ getAlarmAsyncMQ() {
        return asyncMQAlarm;
    }

    public UserResult createUser(AsyncmqUserParamDTO asyncmqUserParamDTO) {
        Result<UserResult> result = asyncMQ.admin().createUser(createUserParam(asyncmqUserParamDTO));
        if (Objects.nonNull(result) && result.isSuccess()) {
            return result.getResponse();
        }
        throw new RuntimeException(exceptionReturn(result));
    }

    public UserResult getUserByName(String name) {
        Result<UserResult> result = asyncMQ.admin().getUserByName(name);
        if (Objects.nonNull(result) && result.isSuccess()) {
            return result.getResponse();
        }
        throw new RuntimeException(exceptionReturn(result));
    }

    public UserResult findOrCreateUser(AsyncmqUserParamDTO asyncmqUserParamDTO) {
        Result<UserResult> result = asyncMQ.admin().getUserByName(asyncmqUserParamDTO.getAccountId());

        if (result == null || !result.isSuccess()) {
            if (result != null && result.getCode() == ERROR_CODE) {
                return AsyncmqAdminInstance.getInstance().createUser(asyncmqUserParamDTO);
            }
            return null;
        }

        UserResult userResult = result.getResponse();
        if (StringUtils.isNotEmpty(userResult.getName())) {
            String newPassword = AsyncmqAdminInstance.getInstance().resetUserSecret(userResult.getId()).getPassword();
            userResult.setPassword(newPassword);
            return userResult;
        }
        return null;
    }

    public UserResult resetUserSecret(String id) {
        if (StringUtils.isBlank(id)) {
            log.error("reset secret error, id is null");
            return new UserResult();
        }
        Result<UserResult> result = asyncMQ.admin().resetUserSecret(id);
        if (Objects.nonNull(result) && result.isSuccess()) {
            return result.getResponse();
        }
        throw new RuntimeException(exceptionReturn(result));
    }

    public String exceptionReturn(Object Object) {
        Result result = (Result) Object;
        return Objects.isNull(result) ? "request asyncmq error, please try again later" : result.getException().getMessage() + result.getReason();
    }

    private UserParam createUserParam(AsyncmqUserParamDTO asyncmqUserParamDTO) {
        UserParam userParam = new UserParam();
        userParam.setRole(Role.APP);
        userParam.setDescription(AsyncmqLoginTokenService.DESCRIPTION);
        userParam.setName(asyncmqUserParamDTO.getAccountId());
        userParam.setEmails(asyncmqUserParamDTO.getEmail());
        userParam.setActive(true);
        userParam.setEnableAcl(true);
        userParam.setL2ServiceId(asyncmqUserParamDTO.getL2ServiceId());
        userParam.setL3ServiceId(asyncmqUserParamDTO.getL3ServiceId());
        userParam.setEnableAlert(asyncmqUserParamDTO.isEnableAlert());
        return userParam;
    }

    public List<TopicResult> getTopicByNames(Set<String> topicNames) {
        Result<List<TopicResult>> result = asyncMQ.admin().getTopicByNames(topicNames);
        if (Objects.nonNull(result) && result.isSuccess()) {
            return result.getResponse();
        }
        throw new RuntimeException(exceptionReturn(result));
    }

    public Result<LoginResult> login(String username, String password) {
        Result<LoginResult> result = asyncMQ.admin().login(new LoginParam(username, password));
        if (Objects.nonNull(result) && result.isSuccess()) {
            return result;
        }
        throw new RuntimeException(exceptionReturn(result));
    }

//    public AsyncmqLoginResult loginByHttp(String username, String password) throws Exception{
//        AsyncmqLoginResult loginResult = new AsyncmqLoginResult();
//        Map<String, String> loginParam = new HashMap<>();
//        loginParam.put("username", username);
//        loginParam.put("password", password);
//        String response = HttpUtils.post(Arrays.asList(environment.getProperty("async.mq.admin.endpoint") + AsyncmqLoginTokenService.LOGIN_URL),
//                loginParam, new HashMap<>(), JsonUtils.toJsonString(loginParam), new AtomicInteger(200), null, 3);
//        return loginResult;
//    }

    public TopicPrivilegesResult getTopicPrivileges(String id) {
        Result<TopicPrivilegesResult> result = asyncMQ.admin().getTopicPrivileges(id);
        if (Objects.nonNull(result) && result.isSuccess()) {
            return result.getResponse();
        }
        throw new RuntimeException(exceptionReturn(result));
    }

    public boolean setTopicPrivileges(String topicId, TopicPrivilegesParam topicPrivilegesParam) {
        Result<TopicPrivilegesResult> result = asyncMQ.admin().changeTopicPrivileges(topicId, topicPrivilegesParam);
        if (Objects.nonNull(result) && result.isSuccess()) {
            return true;
        }
        throw new RuntimeException(exceptionReturn(result));
    }

    public boolean setIncreaseTopicPrivileges(String topicId, TopicPrivilegesParam topicPrivilegesParam) {
        topicPrivilegesParam.setPrivilegesEditType(PrivilegesEditType.PRIVILEGES_ADD);
        Result<TopicPrivilegesResult> result = asyncMQ.admin().changeTopicPrivileges(topicId, topicPrivilegesParam);
        if (Objects.nonNull(result) && result.isSuccess()) {
            return true;
        }
        throw new RuntimeException(exceptionReturn(result));
    }

    public TopicResult createTopic(TopicParam topicParam) {
        Result<TopicResult> result = asyncMQ.admin().createTopic(topicParam);
        if (Objects.nonNull(result) && result.isSuccess()) {
            return result.getResponse();
        }
        throw new RuntimeException(exceptionReturn(result));
    }

    public CheckTopicResult getDefaultMs() {
        Result<CheckTopicResult> result = asyncMQ.admin().checkTopicConfig();
        if (Objects.nonNull(result) && result.isSuccess()) {
            return result.getResponse();
        }
        throw new RuntimeException(exceptionReturn(result));
    }

    public Set<String> getNotExistTopicByName(Set<String> topics, Map<String, Object> results) {
        Result<List<TopicResult>> result = asyncMQ.admin().getTopicByNames(topics);
        asyncMQ.admin().checkTopicConfig();
        if (Objects.nonNull(result) && result.isSuccess()) {
            Set<String> existTopic = result.getResponse().parallelStream().map(TopicResult::getName).collect(Collectors.toSet());
            existTopic.parallelStream().forEach(t -> {
                results.put(t + EXIST_TOPIC, us.zoom.cube.site.biz.template.TemplateConstant.SUCCESS);
            });
            Set<String> notExistTopicSet = new HashSet<>(topics);
            notExistTopicSet.removeAll(existTopic);
            return notExistTopicSet;
        }
        throw new RuntimeException(exceptionReturn(result));
    }

    public List<TopicResult> getExistTopicByName(Set<String> topics) {
        Result<List<TopicResult>> result = asyncMQ.admin().getTopicByNames(topics);
        if (Objects.nonNull(result) && result.isSuccess()) {
            return result.getResponse();
        }
        return new ArrayList<>();
    }

    public UserResult updateUserEmail(String id, UserParam userParam) {
        Result<UserResult> result = asyncMQ.admin().editUser(id, userParam);
        if (Objects.nonNull(result) && result.isSuccess()) {
            return result.getResponse();
        }
        return null;
    }

    public UserResult newGetUserByName(String name) {
        Result<UserResult> result = asyncMQ.admin().getUserByName(name);
        if (Objects.nonNull(result) && result.isSuccess()) {
            return result.getResponse();
        }
        return null;
    }
}
