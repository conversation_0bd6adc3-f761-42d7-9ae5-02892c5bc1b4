package us.zoom.cube.site.core;

import com.amazonaws.util.json.Jackson;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.lib.agent.AgentHeartCacheInfo;
import us.zoom.cube.lib.agent.AgentHeartInfo;
import us.zoom.cube.lib.agent.AgentMsgTypeEnum;
import us.zoom.cube.lib.utils.ExceptionStackUtils;
import us.zoom.cube.sdk.util.MonitorLogReporter;
import us.zoom.cube.site.api.web.StatusEnum;
import us.zoom.cube.site.biz.AgentService;
import us.zoom.cube.site.biz.syspara.AlarmParaService;
import us.zoom.cube.site.infra.enums.AgentHeartBeatDealPhaseEnum;
import us.zoom.cube.site.lib.monitor.AgentHeartBeatDealMetric;
import us.zoom.infra.model.alarm.KeepAliveLevel;
import us.zoom.infra.utils.IpUtils;
import us.zoom.mq.client.clients.consumer.RetryableStraw;
import us.zoom.mq.common.entity.Metadata;
import us.zoom.mq.common.entity.TaskContext;
import us.zoom.mq.common.entity.TaskEntity;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class AgentHeartBeatConsumerHandler implements RetryableStraw<String> {
    private final static TypeReference<String> TYPE_REFERENCE = new TypeReference<>() {
    };
    @Autowired
    private AgentService agentService;

    @Autowired
    private AlarmParaService alarmParaService;

    private final Logger monitorLog = LoggerFactory.getLogger("Monitor");

    @Override
    public boolean onMessage(List<TaskEntity<String>> taskEntities) {

        if (CollectionUtils.isEmpty(taskEntities)) {
            return false;
        }
        long startTime = System.currentTimeMillis();
        AgentHeartBeatDealMetric agentHeartBeatDealMetric;
        try {
            List<AgentHeartInfo> agentHeartInfos = new ArrayList<>();
            List<AgentHeartInfo> agentKeepAliveInfos = new ArrayList<>();
            String level = alarmParaService.getAgentKeepAliveConfig().getLevel();

            for (TaskEntity<String> taskEntity : taskEntities) {
                try {
                    // 1. parse model, complete topic/partition/sourceUnit.
                    AgentHeartInfo agentHeartInfo = Jackson.fromJsonString(taskEntity.getPayload(), AgentHeartInfo.class);
                    agentHeartInfo.setTopic(Optional.of(taskEntity).map(TaskEntity::getTaskContext).map(TaskContext::getTopicName).orElse(null));
                    agentHeartInfo.setPartition(Optional.of(taskEntity).map(TaskEntity::getMetadata).map(Metadata::getPartition).orElse(-1));
                    // TODO testCode
                    if (alarmParaService.isStopHeartBeatAgent(agentHeartInfo.getInstanceId()) || alarmParaService.isStopHeartBeatTopic(agentHeartInfo.getTopic())) {
                        continue;
                    }
                    //fill in sourceUnit
                    fillSourceUnit(agentHeartInfo, level);

                    // 2. filter keepAlive and agentHeart message.
                    if (StringUtils.equals(AgentMsgTypeEnum.keepAlive.name(), agentHeartInfo.getMsgType())) {
                        agentKeepAliveInfos.add(agentHeartInfo);
                    } else {
                        agentHeartInfos.add(agentHeartInfo);
                    }
                } catch (Exception e) {
                    log.error("consumer agent heart beat taskEntity error", e);
                }
            }
            //3. deal with heartInfo
            agentService.keepAliveHeart(agentKeepAliveInfos);
            agentService.heart(agentHeartInfos);
            agentHeartBeatDealMetric = new AgentHeartBeatDealMetric(IpUtils.getHost(), IpUtils.getLocalIP(), AgentHeartBeatDealPhaseEnum.end.name(), StatusEnum.SUCCESS.getStatus(), System.currentTimeMillis() - startTime, null);
        } catch (Exception e) {
            log.error("consumer agent heart beat message error, {}", e.getMessage());
            agentHeartBeatDealMetric = new AgentHeartBeatDealMetric(IpUtils.getHost(), IpUtils.getLocalIP(), AgentHeartBeatDealPhaseEnum.end.name(), StatusEnum.FAIL.getStatus(), System.currentTimeMillis() - startTime, ExceptionStackUtils.parseExceptionStackToString(e));
        }
        MonitorLogReporter.report(monitorLog, agentHeartBeatDealMetric);
        return false;
    }

    private void fillSourceUnit(AgentHeartInfo heartInfo, String level) {
        if (StringUtils.isNotBlank(heartInfo.getSourceUnit())) {
            return;
        }
        KeepAliveLevel keepAliveLevel = KeepAliveLevel.getFromValue(level);
        if (Objects.isNull(keepAliveLevel)) {
            return;
        }
        String sourceUnit = null;
        if (KeepAliveLevel.topic.equals(keepAliveLevel)) {
            sourceUnit = heartInfo.getTopic();
        } else if (KeepAliveLevel.partition.equals(keepAliveLevel)) {
            sourceUnit = heartInfo.getTopic() + AgentHeartCacheInfo.KEEP_ALIVE_SPLIT + heartInfo.getPartition();
        }
        heartInfo.setSourceUnit(sourceUnit);
    }

    @Override
    public TypeReference<String> type() {
        return TYPE_REFERENCE;
    }
}
