package us.zoom.cube.site.core.model.ad;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-01-30 17:44
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AiAlarmRuleCfg {
    private String id;
    private Integer level;
    private Integer needHits;
    private String alarmDefinitionId;
    private Integer rulesOrder;
    private Integer hitCount;
    private List<AiAlarmConditionCfg> aiAlarmConditionCfgList;
}
