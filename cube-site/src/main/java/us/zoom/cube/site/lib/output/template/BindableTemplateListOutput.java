package us.zoom.cube.site.lib.output.template;

import lombok.Data;

import java.util.List;

/**
 * @Author: <PERSON>
 * @Date:12/20/2022 17:18
 * @Description:
 */
@Data
public class BindableTemplateListOutput {

    private String templateGroupName;

    private String description;

    private List<TemplateItem> templateList;

    @Data
    public static class TemplateItem {

        private String id;

        private String name;

        //0 for unbind, 1 for bind
        private Integer bindStatus;

        private Boolean enabled;

    }

}
