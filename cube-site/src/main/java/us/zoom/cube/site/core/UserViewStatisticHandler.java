package us.zoom.cube.site.core;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.RemovalCause;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Triple;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import us.zoom.cube.lib.common.UserViewStatisticTypeEnum;
import us.zoom.cube.site.infra.utils.IdUtils;
import us.zoom.infra.dao.model.DashUserViewStatisticDO;
import us.zoom.infra.dao.model.UserViewStatisticDO;
import us.zoom.infra.dao.service.UserViewStatisticDAO;
import us.zoom.infra.enums.DashModuleEnum;

import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2024/01/04 14:55
 */
@Component
@Slf4j
public class UserViewStatisticHandler {

    private Logger monitorLog = LoggerFactory.getLogger("Monitor");
    private final static int FLUSH_INTERVAL = 3;

    @Autowired
    private UserViewStatisticDAO userViewStatisticDAO;

    private Cache<Triple<String, String, Integer>, Integer> userViewCache = CacheBuilder.newBuilder()
            .expireAfterWrite(FLUSH_INTERVAL, TimeUnit.MINUTES)
            .removalListener(notification -> {
                if (RemovalCause.EXPIRED.equals(notification.getCause()) && notification.getValue() != null) {
                    Triple<String, String, Integer> key = (Triple<String, String, Integer>) notification.getKey();
                    UserViewStatisticDO userViewStatisticDO = new UserViewStatisticDO(IdUtils.generateId(), key.getRight(), key.getLeft(), key.getMiddle(), (Integer) notification.getValue(), new Date(), null);
                    addUserViewStatistic(userViewStatisticDO);

                }
            }).build();

    public void userView(String userId, String relatedId, UserViewStatisticTypeEnum type, int delta) {
        if (delta <= 0 || StringUtils.isBlank(userId) || StringUtils.isBlank(relatedId)) {
            return;
        }
        Triple<String, String, Integer> triple = Triple.of(userId, relatedId, type.getValue());
        //make sure eviction operation executed
        userViewCache.cleanUp();
        Integer ifPresent = userViewCache.getIfPresent(triple);
        userViewCache.put(triple, ifPresent != null ? ifPresent.intValue() + delta : delta);

    }

    public void addUserViewStatistic(UserViewStatisticDO userViewStatisticDO) {
        userViewStatisticDAO.add(userViewStatisticDO);
    }

    public void deleteUserViewStatistic(String id) {
        userViewStatisticDAO.deleteById(id);
    }

    public List<UserViewStatisticDO> findByParam(Integer type, String userId, String relatedId, Integer pageIndex, Integer pageSize) {
        return userViewStatisticDAO.findByParam(type, userId, relatedId, pageIndex, pageSize);
    }

    public int getCountByParam(Integer type, String userId, String relatedId) {
        return userViewStatisticDAO.getCountByParam(type, userId, relatedId);
    }

    public UserViewStatisticDO getUserViewStatistic(String id) {
        return userViewStatisticDAO.getById(id);
    }


    public List<DashUserViewStatisticDO> getTopUserViewDash(String userId, String tenantId, int startIndex, int pageSize) {
        return userViewStatisticDAO.findTopUserViewDash(userId, tenantId, DashModuleEnum.GLOBAL.getCode(), startIndex, pageSize);
    }

    public int countTopUserViewDash(String userId, String tenantId) {
        return userViewStatisticDAO.countTopUserViewDash(userId, tenantId, DashModuleEnum.GLOBAL.getCode());
    }

    public List<DashUserViewStatisticDO> getRecentlyVisitDash(String userId, int startIndex, int pageSize) {
        return userViewStatisticDAO.findRecentlyVisitDash(userId, DashModuleEnum.GLOBAL.getCode(), startIndex, pageSize);
    }

    public int countRecentlyVisitDash(String userId) {
        return userViewStatisticDAO.countRecentlyVisitDash(userId, DashModuleEnum.GLOBAL.getCode());
    }

    public int deleteByTypeAndRelateId(Integer type, String relatedId) {
        return userViewStatisticDAO.deleteByTypeAndRelateId(type, relatedId);
    }
}
