package us.zoom.cube.site.biz;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import us.zoom.cube.site.lib.input.MetaDataTypeInput;

/**
 * @author: canyon.li
 * @date: 2024/01/09
 **/
public class MetadataServiceTest extends BaseTest {

    @Autowired
    private MetadataService metadataService;

    @Test
    public void createDataType() {

        MetaDataTypeInput dataTypeInput = new MetaDataTypeInput();
        dataTypeInput.setService("TXTGW");
        dataTypeInput.setDataType("CANYON_TEST2");
        metadataService.createDataType(dataTypeInput);

    }

    @Test
    public void deleteDataType() {
        MetaDataTypeInput dataTypeInput = new MetaDataTypeInput();
        dataTypeInput.setId(6445L);
        metadataService.deleteDataTypeById(dataTypeInput);
    }
}
