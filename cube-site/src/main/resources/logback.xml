<?xml version="1.0" encoding="UTF-8"?>
<configuration>

    <property resource="application.properties"/>
    <property name="TRACE" value="%replace([%X{trace_id:-}:%X{span_id:-}]){'\[:\]', ''}"/>
    <springProperty scope="context" name="LOG_PATH" source="log.filePath"/>
    <springProperty scope="context" name="APM_LOG_HOME" source="apm.log.home"/>

    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <!-- encoders are assigned the type
             ch.qos.logback.classic.encoder.PatternLayoutEncoder by default -->
        <encoder>
            <pattern>%boldGreen(%date{ISO8601}) ${TRACE} %highlight([%-5level]) %boldCyan([%file]) %boldCyan([%line])
                %yellow([%thread]) %boldMagenta(%logger{10}) %msg%n
            </pattern>
        </encoder>
    </appender>

    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH:-logs}/site.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH:-logs}/%d{yyyy-MM-dd,aux}/cube-site.%d{yyyy-MM-dd_HH}.%i.log.gz
            </fileNamePattern>
            <maxFileSize>${log.maxFileSize:-100MB}</maxFileSize>
            <maxHistory>30</maxHistory>
            <totalSizeCap>${log.totalSizeCap:-2GB}</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>%date{ISO8601} ${TRACE} [%level] [%file] [%M] [%line] [%thread] %logger{10} %msg%n</pattern>
        </encoder>
    </appender>

    <appender name="ERROR_LOG" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH:-logs}/site-error.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH:-logs}/%d{yyyy-MM-dd,aux}/cube-site-error.%d{yyyy-MM-dd_HH}.%i.log.gz
            </fileNamePattern>
            <maxFileSize>${log.maxFileSize:-100MB}</maxFileSize>
            <maxHistory>30</maxHistory>
            <totalSizeCap>${log.totalSizeCap:-2GB}</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>%date{ISO8601} ${TRACE} [%level] [%file] [%M] [%line] [%thread] %logger{10} %msg%n</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <appender name="MONITOR_LOG" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH:-logs}/cube-site-monitor.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH:-logs}/%d{yyyy-MM-dd,aux}/cube-site-monitor.%d{yyyy-MM-dd_HH}.%i.log.gz
            </fileNamePattern>
            <maxFileSize>${log.maxFileSize:-100MB}</maxFileSize>
            <maxHistory>30</maxHistory>
            <totalSizeCap>${log.totalSizeCap:-2GB}</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>%msg%n</pattern>
        </encoder>
    </appender>

    <appender name="ApmMonitorLog" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${APM_LOG_HOME:-logs}/metrics.log</file>
        <append>true</append>
        <immediateFlush>true</immediateFlush>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH:-logs}/%d{yyyy-MM-dd,aux}/apm-metrics.%d{yyyy-MM-dd_HH}.%i.log.gz
            </fileNamePattern>
            <maxFileSize>${log.maxFileSize:-100MB}</maxFileSize>
            <maxHistory>30</maxHistory>
            <totalSizeCap>${log.totalSizeCap:-2GB}</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <Pattern>%msg%n</Pattern>
        </encoder>
    </appender>

    <logger name="us.zoom.monitor.utils.MonitorLogger" level="TRACE" additivity="false">
        <appender-ref ref="ApmMonitorLog"/>
    </logger>

    <logger name="us.zoom.infra" level="info">
        <appender-ref ref="FILE"/>
        <appender-ref ref="ERROR_LOG"/>
    </logger>

    <!-- print out error msg when property is missing -->
    <logger name="org.springframework.boot.SpringApplication" level="info">
        <appender-ref ref="FILE"/>
        <appender-ref ref="ERROR_LOG"/>
    </logger>

    <logger name="us.zoom.cube.site" level="info">
        <appender-ref ref="FILE"/>
        <appender-ref ref="ERROR_LOG"/>
    </logger>

    <logger name="com.zaxxer" level="info">
        <appender-ref ref="FILE"/>
        <appender-ref ref="ERROR_LOG"/>
    </logger>

    <logger name="Monitor" level="TRACE" additivity="false">
        <appender-ref ref="MONITOR_LOG"/>
    </logger>

    <logger name="us.zoom.infra.clickhouse" level="error" additivity="false">
        <appender-ref ref="ERROR_LOG"/>
    </logger>

    <root level="error">
        <appender-ref ref="STDOUT"/>
    </root>
</configuration>