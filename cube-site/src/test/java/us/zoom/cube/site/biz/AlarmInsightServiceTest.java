package us.zoom.cube.site.biz;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import us.zoom.cube.site.biz.alarm.AlarmInsightService;
import us.zoom.cube.site.lib.input.alarm.insight.AlarmDetailInput;
import us.zoom.cube.site.lib.input.alarm.insight.AlarmRecordsInput;
import us.zoom.cube.site.lib.output.alarm.insight.AlarmDetailOutput;
import us.zoom.cube.site.lib.output.alarm.insight.AlarmNotificationOutput;
import us.zoom.cube.site.lib.output.alarm.insight.AlarmRecordsOutput;
import us.zoom.cube.lib.utils.JsonUtils;

import java.util.List;

/**
 * @author: canyon.li
 * @date: 2023/07/03
 **/
public class AlarmInsightServiceTest extends BaseTest {

    @Autowired
    AlarmInsightService alarmInsightService;

    @Test
    public void getAlarmRecordDetail() {
        AlarmDetailInput alarmDetailInput = new AlarmDetailInput();
        alarmDetailInput.setServiceId("39caa432-8f3f-4679-ad39-3f254a203be4");
        alarmDetailInput.setAlarmRecordId("f3eb064a31c821bfa10c3b2789e8b743");
        try {
            for (int i = 0; i < 5; i++) {
                AlarmDetailOutput output = alarmInsightService.getAlarmRecordDetail(alarmDetailInput);
                System.out.println(JsonUtils.toJsonString(output));
            }
        } catch (Exception e) {
            System.out.println(e.getMessage());

        }
    }
    @Test
    public void main() {
        getAlarmRecordNotifications();
        getSuppressedAlarmRecords();
    }

    @Test
    public void getAlarmRecordNotifications() {
        AlarmDetailInput alarmDetailInput = new AlarmDetailInput();
        alarmDetailInput.setServiceId("39caa432-8f3f-4679-ad39-3f254a203be4");
        alarmDetailInput.setAlarmRecordId("f3eb064a31c821bfa10c3b2789e8b743");
        try {
            for (int i = 0; i < 5; i++) {
                List<AlarmNotificationOutput> output = alarmInsightService.getAlarmRecordNotifications(alarmDetailInput);
                System.out.println(JsonUtils.toJsonString(output));
            }
        } catch (Exception e) {
            System.out.println(e.getMessage());

        }
    }

    @Test
    public void getSuppressedAlarmRecords() {

        AlarmRecordsInput alarmRecordsInput = new AlarmRecordsInput();
        alarmRecordsInput.setPreRecordId("72ebd3b099d1e0b31ace23908345e551");
        alarmRecordsInput.setServiceName("Canyon_test_1");
        alarmRecordsInput.setPageIndex(1);
        alarmRecordsInput.setPageSize(3);
        try {
            for (int i = 0; i < 5; i++) {
                AlarmRecordsOutput output = alarmInsightService.getSuppressedAlarmRecords(alarmRecordsInput);
                System.out.println(JsonUtils.toJsonString(output));
            }

        } catch (Exception e) {
            System.out.println(e.getMessage());

        }
    }
}
