package us.zoom.cube.site.lib.output.migration;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class MetricsItemEntry {

    private String sourceMetricsName;

    private String targetMetricsName;

    private MetricsTagCheckResult tagCheckResult;

    private MetricsFieldValueResult fieldValueResult;

    public MetricsItemEntry(String sourceMetricsName, String targetMetricsName) {
        this.sourceMetricsName = sourceMetricsName;
        this.targetMetricsName = targetMetricsName;
    }
}
