package us.zoom.cube.site.core.maintaince;

import com.zoom.op.monitor.domain.alarm.AlarmDefinition;
import com.zoom.op.monitor.domain.alarm.Channel;
import com.zoom.op.monitor.domain.alarm.ChannelParameter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import us.zoom.cube.site.biz.BiAlarmsImportService;
import us.zoom.cube.site.biz.DataParserExportService;
import us.zoom.cube.site.core.*;
import us.zoom.cube.site.lib.biport.BiImportRelationInfo;
import us.zoom.cube.site.lib.input.MetricsMigrationInput;
import us.zoom.cube.site.lib.input.dataparser.StringJsonInput;
import us.zoom.infra.dao.model.*;
import us.zoom.infra.thread.ThreadLocalStore;
import us.zoom.infra.utils.IdUtils;
import us.zoom.infra.utils.Instance;
import us.zoom.cube.lib.utils.JsonUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class MetricsMigrateToServiceMonitorHandler {

    @Autowired
    private TenantHandler tenantHandler;

    @Autowired
    private MetricsHandler metricsHandler;

    @Autowired
    private AlarmHandler alarmHandler;

    @Autowired
    private DataParserExportService dataParserExportService;

    @Autowired
    private BiAlarmsImportService biAlarmsImportService;

    @Autowired
    private ChannelHandler channelHandler;

    @Autowired
    private CardHandler cardHandler;

    @Autowired
    private DashHandler dashHandler;


    private static final String serviceMonitor = "service_monitor";

    private static final String metricsNamePattern ="\"metricsName\"";

    private static final String migrationSuffix = " Migration";

    private  static final int metricsNamePatternLen = metricsNamePattern.length();

    public List<String> migrateFromServiceMonitor(MetricsMigrationInput metricsMigrationInput) {
        TenantDO serviceMonitorTenant = tenantHandler.getTenantByNameInLowerCaseFromCache(serviceMonitor.toLowerCase());
        Assert.isTrue(null != serviceMonitorTenant,"can't find service_monitor!");
        TenantDO destService = tenantHandler.getTenantByNameInLowerCaseFromCache(metricsMigrationInput.getDestServiceName().toLowerCase());
        Assert.isTrue(null != destService,"can't find dest service : "+ metricsMigrationInput.getDestServiceName());
        List<String> result = new ArrayList<>();
        long start = System.currentTimeMillis();
        List<MetricsDO> filteredMetrics = getNeedMigrateMetrics(metricsMigrationInput, serviceMonitorTenant);
        result.add("...... getNeedMigrateMetrics cost is "+(System.currentTimeMillis() - start));

        if(CollectionUtils.isEmpty(filteredMetrics)){
            result.add("there is no metrics need to migrate");
            return result;
        }
        start = System.currentTimeMillis();
        List<MetricsDO> destMetricDOs=  metricsHandler.getMetricsByTenant(destService.getId());
        result.add("...... getMetricsByTenant cost is "+(System.currentTimeMillis() - start));

        if(CollectionUtils.isEmpty(destMetricDOs)){
            result.add("there is no dest metrics for  dest service ");
            return result;
        }

        List<MetricsDO> needMigrateMetrics = new ArrayList<>();
        //Map<metricsName,MetricsDO>
        Map<String,MetricsDO> nameMetricsForDestMap = destMetricDOs.stream().collect(Collectors.toMap(item->item.getMetricsName(),e->e));
        for (MetricsDO needMigrateMetric : filteredMetrics) {
            String newMetricsName = getNewMetricsName(needMigrateMetric,metricsMigrationInput);
            MetricsDO destMetrics = nameMetricsForDestMap.get(newMetricsName);
            if (destMetrics != null) {
                needMigrateMetrics.add(needMigrateMetric);
            } else {
                result.add("There is no such metrics "+newMetricsName);
            }
        }

        result.add("Need to migrate metrics count is "+needMigrateMetrics.size());

        start = System.currentTimeMillis();
        Map<String,Set<AlarmDefinition>> metricIdAlarmsMap = getMetricsAlarmsMap(needMigrateMetrics);
        result.add("...... getMetricsAlarmsMap cost is "+(System.currentTimeMillis() - start));

        start = System.currentTimeMillis();
        //create related channel
        createNotifyChannel(metricIdAlarmsMap,destService,result);
        result.add("...... createNotifyChannel cost is "+(System.currentTimeMillis() - start));

        // just keep the card that contains service_monitor keyword
//        start = System.currentTimeMillis();
//        List<CardDO> allCard = cardHandler.listAllCard();
//        List<CardDO> cardsContainsSuchService = Instance.ofNullable(allCard).stream().filter(item-> StringUtils.contains(item.getConfigs(),serviceMonitor)).collect(Collectors.toList());
//        result.add("...... listAllCard cost is "+(System.currentTimeMillis() - start));


        //Set<CardDO> needMigrateCards = new HashSet<>();
        start = System.currentTimeMillis();
        for(MetricsDO metricsDO:needMigrateMetrics){
            String newMetricsName = getNewMetricsName(metricsDO,metricsMigrationInput);
            MetricsDO destMetrics = nameMetricsForDestMap.get(newMetricsName);
//            if(null == destMetrics){
//                result.add("There is no such metrics "+newMetricsName);
//            }else{
                result.add("------------ migrate for metrics "+newMetricsName+" ----------");
                Set<AlarmDefinition> alarmDefinitionSet = metricIdAlarmsMap.get(metricsDO.getId());
                if(CollectionUtils.isEmpty(alarmDefinitionSet)){
                    result.add("there is no alarm !");
                }else{
                    migrateAlarm(serviceMonitorTenant,destService, metricsDO,destMetrics, alarmDefinitionSet,result);
                }

//                for(CardDO cardDO:cardsContainsSuchService){
//                    if(StringUtils.contains(cardDO.getConfigs(),metricsDO.getMetricsName())){
//                        needMigrateCards.add(cardDO);
//                        cardDO.setConfigs(StringUtils.replace(cardDO.getConfigs(),metricsDO.getMetricsName(),newMetricsName));
//                        cardDO.setConfigs(StringUtils.replace(cardDO.getConfigs(),serviceMonitor,destService.getName()));
//                    }
//                }
            //}
        }
        result.add("...... migrateAlarm cost is "+(System.currentTimeMillis() - start));
//
//        start = System.currentTimeMillis();
//        migrateDashAndCard(needMigrateCards,destService,result,needMigrateMetrics,metricsMigrationInput, allCard);
//        result.add("...... migrateDashAndCard cost is "+(System.currentTimeMillis() - start));
        return result;
    }


    private static String getNewMetricsName(MetricsDO metricsDO, MetricsMigrationInput metricsMigrationInput) {
        String newMetricsName = metricsDO.getMetricsName().replaceAll("\\.", "_").trim();
        if(StringUtils.isBlank(metricsMigrationInput.getNewMetricsCmpPrefix())){
            return newMetricsName;
        }
        if(newMetricsName.startsWith(metricsMigrationInput.getNewMetricsCmpPrefix())){
            newMetricsName = newMetricsName.substring(newMetricsName.indexOf(metricsMigrationInput.getNewMetricsCmpPrefix())+metricsMigrationInput.getNewMetricsCmpPrefix().length());
        }
        return newMetricsName;
    }

    private void replaceCardIdForDash(List<CardDO> tooAddCards, Map<String, String> cardNewIdOldIdMap,DashDO dashDO,List<String> result) {
        if(CollectionUtils.isEmpty(tooAddCards)){
            return;
        }
        for(CardDO cardDO : tooAddCards){
            String oldCardId = cardNewIdOldIdMap.get(cardDO.getId());
            if(StringUtils.isBlank(oldCardId)){
                result.add("Fail , old card is blank , dash name "+dashDO.getName()+" , card name "+cardDO.getName());
                continue;
            }
            String configs =  StringUtils.replace(dashDO.getConfigs(),oldCardId,cardDO.getId());
            dashDO.setConfigs(configs);
        }
    }

    @NotNull
    private static List<CardDO> getCardDOS(Map<String, CardDO> idCardMap, Map<String, Set<String>> dashIdCardIdsMap, String sourceDashId, Map<String, String> cardNewIdOldIdMap) {
        List<CardDO> tooAddCards = new ArrayList<>();
        Set<String> cardIds = dashIdCardIdsMap.get(sourceDashId);
        if(CollectionUtils.isNotEmpty(cardIds)){
            cardIds.forEach(cardId->{
                CardDO toAddCard  = idCardMap.get(cardId);
                String oldId = toAddCard.getId();
                toAddCard.setId(IdUtils.generateId());
                tooAddCards.add(toAddCard);
                cardNewIdOldIdMap.put(toAddCard.getId(),oldId);
            });
        }
        return tooAddCards;
    }

    private void replaceDashConfigs(DashDO dashDO, String serviceMonitor, TenantDO destService, List<MetricsDO> needMigrateMetrics,MetricsMigrationInput metricsMigrationInput) {

        dashDO.setConfigs(StringUtils.replace(dashDO.getConfigs(),serviceMonitor,destService.getName()));
        for(MetricsDO metricsDO : needMigrateMetrics){
            String newMetricsName = getNewMetricsName(metricsDO, metricsMigrationInput);
            String newConfig = StringUtils.replace(dashDO.getConfigs(),metricsDO.getMetricsName(),newMetricsName);
            dashDO.setConfigs(newConfig);
        }
    }


    private void createNotifyChannel(Map<String, Set<AlarmDefinition>> metricIdAlarmsMap, TenantDO destService, List<String> result) {
        if(MapUtils.isEmpty(metricIdAlarmsMap)){
            result.add("there is no alarm to migrate");
            return;
        }
        Set<String> alarmIds = new HashSet<>();
        metricIdAlarmsMap.values().forEach(item->alarmIds.addAll(item.stream().map(alarmDefinition->alarmDefinition.getId()).collect(Collectors.toSet())));
        if(CollectionUtils.isEmpty(alarmIds)){
            return;
        }

        //get channel not created in the dest service
        List<String> channelIds = alarmHandler.listChannelIdsByAlarmIds(new ArrayList<>(alarmIds));
        channelIds = Instance.ofNullable(channelIds).stream().distinct().collect(Collectors.toList());
        List<Channel> needAddChannels = channelHandler.listChannelNotExistInTenantMatchByName(channelIds, destService.getId());
        if(CollectionUtils.isEmpty(needAddChannels)){
            result.add("all channel have created");
            return;
        }

        List<Channel> channelList = new ArrayList<>();
        for(Channel channel:needAddChannels){
            Channel newChannel = copyChannel(channel);
            newChannel.setId(IdUtils.generateId());
            newChannel.setTenantId(destService.getId());
            Instance.ofNullable(newChannel.getParameters()).forEach(item->item.setId(IdUtils.generateId()));
            channelList.add(newChannel);
        }
        channelHandler.batchSave(channelList);
        result.add("add im channel success,["+channelList.stream().map(item->item.getName()).collect(Collectors.joining(","))+"]");
    }

    private Channel copyChannel(Channel source) {
        Channel target = new Channel();
        BeanUtils.copyProperties(source, target);
        List<ChannelParameter> channelParameterList = new ArrayList<>();
        if (source.getParameters() != null) {
            for (ChannelParameter parameter : source.getParameters()) {
                ChannelParameter cp = new ChannelParameter();
                BeanUtils.copyProperties(parameter, cp);
                channelParameterList.add(cp);
            }
        }
        target.setParameters(channelParameterList);
        return target;
    }

    private void migrateAlarm(TenantDO serviceMonitorTenant, TenantDO destService, MetricsDO metricsDO,MetricsDO destMetrics , Set<AlarmDefinition> alarmDefinitionSet, List<String> result) {
        for(AlarmDefinition alarmDefinition : alarmDefinitionSet){
            try{
                String alarmExpJson =   dataParserExportService.exportAlarmWithAll(serviceMonitorTenant.getId(),alarmDefinition.getId());
                alarmExpJson = replaceMetricsName(alarmExpJson,metricsDO.getMetricsName(), destMetrics.getMetricsName(),result);
                StringJsonInput jsonInput = new StringJsonInput();
                ThreadLocalStore.setTenantInfoLocal(destService.getId());
                ThreadLocalStore.setApiPath("/api/import/alarms");
                jsonInput.setJson(alarmExpJson);
                jsonInput.setIncremental(true);
                BiImportRelationInfo importResult =  biAlarmsImportService.importJson(jsonInput);
                result.add("import alarm success for "+alarmDefinition.getName()+"  : "+ JsonUtils.toJsonStringIgnoreExp(importResult));
            }catch (Exception e){
                result.add("import alarm fail : "+ alarmDefinition.getName());
                log.error("migrate for service:{} , metrics:{} ,alarm:{} fail ", destService.getName(), metricsDO.getMetricsName(),alarmDefinition.getName(),e);
            }

        }
    }


    private static  String replaceMetricsName(String alarmExpJson,String oldMetricsName,String newMetricsName,List<String> result) {
        int index = alarmExpJson.indexOf(oldMetricsName);
        if(index == -1){
            result.add("replace metrics name fail for "+newMetricsName);
            return  "";
        }
        return  alarmExpJson.replaceAll(oldMetricsName,newMetricsName);
    }

    private Map<String, Set<AlarmDefinition>> getMetricsAlarmsMap(List<MetricsDO> needMigrateMetrics) {
        List<AlarmDefinition> alarmIds =  alarmHandler.listAlarmsByMetricsIdList(needMigrateMetrics.stream().map(item->item.getId()).collect(Collectors.toList()));
        return Instance.ofNullable(alarmIds).stream().collect(Collectors.groupingBy(item->item.getMetricId(),Collectors.toSet()));

    }


    public List<MetricsDO> getNeedMigrateMetrics(MetricsMigrationInput metricsMigrationInput, TenantDO serviceMonitorTenant) {
        List<MetricsDO> needMigrateMetrics = new ArrayList<>();
        List<MetricsDO> metricsDOS =  metricsHandler.getMetricsByTenant(serviceMonitorTenant.getId());
        if(CollectionUtils.isEmpty(metricsDOS)){
            return Collections.emptyList();
        }

        Set<String> metricsNameNoRepeat= new HashSet<>();
        for(MetricsDO metricsDO: metricsDOS){
            if(CollectionUtils.isNotEmpty(metricsMigrationInput.getSrcMetricsNameFullMatch())
            && metricsMigrationInput.getSrcMetricsNameFullMatch().contains(metricsDO.getMetricsName())
            && !metricsNameNoRepeat.contains(metricsDO.getMetricsName())){
                needMigrateMetrics.add(metricsDO);
                metricsNameNoRepeat.add(metricsDO.getMetricsName());
            }

            if(CollectionUtils.isNotEmpty(metricsMigrationInput.getSrcMetricsNameStartWith())){
                for(String startWithMathMetricsName: metricsMigrationInput.getSrcMetricsNameStartWith()){
                    if(metricsDO.getMetricsName().startsWith(startWithMathMetricsName)
                    && !metricsNameNoRepeat.contains(metricsDO.getMetricsName())){
                        needMigrateMetrics.add(metricsDO);
                        metricsNameNoRepeat.add(metricsDO.getMetricsName());
                        break;
                    }
                }
            }
        }
        return needMigrateMetrics;
    }
}
