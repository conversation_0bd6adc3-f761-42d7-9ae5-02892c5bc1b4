package us.zoom.cube.site.lib.output.catalog;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class IntegrationStatus {
    private String component;
    private boolean enabled;
    private String hint;
    private boolean hidden;
    private List<IntegrationStatus> subComponents;
}
