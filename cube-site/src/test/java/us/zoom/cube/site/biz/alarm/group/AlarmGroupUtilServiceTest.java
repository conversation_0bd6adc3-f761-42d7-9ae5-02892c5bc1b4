package us.zoom.cube.site.biz.alarm.group;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.Map;

import static us.zoom.infra.utils.AlarmSilenceConstants.SUCCESS;

public class AlarmGroupUtilServiceTest {


    @Test
    public void checkAviatorExpression() {
        AlarmGroupUtilService alarmGroupUtilService = new AlarmGroupUtilService();
        Map<String, Object> map = new HashMap<>();
        map.put("ip", "1");
        map.put("cluster", "1");
        Map<String, Object> result = alarmGroupUtilService.checkAviatorExpression("1 == 1", map);
        boolean b = (Boolean) result.get(SUCCESS);

        Map<String, Object> result1 = alarmGroupUtilService.checkAviatorExpression("string.contains(message,'NullPointerException')", map);
        boolean b1 = (Boolean) result1.get(SUCCESS);
        Assertions.assertTrue(b);
        Assertions.assertFalse(b1);
    }
}