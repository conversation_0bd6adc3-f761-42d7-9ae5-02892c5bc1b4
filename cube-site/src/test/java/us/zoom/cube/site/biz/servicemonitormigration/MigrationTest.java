package us.zoom.cube.site.biz.servicemonitormigration;

import com.google.common.collect.Lists;
import com.zoom.op.monitor.domain.alarm.Channel;
import com.zoom.op.monitor.domain.alarm.ChannelParameter;
import org.junit.jupiter.api.Test;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: tobey.zhu
 * @date: /2023/10/10
 * @description:
 */
public class MigrationTest {

    @Test
    public void test() {
        Channel channel = new Channel();
        channel.setCreator("tobey");
        ChannelParameter channelParameter = new ChannelParameter();
        channelParameter.setId("1");
        channelParameter.setName("name");
        channelParameter.setValue("value");
        channel.setParameters(Lists.newArrayList(channelParameter));
        Channel channel2 = new Channel();
        BeanUtils.copyProperties(channel, channel2);
        List<ChannelParameter> channelParameterList = new ArrayList<>();
        for (ChannelParameter parameter : channel.getParameters()) {
            ChannelParameter channelParameter1 = new ChannelParameter();
            BeanUtils.copyProperties(parameter, channelParameter1);
            channelParameterList.add(channelParameter1);
        }
        channel2.setParameters(channelParameterList);
        channel.getParameters().get(0).setValue("value2");
        channel.setCreator("tobey2");
        assert channel2.getCreator().equals("tobey");
    }
}
