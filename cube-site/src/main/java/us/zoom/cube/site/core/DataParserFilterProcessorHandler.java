package us.zoom.cube.site.core;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import us.zoom.infra.dao.model.DataParserFilterProcessorDO;
import us.zoom.infra.dao.service.DataParserFilterProcessorDAO;

import java.util.List;

@Component
public class DataParserFilterProcessorHandler {


    @Autowired
    private DataParserFilterProcessorDAO dataParserFilterProcessorDAO;



    public void addFilterProcessor(DataParserFilterProcessorDO filterProcessorDO) {
        Assert.notNull(filterProcessorDO,"filter processor is null !");
        this.dataParserFilterProcessorDAO.add(filterProcessorDO);
    }

    public void editFilterProcessor(DataParserFilterProcessorDO filterProcessorDO) {
        Assert.notNull(filterProcessorDO,"filter processor   is null !");
        dataParserFilterProcessorDAO.edit(filterProcessorDO);
    }

    public void delFilterProcessor(String id) {
        Assert.notNull(id,"id is null !");
        dataParserFilterProcessorDAO.del(id);
    }

    public void delFilterProcessorByDataParserPipelineId(String dataParserPipelineId) {
        Assert.notNull(dataParserPipelineId,"dataParserPipelineId is null !");
        dataParserFilterProcessorDAO.delByDataParserPipelineId(dataParserPipelineId);
    }

    public DataParserFilterProcessorDO getFilterProcessorById(String id) {
        Assert.notNull(id,"id is null !");
        return dataParserFilterProcessorDAO.getFilterProcessorById(id);
    }

    public List<DataParserFilterProcessorDO> listByPipelineIds(List<String> pipeLineIds) {
        return  dataParserFilterProcessorDAO.listByPipelineIds(pipeLineIds);
    }

    public void delFilterProcessorByPipelineIds(List<String> pipelineIds) {
        if(CollectionUtils.isEmpty(pipelineIds)){
            return;
        }
        dataParserFilterProcessorDAO.delByPipelindIds(pipelineIds);
    }
}
