package us.zoom.cube.site.lib.output.metric;


/**
 * <AUTHOR>
 */
public class ViewComponentOut {

    private String id;
    private String title;
    /**
     *  ,ineChart、cylinderDeploy,cylinderScatter
     */

    private String type;
    /**
     * ID
     */
    private String  metricId;
    private Integer order;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getMetricId() {
        return metricId;
    }

    public void setMetricId(String metricId) {
        this.metricId = metricId;
    }

    public Integer getOrder() {
        return order;
    }

    public void setOrder(Integer order) {
        this.order = order;
    }
}
