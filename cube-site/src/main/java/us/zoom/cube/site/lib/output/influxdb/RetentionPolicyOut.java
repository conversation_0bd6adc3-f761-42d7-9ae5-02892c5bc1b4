package us.zoom.cube.site.lib.output.influxdb;

/**
 * <AUTHOR>
 * @date 2021/9/24 7:16 PM
 */
@Deprecated
public class RetentionPolicyOut {

    private String retentionPolicyName;

    private int durationInDays;

    private int shardDurationInDays;

    private int replicaN;

    private boolean isDefault;

    public String getRetentionPolicyName() {
        return retentionPolicyName;
    }

    public void setRetentionPolicyName(String retentionPolicyName) {
        this.retentionPolicyName = retentionPolicyName;
    }

    public int getDurationInDays() {
        return durationInDays;
    }

    public void setDurationInDays(int durationInDays) {
        this.durationInDays = durationInDays;
    }

    public int getShardDurationInDays() {
        return shardDurationInDays;
    }

    public void setShardDurationInDays(int shardDurationInDays) {
        this.shardDurationInDays = shardDurationInDays;
    }

    public int getReplicaN() {
        return replicaN;
    }

    public void setReplicaN(int replicaN) {
        this.replicaN = replicaN;
    }

    public boolean getIsDefault() {
        return isDefault;
    }

    public void setIsDefault(boolean isDefault) {
        this.isDefault = isDefault;
    }
}
