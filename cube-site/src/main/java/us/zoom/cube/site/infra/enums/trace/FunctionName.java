package us.zoom.cube.site.infra.enums.trace;

import org.apache.commons.lang3.StringUtils;

/**
 * @author: eason.jia
 * @date: 2024/8/19
 */
public enum FunctionName {
    CUT_OFF_MIN("cutOffMin"),
    CUT_OFF_MAX("cutOffMax"),
    CLAMP_MIN("clampMin"),
    CLAMP_MAX("clampMax"),
    ABSOLUTE("absolute"),
    LOG2("log2"),
    LOG10("log10"),
    CUM_SUM("cumSum"),
    EWMA3("ewma3"),
    EWMA5("ewma5"),
    EWMA7("ewma7"),
    MEDIAN3("median3"),
    MEDIAN5("median5"),
    MEDIAN7("median7"),
    TIME_SHIFT("timeShift");

    private final String name;

    FunctionName(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public static FunctionName from(String functionName) {
        for (FunctionName operator : FunctionName.values()) {
            if (StringUtils.equals(operator.getName(), functionName)) {
                return operator;
            }
        }
        throw new IllegalArgumentException("illegal functionName: " + functionName);
    }

    public static boolean validate(String functionName) {
        for (FunctionName operator : FunctionName.values()) {
            if (StringUtils.equalsIgnoreCase(operator.getName(), functionName)) {
                return true;
            }
        }
        return false;
    }
}
