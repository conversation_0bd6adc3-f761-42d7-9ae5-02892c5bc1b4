server.ssl.enabled=false
server.tomcat.min-spare-threads=100
server.tomcat.max-threads=800
server.tomcat.accept-count=1000
#lock config
resource.lock.inteval=20
#log Config
log.sampling.count.info=10000
log.sampling.count.warn=1000
log.sampling.count.error=1000
#mysql
dataSource.url=**************************************************************************************************************************************************************************************************
# To set in CSMS
#cube.dataSource.username=
# To set in CSMS
#cube.dataSource.password=
dataSourceStandby.url=**************************************************************************************************************************************************************************************************
# To set in CSMS
#cube.dataSource.username=
# To set in CSMS
#cube.dataSource.password=
dataSource.csms_rotate_username_key= cube.dataSource.username
dataSource.csms_rotate_password_key= cube.dataSource.password
dataSourceStandby.csms_rotate_username_key= cube.dataSourceStandby.username
dataSourceStandby.csms_rotate_password_key= cube.dataSourceStandby.password

dataSource.maxActive=6

#front-source path
spring.web.resources.static-locations=file:/home/<USER>/cube-front/static

test.kafka.connectivity.timeout=6

#HttpClient
httpClient.connection.pool.maxTotal=200
httpClient.connection.pool.maxPerRoute=20
httpClient.connection.request.timeout=2000
httpClient.connect.timeout=2000
httpClient.socket.timeout=10000

# self monitor IM alarm
self.monitor.im.url=https://inbots.zoom.us/incoming/hook/B2ZHKjYJu6XhveD2LpdA_R4i?format=fields
# To set in CSMS
#self.monitor.im.authorization=

#OKTA authentication, personal developer account
okta.oauth2.issuer=https://dev-448353.oktapreview.com/oauth2/default
# To set in CSMS
okta.oauth2.client-id=
# To set in CSMS
okta.oauth2.client-secret=
okta.oauth2.redirect-uri=/authorization-code/callback

logout.url=https://dev-448353.oktapreview.com/app/UserHome
#change to/** release all
site.oauth2.filter-url=/cfg/**
site.oauth2.filter-url2=/selfmonitor/**

log.filePath=cube-site/logs
apm.log.home=/data/logs/zoom_middleware/apm/Infra_Monitor_Cube-Site
monitor.executorsReplace.enabled=false

spring.jpa.show-sql= true
spring.jpa.hibernate.ddl-auto= none
spring.jpa.hibernate.naming.implicit-strategy=org.springframework.boot.orm.jpa.hibernate.SpringImplicitNamingStrategy
spring.jpa.hibernate.naming.physical-strategy=org.hibernate.boot.model.naming.CamelCaseToUnderscoresNamingStrategy
spring.jpa.database= MYSQL
spring.jpa.properties.hibernate.enable_lazy_load_no_trans= true

#spring.session
#spring.session REDIS or JDBC
spring.session.store-type=JDBC

# auth config
auth.issuer.name=cube
auth.audience.name=hub
# To set in CSMS
#auth.secret.key=

auth.common.audience.name=cube

#true enable, false disable
csms.enable=true
#csms domain
csms.endpoints=https://csmsdev.zoomdev.us
#config in csms console path
csms.app.path=ds01/cube

#CORS configs
cors.allowed.url.okta=dev-448353.oktapreview.com

dataparser.must.admin=no

offline.send.service=false

calc.template.default.unit=template_default_unit
site.okta.redirect-uri=https://cube-perfout.zoomdev.us:8443/oauth2/authorization/okta

cube.dataSource.clickhouse.servers=10.21.5.18
cube.dataSource.clickhouse.username=zoom_cube
cube.clickhouse.flush.interval.millsecond=5000
cube.clickhouse.flush.max.batch.size=100000
cube.clickhouse.thread.pool.size=20

cube.incident.url.alert=https://cube-perfout.zoomdev.us:8443/

async.mq.admin.endpoint=https://asyncmq.zoomdev.us
async.mq.admin.username=app_cube_admin

cmdb.login.url = https://devops-agw-sz.zoomdev.us:6443/gateway/api/v1/user/login
cmdb.server.query.url = https://devops-agw-sz.zoomdev.us:6443/cmdb/api/v2/server/batch
cmdb.device.query.url = https://devops-agw-sz.zoomdev.us:6443/cmdb/api/v1/netdeviceinfo
cmdb.tor.topo.query.url = https://devops-agw-sz.zoomdev.us:6443/cmdb/api/v1/netdeviceinfo/tortopo
cmdb.ca.file.path = /home/<USER>/cube-site/ca.crt
cmdb.server.topic = devops_watch_cmdb_server_v1
cmdb.device.topic = devops_watch_cmdb_netdeviceinfosystemchassis

# probe(E2E)
cube.scheduler.url=https://cube-schedule-perf.zoomdev.us
scheduler.job.group.name=cube_probe
scheduler.job.topic.name=cube_probe_scheduler_job

probe.metrics.service.name=cube_probe
probe.metrics.table.name=cube_probe.probe_task_metrics

probe.task.file.path=/tmp/probe
capacity.distributedLock.type=capacity_assigner

alarm.async.mq.username=app_cube

cube.template.endpoint.url=https://inbots.zoom.us/incoming/hook/l3mLXxaohl6DMdoq0dvOm_Ca?format=fields

alarm.pii-redirect-url.prefix=/alarm-redirect/dashboard

# agent heart beat
agent.heart.beat.topic = us_agent_heart_beat_forward_cube
agent.heart.beat.consumer.group = cube-group-perf
agent.heart.check.topic = us_agent_heart_check
hub.heart.check.topic = us_hub_heart_check
heart.check.consumer.group = cube-group-perf

cube.dataflow.alert.endpoint.url=https://inbots.zoom.us/incoming/hook/-lSSSXrZjGnaHo1Lab6_OgrU?format=fields


async.file.url=https://file.zoomdev.us

host.detail.info.database=Infra_Monitor_Cube_Site
webop.api.url={"integration":"https://devop-integration.zoomdev.us:8443","ds01":"https://ds01op.zoomdev.us:8443","dev":"https://devop.zoomdev.us:8443"}

asyncmq.csms.key.listen.list=async.mq.admin.password,async.mq.password,alarm.async.mq.password
cube.clickhouse.manager.backend.url=http://**************:8809


s3.probe.output.region=ap-southeast-1
s3.probe.output.dir=s3://zoomdev-op/release/web_arch/cube-probe/output/

cube.env=us01
s3.open.env=us01
agent.consumer.env=us01

spring.data.redis.timeout=1000
spring.data.redis.ssl.enabled=true
spring.data.redis.cluster.nodes=clustercfg.ds01-cube-etl-redis.rgsd8f.use1.cache.amazonaws.com:6379

webhooks.api.endpoint=https://appsnws.zoomdev.us
xms.api.endpoint=https://bxmppapi.zoomdev.us
webop.api.endpoint=https://devop.zoomdev.us:8443