package us.zoom.cube.site.lib.input;


import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

/**
 * @Author: luis.zheng
 * @Date: 2021/8/24 11:10
 */
public class AsyncMqQueueInputTest {
    private AsyncMqQueueInput input;

    @Test
    public void testCheck() {
        input = new AsyncMqQueueInput();
        input.setDataParserId(null);
        IllegalArgumentException exception = Assertions.assertThrows(IllegalArgumentException.class, () ->
                input.check());
        Assertions.assertEquals("dataParserId is blank", exception.getMessage());
        input.setDataParserId("");
        exception = Assertions.assertThrows(IllegalArgumentException.class, () ->
                input.check());
        Assertions.assertEquals("dataParserId is blank", exception.getMessage());

        input.setDataParserId("dataparserid");
        input.setAsyncmqClusterId("");
        exception = Assertions.assertThrows(IllegalArgumentException.class, () ->
                input.check());
        Assertions.assertEquals("asyncmqClusterId is blank", exception.getMessage());
        input.setAsyncmqClusterId(null);
        exception = Assertions.assertThrows(IllegalArgumentException.class, () ->
                input.check());
        Assertions.assertEquals("asyncmqClusterId is blank", exception.getMessage());

        input.setAsyncmqClusterId("setAsyncmqClusterId");
        input.setTopic(null);
        exception = Assertions.assertThrows(IllegalArgumentException.class, () ->
                input.check());
        Assertions.assertEquals("topic is blank", exception.getMessage());

        input.setTopic("topic");
        input.setSourceType(3);
        exception = Assertions.assertThrows(IllegalArgumentException.class, () ->
                input.check());
        Assertions.assertEquals("sourceType is null", exception.getMessage());
        input.setSourceType(null);
        exception = Assertions.assertThrows(IllegalArgumentException.class, () ->
                input.check());
        Assertions.assertEquals("sourceType is null", exception.getMessage());
        input.setSourceType(0);
    }
}
