package us.zoom.cube.site.core;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import us.zoom.infra.dao.model.InstanceMessagingChannelDO;
import us.zoom.infra.dao.service.InstanceMessagingChannelDAO;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2020/9/4 10:10 AM
 */
@Service
@Slf4j
public class InstanceMessagingHandler {

    @Autowired
    private InstanceMessagingChannelDAO instanceMessagingChannelDAO;

    @Autowired
    private RsaService rsaService;

    public List<InstanceMessagingChannelDO> listAllForTenant(String tenantId, String name) {
        return instanceMessagingChannelDAO.listAllForTenant(tenantId, name);
    }

    public List<InstanceMessagingChannelDO> findByChannelNameLike(String name, String tenantId, int pageIndex, int pageSize) {
        return instanceMessagingChannelDAO.findByChannelNameLike(name, tenantId, pageSize * (pageIndex - 1), pageSize);
    }

    public int getCountByChannelNameLike(String name, String tenantId) {
        return instanceMessagingChannelDAO.getCountByNameLike(name, Lists.newArrayList(tenantId));
    }
    public boolean hasSameChannelName(String name, List<String> tenantIds) {
        return instanceMessagingChannelDAO.getCountByName(name, tenantIds) > 0;
    }

    public void addChannel(InstanceMessagingChannelDO instanceMessagingChannelDO) {
        instanceMessagingChannelDAO.addChannel(instanceMessagingChannelDO);
    }

    public InstanceMessagingChannelDO getChannelById(String id) {
        return instanceMessagingChannelDAO.getChannelById(id);
    }

    public void editChannel(InstanceMessagingChannelDO instanceMessagingChannelDO) {
        instanceMessagingChannelDAO.editChannel(instanceMessagingChannelDO);
    }

    public void deleteChannelById(String id) {
        instanceMessagingChannelDAO.deleteById(id);
    }

    public void decryptChannelToken(List<InstanceMessagingChannelDO> instanceMessagingChannelDOS) {
        if (CollectionUtils.isEmpty(instanceMessagingChannelDOS)) {
            return;
        }
        for (InstanceMessagingChannelDO instanceMessagingChannelDO : instanceMessagingChannelDOS) {
            String authorizationEncrypted = instanceMessagingChannelDO.getAuthorizationEncrypted();
            try {
                String authorization = rsaService.decrypt(authorizationEncrypted);
                instanceMessagingChannelDO.setAuthorization(authorization);
            } catch (Exception e) {
                log.error("Decrypt IM token error! ", e);
            }
        }
    }
}
