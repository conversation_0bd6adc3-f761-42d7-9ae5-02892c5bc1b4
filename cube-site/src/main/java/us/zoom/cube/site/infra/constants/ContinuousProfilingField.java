package us.zoom.cube.site.infra.constants;

/**
 * @author: <PERSON><PERSON> Ding
 * @date: 2024/2/27 17:37
 * @desc:
 */
public class ContinuousProfilingField {

    public static final String TIME = "time";

    public static final String APP_NAME = "appName";

    public static final String STACK = "stack";

    public static final String CPU_TIME = "cpuTime";

    public static final String HOST = "host";

    public static final String IP = "ip";

    public static final String IP_PUBLIC = "ipPublic";

    public static final String INSTANCE_ID = "instanceId";

    public static final String PID = "pid";

    public static final String PROCESS_NAME = "processName";

    public static final String CLUSTER_ID = "clusterId";

    public static final String REGION_ID = "regionId";

    public static final String ZONE_NAME = "zoneName";

    public static final String NODE_NAME = "nodeName";

    public static final String POD_NAME = "podName";

    public static final String K8S_CLUSTER = "k8sCluster";

    public static final String CONTAINER_NAME = "containerName";

    public static final String IS_K8S = "isK8S";

    public static final String PROFILING_LABLES = "profilingLables";


    /*****  custom definition bellowing *****/

    public static final String DURATION = "duration";

    public static final String MIN_TIME = "min_time";

    public static final String MAX_TIME = "max_time";

    public static final String CPU_PERCENT = "cpuPercent";

    public static final String K8S_TYPE_VALUE = "1";

    public static final String EC2_TYPE_VALUE = "0";

    /*****************************************/

}
