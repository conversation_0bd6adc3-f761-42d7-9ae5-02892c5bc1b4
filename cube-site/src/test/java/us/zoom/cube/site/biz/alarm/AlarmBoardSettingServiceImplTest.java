package us.zoom.cube.site.biz.alarm;


import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ActiveProfiles;
import us.zoom.cube.site.BaseSpringTest;

import java.util.Arrays;
import java.util.List;

@ActiveProfiles("perf_for_local")
public class AlarmBoardSettingServiceImplTest extends BaseSpringTest {

    @Autowired
    private AlarmBoardSettingServiceImpl alarmBoardSetting;

    @Test
    public void getSelectList() {
        String optionsOrder = "in01,eu01";

        String configValue = "us01,au01,ca01,in01,eu01";
        List<String> result = AlarmBoardSettingServiceImpl.getSortList(Arrays.asList(optionsOrder.split(",")), Arrays.asList(configValue.split(",")));

        Assertions.assertEquals("in01", result.get(0));
        Assertions.assertEquals("eu01", result.get(1));
    }

    @Test
    @Disabled
    public void testSync() {

        alarmBoardSetting.sync();
    }

}