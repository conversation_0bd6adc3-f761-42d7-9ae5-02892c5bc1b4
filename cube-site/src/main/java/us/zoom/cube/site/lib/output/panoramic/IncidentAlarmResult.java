package us.zoom.cube.site.lib.output.panoramic;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
public class IncidentAlarmResult implements Serializable {
    @Serial
    private static final long serialVersionUID = -5629738669787414528L;
    private List<IncidentAlarmDetailResult> alarmList = new ArrayList<>();
    private String url;
    private String alertUrl;

    @Data
    public static class IncidentAlarmDetailResult implements Serializable {

        @Serial
        private static final long serialVersionUID = -6038157970174552674L;
        private String serviceName;
        private String alarmName;
        private String alarmId;
        private String aggregate;
        private String actionType;
        private String alertSource;
        private String status;
        private String alertUrl;
        private String pageNum;
        private String pageSize;
    }
}
