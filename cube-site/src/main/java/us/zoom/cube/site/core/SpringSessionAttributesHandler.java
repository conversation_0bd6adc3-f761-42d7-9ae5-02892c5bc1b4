package us.zoom.cube.site.core;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import us.zoom.infra.dao.service.SpringSessionAttributesDAO;

@Component
@Slf4j
public class SpringSessionAttributesHandler {

    @Autowired
    private SpringSessionAttributesDAO springSessionAttributesDAO;

    public int deleteAll() {
        return springSessionAttributesDAO.deleteAll();
    }
}
