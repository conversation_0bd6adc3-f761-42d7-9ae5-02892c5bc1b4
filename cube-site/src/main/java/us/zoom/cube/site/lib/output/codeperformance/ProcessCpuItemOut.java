package us.zoom.cube.site.lib.output.codeperformance;

import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @author: <PERSON><PERSON> Ding
 * @date: 2024/2/29 14:34
 * @desc:
 */
@Data
public class ProcessCpuItemOut {

    private String pid;

    private String processName;

    private Map<String, String> tags = new HashMap<>(1);

    private List<String> columns = new ArrayList<>();

    private List<Object> values = new ArrayList<>();

}
