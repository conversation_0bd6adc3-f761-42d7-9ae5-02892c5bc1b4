package us.zoom.cube.site.biz;


import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import us.zoom.cube.site.core.OutputAqHandler;
import us.zoom.cube.site.lib.BasePara;
import us.zoom.cube.site.lib.IdPara;
import us.zoom.cube.site.lib.ResponseObject;
import us.zoom.cube.site.lib.common.ValueText;
import us.zoom.cube.site.lib.input.OutputAqInput;
import us.zoom.cube.site.lib.output.PageResult;
import us.zoom.cube.site.lib.query.OutputAqQuery;
import us.zoom.cube.site.lib.query.PageQuery;
import us.zoom.infra.dao.model.OutputAqDO;

import java.util.List;

public class OutputAqServiceTest  extends  BaseTest{

    @Autowired
    private  OutputAqService outputAqService;

    @Autowired
    private OutputAqHandler outputAqHandler;

    @Test
    public void test2() throws Exception {
        OutputAqDO outputAqDO=outputAqHandler.getAqById("ed496292-d91f-40c5-b686-e0b9f4b9e047");
        outputAqHandler.editAq(outputAqDO);
    }
    @Test
    public void test() throws Exception {
        String outputAqName="_testOutputAqName1";
        String userId="82b4e894-edc7-47b8-ae99-58b6cbf58326";
        String tenantId="tenant1";

        PageQuery<OutputAqQuery> pageQuery =new PageQuery<>();
        pageQuery.setUserId(userId);
        pageQuery.setTenantId(tenantId);
        pageQuery.setPageIndex(1);
        pageQuery.setPageSize(20);
        OutputAqQuery outputAqQuery=new OutputAqQuery();
        outputAqQuery.setName(outputAqName);
        pageQuery.setQueryPara(outputAqQuery);
        ResponseObject responseObject=outputAqService.searchAq(pageQuery);
        PageResult pageResult= (PageResult) responseObject.getData();
        String outputAqId=null;
        String server1="server1";
        if(pageResult.getTotal() <=0){
            OutputAqInput outputAqInput=new OutputAqInput();
            outputAqInput.setName(outputAqName);
//            outputAqInput.setKeyPwd("keyPwd");
//            outputAqInput.setKeystorePath("keyName");
//            outputAqInput.setKeystorePwd("keyPwd");
            outputAqInput.setTruststorePath("trustName");
            outputAqInput.setTruststorePwd("trustPwd");
            outputAqInput.setJaasConfig("jaas");
            outputAqInput.setServers(server1);
            outputAqInput.setUserId(userId);
            outputAqInput.setAuthEncrType(3);
            outputAqInput.setTenantId(tenantId);
            outputAqInput.setIdenAlgorithm("");
            responseObject=outputAqService.addAq(outputAqInput);
            outputAqId=responseObject.getData().toString();
        }else {
            outputAqId=((OutputAqDO)pageResult.getItems().get(0)).getId();
        }

        IdPara idPara=new IdPara();
        idPara.setId(outputAqId);
        idPara.setUserId(userId);
        idPara.setTenantId(tenantId);
        responseObject=outputAqService.getAqById(idPara);
        OutputAqDO outputAqDO= (OutputAqDO) responseObject.getData();

        Assertions.assertTrue(outputAqDO.getServers().equals(server1));
        Assertions.assertTrue(outputAqDO.getName().equals(outputAqName));
        Assertions.assertTrue(outputAqDO.getKeyPwd().equals("keyPwd"));
        Assertions.assertTrue(outputAqDO.getJaasConfig().equals("jaas"));
        Assertions.assertTrue(outputAqDO.getKeystorePath().equals("keyName"));
        Assertions.assertTrue(outputAqDO.getKeystorePwd().equals("keyPwd"));
        Assertions.assertTrue(outputAqDO.getTruststorePath().equals("trustName"));
        Assertions.assertTrue(outputAqDO.getTruststorePwd().equals("trustPwd"));
        Assertions.assertTrue(outputAqDO.getAuthEncrType().equals(3));

        OutputAqInput outputAqInput=new OutputAqInput();
        outputAqInput.setName(outputAqName);
        String server2="ec2-50-16-132-120.compute-1.amazonaws.com:9093,ec2-3-86-37-86.compute-1.amazonaws.com:9093,ec2-34-227-170-219.compute-1.amazonaws.com:9093";
        outputAqInput.setServers(server2);
        outputAqInput.setUserId(userId);
        outputAqInput.setKeyPwd("keyPwd1");
        outputAqInput.setJaasConfig("jaas1");
        outputAqInput.setKeystorePath("keyName1");
        outputAqInput.setKeystorePwd("keyPwd1");
        outputAqInput.setTruststorePath("trustName1");
        outputAqInput.setTruststorePwd("trustPwd1");
        outputAqInput.setTenantId(tenantId);
        outputAqInput.setAuthEncrType(1);
        outputAqInput.setId(outputAqId);
        outputAqService.editAq(outputAqInput);

        BasePara basePara=new BasePara();
        basePara.setUserId(userId);
        basePara.setTenantId(tenantId);
        responseObject=outputAqService.listAllInLabel(basePara);

        List<ValueText> labels= (List<ValueText>) responseObject.getData();

        ValueText valueText=labels.get(0);
        Assertions.assertTrue(valueText.getText().equals(outputAqName));

        idPara=new IdPara();
        idPara.setId(outputAqId);
        idPara.setUserId(userId);
        idPara.setTenantId(tenantId);
        responseObject=outputAqService.getAqById(idPara);
         outputAqDO= (OutputAqDO) responseObject.getData();

        Assertions.assertTrue(outputAqDO.getServers().equals(server2));
        Assertions.assertTrue(outputAqDO.getName().equals(outputAqName));
        Assertions.assertTrue(outputAqDO.getJaasConfig().equals(""));
        Assertions.assertTrue(outputAqDO.getKeyPwd().equals("keyPwd1"));
        Assertions.assertTrue(outputAqDO.getKeystorePath().equals("keyName1"));
        Assertions.assertTrue(outputAqDO.getKeystorePwd().equals("keyPwd1"));
        Assertions.assertTrue(outputAqDO.getTruststorePath().equals("trustName1"));
        Assertions.assertTrue(outputAqDO.getTruststorePwd().equals("trustPwd1"));
        Assertions.assertTrue(outputAqDO.getAuthEncrType().equals(1));


        outputAqService.deleteById(idPara);
        responseObject=outputAqService.getAqById(idPara);

        Assertions.assertTrue(null == responseObject.getData());


    }
}
