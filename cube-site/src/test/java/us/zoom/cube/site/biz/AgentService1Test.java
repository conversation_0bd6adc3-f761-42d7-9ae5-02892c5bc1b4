package us.zoom.cube.site.biz;

import com.google.gson.Gson;
import org.apache.commons.lang3.StringUtils;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import us.zoom.cube.lib.agent.AgentHeartInfo;

/**
 * @Author: luis.zheng
 * @Date: 2021/7/27 10:23
 */
public class AgentService1Test {

    @Test
    public void testCompatibleHost() {

        //hostostName
        AgentService agentService = new AgentService();
        AgentHeartInfo agentHeartInfo = new AgentHeartInfo();
        agentHeartInfo.setHost("");
        agentHeartInfo.setHostName("");
        agentService.compatibleHost(agentHeartInfo);
        Assertions.assertEquals(agentHeartInfo.getHost(), "");
        Assertions.assertEquals(agentHeartInfo.getHostName(), "");

        //hostostNamenull
        agentHeartInfo.setHost(null);
        agentHeartInfo.setHostName(null);
        agentService.compatibleHost(agentHeartInfo);
        Assertions.assertEquals(agentHeartInfo.getHost(), null);
        Assertions.assertEquals(agentHeartInfo.getHostName(), null);

        //hostostNamenull
        agentHeartInfo.setHost("host");
        agentHeartInfo.setHostName(null);
        agentService.compatibleHost(agentHeartInfo);
        Assertions.assertEquals(agentHeartInfo.getHost(), "host");
        Assertions.assertEquals(agentHeartInfo.getHostName(), "host");

        //hostostNamenull
        agentHeartInfo.setHost(null);
        agentHeartInfo.setHostName("host");
        agentService.compatibleHost(agentHeartInfo);
        Assertions.assertEquals(agentHeartInfo.getHost(), "host");
        Assertions.assertEquals(agentHeartInfo.getHostName(), "host");
    }

    @Test
    public void testRealData() {
        AgentHeartInfo heartInfo = new Gson().fromJson("{\n" +
                "  \"AppName\": \"ds_web\",\n" +
                "  \"agentKey\": \"\",\n" +
                "  \"host\": \"ip-10-21-0-173.ec2.internal\",\n" +
                "  \"ip\": \"***********\",\n" +
                "  \"version\": \"1.1.0\",\n" +
                "  \"ts\": 1627433643790,\n" +
                "  \"infraType\": \"infra.jvm\",\n" +
                "  \"collectType\": 4,\n" +
                "  \"clusterId\": \"ds01\",\n" +
                "  \"regionId\": \"VA\",\n" +
                "  \"cloudType\": 1,\n" +
                "  \"id\": \"\",\n" +
                "  \"instanceId\": \"i-0aec8c258c9daf752\",\n" +
                "  \"ipPublic\": \"************\",\n" +
                "  \"successCount\": 414,\n" +
                "  \"failCount\": 0,\n" +
                "  \"batchCount\": 109,\n" +
                "  \"avgDelay\": 424,\n" +
                "  \"maxDelay\": 505\n" +
                "}", AgentHeartInfo.class);

        AgentService agentService = new AgentService();
        agentService.compatibleHost(heartInfo);

        if(StringUtils.isBlank(heartInfo.getAppName())
                ||StringUtils.isBlank(heartInfo.getClusterId()) || StringUtils.isBlank(heartInfo.getRegionId())
                ||StringUtils.isBlank(heartInfo.getAppName()) || StringUtils.isBlank(heartInfo.getHostName())
                ||StringUtils.isBlank(heartInfo.getIp()) || heartInfo.getTs() == null){
            System.out.println("heart info error");
        }
    }
}