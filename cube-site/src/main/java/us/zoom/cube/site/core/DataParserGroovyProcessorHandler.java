package us.zoom.cube.site.core;

import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import us.zoom.infra.dao.model.DataParserGroovyProcessorDO;
import us.zoom.infra.dao.service.DataParserGroovyProcessorDAO;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2020/5/29 2:25 AM
 */

@Component
public class DataParserGroovyProcessorHandler {

    @Autowired
    private DataParserGroovyProcessorDAO dataParserGroovyProcessorDAO;

    public void addGroovyProcessor(DataParserGroovyProcessorDO groovyProcessorDO) {
        Assert.notNull(groovyProcessorDO,"groovy processor is null !");
        dataParserGroovyProcessorDAO.add(groovyProcessorDO);
    }

    public DataParserGroovyProcessorDO getGroovyProcessorById(String id) {
        Assert.notNull(id, "id is null !");
        return dataParserGroovyProcessorDAO.getGroovyProcessorById(id);
    }

    public void editGroovyProcessor(DataParserGroovyProcessorDO groovyProcessorDO) {
        Assert.notNull(groovyProcessorDO,"groovy processor is null !");
        dataParserGroovyProcessorDAO.edit(groovyProcessorDO);
    }

    public void delGroovyProcessor(String id) {
        Assert.notNull(id, "id is null !");
        dataParserGroovyProcessorDAO.delete(id);
    }

    public void delGroovyProcessorByDataParserPipelineId(String dataParserPipelineId) {
        Assert.notNull(dataParserPipelineId, "dataParserPipelineId is null !");
        dataParserGroovyProcessorDAO.delGroovyProcessorByDataParserPipelineId(dataParserPipelineId);
    }

    public List<DataParserGroovyProcessorDO> listByPipelineIds(List<String> pipeLineIds) {
        List<DataParserGroovyProcessorDO> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(pipeLineIds)) {
            return result;
        }
        return dataParserGroovyProcessorDAO.listByPipelineIds(pipeLineIds);
    }

    public void delGroovyProcessorByPipelineIds(List<String> pipeLineIds) {
        if(CollectionUtils.isEmpty(pipeLineIds)){
            return;
        }
         dataParserGroovyProcessorDAO.delByPipeIds(pipeLineIds);
    }
}
