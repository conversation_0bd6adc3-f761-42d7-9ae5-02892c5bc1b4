package us.zoom.cube.site.biz;

import com.google.common.collect.Sets;
import org.junit.jupiter.api.Test;

/**
 * @Author: <EMAIL>
 * @ModuleOwner: <EMAIL>
 * @Date: 10/16/2023 10:24
 * @Description:
 */
public class InfrastructureServiceTest {
    @Test
    public void test() {
        Sets.SetView<Integer> intersection = Sets.intersection(Sets.newHashSet(1, 2, 3), Sets.newHashSet(2, 3));
        System.out.println(intersection);
    }
}
