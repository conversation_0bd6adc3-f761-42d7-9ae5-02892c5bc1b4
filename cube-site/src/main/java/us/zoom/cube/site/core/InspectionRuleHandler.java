package us.zoom.cube.site.core;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import us.zoom.infra.dao.model.InspectionRuleDO;
import us.zoom.infra.dao.service.InspectionRuleDAO;

/**
 * @Description:
 * @author: <PERSON>
 * @date: 2023/04/11 10:37
 */
@Service
public class InspectionRuleHandler {

    @Autowired
    private InspectionRuleDAO inspectionRuleDAO;

    public int addInspectionRule(InspectionRuleDO inspectionRule) {
        return inspectionRuleDAO.addInspectionRule(inspectionRule);
    }

    public int deleteInspectionRule(String id) {
        return inspectionRuleDAO.deleteInspectionRule(id);
    }
}
