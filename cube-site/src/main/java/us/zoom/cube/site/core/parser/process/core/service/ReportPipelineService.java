package us.zoom.cube.site.core.parser.process.core.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

@Component
@Slf4j
public class ReportPipelineService {
    private static final int REPORT_BATCH_SIZE = 200;

    private final static int PIPELINE_CACHE_SIZE_DEFAULT = 8000;
    private final static Long REPORT_PERIOD_MILLISECOND_DEFAULT = 20 * 60 * 1000L;

    private static final ObjectMapper mapper = new ObjectMapper();
    public static final AtomicInteger addPipelineFail = new AtomicInteger();

    @Autowired
    private Environment environment;

    private List<String> urls;
    private static final int MAX_RETRY_TIME = 3;
    private static final int SUCCESS_CODE = 200;
    private static final String JWT_AUTHENTICATION_BEAR = "Bearer ";
    public static final Long JWT_TOKEN_EXPIRATION_TIME_SECOND = 60 * 60L;
    public static final String PIPELINE_CACHE_SIZE_KEY = "pipeline.cache.size";
    public static final String REPORT_PERIOD_MILLISECOND_KEY = "report.period.millisecond";




}