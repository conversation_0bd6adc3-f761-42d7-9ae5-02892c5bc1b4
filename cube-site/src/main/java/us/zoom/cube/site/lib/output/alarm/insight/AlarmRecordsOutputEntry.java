package us.zoom.cube.site.lib.output.alarm.insight;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import us.zoom.cube.site.lib.output.config.metrics.MetricsTags;
import us.zoom.infra.enums.AlarmSourceTypeEnum;

import java.util.List;
import java.util.Map;

/**
 * @author: <PERSON><PERSON> Ding
 * @date: 2023/6/30 21:03
 * @desc:
 */
@Data
public class AlarmRecordsOutputEntry {

    private String id;

    private String alarmLevel;

    private String alarmId;

    private String alarmName;

    private String alarmRuleId;

    private String hittedRule;

    private String hittedValue;

    private String status;

    private String metricsId;

    private String metricsName;

    @JsonIgnore
    private Map metricsTagMap;

    private List<MetricsTags> metricsTags;

    private Long ackExpireTime;

    private long time;

    private long eventGmtCreate;

    private long recordGmtCreate;

    private String alarmSilenceName;

    private String alarmSilenceId;

    private AlarmSourceTypeEnum alarmSourceType;

    private Map<String, Object> varMap;

}
