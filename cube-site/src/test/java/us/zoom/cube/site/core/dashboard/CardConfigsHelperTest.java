package us.zoom.cube.site.core.dashboard;

import org.apache.commons.io.FileUtils;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.util.ResourceUtils;
import us.zoom.cube.site.biz.dashboard.MigrationServiceMonitor;
import us.zoom.cube.site.infra.utils.JacksonUtils;
import us.zoom.cube.site.lib.output.config.metrics.MetricsTempOut;
import us.zoom.infra.dao.model.CardDO;

import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

public class CardConfigsHelperTest {

    @Test
    public void testZcService() throws IOException {

        SetupResult setupResult = setupZc();
        CardDO cardD4 = new CardDO();
        cardD4.setConfigs("{\"type\":\"card_copy\",\"data\":{\"id\":\"fd63a054-9d0c-4396-b8ee-7b997af6ff69\",\"tenantId\":\"9a2e334e-4b7e-450c-8531-62406bdb5399\",\"name\":\"SJC\",\"type\":2,\"configs\":{\"queryConfig\":{\"dbType\":\"clickhouse\",\"useClickHouse\":true,\"groupByTime\":\"auto\",\"compareTime\":\"none\",\"fill\":\"none\",\"db\":\"service_monitor\",\"rp\":\"autogen\",\"fields\":[],\"fieldsAlias\":{},\"functionSelectors\":{},\"groupByTagKey\":[],\"tagValues\":{},\"rawText\":\"SELECT\\n  now() as `time`,\\n  t5.Region as region,\\n  SUM(Participants) AS \\\"Participants\\\",\\n  SUM(attendees) AS \\\"attendees\\\",\\n  SUM(Meeting) AS Meeting,\\n  SUM(phoneCallCnt) AS phoneCallCnt,\\n  SUM(p2pClients) AS p2pClients,\\n  SUM(\\\"active_node\\\") as active_node,\\n  SUM(\\\"total_node\\\") as total_node,\\n  SUM(\\\"capacity\\\") as capacity\\nFROM\\n  (\\n   select \\n    Zone as zoneName,\\n    Region,\\n    max(\\\"zone.load.client.count\\\") AS \\\"Participants\\\",\\n    max(\\\"zone.load.viewonly.count\\\") AS \\\"attendees\\\",\\n    max(\\\"zone.load.meeting.count\\\") AS Meeting,\\n    max(\\\"zone.load.telephony.count\\\") AS phoneCallCnt,\\n    max(\\\"zone.load.p2p.client.count\\\") AS p2pClients\\n    from \\n    \\\"service_monitor\\\".\\\"ZC_ZONE-LOAD\\\"\\n    WHERE\\n      time > (now () - INTERVAL 5 MINUTE)\\n      AND time < now ()\\n      and Region = 'SJC'\\n      group by Region,Zone\\n  ) as t5\\n  left join (\\n    select\\n      Region,\\n      zoneName,\\n      COUNT(DISTINCT active_data.InstanceId) AS active_node,\\n      COUNT(DISTINCT sm.InstanceId) AS total_node,\\n      (\\n        MAX(toInt32OrZero (active_data.capacity)) * total_node\\n      ) as capacity\\n    from\\n      (\\n        SELECT\\n          Region,\\n          InstanceId,\\n          zoneName\\n        FROM\\n          \\\"service_monitor\\\".\\\"MMR_SERVER-STATUS\\\"\\n        where\\n          time > (now () - INTERVAL 2 DAY)\\n          and Region = 'SJC'\\n        GROUP BY\\n          Region,\\n          InstanceId,\\n          zoneName\\n      ) AS sm\\n      LEFT JOIN (\\n        SELECT\\n          Region,\\n          InstanceId,\\n          zoneName,\\n          capacity\\n        FROM\\n          (\\n            SELECT\\n              Region,\\n              InstanceId,\\n              zoneName,\\n              MAX(\\\"service.status\\\") AS \\\"max_service_status\\\",\\n              MAX(\\\"capacity\\\") as capacity\\n            FROM\\n              \\\"service_monitor\\\".\\\"MMR_SERVER-STATUS\\\"\\n            WHERE\\n              time > (now () - INTERVAL 5 MINUTE)\\n              AND time < now ()\\n              and Region = 'SJC'\\n            GROUP BY\\n              Region,\\n              InstanceId,\\n              zoneName\\n          ) AS t1\\n        WHERE\\n          t1.\\\"max_service_status\\\" = 1\\n      ) AS active_data ON sm.Region = active_data.Region and sm.zoneName = active_data.zoneName\\n    group by\\n      Region,zoneName\\n  ) as t6 on t5.Region = t6.Region and t5.zoneName = t6.zoneName\\ngroup by\\n  t5.Region\",\"visualConfigs\":{\"showToolbar\":true,\"showRowNumber\":false,\"sortType\":\"auto\",\"timezone\":\"utc\",\"timeFormat\":\"YYYY-MM-DD HH:mm:ss\",\"viewType\":\"auto\",\"timeAxis\":\"horizontal\",\"customizeFields\":[{\"visible\":true,\"name\":\"time\",\"alias\":\"time\",\"fmt\":{},\"order\":1},{\"visible\":true,\"name\":\"region\",\"alias\":\"region\",\"fmt\":{},\"order\":1},{\"visible\":true,\"name\":\"Participants\",\"alias\":\"Participants\",\"fmt\":{},\"order\":1},{\"visible\":true,\"name\":\"attendees\",\"alias\":\"attendees\",\"fmt\":{},\"order\":3},{\"visible\":true,\"name\":\"Meeting\",\"alias\":\"Meeting\",\"fmt\":{},\"order\":4},{\"visible\":true,\"name\":\"phoneCallCnt\",\"alias\":\"phoneCallCnt\",\"fmt\":{},\"order\":5},{\"visible\":true,\"name\":\"p2pClients\",\"alias\":\"p2pClients\",\"fmt\":{},\"order\":6},{\"visible\":true,\"name\":\"active_node\",\"alias\":\"active_node\",\"fmt\":{},\"order\":6},{\"visible\":true,\"name\":\"total_node\",\"alias\":\"total_node\",\"fmt\":{},\"order\":7},{\"visible\":true,\"name\":\"capacity\",\"alias\":\"capacity\",\"fmt\":{},\"order\":8}]}},\"gridPos\":{\"w\":4,\"x\":0,\"h\":10,\"y\":0,\"i\":\"fd63a054-9d0c-4396-b8ee-7b997af6ff69\"},\"type\":\"table\",\"visualConfigs\":{\"showToolbar\":true,\"showRowNumber\":false,\"sortType\":\"auto\",\"timezone\":\"utc\",\"timeFormat\":\"YYYY-MM-DD HH:mm:ss\",\"viewType\":\"auto\",\"timeAxis\":\"horizontal\",\"customizeFields\":[{\"visible\":true,\"name\":\"time\",\"alias\":\"time\",\"fmt\":{},\"order\":1},{\"visible\":true,\"name\":\"region\",\"alias\":\"region\",\"fmt\":{},\"order\":1},{\"visible\":true,\"name\":\"Participants\",\"alias\":\"Participants\",\"fmt\":{},\"order\":1},{\"visible\":true,\"name\":\"attendees\",\"alias\":\"attendees\",\"fmt\":{},\"order\":3},{\"visible\":true,\"name\":\"Meeting\",\"alias\":\"Meeting\",\"fmt\":{},\"order\":4},{\"visible\":true,\"name\":\"phoneCallCnt\",\"alias\":\"phoneCallCnt\",\"fmt\":{},\"order\":5},{\"visible\":true,\"name\":\"p2pClients\",\"alias\":\"p2pClients\",\"fmt\":{},\"order\":6},{\"visible\":true,\"name\":\"active_node\",\"alias\":\"active_node\",\"fmt\":{},\"order\":6},{\"visible\":true,\"name\":\"total_node\",\"alias\":\"total_node\",\"fmt\":{},\"order\":7},{\"visible\":true,\"name\":\"capacity\",\"alias\":\"capacity\",\"fmt\":{},\"order\":8}]}},\"description\":\"\",\"gmtCreate\":\"2025-08-14T08:49:51.000+00:00\",\"gmtModify\":\"2025-08-14T08:49:51.000+00:00\",\"creator\":\"<EMAIL>\",\"modifier\":\"<EMAIL>\",\"dashId\":\"ee0feb4f-d1ce-4be7-ab93-2feca16538c8\"}}");

        CardConfigsHelper.ConfigsResult configsResult = CardConfigsHelper.parseConfigs(setupResult.migrationServiceMonitor(), setupResult.metricsMap(), cardD4, setupResult.serviceMap());

        Assertions.assertTrue(configsResult.configs().contains("Meeting_Realtime_realtime-zc"));
        Assertions.assertTrue(configsResult.configs().contains("\\\"service_monitor\\\".\\\"MMR_SERVER-STATUS\\\""));

    }

    @Test
    public void testVdiService() throws IOException {

        SetupResult setupResult = setupVdi();
        CardDO cardD4 = new CardDO();
        cardD4.setConfigs("{\"type\":\"card_copy\",\"data\":{\"id\":\"8d58144f-35fd-4358-890c-4b1dd7faabb2\",\"tenantId\":\"9a2e334e-4b7e-450c-8531-62406bdb5399\",\"name\":\"vdigw_client_count\",\"type\":2,\"configs\":{\"queryConfig\":{\"dbType\":\"clickhouse\",\"useClickHouse\":true,\"groupByTime\":\"auto\",\"compareTime\":\"none\",\"fill\":\"none\",\"db\":\"service_monitor\",\"rp\":\"autogen\",\"fields\":[],\"fieldsAlias\":{},\"functionSelectors\":{},\"groupByTagKey\":[],\"tagValues\":{},\"rawText\":\"SELECT\\n  t2.Zone,\\n  t3.window_start as time,\\n  SUM(\\\"vdigw_client_count\\\") AS vdigw_client_count\\nFROM\\n  (\\n    SELECT\\n      Zone,\\n      InstanceId\\n    FROM\\n      (\\n        SELECT\\n          Zone,\\n          InstanceId,\\n          MAX(\\\"STATUS\\\") AS max_service_status\\n        FROM\\n          \\\"service_monitor\\\".\\\"VDI-SERVER-STATUS\\\"\\n        WHERE\\n          time > (now () - INTERVAL 5 MINUTE)\\n          AND time < now ()\\n          AND Zone = :zoneName:\\n        GROUP BY\\n          Zone,\\n          InstanceId\\n      ) AS t1\\n    WHERE\\n      t1.max_service_status = 1\\n  ) AS t2\\n  JOIN (\\n    SELECT\\n      InstanceId,\\n      toStartOfMinute (time) as window_start,\\n      MAX(\\\"vdigw_client_count\\\") AS vdigw_client_count\\n    FROM\\n      \\\"service_monitor\\\".\\\"VDI-VDIGW-STAT\\\"\\n    WHERE\\n      time > :dashboardTime:\\n      AND time < :upperDashboardTime:\\n    group by\\n      window_start,\\n      InstanceId\\n  ) t3 ON t2.InstanceId = t3.InstanceId\\ngroup by\\n  t3.window_start,\\n  t2.Zone\\norder by\\n  t3.window_start asc\",\"visualConfigs\":{\"eChartTheme\":\"default\",\"yAxisMin\":\"auto\",\"yAxisMax\":\"auto\",\"yAxisFormat\":\"raw\",\"baseUnit\":\"raw\",\"decimalPlaces\":\"auto\",\"thousandSeparator\":false,\"timeFormat\":\"YYYY-MM-DD HH:mm:ss\",\"timezone\":\"utc\",\"omitHead\":false,\"legendHeight\":\"auto\",\"legendMode\":\"list\",\"legendPlacement\":\"bottom\",\"pointType\":\"auto\",\"lineType\":\"smooth\",\"thresholdMode\":\"abs\",\"baseColor\":\"#73bf69\"}},\"gridPos\":{\"x\":0,\"y\":0,\"w\":6,\"h\":8,\"i\":\"8d58144f-35fd-4358-890c-4b1dd7faabb2\"},\"type\":\"line\",\"visualConfigs\":{\"eChartTheme\":\"default\",\"yAxisMin\":\"auto\",\"yAxisMax\":\"auto\",\"yAxisFormat\":\"raw\",\"baseUnit\":\"raw\",\"decimalPlaces\":\"auto\",\"thousandSeparator\":false,\"timeFormat\":\"YYYY-MM-DD HH:mm:ss\",\"timezone\":\"utc\",\"omitHead\":false,\"legendHeight\":\"auto\",\"legendMode\":\"list\",\"legendPlacement\":\"bottom\",\"pointType\":\"auto\",\"lineType\":\"smooth\",\"thresholdMode\":\"abs\",\"baseColor\":\"#73bf69\"}},\"description\":\"\",\"gmtCreate\":\"2025-05-19T06:35:10.000+00:00\",\"gmtModify\":\"2025-05-19T06:35:10.000+00:00\",\"creator\":\"<EMAIL>\",\"modifier\":\"<EMAIL>\",\"dashId\":\"c8838180-ee85-4726-a73e-6f4b6db635f8\"}}");

        CardConfigsHelper.ConfigsResult configsResult = CardConfigsHelper.parseConfigs(setupResult.migrationServiceMonitor(), setupResult.metricsMap(), cardD4, setupResult.serviceMap());

        Assertions.assertTrue(configsResult.configs().contains("Meeting_Gateway_vdigw"));
        Assertions.assertTrue(configsResult.configs().contains("zone"));
        Assertions.assertFalse(configsResult.configs().contains("Zone"));
        Assertions.assertFalse(configsResult.configs().contains("InstanceId"));
        Assertions.assertTrue(configsResult.configs().contains("instanceid"));

    }


    @Test
    public void testMultipleService() throws IOException {

        SetupResult setupResult = setup();
        CardDO cardD4 = new CardDO();
        cardD4.setConfigs("{\"type\":\"card_copy\",\"data\":{\"id\":\"35a72476-303d-4d42-8e3e-91999e24805b\",\"tenantId\":\"9a2e334e-4b7e-450c-8531-62406bdb5399\",\"name\":\"Call Fail Rate by Region\",\"type\":2,\"configs\":{\"queryConfig\":{\"dbType\":\"clickhouse\",\"useClickHouse\":true,\"groupByTime\":\"auto\",\"compareTime\":\"none\",\"fill\":\"none\",\"db\":\"service_monitor\",\"rp\":\"autogen\",\"fields\":[],\"fieldsAlias\":{},\"functionSelectors\":{},\"groupByTagKey\":[],\"tagValues\":{},\"rawText\":\"SELECT \\r\\n    Region,\\r\\n    COALESCE(success.interval_time, fail.interval_time) AS time,\\r\\n    COALESCE(fail.sum_call_join_fail_count, 0) / (COALESCE(fail.sum_call_join_fail_count, 0) + COALESCE(success.sum_call_join_success_count, 0)) * 100 AS fail_rate\\r\\nFROM (\\r\\n    SELECT \\r\\n        toStartOfInterval(time, INTERVAL :INTERVAL:) AS interval_time,\\r\\n        sum(\\\"call_join_success_count\\\") AS sum_call_join_success_count,\\r\\n        Region\\r\\n    FROM \\\"service_monitor\\\".\\\"TEL_CALL-JOIN-SUCCESS\\\"\\r\\n    WHERE time > :dashboardTime: AND time < :upperDashboardTime:\\r\\n    GROUP BY interval_time, Region\\r\\n) AS success\\r\\nFULL OUTER JOIN (\\r\\n    SELECT \\r\\n        toStartOfInterval(time, INTERVAL :INTERVAL:) AS interval_time,\\r\\n        count(\\\"errorConnectTimeout\\\") AS sum_call_join_fail_count,\\r\\n        Region\\r\\n    FROM \\\"service_monitor\\\".\\\"TEL_CALL-JOIN-FAIL\\\"\\r\\n    WHERE time > :dashboardTime: AND time < :upperDashboardTime: AND (\\\"ivrReason\\\" != 7 AND \\\"ivrReason\\\" != 10)\\r\\n    GROUP BY interval_time, Region\\r\\n) AS fail\\r\\nON success.interval_time = fail.interval_time AND success.Region = fail.Region  \\r\\nWHERE COALESCE(fail.sum_call_join_fail_count, 0) + COALESCE(success.sum_call_join_success_count, 0) > 0 AND success.Region != '' \\r\\nORDER BY time;\",\"visualConfigs\":{\"eChartTheme\":\"default\",\"yAxisMin\":\"auto\",\"yAxisMax\":\"auto\",\"yAxisFormat\":\"raw\",\"baseUnit\":\"raw\",\"decimalPlaces\":\"auto\",\"timeFormat\":\"YYYY-MM-DD HH:mm:ss\",\"yAxisSuffix\":\"%\",\"omitHead\":false,\"legendHeight\":\"auto\",\"legendMode\":\"list\",\"legendPlacement\":\"bottom\",\"pointType\":\"auto\",\"lineType\":\"smooth\",\"thresholdMode\":\"abs\",\"baseColor\":\"#73bf69\"}},\"gridPos\":{\"x\":0,\"y\":48,\"h\":8,\"w\":6,\"i\":\"35a72476-303d-4d42-8e3e-91999e24805b\"},\"type\":\"line\",\"visualConfigs\":{\"eChartTheme\":\"default\",\"yAxisMin\":\"auto\",\"yAxisMax\":\"auto\",\"yAxisFormat\":\"raw\",\"baseUnit\":\"raw\",\"decimalPlaces\":\"auto\",\"timeFormat\":\"YYYY-MM-DD HH:mm:ss\",\"yAxisSuffix\":\"%\",\"omitHead\":false,\"legendHeight\":\"auto\",\"legendMode\":\"list\",\"legendPlacement\":\"bottom\",\"pointType\":\"auto\",\"lineType\":\"smooth\",\"thresholdMode\":\"abs\",\"baseColor\":\"#73bf69\"}},\"description\":\"\",\"gmtCreate\":\"2024-08-07T18:07:41.000+00:00\",\"gmtModify\":\"2025-09-03T03:31:27.000+00:00\",\"creator\":\"\",\"modifier\":\"<EMAIL>\",\"dashId\":\"3190bd50-90a2-4457-8aea-2b1b5bc6f2ee\"}}");

        CardConfigsHelper.ConfigsResult configsResult = CardConfigsHelper.parseConfigs(setupResult.migrationServiceMonitor(), setupResult.metricsMap(), cardD4, setupResult.serviceMap());

        Assertions.assertTrue(configsResult.configs().contains("Meeting_Telephony_telgw"));
        Assertions.assertTrue(configsResult.configs().contains("region"));
        Assertions.assertFalse(configsResult.configs().contains("Region\r"));
        Assertions.assertFalse(configsResult.configs().contains("Region,"));
        Assertions.assertFalse(configsResult.configs().contains(".Region"));

    }

    @Test
    public void testReplace() throws IOException {

        SetupResult setupResult = setup();
        CardDO cardD4 = new CardDO();
        cardD4.setConfigs("{\"type\":\"card_copy\",\"data\":{\"id\":\"c57438b4-10a6-4397-a5a3-9dba9682ba73\",\"tenantId\":\"9a2e334e-4b7e-450c-8531-62406bdb5399\",\"name\":\"TG Conference Load Percentage By Zone (Max: 80, Threshold: 60)\",\"type\":2,\"configs\":{\"queryConfig\":{\"dbType\":\"clickhouse\",\"useClickHouse\":true,\"groupByTime\":\"auto\",\"compareTime\":\"none\",\"fill\":\"none\",\"db\":\"service_monitor\",\"rp\":\"autogen\",\"fields\":[],\"fieldsAlias\":{},\"functionSelectors\":{},\"groupByTagKey\":[],\"tagValues\":{},\"rawText\":\"SELECT IF(max_telgw.count > 0, 100*(\\\"max_meeting.count.peak\\\"/(\\\"max_telgw.count\\\"*200)), 0) AS PCT, time, Zone FROM(SELECT max(\\\"meeting.count.peak\\\") AS \\\"max_meeting.count.peak\\\", max(\\\"telgw.count\\\") AS \\\"max_telgw.count\\\", toStartOfInterval(time, INTERVAL 1 MINUTE) AS time, \\\"Zone\\\" FROM \\\"service_monitor\\\".\\\"TC_TC-STAT\\\" WHERE time > subtractMinutes(now(), 43200) AND time < now() GROUP BY time, Zone ORDER BY time DESC)\",\"visualConfigs\":{\"eChartTheme\":\"default\",\"yAxisMin\":null,\"yAxisMax\":100,\"yAxisFormat\":\"raw\",\"baseUnit\":\"raw\",\"decimalPlaces\":\"auto\",\"thousandSeparator\":false,\"timeFormat\":\"YYYY-MM-DD HH:mm:ss\",\"omitHead\":false,\"legendHeight\":\"auto\",\"legendMode\":\"table\",\"legendPlacement\":\"bottom\",\"thresholds\":[{\"value\":60,\"color\":\"#e3ab1b\"},{\"value\":80,\"color\":\"#ae2b2b\"}],\"pointType\":\"auto\",\"lineType\":\"smooth\",\"legendValueSortType\":\"auto\",\"thresholdMode\":\"abs\",\"baseColor\":\"#73bf69\"}},\"gridPos\":{\"x\":0,\"y\":14,\"h\":10,\"w\":6,\"i\":\"c57438b4-10a6-4397-a5a3-9dba9682ba73\"},\"type\":\"line\",\"visualConfigs\":{\"eChartTheme\":\"default\",\"yAxisMin\":null,\"yAxisMax\":100,\"yAxisFormat\":\"raw\",\"baseUnit\":\"raw\",\"decimalPlaces\":\"auto\",\"thousandSeparator\":false,\"timeFormat\":\"YYYY-MM-DD HH:mm:ss\",\"omitHead\":false,\"legendHeight\":\"auto\",\"legendMode\":\"table\",\"legendPlacement\":\"bottom\",\"thresholds\":[{\"value\":60,\"color\":\"#e3ab1b\"},{\"value\":80,\"color\":\"#ae2b2b\"}],\"pointType\":\"auto\",\"lineType\":\"smooth\",\"legendValueSortType\":\"auto\",\"thresholdMode\":\"abs\",\"baseColor\":\"#73bf69\"}},\"description\":\"TG Zone Conference Load Status PCT (0~100),(ZoneTotalConferenceNumber/ZoneConferenceCapacity)*100, default 200 conference per ZMS\",\"gmtCreate\":\"2024-01-29T21:52:25.000+00:00\",\"gmtModify\":\"2025-09-03T03:31:27.000+00:00\",\"creator\":\"\",\"modifier\":\"<EMAIL>\",\"dashId\":\"3190bd50-90a2-4457-8aea-2b1b5bc6f2ee\"}}\n");

        CardConfigsHelper.ConfigsResult configsResult = CardConfigsHelper.parseConfigs(setupResult.migrationServiceMonitor(), setupResult.metricsMap(), cardD4, setupResult.serviceMap());

        Assertions.assertTrue(configsResult.configs().contains("Meeting_Telephony_telgw"));
        Assertions.assertFalse(configsResult.configs().contains("service_monitor"));
        Assertions.assertTrue(configsResult.configs().contains("zone"));
        Assertions.assertFalse(configsResult.configs().contains(" Zone "));
        Assertions.assertFalse(configsResult.configs().contains("\\\"Zone\\\""));


    }

    @Test
    public void testCommon() throws IOException {

        SetupResult setupResult = setup();


        CardDO cardD1 = new CardDO();
        cardD1.setConfigs("{\"type\":\"card_copy\",\"data\":{\"id\":\"57088aeb-2ff6-4bed-85c0-368db24fc815\",\"tenantId\":\"b5c8c3c2-dcb7-4157-ab51-824b835f3ba5\",\"name\":\"ASYNMQ Produce Success Percentage by Zone\",\"type\":2,\"configs\":{\"queryConfig\":{\"dbType\":\"clickhouse\",\"useClickHouse\":true,\"groupByTime\":\"auto\",\"compareTime\":\"none\",\"fill\":\"none\",\"db\":\"service_monitor\",\"rp\":\"autogen\",\"fields\":[],\"fieldsAlias\":{},\"functionSelectors\":{},\"groupByTagKey\":[],\"tagValues\":{},\"rawText\":\"SELECT \\r\\n     (max(\\\"produce_success_count\\\")/max(\\\"produce_count\\\")) * 100 AS \\\"produce_success_percentage\\\", toStartOfInterval(time, INTERVAL :INTERVAL:) AS time, \\\"Zone\\\"\\r\\nFROM \\\"service_monitor\\\".\\\"TEL_TC_ASYNMQ_STATUS\\\" WHERE time > :dashboardTime: AND time < :upperDashboardTime: GROUP BY time, \\\"Zone\\\" \",\"visualConfigs\":{\"eChartTheme\":\"default\",\"yAxisMin\":\"auto\",\"yAxisMax\":\"auto\",\"yAxisFormat\":\"raw\",\"baseUnit\":\"raw\",\"decimalPlaces\":\"auto\",\"thousandSeparator\":false,\"timeFormat\":\"YYYY-MM-DD HH:mm:ss\",\"yAxisSuffix\":\"%\",\"omitHead\":false,\"legendHeight\":\"auto\",\"legendMode\":\"list\",\"legendPlacement\":\"bottom\",\"pointType\":\"auto\",\"lineType\":\"smooth\",\"thresholdMode\":\"abs\",\"baseColor\":\"#73bf69\"}},\"gridPos\":{\"x\":4,\"y\":0,\"w\":6,\"h\":8,\"i\":\"57088aeb-2ff6-4bed-85c0-368db24fc815\"},\"type\":\"line\",\"visualConfigs\":{\"eChartTheme\":\"default\",\"yAxisMin\":\"auto\",\"yAxisMax\":\"auto\",\"yAxisFormat\":\"raw\",\"baseUnit\":\"raw\",\"decimalPlaces\":\"auto\",\"thousandSeparator\":false,\"timeFormat\":\"YYYY-MM-DD HH:mm:ss\",\"yAxisSuffix\":\"%\",\"omitHead\":false,\"legendHeight\":\"auto\",\"legendMode\":\"list\",\"legendPlacement\":\"bottom\",\"pointType\":\"auto\",\"lineType\":\"smooth\",\"thresholdMode\":\"abs\",\"baseColor\":\"#73bf69\"}},\"description\":\"\",\"gmtCreate\":\"2025-09-03T16:52:46.000+00:00\",\"gmtModify\":\"2025-09-04T03:29:15.000+00:00\",\"creator\":\"<EMAIL>\",\"modifier\":\"<EMAIL>\",\"dashId\":\"12c6ddc0-7dda-4b56-aa57-c6e2a4ef88ff\"}}");

        CardDO cardD2 = new CardDO();
        cardD2.setConfigs("{\"type\":\"card_copy\",\"data\":{\"id\":\"4a4c7a58-9043-4146-86ba-029f380c7ffb\",\"tenantId\":\"b5c8c3c2-dcb7-4157-ab51-824b835f3ba5\",\"name\":\"TG -Dialout_Failures\",\"type\":2,\"configs\":{\"queryConfig\":{\"dbType\":\"clickhouse\",\"useClickHouse\":true,\"groupByTime\":\"auto\",\"compareTime\":\"none\",\"fill\":\"none\",\"db\":\"service_monitor\",\"measurement\":\"TEL_GLOBAL-STAT\",\"rp\":null,\"fields\":[\"OutDialFailures\"],\"fieldsAlias\":{},\"functionSelectors\":{\"OutDialFailures\":{\"max\":[]}},\"groupByTagKey\":[\"Region\"],\"tagValues\":{},\"rawText\":null,\"visualConfigs\":{\"eChartTheme\":\"default\",\"yAxisMin\":\"auto\",\"yAxisMax\":\"auto\",\"yAxisFormat\":\"raw\",\"baseUnit\":\"raw\",\"decimalPlaces\":\"auto\",\"thousandSeparator\":false,\"timeFormat\":\"YYYY-MM-DD HH:mm:ss\",\"timezone\":\"utc\",\"omitHead\":false,\"legendHeight\":\"auto\",\"legendMode\":\"list\",\"legendPlacement\":\"bottom\",\"pointType\":\"auto\",\"lineType\":\"smooth\",\"thresholdMode\":\"abs\",\"baseColor\":\"#73bf69\"},\"buckets\":{}},\"gridPos\":{\"x\":0,\"y\":0,\"w\":6,\"h\":8,\"i\":\"4a4c7a58-9043-4146-86ba-029f380c7ffb\"},\"type\":\"line\",\"visualConfigs\":{\"eChartTheme\":\"default\",\"yAxisMin\":\"auto\",\"yAxisMax\":\"auto\",\"yAxisFormat\":\"raw\",\"baseUnit\":\"raw\",\"decimalPlaces\":\"auto\",\"thousandSeparator\":false,\"timeFormat\":\"YYYY-MM-DD HH:mm:ss\",\"timezone\":\"utc\",\"omitHead\":false,\"legendHeight\":\"auto\",\"legendMode\":\"list\",\"legendPlacement\":\"bottom\",\"pointType\":\"auto\",\"lineType\":\"smooth\",\"thresholdMode\":\"abs\",\"baseColor\":\"#73bf69\"}},\"description\":\"\",\"gmtCreate\":\"2025-09-04T03:30:41.000+00:00\",\"gmtModify\":\"2025-09-04T03:30:41.000+00:00\",\"creator\":\"<EMAIL>\",\"modifier\":\"<EMAIL>\",\"dashId\":\"12c6ddc0-7dda-4b56-aa57-c6e2a4ef88ff\"}}");

        CardDO cardD3 = new CardDO();
        cardD3.setConfigs("{\"type\":\"card_copy\",\"data\":{\"id\":\"7efddc7f-3dac-4d09-8f5e-7863b74f5f6e\",\"tenantId\":\"b5c8c3c2-dcb7-4157-ab51-824b835f3ba5\",\"name\":\"4XX_SIP_RESPONSE_ERROR_(REGION/COUNTRY/CARRIER)\",\"type\":2,\"configs\":{\"queryConfig\":{\"dbType\":\"clickhouse\",\"useClickHouse\":true,\"groupByTime\":\"auto\",\"compareTime\":\"none\",\"fill\":\"none\",\"db\":\"service_monitor\",\"measurement\":\"TEL_CALL-JOIN-FAIL\",\"rp\":\"autogen\",\"fields\":[\"vendor.Bandwidth\",\"vendor.Bandwidth_Int\",\"vendor.bics\",\"vendor.Colt\",\"vendor.Inteliquent\",\"vendor.Inteliquent_INTL\",\"vendor.Iristel\",\"vendor.Iristel_Int_new\",\"vendor.Iristel_new\",\"vendor.Level3\",\"vendor.orangecarrier\",\"vendor.OTW\",\"vendor.Rogers\",\"vendor.Peerless\",\"vendor.Symbio\",\"vendor.TATA\",\"vendor.ThinQ\",\"vendor.TNZI\",\"vendor.Union400\",\"vendor.vodafone\",\"vendor.VoxBone\",\"vendor.XO\",\"vendor.Iristel_Int\"],\"fieldsAlias\":{},\"functionSelectors\":{\"errorConnectTimeout\":{\"avg\":[]},\"errorRingTimeout\":{\"avg\":[]},\"ivrReason\":{\"avg\":[]},\"vendor.Bandwidth\":{\"sum\":[]},\"vendor.Bandwidth_Int\":{\"sum\":[]},\"vendor.bics\":{\"sum\":[]},\"vendor.Colt\":{\"sum\":[]},\"vendor.Inteliquent\":{\"sum\":[]},\"vendor.Inteliquent_INTL\":{\"sum\":[]},\"vendor.Iristel\":{\"sum\":[]},\"vendor.Iristel_Int\":{\"sum\":[]},\"vendor.Iristel_Int_new\":{\"sum\":[]},\"vendor.Iristel_new\":{\"sum\":[]},\"vendor.Level3\":{\"sum\":[]},\"vendor.orangecarrier\":{\"sum\":[]},\"vendor.OTW\":{\"sum\":[]},\"vendor.Peerless\":{\"sum\":[]},\"vendor.Symbio\":{\"sum\":[]},\"vendor.Rogers\":{\"sum\":[]},\"vendor.TATA\":{\"sum\":[]},\"vendor.ThinQ\":{\"sum\":[]},\"vendor.TNZI\":{\"sum\":[]},\"vendor.Union400\":{\"sum\":[]},\"vendor.vodafone\":{\"sum\":[]},\"vendor.VoxBone\":{\"sum\":[]},\"vendor.XO\":{\"sum\":[]}},\"groupByTagKey\":[\"error_Code\",\"Region\",\"country_Code\"],\"tagValues\":{\"error_Code\":[\"480\",\"486\",\"404\",\"482\",\"403\",\"488\",\"484\",\"483\",\"408\",\"487\",\"401\",\"400\"]},\"rawText\":null,\"visualConfigs\":{\"eChartTheme\":\"default\",\"yAxisMin\":\"auto\",\"yAxisMax\":\"auto\",\"yAxisFormat\":\"raw\",\"baseUnit\":\"raw\",\"decimalPlaces\":\"auto\",\"thousandSeparator\":false,\"timeFormat\":\"YYYY-MM-DD HH:mm:ss\",\"timezone\":\"utc\",\"omitHead\":false,\"legendHeight\":\"auto\",\"legendMode\":\"list\",\"legendPlacement\":\"bottom\",\"thresholds\":[],\"pointType\":\"never\",\"lineType\":\"smooth\",\"thresholdMode\":\"abs\",\"baseColor\":\"#73bf69\"},\"buckets\":{}},\"gridPos\":{\"x\":0,\"y\":16,\"w\":6,\"h\":8,\"i\":\"7efddc7f-3dac-4d09-8f5e-7863b74f5f6e\"},\"type\":\"line\",\"visualConfigs\":{\"eChartTheme\":\"default\",\"yAxisMin\":\"auto\",\"yAxisMax\":\"auto\",\"yAxisFormat\":\"raw\",\"baseUnit\":\"raw\",\"decimalPlaces\":\"auto\",\"thousandSeparator\":false,\"timeFormat\":\"YYYY-MM-DD HH:mm:ss\",\"timezone\":\"utc\",\"omitHead\":false,\"legendHeight\":\"auto\",\"legendMode\":\"list\",\"legendPlacement\":\"bottom\",\"thresholds\":[],\"pointType\":\"never\",\"lineType\":\"smooth\",\"thresholdMode\":\"abs\",\"baseColor\":\"#73bf69\"}},\"description\":\"\",\"gmtCreate\":\"2025-09-03T16:49:04.000+00:00\",\"gmtModify\":\"2025-09-04T03:29:15.000+00:00\",\"creator\":\"<EMAIL>\",\"modifier\":\"<EMAIL>\",\"dashId\":\"12c6ddc0-7dda-4b56-aa57-c6e2a4ef88ff\"}}");


        CardConfigsHelper.ConfigsResult configsResult = CardConfigsHelper.parseConfigs(setupResult.migrationServiceMonitor(), setupResult.metricsMap(), cardD1, setupResult.serviceMap());

        Assertions.assertTrue(configsResult.configs().contains("Meeting_Telephony_telgw"));
        Assertions.assertTrue(configsResult.configs().contains("produce_success_count.max"));
        Assertions.assertTrue(configsResult.configs().contains("zone"));

        configsResult = CardConfigsHelper.parseConfigs(setupResult.migrationServiceMonitor(), setupResult.metricsMap(), cardD2, setupResult.serviceMap());
        Assertions.assertTrue(configsResult.configs().contains("Meeting_Telephony_telgw"));
        Assertions.assertTrue(configsResult.configs().contains("TEL_GLOBAL_STAT"));
        Assertions.assertTrue(configsResult.configs().contains("outdialfailures"));
        Assertions.assertTrue(configsResult.configs().contains("region"));


        configsResult = CardConfigsHelper.parseConfigs(setupResult.migrationServiceMonitor(), setupResult.metricsMap(), cardD3, setupResult.serviceMap());

        Assertions.assertTrue(configsResult.configs().contains("Meeting_Telephony_telgw"));
        Assertions.assertTrue(configsResult.configs().contains("vendor_bandwidth"));
        Assertions.assertTrue(configsResult.configs().contains("vendor_bandwidth_int"));
        Assertions.assertTrue(configsResult.configs().contains("TEL_CALL_JOIN_FAIL"));


    }

    @NotNull
    private static SetupResult setup() throws IOException {
        File file = ResourceUtils.getFile("classpath:metrics_def.json");
        String serviceMonitorJson = FileUtils.readFileToString(file);

        MigrationServiceMonitor migrationServiceMonitor = JacksonUtils.getObjectMapper().readValue(serviceMonitorJson, MigrationServiceMonitor.class);

        Map<String, MetricsTempOut> metricsMap = migrationServiceMonitor.getMetrics().stream().collect(Collectors.toMap(x -> x.getOldMetricsName() != null ? x.getOldMetricsName() : x.getMetricsName(), m -> m));
        Map<String, String> serviceMap = new HashMap<>();
        serviceMap.put("TEL", "Meeting_Telephony_telgw");
        SetupResult setupResult = new SetupResult(migrationServiceMonitor, metricsMap, serviceMap);

        return setupResult;
    }

    @NotNull
    private static SetupResult setupVdi() throws IOException {
        File file = ResourceUtils.getFile("classpath:metrics_def_vdi.json");
        String serviceMonitorJson = FileUtils.readFileToString(file);

        MigrationServiceMonitor migrationServiceMonitor = JacksonUtils.getObjectMapper().readValue(serviceMonitorJson, MigrationServiceMonitor.class);

        Map<String, MetricsTempOut> metricsMap = migrationServiceMonitor.getMetrics().stream().collect(Collectors.toMap(x -> x.getOldMetricsName() != null ? x.getOldMetricsName() : x.getMetricsName(), m -> m));
        Map<String, String> serviceMap = new HashMap<>();
        serviceMap.put("VDI", "Meeting_Gateway_vdigw");
        SetupResult setupResult = new SetupResult(migrationServiceMonitor, metricsMap, serviceMap);

        return setupResult;
    }

    @NotNull
    private static SetupResult setupZc() throws IOException {
        File file = ResourceUtils.getFile("classpath:metrics_def_zc.json");
        String serviceMonitorJson = FileUtils.readFileToString(file);

        MigrationServiceMonitor migrationServiceMonitor = JacksonUtils.getObjectMapper().readValue(serviceMonitorJson, MigrationServiceMonitor.class);

        Map<String, MetricsTempOut> metricsMap = migrationServiceMonitor.getMetrics().stream().collect(Collectors.toMap(x -> x.getOldMetricsName() != null ? x.getOldMetricsName() : x.getMetricsName(), m -> m));
        Map<String, String> serviceMap = new HashMap<>();
        serviceMap.put("ZC", "Meeting_Realtime_realtime-zc");
        SetupResult setupResult = new SetupResult(migrationServiceMonitor, metricsMap, serviceMap);

        return setupResult;
    }

    private record SetupResult(MigrationServiceMonitor migrationServiceMonitor, Map<String, MetricsTempOut> metricsMap,
                               Map<String, String> serviceMap) {
    }
}
