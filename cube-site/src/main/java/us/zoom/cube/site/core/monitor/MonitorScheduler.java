package us.zoom.cube.site.core.monitor;

import org.apache.commons.collections4.ListUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.lib.common.ServerTypeEnum;
import us.zoom.cube.lib.utils.ExceptionStackUtils;
import us.zoom.cube.site.biz.SysParaService;
import us.zoom.cube.site.core.ServerHandler;
import us.zoom.cube.site.core.TenantHandler;
import us.zoom.cube.site.core.model.common.TenantHeartCheckModel;
import us.zoom.cube.site.infra.AsyncMQInstance;
import us.zoom.cube.site.lib.common.MonitorTypeEnum;
import us.zoom.infra.dao.model.DistributedLockDO;
import us.zoom.infra.dao.model.TenantDO;
import us.zoom.infra.dao.service.DistributedLockDAO;
import us.zoom.infra.thread.NamedThreadFactory;
import us.zoom.infra.utils.DateUtils;
import us.zoom.infra.utils.IpUtils;
import us.zoom.cube.lib.utils.JsonUtils;
import us.zoom.mq.common.Result;
import us.zoom.mq.common.client.task.Task;
import us.zoom.mq.common.response.ProduceResult;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * dispath the service to check the heartbeat including agent and hub
 */
@Component
public class MonitorScheduler {

    @Autowired
    private ServerHandler serverHandler;
    @Autowired
    private DistributedLockDAO distributedLockDAO;
    @Autowired
    private TenantHandler tenantHandler;

    @Autowired
    private SysParaService sysParaService;

    private static ScheduledExecutorService scheduler;
    private static final Logger LOG= LoggerFactory.getLogger(MonitorScheduler.class.getName());
    private Logger nonitorLog = LoggerFactory.getLogger("Monitor");
    private Integer scheduleSeconds=60*5;
    public static final  String HEART_CHECK_ASSIGNER="heart_check_assigner";
    private int lockTimeOut=(scheduleSeconds/60);
    public static final Integer MAX_NOTICE_COUNT = 2 ;
    @Value("${agent.heart.check.topic}")
    private String agentHeartCheckTopic;
    @Value("${hub.heart.check.topic}")
    private String hubHeartCheckTopic;

    // @PostConstruct
    public void init(){
        scheduler= Executors.newScheduledThreadPool(1,new NamedThreadFactory("monitor agent or hub scheduler "));
        scheduler.scheduleAtFixedRate(new Runnable() {
            @Override
            public void run() {
                try{
                    LOG.info("begin hub heart monitor!");
                    if(!lock()){
                        LOG.info("do not get the lock");
                        return;
                    }
                    heartCheck();
                }catch (Exception e){
                    LOG.error("monitor error",e);
                }
            }
        },30 ,scheduleSeconds, TimeUnit.SECONDS);
    }

    public void heartCheck(){
        Map<String,Object> logMap = new HashMap<>();
        Long begin=System.currentTimeMillis();

        //offline server
        serverHandler.offlineServer(sysParaService.getHeartCheckInternalInMins());
        logMap.put("type",MonitorTypeEnum.offlineServer.name());
        logMap.put("ts",System.currentTimeMillis());
        logMap.put("cost",System.currentTimeMillis()- begin);
        logMap.put("timeShow", DateUtils.format(new java.util.Date(),DateUtils.FORMART1));
        nonitorLog.info(JsonUtils.toJsonStringIgnoreExp(logMap));
        begin=System.currentTimeMillis();

        // rest notify info
        tenantHandler.recoryNotice(sysParaService.getNotifyIntervalInMinutes(),sysParaService.getManagerService());
        logMap.clear();
        logMap.put("type",MonitorTypeEnum.recoryNotice.name());
        logMap.put("ts",System.currentTimeMillis());
        logMap.put("cost",System.currentTimeMillis()- begin);
        logMap.put("timeShow", DateUtils.format(new java.util.Date(),DateUtils.FORMART1));
        nonitorLog.info(JsonUtils.toJsonStringIgnoreExp(logMap));


        // dispatch

        dispatchHeartCheck(ServerTypeEnum.agent,agentHeartCheckTopic);

        dispatchHeartCheck(ServerTypeEnum.hub,hubHeartCheckTopic);

    }

    private void dispatchHeartCheck(ServerTypeEnum serverTypeEnum,String topic) {
        Long begin = System.currentTimeMillis();
        int notifySuccessSize = 0;
        int batchSize =  0;
        String serviceName = ServerTypeEnum.hub == serverTypeEnum ? sysParaService.getManagerService() : "";
        List<TenantDO> toNotifyTenants=tenantHandler.listUnNoticeTenant(serverTypeEnum,MAX_NOTICE_COUNT,serviceName);
        if(CollectionUtils.isEmpty(toNotifyTenants)){
            monitorDispatcher(serverTypeEnum, begin, notifySuccessSize, toNotifyTenants,batchSize);
            return;
        }

        if(toNotifyTenants.size() < 500 ){
            batchSize = 5 ;
        }else   if(toNotifyTenants.size() >= 500 && toNotifyTenants.size() < 5000 ){
            batchSize = 20 ;
        }else   if(toNotifyTenants.size() >= 5000 && toNotifyTenants.size() < 25000 ){
            batchSize = 50 ;
        }else  {
            batchSize = 100 ;
        }

        List<List<TenantDO>> notifyBatch = ListUtils.partition(toNotifyTenants,batchSize);
        for(List<TenantDO> tenantDOS : notifyBatch){
            try{
                List<TenantHeartCheckModel> tenantHeartCheckModels = tenantDOS.stream().map(item -> {
                    TenantHeartCheckModel model = new TenantHeartCheckModel();
                    BeanUtils.copyProperties(item,model);
                    model.setDispatchTime(System.currentTimeMillis());
                    return model;
                }).collect(Collectors.toList());
                Task<String> task = createTask(tenantHeartCheckModels, topic);
                Result<ProduceResult> result = AsyncMQInstance.getInstance().getProducer().sendSync(task);
                if (result.isSuccess()){
                    notifySuccessSize = notifySuccessSize +tenantDOS.size();
                }
            }catch (Exception e){
                LOG.error("send tenant heart check to async mq error , tenant {}",JsonUtils.toJsonStringIgnoreExp(tenantDOS),e);
                monitorHeartDispatchError(tenantDOS, e);
            }
        }

        monitorDispatcher(serverTypeEnum, begin, notifySuccessSize, toNotifyTenants,batchSize);

    }
    private Task<String> createTask(List<TenantHeartCheckModel> tenantHeartCheckModels, String topic) {
        Task<String> task = new Task<>();
        task.setTopicName(topic);
        task.setTaskType(AsyncMQInstance.ASYNC_MQ_TYPE);
        task.setPayload(JsonUtils.toJsonStringIgnoreExp(tenantHeartCheckModels));
        return task;
    }


    private void monitorHeartDispatchError(List<TenantDO> tenantDOS, Exception e) {
        try{
            Map<String,Object> logMap = new HashMap<>();
            logMap.put("type", MonitorTypeEnum.dispatchHeartCheckError.name());
            logMap.put("ts",System.currentTimeMillis());
            logMap.put("tenants", JsonUtils.toJsonStringIgnoreExp(tenantDOS.stream().map(item->item.getName()).collect(Collectors.toList())));
            logMap.put("stack", ExceptionStackUtils.parseExceptionStackToString(e));
            logMap.put("timeShow", DateUtils.format(new java.util.Date(),DateUtils.FORMART1));
            nonitorLog.info(JsonUtils.toJsonStringIgnoreExp(logMap));
        }catch (Exception e2){
            LOG.error("print monitor log error ",e);
        }
    }

    private void monitorDispatcher(ServerTypeEnum serverTypeEnum, Long begin, int notifySuccessSize, List<TenantDO> toNotifyTenants, int batchSize) {
        Map<String,Object> logMap = new HashMap<>();
        logMap.put("type", MonitorTypeEnum.dispatchHeartCheck.name());
        logMap.put("serverType",serverTypeEnum.name());
        logMap.put("ts",System.currentTimeMillis());
        logMap.put("cost",System.currentTimeMillis()- begin);
        logMap.put("totalCount",null == toNotifyTenants ? 0 : toNotifyTenants.size());
        logMap.put("successCount",notifySuccessSize);
        logMap.put("failCount",null == toNotifyTenants ? 0 :toNotifyTenants.size() - notifySuccessSize);
        logMap.put("batchSize",batchSize);
        logMap.put("timeShow", DateUtils.format(new java.util.Date(),DateUtils.FORMART1));
        nonitorLog.info(JsonUtils.toJsonStringIgnoreExp(logMap));
    }



    public boolean lock() {
//        if(1== 1){
//            return true;
//        }
        List<DistributedLockDO> distributedLocks=distributedLockDAO.listByType(HEART_CHECK_ASSIGNER);
        if(CollectionUtils.isEmpty(distributedLocks)){
            return false;
        }
        return  distributedLockDAO.lockWithType(HEART_CHECK_ASSIGNER,distributedLocks.get(0).getHandler(), IpUtils.getLocalIP(),lockTimeOut)>0;
    }
}
