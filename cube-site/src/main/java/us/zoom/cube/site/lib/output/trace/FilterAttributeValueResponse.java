package us.zoom.cube.site.lib.output.trace;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: eason.jia
 * @date: 2024/8/18
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FilterAttributeValueResponse {
    private List<String> stringAttributeValues;
    private List<Double> numberAttributeValues;
    private List<Boolean> boolAttributeValues;
}
