package us.zoom.cube.site.core;

import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import us.zoom.infra.dao.model.ServiceGroupDO;
import us.zoom.infra.dao.model.ServiceGroupTeamRelaDO;
import us.zoom.infra.dao.model.ServiceGroupTenantRelaDO;
import us.zoom.infra.dao.model.TeamDO;
import us.zoom.infra.dao.service.ServiceGroupDAO;
import us.zoom.infra.dao.service.ServiceGroupTeamRelaDAO;
import us.zoom.infra.dao.service.ServiceGroupTenantRelaDAO;
import us.zoom.infra.dao.service.TeamDAO;
import us.zoom.infra.utils.Instance;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class ServiceGroupHandler {

    private Logger monitorLog = LoggerFactory.getLogger("Monitor");

    /**
     * map<Id,serviceGroup>
     */
    private final AtomicReference<Map<String, ServiceGroupDO>> serviceGroupIdCache = new AtomicReference<>(new ConcurrentHashMap<>(16));

    /**
     * map<serviceKey,serviceGroup>
     */
    private final AtomicReference<Map<String, ServiceGroupDO>> serviceGroupKeyCache = new AtomicReference<>(new ConcurrentHashMap<>(16));


    @Autowired
    private ServiceGroupDAO serviceGroupDAO;

    @Autowired
    private ServiceGroupTenantRelaDAO serviceGroupTenantRelaDAO;

    @Autowired
    private TeamDAO teamDAO;
    @Autowired
    private ServiceGroupTeamRelaDAO serviceGroupTeamRelaDAO;

    public List<ServiceGroupDO> getAllServicerGroup() {
        return serviceGroupDAO.listAll();
    }

    public void load() {
        List<ServiceGroupDO> allServicerGroup = getAllServicerGroup();
        Map<String, ServiceGroupDO> idMap = Instance.ofNullable(allServicerGroup).stream()
                .collect(Collectors.toMap(ServiceGroupDO::getId, x -> x));
        serviceGroupIdCache.set(idMap);

        Map<String, ServiceGroupDO> serviceKeyMap = Instance.ofNullable(allServicerGroup).stream()
                .filter(x -> x.getServiceKey() != null)
                .collect(Collectors.toMap(ServiceGroupDO::getServiceKey, x -> x));
        serviceGroupKeyCache.set(serviceKeyMap);
    }

    public AtomicReference<Map<String, ServiceGroupDO>> getServiceGroupKeyCache() {
        return serviceGroupKeyCache;
    }

    public AtomicReference<Map<String, ServiceGroupDO>> getServiceGroupIdCache() {
        return serviceGroupIdCache;
    }

    public void batchAddOrUpdateServiceGroup(List<ServiceGroupDO> serviceGroupList) {
        serviceGroupDAO.batchAddOrUpdate(serviceGroupList);
    }


    public void batchAddOrUpdateTenantRela(List<ServiceGroupTenantRelaDO> relaList) {
        serviceGroupTenantRelaDAO.batchAddOrUpdate(relaList);
    }

    public void batchAddOrUpdateTeams(List<TeamDO> teams) {
        teamDAO.batchAddOrUpdate(teams);
    }

    public void batchAddOrUpdateTeamsRela(List<ServiceGroupTeamRelaDO> serviceGroupTeamRelaDOS) {
        serviceGroupTeamRelaDAO.batchAddOrUpdate(serviceGroupTeamRelaDOS);
    }

}
