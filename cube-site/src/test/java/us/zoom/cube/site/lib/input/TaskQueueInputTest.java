package us.zoom.cube.site.lib.input;


import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import us.zoom.cube.lib.common.AuthEncrTypeEnum;
import us.zoom.cube.lib.common.QueueTypeEnum;
import us.zoom.cube.lib.common.SwitchEnum;
import us.zoom.cube.lib.common.TaskTypeEnum;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: luis.zheng
 * @Date: 2021/8/17 4:08 
 */
public class TaskQueueInputTest {

    @Test
    public void testCheckTopic() {
        TaskQueueInput input = createTaskTopicInput();
        input.addCheck();

        input.setTopic("0123456789" + "0123456789" + "0123456789" + "0123456789" + "0123456789" + "0123456789" + "0123456789" + "0123456789" + "0123456789" + "012345678");
        input.addCheck();

        input.setTopic("   ");
        IllegalArgumentException err = Assertions.assertThrows(IllegalArgumentException.class, () -> {
            input.addCheck();
        });
        Assertions.assertEquals(err.getMessage(), "topic is blank");

        input.setTopic("0123456789" + "0123456789" + "0123456789" + "0123456789" + "0123456789" + "0123456789" + "0123456789" + "0123456789" + "0123456789" + "0123456789" + "0");
        IllegalArgumentException err1 = Assertions.assertThrows(IllegalArgumentException.class, () -> {
            input.addCheck();
        });
        Assertions.assertEquals(err1.getMessage(), "topic length is more than 100");

        input.setTopic("0");
        input.setUnitTagId("");
        IllegalArgumentException err2 = Assertions.assertThrows(IllegalArgumentException.class, () -> {
            input.addCheck();
        });
        Assertions.assertEquals(err2.getMessage(), "unitTagId is blank");
    }

    @Test
    public void testCheckUnitTagId() {
        TaskQueueInput input = createTaskTopicInput();
        input.setUnitTagId("  ");
        IllegalArgumentException err = Assertions.assertThrows(IllegalArgumentException.class, () -> {
            input.addCheck();
        });
        Assertions.assertEquals(err.getMessage(), "unitTagId is blank");
    }

    @Test
    public void testCheckType() {
        TaskQueueInput input = createTaskTopicInput();
        input.setTaskType("type");
        IllegalArgumentException err = Assertions.assertThrows(IllegalArgumentException.class, () -> {
            input.addCheck();
        });
        Assertions.assertEquals(err.getMessage(), "taskType " + input.getTaskType() + " not exist");

        input.setTaskType(TaskTypeEnum.etl.name());
        List<EnvironmentTaskRelaInput> environmentTaskRelaInputs = new ArrayList<>();
        EnvironmentTaskRelaInput environmentTaskRelaInput =createEnvTaskRelaInput();
        environmentTaskRelaInput.setQueueType("test");
        environmentTaskRelaInputs.add(environmentTaskRelaInput);
        input.setQueueCluster(environmentTaskRelaInputs);


        IllegalArgumentException err1 = Assertions.assertThrows(IllegalArgumentException.class, () -> {
            input.addCheck();
        });
        Assertions.assertEquals(err1.getMessage(), "queueType " + environmentTaskRelaInput.getQueueType() + " not exist");
    }

    @Test
    public void testCheckKafka() {
        TaskQueueInput input = createTaskTopicInput();

        List<EnvironmentTaskRelaInput> environmentTaskRelaInputs = new ArrayList<>();
        EnvironmentTaskRelaInput environmentTaskRelaInput =createEnvTaskRelaInput();
        environmentTaskRelaInputs.add(environmentTaskRelaInput);
        input.setQueueCluster(environmentTaskRelaInputs);

        input.setGroupId(null);
        input.setPollSize(1);
        input.setSessionTimeout(1);
        input.setProtocol(null);
        input.setIsDefault(null);
        IllegalArgumentException err1 = Assertions.assertThrows(IllegalArgumentException.class, () -> {
            input.addCheck();
        });
        Assertions.assertEquals(err1.getMessage(), "groupId is blank");
        input.setGroupId("test-group-id");
        input.getQueueCluster().get(0).setQueueClusterId("0123456789" + "0123456789" + "0123456789" + "0123456789" + "0123456789" + "0123456789" + "0123456789" + "0123456789" + "0123456789" + "0123456789" + "0");
        IllegalArgumentException err2 = Assertions.assertThrows(IllegalArgumentException.class, () -> {
            input.addCheck();
        });

        Assertions.assertEquals(err2.getMessage(), "kafkaClusterId length is more than 100");

        input.setGroupId(null);
        IllegalArgumentException err3 = Assertions.assertThrows(IllegalArgumentException.class, () -> {
            input.addCheck();
        });
        Assertions.assertEquals(err3.getMessage(), "groupId is blank");

        input.setGroupId("0123456789" + "0123456789" + "0123456789" + "0123456789" + "0123456789" + "0123456789" + "0123456789" + "0123456789" + "0123456789" + "0123456789" + "0");
        IllegalArgumentException err4 = Assertions.assertThrows(IllegalArgumentException.class, () -> {
            input.addCheck();
        });
        Assertions.assertEquals(err4.getMessage(), "groupId length is more than 100");
    }

    @Test
    public void testCheckAsyncMq() {
        TaskQueueInput input = createTaskTopicInput();

        input.setPollSize(null);
        input.setSessionTimeout(null);
        input.setProtocol(null);
        input.setIsDefault(null);

        List<EnvironmentTaskRelaInput> environmentTaskRelaInputs = new ArrayList<>();
        EnvironmentTaskRelaInput environmentTaskRelaInput =createEnvTaskRelaInput();
        environmentTaskRelaInput.setQueueType("async_mq");
        environmentTaskRelaInputs.add(environmentTaskRelaInput);
        input.setQueueCluster(environmentTaskRelaInputs);

        input.addCheck();

        Assertions.assertTrue(input.getPollSize() == 200);
        Assertions.assertTrue(input.getSessionTimeout().equals(20000));
        Assertions.assertEquals(input.getProtocol(), AuthEncrTypeEnum.ssl.name());
        Assertions.assertEquals(input.getIsDefault(), SwitchEnum.no.name());
    }

    public TaskQueueInput createTaskTopicInput() {

        TaskQueueInput input = new TaskQueueInput();
        input.setId("id");
        input.setName("name");
        input.setUnitTagId("id");
        input.setTaskType(TaskTypeEnum.etl.name());
        input.setTopic("topicName");
        input.setGroupId("groupId");
        input.setPollSize(200);
        input.setSessionTimeout(20000);
        input.setProtocol(AuthEncrTypeEnum.ssl.name());
        input.setIsDefault(AuthEncrTypeEnum.no.name());

        List<EnvironmentTaskRelaInput> environmentTaskRelaInputs = new ArrayList<>();
        EnvironmentTaskRelaInput environmentTaskRelaInput =createEnvTaskRelaInput();
        environmentTaskRelaInputs.add(environmentTaskRelaInput);
        input.setQueueCluster(environmentTaskRelaInputs);

        return input;
    }

    private EnvironmentTaskRelaInput createEnvTaskRelaInput(){
        EnvironmentTaskRelaInput environmentTaskRelaInput = new EnvironmentTaskRelaInput();
        environmentTaskRelaInput.setQueueType("kafka");
        environmentTaskRelaInput.setEnvironmentName("MAIN");
        environmentTaskRelaInput.setQueueClusterId("test-id");
        environmentTaskRelaInput.setQueueClusterName("test-cluster");

        return environmentTaskRelaInput;
    }
}
