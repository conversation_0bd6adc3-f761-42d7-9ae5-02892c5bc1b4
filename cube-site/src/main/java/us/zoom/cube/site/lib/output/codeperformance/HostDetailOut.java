package us.zoom.cube.site.lib.output.codeperformance;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @author: <PERSON><PERSON> Ding
 * @date: 2024/3/1 15:06
 * @desc:
 */
@Data
public class HostDetailOut {

    @JsonProperty("isK8S")
    private boolean isK8S;

    private String host;

    private String ip;

    private String ipPublic;

    private String podName;

    private String nodeName;

    private String k8sCluster;

    public static HostDetailOut newEc2HostInstance(String host, String ip, String ipPublic) {
        HostDetailOut hostDetailOut = new HostDetailOut();
        hostDetailOut.setK8S(false);
        hostDetailOut.setHost(host);
        hostDetailOut.setIp(ip);
        hostDetailOut.setIpPublic(ipPublic);
        return hostDetailOut;
    }

    public static HostDetailOut newK8sInstance(String podName, String nodeName, String k8sCluster) {
        HostDetailOut hostDetailOut = new HostDetailOut();
        hostDetailOut.setK8S(true);
        hostDetailOut.setPodName(podName);
        hostDetailOut.setNodeName(nodeName);
        hostDetailOut.setK8sCluster(k8sCluster);
        return hostDetailOut;
    }

}
