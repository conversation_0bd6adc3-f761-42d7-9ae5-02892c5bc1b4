package us.zoom.cube.site.infra.utils;

import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;
import us.zoom.cube.site.lib.Condition;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * parse the aggregation and alarm where string
 */
public class ConditionParseUtils {


//    public static void main(String [] fs){
//       List<Condition> conditions= parse("a > 1 ");
//       System.out.println(generate(conditions));
//        conditions=parse("a > 1 and b < 2");
//        System.out.println(generate(conditions));
//        conditions=parse("a > 1 AND c < 5");
//        System.out.println(generate(conditions));
//        conditions= parse("a > 1 AND ");
//        System.out.println(generate(conditions));
//
//    }

    public static String generate(List<Condition> conditions){
        if(CollectionUtils.isEmpty(conditions)){
            return "";
        }

//        StringBuilder result=new StringBuilder();
        return conditions.stream().map(e->creConditionStr(e)).collect(Collectors.joining(" AND "));
//        return  result.toString();
    }

    private static String creConditionStr(Condition condition) {
        return condition.getField()+" "+condition.getOperate()+" "+condition.getContent();
    }

    public static List<Condition> parse(String conditionOrigin){
        if(StringUtils.isBlank(conditionOrigin)){
            return Collections.emptyList();
        }

        List<String> conditionArrs=split(conditionOrigin);
        List<Condition> result=new ArrayList<>(conditionArrs.size());
        conditionArrs.forEach(ele->{
            ele=ele.trim();
            int firstIndex=ele.indexOf(" ");
            int lastIndex=ele.lastIndexOf(" ");
            if(firstIndex != -1 && lastIndex != -1){
                String field = ele.substring(0,firstIndex).trim();
                String operate=ele.substring(firstIndex,lastIndex).trim();
                String content=ele.substring(lastIndex).trim();
                result.add(new Condition(field,operate,content));
            }
        });
        return  result;
    }

    public static  List<String> split(String input){
        int index=StringUtils.indexOfIgnoreCase(input," and ");
        if(index == -1){
            return Arrays.asList(input.trim());
        }

        List<String> result=new ArrayList<>();
        while( index != -1){
            result.add(input.substring(0,index).trim());
            input=input.substring(index+5);
            index=StringUtils.indexOfIgnoreCase(input," and ");
        }
        if(StringUtils.isNotBlank(StringUtils.trim(input))){
            result.add(input.trim());
        }

        return  result;
    }
}
