package us.zoom.cube.site.lib.output.metric;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

public class ItemOut {

    @JsonFormat(pattern = "MM-dd/HH:mm:ss")
    private Date periodTime;
    private String type;
    private Object value;

    public Date getPeriodTime() {
        return periodTime;
    }

    public void setPeriodTime(Date periodTime) {
        this.periodTime = periodTime;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Object getValue() {
        return value;
    }

    public void setValue(Object value) {
        this.value = value;
    }
}
