package us.zoom.infra.dao.service;


import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import us.zoom.cube.lib.common.InfluxHaStatus;
import us.zoom.cube.site.BaseSpringTest;
import us.zoom.infra.dao.model.InfluxdbClusterDO;

import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @create 8/18/21 10:10 AM
 */
public class InfluxdbClusterDAOTest extends BaseSpringTest {

    @Autowired
    private InfluxdbClusterDAO influxdbClusterDAO;

    @Test
    public void search() {

        InfluxdbClusterDO influxdbClusterDO1 = buildInfluxdbCluster1();
        influxdbClusterDAO.add(influxdbClusterDO1);
        InfluxdbClusterDO influxdbClusterDO2 = buildInfluxdbCluster2();
        influxdbClusterDAO.add(influxdbClusterDO2);

        List<InfluxdbClusterDO> result = influxdbClusterDAO.listAll();
        Assertions.assertTrue(result.size() > 0);

        List<InfluxdbClusterDO> searchResult0 = influxdbClusterDAO.search(0, 5, "", null);
        Assertions.assertTrue(searchResult0.size() >= 2);

        List<InfluxdbClusterDO> searchResult1 = influxdbClusterDAO.search(0, 5, "cluster", null);
        Assertions.assertTrue(searchResult1.size() >= 2);

        List<InfluxdbClusterDO> searchResult2 = influxdbClusterDAO.search(0, 5, "cluster", "cloud");
        Assertions.assertTrue(searchResult2.size() >= 1);

        List<InfluxdbClusterDO> searchResult3 = influxdbClusterDAO.search(0, 5, "cluster", "onpremise");
        Assertions.assertTrue(searchResult3.size() >= 1);

        List<InfluxdbClusterDO> searchResult4 = influxdbClusterDAO.search(0, 5, "test_cloud_cluster", "onpremise");
        Assertions.assertEquals(0, searchResult4.size());

        int countResult1 = influxdbClusterDAO.count("", null);
        Assertions.assertTrue(countResult1 >= 2);

        int countResult2 = influxdbClusterDAO.count("premise", "onpremise");
        Assertions.assertTrue(countResult2 >= 1);

        influxdbClusterDAO.delete(influxdbClusterDO1.getId());
        influxdbClusterDAO.delete(influxdbClusterDO2.getId());

        System.out.println("hello!");
    }

    @Test
    public void edit() {
        InfluxdbClusterDO influxdbClusterDO = buildInfluxdbCluster1();
        influxdbClusterDAO.add(influxdbClusterDO);

        InfluxdbClusterDO getByIdResult = influxdbClusterDAO.getById(influxdbClusterDO.getId());
        Assertions.assertNotNull(getByIdResult);

        influxdbClusterDO.setName("on_premise_cluster-1");
        influxdbClusterDO.setType("cloud");
        influxdbClusterDO.setServerUrl("https://cloud.zoom.us");
        influxdbClusterDO.setClusterId("cubePrime-1");
        influxdbClusterDO.setRegionId("Ohio-1");
        influxdbClusterDO.setIsDefault(true);
        influxdbClusterDO.setCreateTime(new Date());
        influxdbClusterDO.setModifyTime(new Date());
        influxdbClusterDAO.edit(influxdbClusterDO);

        influxdbClusterDAO.delete(influxdbClusterDO.getId());
    }

    @Test
    public void setDefault() {
        InfluxdbClusterDO influxdbClusterDO1 = buildInfluxdbCluster1();
        influxdbClusterDAO.add(influxdbClusterDO1);
        influxdbClusterDAO.setDefaultCluster(influxdbClusterDO1.getId());
        InfluxdbClusterDO getResult = influxdbClusterDAO.getById(influxdbClusterDO1.getId());
        Assertions.assertTrue(getResult.getIsDefault());

        InfluxdbClusterDO influxdbClusterDO2 = buildInfluxdbCluster2();
        influxdbClusterDAO.add(influxdbClusterDO2);
//        influxdbClusterDAO.resetOtherDefaultClusters(influxdbClusterDO2.getId());
        getResult = influxdbClusterDAO.getById(influxdbClusterDO1.getId());
//        Assertions.assertFalse(getResult.getIsDefault());

        influxdbClusterDAO.delete(influxdbClusterDO1.getId());
        influxdbClusterDAO.delete(influxdbClusterDO2.getId());
    }

    private InfluxdbClusterDO buildInfluxdbCluster1() {
        InfluxdbClusterDO influxdbClusterDO = new InfluxdbClusterDO();
        influxdbClusterDO.setId(UUID.randomUUID().toString());
        influxdbClusterDO.setName("on_premise_cluster");
        influxdbClusterDO.setType("onpremise");
        influxdbClusterDO.setServerUrl("https://influx.zoom.us");
        influxdbClusterDO.setUsername("");
        influxdbClusterDO.setPassword("");
        influxdbClusterDO.setClusterId("cubePrime");
        influxdbClusterDO.setRegionId("Ohio");
        influxdbClusterDO.setIsDefault(false);
        influxdbClusterDO.setCreateTime(new Date());
        influxdbClusterDO.setModifyTime(new Date());
        influxdbClusterDO.setOrgID("org");
        influxdbClusterDO.setClusterId(InfluxHaStatus.SINGLE_CLUSTER.getValue());
        return influxdbClusterDO;
    }

    private InfluxdbClusterDO buildInfluxdbCluster2() {
        InfluxdbClusterDO influxdbClusterDO = new InfluxdbClusterDO();
        influxdbClusterDO.setId(UUID.randomUUID().toString());
        influxdbClusterDO.setName("test_cloud_cluster");
        influxdbClusterDO.setType("cloud");
        influxdbClusterDO.setServerUrl("https://us-east-1-1.aws.cloud2.influxdata.com");
        influxdbClusterDO.setUsername("");
        influxdbClusterDO.setPassword("");
        influxdbClusterDO.setClusterId("cubePrime");
        influxdbClusterDO.setRegionId("Ohio");
        influxdbClusterDO.setIsDefault(false);
        influxdbClusterDO.setCreateTime(new Date());
        influxdbClusterDO.setModifyTime(new Date());
        influxdbClusterDO.setOrgID("org");
        influxdbClusterDO.setClusterId(InfluxHaStatus.SINGLE_CLUSTER.getValue());
        return influxdbClusterDO;
    }

}
