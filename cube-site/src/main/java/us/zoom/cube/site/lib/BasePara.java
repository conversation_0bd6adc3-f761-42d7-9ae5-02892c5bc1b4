package us.zoom.cube.site.lib;

import us.zoom.cube.site.api.intercept.AuthInterceptor;
import us.zoom.infra.thread.ThreadLocalStore;

public class BasePara {
    /**
     * get from head
     */
    public String userId;

    public String operId;

    public String accessToken;

    public Integer timeZone;

    public String authResourceUrl;
    /**
     * get from head
     */
    public String tenantId;

    public String getUserId() {
        return AuthInterceptor.getUserId();
    }

    public String getUserName() {
        return ThreadLocalStore.getUserNameLocal();
    }

    public void setUserId(String userId) {
        //sync for getUserId
        ThreadLocalStore.setUserInfoLocal(userId);
    }

    public String getOperId() {
        return operId;
    }

    public void setOperId(String operId) {
        this.operId = operId;
    }

    public String getAccessToken() {
        return accessToken;
    }

    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }

    public String getTenantId() {
        return ThreadLocalStore.getTenantInfoLocal();
    }

    public void setTenantId(String tenantId) {
        //sync for getTenantId
        ThreadLocalStore.setTenantInfoLocal(tenantId);
    }


    public Integer getTimeZone() {

        return ThreadLocalStore.getTimeZoneLocal();
    }

    public String getAuthResourceUrl() {
        return ThreadLocalStore.getApiPath();
    }

    public void setAuthResourceUrl(String authResourceUrl) {
        ThreadLocalStore.setApiPath(authResourceUrl);
    }
}
