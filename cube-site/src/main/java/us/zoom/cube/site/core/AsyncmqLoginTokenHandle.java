package us.zoom.cube.site.core;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import us.zoom.infra.dao.model.AsyncmqAccountMappingDO;
import us.zoom.infra.dao.service.AsyncmqLoginTokenDAO;
import us.zoom.cube.lib.utils.JsonUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
@Slf4j
public class AsyncmqLoginTokenHandle {

    @Autowired
    private AsyncmqLoginTokenDAO asyncmqLoginTokenDAO;

    public AsyncmqAccountMappingDO getAsyncmqInfo(String name) {
        return asyncmqLoginTokenDAO.getAsyncmqInfoByName(name);
    }

    public void addOrUpdate(AsyncmqAccountMappingDO asyncmqAccountMappingDO) {
        asyncmqLoginTokenDAO.addOrUpdate(asyncmqAccountMappingDO);
    }

    public List<AsyncmqAccountMappingDO> getAll() {
        return asyncmqLoginTokenDAO.getAllAccount();
    }

    public void update(List<String> topics, String asyncmqName, String authorizedTopics) {
        if (!StringUtils.isEmpty(authorizedTopics)) {
            List<String> authTopic = JsonUtils.toObject(authorizedTopics, List.class);
            topics.addAll(authTopic);
        }
        topics  = Optional.ofNullable(topics).orElse(new ArrayList<>()).stream().collect(Collectors.toSet()).stream().collect(Collectors.toList());
        asyncmqLoginTokenDAO.update(JsonUtils.toJsonString(topics), asyncmqName);
    }

    public void updatePassword(String password, String asyncmqName) {
        if (StringUtils.isBlank(password) || StringUtils.isBlank(asyncmqName)) {
            log.info("skip update password, password or name is null. password is null: {}, name: {}", StringUtils.isBlank(password), asyncmqName);
            return;
        }
        asyncmqLoginTokenDAO.updatePassword(password, asyncmqName);
    }
}
