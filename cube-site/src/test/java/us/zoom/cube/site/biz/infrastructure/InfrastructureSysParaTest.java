package us.zoom.cube.site.biz.infrastructure;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import us.zoom.cube.lib.utils.JsonUtils;

/**
 * @Author: <EMAIL>
 * @ModuleOwner: <EMAIL>
 * @Date: 02/27/2024 19:08
 * @Description:
 */
public class InfrastructureSysParaTest {
    @Test
    public void buildObject() {
        InfrastructureSysPara infrastructureSysPara = new InfrastructureSysPara();
        infrastructureSysPara.setGroupBy(Lists.newArrayList(new InfrastructureSysPara.Value("Cluster", "clusterId"),
                new InfrastructureSysPara.Value("Region", "regionId"),
                new InfrastructureSysPara.Value("Cluster", "clusterId"),
                new InfrastructureSysPara.Value("Zone Name", "zoneName")
        ));
        infrastructureSysPara.setFillBy(Lists.newArrayList(
                new InfrastructureSysPara.Value("cpu.us", "cpu.us"),
                new InfrastructureSysPara.Value("disk.usedPercent", "disk.usedPercent"),
                new InfrastructureSysPara.Value("memory.usedPercent", "memory.usedPercent"),
                new InfrastructureSysPara.Value("load.loadNorm1", "load.loadNorm1"),
                new InfrastructureSysPara.Value("load.loadNorm5", "load.loadNorm5"),
                new InfrastructureSysPara.Value("load.loadNorm15", "load.loadNorm15"),
                new InfrastructureSysPara.Value("process", "monitor_process")));
        infrastructureSysPara.setFilterBy(Lists.newArrayList(
                new InfrastructureSysPara.Value("Zone Name", "zoneName"),
                new InfrastructureSysPara.Value("Host Name", "host"),
                new InfrastructureSysPara.Value("Pod Name", "podName"),
                new InfrastructureSysPara.Value("Node Name", "nodeName"),
                new InfrastructureSysPara.Value("Container Id", "containerId"),
                new InfrastructureSysPara.Value("Contain Name", "containerName"),
                new InfrastructureSysPara.Value("IsK8S", "isK8S")));
        infrastructureSysPara.setBaseTags(Sets.newHashSet(
                "processName",
                "host",
                "instanceId",
                "clusterId",
                "regionId",
                "ip",
                "version",
                "ipPublic",
                "zoneName",
                "isK8S",
                "podName",
                "nodeName",
                "containerId",
                "containerName",
                "cell",
                "stack",
                "namespace"));
        infrastructureSysPara.setVerifyMetrics(Sets.newHashSet(
                "cpu",
                "disk",
                "memory",
                "load",
                "monitor_process"));
        infrastructureSysPara.setLinkToNodeDashboard("https://cube.zoom.us/dashboards/d77784e6-3550-4e88-9af4-e80dde4574ba");
    }

    @Test
    public void mapperToObject() {
        String config = "{\n" +
                "    \"groupBy\": [\n" +
                "        {\n" +
                "            \"label\": \"Cluster\",\n" +
                "            \"field\": \"clusterId\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"label\": \"Region\",\n" +
                "            \"field\": \"regionId\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"label\": \"Zone Name\",\n" +
                "            \"field\": \"zoneName\"\n" +
                "        }\n" +
                "    ],\n" +
                "    \"fillBy\": [\n" +
                "        {\n" +
                "            \"label\": \"cpu.us\",\n" +
                "            \"field\": \"cpu.us\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"label\": \"disk.usedPercent\",\n" +
                "            \"field\": \"disk.usedPercent\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"label\": \"memory.usedPercent\",\n" +
                "            \"field\": \"memory.usedPercent\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"label\": \"load.loadNorm1\",\n" +
                "            \"field\": \"load.loadNorm1\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"label\": \"load.loadNorm5\",\n" +
                "            \"field\": \"load.loadNorm5\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"label\": \"load.loadNorm15\",\n" +
                "            \"field\": \"load.loadNorm15\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"label\": \"process\",\n" +
                "            \"field\": \"monitor_process\"\n" +
                "        }\n" +
                "    ],\n" +
                "    \"filterBy\": [\n" +
                "        {\n" +
                "            \"label\": \"Cluster\",\n" +
                "            \"field\": \"clusterId\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"label\": \"Region\",\n" +
                "            \"field\": \"regionId\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"label\": \"Instance\",\n" +
                "            \"field\": \"instanceId\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"label\": \"Zone Name\",\n" +
                "            \"field\": \"zoneName\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"label\": \"Host Name\",\n" +
                "            \"field\": \"host\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"label\": \"Pod Name\",\n" +
                "            \"field\": \"podName\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"label\": \"Node Name\",\n" +
                "            \"field\": \"nodeName\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"label\": \"Container Id\",\n" +
                "            \"field\": \"containerId\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"label\": \"Contain Name\",\n" +
                "            \"field\": \"containerName\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"label\": \"IsK8s\",\n" +
                "            \"field\": \"isK8S\"\n" +
                "        }\n" +
                "    ],\n" +
                "    \"linkToNodeDashboard\": \"https://cube.zoom.us/dashboards/d77784e6-3550-4e88-9af4-e80dde4574ba\"\n" +
                "}";
        try {
            InfrastructureSysPara infrastructureSysPara = JsonUtils.toObjectByTypeRef(config, new TypeReference<InfrastructureSysPara>() {
            });
        } catch (Exception e) {
            Assertions.fail("toObjectByTypeRef fail");
        }
    }
}
