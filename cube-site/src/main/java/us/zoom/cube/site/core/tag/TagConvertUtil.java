package us.zoom.cube.site.core.tag;

import com.zoom.op.monitor.domain.TagInputDo;
import com.zoom.op.monitor.domain.TagTypeOutDo;
import us.zoom.cube.site.lib.output.tag.TagInfoOut;
import us.zoom.cube.site.lib.output.tag.TagTypeOut;
import us.zoom.infra.dao.model.TagNameDo;

/**
 * <AUTHOR>
 * @date 2025/1/24 17:14
 */
public class TagConvertUtil {
    public static TagInfoOut toTagInfoOut(TagNameDo tagNameDo) {
        TagInfoOut tagInfoOut = new TagInfoOut();
        tagInfoOut.setTagName(tagNameDo.getTagName());
        tagInfoOut.setId(tagNameDo.getId());
        tagInfoOut.setTagTypeId(tagNameDo.getTagTypeId());
        TagTypeOut tagTypeOut = new TagTypeOut();
        tagTypeOut.setTagTypeName(tagNameDo.getTagTypeDo().getTagTypeName());
        tagTypeOut.setDesc(tagNameDo.getTagTypeDo().getDesc());
        tagTypeOut.setSource(tagNameDo.getTagTypeDo().getSource());
        tagTypeOut.setId(tagNameDo.getTagTypeDo().getId());
        tagInfoOut.setTagTypeId(tagNameDo.getTagTypeId());
        tagTypeOut.setScopes(tagNameDo.getTagTypeDo().getScopes());
        tagInfoOut.setTagType(tagTypeOut);

        return tagInfoOut;
    }

    public static TagInputDo toTagInfoOutDo(TagNameDo tagNameDo) {
        TagInputDo tagInfoOut = new TagInputDo();
        tagInfoOut.setTagName(tagNameDo.getTagName());
        tagInfoOut.setId(tagNameDo.getId());
        tagInfoOut.setTagTypeId(tagNameDo.getTagTypeId());
        TagTypeOutDo tagTypeOut = new TagTypeOutDo();
        tagTypeOut.setTagTypeName(tagNameDo.getTagTypeDo().getTagTypeName());
        tagTypeOut.setDesc(tagNameDo.getTagTypeDo().getDesc());
        tagTypeOut.setSource(tagNameDo.getTagTypeDo().getSource());
        tagTypeOut.setId(tagNameDo.getTagTypeDo().getId());
        tagTypeOut.setScopes(tagNameDo.getTagTypeDo().getScopes());
        tagInfoOut.setTagTypeId(tagNameDo.getTagTypeId());
        tagInfoOut.setTagType(tagTypeOut);
        return tagInfoOut;
    }

    // only for import/export interface
    public static TagInputDo toTagInfoInputDo(TagNameDo tagNameDo) {
        TagInputDo tagInfoOut = new TagInputDo();
        tagInfoOut.setTagName(tagNameDo.getTagName());
        TagTypeOutDo tagTypeOut = new TagTypeOutDo();
        tagTypeOut.setTagTypeName(tagNameDo.getTagTypeDo().getTagTypeName());
        tagTypeOut.setDesc(tagNameDo.getTagTypeDo().getDesc());
        tagTypeOut.setSource(tagNameDo.getTagTypeDo().getSource());
        tagTypeOut.setId(tagNameDo.getTagTypeDo().getId());
        tagTypeOut.setScopes(tagNameDo.getTagTypeDo().getScopes());
        tagInfoOut.setTagType(tagTypeOut);
        return tagInfoOut;
    }

    public static TagInfoOut toTagInfoOut(TagInputDo tagNameDo) {
        TagInfoOut tagInfoOut = new TagInfoOut();
        tagInfoOut.setTagName(tagNameDo.getTagName());
        tagInfoOut.setId(tagNameDo.getId());
        tagInfoOut.setTagTypeId(tagNameDo.getTagTypeId());
        TagTypeOut tagTypeOut = new TagTypeOut();
        tagTypeOut.setTagTypeName(tagNameDo.getTagType().getTagTypeName());
        tagTypeOut.setDesc(tagNameDo.getTagType().getDesc());
        tagTypeOut.setSource(tagNameDo.getTagType().getSource());
        tagTypeOut.setId(tagNameDo.getTagType().getId());
        tagTypeOut.setScopes(tagNameDo.getTagType().getScopes());
        tagInfoOut.setTagTypeId(tagNameDo.getTagTypeId());
        tagInfoOut.setTagType(tagTypeOut);
        return tagInfoOut;
    }

}
