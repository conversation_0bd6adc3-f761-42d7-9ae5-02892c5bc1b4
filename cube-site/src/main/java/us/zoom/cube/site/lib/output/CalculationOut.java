package us.zoom.cube.site.lib.output;

import lombok.Data;
import us.zoom.infra.dao.model.EnvironmentTaskRelaDO;

import java.util.List;

@Data
public class CalculationOut {
    private String id;
    private String name;
    private String sourceQueueId;
    private String sourceQueueName;
    private String sourceTopic;
    private List<EnvironmentTaskRelaDO> sourceQueueEnvRelas;
    private String sourceGroupId;
    private String sinkQueueId;
    private String sinkQueueName;
    private String sinkTopic;
    private List<EnvironmentTaskRelaDO> sinkQueueEnvRelas;
    private String configParam;
}
