package us.zoom.cube.site.lib;

import us.zoom.cube.site.infra.enums.WebCodeEnum;

/**
 * <AUTHOR>
 */
public class AuthException extends RuntimeException {

    private String code;
    private String tips;
    public AuthException(WebCodeEnum codeEnum){
        super(codeEnum.getErrMsg());
        this.code=codeEnum.getCode();
        this.tips=codeEnum.getErrMsg();
    }

    public AuthException(String code,String tips){
        super(tips);
        this.code=code;
        this.tips=tips;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getTips() {
        return tips;
    }

    public void setTips(String tips) {
        this.tips = tips;
    }
}
